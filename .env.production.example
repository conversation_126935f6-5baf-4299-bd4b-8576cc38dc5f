# Production Environment Variables Template
# Copy this file to .env.production and fill in your actual values
# NEVER commit .env.production to version control!

DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# Database Connection (Alternative format for some tools)
PGDATABASE="your_database_name"
PGHOST="your_database_host"
PGPORT="5432"
PGUSER="your_database_user"
PGPASSWORD="your_database_password"

# Production Security Secrets (32+ characters, cryptographically secure)
# Generate using: openssl rand -hex 32
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
COOKIE_SECRET=your_cookie_secret_here_minimum_32_characters
SESSION_SECRET=your_session_secret_here_minimum_32_characters

# Cloudinary Configuration
CLOUDINARY_API_KEY="your_cloudinary_api_key"
CLOUDINARY_API_SECRET="your_cloudinary_api_secret"
CLOUDINARY_CLOUD_NAME="your_cloudinary_cloud_name"

# Twilio SMS + Sendgrid Email
TWILIO_MESSAGING_SID="your_twilio_messaging_sid"
TWILIO_ACCOUNT_SID="your_twilio_account_sid"
TWILIO_AUTH_TOKEN="your_twilio_auth_token"
DLT_ENTITY_ID="your_dlt_entity_id"
SENDGRID_API_KEY="your_sendgrid_api_key"

# Application Configuration
PORT=5000
NODE_ENV="production"