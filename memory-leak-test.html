<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Leak Prevention Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>OwnerDashboard Memory Leak Prevention Test</h1>
    
    <div class="test-section">
        <h2>WebSocket Cleanup Test</h2>
        <p>This test simulates WebSocket connections and cleanup to verify no memory leaks occur.</p>
        <button onclick="testWebSocketCleanup()">Test WebSocket Cleanup</button>
        <div id="websocket-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Sound Timeout Cleanup Test</h2>
        <p>This test verifies that sound notification timeouts are properly cleaned up.</p>
        <button onclick="testSoundTimeoutCleanup()">Test Sound Timeout Cleanup</button>
        <div id="sound-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Polling Interval Cleanup Test</h2>
        <p>This test verifies that polling intervals are properly cleared on cleanup.</p>
        <button onclick="testPollingCleanup()">Test Polling Cleanup</button>
        <div id="polling-results"></div>
    </div>

    <script>
    // WebSocket Cleanup Test
    function testWebSocketCleanup() {
        const results = document.getElementById('websocket-results');
        results.innerHTML = '<div class="warning">Testing WebSocket cleanup...</div>';
        
        const wsRefs = [];
        const cleanupFunctions = [];
        
        // Simulate creating multiple WebSocket connections
        for (let i = 0; i < 5; i++) {
            const wsRef = { current: null };
            wsRefs.push(wsRef);
            
            // Simulate WebSocket creation (without actually connecting)
            wsRef.current = {
                close: function(code, reason) {
                    this.readyState = 3; // CLOSED
                    console.log(`Mock WebSocket ${i} closed: ${code} - ${reason}`);
                },
                readyState: 1 // OPEN
            };
            
            // Create cleanup function
            const cleanup = () => {
                if (wsRef.current && wsRef.current.readyState !== 3) {
                    wsRef.current.close(1000, 'Component unmounting');
                    wsRef.current = null;
                }
            };
            cleanupFunctions.push(cleanup);
        }
        
        // Simulate cleanup
        setTimeout(() => {
            cleanupFunctions.forEach(cleanup => cleanup());
            
            // Verify all connections are closed
            const allClosed = wsRefs.every(ref => ref.current === null);
            
            if (allClosed) {
                results.innerHTML = '<div class="success">✅ WebSocket cleanup test passed - all connections properly closed</div>';
            } else {
                results.innerHTML = '<div class="error">❌ WebSocket cleanup test failed - some connections not closed</div>';
            }
        }, 1000);
    }
    
    // Sound Timeout Cleanup Test
    function testSoundTimeoutCleanup() {
        const results = document.getElementById('sound-results');
        results.innerHTML = '<div class="warning">Testing sound timeout cleanup...</div>';
        
        const timeouts = new Set();
        let completedCount = 0;
        
        // Simulate creating sound timeouts
        for (let i = 0; i < 10; i++) {
            const timeout = setTimeout(() => {
                completedCount++;
                console.log(`Sound timeout ${i} completed`);
            }, 100 + (i * 50));
            timeouts.add(timeout);
        }
        
        // Simulate cleanup after 200ms (before most timeouts complete)
        setTimeout(() => {
            timeouts.forEach(timeout => clearTimeout(timeout));
            timeouts.clear();
            
            // Wait a bit more to see if any timeouts fire after cleanup
            setTimeout(() => {
                if (completedCount < 5) { // Some should have been cancelled
                    results.innerHTML = `<div class="success">✅ Sound timeout cleanup test passed - ${completedCount}/10 timeouts completed (${10 - completedCount} properly cancelled)</div>`;
                } else {
                    results.innerHTML = '<div class="error">❌ Sound timeout cleanup test failed - timeouts were not properly cancelled</div>';
                }
            }, 1000);
        }, 200);
    }
    
    // Polling Interval Cleanup Test
    function testPollingCleanup() {
        const results = document.getElementById('polling-results');
        results.innerHTML = '<div class="warning">Testing polling interval cleanup...</div>';
        
        let pollCount = 0;
        const pollingIntervalRef = { current: null };
        
        // Start polling
        pollingIntervalRef.current = setInterval(() => {
            pollCount++;
            console.log(`Poll count: ${pollCount}`);
        }, 100);
        
        // Clean up after 350ms (should allow ~3 polls)
        setTimeout(() => {
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
                pollingIntervalRef.current = null;
            }
            
            const pollCountAtCleanup = pollCount;
            
            // Wait another 500ms to verify polling stopped
            setTimeout(() => {
                if (pollCount === pollCountAtCleanup) {
                    results.innerHTML = `<div class="success">✅ Polling cleanup test passed - polling stopped after ${pollCountAtCleanup} cycles</div>`;
                } else {
                    results.innerHTML = '<div class="error">❌ Polling cleanup test failed - polling continued after cleanup</div>';
                }
            }, 500);
        }, 350);
    }
    
    // Auto-run all tests when page loads
    window.addEventListener('load', () => {
        setTimeout(() => {
            console.log('Running automated memory leak prevention tests...');
            testWebSocketCleanup();
            setTimeout(() => testSoundTimeoutCleanup(), 2000);
            setTimeout(() => testPollingCleanup(), 4000);
        }, 1000);
    });
    </script>
</body>
</html>