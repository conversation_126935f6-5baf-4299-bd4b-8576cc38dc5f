/**
 * ESLint Configuration for Naming Convention Enforcement
 * 
 * This configuration enforces consistent naming conventions across the codebase:
 * - Components: PascalCase
 * - Variables/Functions: camelCase
 * - Constants: SCREAMING_SNAKE_CASE
 * - Types/Interfaces: PascalCase
 * - Files: PascalCase for components, camelCase for utilities
 */

module.exports = {
  extends: [
    // Your existing ESLint configuration
    '@typescript-eslint/recommended'
  ],
  
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks'
  ],
  
  rules: {
    // =============================================
    // TYPESCRIPT NAMING CONVENTIONS
    // =============================================
    '@typescript-eslint/naming-convention': [
      'error',
      
      // Interfaces should be PascalCase
      {
        selector: 'interface',
        format: ['PascalCase'],
        custom: {
          regex: '^I[A-Z]',
          match: false // Don't use Hungarian notation (IInterface)
        }
      },
      
      // Type aliases should be PascalCase
      {
        selector: 'typeAlias',
        format: ['PascalCase']
      },
      
      // Enums should be PascalCase
      {
        selector: 'enum',
        format: ['PascalCase']
      },
      
      // Enum members should be PascalCase
      {
        selector: 'enumMember',
        format: ['PascalCase']
      },
      
      // Classes should be PascalCase
      {
        selector: 'class',
        format: ['PascalCase']
      },
      
      // Variables should be camelCase or UPPER_CASE (for constants)
      {
        selector: 'variable',
        format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
        leadingUnderscore: 'allow',
        trailingUnderscore: 'allow'
      },
      
      // Functions should be camelCase
      {
        selector: 'function',
        format: ['camelCase', 'PascalCase'], // PascalCase for React components
        leadingUnderscore: 'allow'
      },
      
      // Parameters should be camelCase
      {
        selector: 'parameter',
        format: ['camelCase'],
        leadingUnderscore: 'allow'
      },
      
      // Methods should be camelCase
      {
        selector: 'classMethod',
        format: ['camelCase'],
        leadingUnderscore: 'allow'
      },
      
      // Properties should be camelCase
      {
        selector: 'classProperty',
        format: ['camelCase', 'UPPER_CASE'], // UPPER_CASE for static constants
        leadingUnderscore: 'allow'
      },
      
      // Object literal properties should be camelCase
      {
        selector: 'objectLiteralProperty',
        format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
        leadingUnderscore: 'allow',
        trailingUnderscore: 'allow'
      },
      
      // Type parameters should be PascalCase (T, TData, etc.)
      {
        selector: 'typeParameter',
        format: ['PascalCase'],
        prefix: ['T', 'K', 'V', 'U']
      }
    ],
    
    // =============================================
    // REACT COMPONENT CONVENTIONS
    // =============================================
    
    // React components should be PascalCase
    'react/jsx-pascal-case': [
      'error',
      {
        allowAllCaps: false,
        ignore: []
      }
    ],
    
    // React hooks should start with 'use'
    'react-hooks/rules-of-hooks': 'error',
    
    // =============================================
    // GENERAL NAMING RULES
    // =============================================
    
    // Prefer const assertions for immutable data
    '@typescript-eslint/prefer-as-const': 'error',
    
    // No unused variables (but allow underscore prefix for intentionally unused)
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        ignoreRestSiblings: true
      }
    ],
    
    // Consistent type imports
    '@typescript-eslint/consistent-type-imports': [
      'error',
      {
        prefer: 'type-imports',
        disallowTypeAnnotations: false
      }
    ]
  },
  
  // =============================================
  // OVERRIDES FOR SPECIFIC FILE PATTERNS
  // =============================================
  overrides: [
    
    // React component files
    {
      files: ['**/*.tsx'],
      rules: {
        // Component files should export a default component
        'import/prefer-default-export': 'off', // We prefer named exports
        
        // React component naming
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'variable',
            format: ['PascalCase'],
            filter: {
              regex: '^[A-Z].*Component$|^[A-Z].*$',
              match: true
            }
          }
        ]
      }
    },
    
    // Type definition files
    {
      files: ['**/*.types.ts', '**/*.d.ts', '**/types/**/*.ts'],
      rules: {
        // More flexible naming for type files
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'interface',
            format: ['PascalCase']
          },
          {
            selector: 'typeAlias',
            format: ['PascalCase']
          }
        ]
      }
    },
    
    // Constants files
    {
      files: ['**/constants.ts', '**/constants/**/*.ts'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'variable',
            format: ['UPPER_CASE', 'camelCase'] // Allow both for flexibility
          }
        ]
      }
    },
    
    // Test files
    {
      files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
      rules: {
        // More flexible naming in tests
        '@typescript-eslint/naming-convention': 'off'
      }
    },
    
    // Configuration files
    {
      files: ['*.config.js', '*.config.ts', '.eslintrc.js'],
      rules: {
        // Disable naming conventions for config files
        '@typescript-eslint/naming-convention': 'off'
      }
    },

    // API route files (may need snake_case for database compatibility)
    {
      files: ['**/routes/**/*.ts', '**/api/**/*.ts'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          // Allow snake_case for database field names in API routes
          {
            selector: 'objectLiteralProperty',
            format: ['camelCase', 'snake_case', 'UPPER_CASE'],
            leadingUnderscore: 'allow'
          }
        ]
      }
    }
  ],
  
  // =============================================
  // SETTINGS
  // =============================================
  settings: {
    react: {
      version: 'detect'
    }
  },
  
  // =============================================
  // PARSER OPTIONS
  // =============================================
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    },
    project: './tsconfig.json'
  }
};

/**
 * NAMING CONVENTION EXAMPLES
 * 
 * ✅ CORRECT EXAMPLES:
 * 
 * // Interfaces & Types
 * interface UserProfile { ... }
 * type BookingStatus = 'pending' | 'confirmed';
 * 
 * // Components
 * const BookingForm: React.FC = () => { ... };
 * export const PropertyCard = () => { ... };
 * 
 * // Variables & Functions
 * const userName = 'john_doe';
 * const isUserActive = true;
 * const handleSubmit = () => { ... };
 * 
 * // Constants
 * const MAX_FILE_SIZE = 1024 * 1024;
 * const API_ENDPOINTS = { ... };
 * 
 * // Hooks
 * const useBookingForm = () => { ... };
 * const useApiCall = () => { ... };
 * 
 * // Enums
 * enum BookingType {
 *   Morning = 'morning',
 *   FullDay = 'full_day'
 * }
 * 
 * ❌ INCORRECT EXAMPLES:
 * 
 * // Wrong interface naming
 * interface userProfile { ... }          // Should be: UserProfile
 * interface IUserProfile { ... }         // Avoid Hungarian notation
 * 
 * // Wrong component naming
 * const bookingForm = () => { ... };     // Should be: BookingForm
 * const booking_form = () => { ... };    // Should be: BookingForm
 * 
 * // Wrong variable naming
 * const UserName = 'john_doe';           // Should be: userName
 * const user_name = 'john_doe';          // Should be: userName
 * 
 * // Wrong constant naming
 * const maxFileSize = 1024;              // Should be: MAX_FILE_SIZE
 * const max_file_size = 1024;            // Should be: MAX_FILE_SIZE
 * 
 * // Wrong hook naming
 * const BookingFormHook = () => { ... }; // Should be: useBookingForm
 * const booking_form_hook = () => { ... }; // Should be: useBookingForm
 */