{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "build": "npm run build:frontend", "build:frontend": "vite build", "start": "npm run build:frontend && NODE_ENV=production tsx server/index.ts", "start:dev": "NODE_ENV=development tsx server/index.ts", "start:prod": "NODE_ENV=production tsx server/index.ts", "check": "tsc", "test": "vitest --config vitest.simple.config.ts", "test:integration": "vitest --config vitest.integration.config.ts", "test:unit": "vitest run", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:db": "tsx scripts/test-db-resilience.ts", "monitor:db": "tsx scripts/monitor-db-health.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/seed-database.ts", "migrate-production": "tsx scripts/migrate-production.ts", "seed-production-templates": "NODE_ENV=production tsx scripts/seed-production-templates.ts", "test-production-sms": "NODE_ENV=production tsx scripts/production-sms-test.ts", "simple-sms-test": "tsx scripts/simple-sms-test.ts", "test-sms-live": "tsx scripts/test-sms-live.ts", "test:whatsapp-booking": "tsx scripts/test-whatsapp-booking-flow.ts", "deadcode": "tsx scripts/detect-dead-code.ts", "deadcode:eslint": "eslint --config .eslintrc.deadcode.cjs --ext .ts,.tsx server/ client/ shared/", "deadcode:fix": "eslint --config .eslintrc.deadcode.cjs --ext .ts,.tsx --fix server/ client/ shared/", "deadcode:report": "tsx scripts/detect-dead-code.ts > dead-code-analysis.txt", "test-sms-simple": "tsx scripts/simple-twilio-test.ts", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down"}, "dependencies": {"@esbuild/linux-x64": "^0.25.8", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.18", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "date-fns": "^3.6.0", "dotenv": "^17.0.1", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^2.0.1", "next-themes": "^0.4.6", "node-cron": "^4.2.1", "openid-client": "^6.6.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "postcss": "^8.4.47", "razorpay": "^2.9.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "rollup": "^4.45.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.1", "tw-animate-css": "^1.2.5", "twilio": "^5.7.1", "typescript": "5.6.3", "vaul": "^1.1.2", "vite": "^5.4.19", "wouter": "^3.3.5", "ws": "^8.18.2", "zod": "^3.25.7", "zod-validation-error": "^3.4.1"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@playwright/test": "^1.40.0", "@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.0", "@types/compression": "^1.8.1", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.18", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/node-cron": "^3.0.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.2", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.38.0", "@vitest/coverage-v8": "^1.0.0", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-unused-imports": "^4.1.4", "jsdom": "^23.0.0", "msw": "^2.0.0", "supertest": "^6.3.0", "vitest": "^1.0.0"}, "optionalDependencies": {"@esbuild/darwin-arm64": "^0.25.7", "@rollup/rollup-linux-x64-gnu": "^4.45.1", "bufferutil": "^4.0.8"}, "description": "A full-stack web application for farmhouse rental listings built with React, TypeScript, Express, and PostgreSQL.", "main": "postcss.config.js", "directories": {"doc": "docs", "test": "tests"}, "repository": {"type": "git", "url": "git+https://ayesha-usmani:<EMAIL>/EventLocale/Farmhouse.git"}, "keywords": [], "author": "", "bugs": {"url": "https://github.com/EventLocale/Farmhouse/issues"}, "homepage": "https://github.com/EventLocale/Farmhouse#readme"}