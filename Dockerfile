# Multi-stage Dockerfile for Farmhouse Rental Platform

# Stage 1: Build the frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY client/package*.json ./client/

# Install dependencies
RUN npm ci

# Copy client source code
COPY client/ ./client/
COPY shared/ ./shared/
COPY vite.config.ts ./
COPY tailwind.config.ts ./
COPY components.json ./
COPY postcss.config.js ./

# Build the frontend
RUN npm run build:frontend

# Stage 2: Setup the backend
FROM node:18-alpine AS backend

WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Install all dependencies (needed for build)
COPY package*.json ./
RUN npm ci

# Copy backend source
COPY server/ ./server/
COPY shared/ ./shared/
COPY drizzle.config.ts ./
COPY vite.config.ts ./
COPY tsconfig.json ./
COPY migrations/ ./migrations/

# Copy built frontend from previous stage
COPY --from=frontend-builder /app/dist/public ./dist/public

# Copy public directory with uploads
COPY public/ ./public/

# Create uploads directory
RUN mkdir -p uploads

# Expose port
EXPOSE 5000

# Set environment
ENV NODE_ENV=production

# Start the application with tsx
CMD ["npx", "tsx", "server/index.ts"]