# PowerShell script to fix WSL networking issues
# Run this in Windows PowerShell as Administrator

Write-Host "===============================================" -ForegroundColor Green
Write-Host "WSL Networking Fix for Farmhouse Application" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Step 1: Enable WSL port forwarding
Write-Host "`n🔧 Configuring WSL port forwarding..." -ForegroundColor Yellow
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=127.0.0.1
netsh interface portproxy add v4tov4 listenport=3000 listenaddress=0.0.0.0 connectport=3000 connectaddress=127.0.0.1

# Step 2: Configure Windows Firewall
Write-Host "`n🛡️ Configuring Windows Firewall..." -ForegroundColor Yellow
netsh advfirewall firewall add rule name="WSL Port 5000" dir=in action=allow protocol=TCP localport=5000
netsh advfirewall firewall add rule name="WSL Port 3000" dir=in action=allow protocol=TCP localport=3000

# Step 3: Get WSL IP address
Write-Host "`n📍 Getting WSL IP address..." -ForegroundColor Yellow
$wslIp = wsl hostname -I | ForEach-Object { $_.Trim() }
Write-Host "WSL IP Address: $wslIp" -ForegroundColor Cyan

# Step 4: Test connectivity
Write-Host "`n🧪 Testing connectivity..." -ForegroundColor Yellow
Test-NetConnection -ComputerName $wslIp -Port 5000 -InformationLevel Quiet

# Step 5: Start WSL server
Write-Host "`n🚀 Starting WSL server..." -ForegroundColor Yellow
wsl -d Ubuntu -u ayesha -e bash -c "cd /mnt/c/Users/<USER>/Documents/Farmhouse && node simple-server.mjs"

Write-Host "`n✅ Setup complete! Try opening:" -ForegroundColor Green
Write-Host "   http://localhost:5000" -ForegroundColor Cyan
Write-Host "   http://127.0.0.1:5000" -ForegroundColor Cyan
Write-Host "   http://$wslIp:5000" -ForegroundColor Cyan