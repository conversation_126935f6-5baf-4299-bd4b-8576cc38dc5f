#!/bin/bash

echo "🧪 Testing WhatsApp Integration..."
echo "=================================="

BASE_URL="http://localhost:5000/api/whatsapp"

echo -e "\n1. Testing Service Status:"
curl -s "$BASE_URL/status" | jq '.'

echo -e "\n2. Testing Help Message:"
curl -s -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{"Body": "help", "From": "whatsapp:+919876543210", "MessageSid": "test1"}' 
echo " ✅ Help message processed"

echo -e "\n3. Testing Booking Intent:"
curl -s -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{"Body": "book farmhouse for Dec 15-17, 8 people, Gurgaon", "From": "whatsapp:+919876543210", "MessageSid": "test2"}'
echo " ✅ Booking message processed"

echo -e "\n4. Testing Availability Check:"
curl -s -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{"Body": "check availability this weekend for 6 people", "From": "whatsapp:+919876543210", "MessageSid": "test3"}'
echo " ✅ Availability message processed"

echo -e "\n5. Testing Cancellation:"
curl -s -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{"Body": "cancel booking REF12345", "From": "whatsapp:+919876543210", "MessageSid": "test4"}'
echo " ✅ Cancellation message processed"

echo -e "\n6. Testing Unknown Message:"
curl -s -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{"Body": "random text here", "From": "whatsapp:+919876543210", "MessageSid": "test5"}'
echo " ✅ Unknown message processed"

echo -e "\n🎉 All tests completed!"
echo -e "Check your server logs to see the intelligent responses generated.\n"