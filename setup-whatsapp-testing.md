# WhatsApp Real Testing Setup Guide

## Option 1: <PERSON><PERSON><PERSON> (Easiest for Testing)

### Step 1: Enable Twilio WhatsApp Sandbox
1. Go to https://console.twilio.com/us1/develop/sms/settings/whatsapp-sandbox
2. Follow instructions to join sandbox (send "join <sandbox-code>" to <PERSON><PERSON><PERSON>'s WhatsApp number)
3. Get your sandbox WhatsApp number (usually `whatsapp:+14155238886`)

### Step 2: Update Your Environment
```bash
# Add to your .env file:
TWILIO_WHATSAPP_NUMBER=whatsapp:+14155238886  # Sandbox number
```

### Step 3: Set Webhook URL
In Twilio Console → WhatsApp Sandbox Settings:
- Set webhook URL to: `https://your-ngrok-url.ngrok.io/api/whatsapp/webhook`
- Set HTTP method to: POST

## Option 2: Use ngrok for Local Testing

### Step 1: Install ngrok
```bash
# Download from https://ngrok.com/download
# Or install via package manager
npm install -g ngrok
```

### Step 2: Expose Your Local Server
```bash
# In a new terminal, run:
ngrok http 5000

# Copy the HTTPS URL (like https://abc123.ngrok.io)
```

### Step 3: Update Twilio Webhook
In Twilio Console:
- Webhook URL: `https://your-ngrok-url.ngrok.io/api/whatsapp/webhook`
- Method: POST

## Option 3: WhatsApp Business API (Production)

### Requirements:
1. Facebook Business Manager account
2. WhatsApp Business API approval
3. Verified business phone number
4. Can take 2-7 days for approval

## Quick Test Commands

### Test Webhook Verification:
```bash
curl "http://localhost:5000/api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=farmhouse-webhook-2024&hub.challenge=test123"
# Should return: test123
```

### Test Message Processing:
```bash
# Booking test
curl -X POST "http://localhost:5000/api/whatsapp/webhook" \
  -H "Content-Type: application/json" \
  -d '{"Body": "book farmhouse for Dec 25-27, 10 people, Delhi", "From": "whatsapp:+************"}'

# Check logs to see parsed response
```

### Test Service Status:
```bash
curl "http://localhost:5000/api/whatsapp/status"
```

## What You'll See When Testing:

### In Your Logs:
```
[INFO] Parsed WhatsApp command
[INFO] Intent: booking, Confidence: 0.85  
[INFO] Entities: dates=Dec 25-27, guests=10, location=Delhi
[WARN] WhatsApp not configured - would send response...
```

### Response Messages:
Your system will generate contextual responses like:
- "Great! I'd love to help you book a farmhouse..."
- "Let me check availability for Dec 25-27..."
- "I'll help you cancel booking ABC123..."

## Troubleshooting:

### If webhook verification fails:
- Check your WHATSAPP_WEBHOOK_TOKEN matches
- Ensure ngrok URL is correct
- Verify Twilio webhook settings

### If messages aren't processed:
- Check server logs for errors
- Verify JSON format in curl commands
- Ensure server is running on port 5000

## Next Steps After Testing:
1. ✅ Verify all message intents work
2. ✅ Check entity extraction accuracy  
3. ✅ Test error handling
4. 🚀 Apply for WhatsApp Business API approval
5. 🚀 Deploy to production server