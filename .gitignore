node_modules
dist
.DS_Store
server/public
vite.config.ts.*
*.tar.gz

# Test results and reports (should not be committed)
test-results/
playwright-report/
coverage/
.nyc_output/

# Environment variables (should never be committed)
.env
.env.local
.env.development.local
.env.production.local
.env.test.local
.env.staging.local
.env.production
.env.development

# Environment files (never commit these!)
.env
.env.local
.env.*.local
*.env
*.env.backup
docker-compose.env

# Scripts with credentials
scripts/set-production-env.sh.local

# Sensitive configuration files
config/production.json
config/staging.json
secrets/
credentials/
attached_assets/

*.log