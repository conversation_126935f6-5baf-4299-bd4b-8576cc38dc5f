CREATE TABLE "otp_tokens" (
	"id" serial PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"code" text NOT NULL,
	"type" text NOT NULL,
	"attempts" integer DEFAULT 0 NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "owner_interest_requests" (
	"id" serial PRIMARY KEY NOT NULL,
	"full_name" text NOT NULL,
	"email" text NOT NULL,
	"phone" text NOT NULL,
	"property_location" text NOT NULL,
	"property_type" text NOT NULL,
	"property_size" text,
	"expected_revenue" text,
	"current_occupancy" text,
	"amenities" json DEFAULT '[]'::json NOT NULL,
	"property_description" text,
	"experience" text,
	"availability" text,
	"additional_info" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"contacted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "bookings" ALTER COLUMN "booking_date" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "special_requests" text;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "latitude" double precision;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "longitude" double precision;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "address" text;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "bio" text;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "terms_accepted" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "privacy_policy_accepted" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "cookie_policy_accepted" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "data_processing_consent" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "marketing_consent" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "consent_timestamp" timestamp;--> statement-breakpoint
CREATE INDEX "otp_tokens_identifier_idx" ON "otp_tokens" USING btree ("identifier");--> statement-breakpoint
CREATE INDEX "otp_tokens_type_idx" ON "otp_tokens" USING btree ("type");--> statement-breakpoint
CREATE INDEX "otp_tokens_expires_at_idx" ON "otp_tokens" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "otp_tokens_created_at_idx" ON "otp_tokens" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "owner_interest_requests_email_idx" ON "owner_interest_requests" USING btree ("email");--> statement-breakpoint
CREATE INDEX "owner_interest_requests_phone_idx" ON "owner_interest_requests" USING btree ("phone");--> statement-breakpoint
CREATE INDEX "owner_interest_requests_status_idx" ON "owner_interest_requests" USING btree ("status");--> statement-breakpoint
CREATE INDEX "owner_interest_requests_created_at_idx" ON "owner_interest_requests" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "bookings_property_id_idx" ON "bookings" USING btree ("property_id");--> statement-breakpoint
CREATE INDEX "bookings_user_id_idx" ON "bookings" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "bookings_booking_date_idx" ON "bookings" USING btree ("booking_date");--> statement-breakpoint
CREATE INDEX "bookings_status_idx" ON "bookings" USING btree ("status");--> statement-breakpoint
CREATE INDEX "bookings_availability_idx" ON "bookings" USING btree ("property_id","booking_date");--> statement-breakpoint
CREATE INDEX "bookings_created_at_idx" ON "bookings" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "properties_owner_id_idx" ON "properties" USING btree ("owner_id");--> statement-breakpoint
CREATE INDEX "properties_location_idx" ON "properties" USING btree ("location");--> statement-breakpoint
CREATE INDEX "properties_status_idx" ON "properties" USING btree ("status");--> statement-breakpoint
CREATE INDEX "properties_featured_idx" ON "properties" USING btree ("featured");--> statement-breakpoint
CREATE INDEX "properties_price_range_idx" ON "properties" USING btree ("half_day_price","full_day_price");--> statement-breakpoint
CREATE INDEX "properties_created_at_idx" ON "properties" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "reviews_property_id_idx" ON "reviews" USING btree ("property_id");--> statement-breakpoint
CREATE INDEX "reviews_user_id_idx" ON "reviews" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "reviews_booking_id_idx" ON "reviews" USING btree ("booking_id");--> statement-breakpoint
CREATE INDEX "reviews_rating_idx" ON "reviews" USING btree ("rating");--> statement-breakpoint
CREATE INDEX "reviews_created_at_idx" ON "reviews" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "users_phone_idx" ON "users" USING btree ("phone");--> statement-breakpoint
CREATE INDEX "users_role_idx" ON "users" USING btree ("role");--> statement-breakpoint
CREATE INDEX "users_created_at_idx" ON "users" USING btree ("created_at");--> statement-breakpoint
ALTER TABLE "public"."bookings" ALTER COLUMN "booking_type" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."booking_type";--> statement-breakpoint
CREATE TYPE "public"."booking_type" AS ENUM('morning', 'full_day');--> statement-breakpoint
ALTER TABLE "public"."bookings" ALTER COLUMN "booking_type" SET DATA TYPE "public"."booking_type" USING "booking_type"::"public"."booking_type";