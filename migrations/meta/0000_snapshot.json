{"id": "a7aaaf10-936a-4cc1-9d4a-17ff9bba0a8f", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.bookings": {"name": "bookings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_date": {"name": "booking_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "booking_type": {"name": "booking_type", "type": "booking_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "guests": {"name": "guests", "type": "integer", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "double precision", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'confirmed'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bookings_property_id_properties_id_fk": {"name": "bookings_property_id_properties_id_fk", "tableFrom": "bookings", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bookings_user_id_users_id_fk": {"name": "bookings_user_id_users_id_fk", "tableFrom": "bookings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "owner_id": {"name": "owner_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "half_day_price": {"name": "half_day_price", "type": "double precision", "primaryKey": false, "notNull": true}, "full_day_price": {"name": "full_day_price", "type": "double precision", "primaryKey": false, "notNull": true}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": true}, "bathrooms": {"name": "bathrooms", "type": "integer", "primaryKey": false, "notNull": true}, "amenities": {"name": "amenities", "type": "json", "primaryKey": false, "notNull": true}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"properties_owner_id_users_id_fk": {"name": "properties_owner_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_id": {"name": "booking_id", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "review_stars", "typeSchema": "public", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "owner_response": {"name": "owner_response", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reviews_property_id_properties_id_fk": {"name": "reviews_property_id_properties_id_fk", "tableFrom": "reviews", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_user_id_users_id_fk": {"name": "reviews_user_id_users_id_fk", "tableFrom": "reviews", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_booking_id_bookings_id_fk": {"name": "reviews_booking_id_bookings_id_fk", "tableFrom": "reviews", "tableTo": "bookings", "columnsFrom": ["booking_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.booking_type": {"name": "booking_type", "schema": "public", "values": ["12h", "24h"]}, "public.review_stars": {"name": "review_stars", "schema": "public", "values": ["1", "2", "3", "4", "5"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "owner"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}