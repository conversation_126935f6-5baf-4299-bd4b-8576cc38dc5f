{"id": "d930bdce-8783-43e9-94b6-74611f5bad6c", "prevId": "d02df747-4a8c-4cde-af6c-8cc9801c2757", "version": "7", "dialect": "postgresql", "tables": {"public.bookings": {"name": "bookings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_date": {"name": "booking_date", "type": "text", "primaryKey": false, "notNull": true}, "booking_type": {"name": "booking_type", "type": "booking_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "guests": {"name": "guests", "type": "integer", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "double precision", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'confirmed'"}, "special_requests": {"name": "special_requests", "type": "text", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "advance_amount": {"name": "advance_amount", "type": "integer", "primaryKey": false, "notNull": false}, "remaining_amount": {"name": "remaining_amount", "type": "integer", "primaryKey": false, "notNull": false}, "gst_amount": {"name": "gst_amount", "type": "integer", "primaryKey": false, "notNull": false}, "payment_due_date": {"name": "payment_due_date", "type": "text", "primaryKey": false, "notNull": false}, "payment_expiry": {"name": "payment_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"bookings_property_id_idx": {"name": "bookings_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_user_id_idx": {"name": "bookings_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_booking_date_idx": {"name": "bookings_booking_date_idx", "columns": [{"expression": "booking_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_status_idx": {"name": "bookings_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_payment_status_idx": {"name": "bookings_payment_status_idx", "columns": [{"expression": "payment_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_availability_idx": {"name": "bookings_availability_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "booking_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_created_at_idx": {"name": "bookings_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bookings_property_id_properties_id_fk": {"name": "bookings_property_id_properties_id_fk", "tableFrom": "bookings", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bookings_user_id_users_id_fk": {"name": "bookings_user_id_users_id_fk", "tableFrom": "bookings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.calendar_bookings": {"name": "calendar_bookings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'confirmed'"}, "booking_type": {"name": "booking_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'direct'"}, "guest_name": {"name": "guest_name", "type": "text", "primaryKey": false, "notNull": false}, "guest_phone": {"name": "guest_phone", "type": "text", "primaryKey": false, "notNull": false}, "guest_count": {"name": "guest_count", "type": "integer", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": false, "default": "'website'"}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"calendar_bookings_property_id_idx": {"name": "calendar_bookings_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_bookings_date_range_idx": {"name": "calendar_bookings_date_range_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_bookings_status_idx": {"name": "calendar_bookings_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_bookings_source_idx": {"name": "calendar_bookings_source_idx", "columns": [{"expression": "source", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_bookings_created_by_idx": {"name": "calendar_bookings_created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_bookings_overlap_idx": {"name": "calendar_bookings_overlap_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_bookings_created_at_idx": {"name": "calendar_bookings_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"calendar_bookings_property_id_properties_id_fk": {"name": "calendar_bookings_property_id_properties_id_fk", "tableFrom": "calendar_bookings", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "calendar_bookings_created_by_users_id_fk": {"name": "calendar_bookings_created_by_users_id_fk", "tableFrom": "calendar_bookings", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.calendar_sync_status": {"name": "calendar_sync_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "calendar_type": {"name": "calendar_type", "type": "text", "primaryKey": false, "notNull": true}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sync_token": {"name": "sync_token", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "webhook_url": {"name": "webhook_url", "type": "text", "primaryKey": false, "notNull": false}, "sync_settings": {"name": "sync_settings", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"calendar_sync_status_property_id_idx": {"name": "calendar_sync_status_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_sync_status_calendar_type_idx": {"name": "calendar_sync_status_calendar_type_idx", "columns": [{"expression": "calendar_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_sync_status_is_active_idx": {"name": "calendar_sync_status_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "calendar_sync_status_unique_idx": {"name": "calendar_sync_status_unique_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "calendar_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"calendar_sync_status_property_id_properties_id_fk": {"name": "calendar_sync_status_property_id_properties_id_fk", "tableFrom": "calendar_sync_status", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.device_verifications": {"name": "device_verifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "device_id": {"name": "device_id", "type": "text", "primaryKey": false, "notNull": true}, "device_fingerprint": {"name": "device_fingerprint", "type": "text", "primaryKey": false, "notNull": true}, "device_info": {"name": "device_info", "type": "json", "primaryKey": false, "notNull": false}, "verification_method": {"name": "verification_method", "type": "text", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"device_verifications_user_id_idx": {"name": "device_verifications_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "device_verifications_device_id_idx": {"name": "device_verifications_device_id_idx", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "device_verifications_is_verified_idx": {"name": "device_verifications_is_verified_idx", "columns": [{"expression": "is_verified", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"device_verifications_user_id_users_id_fk": {"name": "device_verifications_user_id_users_id_fk", "tableFrom": "device_verifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.encryption_keys": {"name": "encryption_keys", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key_id": {"name": "key_id", "type": "text", "primaryKey": false, "notNull": true}, "key_type": {"name": "key_type", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_key": {"name": "encrypted_key", "type": "text", "primaryKey": false, "notNull": true}, "key_version": {"name": "key_version", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"encryption_keys_key_id_idx": {"name": "encryption_keys_key_id_idx", "columns": [{"expression": "key_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "encryption_keys_is_active_idx": {"name": "encryption_keys_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"encryption_keys_key_id_unique": {"name": "encryption_keys_key_id_unique", "nullsNotDistinct": false, "columns": ["key_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.gst_rate_configurations": {"name": "gst_rate_configurations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_type": {"name": "service_type", "type": "text", "primaryKey": false, "notNull": true}, "hsn_sac_code": {"name": "hsn_sac_code", "type": "text", "primaryKey": false, "notNull": true}, "rate_structure": {"name": "rate_structure", "type": "json", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "text", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"gst_rate_config_service_type_idx": {"name": "gst_rate_config_service_type_idx", "columns": [{"expression": "service_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_rate_config_hsn_sac_idx": {"name": "gst_rate_config_hsn_sac_idx", "columns": [{"expression": "hsn_sac_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_rate_config_effective_dates_idx": {"name": "gst_rate_config_effective_dates_idx", "columns": [{"expression": "effective_from", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "effective_to", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_rate_config_unique_idx": {"name": "gst_rate_config_unique_idx", "columns": [{"expression": "service_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "hsn_sac_code", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "effective_from", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"gst_rate_configurations_created_by_users_id_fk": {"name": "gst_rate_configurations_created_by_users_id_fk", "tableFrom": "gst_rate_configurations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.gst_records": {"name": "gst_records", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "booking_id": {"name": "booking_id", "type": "integer", "primaryKey": false, "notNull": false}, "base_amount": {"name": "base_amount", "type": "integer", "primaryKey": false, "notNull": true}, "transaction_type": {"name": "transaction_type", "type": "text", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "text", "primaryKey": false, "notNull": true}, "hsn_sac_code": {"name": "hsn_sac_code", "type": "text", "primaryKey": false, "notNull": true}, "supplier_gstin": {"name": "supplier_gstin", "type": "text", "primaryKey": false, "notNull": false}, "recipient_gstin": {"name": "recipient_gstin", "type": "text", "primaryKey": false, "notNull": false}, "supplier_state": {"name": "supplier_state", "type": "text", "primaryKey": false, "notNull": true}, "recipient_state": {"name": "recipient_state", "type": "text", "primaryKey": false, "notNull": true}, "place_of_supply": {"name": "place_of_supply", "type": "text", "primaryKey": false, "notNull": true}, "gst_rate_config_id": {"name": "gst_rate_config_id", "type": "integer", "primaryKey": false, "notNull": false}, "applicable_date": {"name": "applicable_date", "type": "text", "primaryKey": false, "notNull": true}, "cgst_rate": {"name": "cgst_rate", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "sgst_rate": {"name": "sgst_rate", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "cgst_amount": {"name": "cgst_amount", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "sgst_amount": {"name": "sgst_amount", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "igst_rate": {"name": "igst_rate", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "igst_amount": {"name": "igst_amount", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cess_rate": {"name": "cess_rate", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "cess_amount": {"name": "cess_amount", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_gst": {"name": "total_gst", "type": "integer", "primaryKey": false, "notNull": true}, "total_amount": {"name": "total_amount", "type": "integer", "primaryKey": false, "notNull": true}, "invoice_number": {"name": "invoice_number", "type": "text", "primaryKey": false, "notNull": false}, "invoice_date": {"name": "invoice_date", "type": "text", "primaryKey": false, "notNull": false}, "irn": {"name": "irn", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"gst_records_booking_id_idx": {"name": "gst_records_booking_id_idx", "columns": [{"expression": "booking_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_records_transaction_type_idx": {"name": "gst_records_transaction_type_idx", "columns": [{"expression": "transaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_records_service_type_idx": {"name": "gst_records_service_type_idx", "columns": [{"expression": "service_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_records_supplier_state_idx": {"name": "gst_records_supplier_state_idx", "columns": [{"expression": "supplier_state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_records_applicable_date_idx": {"name": "gst_records_applicable_date_idx", "columns": [{"expression": "applicable_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gst_records_created_at_idx": {"name": "gst_records_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"gst_records_booking_id_bookings_id_fk": {"name": "gst_records_booking_id_bookings_id_fk", "tableFrom": "gst_records", "tableTo": "bookings", "columnsFrom": ["booking_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "gst_records_gst_rate_config_id_gst_rate_configurations_id_fk": {"name": "gst_records_gst_rate_config_id_gst_rate_configurations_id_fk", "tableFrom": "gst_records", "tableTo": "gst_rate_configurations", "columnsFrom": ["gst_rate_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.idempotency_keys": {"name": "idempotency_keys", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "resource_type": {"name": "resource_type", "type": "text", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "integer", "primaryKey": false, "notNull": false}, "response_data": {"name": "response_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"idempotency_keys_key_idx": {"name": "idempotency_keys_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idempotency_keys_expires_at_idx": {"name": "idempotency_keys_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"idempotency_keys_key_unique": {"name": "idempotency_keys_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.otp_tokens": {"name": "otp_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"otp_tokens_identifier_idx": {"name": "otp_tokens_identifier_idx", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "otp_tokens_type_idx": {"name": "otp_tokens_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "otp_tokens_expires_at_idx": {"name": "otp_tokens_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "otp_tokens_created_at_idx": {"name": "otp_tokens_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.owner_interest_requests": {"name": "owner_interest_requests", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "property_location": {"name": "property_location", "type": "text", "primaryKey": false, "notNull": true}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": true}, "property_size": {"name": "property_size", "type": "text", "primaryKey": false, "notNull": false}, "expected_revenue": {"name": "expected_revenue", "type": "text", "primaryKey": false, "notNull": false}, "current_occupancy": {"name": "current_occupancy", "type": "text", "primaryKey": false, "notNull": false}, "amenities": {"name": "amenities", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "property_description": {"name": "property_description", "type": "text", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "text", "primaryKey": false, "notNull": false}, "additional_info": {"name": "additional_info", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "contacted_at": {"name": "contacted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"owner_interest_requests_email_idx": {"name": "owner_interest_requests_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "owner_interest_requests_phone_idx": {"name": "owner_interest_requests_phone_idx", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "owner_interest_requests_status_idx": {"name": "owner_interest_requests_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "owner_interest_requests_created_at_idx": {"name": "owner_interest_requests_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_audit_logs": {"name": "payment_audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "payment_order_id": {"name": "payment_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "payment_transaction_id": {"name": "payment_transaction_id", "type": "integer", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "actor_type": {"name": "actor_type", "type": "text", "primaryKey": false, "notNull": true}, "actor_id": {"name": "actor_id", "type": "integer", "primaryKey": false, "notNull": false}, "actor_ip": {"name": "actor_ip", "type": "text", "primaryKey": false, "notNull": false}, "actor_user_agent": {"name": "actor_user_agent", "type": "text", "primaryKey": false, "notNull": false}, "before_state": {"name": "before_state", "type": "json", "primaryKey": false, "notNull": false}, "after_state": {"name": "after_state", "type": "json", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "security_context": {"name": "security_context", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payment_audit_logs_payment_order_id_idx": {"name": "payment_audit_logs_payment_order_id_idx", "columns": [{"expression": "payment_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_audit_logs_actor_id_idx": {"name": "payment_audit_logs_actor_id_idx", "columns": [{"expression": "actor_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_audit_logs_action_idx": {"name": "payment_audit_logs_action_idx", "columns": [{"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_audit_logs_created_at_idx": {"name": "payment_audit_logs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payment_audit_logs_payment_order_id_payment_orders_id_fk": {"name": "payment_audit_logs_payment_order_id_payment_orders_id_fk", "tableFrom": "payment_audit_logs", "tableTo": "payment_orders", "columnsFrom": ["payment_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_audit_logs_payment_transaction_id_payment_transactions_id_fk": {"name": "payment_audit_logs_payment_transaction_id_payment_transactions_id_fk", "tableFrom": "payment_audit_logs", "tableTo": "payment_transactions", "columnsFrom": ["payment_transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_orders": {"name": "payment_orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "razorpay_order_id": {"name": "razorpay_order_id", "type": "text", "primaryKey": false, "notNull": true}, "booking_id": {"name": "booking_id", "type": "integer", "primaryKey": false, "notNull": false}, "idempotency_key": {"name": "idempotency_key", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'INR'"}, "receipt": {"name": "receipt", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'created'"}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payment_orders_booking_id_idx": {"name": "payment_orders_booking_id_idx", "columns": [{"expression": "booking_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_orders_status_idx": {"name": "payment_orders_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_orders_idempotency_key_idx": {"name": "payment_orders_idempotency_key_idx", "columns": [{"expression": "idempotency_key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_orders_created_at_idx": {"name": "payment_orders_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payment_orders_booking_id_bookings_id_fk": {"name": "payment_orders_booking_id_bookings_id_fk", "tableFrom": "payment_orders", "tableTo": "bookings", "columnsFrom": ["booking_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payment_orders_razorpay_order_id_unique": {"name": "payment_orders_razorpay_order_id_unique", "nullsNotDistinct": false, "columns": ["razorpay_order_id"]}, "payment_orders_idempotency_key_unique": {"name": "payment_orders_idempotency_key_unique", "nullsNotDistinct": false, "columns": ["idempotency_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_roles": {"name": "payment_roles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payment_roles_name_unique": {"name": "payment_roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_transactions": {"name": "payment_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "payment_order_id": {"name": "payment_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "razorpay_payment_id": {"name": "razorpay_payment_id", "type": "text", "primaryKey": false, "notNull": false}, "razorpay_signature_hash": {"name": "razorpay_signature_hash", "type": "text", "primaryKey": false, "notNull": false}, "signature_verification_status": {"name": "signature_verification_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "text", "primaryKey": false, "notNull": false}, "bank": {"name": "bank", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'INR'"}, "exchange_rate": {"name": "exchange_rate", "type": "double precision", "primaryKey": false, "notNull": false}, "base_currency": {"name": "base_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'INR'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'initiated'"}, "failure_code": {"name": "failure_code", "type": "text", "primaryKey": false, "notNull": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "gateway_response": {"name": "gateway_response", "type": "json", "primaryKey": false, "notNull": false}, "encryption_key_id": {"name": "encryption_key_id", "type": "text", "primaryKey": false, "notNull": false}, "sensitive_data_hash": {"name": "sensitive_data_hash", "type": "text", "primaryKey": false, "notNull": false}, "security_flags": {"name": "security_flags", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payment_transactions_payment_order_id_idx": {"name": "payment_transactions_payment_order_id_idx", "columns": [{"expression": "payment_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_transactions_status_idx": {"name": "payment_transactions_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_transactions_razorpay_payment_id_idx": {"name": "payment_transactions_razorpay_payment_id_idx", "columns": [{"expression": "razorpay_payment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_transactions_created_at_idx": {"name": "payment_transactions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payment_transactions_payment_order_id_payment_orders_id_fk": {"name": "payment_transactions_payment_order_id_payment_orders_id_fk", "tableFrom": "payment_transactions", "tableTo": "payment_orders", "columnsFrom": ["payment_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payment_transactions_razorpay_payment_id_unique": {"name": "payment_transactions_razorpay_payment_id_unique", "nullsNotDistinct": false, "columns": ["razorpay_payment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "owner_id": {"name": "owner_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "half_day_price": {"name": "half_day_price", "type": "double precision", "primaryKey": false, "notNull": true}, "full_day_price": {"name": "full_day_price", "type": "double precision", "primaryKey": false, "notNull": true}, "weekday_half_day_price": {"name": "weekday_half_day_price", "type": "double precision", "primaryKey": false, "notNull": false}, "weekday_full_day_price": {"name": "weekday_full_day_price", "type": "double precision", "primaryKey": false, "notNull": false}, "weekend_half_day_price": {"name": "weekend_half_day_price", "type": "double precision", "primaryKey": false, "notNull": false}, "weekend_full_day_price": {"name": "weekend_full_day_price", "type": "double precision", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": true}, "bathrooms": {"name": "bathrooms", "type": "integer", "primaryKey": false, "notNull": true}, "amenities": {"name": "amenities", "type": "json", "primaryKey": false, "notNull": true}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": true}, "videos": {"name": "videos", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "latitude": {"name": "latitude", "type": "double precision", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "double precision", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"properties_owner_id_idx": {"name": "properties_owner_id_idx", "columns": [{"expression": "owner_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_location_idx": {"name": "properties_location_idx", "columns": [{"expression": "location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_status_idx": {"name": "properties_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_featured_idx": {"name": "properties_featured_idx", "columns": [{"expression": "featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_price_range_idx": {"name": "properties_price_range_idx", "columns": [{"expression": "half_day_price", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "full_day_price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_created_at_idx": {"name": "properties_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"properties_owner_id_users_id_fk": {"name": "properties_owner_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_id": {"name": "booking_id", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "review_stars", "typeSchema": "public", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "owner_response": {"name": "owner_response", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"reviews_property_id_idx": {"name": "reviews_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_user_id_idx": {"name": "reviews_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_booking_id_idx": {"name": "reviews_booking_id_idx", "columns": [{"expression": "booking_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_rating_idx": {"name": "reviews_rating_idx", "columns": [{"expression": "rating", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_created_at_idx": {"name": "reviews_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"reviews_property_id_properties_id_fk": {"name": "reviews_property_id_properties_id_fk", "tableFrom": "reviews", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_user_id_users_id_fk": {"name": "reviews_user_id_users_id_fk", "tableFrom": "reviews", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_booking_id_bookings_id_fk": {"name": "reviews_booking_id_bookings_id_fk", "tableFrom": "reviews", "tableTo": "bookings", "columnsFrom": ["booking_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.security_incidents": {"name": "security_incidents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "incident_type": {"name": "incident_type", "type": "text", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "source_ip": {"name": "source_ip", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "payment_order_id": {"name": "payment_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "incident_data": {"name": "incident_data", "type": "json", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'open'"}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "resolved_by": {"name": "resolved_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"security_incidents_type_idx": {"name": "security_incidents_type_idx", "columns": [{"expression": "incident_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_incidents_severity_idx": {"name": "security_incidents_severity_idx", "columns": [{"expression": "severity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_incidents_status_idx": {"name": "security_incidents_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_incidents_created_at_idx": {"name": "security_incidents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"security_incidents_user_id_users_id_fk": {"name": "security_incidents_user_id_users_id_fk", "tableFrom": "security_incidents", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "security_incidents_payment_order_id_payment_orders_id_fk": {"name": "security_incidents_payment_order_id_payment_orders_id_fk", "tableFrom": "security_incidents", "tableTo": "payment_orders", "columnsFrom": ["payment_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "security_incidents_resolved_by_users_id_fk": {"name": "security_incidents_resolved_by_users_id_fk", "tableFrom": "security_incidents", "tableTo": "users", "columnsFrom": ["resolved_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.security_sessions": {"name": "security_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "session_type": {"name": "session_type", "type": "text", "primaryKey": false, "notNull": true}, "session_data": {"name": "session_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {"security_sessions_user_id_idx": {"name": "security_sessions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_sessions_session_type_idx": {"name": "security_sessions_session_type_idx", "columns": [{"expression": "session_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_sessions_expires_at_idx": {"name": "security_sessions_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"security_sessions_user_id_users_id_fk": {"name": "security_sessions_user_id_users_id_fk", "tableFrom": "security_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_logs": {"name": "sms_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "template_id": {"name": "template_id", "type": "integer", "primaryKey": false, "notNull": false}, "recipient_phone": {"name": "recipient_phone", "type": "text", "primaryKey": false, "notNull": true}, "message_content": {"name": "message_content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "twilio_message_sid": {"name": "twilio_message_sid", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"sms_logs_template_id_idx": {"name": "sms_logs_template_id_idx", "columns": [{"expression": "template_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_logs_recipient_phone_idx": {"name": "sms_logs_recipient_phone_idx", "columns": [{"expression": "recipient_phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_logs_status_idx": {"name": "sms_logs_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_logs_created_at_idx": {"name": "sms_logs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sms_logs_template_id_sms_templates_id_fk": {"name": "sms_logs_template_id_sms_templates_id_fk", "tableFrom": "sms_logs", "tableTo": "sms_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_templates": {"name": "sms_templates", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "dlt_template_id": {"name": "dlt_template_id", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "default": "'transactional'"}, "variables": {"name": "variables", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"sms_templates_key_idx": {"name": "sms_templates_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_templates_status_idx": {"name": "sms_templates_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_templates_category_idx": {"name": "sms_templates_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_templates_created_at_idx": {"name": "sms_templates_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sms_templates_key_unique": {"name": "sms_templates_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_payment_roles": {"name": "user_payment_roles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": false}, "granted_by": {"name": "granted_by", "type": "integer", "primaryKey": false, "notNull": false}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {"user_payment_roles_unique_idx": {"name": "user_payment_roles_unique_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_payment_roles_user_id_users_id_fk": {"name": "user_payment_roles_user_id_users_id_fk", "tableFrom": "user_payment_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_payment_roles_role_id_payment_roles_id_fk": {"name": "user_payment_roles_role_id_payment_roles_id_fk", "tableFrom": "user_payment_roles", "tableTo": "payment_roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_payment_roles_granted_by_users_id_fk": {"name": "user_payment_roles_granted_by_users_id_fk", "tableFrom": "user_payment_roles", "tableTo": "users", "columnsFrom": ["granted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "terms_accepted": {"name": "terms_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "privacy_policy_accepted": {"name": "privacy_policy_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cookie_policy_accepted": {"name": "cookie_policy_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data_processing_consent": {"name": "data_processing_consent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "marketing_consent": {"name": "marketing_consent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "consent_timestamp": {"name": "consent_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_phone_idx": {"name": "users_phone_idx", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_events": {"name": "webhook_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "event_id": {"name": "event_id", "type": "text", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "json", "primaryKey": false, "notNull": true}, "signature_hash": {"name": "signature_hash", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'received'"}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"webhook_events_event_id_idx": {"name": "webhook_events_event_id_idx", "columns": [{"expression": "event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "webhook_events_status_idx": {"name": "webhook_events_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "webhook_events_created_at_idx": {"name": "webhook_events_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"webhook_events_event_id_unique": {"name": "webhook_events_event_id_unique", "nullsNotDistinct": false, "columns": ["event_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.whatsapp_conversations": {"name": "whatsapp_conversations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": true}, "conversation_state": {"name": "conversation_state", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"whatsapp_conversations_property_id_idx": {"name": "whatsapp_conversations_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "whatsapp_conversations_phone_number_idx": {"name": "whatsapp_conversations_phone_number_idx", "columns": [{"expression": "phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "whatsapp_conversations_is_active_idx": {"name": "whatsapp_conversations_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "whatsapp_conversations_last_message_at_idx": {"name": "whatsapp_conversations_last_message_at_idx", "columns": [{"expression": "last_message_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "whatsapp_conversations_unique_idx": {"name": "whatsapp_conversations_unique_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"whatsapp_conversations_property_id_properties_id_fk": {"name": "whatsapp_conversations_property_id_properties_id_fk", "tableFrom": "whatsapp_conversations", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.booking_type": {"name": "booking_type", "schema": "public", "values": ["morning", "full_day"]}, "public.review_stars": {"name": "review_stars", "schema": "public", "values": ["1", "2", "3", "4", "5"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "owner"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}