{"id": "d02df747-4a8c-4cde-af6c-8cc9801c2757", "prevId": "a7aaaf10-936a-4cc1-9d4a-17ff9bba0a8f", "version": "7", "dialect": "postgresql", "tables": {"public.bookings": {"name": "bookings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_date": {"name": "booking_date", "type": "text", "primaryKey": false, "notNull": true}, "booking_type": {"name": "booking_type", "type": "booking_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "guests": {"name": "guests", "type": "integer", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "double precision", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'confirmed'"}, "special_requests": {"name": "special_requests", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"bookings_property_id_idx": {"name": "bookings_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_user_id_idx": {"name": "bookings_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_booking_date_idx": {"name": "bookings_booking_date_idx", "columns": [{"expression": "booking_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_status_idx": {"name": "bookings_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_availability_idx": {"name": "bookings_availability_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "booking_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookings_created_at_idx": {"name": "bookings_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bookings_property_id_properties_id_fk": {"name": "bookings_property_id_properties_id_fk", "tableFrom": "bookings", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bookings_user_id_users_id_fk": {"name": "bookings_user_id_users_id_fk", "tableFrom": "bookings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.otp_tokens": {"name": "otp_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"otp_tokens_identifier_idx": {"name": "otp_tokens_identifier_idx", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "otp_tokens_type_idx": {"name": "otp_tokens_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "otp_tokens_expires_at_idx": {"name": "otp_tokens_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "otp_tokens_created_at_idx": {"name": "otp_tokens_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.owner_interest_requests": {"name": "owner_interest_requests", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "property_location": {"name": "property_location", "type": "text", "primaryKey": false, "notNull": true}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": true}, "property_size": {"name": "property_size", "type": "text", "primaryKey": false, "notNull": false}, "expected_revenue": {"name": "expected_revenue", "type": "text", "primaryKey": false, "notNull": false}, "current_occupancy": {"name": "current_occupancy", "type": "text", "primaryKey": false, "notNull": false}, "amenities": {"name": "amenities", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "property_description": {"name": "property_description", "type": "text", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "text", "primaryKey": false, "notNull": false}, "additional_info": {"name": "additional_info", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "contacted_at": {"name": "contacted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"owner_interest_requests_email_idx": {"name": "owner_interest_requests_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "owner_interest_requests_phone_idx": {"name": "owner_interest_requests_phone_idx", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "owner_interest_requests_status_idx": {"name": "owner_interest_requests_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "owner_interest_requests_created_at_idx": {"name": "owner_interest_requests_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "owner_id": {"name": "owner_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "half_day_price": {"name": "half_day_price", "type": "double precision", "primaryKey": false, "notNull": true}, "full_day_price": {"name": "full_day_price", "type": "double precision", "primaryKey": false, "notNull": true}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": true}, "bathrooms": {"name": "bathrooms", "type": "integer", "primaryKey": false, "notNull": true}, "amenities": {"name": "amenities", "type": "json", "primaryKey": false, "notNull": true}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "latitude": {"name": "latitude", "type": "double precision", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "double precision", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"properties_owner_id_idx": {"name": "properties_owner_id_idx", "columns": [{"expression": "owner_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_location_idx": {"name": "properties_location_idx", "columns": [{"expression": "location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_status_idx": {"name": "properties_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_featured_idx": {"name": "properties_featured_idx", "columns": [{"expression": "featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_price_range_idx": {"name": "properties_price_range_idx", "columns": [{"expression": "half_day_price", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "full_day_price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_created_at_idx": {"name": "properties_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"properties_owner_id_users_id_fk": {"name": "properties_owner_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_id": {"name": "booking_id", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "review_stars", "typeSchema": "public", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "owner_response": {"name": "owner_response", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"reviews_property_id_idx": {"name": "reviews_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_user_id_idx": {"name": "reviews_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_booking_id_idx": {"name": "reviews_booking_id_idx", "columns": [{"expression": "booking_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_rating_idx": {"name": "reviews_rating_idx", "columns": [{"expression": "rating", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reviews_created_at_idx": {"name": "reviews_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"reviews_property_id_properties_id_fk": {"name": "reviews_property_id_properties_id_fk", "tableFrom": "reviews", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_user_id_users_id_fk": {"name": "reviews_user_id_users_id_fk", "tableFrom": "reviews", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_booking_id_bookings_id_fk": {"name": "reviews_booking_id_bookings_id_fk", "tableFrom": "reviews", "tableTo": "bookings", "columnsFrom": ["booking_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "terms_accepted": {"name": "terms_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "privacy_policy_accepted": {"name": "privacy_policy_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cookie_policy_accepted": {"name": "cookie_policy_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data_processing_consent": {"name": "data_processing_consent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "marketing_consent": {"name": "marketing_consent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "consent_timestamp": {"name": "consent_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_phone_idx": {"name": "users_phone_idx", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.booking_type": {"name": "booking_type", "schema": "public", "values": ["morning", "full_day"]}, "public.review_stars": {"name": "review_stars", "schema": "public", "values": ["1", "2", "3", "4", "5"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "owner"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}