-- SMS Template System Migration
-- This migration adds SMS template management and logging capabilities

-- SMS Templates Table
CREATE TABLE "sms_templates" (
    "id" serial PRIMARY KEY,
    "key" text NOT NULL UNIQUE,
    "name" text NOT NULL,
    "content" text NOT NULL,
    "dlt_template_id" text NOT NULL,
    "category" text NOT NULL DEFAULT 'transactional',
    "variables" json NOT NULL DEFAULT '[]',
    "status" text NOT NULL DEFAULT 'active',
    "created_at" timestamp DEFAULT now() NOT NULL,
    "updated_at" timestamp DEFAULT now() NOT NULL
);

-- SMS Logs Table for audit trail
CREATE TABLE "sms_logs" (
    "id" serial PRIMARY KEY,
    "template_id" integer REFERENCES "sms_templates"("id"),
    "recipient_phone" text NOT NULL,
    "message_content" text NOT NULL,
    "status" text NOT NULL DEFAULT 'pending',
    "twilio_message_sid" text,
    "error_message" text,
    "created_at" timestamp DEFAULT now() NOT NULL
);

-- Indexes for SMS Templates
CREATE INDEX "sms_templates_key_idx" ON "sms_templates" ("key");
CREATE INDEX "sms_templates_status_idx" ON "sms_templates" ("status");
CREATE INDEX "sms_templates_category_idx" ON "sms_templates" ("category");
CREATE INDEX "sms_templates_created_at_idx" ON "sms_templates" ("created_at");

-- Indexes for SMS Logs
CREATE INDEX "sms_logs_template_id_idx" ON "sms_logs" ("template_id");
CREATE INDEX "sms_logs_recipient_phone_idx" ON "sms_logs" ("recipient_phone");
CREATE INDEX "sms_logs_status_idx" ON "sms_logs" ("status");
CREATE INDEX "sms_logs_created_at_idx" ON "sms_logs" ("created_at");

-- Composite indexes for common query patterns
CREATE INDEX "sms_logs_template_status_idx" ON "sms_logs" ("template_id", "status", "created_at");
CREATE INDEX "sms_logs_phone_date_idx" ON "sms_logs" ("recipient_phone", "created_at" DESC);

-- Insert initial booking confirmation template
INSERT INTO "sms_templates" ("key", "name", "content", "dlt_template_id", "category", "variables", "status") 
VALUES (
    'booking_confirmation',
    'BKAFARM_BOOKINGCONFIRM', 
    'BookAFarm booking for {#var#} on {#var#} confirmed. Thank you for choosing us!',
    '1207175138826492810',
    'transactional',
    '["property_name", "booking_date"]',
    'active'
);

-- Insert existing payment confirmation template for reference
INSERT INTO "sms_templates" ("key", "name", "content", "dlt_template_id", "category", "variables", "status") 
VALUES (
    'payment_confirmation',
    'BKAFARM_PAYMENTCONFIRM',
    'Your payment of Rs. {#var#} for booking {#var#} has been received successfully. Thank you for choosing BookAFarm.',
    '1207175138762110631',
    'transactional',
    '["amount", "property_name"]',
    'active'
);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_sms_template_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updated_at on sms_templates
CREATE TRIGGER sms_templates_updated_at_trigger
    BEFORE UPDATE ON "sms_templates"
    FOR EACH ROW EXECUTE FUNCTION update_sms_template_updated_at();

-- Function to get active template by key
CREATE OR REPLACE FUNCTION get_active_template_by_key(template_key text)
RETURNS TABLE (
    id integer,
    key text,
    name text,
    content text,
    dlt_template_id text,
    category text,
    variables json,
    status text,
    created_at timestamp,
    updated_at timestamp
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id, t.key, t.name, t.content, t.dlt_template_id, 
        t.category, t.variables, t.status, t.created_at, t.updated_at
    FROM sms_templates t
    WHERE t.key = template_key AND t.status = 'active'
    ORDER BY t.updated_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to log SMS sends
CREATE OR REPLACE FUNCTION log_sms_send(
    p_template_id integer,
    p_recipient_phone text,
    p_message_content text,
    p_twilio_message_sid text DEFAULT NULL,
    p_status text DEFAULT 'sent',
    p_error_message text DEFAULT NULL
)
RETURNS integer AS $$
DECLARE
    log_id integer;
BEGIN
    INSERT INTO sms_logs (template_id, recipient_phone, message_content, twilio_message_sid, status, error_message)
    VALUES (p_template_id, p_recipient_phone, p_message_content, p_twilio_message_sid, p_status, p_error_message)
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- View for SMS template analytics
CREATE VIEW "sms_template_analytics" AS
SELECT 
    st.id,
    st.key,
    st.name,
    st.status,
    COUNT(sl.id) as total_sends,
    COUNT(CASE WHEN sl.status = 'sent' THEN 1 END) as successful_sends,
    COUNT(CASE WHEN sl.status = 'failed' THEN 1 END) as failed_sends,
    ROUND(
        (COUNT(CASE WHEN sl.status = 'sent' THEN 1 END) * 100.0 / NULLIF(COUNT(sl.id), 0)), 2
    ) as success_rate,
    MAX(sl.created_at) as last_sent_at,
    MIN(sl.created_at) as first_sent_at
FROM sms_templates st
LEFT JOIN sms_logs sl ON st.id = sl.template_id
GROUP BY st.id, st.key, st.name, st.status
ORDER BY total_sends DESC;

-- Comments for documentation
COMMENT ON TABLE "sms_templates" IS 'Storage for SMS message templates with DLT compliance';
COMMENT ON TABLE "sms_logs" IS 'Audit trail for all SMS messages sent through the system';
COMMENT ON FUNCTION "get_active_template_by_key" IS 'Retrieves active SMS template by key for sending messages';
COMMENT ON FUNCTION "log_sms_send" IS 'Logs SMS send attempts with status and error tracking';
COMMENT ON VIEW "sms_template_analytics" IS 'Analytics view for SMS template usage and success rates';