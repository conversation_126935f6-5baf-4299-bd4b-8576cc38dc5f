-- Rollback script for 0001_add_performance_indexes.sql
-- This script drops all performance indexes added in the forward migration
-- Safe to execute - only removes indexes, no data loss

-- Drop composite indexes first
DROP INDEX IF EXISTS idx_properties_price_search;

-- Drop individual property indexes
DROP INDEX IF EXISTS idx_properties_location;
DROP INDEX IF EXISTS idx_properties_featured;
DROP INDEX IF EXISTS idx_properties_owner_id;
DROP INDEX IF EXISTS idx_properties_status;

-- Drop booking indexes
DROP INDEX IF EXISTS idx_bookings_property_date;
DROP INDEX IF EXISTS idx_bookings_user_id;
DROP INDEX IF EXISTS idx_bookings_date;
DROP INDEX IF EXISTS idx_bookings_status;

-- Drop user indexes
DROP INDEX IF EXISTS idx_users_email;

-- Drop review indexes
DROP INDEX IF EXISTS idx_reviews_property_id;
DROP INDEX IF EXISTS idx_reviews_user_id;

-- Note: Rolling back this migration will impact query performance
-- Consider the performance implications before executing this rollback