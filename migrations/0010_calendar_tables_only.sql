-- Manual migration for calendar tables only
CREATE TABLE IF NOT EXISTS "calendar_bookings" (
	"id" serial PRIMARY KEY NOT NULL,
	"property_id" integer NOT NULL,
	"start_date" text NOT NULL,
	"end_date" text NOT NULL,
	"status" text DEFAULT 'confirmed' NOT NULL,
	"booking_type" text DEFAULT 'direct',
	"guest_name" text,
	"guest_phone" text,
	"guest_count" integer,
	"notes" text,
	"source" text DEFAULT 'website',
	"external_id" text,
	"created_by" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "calendar_sync_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"property_id" integer NOT NULL,
	"calendar_type" text NOT NULL,
	"last_sync_at" timestamp,
	"sync_token" text,
	"is_active" boolean DEFAULT true,
	"webhook_url" text,
	"sync_settings" json DEFAULT '{}'::json,
	"error_message" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "whatsapp_conversations" (
	"id" serial PRIMARY KEY NOT NULL,
	"property_id" integer NOT NULL,
	"phone_number" text NOT NULL,
	"conversation_state" json DEFAULT '{}'::json,
	"last_message_at" timestamp,
	"is_active" boolean DEFAULT true,
	"metadata" json DEFAULT '{}'::json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "calendar_bookings" ADD CONSTRAINT "calendar_bookings_property_id_properties_id_fk" 
FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;

ALTER TABLE "calendar_bookings" ADD CONSTRAINT "calendar_bookings_created_by_users_id_fk" 
FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;

ALTER TABLE "calendar_sync_status" ADD CONSTRAINT "calendar_sync_status_property_id_properties_id_fk" 
FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;

ALTER TABLE "whatsapp_conversations" ADD CONSTRAINT "whatsapp_conversations_property_id_properties_id_fk" 
FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;

-- Create indexes for calendar_bookings
CREATE INDEX IF NOT EXISTS "calendar_bookings_property_id_idx" ON "calendar_bookings" USING btree ("property_id");
CREATE INDEX IF NOT EXISTS "calendar_bookings_date_range_idx" ON "calendar_bookings" USING btree ("property_id","start_date","end_date");
CREATE INDEX IF NOT EXISTS "calendar_bookings_status_idx" ON "calendar_bookings" USING btree ("status");
CREATE INDEX IF NOT EXISTS "calendar_bookings_source_idx" ON "calendar_bookings" USING btree ("source");
CREATE INDEX IF NOT EXISTS "calendar_bookings_created_by_idx" ON "calendar_bookings" USING btree ("created_by");
CREATE INDEX IF NOT EXISTS "calendar_bookings_overlap_idx" ON "calendar_bookings" USING btree ("property_id","start_date","end_date");
CREATE INDEX IF NOT EXISTS "calendar_bookings_created_at_idx" ON "calendar_bookings" USING btree ("created_at");

-- Create indexes for calendar_sync_status
CREATE INDEX IF NOT EXISTS "calendar_sync_status_property_id_idx" ON "calendar_sync_status" USING btree ("property_id");
CREATE INDEX IF NOT EXISTS "calendar_sync_status_calendar_type_idx" ON "calendar_sync_status" USING btree ("calendar_type");
CREATE INDEX IF NOT EXISTS "calendar_sync_status_is_active_idx" ON "calendar_sync_status" USING btree ("is_active");
CREATE INDEX IF NOT EXISTS "calendar_sync_status_unique_idx" ON "calendar_sync_status" USING btree ("property_id","calendar_type");

-- Create indexes for whatsapp_conversations
CREATE INDEX IF NOT EXISTS "whatsapp_conversations_property_id_idx" ON "whatsapp_conversations" USING btree ("property_id");
CREATE INDEX IF NOT EXISTS "whatsapp_conversations_phone_number_idx" ON "whatsapp_conversations" USING btree ("phone_number");
CREATE INDEX IF NOT EXISTS "whatsapp_conversations_is_active_idx" ON "whatsapp_conversations" USING btree ("is_active");
CREATE INDEX IF NOT EXISTS "whatsapp_conversations_last_message_at_idx" ON "whatsapp_conversations" USING btree ("last_message_at");
CREATE INDEX IF NOT EXISTS "whatsapp_conversations_unique_idx" ON "whatsapp_conversations" USING btree ("property_id","phone_number");