-- Performance optimization migration: Composite indexes and full-text search
-- This migration adds advanced composite indexes for property search optimization

-- Advanced search composite index for property listings
-- Optimizes queries that filter by status, location, featured flag, and price ranges
CREATE INDEX "properties_advanced_search_idx" ON "properties" USING btree ("status", "location", "featured", "half_day_price", "full_day_price", "created_at");

-- Geospatial index for location-based searches (if implementing map features)
-- This index supports efficient radius-based location searches
CREATE INDEX "properties_location_geo_idx" ON "properties" USING btree ("latitude", "longitude", "status") WHERE "latitude" IS NOT NULL AND "longitude" IS NOT NULL;

-- Partial indexes for active properties with price filtering
-- These indexes are smaller and faster for the most common queries
CREATE INDEX "properties_active_half_day_price_idx" ON "properties" USING btree ("half_day_price", "featured", "created_at") WHERE "status" = 'active';
CREATE INDEX "properties_active_full_day_price_idx" ON "properties" USING btree ("full_day_price", "featured", "created_at") WHERE "status" = 'active';

-- Full-text search support
-- Add tsvector column for efficient text search across title, description, and location
ALTER TABLE "properties" ADD COLUMN "search_vector" tsvector;

-- Populate search_vector for existing records
UPDATE "properties" SET "search_vector" = to_tsvector('english', 
    COALESCE("title", '') || ' ' || 
    COALESCE("description", '') || ' ' || 
    COALESCE("location", '')
);

-- Create GIN index for full-text search
CREATE INDEX "properties_search_vector_idx" ON "properties" USING gin ("search_vector");

-- Create trigger to automatically update search_vector on changes
CREATE OR REPLACE FUNCTION update_properties_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.title, '') || ' ' || 
        COALESCE(NEW.description, '') || ' ' || 
        COALESCE(NEW.location, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER properties_search_vector_trigger
    BEFORE INSERT OR UPDATE ON "properties"
    FOR EACH ROW EXECUTE FUNCTION update_properties_search_vector();

-- Booking analytics composite indexes
-- Optimize queries for property owner analytics and reporting
CREATE INDEX "bookings_property_analytics_idx" ON "bookings" USING btree ("property_id", "booking_date", "status", "total_price");
CREATE INDEX "bookings_user_recent_idx" ON "bookings" USING btree ("user_id", "created_at" DESC, "status");

-- Optimize date range queries for availability checking
-- This index speeds up calendar-based availability queries
CREATE INDEX "bookings_date_range_idx" ON "bookings" USING btree ("booking_date", "property_id", "booking_type") WHERE "status" IN ('confirmed', 'pending');

-- Review aggregation optimization
-- Speeds up property detail pages that show review statistics
CREATE INDEX "reviews_property_rating_idx" ON "reviews" USING btree ("property_id", "rating", "created_at" DESC);

-- Property search with amenities (if moving amenities filtering to database)
-- GIN index for JSON amenities column
CREATE INDEX "properties_amenities_gin_idx" ON "properties" USING gin ("amenities");

-- Owner dashboard optimization
-- Composite index for owner property management queries
CREATE INDEX "properties_owner_management_idx" ON "properties" USING btree ("owner_id", "status", "created_at" DESC);

-- Materialized view for property availability (for high-performance availability checking)
-- This pre-computes availability data for faster lookups
CREATE MATERIALIZED VIEW "property_availability_cache" AS
SELECT 
    p.id as property_id,
    p.title,
    p.location,
    p.half_day_price,
    p.full_day_price,
    p.featured,
    p.status,
    COALESCE(
        array_agg(
            DISTINCT b.booking_date || ':' || b.booking_type
        ) FILTER (WHERE b.status IN ('confirmed', 'pending')),
        '{}'::text[]
    ) as unavailable_slots
FROM "properties" p
LEFT JOIN "bookings" b ON p.id = b.property_id 
WHERE p.status = 'active'
GROUP BY p.id, p.title, p.location, p.half_day_price, p.full_day_price, p.featured, p.status;

-- Index for the materialized view
CREATE UNIQUE INDEX "property_availability_cache_property_id_idx" ON "property_availability_cache" ("property_id");
CREATE INDEX "property_availability_cache_location_idx" ON "property_availability_cache" ("location");
CREATE INDEX "property_availability_cache_price_idx" ON "property_availability_cache" ("half_day_price", "full_day_price");

-- Function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_property_availability_cache()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY "property_availability_cache";
END;
$$ LANGUAGE plpgsql;

-- Performance monitoring views
-- Create views to monitor query performance and index usage

CREATE VIEW "index_usage_stats" AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Unused'
        WHEN idx_scan < 100 THEN 'Low Usage'
        WHEN idx_scan < 1000 THEN 'Medium Usage'
        ELSE 'High Usage'
    END as usage_level
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

CREATE VIEW "table_size_stats" AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Create function for database maintenance
CREATE OR REPLACE FUNCTION perform_database_maintenance()
RETURNS text AS $$
DECLARE
    result text := '';
BEGIN
    -- Refresh materialized view
    PERFORM refresh_property_availability_cache();
    result := result || 'Property availability cache refreshed. ';
    
    -- Update table statistics
    ANALYZE;
    result := result || 'Table statistics updated. ';
    
    -- Reindex if needed (only run during maintenance windows)
    -- REINDEX DATABASE CONCURRENTLY;
    
    RETURN result || 'Database maintenance completed.';
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON INDEX "properties_advanced_search_idx" IS 'Composite index for optimizing property search queries with multiple filters';
COMMENT ON INDEX "properties_search_vector_idx" IS 'Full-text search index for property title, description, and location';
COMMENT ON MATERIALIZED VIEW "property_availability_cache" IS 'Pre-computed availability data for faster property listing queries';
COMMENT ON FUNCTION "perform_database_maintenance" IS 'Function to perform routine database maintenance tasks';