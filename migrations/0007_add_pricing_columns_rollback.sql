-- Rollback script for 0007_add_pricing_columns.sql
-- This script safely removes pricing columns and their indexes
-- ⚠️ WARNING: This will permanently delete pricing data - use with caution!

-- First, drop all indexes created for pricing columns
DROP INDEX IF EXISTS "properties_weekday_weekend_pricing_idx";
DROP INDEX IF EXISTS "properties_weekday_half_day_price_idx";
DROP INDEX IF EXISTS "properties_weekday_full_day_price_idx";
DROP INDEX IF EXISTS "properties_weekend_half_day_price_idx";
DROP INDEX IF EXISTS "properties_weekend_full_day_price_idx";
DROP INDEX IF EXISTS "properties_active_weekday_pricing_idx";
DROP INDEX IF EXISTS "properties_active_weekend_pricing_idx";

-- Then remove the pricing columns (this will delete data!)
ALTER TABLE properties 
DROP COLUMN IF EXISTS weekday_half_day_price,
DROP COLUMN IF EXISTS weekday_full_day_price,
DROP COLUMN IF EXISTS weekend_half_day_price,
DROP COLUMN IF EXISTS weekend_full_day_price;

-- Note: Rolling back this migration will result in data loss
-- Make sure to backup weekday/weekend pricing data before rollback if needed