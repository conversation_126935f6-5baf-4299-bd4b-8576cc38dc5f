-- Rollback script for 0008_add_pricing_indexes.sql
-- This script safely removes all pricing indexes created in the forward migration
-- Execute this script if you need to rollback the pricing index migration

-- Drop composite indexes
DROP INDEX IF EXISTS "properties_weekday_weekend_pricing_idx";

-- Drop individual pricing indexes
DROP INDEX IF EXISTS "properties_weekday_half_day_price_idx";
DROP INDEX IF EXISTS "properties_weekday_full_day_price_idx";
DROP INDEX IF EXISTS "properties_weekend_half_day_price_idx";
DROP INDEX IF EXISTS "properties_weekend_full_day_price_idx";

-- Drop advanced composite indexes for active properties
DROP INDEX IF EXISTS "properties_active_weekday_pricing_idx";
DROP INDEX IF EXISTS "properties_active_weekend_pricing_idx";

-- Drop price range filtering indexes
DROP INDEX IF EXISTS "properties_weekday_price_range_idx";
DROP INDEX IF EXISTS "properties_weekend_price_range_idx";

-- Drop advanced search optimization indexes
DROP INDEX IF EXISTS "properties_advanced_weekday_search_idx";
DROP INDEX IF EXISTS "properties_advanced_weekend_search_idx";

-- Note: This rollback script only removes indexes, not the pricing columns themselves
-- If you need to rollback the pricing columns, you would need to run a separate migration
-- that drops the columns (be careful about data loss)