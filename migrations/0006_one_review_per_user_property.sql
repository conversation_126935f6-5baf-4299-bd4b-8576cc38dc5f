-- Migration: Prevent Multiple Reviews Per User Per Property
-- Ensures users can only leave one review per property they've booked

-- Add unique constraint to prevent multiple reviews from same user for same property
-- This will prevent race conditions and ensure business rule enforcement at DB level
CREATE UNIQUE INDEX IF NOT EXISTS reviews_one_per_user_property_idx 
ON reviews (user_id, property_id);

-- Add index for performance on common review queries
CREATE INDEX IF NOT EXISTS reviews_property_user_created_idx 
ON reviews (property_id, user_id, created_at DESC);

-- Add index for user's review history queries
CREATE INDEX IF NOT EXISTS reviews_user_created_idx 
ON reviews (user_id, created_at DESC);