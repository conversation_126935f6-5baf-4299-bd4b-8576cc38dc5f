-- Rollback script for 0004_cultured_logan.sql
-- This script drops SMS tables and pricing columns added in the forward migration
-- ⚠️ WARNING: This will permanently delete all SMS data and pricing data!

-- Drop all indexes first
DROP INDEX IF EXISTS "sms_templates_created_at_idx";
DROP INDEX IF EXISTS "sms_templates_category_idx";
DROP INDEX IF EXISTS "sms_templates_status_idx";
DROP INDEX IF EXISTS "sms_templates_key_idx";
DROP INDEX IF EXISTS "sms_logs_created_at_idx";
DROP INDEX IF EXISTS "sms_logs_status_idx";
DROP INDEX IF EXISTS "sms_logs_recipient_phone_idx";
DROP INDEX IF EXISTS "sms_logs_template_id_idx";

-- Drop foreign key constraints
ALTER TABLE "sms_logs" DROP CONSTRAINT IF EXISTS "sms_logs_template_id_sms_templates_id_fk";

-- Drop tables
DROP TABLE IF EXISTS "sms_logs";
DROP TABLE IF EXISTS "sms_templates";

-- Remove columns added to existing tables
DO $$
BEGIN
    -- Remove pricing columns from properties table
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'properties' AND column_name = 'weekend_full_day_price') THEN
        ALTER TABLE properties DROP COLUMN weekend_full_day_price;
        RAISE NOTICE 'Removed weekend_full_day_price column from properties table';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'properties' AND column_name = 'weekend_half_day_price') THEN
        ALTER TABLE properties DROP COLUMN weekend_half_day_price;
        RAISE NOTICE 'Removed weekend_half_day_price column from properties table';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'properties' AND column_name = 'weekday_full_day_price') THEN
        ALTER TABLE properties DROP COLUMN weekday_full_day_price;
        RAISE NOTICE 'Removed weekday_full_day_price column from properties table';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'properties' AND column_name = 'weekday_half_day_price') THEN
        ALTER TABLE properties DROP COLUMN weekday_half_day_price;
        RAISE NOTICE 'Removed weekday_half_day_price column from properties table';
    END IF;
    
    -- Remove is_used column from otp_tokens table
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'otp_tokens' AND column_name = 'is_used') THEN
        ALTER TABLE otp_tokens DROP COLUMN is_used;
        RAISE NOTICE 'Removed is_used column from otp_tokens table';
    END IF;
END $$;

-- Note: This rollback will result in permanent data loss for:
-- - All SMS logs and templates
-- - All weekday/weekend pricing data for properties
-- - OTP token usage tracking data
-- 
-- Ensure you have proper backups before executing this rollback!