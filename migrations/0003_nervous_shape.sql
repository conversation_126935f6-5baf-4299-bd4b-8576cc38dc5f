CREATE TABLE "encryption_keys" (
	"id" serial PRIMARY KEY NOT NULL,
	"key_id" text NOT NULL,
	"key_type" text NOT NULL,
	"encrypted_key" text NOT NULL,
	"key_version" integer NOT NULL,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp,
	CONSTRAINT "encryption_keys_key_id_unique" UNIQUE("key_id")
);
--> statement-breakpoint
CREATE TABLE "gst_rate_configurations" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_type" text NOT NULL,
	"hsn_sac_code" text NOT NULL,
	"rate_structure" json NOT NULL,
	"effective_from" text NOT NULL,
	"effective_to" text,
	"created_by" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "gst_records" (
	"id" serial PRIMARY KEY NOT NULL,
	"booking_id" integer,
	"base_amount" integer NOT NULL,
	"transaction_type" text NOT NULL,
	"service_type" text NOT NULL,
	"hsn_sac_code" text NOT NULL,
	"supplier_gstin" text,
	"recipient_gstin" text,
	"supplier_state" text NOT NULL,
	"recipient_state" text NOT NULL,
	"place_of_supply" text NOT NULL,
	"gst_rate_config_id" integer,
	"applicable_date" text NOT NULL,
	"cgst_rate" double precision DEFAULT 0,
	"sgst_rate" double precision DEFAULT 0,
	"cgst_amount" integer DEFAULT 0,
	"sgst_amount" integer DEFAULT 0,
	"igst_rate" double precision DEFAULT 0,
	"igst_amount" integer DEFAULT 0,
	"cess_rate" double precision DEFAULT 0,
	"cess_amount" integer DEFAULT 0,
	"total_gst" integer NOT NULL,
	"total_amount" integer NOT NULL,
	"invoice_number" text,
	"invoice_date" text,
	"irn" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "idempotency_keys" (
	"id" serial PRIMARY KEY NOT NULL,
	"key" text NOT NULL,
	"resource_type" text NOT NULL,
	"resource_id" integer,
	"response_data" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp NOT NULL,
	CONSTRAINT "idempotency_keys_key_unique" UNIQUE("key")
);
--> statement-breakpoint
CREATE TABLE "payment_audit_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"payment_order_id" integer,
	"payment_transaction_id" integer,
	"action" text NOT NULL,
	"actor_type" text NOT NULL,
	"actor_id" integer,
	"actor_ip" text,
	"actor_user_agent" text,
	"before_state" json,
	"after_state" json,
	"metadata" json,
	"security_context" json,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payment_orders" (
	"id" serial PRIMARY KEY NOT NULL,
	"razorpay_order_id" text NOT NULL,
	"booking_id" integer,
	"idempotency_key" text NOT NULL,
	"amount" integer NOT NULL,
	"currency" text DEFAULT 'INR',
	"receipt" text,
	"status" text DEFAULT 'created',
	"attempts" integer DEFAULT 0,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "payment_orders_razorpay_order_id_unique" UNIQUE("razorpay_order_id"),
	CONSTRAINT "payment_orders_idempotency_key_unique" UNIQUE("idempotency_key")
);
--> statement-breakpoint
CREATE TABLE "payment_roles" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"permissions" json NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "payment_roles_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "payment_transactions" (
	"id" serial PRIMARY KEY NOT NULL,
	"payment_order_id" integer,
	"razorpay_payment_id" text,
	"razorpay_signature_hash" text,
	"signature_verification_status" text DEFAULT 'pending',
	"amount" integer NOT NULL,
	"method" text,
	"bank" text,
	"currency" text DEFAULT 'INR',
	"exchange_rate" double precision,
	"base_currency" text DEFAULT 'INR',
	"status" text DEFAULT 'initiated',
	"failure_code" text,
	"failure_reason" text,
	"gateway_response" json,
	"encryption_key_id" text,
	"sensitive_data_hash" text,
	"security_flags" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "payment_transactions_razorpay_payment_id_unique" UNIQUE("razorpay_payment_id")
);
--> statement-breakpoint
CREATE TABLE "security_incidents" (
	"id" serial PRIMARY KEY NOT NULL,
	"incident_type" text NOT NULL,
	"severity" text NOT NULL,
	"description" text NOT NULL,
	"source_ip" text,
	"user_id" integer,
	"payment_order_id" integer,
	"incident_data" json,
	"status" text DEFAULT 'open',
	"resolved_at" timestamp,
	"resolved_by" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sms_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"template_id" integer,
	"recipient_phone" text NOT NULL,
	"message_content" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"twilio_message_sid" text,
	"error_message" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sms_templates" (
	"id" serial PRIMARY KEY NOT NULL,
	"key" text NOT NULL,
	"name" text NOT NULL,
	"content" text NOT NULL,
	"dlt_template_id" text NOT NULL,
	"category" text DEFAULT 'transactional' NOT NULL,
	"variables" json DEFAULT '[]'::json NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "sms_templates_key_unique" UNIQUE("key")
);
--> statement-breakpoint
CREATE TABLE "user_payment_roles" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"role_id" integer,
	"granted_by" integer,
	"granted_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true
);
--> statement-breakpoint
CREATE TABLE "webhook_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"event_id" text NOT NULL,
	"event_type" text NOT NULL,
	"payload" json NOT NULL,
	"signature_hash" text NOT NULL,
	"status" text DEFAULT 'received',
	"processed_at" timestamp,
	"retry_count" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "webhook_events_event_id_unique" UNIQUE("event_id")
);
--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "payment_status" text DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "advance_amount" integer;--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "remaining_amount" integer;--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "gst_amount" integer;--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "payment_due_date" text;--> statement-breakpoint
ALTER TABLE "bookings" ADD COLUMN "payment_expiry" timestamp;--> statement-breakpoint
ALTER TABLE "otp_tokens" ADD COLUMN "purpose" text;--> statement-breakpoint
ALTER TABLE "otp_tokens" ADD COLUMN "is_used" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "is_verified" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "gst_rate_configurations" ADD CONSTRAINT "gst_rate_configurations_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "gst_records" ADD CONSTRAINT "gst_records_booking_id_bookings_id_fk" FOREIGN KEY ("booking_id") REFERENCES "public"."bookings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "gst_records" ADD CONSTRAINT "gst_records_gst_rate_config_id_gst_rate_configurations_id_fk" FOREIGN KEY ("gst_rate_config_id") REFERENCES "public"."gst_rate_configurations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_audit_logs" ADD CONSTRAINT "payment_audit_logs_payment_order_id_payment_orders_id_fk" FOREIGN KEY ("payment_order_id") REFERENCES "public"."payment_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_audit_logs" ADD CONSTRAINT "payment_audit_logs_payment_transaction_id_payment_transactions_id_fk" FOREIGN KEY ("payment_transaction_id") REFERENCES "public"."payment_transactions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_orders" ADD CONSTRAINT "payment_orders_booking_id_bookings_id_fk" FOREIGN KEY ("booking_id") REFERENCES "public"."bookings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_transactions" ADD CONSTRAINT "payment_transactions_payment_order_id_payment_orders_id_fk" FOREIGN KEY ("payment_order_id") REFERENCES "public"."payment_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "security_incidents" ADD CONSTRAINT "security_incidents_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "security_incidents" ADD CONSTRAINT "security_incidents_payment_order_id_payment_orders_id_fk" FOREIGN KEY ("payment_order_id") REFERENCES "public"."payment_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "security_incidents" ADD CONSTRAINT "security_incidents_resolved_by_users_id_fk" FOREIGN KEY ("resolved_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_logs" ADD CONSTRAINT "sms_logs_template_id_sms_templates_id_fk" FOREIGN KEY ("template_id") REFERENCES "public"."sms_templates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_payment_roles" ADD CONSTRAINT "user_payment_roles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_payment_roles" ADD CONSTRAINT "user_payment_roles_role_id_payment_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."payment_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_payment_roles" ADD CONSTRAINT "user_payment_roles_granted_by_users_id_fk" FOREIGN KEY ("granted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "encryption_keys_key_id_idx" ON "encryption_keys" USING btree ("key_id");--> statement-breakpoint
CREATE INDEX "encryption_keys_is_active_idx" ON "encryption_keys" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "gst_rate_config_service_type_idx" ON "gst_rate_configurations" USING btree ("service_type");--> statement-breakpoint
CREATE INDEX "gst_rate_config_hsn_sac_idx" ON "gst_rate_configurations" USING btree ("hsn_sac_code");--> statement-breakpoint
CREATE INDEX "gst_rate_config_effective_dates_idx" ON "gst_rate_configurations" USING btree ("effective_from","effective_to");--> statement-breakpoint
CREATE INDEX "gst_rate_config_unique_idx" ON "gst_rate_configurations" USING btree ("service_type","hsn_sac_code","effective_from");--> statement-breakpoint
CREATE INDEX "gst_records_booking_id_idx" ON "gst_records" USING btree ("booking_id");--> statement-breakpoint
CREATE INDEX "gst_records_transaction_type_idx" ON "gst_records" USING btree ("transaction_type");--> statement-breakpoint
CREATE INDEX "gst_records_service_type_idx" ON "gst_records" USING btree ("service_type");--> statement-breakpoint
CREATE INDEX "gst_records_supplier_state_idx" ON "gst_records" USING btree ("supplier_state");--> statement-breakpoint
CREATE INDEX "gst_records_applicable_date_idx" ON "gst_records" USING btree ("applicable_date");--> statement-breakpoint
CREATE INDEX "gst_records_created_at_idx" ON "gst_records" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "idempotency_keys_key_idx" ON "idempotency_keys" USING btree ("key");--> statement-breakpoint
CREATE INDEX "idempotency_keys_expires_at_idx" ON "idempotency_keys" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "payment_audit_logs_payment_order_id_idx" ON "payment_audit_logs" USING btree ("payment_order_id");--> statement-breakpoint
CREATE INDEX "payment_audit_logs_actor_id_idx" ON "payment_audit_logs" USING btree ("actor_id");--> statement-breakpoint
CREATE INDEX "payment_audit_logs_action_idx" ON "payment_audit_logs" USING btree ("action");--> statement-breakpoint
CREATE INDEX "payment_audit_logs_created_at_idx" ON "payment_audit_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "payment_orders_booking_id_idx" ON "payment_orders" USING btree ("booking_id");--> statement-breakpoint
CREATE INDEX "payment_orders_status_idx" ON "payment_orders" USING btree ("status");--> statement-breakpoint
CREATE INDEX "payment_orders_idempotency_key_idx" ON "payment_orders" USING btree ("idempotency_key");--> statement-breakpoint
CREATE INDEX "payment_orders_created_at_idx" ON "payment_orders" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "payment_transactions_payment_order_id_idx" ON "payment_transactions" USING btree ("payment_order_id");--> statement-breakpoint
CREATE INDEX "payment_transactions_status_idx" ON "payment_transactions" USING btree ("status");--> statement-breakpoint
CREATE INDEX "payment_transactions_razorpay_payment_id_idx" ON "payment_transactions" USING btree ("razorpay_payment_id");--> statement-breakpoint
CREATE INDEX "payment_transactions_created_at_idx" ON "payment_transactions" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "security_incidents_type_idx" ON "security_incidents" USING btree ("incident_type");--> statement-breakpoint
CREATE INDEX "security_incidents_severity_idx" ON "security_incidents" USING btree ("severity");--> statement-breakpoint
CREATE INDEX "security_incidents_status_idx" ON "security_incidents" USING btree ("status");--> statement-breakpoint
CREATE INDEX "security_incidents_created_at_idx" ON "security_incidents" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "sms_logs_template_id_idx" ON "sms_logs" USING btree ("template_id");--> statement-breakpoint
CREATE INDEX "sms_logs_recipient_phone_idx" ON "sms_logs" USING btree ("recipient_phone");--> statement-breakpoint
CREATE INDEX "sms_logs_status_idx" ON "sms_logs" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sms_logs_created_at_idx" ON "sms_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "sms_templates_key_idx" ON "sms_templates" USING btree ("key");--> statement-breakpoint
CREATE INDEX "sms_templates_status_idx" ON "sms_templates" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sms_templates_category_idx" ON "sms_templates" USING btree ("category");--> statement-breakpoint
CREATE INDEX "sms_templates_created_at_idx" ON "sms_templates" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "user_payment_roles_unique_idx" ON "user_payment_roles" USING btree ("user_id","role_id");--> statement-breakpoint
CREATE INDEX "webhook_events_event_id_idx" ON "webhook_events" USING btree ("event_id");--> statement-breakpoint
CREATE INDEX "webhook_events_status_idx" ON "webhook_events" USING btree ("status");--> statement-breakpoint
CREATE INDEX "webhook_events_created_at_idx" ON "webhook_events" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "bookings_payment_status_idx" ON "bookings" USING btree ("payment_status");