#!/usr/bin/env node

/**
 * Rollback Validation Script for Farmhouse Database Migrations
 * 
 * This script validates that:
 * 1. Every migration has a corresponding rollback script
 * 2. Rollback scripts follow naming conventions
 * 3. Rollback scripts contain proper safety warnings
 * 4. Critical migrations have data backup instructions
 * 
 * Usage:
 *   node rollback_validation.js
 *   node rollback_validation.js --fix  # Auto-generate missing rollback templates
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const MIGRATIONS_DIR = __dirname;
const ROLLBACK_SUFFIX = '_rollback.sql';

// Configuration
const CONFIG = {
  // Critical migrations that require special attention
  CRITICAL_MIGRATIONS: [
    '0000_faulty_shinobi_shaw',  // Initial schema
    '0003_nervous_shape',        // Payment system
    '0007_add_pricing_columns'   // Pricing data
  ],
  
  // Required warnings in rollback scripts
  REQUIRED_WARNINGS: [
    'WARNING',
    'data loss',
    'permanent'
  ],
  
  // Required sections in rollback scripts
  REQUIRED_SECTIONS: [
    'DROP',
    'Note:'
  ]
};

class RollbackValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.migrations = [];
    this.rollbacks = [];
  }

  /**
   * Scan migrations directory for migration and rollback files
   */
  scanMigrations() {
    console.log('🔍 Scanning migrations directory...');
    
    const files = fs.readdirSync(MIGRATIONS_DIR)
      .filter(file => file.endsWith('.sql'))
      .sort();

    for (const file of files) {
      if (file.includes(ROLLBACK_SUFFIX)) {
        this.rollbacks.push(file);
      } else if (file.match(/^\d{4}_/)) {
        this.migrations.push(file);
      }
    }

    console.log(`📄 Found ${this.migrations.length} migrations`);
    console.log(`🔄 Found ${this.rollbacks.length} rollback scripts`);
  }

  /**
   * Validate that every migration has a rollback script
   */
  validateRollbackExists() {
    console.log('\n✅ Validating rollback script existence...');
    
    for (const migration of this.migrations) {
      const expectedRollback = migration.replace('.sql', ROLLBACK_SUFFIX);
      
      if (!this.rollbacks.includes(expectedRollback)) {
        this.errors.push(`❌ Missing rollback script: ${expectedRollback}`);
      } else {
        console.log(`✅ ${migration} -> ${expectedRollback}`);
      }
    }
  }

  /**
   * Validate rollback script content and structure
   */
  validateRollbackContent() {
    console.log('\n🔍 Validating rollback script content...');
    
    for (const rollback of this.rollbacks) {
      const rollbackPath = path.join(MIGRATIONS_DIR, rollback);
      
      try {
        const content = fs.readFileSync(rollbackPath, 'utf8');
        this.validateSingleRollback(rollback, content);
      } catch (error) {
        this.errors.push(`❌ Error reading ${rollback}: ${error.message}`);
      }
    }
  }

  /**
   * Validate a single rollback script
   */
  validateSingleRollback(filename, content) {
    const migrationName = filename.replace(ROLLBACK_SUFFIX, '');
    
    // Check for required warnings
    const hasWarnings = CONFIG.REQUIRED_WARNINGS.some(warning => 
      content.toLowerCase().includes(warning.toLowerCase())
    );
    
    if (!hasWarnings) {
      this.warnings.push(`⚠️  ${filename}: Missing data loss warnings`);
    }

    // Check for required sections
    const missingSections = CONFIG.REQUIRED_SECTIONS.filter(section =>
      !content.toUpperCase().includes(section.toUpperCase())
    );
    
    if (missingSections.length > 0) {
      this.warnings.push(`⚠️  ${filename}: Missing sections: ${missingSections.join(', ')}`);
    }

    // Check for proper header comment
    if (!content.startsWith('-- Rollback script for')) {
      this.warnings.push(`⚠️  ${filename}: Missing proper header comment`);
    }

    // Check for IF EXISTS clauses (safety measure)
    const dropStatements = content.match(/DROP\s+\w+\s+(?!IF\s+EXISTS)/gi);
    if (dropStatements && dropStatements.length > 0) {
      this.warnings.push(`⚠️  ${filename}: Has DROP statements without IF EXISTS`);
    }

    // Critical migration checks
    if (CONFIG.CRITICAL_MIGRATIONS.includes(migrationName)) {
      this.validateCriticalRollback(filename, content);
    }

    console.log(`✅ ${filename}: Content validated`);
  }

  /**
   * Additional validation for critical migrations
   */
  validateCriticalRollback(filename, content) {
    // Check for backup instructions
    if (!content.toLowerCase().includes('backup')) {
      this.errors.push(`❌ ${filename}: Critical migration missing backup instructions`);
    }

    // Check for more prominent warnings
    if (!content.includes('⚠️')) {
      this.warnings.push(`⚠️  ${filename}: Critical migration should have prominent warning symbols`);
    }
  }

  /**
   * Validate migration naming conventions
   */
  validateNamingConventions() {
    console.log('\n📝 Validating naming conventions...');
    
    for (const migration of this.migrations) {
      // Check migration naming pattern
      if (!migration.match(/^\d{4}_[a-z_]+\.sql$/)) {
        this.warnings.push(`⚠️  ${migration}: Does not follow naming convention XXXX_description.sql`);
      }
    }
    
    for (const rollback of this.rollbacks) {
      // Check rollback naming pattern
      if (!rollback.match(/^\d{4}_[a-z_]+_rollback\.sql$/)) {
        this.errors.push(`❌ ${rollback}: Does not follow naming convention XXXX_description_rollback.sql`);
      }
    }
  }

  /**
   * Generate missing rollback templates
   */
  generateMissingRollbacks() {
    console.log('\n🛠️  Generating missing rollback templates...');
    
    for (const migration of this.migrations) {
      const expectedRollback = migration.replace('.sql', ROLLBACK_SUFFIX);
      
      if (!this.rollbacks.includes(expectedRollback)) {
        this.generateRollbackTemplate(migration, expectedRollback);
      }
    }
  }

  /**
   * Generate a rollback template for a migration
   */
  generateRollbackTemplate(migrationFile, rollbackFile) {
    const migrationPath = path.join(MIGRATIONS_DIR, migrationFile);
    const rollbackPath = path.join(MIGRATIONS_DIR, rollbackFile);
    
    try {
      const migrationContent = fs.readFileSync(migrationPath, 'utf8');
      const template = this.createRollbackTemplate(migrationFile, migrationContent);
      
      fs.writeFileSync(rollbackPath, template);
      console.log(`✅ Generated template: ${rollbackFile}`);
      
    } catch (error) {
      this.errors.push(`❌ Failed to generate ${rollbackFile}: ${error.message}`);
    }
  }

  /**
   * Create a rollback template based on migration content
   */
  createRollbackTemplate(migrationFile, migrationContent) {
    const migrationName = migrationFile.replace('.sql', '');
    
    // Analyze migration content to suggest rollback operations
    const hasCreateTable = migrationContent.includes('CREATE TABLE');
    const hasAlterTable = migrationContent.includes('ALTER TABLE');
    const hasCreateIndex = migrationContent.includes('CREATE INDEX');
    const hasDrop = migrationContent.includes('DROP');
    
    let suggestions = [];
    
    if (hasCreateTable) {
      suggestions.push('-- Drop tables created in this migration');
      suggestions.push('-- DROP TABLE IF EXISTS "table_name";');
    }
    
    if (hasCreateIndex) {
      suggestions.push('-- Drop indexes created in this migration');
      suggestions.push('-- DROP INDEX IF EXISTS "index_name";');
    }
    
    if (hasAlterTable) {
      suggestions.push('-- Reverse ALTER TABLE operations');
      suggestions.push('-- ALTER TABLE table_name DROP COLUMN IF EXISTS column_name;');
    }

    return `-- Rollback script for ${migrationFile}
-- This script reverses the changes made in the forward migration
-- ⚠️ WARNING: Review this template and customize for your specific migration
-- ⚠️ WARNING: This operation may result in data loss

${suggestions.join('\n')}

-- Note: This is a generated template. Please review and customize:
-- 1. Add specific DROP statements for objects created in the migration
-- 2. Add IF EXISTS clauses to prevent errors
-- 3. Consider data backup requirements
-- 4. Test in development environment first
-- 5. Add specific warnings about data loss

-- TODO: Replace this template with actual rollback operations
-- TODO: Test this rollback script thoroughly
-- TODO: Update documentation if needed
`;
  }

  /**
   * Check database connection and validate against current schema
   */
  validateAgainstDatabase() {
    console.log('\n🗄️  Validating against database schema...');
    
    try {
      // This would require database connection
      // For now, just check if we can connect
      console.log('ℹ️  Database validation requires manual setup');
      console.log('ℹ️  Consider implementing pg connection for automated schema validation');
    } catch (error) {
      this.warnings.push(`⚠️  Database validation skipped: ${error.message}`);
    }
  }

  /**
   * Generate validation report
   */
  generateReport() {
    console.log('\n📊 VALIDATION REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n📄 Migrations: ${this.migrations.length}`);
    console.log(`🔄 Rollbacks: ${this.rollbacks.length}`);
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('\n🎉 All validations passed!');
      return true;
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.errors.forEach(error => console.log(`  ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach(warning => console.log(`  ${warning}`));
    }

    console.log(`\n📊 Summary: ${this.errors.length} errors, ${this.warnings.length} warnings`);
    
    return this.errors.length === 0;
  }

  /**
   * Run all validations
   */
  async run(options = {}) {
    console.log('🚀 Starting rollback validation...\n');

    this.scanMigrations();
    this.validateRollbackExists();
    this.validateNamingConventions();
    this.validateRollbackContent();
    this.validateAgainstDatabase();

    if (options.fix) {
      this.generateMissingRollbacks();
    }

    const success = this.generateReport();
    
    if (!success) {
      console.log('\n💡 Suggestions:');
      console.log('  • Run with --fix to generate missing rollback templates');
      console.log('  • Review and customize generated templates');
      console.log('  • Add proper data loss warnings');
      console.log('  • Test rollbacks in development environment');
    }

    return success;
  }
}

// CLI execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  const options = {
    fix: args.includes('--fix')
  };

  const validator = new RollbackValidator();
  validator.run(options)
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Validation failed:', error);
      process.exit(1);
    });
}

export default RollbackValidator;