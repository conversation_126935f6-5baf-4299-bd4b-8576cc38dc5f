-- Add performance indexes for farmhouse rental platform
-- This migration adds indexes for frequently queried fields

-- Index for properties location search (most common filter)
CREATE INDEX IF NOT EXISTS idx_properties_location ON properties(location);

-- Index for featured properties (homepage queries)
CREATE INDEX IF NOT EXISTS idx_properties_featured ON properties(featured);

-- Index for property owner queries
CREATE INDEX IF NOT EXISTS idx_properties_owner_id ON properties(owner_id);

-- Composite index for booking availability checks (most critical for performance)
CREATE INDEX IF NOT EXISTS idx_bookings_property_date ON bookings(property_id, booking_date);

-- Index for user bookings
CREATE INDEX IF NOT EXISTS idx_bookings_user_id ON bookings(user_id);

-- Index for booking dates (date range queries)
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(booking_date);

-- Index for booking status
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);

-- Index for users email (login queries)
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Index for reviews by property (property detail page)
CREATE INDEX IF NOT EXISTS idx_reviews_property_id ON reviews(property_id);

-- Index for reviews by user
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews(user_id);

-- Index for property status (active properties filter)
CREATE INDEX IF NOT EXISTS idx_properties_status ON properties(status);

-- Composite index for property search with price range
CREATE INDEX IF NOT EXISTS idx_properties_price_search ON properties(status, featured, half_day_price, full_day_price);