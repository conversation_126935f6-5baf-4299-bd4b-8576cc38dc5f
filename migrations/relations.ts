import { relations } from "drizzle-orm/relations";
import { users, properties, smsTemplates, smsLogs, gstRateConfigurations, bookings, gstRecords, userPaymentRoles, paymentRoles, whatsappConversations, reviews, calendarBookings, calendarSyncStatus, deviceVerifications, securitySessions, paymentOrders, paymentAuditLogs, paymentTransactions, securityIncidents } from "./schema";

export const propertiesRelations = relations(properties, ({one, many}) => ({
	user: one(users, {
		fields: [properties.ownerId],
		references: [users.id]
	}),
	whatsappConversations: many(whatsappConversations),
	bookings: many(bookings),
	reviews: many(reviews),
	calendarBookings: many(calendarBookings),
	calendarSyncStatuses: many(calendarSyncStatus),
}));

export const usersRelations = relations(users, ({many}) => ({
	properties: many(properties),
	gstRateConfigurations: many(gstRateConfigurations),
	userPaymentRoles_userId: many(userPaymentRoles, {
		relationName: "userPaymentRoles_userId_users_id"
	}),
	userPaymentRoles_grantedBy: many(userPaymentRoles, {
		relationName: "userPaymentRoles_grantedBy_users_id"
	}),
	bookings: many(bookings),
	reviews: many(reviews),
	calendarBookings: many(calendarBookings),
	deviceVerifications: many(deviceVerifications),
	securitySessions: many(securitySessions),
	securityIncidents_userId: many(securityIncidents, {
		relationName: "securityIncidents_userId_users_id"
	}),
	securityIncidents_resolvedBy: many(securityIncidents, {
		relationName: "securityIncidents_resolvedBy_users_id"
	}),
}));

export const smsLogsRelations = relations(smsLogs, ({one}) => ({
	smsTemplate: one(smsTemplates, {
		fields: [smsLogs.templateId],
		references: [smsTemplates.id]
	}),
}));

export const smsTemplatesRelations = relations(smsTemplates, ({many}) => ({
	smsLogs: many(smsLogs),
}));

export const gstRateConfigurationsRelations = relations(gstRateConfigurations, ({one, many}) => ({
	user: one(users, {
		fields: [gstRateConfigurations.createdBy],
		references: [users.id]
	}),
	gstRecords: many(gstRecords),
}));

export const gstRecordsRelations = relations(gstRecords, ({one}) => ({
	booking: one(bookings, {
		fields: [gstRecords.bookingId],
		references: [bookings.id]
	}),
	gstRateConfiguration: one(gstRateConfigurations, {
		fields: [gstRecords.gstRateConfigId],
		references: [gstRateConfigurations.id]
	}),
}));

export const bookingsRelations = relations(bookings, ({one, many}) => ({
	gstRecords: many(gstRecords),
	property: one(properties, {
		fields: [bookings.propertyId],
		references: [properties.id]
	}),
	user: one(users, {
		fields: [bookings.userId],
		references: [users.id]
	}),
	reviews: many(reviews),
	paymentOrders: many(paymentOrders),
}));

export const userPaymentRolesRelations = relations(userPaymentRoles, ({one}) => ({
	user_userId: one(users, {
		fields: [userPaymentRoles.userId],
		references: [users.id],
		relationName: "userPaymentRoles_userId_users_id"
	}),
	paymentRole: one(paymentRoles, {
		fields: [userPaymentRoles.roleId],
		references: [paymentRoles.id]
	}),
	user_grantedBy: one(users, {
		fields: [userPaymentRoles.grantedBy],
		references: [users.id],
		relationName: "userPaymentRoles_grantedBy_users_id"
	}),
}));

export const paymentRolesRelations = relations(paymentRoles, ({many}) => ({
	userPaymentRoles: many(userPaymentRoles),
}));

export const whatsappConversationsRelations = relations(whatsappConversations, ({one}) => ({
	property: one(properties, {
		fields: [whatsappConversations.propertyId],
		references: [properties.id]
	}),
}));

export const reviewsRelations = relations(reviews, ({one}) => ({
	property: one(properties, {
		fields: [reviews.propertyId],
		references: [properties.id]
	}),
	user: one(users, {
		fields: [reviews.userId],
		references: [users.id]
	}),
	booking: one(bookings, {
		fields: [reviews.bookingId],
		references: [bookings.id]
	}),
}));

export const calendarBookingsRelations = relations(calendarBookings, ({one}) => ({
	property: one(properties, {
		fields: [calendarBookings.propertyId],
		references: [properties.id]
	}),
	user: one(users, {
		fields: [calendarBookings.createdBy],
		references: [users.id]
	}),
}));

export const calendarSyncStatusRelations = relations(calendarSyncStatus, ({one}) => ({
	property: one(properties, {
		fields: [calendarSyncStatus.propertyId],
		references: [properties.id]
	}),
}));

export const deviceVerificationsRelations = relations(deviceVerifications, ({one}) => ({
	user: one(users, {
		fields: [deviceVerifications.userId],
		references: [users.id]
	}),
}));

export const securitySessionsRelations = relations(securitySessions, ({one}) => ({
	user: one(users, {
		fields: [securitySessions.userId],
		references: [users.id]
	}),
}));

export const paymentOrdersRelations = relations(paymentOrders, ({one, many}) => ({
	booking: one(bookings, {
		fields: [paymentOrders.bookingId],
		references: [bookings.id]
	}),
	paymentAuditLogs: many(paymentAuditLogs),
	paymentTransactions: many(paymentTransactions),
	securityIncidents: many(securityIncidents),
}));

export const paymentAuditLogsRelations = relations(paymentAuditLogs, ({one}) => ({
	paymentOrder: one(paymentOrders, {
		fields: [paymentAuditLogs.paymentOrderId],
		references: [paymentOrders.id]
	}),
	paymentTransaction: one(paymentTransactions, {
		fields: [paymentAuditLogs.paymentTransactionId],
		references: [paymentTransactions.id]
	}),
}));

export const paymentTransactionsRelations = relations(paymentTransactions, ({one, many}) => ({
	paymentAuditLogs: many(paymentAuditLogs),
	paymentOrder: one(paymentOrders, {
		fields: [paymentTransactions.paymentOrderId],
		references: [paymentOrders.id]
	}),
}));

export const securityIncidentsRelations = relations(securityIncidents, ({one}) => ({
	user_userId: one(users, {
		fields: [securityIncidents.userId],
		references: [users.id],
		relationName: "securityIncidents_userId_users_id"
	}),
	paymentOrder: one(paymentOrders, {
		fields: [securityIncidents.paymentOrderId],
		references: [paymentOrders.id]
	}),
	user_resolvedBy: one(users, {
		fields: [securityIncidents.resolvedBy],
		references: [users.id],
		relationName: "securityIncidents_resolvedBy_users_id"
	}),
}));