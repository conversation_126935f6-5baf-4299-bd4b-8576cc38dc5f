-- Rollback script for 0003_nervous_shape.sql
-- This script drops all tables, indexes, and columns added in the forward migration
-- ⚠️ WARNING: This will permanently delete ALL payment, security, and SMS data!

-- Drop all indexes first
DROP INDEX IF EXISTS "bookings_payment_status_idx";
DROP INDEX IF EXISTS "webhook_events_created_at_idx";
DROP INDEX IF EXISTS "webhook_events_status_idx";
DROP INDEX IF EXISTS "webhook_events_event_id_idx";
DROP INDEX IF EXISTS "user_payment_roles_unique_idx";
DROP INDEX IF EXISTS "sms_templates_created_at_idx";
DROP INDEX IF EXISTS "sms_templates_category_idx";
DROP INDEX IF EXISTS "sms_templates_status_idx";
DROP INDEX IF EXISTS "sms_templates_key_idx";
DROP INDEX IF EXISTS "sms_logs_created_at_idx";
DROP INDEX IF EXISTS "sms_logs_status_idx";
DROP INDEX IF EXISTS "sms_logs_recipient_phone_idx";
DROP INDEX IF EXISTS "sms_logs_template_id_idx";
DROP INDEX IF EXISTS "security_incidents_created_at_idx";
DROP INDEX IF EXISTS "security_incidents_status_idx";
DROP INDEX IF EXISTS "security_incidents_severity_idx";
DROP INDEX IF EXISTS "security_incidents_type_idx";
DROP INDEX IF EXISTS "payment_transactions_created_at_idx";
DROP INDEX IF EXISTS "payment_transactions_razorpay_payment_id_idx";
DROP INDEX IF EXISTS "payment_transactions_status_idx";
DROP INDEX IF EXISTS "payment_transactions_payment_order_id_idx";
DROP INDEX IF EXISTS "payment_orders_created_at_idx";
DROP INDEX IF EXISTS "payment_orders_idempotency_key_idx";
DROP INDEX IF EXISTS "payment_orders_status_idx";
DROP INDEX IF EXISTS "payment_orders_booking_id_idx";
DROP INDEX IF EXISTS "payment_audit_logs_created_at_idx";
DROP INDEX IF EXISTS "payment_audit_logs_action_idx";
DROP INDEX IF EXISTS "payment_audit_logs_actor_id_idx";
DROP INDEX IF EXISTS "payment_audit_logs_payment_order_id_idx";
DROP INDEX IF EXISTS "idempotency_keys_expires_at_idx";
DROP INDEX IF EXISTS "idempotency_keys_key_idx";
DROP INDEX IF EXISTS "gst_records_created_at_idx";
DROP INDEX IF EXISTS "gst_records_applicable_date_idx";
DROP INDEX IF EXISTS "gst_records_supplier_state_idx";
DROP INDEX IF EXISTS "gst_records_service_type_idx";
DROP INDEX IF EXISTS "gst_records_transaction_type_idx";
DROP INDEX IF EXISTS "gst_records_booking_id_idx";
DROP INDEX IF EXISTS "gst_rate_config_unique_idx";
DROP INDEX IF EXISTS "gst_rate_config_effective_dates_idx";
DROP INDEX IF EXISTS "gst_rate_config_hsn_sac_idx";
DROP INDEX IF EXISTS "gst_rate_config_service_type_idx";
DROP INDEX IF EXISTS "encryption_keys_is_active_idx";
DROP INDEX IF EXISTS "encryption_keys_key_id_idx";

-- Drop foreign key constraints (if any exist)
-- Note: Check the original migration for foreign key constraints and add them here

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS "webhook_events";
DROP TABLE IF EXISTS "user_payment_roles";
DROP TABLE IF EXISTS "sms_logs";
DROP TABLE IF EXISTS "sms_templates";
DROP TABLE IF EXISTS "security_incidents";
DROP TABLE IF EXISTS "payment_transactions";
DROP TABLE IF EXISTS "payment_roles";
DROP TABLE IF EXISTS "payment_orders";
DROP TABLE IF EXISTS "payment_audit_logs";
DROP TABLE IF EXISTS "idempotency_keys";
DROP TABLE IF EXISTS "gst_records";
DROP TABLE IF EXISTS "gst_rate_configurations";
DROP TABLE IF EXISTS "encryption_keys";

-- Remove columns added to existing tables
DO $$
BEGIN
    -- Remove payment_status column from bookings table if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'bookings' AND column_name = 'payment_status'
    ) THEN
        ALTER TABLE bookings DROP COLUMN payment_status;
        RAISE NOTICE 'Removed payment_status column from bookings table';
    END IF;
    
    -- Add any other column removals here if the migration added columns to existing tables
END $$;

-- Note: This rollback will result in permanent data loss for:
-- - All payment orders and transactions
-- - All GST records and configurations
-- - All SMS logs and templates
-- - All security incident records
-- - All encryption keys
-- - All webhook events
-- - All user payment role assignments
-- - All idempotency keys
-- 
-- Ensure you have proper backups before executing this rollback!