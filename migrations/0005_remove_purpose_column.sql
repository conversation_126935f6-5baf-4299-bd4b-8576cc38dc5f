-- Remove purpose column from otp_tokens table
-- This migration safely removes the purpose column which is no longer needed

DO $$
BEGIN
    -- Check if the purpose column exists and remove it
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'otp_tokens' 
        AND column_name = 'purpose'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE otp_tokens DROP COLUMN purpose;
        RAISE NOTICE 'Purpose column removed from otp_tokens table';
    ELSE
        RAISE NOTICE 'Purpose column does not exist in otp_tokens table';
    END IF;
END $$;