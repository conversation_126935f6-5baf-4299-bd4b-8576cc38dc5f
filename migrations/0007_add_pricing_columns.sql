-- Add weekday/weekend pricing columns to properties table
ALTER TABLE properties 
ADD COLUMN weekday_half_day_price DOUBLE PRECISION,
ADD COLUMN weekday_full_day_price DOUBLE PRECISION,
ADD COLUMN weekend_half_day_price DOUBLE PRECISION,
ADD COLUMN weekend_full_day_price DOUBLE PRECISION;

-- Set default values based on existing pricing (as fallback)
UPDATE properties 
SET 
  weekday_half_day_price = half_day_price,
  weekday_full_day_price = full_day_price,
  weekend_half_day_price = half_day_price,
  weekend_full_day_price = full_day_price
WHERE weekday_half_day_price IS NULL;

-- ✅ Add indexes for pricing columns to optimize query performance
-- Composite index for pricing-based queries (weekday/weekend pricing)
CREATE INDEX "properties_weekday_weekend_pricing_idx" ON "properties" USING btree ("weekday_half_day_price", "weekend_half_day_price", "weekday_full_day_price", "weekend_full_day_price");

-- Individual indexes for specific pricing searches (with NULL filtering)
CREATE INDEX "properties_weekday_half_day_price_idx" ON "properties" USING btree ("weekday_half_day_price") WHERE "weekday_half_day_price" IS NOT NULL;
CREATE INDEX "properties_weekday_full_day_price_idx" ON "properties" USING btree ("weekday_full_day_price") WHERE "weekday_full_day_price" IS NOT NULL;
CREATE INDEX "properties_weekend_half_day_price_idx" ON "properties" USING btree ("weekend_half_day_price") WHERE "weekend_half_day_price" IS NOT NULL;
CREATE INDEX "properties_weekend_full_day_price_idx" ON "properties" USING btree ("weekend_full_day_price") WHERE "weekend_full_day_price" IS NOT NULL;

-- Advanced composite indexes for active properties with pricing filters
CREATE INDEX "properties_active_weekday_pricing_idx" ON "properties" USING btree ("status", "weekday_half_day_price", "weekday_full_day_price", "featured", "created_at") WHERE "status" = 'active';
CREATE INDEX "properties_active_weekend_pricing_idx" ON "properties" USING btree ("status", "weekend_half_day_price", "weekend_full_day_price", "featured", "created_at") WHERE "status" = 'active';