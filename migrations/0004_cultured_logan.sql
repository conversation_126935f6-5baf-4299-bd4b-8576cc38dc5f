CREATE TABLE "sms_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"template_id" integer,
	"recipient_phone" text NOT NULL,
	"message_content" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"twilio_message_sid" text,
	"error_message" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sms_templates" (
	"id" serial PRIMARY KEY NOT NULL,
	"key" text NOT NULL,
	"name" text NOT NULL,
	"content" text NOT NULL,
	"dlt_template_id" text NOT NULL,
	"category" text DEFAULT 'transactional' NOT NULL,
	"variables" json DEFAULT '[]'::json NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "sms_templates_key_unique" UNIQUE("key")
);
--> statement-breakpoint
ALTER TABLE "otp_tokens" ADD COLUMN "is_used" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "weekday_half_day_price" double precision;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "weekday_full_day_price" double precision;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "weekend_half_day_price" double precision;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "weekend_full_day_price" double precision;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "videos" json DEFAULT '[]'::json NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "is_verified" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "sms_logs" ADD CONSTRAINT "sms_logs_template_id_sms_templates_id_fk" FOREIGN KEY ("template_id") REFERENCES "public"."sms_templates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "sms_logs_template_id_idx" ON "sms_logs" USING btree ("template_id");--> statement-breakpoint
CREATE INDEX "sms_logs_recipient_phone_idx" ON "sms_logs" USING btree ("recipient_phone");--> statement-breakpoint
CREATE INDEX "sms_logs_status_idx" ON "sms_logs" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sms_logs_created_at_idx" ON "sms_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "sms_templates_key_idx" ON "sms_templates" USING btree ("key");--> statement-breakpoint
CREATE INDEX "sms_templates_status_idx" ON "sms_templates" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sms_templates_category_idx" ON "sms_templates" USING btree ("category");--> statement-breakpoint
CREATE INDEX "sms_templates_created_at_idx" ON "sms_templates" USING btree ("created_at");