-- Rollback script for 0005_remove_purpose_column.sql
-- This script adds back the purpose column to otp_tokens table
-- Note: Original data in the purpose column will be lost and needs to be repopulated

DO $$
BEGIN
    -- Check if the purpose column doesn't exist and add it back
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'otp_tokens' 
        AND column_name = 'purpose'
        AND table_schema = 'public'
    ) THEN
        -- Add the purpose column back with a default value
        ALTER TABLE otp_tokens ADD COLUMN purpose TEXT DEFAULT 'authentication';
        RAISE NOTICE 'Purpose column added back to otp_tokens table';
        
        -- You may want to update existing records with appropriate purpose values
        -- UPDATE otp_tokens SET purpose = 'authentication' WHERE type = 'login';
        -- UPDATE otp_tokens SET purpose = 'registration' WHERE type = 'signup';
        -- etc.
        
    ELSE
        RAISE NOTICE 'Purpose column already exists in otp_tokens table';
    END IF;
END $$;

-- Note: Rolling back this migration will restore the column structure
-- but the original purpose data will be lost and need manual restoration