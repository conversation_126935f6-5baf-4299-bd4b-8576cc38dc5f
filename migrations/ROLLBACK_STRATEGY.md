# Database Migration Rollback Strategy

## Overview
This document outlines the rollback strategy for database migrations in the Farmhouse project. Every migration has a corresponding rollback script to ensure safe schema reversals during deployment failures or emergency situations.

## Naming Convention

### Forward Migrations
- Format: `XXXX_migration_description.sql`
- Example: `0007_add_pricing_columns.sql`

### Rollback Scripts
- Format: `XXXX_migration_description_rollback.sql`
- Example: `0007_add_pricing_columns_rollback.sql`

## File Structure
```
migrations/
├── 0000_faulty_shinobi_shaw.sql
├── 0000_faulty_shinobi_shaw_rollback.sql
├── 0001_add_performance_indexes.sql
├── 0001_add_performance_indexes_rollback.sql
├── ...
├── ROLLBACK_STRATEGY.md
└── rollback_validation.js
```

## Rollback Safety Levels

### 🟢 Safe Rollbacks (No Data Loss)
- **Index removals**: Dropping indexes only impacts performance
- **View removals**: Can be recreated from source tables
- **Function/Trigger removals**: Logic can be restored

### 🟡 Caution Rollbacks (Potential Data Loss)
- **Column additions**: Rolling back loses data in those columns
- **Table modifications**: May lose specific field data

### 🔴 Destructive Rollbacks (Permanent Data Loss)
- **Table drops**: Complete data loss for entire entities
- **Type drops**: May cause cascading failures
- **Constraint removals**: Data integrity may be compromised

## Rollback Execution Order

### For Single Migration Rollback
```sql
-- Execute the specific rollback script
\i migrations/XXXX_migration_name_rollback.sql
```

### For Multiple Migration Rollback
Execute rollback scripts in **reverse order**:
```bash
# Roll back from migration 0008 to 0005
\i migrations/0008_add_pricing_indexes_rollback.sql
\i migrations/0007_add_pricing_columns_rollback.sql
\i migrations/0006_add_videos_column_rollback.sql
```

## Pre-Rollback Checklist

### 1. Data Backup
```sql
-- Create backup of affected tables
CREATE TABLE properties_backup AS SELECT * FROM properties;
CREATE TABLE bookings_backup AS SELECT * FROM bookings;
-- ... backup other critical tables
```

### 2. Impact Assessment
- Review rollback script warnings
- Identify data that will be permanently lost
- Notify stakeholders of downtime/data loss
- Plan data recovery if needed

### 3. Environment Validation
- Test rollback in staging environment first
- Verify application functionality post-rollback
- Check for broken queries or missing indexes

## Emergency Rollback Procedure

### Step 1: Stop Application Services
```bash
docker-compose down
# or
pm2 stop all
```

### Step 2: Create Emergency Backup
```sql
pg_dump farmhouse_db > emergency_backup_$(date +%Y%m%d_%H%M%S).sql
```

### Step 3: Execute Rollback
```sql
-- Connect to database
psql -d farmhouse_db

-- Execute rollback script(s)
\i migrations/XXXX_migration_rollback.sql
```

### Step 4: Validate Database State
```sql
-- Check table structures
\dt
\di -- List indexes

-- Verify critical data
SELECT COUNT(*) FROM properties;
SELECT COUNT(*) FROM bookings;
```

### Step 5: Restart Services
```bash
docker-compose up -d
# or
pm2 start all
```

## Rollback Script Standards

### Header Template
```sql
-- Rollback script for XXXX_migration_name.sql
-- This script [brief description of what it rolls back]
-- ⚠️ WARNING: [Specific warnings about data loss]

-- [Rollback operations in reverse order]

-- Note: [Additional notes about consequences and recovery]
```

### Best Practices
1. **Drop in reverse order**: Constraints, indexes, tables, types
2. **Use IF EXISTS**: Prevent errors if objects don't exist
3. **Explicit warnings**: Clear data loss warnings
4. **Detailed comments**: Explain each step
5. **Recovery notes**: Suggest data recovery approaches

## Migration Journal Updates

When creating rollback scripts, ensure the `meta/_journal.json` is properly maintained:

```json
{
  "version": "7",
  "dialect": "postgresql",
  "entries": [
    {
      "idx": 0,
      "version": "7", 
      "when": 1747765846347,
      "tag": "0000_faulty_shinobi_shaw",
      "breakpoints": true
    }
  ]
}
```

## Testing Strategy

### Automated Testing
1. Run migration forward
2. Verify schema changes
3. Execute rollback
4. Verify original schema restored
5. Test application functionality

### Manual Testing
1. Test rollback in development
2. Validate in staging environment
3. Performance test post-rollback
4. User acceptance testing

## Monitoring and Alerts

### Post-Rollback Monitoring
- Database connection health
- Query performance metrics
- Application error rates
- Data consistency checks

### Alert Thresholds
- Query time > 2x baseline
- Error rate > 1%
- Missing critical data

## Recovery Procedures

### Data Recovery Post-Rollback
1. **From backups**: Restore specific table data
2. **From logs**: Reconstruct transactions
3. **From exports**: Import critical data
4. **Manual entry**: Re-enter lost configuration

### Performance Recovery
1. **Index recreation**: Add missing performance indexes
2. **Statistics update**: Run ANALYZE on tables
3. **Cache warming**: Pre-populate application caches
4. **Connection pooling**: Adjust pool sizes if needed

## Version Control Integration

### Git Hooks
Consider adding pre-commit hooks to:
- Validate rollback script exists for new migrations
- Check rollback script syntax
- Ensure proper documentation

### Code Review Requirements
- Every migration PR must include rollback script
- Rollback script must be reviewed by DBA/senior dev
- Documentation updates required for destructive changes

## Troubleshooting Common Issues

### "Object does not exist" errors
```sql
-- Use IF EXISTS clauses
DROP TABLE IF EXISTS table_name;
DROP INDEX IF EXISTS index_name;
```

### Foreign key constraint violations
```sql
-- Drop foreign keys before tables
ALTER TABLE child_table DROP CONSTRAINT IF EXISTS fk_constraint_name;
DROP TABLE IF EXISTS parent_table;
```

### Permission issues
```sql
-- Ensure proper database permissions
GRANT ALL PRIVILEGES ON DATABASE farmhouse_db TO migration_user;
```

## Contact Information

- **Database Team**: [Your DB team contact]
- **DevOps Team**: [Your DevOps contact]  
- **Emergency Escalation**: [Emergency contact]

---
**Last Updated**: July 2025  
**Next Review**: August 2025