import { pgTable, index, unique, serial, text, json, timestamp, foreignKey, integer, doublePrecision, boolean, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const bookingType = pgEnum("booking_type", ['morning', 'full_day'])
export const reviewStars = pgEnum("review_stars", ['1', '2', '3', '4', '5'])
export const userRole = pgEnum("user_role", ['user', 'owner'])


export const smsTemplates = pgTable("sms_templates", {
	id: serial().primaryKey().notNull(),
	key: text().notNull(),
	name: text().notNull(),
	content: text().notNull(),
	dltTemplateId: text("dlt_template_id").notNull(),
	category: text().default('transactional').notNull(),
	variables: json().default([]).notNull(),
	status: text().default('active').notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("sms_templates_category_idx").using("btree", table.category.asc().nullsLast().op("text_ops")),
	index("sms_templates_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("sms_templates_key_idx").using("btree", table.key.asc().nullsLast().op("text_ops")),
	index("sms_templates_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	unique("sms_templates_key_unique").on(table.key),
]);

export const ownerInterestRequests = pgTable("owner_interest_requests", {
	id: serial().primaryKey().notNull(),
	fullName: text("full_name").notNull(),
	email: text().notNull(),
	phone: text().notNull(),
	propertyLocation: text("property_location").notNull(),
	propertyType: text("property_type").notNull(),
	propertySize: text("property_size"),
	expectedRevenue: text("expected_revenue"),
	currentOccupancy: text("current_occupancy"),
	amenities: json().default([]).notNull(),
	propertyDescription: text("property_description"),
	experience: text(),
	availability: text(),
	additionalInfo: text("additional_info"),
	status: text().default('pending').notNull(),
	contactedAt: timestamp("contacted_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("owner_interest_requests_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("owner_interest_requests_email_idx").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("owner_interest_requests_phone_idx").using("btree", table.phone.asc().nullsLast().op("text_ops")),
	index("owner_interest_requests_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
]);

export const properties = pgTable("properties", {
	id: serial().primaryKey().notNull(),
	ownerId: integer("owner_id").notNull(),
	title: text().notNull(),
	description: text().notNull(),
	location: text().notNull(),
	halfDayPrice: doublePrecision("half_day_price").notNull(),
	fullDayPrice: doublePrecision("full_day_price").notNull(),
	bedrooms: integer().notNull(),
	bathrooms: integer().notNull(),
	amenities: json().notNull(),
	images: json().notNull(),
	status: text().default('active').notNull(),
	featured: boolean().default(false),
	latitude: doublePrecision(),
	longitude: doublePrecision(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	weekdayHalfDayPrice: doublePrecision("weekday_half_day_price"),
	weekdayFullDayPrice: doublePrecision("weekday_full_day_price"),
	weekendHalfDayPrice: doublePrecision("weekend_half_day_price"),
	weekendFullDayPrice: doublePrecision("weekend_full_day_price"),
	videos: json().default([]).notNull(),
}, (table) => [
	index("properties_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("properties_featured_idx").using("btree", table.featured.asc().nullsLast().op("bool_ops")),
	index("properties_location_idx").using("btree", table.location.asc().nullsLast().op("text_ops")),
	index("properties_owner_id_idx").using("btree", table.ownerId.asc().nullsLast().op("int4_ops")),
	index("properties_price_range_idx").using("btree", table.halfDayPrice.asc().nullsLast().op("float8_ops"), table.fullDayPrice.asc().nullsLast().op("float8_ops")),
	index("properties_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.ownerId],
			foreignColumns: [users.id],
			name: "properties_owner_id_users_id_fk"
		}),
]);

export const smsLogs = pgTable("sms_logs", {
	id: serial().primaryKey().notNull(),
	templateId: integer("template_id"),
	recipientPhone: text("recipient_phone").notNull(),
	messageContent: text("message_content").notNull(),
	status: text().default('pending').notNull(),
	twilioMessageSid: text("twilio_message_sid"),
	errorMessage: text("error_message"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("sms_logs_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("sms_logs_recipient_phone_idx").using("btree", table.recipientPhone.asc().nullsLast().op("text_ops")),
	index("sms_logs_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("sms_logs_template_id_idx").using("btree", table.templateId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.templateId],
			foreignColumns: [smsTemplates.id],
			name: "sms_logs_template_id_sms_templates_id_fk"
		}),
]);

export const users = pgTable("users", {
	id: serial().primaryKey().notNull(),
	username: text().notNull(),
	password: text().notNull(),
	email: text().notNull(),
	fullName: text("full_name").notNull(),
	phone: text(),
	address: text(),
	bio: text(),
	role: userRole().default('user').notNull(),
	termsAccepted: boolean("terms_accepted").default(false),
	privacyPolicyAccepted: boolean("privacy_policy_accepted").default(false),
	cookiePolicyAccepted: boolean("cookie_policy_accepted").default(false),
	dataProcessingConsent: boolean("data_processing_consent").default(false),
	marketingConsent: boolean("marketing_consent").default(false),
	consentTimestamp: timestamp("consent_timestamp", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	isVerified: boolean("is_verified").default(false),
}, (table) => [
	index("users_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("users_phone_idx").using("btree", table.phone.asc().nullsLast().op("text_ops")),
	index("users_role_idx").using("btree", table.role.asc().nullsLast().op("enum_ops")),
	unique("users_username_unique").on(table.username),
	unique("users_email_unique").on(table.email),
]);

export const encryptionKeys = pgTable("encryption_keys", {
	id: serial().primaryKey().notNull(),
	keyId: text("key_id").notNull(),
	keyType: text("key_type").notNull(),
	encryptedKey: text("encrypted_key").notNull(),
	keyVersion: integer("key_version").notNull(),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }),
}, (table) => [
	index("encryption_keys_is_active_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("encryption_keys_key_id_idx").using("btree", table.keyId.asc().nullsLast().op("text_ops")),
	unique("encryption_keys_key_id_unique").on(table.keyId),
]);

export const gstRateConfigurations = pgTable("gst_rate_configurations", {
	id: serial().primaryKey().notNull(),
	serviceType: text("service_type").notNull(),
	hsnSacCode: text("hsn_sac_code").notNull(),
	rateStructure: json("rate_structure").notNull(),
	effectiveFrom: text("effective_from").notNull(),
	effectiveTo: text("effective_to"),
	createdBy: integer("created_by"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("gst_rate_config_effective_dates_idx").using("btree", table.effectiveFrom.asc().nullsLast().op("text_ops"), table.effectiveTo.asc().nullsLast().op("text_ops")),
	index("gst_rate_config_hsn_sac_idx").using("btree", table.hsnSacCode.asc().nullsLast().op("text_ops")),
	index("gst_rate_config_service_type_idx").using("btree", table.serviceType.asc().nullsLast().op("text_ops")),
	index("gst_rate_config_unique_idx").using("btree", table.serviceType.asc().nullsLast().op("text_ops"), table.hsnSacCode.asc().nullsLast().op("text_ops"), table.effectiveFrom.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "gst_rate_configurations_created_by_users_id_fk"
		}),
]);

export const idempotencyKeys = pgTable("idempotency_keys", {
	id: serial().primaryKey().notNull(),
	key: text().notNull(),
	resourceType: text("resource_type").notNull(),
	resourceId: integer("resource_id"),
	responseData: json("response_data"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
}, (table) => [
	index("idempotency_keys_expires_at_idx").using("btree", table.expiresAt.asc().nullsLast().op("timestamp_ops")),
	index("idempotency_keys_key_idx").using("btree", table.key.asc().nullsLast().op("text_ops")),
	unique("idempotency_keys_key_unique").on(table.key),
]);

export const paymentRoles = pgTable("payment_roles", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	permissions: json().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	unique("payment_roles_name_unique").on(table.name),
]);

export const gstRecords = pgTable("gst_records", {
	id: serial().primaryKey().notNull(),
	bookingId: integer("booking_id"),
	baseAmount: integer("base_amount").notNull(),
	transactionType: text("transaction_type").notNull(),
	serviceType: text("service_type").notNull(),
	hsnSacCode: text("hsn_sac_code").notNull(),
	supplierGstin: text("supplier_gstin"),
	recipientGstin: text("recipient_gstin"),
	supplierState: text("supplier_state").notNull(),
	recipientState: text("recipient_state").notNull(),
	placeOfSupply: text("place_of_supply").notNull(),
	gstRateConfigId: integer("gst_rate_config_id"),
	applicableDate: text("applicable_date").notNull(),
	cgstRate: doublePrecision("cgst_rate").default(0),
	sgstRate: doublePrecision("sgst_rate").default(0),
	cgstAmount: integer("cgst_amount").default(0),
	sgstAmount: integer("sgst_amount").default(0),
	igstRate: doublePrecision("igst_rate").default(0),
	igstAmount: integer("igst_amount").default(0),
	cessRate: doublePrecision("cess_rate").default(0),
	cessAmount: integer("cess_amount").default(0),
	totalGst: integer("total_gst").notNull(),
	totalAmount: integer("total_amount").notNull(),
	invoiceNumber: text("invoice_number"),
	invoiceDate: text("invoice_date"),
	irn: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("gst_records_applicable_date_idx").using("btree", table.applicableDate.asc().nullsLast().op("text_ops")),
	index("gst_records_booking_id_idx").using("btree", table.bookingId.asc().nullsLast().op("int4_ops")),
	index("gst_records_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("gst_records_service_type_idx").using("btree", table.serviceType.asc().nullsLast().op("text_ops")),
	index("gst_records_supplier_state_idx").using("btree", table.supplierState.asc().nullsLast().op("text_ops")),
	index("gst_records_transaction_type_idx").using("btree", table.transactionType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.bookingId],
			foreignColumns: [bookings.id],
			name: "gst_records_booking_id_bookings_id_fk"
		}),
	foreignKey({
			columns: [table.gstRateConfigId],
			foreignColumns: [gstRateConfigurations.id],
			name: "gst_records_gst_rate_config_id_gst_rate_configurations_id_fk"
		}),
]);

export const userPaymentRoles = pgTable("user_payment_roles", {
	id: serial().primaryKey().notNull(),
	userId: integer("user_id"),
	roleId: integer("role_id"),
	grantedBy: integer("granted_by"),
	grantedAt: timestamp("granted_at", { mode: 'string' }).defaultNow().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }),
	isActive: boolean("is_active").default(true),
}, (table) => [
	index("user_payment_roles_unique_idx").using("btree", table.userId.asc().nullsLast().op("int4_ops"), table.roleId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_payment_roles_user_id_users_id_fk"
		}),
	foreignKey({
			columns: [table.roleId],
			foreignColumns: [paymentRoles.id],
			name: "user_payment_roles_role_id_payment_roles_id_fk"
		}),
	foreignKey({
			columns: [table.grantedBy],
			foreignColumns: [users.id],
			name: "user_payment_roles_granted_by_users_id_fk"
		}),
]);

export const webhookEvents = pgTable("webhook_events", {
	id: serial().primaryKey().notNull(),
	eventId: text("event_id").notNull(),
	eventType: text("event_type").notNull(),
	payload: json().notNull(),
	signatureHash: text("signature_hash").notNull(),
	status: text().default('received'),
	processedAt: timestamp("processed_at", { mode: 'string' }),
	retryCount: integer("retry_count").default(0),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("webhook_events_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("webhook_events_event_id_idx").using("btree", table.eventId.asc().nullsLast().op("text_ops")),
	index("webhook_events_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	unique("webhook_events_event_id_unique").on(table.eventId),
]);

export const otpTokens = pgTable("otp_tokens", {
	id: serial().primaryKey().notNull(),
	identifier: text().notNull(),
	code: text().notNull(),
	type: text().notNull(),
	attempts: integer().default(0).notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	isUsed: boolean("is_used").default(false),
}, (table) => [
	index("otp_tokens_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("otp_tokens_expires_at_idx").using("btree", table.expiresAt.asc().nullsLast().op("timestamp_ops")),
	index("otp_tokens_identifier_idx").using("btree", table.identifier.asc().nullsLast().op("text_ops")),
	index("otp_tokens_type_idx").using("btree", table.type.asc().nullsLast().op("text_ops")),
]);

export const whatsappConversations = pgTable("whatsapp_conversations", {
	id: serial().primaryKey().notNull(),
	propertyId: integer("property_id").notNull(),
	phoneNumber: text("phone_number").notNull(),
	conversationState: json("conversation_state").default({}),
	lastMessageAt: timestamp("last_message_at", { mode: 'string' }),
	isActive: boolean("is_active").default(true),
	metadata: json().default({}),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("whatsapp_conversations_is_active_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("whatsapp_conversations_last_message_at_idx").using("btree", table.lastMessageAt.asc().nullsLast().op("timestamp_ops")),
	index("whatsapp_conversations_phone_number_idx").using("btree", table.phoneNumber.asc().nullsLast().op("text_ops")),
	index("whatsapp_conversations_property_id_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops")),
	index("whatsapp_conversations_unique_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops"), table.phoneNumber.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.propertyId],
			foreignColumns: [properties.id],
			name: "whatsapp_conversations_property_id_properties_id_fk"
		}),
]);

export const bookings = pgTable("bookings", {
	id: serial().primaryKey().notNull(),
	propertyId: integer("property_id").notNull(),
	userId: integer("user_id").notNull(),
	bookingDate: text("booking_date").notNull(),
	bookingType: bookingType("booking_type").notNull(),
	guests: integer().notNull(),
	totalPrice: doublePrecision("total_price").notNull(),
	status: text().default('confirmed').notNull(),
	specialRequests: text("special_requests"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	paymentStatus: text("payment_status").default('pending'),
	advanceAmount: integer("advance_amount"),
	remainingAmount: integer("remaining_amount"),
	gstAmount: integer("gst_amount"),
	paymentDueDate: text("payment_due_date"),
	paymentExpiry: timestamp("payment_expiry", { mode: 'string' }),
}, (table) => [
	index("bookings_availability_idx").using("btree", table.propertyId.asc().nullsLast().op("text_ops"), table.bookingDate.asc().nullsLast().op("int4_ops")),
	index("bookings_booking_date_idx").using("btree", table.bookingDate.asc().nullsLast().op("text_ops")),
	index("bookings_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("bookings_payment_status_idx").using("btree", table.paymentStatus.asc().nullsLast().op("text_ops")),
	index("bookings_property_id_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops")),
	index("bookings_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("bookings_user_id_idx").using("btree", table.userId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.propertyId],
			foreignColumns: [properties.id],
			name: "bookings_property_id_properties_id_fk"
		}),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "bookings_user_id_users_id_fk"
		}),
]);

export const reviews = pgTable("reviews", {
	id: serial().primaryKey().notNull(),
	propertyId: integer("property_id").notNull(),
	userId: integer("user_id").notNull(),
	bookingId: integer("booking_id"),
	rating: reviewStars().notNull(),
	comment: text(),
	ownerResponse: text("owner_response"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("reviews_booking_id_idx").using("btree", table.bookingId.asc().nullsLast().op("int4_ops")),
	index("reviews_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("reviews_property_id_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops")),
	index("reviews_rating_idx").using("btree", table.rating.asc().nullsLast().op("enum_ops")),
	index("reviews_user_id_idx").using("btree", table.userId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.propertyId],
			foreignColumns: [properties.id],
			name: "reviews_property_id_properties_id_fk"
		}),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "reviews_user_id_users_id_fk"
		}),
	foreignKey({
			columns: [table.bookingId],
			foreignColumns: [bookings.id],
			name: "reviews_booking_id_bookings_id_fk"
		}),
]);

export const calendarBookings = pgTable("calendar_bookings", {
	id: serial().primaryKey().notNull(),
	propertyId: integer("property_id").notNull(),
	startDate: text("start_date").notNull(),
	endDate: text("end_date").notNull(),
	status: text().default('confirmed').notNull(),
	bookingType: text("booking_type").default('direct'),
	guestName: text("guest_name"),
	guestPhone: text("guest_phone"),
	guestCount: integer("guest_count"),
	notes: text(),
	source: text().default('website'),
	externalId: text("external_id"),
	createdBy: integer("created_by"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("calendar_bookings_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("calendar_bookings_created_by_idx").using("btree", table.createdBy.asc().nullsLast().op("int4_ops")),
	index("calendar_bookings_date_range_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops"), table.startDate.asc().nullsLast().op("text_ops"), table.endDate.asc().nullsLast().op("text_ops")),
	index("calendar_bookings_overlap_idx").using("btree", table.propertyId.asc().nullsLast().op("text_ops"), table.startDate.asc().nullsLast().op("int4_ops"), table.endDate.asc().nullsLast().op("text_ops")),
	index("calendar_bookings_property_id_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops")),
	index("calendar_bookings_source_idx").using("btree", table.source.asc().nullsLast().op("text_ops")),
	index("calendar_bookings_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.propertyId],
			foreignColumns: [properties.id],
			name: "calendar_bookings_property_id_properties_id_fk"
		}),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "calendar_bookings_created_by_users_id_fk"
		}),
]);

export const calendarSyncStatus = pgTable("calendar_sync_status", {
	id: serial().primaryKey().notNull(),
	propertyId: integer("property_id").notNull(),
	calendarType: text("calendar_type").notNull(),
	lastSyncAt: timestamp("last_sync_at", { mode: 'string' }),
	syncToken: text("sync_token"),
	isActive: boolean("is_active").default(true),
	webhookUrl: text("webhook_url"),
	syncSettings: json("sync_settings").default({}),
	errorMessage: text("error_message"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("calendar_sync_status_calendar_type_idx").using("btree", table.calendarType.asc().nullsLast().op("text_ops")),
	index("calendar_sync_status_is_active_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("calendar_sync_status_property_id_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops")),
	index("calendar_sync_status_unique_idx").using("btree", table.propertyId.asc().nullsLast().op("int4_ops"), table.calendarType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.propertyId],
			foreignColumns: [properties.id],
			name: "calendar_sync_status_property_id_properties_id_fk"
		}),
]);

export const deviceVerifications = pgTable("device_verifications", {
	id: serial().primaryKey().notNull(),
	userId: integer("user_id").notNull(),
	deviceId: text("device_id").notNull(),
	deviceFingerprint: text("device_fingerprint").notNull(),
	deviceInfo: json("device_info"),
	verificationMethod: text("verification_method").notNull(),
	isVerified: boolean("is_verified").default(false),
	verifiedAt: timestamp("verified_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }),
}, (table) => [
	index("device_verifications_device_id_idx").using("btree", table.deviceId.asc().nullsLast().op("text_ops")),
	index("device_verifications_is_verified_idx").using("btree", table.isVerified.asc().nullsLast().op("bool_ops")),
	index("device_verifications_user_id_idx").using("btree", table.userId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "device_verifications_user_id_users_id_fk"
		}),
]);

export const securitySessions = pgTable("security_sessions", {
	id: serial().primaryKey().notNull(),
	userId: integer("user_id").notNull(),
	sessionType: text("session_type").notNull(),
	sessionData: json("session_data"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	isActive: boolean("is_active").default(true),
}, (table) => [
	index("security_sessions_expires_at_idx").using("btree", table.expiresAt.asc().nullsLast().op("timestamp_ops")),
	index("security_sessions_session_type_idx").using("btree", table.sessionType.asc().nullsLast().op("text_ops")),
	index("security_sessions_user_id_idx").using("btree", table.userId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "security_sessions_user_id_users_id_fk"
		}),
]);

export const paymentOrders = pgTable("payment_orders", {
	id: serial().primaryKey().notNull(),
	razorpayOrderId: text("razorpay_order_id").notNull(),
	bookingId: integer("booking_id"),
	idempotencyKey: text("idempotency_key").notNull(),
	amount: integer().notNull(),
	currency: text().default('INR'),
	receipt: text(),
	status: text().default('created'),
	attempts: integer().default(0),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payment_orders_booking_id_idx").using("btree", table.bookingId.asc().nullsLast().op("int4_ops")),
	index("payment_orders_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("payment_orders_idempotency_key_idx").using("btree", table.idempotencyKey.asc().nullsLast().op("text_ops")),
	index("payment_orders_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.bookingId],
			foreignColumns: [bookings.id],
			name: "payment_orders_booking_id_bookings_id_fk"
		}),
	unique("payment_orders_razorpay_order_id_unique").on(table.razorpayOrderId),
	unique("payment_orders_idempotency_key_unique").on(table.idempotencyKey),
]);

export const paymentAuditLogs = pgTable("payment_audit_logs", {
	id: serial().primaryKey().notNull(),
	paymentOrderId: integer("payment_order_id"),
	paymentTransactionId: integer("payment_transaction_id"),
	action: text().notNull(),
	actorType: text("actor_type").notNull(),
	actorId: integer("actor_id"),
	actorIp: text("actor_ip"),
	actorUserAgent: text("actor_user_agent"),
	beforeState: json("before_state"),
	afterState: json("after_state"),
	metadata: json(),
	securityContext: json("security_context"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payment_audit_logs_action_idx").using("btree", table.action.asc().nullsLast().op("text_ops")),
	index("payment_audit_logs_actor_id_idx").using("btree", table.actorId.asc().nullsLast().op("int4_ops")),
	index("payment_audit_logs_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("payment_audit_logs_payment_order_id_idx").using("btree", table.paymentOrderId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.paymentOrderId],
			foreignColumns: [paymentOrders.id],
			name: "payment_audit_logs_payment_order_id_payment_orders_id_fk"
		}),
	foreignKey({
			columns: [table.paymentTransactionId],
			foreignColumns: [paymentTransactions.id],
			name: "payment_audit_logs_payment_transaction_id_payment_transactions_"
		}),
]);

export const paymentTransactions = pgTable("payment_transactions", {
	id: serial().primaryKey().notNull(),
	paymentOrderId: integer("payment_order_id"),
	razorpayPaymentId: text("razorpay_payment_id"),
	razorpaySignatureHash: text("razorpay_signature_hash"),
	signatureVerificationStatus: text("signature_verification_status").default('pending'),
	amount: integer().notNull(),
	method: text(),
	bank: text(),
	currency: text().default('INR'),
	exchangeRate: doublePrecision("exchange_rate"),
	baseCurrency: text("base_currency").default('INR'),
	status: text().default('initiated'),
	failureCode: text("failure_code"),
	failureReason: text("failure_reason"),
	gatewayResponse: json("gateway_response"),
	encryptionKeyId: text("encryption_key_id"),
	sensitiveDataHash: text("sensitive_data_hash"),
	securityFlags: json("security_flags"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payment_transactions_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("payment_transactions_payment_order_id_idx").using("btree", table.paymentOrderId.asc().nullsLast().op("int4_ops")),
	index("payment_transactions_razorpay_payment_id_idx").using("btree", table.razorpayPaymentId.asc().nullsLast().op("text_ops")),
	index("payment_transactions_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.paymentOrderId],
			foreignColumns: [paymentOrders.id],
			name: "payment_transactions_payment_order_id_payment_orders_id_fk"
		}),
	unique("payment_transactions_razorpay_payment_id_unique").on(table.razorpayPaymentId),
]);

export const securityIncidents = pgTable("security_incidents", {
	id: serial().primaryKey().notNull(),
	incidentType: text("incident_type").notNull(),
	severity: text().notNull(),
	description: text().notNull(),
	sourceIp: text("source_ip"),
	userId: integer("user_id"),
	paymentOrderId: integer("payment_order_id"),
	incidentData: json("incident_data"),
	status: text().default('open'),
	resolvedAt: timestamp("resolved_at", { mode: 'string' }),
	resolvedBy: integer("resolved_by"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("security_incidents_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("security_incidents_severity_idx").using("btree", table.severity.asc().nullsLast().op("text_ops")),
	index("security_incidents_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("security_incidents_type_idx").using("btree", table.incidentType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "security_incidents_user_id_users_id_fk"
		}),
	foreignKey({
			columns: [table.paymentOrderId],
			foreignColumns: [paymentOrders.id],
			name: "security_incidents_payment_order_id_payment_orders_id_fk"
		}),
	foreignKey({
			columns: [table.resolvedBy],
			foreignColumns: [users.id],
			name: "security_incidents_resolved_by_users_id_fk"
		}),
]);
