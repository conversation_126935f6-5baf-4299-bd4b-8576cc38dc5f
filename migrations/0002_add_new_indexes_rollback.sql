-- Rollback script for 0002_add_new_indexes.sql
-- This script drops all tables and indexes added in the forward migration
-- ⚠️ WARNING: This will delete OTP tokens and owner interest requests data

-- Drop all indexes first
DROP INDEX IF EXISTS "users_created_at_idx";
DROP INDEX IF EXISTS "users_role_idx";
DROP INDEX IF EXISTS "users_phone_idx";
DROP INDEX IF EXISTS "reviews_created_at_idx";
DROP INDEX IF EXISTS "reviews_rating_idx";
DROP INDEX IF EXISTS "reviews_booking_id_idx";
DROP INDEX IF EXISTS "reviews_user_id_idx";
DROP INDEX IF EXISTS "reviews_property_id_idx";
DROP INDEX IF EXISTS "properties_created_at_idx";
DROP INDEX IF EXISTS "properties_price_range_idx";
DROP INDEX IF EXISTS "properties_featured_idx";
DROP INDEX IF EXISTS "properties_status_idx";
DROP INDEX IF EXISTS "properties_location_idx";
DROP INDEX IF EXISTS "properties_owner_id_idx";
DROP INDEX IF EXISTS "bookings_created_at_idx";
DROP INDEX IF EXISTS "bookings_availability_idx";
DROP INDEX IF EXISTS "bookings_status_idx";
DROP INDEX IF EXISTS "bookings_booking_date_idx";
DROP INDEX IF EXISTS "bookings_user_id_idx";
DROP INDEX IF EXISTS "bookings_property_id_idx";
DROP INDEX IF EXISTS "owner_interest_requests_created_at_idx";
DROP INDEX IF EXISTS "owner_interest_requests_status_idx";
DROP INDEX IF EXISTS "owner_interest_requests_phone_idx";
DROP INDEX IF EXISTS "owner_interest_requests_email_idx";
DROP INDEX IF EXISTS "otp_tokens_created_at_idx";
DROP INDEX IF EXISTS "otp_tokens_expires_at_idx";
DROP INDEX IF EXISTS "otp_tokens_type_idx";
DROP INDEX IF EXISTS "otp_tokens_identifier_idx";

-- Drop tables (this will result in data loss)
DROP TABLE IF EXISTS "owner_interest_requests";
DROP TABLE IF EXISTS "otp_tokens";

-- Note: Rolling back this migration will result in data loss for:
-- - All OTP tokens (users will need to request new ones)
-- - All owner interest requests (permanent data loss)
-- - Performance will be impacted due to removed indexes