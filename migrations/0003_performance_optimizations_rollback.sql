-- Rollback script for 0003_performance_optimizations.sql
-- This script drops all performance optimizations added in the forward migration
-- Safe to execute - primarily removes indexes and optimization features

-- Drop all indexes created in the migration
DROP INDEX IF EXISTS "property_availability_cache_price_idx";
DROP INDEX IF EXISTS "property_availability_cache_location_idx";
DROP INDEX IF EXISTS "properties_owner_management_idx";
DROP INDEX IF EXISTS "properties_amenities_gin_idx";
DROP INDEX IF EXISTS "reviews_property_rating_idx";
DROP INDEX IF EXISTS "bookings_date_range_idx";
DROP INDEX IF EXISTS "bookings_user_recent_idx";
DROP INDEX IF EXISTS "bookings_property_analytics_idx";
DROP INDEX IF EXISTS "properties_search_vector_idx";
DROP INDEX IF EXISTS "properties_active_full_day_price_idx";
DROP INDEX IF EXISTS "properties_active_half_day_price_idx";
DROP INDEX IF EXISTS "properties_location_geo_idx";
DROP INDEX IF EXISTS "properties_advanced_search_idx";

-- Drop triggers and functions
DROP TRIGGER IF EXISTS properties_search_vector_trigger ON "properties";
DROP FUNCTION IF EXISTS update_properties_search_vector();

-- Drop cache table if it was created
DROP TABLE IF EXISTS "property_availability_cache";

-- Remove columns added to existing tables
DO $$
BEGIN
    -- Remove search_vector column from properties table
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'properties' AND column_name = 'search_vector'
    ) THEN
        ALTER TABLE properties DROP COLUMN search_vector;
        RAISE NOTICE 'Removed search_vector column from properties table';
    END IF;
    
    -- Remove latitude column if it was added
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'properties' AND column_name = 'latitude'
    ) THEN
        ALTER TABLE properties DROP COLUMN latitude;
        RAISE NOTICE 'Removed latitude column from properties table';
    END IF;
    
    -- Remove longitude column if it was added
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'properties' AND column_name = 'longitude'
    ) THEN
        ALTER TABLE properties DROP COLUMN longitude;
        RAISE NOTICE 'Removed longitude column from properties table';
    END IF;
END $$;

-- Note: Rolling back this migration will impact performance significantly
-- The system will still function but queries will be slower
-- Consider the performance implications before executing this rollback
-- 
-- Data loss will be minimal, mainly affecting:
-- - Full-text search capabilities (search_vector data)
-- - Cached availability data (if property_availability_cache table existed)
-- - Geographic coordinate data (latitude/longitude if they were added)