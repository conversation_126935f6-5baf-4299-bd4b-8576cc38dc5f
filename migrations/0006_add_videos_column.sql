-- Add videos column to properties table for video storage and streaming
-- This migration adds support for video URLs to be stored in the properties table

DO $$
BEGIN
    -- Check if the videos column doesn't exist and add it
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'properties' 
        AND column_name = 'videos'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE properties ADD COLUMN videos JSONB DEFAULT '[]'::JSONB NOT NULL;
        RAISE NOTICE 'Videos column added to properties table';
    ELSE
        RAISE NOTICE 'Videos column already exists in properties table';
    END IF;
END $$;