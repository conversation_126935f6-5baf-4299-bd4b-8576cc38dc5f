-- Rollback script for 0004_add_sms_template_system.sql
-- This script drops all SMS-related tables and indexes added in the forward migration
-- ⚠️ WARNING: This will permanently delete all SMS template and log data!

-- Drop all indexes first
DROP INDEX IF EXISTS "sms_logs_phone_date_idx";
DROP INDEX IF EXISTS "sms_logs_template_status_idx";
DROP INDEX IF EXISTS "sms_logs_created_at_idx";
DROP INDEX IF EXISTS "sms_logs_status_idx";
DROP INDEX IF EXISTS "sms_logs_recipient_phone_idx";
DROP INDEX IF EXISTS "sms_logs_template_id_idx";
DROP INDEX IF EXISTS "sms_templates_created_at_idx";
DROP INDEX IF EXISTS "sms_templates_category_idx";
DROP INDEX IF EXISTS "sms_templates_status_idx";
DROP INDEX IF EXISTS "sms_templates_key_idx";

-- Drop tables (drop dependent table first)
DROP TABLE IF EXISTS "sms_logs";
DROP TABLE IF EXISTS "sms_templates";

-- Note: This rollback will result in permanent data loss for:
-- - All SMS templates (system templates will need to be recreated)
-- - All SMS logs and audit trails
-- 
-- Ensure you have proper backups before executing this rollback!
-- You may want to export SMS templates before rollback:
-- SELECT * FROM sms_templates WHERE status = 'active';