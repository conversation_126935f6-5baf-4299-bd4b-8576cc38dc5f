-- Rollback script for 0000_faulty_shinobi_shaw.sql
-- ⚠️ WARNING: This will completely destroy the database schema and ALL DATA!
-- Only use this in development or emergency situations with proper data backup

-- Drop foreign key constraints first (in reverse order)
ALTER TABLE "reviews" DROP CONSTRAINT IF EXISTS "reviews_booking_id_bookings_id_fk";
ALTER TABLE "reviews" DROP CONSTRAINT IF EXISTS "reviews_user_id_users_id_fk";
ALTER TABLE "reviews" DROP CONSTRAINT IF EXISTS "reviews_property_id_properties_id_fk";
ALTER TABLE "properties" DROP CONSTRAINT IF EXISTS "properties_owner_id_users_id_fk";
ALTER TABLE "bookings" DROP CONSTRAINT IF EXISTS "bookings_user_id_users_id_fk";
ALTER TABLE "bookings" DROP CONSTRAINT IF EXISTS "bookings_property_id_properties_id_fk";

-- Drop tables (in reverse order to avoid dependency issues)
DROP TABLE IF EXISTS "reviews";
DROP TABLE IF EXISTS "bookings";
DROP TABLE IF EXISTS "properties";
DROP TABLE IF EXISTS "users";

-- Drop custom types
DROP TYPE IF EXISTS "public"."user_role";
DROP TYPE IF EXISTS "public"."review_stars";
DROP TYPE IF EXISTS "public"."booking_type";

-- Note: This rollback will result in complete data loss
-- Ensure you have proper backups before executing this script