-- Migration: Prevent Duplicate Bookings
-- Fix race condition allowing multiple bookings on same date/time

-- Add unique constraint to prevent future duplicates
-- For confirmed and pending_payment bookings, only one booking per property/date/type allowed
CREATE UNIQUE INDEX IF NOT EXISTS bookings_no_duplicates_idx 
ON bookings (property_id, booking_date, booking_type) 
WHERE status IN ('confirmed', 'pending_payment');

-- Add partial unique constraint for full_day bookings (blocks any other booking)
CREATE UNIQUE INDEX IF NOT EXISTS bookings_fullday_exclusive_idx 
ON bookings (property_id, booking_date) 
WHERE booking_type = 'full_day' AND status IN ('confirmed', 'pending_payment');