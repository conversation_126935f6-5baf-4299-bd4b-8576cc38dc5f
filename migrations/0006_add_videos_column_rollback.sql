-- Rollback script for 0006_add_videos_column.sql
-- This script removes the videos column from properties table
-- ⚠️ WARNING: This will permanently delete all video data stored in properties

DO $$
BEGIN
    -- Check if the videos column exists and remove it
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'properties' 
        AND column_name = 'videos'
        AND table_schema = 'public'
    ) THEN
        -- Before dropping, you might want to backup the data
        -- CREATE TABLE properties_videos_backup AS 
        --   SELECT id, videos FROM properties WHERE videos IS NOT NULL AND videos != '[]'::JSONB;
        
        ALTER TABLE properties DROP COLUMN videos;
        RAISE NOTICE 'Videos column removed from properties table';
    ELSE
        RAISE NOTICE 'Videos column does not exist in properties table';
    END IF;
END $$;

-- Note: Rolling back this migration will result in permanent data loss
-- Consider backing up video data before executing this rollback:
-- CREATE TABLE properties_videos_backup AS 
--   SELECT id, videos FROM properties WHERE videos IS NOT NULL AND videos != '[]'::JSON<PERSON>;