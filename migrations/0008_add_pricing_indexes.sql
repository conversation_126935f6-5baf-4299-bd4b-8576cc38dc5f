-- Add indexes for weekday/weekend pricing columns to improve query performance
-- This addresses the missing indexes from migration 0007_add_pricing_columns.sql

-- Composite index for pricing-based queries (weekday/weekend pricing)
CREATE INDEX "properties_weekday_weekend_pricing_idx" ON "properties" USING btree ("weekday_half_day_price", "weekend_half_day_price", "weekday_full_day_price", "weekend_full_day_price");

-- Individual indexes for specific pricing searches
CREATE INDEX "properties_weekday_half_day_price_idx" ON "properties" USING btree ("weekday_half_day_price") WHERE "weekday_half_day_price" IS NOT NULL;
CREATE INDEX "properties_weekday_full_day_price_idx" ON "properties" USING btree ("weekday_full_day_price") WHERE "weekday_full_day_price" IS NOT NULL;
CREATE INDEX "properties_weekend_half_day_price_idx" ON "properties" USING btree ("weekend_half_day_price") WHERE "weekend_half_day_price" IS NOT NULL;
CREATE INDEX "properties_weekend_full_day_price_idx" ON "properties" USING btree ("weekend_full_day_price") WHERE "weekend_full_day_price" IS NOT NULL;

-- Advanced composite indexes for active properties with pricing filters
CREATE INDEX "properties_active_weekday_pricing_idx" ON "properties" USING btree ("status", "weekday_half_day_price", "weekday_full_day_price", "featured", "created_at") WHERE "status" = 'active';
CREATE INDEX "properties_active_weekend_pricing_idx" ON "properties" USING btree ("status", "weekend_half_day_price", "weekend_full_day_price", "featured", "created_at") WHERE "status" = 'active';

-- Price range filtering index (for min/max price searches by day type)
CREATE INDEX "properties_weekday_price_range_idx" ON "properties" USING btree ("weekday_half_day_price", "weekday_full_day_price", "status", "location") WHERE "weekday_half_day_price" IS NOT NULL;
CREATE INDEX "properties_weekend_price_range_idx" ON "properties" USING btree ("weekend_half_day_price", "weekend_full_day_price", "status", "location") WHERE "weekend_half_day_price" IS NOT NULL;

-- Full search optimization index (supports complex pricing queries with location and features)
CREATE INDEX "properties_advanced_weekday_search_idx" ON "properties" USING btree ("status", "location", "featured", "weekday_half_day_price", "weekday_full_day_price", "created_at") WHERE "weekday_half_day_price" IS NOT NULL;
CREATE INDEX "properties_advanced_weekend_search_idx" ON "properties" USING btree ("status", "location", "featured", "weekend_half_day_price", "weekend_full_day_price", "created_at") WHERE "weekend_half_day_price" IS NOT NULL;