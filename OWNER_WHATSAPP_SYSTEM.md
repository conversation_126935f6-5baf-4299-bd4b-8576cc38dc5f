# Owner WhatsApp Booking System

## Overview
The Owner WhatsApp Booking System allows customers to book farmhouses directly through property owners' WhatsApp numbers. Each owner gets their own WhatsApp booking flow that shows only their properties.

## ✅ Features Implemented

### 1. Owner-Specific WhatsApp Numbers
- Added `whatsapp_number` field to users table
- Each property owner can have a dedicated WhatsApp number
- Database migration and indexing for fast lookups

### 2. Owner-Specific Routing
- Messages sent to owner WhatsApp numbers trigger owner-specific flows
- System identifies owner by WhatsApp number
- Only owner's properties are shown to customers

### 3. Complete Booking Flow
- **Property Inquiry**: Customer contacts owner
- **Property Selection**: View owner's properties only
- **Date Selection**: Choose dates and stay type
- **Guest Details**: Provide contact and guest information
- **Confirmation**: Review and confirm booking

### 4. Calendar Integration
- Bookings automatically added to owner's calendar
- Calendar service integration for availability checking
- Booking references for easy tracking

### 5. Dual Notifications
- Customer receives booking confirmation
- Owner receives notification with customer details
- Both parties get booking reference numbers

## 🚀 How to Use

### Setup Owner WhatsApp Numbers
```bash
npx tsx scripts/setup-owner-whatsapp.ts
```

### Test the System
```bash
./scripts/test-owner-whatsapp-flow.sh
```

### Run Complete Demo
```bash
./scripts/demo-owner-whatsapp-complete.sh
```

## 📱 Current Owner Mappings

| Owner | WhatsApp | Properties |
|-------|----------|------------|
| Fazeel Usmani | +91********** | 5 properties (Ravi Heritage, Traditional Courtyard, etc.) |
| Faz 16 | +************ | 6 properties (Lakshmi Heritage Villa, Mango Grove, etc.) |
| Ravi Kumar Reddy | +************ | 4 properties (Venkatesh Eco Farm, Eco Farm Stay, etc.) |
| Ayesha Usmani | +************ | 1 property (MM farm) |
| Syed Adnan Hussain | +************ | 1 property (Farmstay 1001) |

## 🔧 Technical Implementation

### Files Created/Modified
- `server/services/OwnerWhatsAppService.ts` - Main service logic
- `shared/schema.ts` - Added WhatsApp field
- `server/routes/whatsapp.ts` - Owner routing logic
- `migrations/0011_add_whatsapp_to_users.sql` - Database migration
- `scripts/setup-owner-whatsapp.ts` - Setup script
- `scripts/test-owner-whatsapp-flow.sh` - Test script

### Key Functions
- `findOwnerByWhatsApp()` - Identify owner from WhatsApp number
- `getOwnerProperties()` - Get owner's active properties
- `processCustomerMessage()` - Handle customer interactions
- `createBooking()` - Create calendar booking and send confirmations

## 📋 Message Flow Example

1. **Customer**: "Hi, I want to book your farmhouse"
   - **System**: Shows owner's property list

2. **Customer**: "1" (selects first property)
   - **System**: Asks for dates

3. **Customer**: "Tomorrow, full day"
   - **System**: Asks for guest details

4. **Customer**: "Name: John Doe\nPhone: **********\nGuests: 4"
   - **System**: Shows booking summary

5. **Customer**: "CONFIRM"
   - **System**: Creates booking, adds to calendar, sends confirmations

## 🎯 Benefits

- **Owner-Centric**: Each owner has their own booking channel
- **Simplified**: Customers see only relevant properties
- **Automated**: Bookings automatically added to calendars
- **Trackable**: Both parties receive confirmation details
- **Scalable**: Easy to add new owners and properties

## 🔮 Future Enhancements

- Payment integration for owner bookings
- Real-time availability checking
- Owner dashboard for managing WhatsApp bookings
- Custom pricing per owner
- Multi-language support
- WhatsApp Business API integration

## 📞 Support

The system is ready for production use with:
- Real Twilio WhatsApp API integration
- Owner dashboard integration
- Payment processing
- Calendar synchronization

Each owner can now receive direct bookings through their WhatsApp number!