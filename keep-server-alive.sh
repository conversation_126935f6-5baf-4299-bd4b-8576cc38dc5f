#!/bin/bash

# Keep Server Alive Script
# This script ensures the farmhouse rental app never stops running

echo "🔄 Ensuring server stays alive..."

while true; do
    # Check if server is running
    if ! pgrep -f "tsx server/index.ts" > /dev/null; then
        echo "⚠️  Server not running, starting it..."
        
        # Clean up any stale processes
        pkill -f "tsx server/index.ts" 2>/dev/null || true
        fuser -k 5000/tcp 2>/dev/null || true
        
        # Start server in background
        NODE_ENV=development tsx server/index.ts &
        
        echo "✅ Server restarted"
    fi
    
    # Wait 10 seconds before next check
    sleep 10
done