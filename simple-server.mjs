import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { createServer } from 'http';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = 5000;

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Serve static files from dist/public
const staticPath = path.join(__dirname, 'dist', 'public');
console.log('📂 Serving static files from:', staticPath);

// Serve static assets
app.use('/assets', express.static(path.join(staticPath, 'assets')));
app.use(express.static(staticPath));

// Basic API endpoint for testing
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Server is working!', 
    timestamp: new Date().toISOString(),
    youtube_features: 'Ready to test!'
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', port, timestamp: new Date().toISOString() });
});

// Catch-all handler: send back index.html for SPA routing
app.get('*', (req, res) => {
  const indexPath = path.join(staticPath, 'index.html');
  console.log(`📄 Serving index.html for route: ${req.path}`);
  res.sendFile(indexPath, (err) => {
    if (err) {
      console.error('❌ Error serving index.html:', err);
      res.status(500).send('Server Error');
    }
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('❌ Server Error:', err);
  res.status(500).json({ error: 'Internal Server Error' });
});

// Create HTTP server
const server = createServer(app);

// Start server with enhanced error handling
server.listen(port, '0.0.0.0', () => {
  console.log('🚀 ===================================');
  console.log('🚀 SERVER STARTED SUCCESSFULLY!');
  console.log('🚀 ===================================');
  console.log(`📍 Local: http://localhost:${port}`);
  console.log(`📍 Network: http://0.0.0.0:${port}`);
  console.log(`📱 Test API: http://localhost:${port}/api/test`);
  console.log(`❤️ Health: http://localhost:${port}/health`);
  console.log('🚀 ===================================');
  console.log('📺 YouTube Video Features: READY!');
  console.log('🎬 Media Management: AVAILABLE!');
  console.log('🚀 ===================================');
  
  // Test server immediately
  setTimeout(() => {
    import('http').then(http => {
      const testReq = http.request(`http://localhost:${port}/health`, (res) => {
        console.log('✅ Self-test passed - Server is responding!');
      });
      testReq.on('error', (err) => {
        console.log('⚠️ Self-test failed:', err.message);
      });
      testReq.end();
    });
  }, 1000);
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${port} is already in use. Trying to kill existing process...`);
    process.exit(1);
  } else {
    console.error('❌ Server error:', err);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server shut down successfully');
    process.exit(0);
  });
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});