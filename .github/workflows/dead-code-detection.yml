name: Dead Code Detection

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]
  schedule:
    # Run weekly on Sundays at 2 AM UTC
    - cron: '0 2 * * 0'

jobs:
  detect-dead-code:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ts-prune for unused exports
      id: ts-prune
      run: |
        echo "## Unused Exports Report" >> $GITHUB_STEP_SUMMARY
        echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
        npx ts-prune --project tsconfig.json | head -20 >> $GITHUB_STEP_SUMMARY || true
        echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
        
        # Count unused exports
        UNUSED_COUNT=$(npx ts-prune --project tsconfig.json | wc -l)
        echo "unused_exports=$UNUSED_COUNT" >> $GITHUB_OUTPUT
        
    - name: Run ESLint dead code detection
      id: eslint-deadcode
      run: |
        echo "## ESLint Dead Code Report" >> $GITHUB_STEP_SUMMARY
        
        # Run ESLint and capture results (allow failure for reporting)
        set +e
        npx eslint --config .eslintrc.deadcode.cjs --ext .ts,.tsx --format json server/ client/ shared/ > eslint-deadcode.json
        ESLINT_EXIT_CODE=$?
        set -e
        
        # Process results
        if [ -f eslint-deadcode.json ]; then
          # Count total issues
          TOTAL_ISSUES=$(cat eslint-deadcode.json | jq '[.[] | .errorCount + .warningCount] | add // 0')
          echo "total_issues=$TOTAL_ISSUES" >> $GITHUB_OUTPUT
          
          # Display summary
          echo "**Total ESLint Issues Found:** $TOTAL_ISSUES" >> $GITHUB_STEP_SUMMARY
          
          # Show sample issues
          echo "<details><summary>Sample Issues</summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          cat eslint-deadcode.json | jq -r '.[] | select(.messages | length > 0) | .filePath as $file | .messages[] | "\($file):\(.line) - \(.message) (\(.ruleId))"' | head -10 >> $GITHUB_STEP_SUMMARY || true
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY
        else
          echo "total_issues=0" >> $GITHUB_OUTPUT
        fi
        
    - name: Run comprehensive dead code analysis
      id: comprehensive
      run: |
        echo "## Comprehensive Dead Code Analysis" >> $GITHUB_STEP_SUMMARY
        
        # Run our custom dead code detection script
        npm run deadcode > dead-code-results.txt 2>&1 || true
        
        # Display results
        if [ -f dead-code-results.txt ]; then
          echo "<details><summary>Full Analysis Results</summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          cat dead-code-results.txt >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Upload dead code artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: dead-code-analysis-${{ github.sha }}
        path: |
          eslint-deadcode.json
          dead-code-results.txt
          dead-code-report.json
        retention-days: 30
        
    - name: Comment on Pull Request
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const unusedExports = '${{ steps.ts-prune.outputs.unused_exports }}';
          const eslintIssues = '${{ steps.eslint-deadcode.outputs.total_issues }}';
          
          const body = `## 🧹 Dead Code Detection Report
          
          **Unused Exports:** ${unusedExports}
          **ESLint Issues:** ${eslintIssues}
          
          ### 📊 Summary
          ${unusedExports > 0 ? '⚠️' : '✅'} **Unused Exports**: ${unusedExports} found
          ${eslintIssues > 0 ? '⚠️' : '✅'} **ESLint Issues**: ${eslintIssues} found
          
          ### 💡 Recommendations
          ${unusedExports > 20 ? '- Consider running a cleanup sprint to remove unused exports' : ''}
          ${eslintIssues > 0 ? '- Run `npm run deadcode:fix` to automatically fix unused imports' : ''}
          - Review the full analysis results in the workflow artifacts
          
          ### 🔧 Quick Fixes
          \`\`\`bash
          # Fix unused imports automatically
          npm run deadcode:fix
          
          # Generate detailed report
          npm run deadcode:report
          
          # View unused exports
          npx ts-prune
          \`\`\`
          
          <sub>🤖 This comment was generated by the Dead Code Detection workflow</sub>`;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: body
          });
          
    - name: Fail if critical dead code threshold exceeded
      if: |
        steps.ts-prune.outputs.unused_exports > 50 || 
        steps.eslint-deadcode.outputs.total_issues > 100
      run: |
        echo "❌ Critical dead code threshold exceeded!"
        echo "Unused exports: ${{ steps.ts-prune.outputs.unused_exports }} (threshold: 50)"
        echo "ESLint issues: ${{ steps.eslint-deadcode.outputs.total_issues }} (threshold: 100)"
        echo "Please clean up dead code before merging."
        exit 1
        
    - name: Success summary
      if: success()
      run: |
        echo "✅ Dead code detection completed successfully!"
        echo "Unused exports: ${{ steps.ts-prune.outputs.unused_exports }}"
        echo "ESLint issues: ${{ steps.eslint-deadcode.outputs.total_issues }}"