name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  DATABASE_URL: 'postgresql://farmhouse_user:farmhouse_password@localhost:5433/farmhouse_rental'
  JWT_SECRET: 'test-jwt-secret-key-12345'
  COOKIE_SECRET: 'test-cookie-secret-key-12345'

jobs:
  # Mandatory Tests - Must pass for merge
  unit-tests:
    name: 🧪 Unit Tests (Mandatory)
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: TypeScript type check
        run: npm run check
        
      - name: Run unit tests
        run: npm run test
        
      - name: Generate coverage report
        run: npm run test -- --coverage
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unit-tests
          name: unit-test-coverage
          fail_ci_if_error: false  # Don't fail CI if codecov upload fails

  # Mandatory Tests - Must pass for merge  
  integration-tests:
    name: 🔗 Integration Tests (Mandatory)
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: farmhouse_user
          POSTGRES_PASSWORD: farmhouse_password
          POSTGRES_DB: farmhouse_rental
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client
          
      - name: Wait for PostgreSQL
        run: |
          until pg_isready -h localhost -p 5433 -U farmhouse_user; do
            echo "Waiting for postgres..."
            sleep 2
          done
          
      - name: Setup test database
        run: |
          PGPASSWORD=farmhouse_password psql -h localhost -p 5433 -U farmhouse_user -d farmhouse_rental -c "SELECT 1;"
          
      - name: Run database migrations
        run: npm run db:push
        
      - name: Run integration tests
        run: npm run test:integration

  # Optional Tests - Won't block merge but will show status
  e2e-tests:
    name: 🌐 E2E Tests (Optional)
    runs-on: ubuntu-latest
    continue-on-error: true  # This makes E2E tests non-blocking
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: farmhouse_user
          POSTGRES_PASSWORD: farmhouse_password
          POSTGRES_DB: farmhouse_rental
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client
          
      - name: Wait for PostgreSQL
        run: |
          until pg_isready -h localhost -p 5433 -U farmhouse_user; do
            echo "Waiting for postgres..."
            sleep 2
          done
          
      - name: Setup test database
        run: |
          PGPASSWORD=farmhouse_password psql -h localhost -p 5433 -U farmhouse_user -d farmhouse_rental -c "SELECT 1;"
          
      - name: Run database migrations
        run: npm run db:push
        
      - name: Run E2E tests
        run: npm run test:e2e
        
      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: failure()  # Only upload if tests fail
        with:
          name: e2e-test-results-${{ github.run_number }}
          path: |
            test-results/
            playwright-report/
          retention-days: 1

  # Build validation
  build-check:
    name: 🏗️ Build Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build frontend
        run: npm run build:frontend
        
      - name: Build project
        run: npm run build
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        if: success() && github.ref == 'refs/heads/main'  # Only upload on main branch success
        with:
          name: build-artifacts-${{ github.run_number }}
          path: dist/
          retention-days: 1

  # Test Summary Job
  test-summary:
    name: 📊 Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, build-check]
    if: always()
    
    steps:
      - name: Test Summary
        run: |
          echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Test Type | Status | Required |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|----------|" >> $GITHUB_STEP_SUMMARY
          echo "| Unit Tests | ${{ needs.unit-tests.result == 'success' && '✅ Passed' || '❌ Failed' }} | ✅ Mandatory |" >> $GITHUB_STEP_SUMMARY
          echo "| Integration Tests | ${{ needs.integration-tests.result == 'success' && '✅ Passed' || '❌ Failed' }} | ✅ Mandatory |" >> $GITHUB_STEP_SUMMARY
          echo "| E2E Tests | ${{ needs.e2e-tests.result == 'success' && '✅ Passed' || needs.e2e-tests.result == 'failure' && '⚠️ Failed (Optional)' || '⏸️ Skipped' }} | ⚪ Optional |" >> $GITHUB_STEP_SUMMARY
          echo "| Build Check | ${{ needs.build-check.result == 'success' && '✅ Passed' || '❌ Failed' }} | ✅ Mandatory |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🎯 Testing Pyramid Status:" >> $GITHUB_STEP_SUMMARY
          echo "- **Unit Tests (70%)**: ${{ needs.unit-tests.result == 'success' && 'Ready for Production' || 'Needs Attention' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Integration Tests (20%)**: ${{ needs.integration-tests.result == 'success' && 'Ready for Production' || 'Needs Attention' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **E2E Tests (10%)**: ${{ needs.e2e-tests.result == 'success' && 'Fully Operational' || 'Infrastructure Ready' }}" >> $GITHUB_STEP_SUMMARY