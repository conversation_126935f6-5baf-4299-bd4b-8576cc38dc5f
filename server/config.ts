// Note: Environment loading is handled by env-loader.ts before this file is loaded
import { log } from './utils/logger';
import { validateEnvironment } from './utils/security';

interface EnvConfig {
  // Database
  DATABASE_URL: string;

  // JWT & Security
  JWT_SECRET: string;
  COOKIE_SECRET: string;
  SESSION_SECRET: string;

  // Twilio
  TWILIO_ACCOUNT_SID: string | undefined;
  TWILIO_AUTH_TOKEN: string | undefined;
  TWILIO_VERIFY_SERVICE_SID: string | undefined;
  TWILIO_MESSAGING_SID: string | undefined;
  
  // WhatsApp
  TWILIO_WHATSAPP_NUMBER: string | undefined;
  WHATSAPP_WEBHOOK_URL: string | undefined;
  WHATSAPP_WEBHOOK_TOKEN: string | undefined;
  
  // DLT Compliance
  DLT_ENTITY_ID: string | undefined;
  
  // Email Services
  SENDGRID_API_KEY: string | undefined;
  
  // Cloudinary
  CLOUDINARY_CLOUD_NAME: string | undefined;
  CLOUDINARY_API_KEY: string | undefined;
  CLOUDINARY_API_SECRET: string | undefined;
  
  // App
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;

  // Security
  USE_SECURE_COOKIES: boolean;
}

interface ServiceStatus {
  database: boolean;
  twilio: boolean;
  whatsapp: boolean;
  dlt: boolean;
  sendgrid: boolean;
  cloudinary: boolean;
}

class ConfigService {
  private config: EnvConfig;
  private serviceStatus: ServiceStatus;

  constructor() {
    // Validate environment before loading config
    validateEnvironment();
    this.config = this.loadConfig();
    this.serviceStatus = this.validateServices();
    this.logServiceStatus();
  }

  private loadConfig(): EnvConfig {
    // Use NODE_ENV as the standard environment variable
    const nodeEnv = process.env.NODE_ENV || 'development';
    const isDev = nodeEnv === 'development';
    const isProd = nodeEnv === 'production';
    
    log(`🔧 Loading configuration for environment: ${nodeEnv}`, 'config');
    
    // Define required environment variables based on environment
    const baseRequired = ['DATABASE_URL'];
    const productionRequired = ['JWT_SECRET', 'COOKIE_SECRET', 'SESSION_SECRET'];
    
    const required = isProd 
      ? [...baseRequired, ...productionRequired]
      : baseRequired;
      
    const missing = required.filter(key => !process.env[key]);

    if (missing.length > 0) {
      throw new Error(`Missing required environment variables for ${nodeEnv}: ${missing.join(', ')}`);
    }

    // Validate secret strength in production
    if (isProd) {
      this.validateSecretStrength('JWT_SECRET', process.env.JWT_SECRET!);
      this.validateSecretStrength('COOKIE_SECRET', process.env.COOKIE_SECRET!);
      this.validateSecretStrength('SESSION_SECRET', process.env.SESSION_SECRET!);
    }

    return {
      DATABASE_URL: process.env.DATABASE_URL!,
      JWT_SECRET: isDev 
        ? (process.env.JWT_SECRET || 'dev-jwt-secret-change-in-production')
        : process.env.JWT_SECRET!,
      COOKIE_SECRET: isDev 
        ? (process.env.COOKIE_SECRET || 'dev-cookie-secret-change-in-production')
        : process.env.COOKIE_SECRET!,
      SESSION_SECRET: isDev 
        ? (process.env.SESSION_SECRET || process.env.COOKIE_SECRET || 'dev-session-secret-change-in-production')
        : process.env.SESSION_SECRET!,
      TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
      TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
      TWILIO_VERIFY_SERVICE_SID: process.env.TWILIO_VERIFY_SERVICE_SID,
      TWILIO_MESSAGING_SID: process.env.TWILIO_MESSAGING_SID,
      TWILIO_WHATSAPP_NUMBER: process.env.TWILIO_WHATSAPP_NUMBER,
      WHATSAPP_WEBHOOK_URL: process.env.WHATSAPP_WEBHOOK_URL,
      WHATSAPP_WEBHOOK_TOKEN: process.env.WHATSAPP_WEBHOOK_TOKEN,
      DLT_ENTITY_ID: process.env.DLT_ENTITY_ID,
      SENDGRID_API_KEY: process.env.SENDGRID_API_KEY,
      CLOUDINARY_CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME,
      CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,
      CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,
      NODE_ENV: (nodeEnv as 'development' | 'production' | 'test'),
      PORT: parseInt(process.env.PORT || '5000', 10),
      USE_SECURE_COOKIES: isProd
    };
  }

  private validateSecretStrength(name: string, value: string): void {
    if (!value || value.length < 32) {
      throw new Error(`${name} must be at least 32 characters long in production`);
    }
    
    if (value.includes('dev-') || value.includes('change-in-production')) {
      throw new Error(`${name} cannot use development placeholder values in production`);
    }
    
    // Check for common weak patterns
    const weakPatterns = ['123456', 'password', 'secret', 'admin', 'test'];
    const lowerValue = value.toLowerCase();
    
    for (const pattern of weakPatterns) {
      if (lowerValue.includes(pattern)) {
        throw new Error(`${name} contains weak patterns and is not suitable for production`);
      }
    }
    
    log(`✅ ${name} validated - meets production security requirements`, 'config');
  }

  private validateServices(): ServiceStatus {
    return {
      database: !!this.config.DATABASE_URL,
      twilio: !!(this.config.TWILIO_ACCOUNT_SID && 
                 this.config.TWILIO_AUTH_TOKEN && 
                 this.config.TWILIO_ACCOUNT_SID.startsWith('AC')),
      whatsapp: !!(this.config.TWILIO_WHATSAPP_NUMBER && 
                   this.config.WHATSAPP_WEBHOOK_TOKEN && 
                   this.config.TWILIO_WHATSAPP_NUMBER.startsWith('whatsapp:')),
      dlt: !!(this.config.DLT_ENTITY_ID && 
              this.config.DLT_ENTITY_ID.length > 0),
      sendgrid: !!(this.config.SENDGRID_API_KEY && 
                   this.config.SENDGRID_API_KEY.startsWith('SG.')),
      cloudinary: !!(this.config.CLOUDINARY_CLOUD_NAME && 
                     this.config.CLOUDINARY_API_KEY && 
                     this.config.CLOUDINARY_API_SECRET)
    };
  }

  private logServiceStatus(): void {
    log('🔧 Service Configuration Status:', 'config');
    log(`  📊 Database: ${this.serviceStatus.database ? '✅ Connected' : '❌ Missing'}`, 'config');
    log(`  📱 Twilio SMS: ${this.serviceStatus.twilio ? '✅ Configured' : '⚠️ Disabled (development fallback)'}`, 'config');
    log(`  💬 WhatsApp: ${this.serviceStatus.whatsapp ? '✅ Configured' : '⚠️ Disabled (development fallback)'}`, 'config');
    log(`  🇮🇳 DLT Compliance: ${this.serviceStatus.dlt ? '✅ Configured' : '⚠️ Disabled (SMS will not be DLT compliant)'}`, 'config');
    log(`  ✉️ SendGrid Email: ${this.serviceStatus.sendgrid ? '✅ Configured' : '⚠️ Disabled (development fallback)'}`, 'config');
    log(`  🖼️ Cloudinary Images: ${this.serviceStatus.cloudinary ? '✅ Configured' : '⚠️ Disabled (image uploads unavailable)'}`, 'config');

    if (this.config.NODE_ENV === 'development') {
      log('  🔨 Development Mode: OTP codes will be logged to console when external services are unavailable', 'config');
    }
  }

  // Getters for configuration
  get database() { return { url: this.config.DATABASE_URL, available: this.serviceStatus.database }; }
  get jwt() { return { secret: this.config.JWT_SECRET }; }
  get cookie() { return { secret: this.config.COOKIE_SECRET }; }
  get session() { return { secret: this.config.SESSION_SECRET }; }
  get app() { return { nodeEnv: this.config.NODE_ENV, port: this.config.PORT, useSecureCookies: this.config.USE_SECURE_COOKIES }; }

  get twilio() {
    return {
      accountSid: this.config.TWILIO_ACCOUNT_SID,
      authToken: this.config.TWILIO_AUTH_TOKEN,
      verifyServiceSid: this.config.TWILIO_VERIFY_SERVICE_SID,
      messagingServiceSid: this.config.TWILIO_MESSAGING_SID,
      available: this.serviceStatus.twilio
    };
  }

  get whatsapp() {
    return {
      twilioNumber: this.config.TWILIO_WHATSAPP_NUMBER,
      webhookUrl: this.config.WHATSAPP_WEBHOOK_URL,
      webhookToken: this.config.WHATSAPP_WEBHOOK_TOKEN,
      available: this.serviceStatus.whatsapp
    };
  }

  get dlt() {
    return {
      entityId: this.config.DLT_ENTITY_ID,
      available: this.serviceStatus.dlt
    };
  }

  get sendgrid() {
    return {
      apiKey: this.config.SENDGRID_API_KEY,
      available: this.serviceStatus.sendgrid
    };
  }


  get cloudinary() {
    return {
      cloudName: this.config.CLOUDINARY_CLOUD_NAME,
      apiKey: this.config.CLOUDINARY_API_KEY,
      apiSecret: this.config.CLOUDINARY_API_SECRET,
      available: this.serviceStatus.cloudinary
    };
  }

  // Service availability checks
  isServiceAvailable(service: keyof ServiceStatus): boolean {
    return this.serviceStatus[service];
  }

  isDevelopment(): boolean {
    return this.config.NODE_ENV === 'development';
  }

  isProduction(): boolean {
    return this.config.NODE_ENV === 'production';
  }

  // Get status for health checks
  getHealthStatus() {
    return {
      environment: this.config.NODE_ENV,
      services: this.serviceStatus,
      timestamp: new Date().toISOString()
    };
  }
}

// Lazy initialization of config service
let _config: ConfigService | null = null;

export const config = new Proxy({} as ConfigService, {
  get(target, prop) {
    if (!_config) {
      _config = new ConfigService();
    }
    return (_config as any)[prop];
  }
});