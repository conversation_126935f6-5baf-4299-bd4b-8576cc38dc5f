import crypto from 'crypto';
import { db } from "../db";
import { 
  webhookEvents, 
  paymentOrders, 
  paymentTransactions, 
  bookings,
  WebhookEvent,
  InsertWebhookEvent,
  PaymentOrder,
  PaymentTransaction
} from "../../shared/schema";
import { eq, and } from "drizzle-orm";
import { auditLogger } from "./AuditLogger";
import { enhancedPaymentService } from "./EnhancedPaymentService";
import { cacheService } from "./CacheService";

interface WebhookPayload {
  entity: string;
  event: string;
  account_id: string;
  created_at: number;
  payload: {
    payment?: {
      entity: any;
    };
    order?: {
      entity: any;
    };
    refund?: {
      entity: any;
    };
  };
}

interface WebhookSecurityContext {
  eventId: string;
  signature: string;
  timestamp: number;
  sourceIp: string;
  userAgent?: string;
  contentType?: string;
  payload: WebhookPayload;
}

interface WebhookProcessingResult {
  success: boolean;
  eventId: string;
  message: string;
  processed: boolean;
  error?: string;
  shouldRetry?: boolean;
}

// Allowed IP ranges for Razorpay webhooks
const RAZORPAY_IP_RANGES = [
  '***********/20',
  '***********/18',
  '***********/18',
  '*********/17',
  '**********/16',
  '*********/16',
  '**********/16',
  '**********/16',
  '*******/16'
];

export class EnhancedWebhookService {
  private static instance: EnhancedWebhookService;
  private readonly replayWindowMs = 5 * 60 * 1000; // 5 minutes
  private readonly maxRetries = 3;
  private readonly retryDelayMs = 1000;

  private constructor() {
    // Using existing memory cache service instead of Redis
  }

  public static getInstance(): EnhancedWebhookService {
    if (!EnhancedWebhookService.instance) {
      EnhancedWebhookService.instance = new EnhancedWebhookService();
    }
    return EnhancedWebhookService.instance;
  }

  // Main webhook processing method
  async processWebhook(
    rawPayload: string,
    signature: string,
    eventId: string,
    timestamp: number,
    sourceIp: string,
    userAgent?: string,
    contentType?: string
  ): Promise<WebhookProcessingResult> {
    try {
      // Parse payload
      const payload: WebhookPayload = JSON.parse(rawPayload);

      const context: WebhookSecurityContext = {
        eventId,
        signature,
        timestamp,
        sourceIp,
        userAgent: userAgent || 'unknown',
        contentType: contentType || 'application/json',
        payload
      };

      // Security validations
      const securityResult = await this.performSecurityValidations(context);
      if (!securityResult.valid) {
        return {
          success: false,
          eventId,
          message: securityResult.reason || 'Security validation failed',
          processed: false,
          error: securityResult.reason || 'Security validation failed'
        };
      }

      // Check for replay attacks
      const replayResult = await this.checkReplayAttack(eventId, timestamp);
      if (replayResult.isReplay) {
        await auditLogger.logWebhookSecurityViolation(
          `Webhook replay attack detected: ${eventId}`,
          sourceIp,
          { eventId, timestamp, originalTimestamp: replayResult.originalTimestamp }
        );

        return {
          success: false,
          eventId,
          message: 'Replay attack detected',
          processed: false,
          error: 'duplicate_event'
        };
      }

      // Store webhook event
      await this.storeWebhookEvent(context, rawPayload);

      // Process the webhook based on event type
      const processingResult = await this.processWebhookEvent(payload, context);

      // Update webhook status
      await this.updateWebhookStatus(eventId, processingResult.success ? 'processed' : 'failed');

      // Log successful processing
      if (processingResult.success) {
        await auditLogger.logPaymentAction('webhook_processed', {
          actorType: 'webhook',
          metadata: {
            eventId,
            eventType: payload.event,
            entityType: payload.entity,
            sourceIp
          }
        });
      }

      return processingResult;
    } catch (error) {
      console.error('Webhook processing error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      await auditLogger.logWebhookSecurityViolation(
        `Webhook processing failed: ${errorMessage}`,
        sourceIp,
        { eventId, error: errorMessage }
      );

      return {
        success: false,
        eventId,
        message: 'Webhook processing failed',
        processed: false,
        error: errorMessage,
        shouldRetry: this.isRetryableError(error)
      };
    }
  }

  // Perform comprehensive security validations
  private async performSecurityValidations(context: WebhookSecurityContext): Promise<{valid: boolean, reason?: string}> {
    // 1. Timestamp validation - prevent old webhooks
    const currentTime = Date.now();
    const webhookTime = context.timestamp * 1000; // Convert to milliseconds
    
    if (Math.abs(currentTime - webhookTime) > this.replayWindowMs) {
      return { valid: false, reason: 'Webhook timestamp is too old or too far in the future' };
    }

    // 2. IP address validation
    if (!this.isValidSourceIp(context.sourceIp)) {
      await auditLogger.logWebhookSecurityViolation(
        `Webhook from unauthorized IP: ${context.sourceIp}`,
        context.sourceIp,
        { eventId: context.eventId, timestamp: context.timestamp }
      );
      return { valid: false, reason: 'Unauthorized source IP address' };
    }

    // 3. Signature verification
    if (!await this.verifyWebhookSignature(context)) {
      await auditLogger.logWebhookSecurityViolation(
        `Invalid webhook signature: ${context.eventId}`,
        context.sourceIp,
        { eventId: context.eventId, signature: context.signature }
      );
      return { valid: false, reason: 'Invalid webhook signature' };
    }

    // 4. Content type validation
    if (context.contentType && !context.contentType.includes('application/json')) {
      return { valid: false, reason: 'Invalid content type' };
    }

    // 5. Event structure validation
    if (!this.validateEventStructure(context.payload)) {
      return { valid: false, reason: 'Invalid event structure' };
    }

    return { valid: true };
  }

  // Check for replay attacks using existing cache service
  private async checkReplayAttack(eventId: string, timestamp: number): Promise<{isReplay: boolean, originalTimestamp?: number}> {
    try {
      const key = `webhook:${eventId}`;
      const existingTimestamp = await cacheService.get<string>(key);
      
      if (existingTimestamp) {
        return { 
          isReplay: true, 
          originalTimestamp: parseInt(existingTimestamp) 
        };
      }

      // Store this event with expiration (convert ms to seconds for TTL)
      await cacheService.set(key, timestamp.toString(), Math.ceil(this.replayWindowMs / 1000));
      
      return { isReplay: false };
    } catch (error) {
      console.error('Replay check failed:', error);
      // On cache failure, allow the webhook but log the incident
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      await auditLogger.logWebhookSecurityViolation(
        `Replay check failed for webhook: ${eventId}`,
        'unknown',
        { eventId, error: errorMessage }
      );
      return { isReplay: false };
    }
  }

  // Verify webhook signature using timing-safe comparison
  private async verifyWebhookSignature(context: WebhookSecurityContext): Promise<boolean> {
    try {
      const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET!;
      const payload = JSON.stringify(context.payload);
      
      // Create expected signature
      const expectedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(payload)
        .digest('hex');

      // Timing-safe comparison
      const providedSignature = context.signature.replace('sha256=', '');
      
      return crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(providedSignature, 'hex')
      );
    } catch (error) {
      console.error('Signature verification failed:', error);
      return false;
    }
  }

  // Check if IP is in allowed ranges
  private isValidSourceIp(ip: string): boolean {
    // For development, allow localhost
    if (process.env.NODE_ENV === 'development' && 
        (ip === '127.0.0.1' || ip === '::1' || ip === 'localhost')) {
      return true;
    }

    // In production, validate against Razorpay IP ranges
    // This is a simplified check - in production, use proper CIDR validation
    if (process.env.NODE_ENV === 'production') {
      return RAZORPAY_IP_RANGES.some(range => {
        // Simplified check - in production, use a proper IP range library
        return ip.startsWith(range.split('/')[0].split('.').slice(0, 2).join('.'));
      });
    }

    return true; // Allow all IPs in non-production environments
  }

  // Validate event structure
  private validateEventStructure(payload: WebhookPayload): boolean {
    return !!(
      payload.entity &&
      payload.event &&
      payload.account_id &&
      payload.created_at &&
      payload.payload
    );
  }

  // Store webhook event in database
  private async storeWebhookEvent(context: WebhookSecurityContext, rawPayload: string): Promise<void> {
    try {
      const hashedSignature = crypto
        .createHash('sha256')
        .update(context.signature)
        .digest('hex');

      const webhookData: InsertWebhookEvent = {
        eventId: context.eventId,
        eventType: context.payload.event,
        payload: context.payload,
        signatureHash: hashedSignature,
        status: 'received',
        retryCount: 0,
      };

      await db
        .insert(webhookEvents)
        .values(webhookData);
    } catch (error) {
      console.error('Failed to store webhook event:', error);
      throw error;
    }
  }

  // Process webhook event based on type
  private async processWebhookEvent(
    payload: WebhookPayload,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    try {
      const { entity, event } = payload;

      switch (entity) {
        case 'payment':
          return await this.processPaymentWebhook(payload, context);
        case 'order':
          return await this.processOrderWebhook(payload, context);
        case 'refund':
          return await this.processRefundWebhook(payload, context);
        default:
          console.warn(`Unknown webhook entity: ${entity}`);
          return {
            success: true,
            eventId: context.eventId,
            message: `Ignored unknown entity: ${entity}`,
            processed: false
          };
      }
    } catch (error) {
      console.error('Webhook event processing failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        eventId: context.eventId,
        message: 'Event processing failed',
        processed: false,
        error: errorMessage,
        shouldRetry: this.isRetryableError(error)
      };
    }
  }

  // Process payment webhooks
  private async processPaymentWebhook(
    payload: WebhookPayload,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    const paymentEntity = payload.payload.payment?.entity;
    if (!paymentEntity) {
      return {
        success: false,
        eventId: context.eventId,
        message: 'Missing payment entity',
        processed: false
      };
    }

    const paymentId = paymentEntity.id;
    const orderId = paymentEntity.order_id;
    const status = paymentEntity.status;

    // Find the payment order
    const [paymentOrder] = await db
      .select()
      .from(paymentOrders)
      .where(eq(paymentOrders.razorpayOrderId, orderId));

    if (!paymentOrder) {
      return {
        success: false,
        eventId: context.eventId,
        message: 'Payment order not found',
        processed: false
      };
    }

    // Process based on payment status
    switch (payload.event) {
      case 'payment.captured':
        return await this.handlePaymentCaptured(paymentOrder, paymentEntity, context);
      case 'payment.failed':
        return await this.handlePaymentFailed(paymentOrder, paymentEntity, context);
      case 'payment.authorized':
        return await this.handlePaymentAuthorized(paymentOrder, paymentEntity, context);
      default:
        return {
          success: true,
          eventId: context.eventId,
          message: `Processed payment event: ${payload.event}`,
          processed: true
        };
    }
  }

  // Handle payment captured event
  private async handlePaymentCaptured(
    paymentOrder: PaymentOrder,
    paymentEntity: any,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    try {
      // Update payment transaction status
      await db
        .update(paymentTransactions)
        .set({
          status: 'captured',
          gatewayResponse: paymentEntity,
          updatedAt: new Date()
        })
        .where(eq(paymentTransactions.razorpayPaymentId, paymentEntity.id));

      // Update payment order status
      await db
        .update(paymentOrders)
        .set({
          status: 'paid',
          updatedAt: new Date()
        })
        .where(eq(paymentOrders.id, paymentOrder.id));

      // Update booking status
      if (paymentOrder.bookingId) {
        await db
          .update(bookings)
          .set({
            paymentStatus: 'completed',
            status: 'confirmed'
          })
          .where(eq(bookings.id, paymentOrder.bookingId));
      }

      await auditLogger.logPaymentOrderAction(
        'payment_captured_via_webhook',
        paymentOrder.id,
        { actorType: 'webhook' },
        { status: 'pending' },
        { status: 'captured', paymentId: paymentEntity.id }
      );

      return {
        success: true,
        eventId: context.eventId,
        message: 'Payment captured successfully',
        processed: true
      };
    } catch (error) {
      console.error('Failed to handle payment captured:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        eventId: context.eventId,
        message: 'Failed to process payment captured',
        processed: false,
        error: errorMessage,
        shouldRetry: true
      };
    }
  }

  // Handle payment failed event
  private async handlePaymentFailed(
    paymentOrder: PaymentOrder,
    paymentEntity: any,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    try {
      // Update payment transaction status
      await db
        .update(paymentTransactions)
        .set({
          status: 'failed',
          failureCode: paymentEntity.error_code,
          failureReason: paymentEntity.error_description,
          gatewayResponse: paymentEntity,
          updatedAt: new Date()
        })
        .where(eq(paymentTransactions.razorpayPaymentId, paymentEntity.id));

      // Update payment order status
      await db
        .update(paymentOrders)
        .set({
          status: 'failed',
          updatedAt: new Date()
        })
        .where(eq(paymentOrders.id, paymentOrder.id));

      // Update booking status
      if (paymentOrder.bookingId) {
        await db
          .update(bookings)
          .set({
            paymentStatus: 'failed',
            status: 'payment_pending'
          })
          .where(eq(bookings.id, paymentOrder.bookingId));
      }

      await auditLogger.logPaymentOrderAction(
        'payment_failed_via_webhook',
        paymentOrder.id,
        { actorType: 'webhook' },
        { status: 'pending' },
        { 
          status: 'failed', 
          paymentId: paymentEntity.id,
          errorCode: paymentEntity.error_code,
          errorDescription: paymentEntity.error_description
        }
      );

      return {
        success: true,
        eventId: context.eventId,
        message: 'Payment failure processed',
        processed: true
      };
    } catch (error) {
      console.error('Failed to handle payment failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        eventId: context.eventId,
        message: 'Failed to process payment failure',
        processed: false,
        error: errorMessage,
        shouldRetry: true
      };
    }
  }

  // Handle payment authorized event
  private async handlePaymentAuthorized(
    paymentOrder: PaymentOrder,
    paymentEntity: any,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    try {
      // Update payment transaction status
      await db
        .update(paymentTransactions)
        .set({
          status: 'authorized',
          method: paymentEntity.method,
          bank: paymentEntity.bank,
          gatewayResponse: paymentEntity,
          updatedAt: new Date()
        })
        .where(eq(paymentTransactions.razorpayPaymentId, paymentEntity.id));

      await auditLogger.logPaymentOrderAction(
        'payment_authorized_via_webhook',
        paymentOrder.id,
        { actorType: 'webhook' },
        { status: 'pending' },
        { status: 'authorized', paymentId: paymentEntity.id }
      );

      return {
        success: true,
        eventId: context.eventId,
        message: 'Payment authorization processed',
        processed: true
      };
    } catch (error) {
      console.error('Failed to handle payment authorized:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        eventId: context.eventId,
        message: 'Failed to process payment authorization',
        processed: false,
        error: errorMessage,
        shouldRetry: true
      };
    }
  }

  // Process order webhooks
  private async processOrderWebhook(
    payload: WebhookPayload,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    // Handle order-related events
    return {
      success: true,
      eventId: context.eventId,
      message: `Processed order event: ${payload.event}`,
      processed: true
    };
  }

  // Process refund webhooks
  private async processRefundWebhook(
    payload: WebhookPayload,
    context: WebhookSecurityContext
  ): Promise<WebhookProcessingResult> {
    // Handle refund-related events
    return {
      success: true,
      eventId: context.eventId,
      message: `Processed refund event: ${payload.event}`,
      processed: true
    };
  }

  // Update webhook status
  private async updateWebhookStatus(eventId: string, status: 'processed' | 'failed'): Promise<void> {
    try {
      await db
        .update(webhookEvents)
        .set({
          status,
          processedAt: new Date()
        })
        .where(eq(webhookEvents.eventId, eventId));
    } catch (error) {
      console.error('Failed to update webhook status:', error);
    }
  }

  // Check if error is retryable
  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'TIMEOUT',
      'ECONNRESET',
      'Database connection failed'
    ];

    return retryableErrors.some(retryableError => 
      error.message?.includes(retryableError) || 
      error.code === retryableError
    );
  }

  // Clean up expired webhook events
  async cleanupExpiredEvents(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // In a real implementation, you'd delete old webhook events
      // For now, we'll just log the cleanup
      console.log('Webhook cleanup would remove events older than:', thirtyDaysAgo);
    } catch (error) {
      console.error('Failed to cleanup expired webhook events:', error);
    }
  }
}

// Export singleton instance
export const enhancedWebhookService = EnhancedWebhookService.getInstance();