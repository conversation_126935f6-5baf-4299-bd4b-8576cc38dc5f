import { logger } from './LoggerService';

// Metric types
export enum MetricType {
  COUNTER = 'COUNTER',           // Monotonically increasing values
  GAUGE = 'GAUGE',              // Values that can go up or down
  HISTOGRAM = 'HISTOGRAM',      // Distribution of values
  TIMER = 'TIMER'               // Time-based measurements
}

// Metric data structure
export interface Metric {
  name: string;
  type: MetricType;
  value: number;
  timestamp: Date;
  labels?: Record<string, string>;
  description?: string;
}

// Histogram bucket configuration
export interface HistogramConfig {
  buckets: number[];
}

// Timer result
export interface TimerResult {
  duration: number;
  labels?: Record<string, string>;
}

// Metrics storage interface
interface MetricStorage {
  counters: Map<string, { value: number; labels?: Record<string, string> }>;
  gauges: Map<string, { value: number; labels?: Record<string, string>; timestamp: Date }>;
  histograms: Map<string, {
    buckets: Map<number, number>;
    sum: number;
    count: number;
    config: HistogramConfig;
  }>;
  timers: Map<string, { startTime: number; labels?: Record<string, string> }>;
}

// Application-specific metric names
export const METRICS = {
  // HTTP Requests
  HTTP_REQUESTS_TOTAL: 'http_requests_total',
  HTTP_REQUEST_DURATION: 'http_request_duration_seconds',
  HTTP_RESPONSE_SIZE: 'http_response_size_bytes',

  // Database
  DB_CONNECTIONS_ACTIVE: 'db_connections_active',
  DB_CONNECTIONS_IDLE: 'db_connections_idle',
  DB_QUERIES_TOTAL: 'db_queries_total',
  DB_QUERY_DURATION: 'db_query_duration_seconds',
  DB_ERRORS_TOTAL: 'db_errors_total',

  // Bookings
  BOOKINGS_CREATED_TOTAL: 'bookings_created_total',
  BOOKING_CONFLICTS_TOTAL: 'booking_conflicts_total',
  BOOKING_CLEANUP_RUNS: 'booking_cleanup_runs_total',
  BOOKING_CLEANUP_DELETED: 'booking_cleanup_deleted_total',
  BOOKING_CLEANUP_DURATION: 'booking_cleanup_duration_seconds',

  // Payments
  PAYMENTS_PROCESSED_TOTAL: 'payments_processed_total',
  PAYMENT_FAILURES_TOTAL: 'payment_failures_total',
  PAYMENT_PROCESSING_DURATION: 'payment_processing_duration_seconds',
  PAYMENT_AMOUNT_TOTAL: 'payment_amount_total',

  // Authentication
  AUTH_ATTEMPTS_TOTAL: 'auth_attempts_total',
  OTP_SENT_TOTAL: 'otp_sent_total',
  OTP_VERIFICATION_ATTEMPTS: 'otp_verification_attempts_total',

  // Circuit Breakers
  CIRCUIT_BREAKER_STATE: 'circuit_breaker_state',
  CIRCUIT_BREAKER_TRANSITIONS: 'circuit_breaker_transitions_total',

  // Error Recovery
  RETRY_ATTEMPTS_TOTAL: 'retry_attempts_total',
  FALLBACK_USED_TOTAL: 'fallback_used_total',
  ERROR_RECOVERY_SUCCESS: 'error_recovery_success_total',

  // System
  MEMORY_USAGE: 'memory_usage_bytes',
  CPU_USAGE: 'cpu_usage_percent',
  ACTIVE_CONNECTIONS: 'active_connections_total'
} as const;

export class MetricsService {
  private storage: MetricStorage;
  private systemMetricsInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.storage = {
      counters: new Map(),
      gauges: new Map(),
      histograms: new Map(),
      timers: new Map()
    };

    this.initializeSystemMetrics();
  }

  /**
   * Increment a counter metric
   */
  incrementCounter(name: string, value: number = 1, labels?: Record<string, string>): void {
    const key = this.getMetricKey(name, labels);
    const existing = this.storage.counters.get(key) || { value: 0, labels };
    
    const counterData: any = {
      value: existing.value + value
    };
    
    if (labels !== undefined) {
      counterData.labels = labels;
    } else if (existing.labels !== undefined) {
      counterData.labels = existing.labels;
    }
    
    this.storage.counters.set(key, counterData);

    logger.debug('Counter incremented', 'metrics', {
      metric: name,
      value,
      newTotal: existing.value + value,
      labels
    });
  }

  /**
   * Set a gauge metric value
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    const key = this.getMetricKey(name, labels);
    
    const gaugeData: any = {
      value,
      timestamp: new Date()
    };
    
    if (labels !== undefined) {
      gaugeData.labels = labels;
    }
    
    this.storage.gauges.set(key, gaugeData);

    logger.debug('Gauge set', 'metrics', { metric: name, value, labels });
  }

  /**
   * Record a value in a histogram
   */
  recordHistogram(
    name: string, 
    value: number, 
    labels?: Record<string, string>,
    config?: HistogramConfig
  ): void {
    const key = this.getMetricKey(name, labels);
    
    if (!this.storage.histograms.has(key)) {
      const defaultBuckets = [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10];
      this.storage.histograms.set(key, {
        buckets: new Map(),
        sum: 0,
        count: 0,
        config: config || { buckets: defaultBuckets }
      });
    }

    const histogram = this.storage.histograms.get(key)!;
    histogram.sum += value;
    histogram.count++;

    // Update buckets
    for (const bucket of histogram.config.buckets) {
      if (value <= bucket) {
        const currentCount = histogram.buckets.get(bucket) || 0;
        histogram.buckets.set(bucket, currentCount + 1);
      }
    }

    logger.debug('Histogram recorded', 'metrics', {
      metric: name,
      value,
      count: histogram.count,
      sum: histogram.sum,
      labels
    });
  }

  /**
   * Start a timer
   */
  startTimer(name: string, labels?: Record<string, string>): string {
    const timerId = `${name}_${Date.now()}_${Math.random()}`;
    
    const timerData: any = {
      startTime: Date.now()
    };
    
    if (labels !== undefined) {
      timerData.labels = labels;
    }
    
    this.storage.timers.set(timerId, timerData);

    return timerId;
  }

  /**
   * Stop a timer and record the duration
   */
  stopTimer(timerId: string): TimerResult | null {
    const timer = this.storage.timers.get(timerId);
    if (!timer) {
      logger.warn('Timer not found', 'metrics', { timerId });
      return null;
    }

    const duration = (Date.now() - timer.startTime) / 1000; // Convert to seconds
    this.storage.timers.delete(timerId);

    // Extract metric name from timer ID (format: name_timestamp_random)
    const metricName = timerId.split('_').slice(0, -2).join('_');
    this.recordHistogram(metricName, duration, timer.labels);

    logger.debug('Timer stopped', 'metrics', {
      timerId,
      duration,
      labels: timer.labels
    });

    const result: any = { duration };
    
    if (timer.labels !== undefined) {
      result.labels = timer.labels;
    }
    
    return result;
  }

  /**
   * Time a function execution
   */
  async timeFunction<T>(
    name: string,
    fn: () => Promise<T>,
    labels?: Record<string, string>
  ): Promise<T> {
    const timerId = this.startTimer(name, labels);
    
    try {
      const result = await fn();
      this.stopTimer(timerId);
      return result;
    } catch (error) {
      this.stopTimer(timerId);
      this.incrementCounter(`${name}_error_total`, 1, labels);
      throw error;
    }
  }

  /**
   * Get all current metrics
   */
  getAllMetrics(): {
    counters: Array<Metric>;
    gauges: Array<Metric>;
    histograms: Array<{
      name: string;
      buckets: Array<{ le: number; count: number }>;
      sum: number;
      count: number;
      labels?: Record<string, string>;
    }>;
  } {
    const counters: Array<Metric> = [];
    const gauges: Array<Metric> = [];
    const histograms: Array<any> = [];

    // Process counters
    for (const [key, counter] of this.storage.counters) {
      const { name } = this.parseMetricKey(key);
      const counterMetric: any = {
        name,
        type: MetricType.COUNTER,
        value: counter.value,
        timestamp: new Date()
      };
      
      if (counter.labels !== undefined) {
        counterMetric.labels = counter.labels;
      }
      
      counters.push(counterMetric);
    }

    // Process gauges
    for (const [key, gauge] of this.storage.gauges) {
      const { name } = this.parseMetricKey(key);
      const gaugeMetric: any = {
        name,
        type: MetricType.GAUGE,
        value: gauge.value,
        timestamp: gauge.timestamp
      };
      
      if (gauge.labels !== undefined) {
        gaugeMetric.labels = gauge.labels;
      }
      
      gauges.push(gaugeMetric);
    }

    // Process histograms
    for (const [key, histogram] of this.storage.histograms) {
      const { name, labels } = this.parseMetricKey(key);
      const buckets = Array.from(histogram.buckets.entries()).map(([le, count]) => ({
        le,
        count
      }));

      histograms.push({
        name,
        buckets,
        sum: histogram.sum,
        count: histogram.count,
        labels
      });
    }

    return { counters, gauges, histograms };
  }

  /**
   * Get metrics in Prometheus format
   */
  getPrometheusMetrics(): string {
    const lines: string[] = [];
    const { counters, gauges, histograms } = this.getAllMetrics();

    // Add counters
    for (const counter of counters) {
      const labelsStr = this.formatLabels(counter.labels);
      lines.push(`# TYPE ${counter.name} counter`);
      lines.push(`${counter.name}${labelsStr} ${counter.value}`);
    }

    // Add gauges
    for (const gauge of gauges) {
      const labelsStr = this.formatLabels(gauge.labels);
      lines.push(`# TYPE ${gauge.name} gauge`);
      lines.push(`${gauge.name}${labelsStr} ${gauge.value}`);
    }

    // Add histograms
    for (const histogram of histograms) {
      const baseLabelsStr = this.formatLabels(histogram.labels);
      lines.push(`# TYPE ${histogram.name} histogram`);
      
      // Add buckets
      for (const bucket of histogram.buckets) {
        const bucketLabels = { ...histogram.labels, le: bucket.le.toString() };
        const bucketLabelsStr = this.formatLabels(bucketLabels);
        lines.push(`${histogram.name}_bucket${bucketLabelsStr} ${bucket.count}`);
      }
      
      // Add sum and count
      lines.push(`${histogram.name}_sum${baseLabelsStr} ${histogram.sum}`);
      lines.push(`${histogram.name}_count${baseLabelsStr} ${histogram.count}`);
    }

    return lines.join('\n');
  }

  /**
   * Initialize system metrics collection
   */
  private initializeSystemMetrics(): void {
    // Collect system metrics every 30 seconds
    this.systemMetricsInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    logger.info('System metrics collection started');
  }

  /**
   * Collect system metrics
   */
  private collectSystemMetrics(): void {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      // Memory metrics
      this.setGauge(METRICS.MEMORY_USAGE, memUsage.heapUsed, { type: 'heap_used' });
      this.setGauge(METRICS.MEMORY_USAGE, memUsage.heapTotal, { type: 'heap_total' });
      this.setGauge(METRICS.MEMORY_USAGE, memUsage.rss, { type: 'rss' });
      this.setGauge(METRICS.MEMORY_USAGE, memUsage.external, { type: 'external' });

      // Note: CPU usage calculation would need more sophisticated implementation
      // This is a simplified version
      this.setGauge(METRICS.CPU_USAGE, cpuUsage.user / 1000000, { type: 'user' });
      this.setGauge(METRICS.CPU_USAGE, cpuUsage.system / 1000000, { type: 'system' });

    } catch (error) {
      logger.error('Failed to collect system metrics', error instanceof Error ? error : new Error(String(error)), 'metrics');
    }
  }

  /**
   * Stop metrics collection
   */
  stop(): void {
    if (this.systemMetricsInterval) {
      clearInterval(this.systemMetricsInterval);
      this.systemMetricsInterval = null;
    }
    logger.info('Metrics collection stopped');
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.storage = {
      counters: new Map(),
      gauges: new Map(),
      histograms: new Map(),
      timers: new Map()
    };
    logger.info('All metrics reset');
  }

  /**
   * Generate metric key with labels
   */
  private getMetricKey(name: string, labels?: Record<string, string>): string {
    if (!labels || Object.keys(labels).length === 0) {
      return name;
    }
    
    const labelPairs = Object.entries(labels)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');
    
    return `${name}{${labelPairs}}`;
  }

  /**
   * Parse metric key to extract name and labels
   */
  private parseMetricKey(key: string): { name: string; labels?: Record<string, string> } {
    const braceIndex = key.indexOf('{');
    if (braceIndex === -1) {
      return { name: key };
    }

    const name = key.substring(0, braceIndex);
    const labelsStr = key.substring(braceIndex + 1, key.length - 1);
    
    const labels: Record<string, string> = {};
    if (labelsStr) {
      const labelPairs = labelsStr.split(',');
      for (const pair of labelPairs) {
        const [key, value] = pair.split('=');
        if (key && value) {
          labels[key] = value.replace(/"/g, '');
        }
      }
    }

    return { name, labels };
  }

  /**
   * Format labels for Prometheus output
   */
  private formatLabels(labels?: Record<string, string>): string {
    if (!labels || Object.keys(labels).length === 0) {
      return '';
    }

    const labelPairs = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');

    return `{${labelPairs}}`;
  }
}

// Export singleton instance
export const metricsService = new MetricsService();