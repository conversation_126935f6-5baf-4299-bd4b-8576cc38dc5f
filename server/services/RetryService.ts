import { logger } from './LoggerService';

// Error types that should be retried
export enum RetryableErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  TEMPORARY_SERVICE_ERROR = 'TEMPORARY_SERVICE_ERROR'
}

// Configuration for retry behavior
export interface RetryConfig {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  jitterMs?: number;
  retryableErrors?: RetryableErrorType[];
  onRetry?: (attempt: number, error: Error) => void;
}

// Default retry configuration
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelayMs: 1000,
  maxDelayMs: 30000,
  backoffMultiplier: 2,
  jitterMs: 100,
  retryableErrors: [
    RetryableErrorType.NETWORK_ERROR,
    RetryableErrorType.TIMEOUT_ERROR,
    RetryableErrorType.DATABASE_CONNECTION_ERROR,
    RetryableErrorType.RATE_LIMIT_ERROR,
    RetryableErrorType.TEMPORARY_SERVICE_ERROR
  ]
};

// Result of retry operation
export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalDuration: number;
}

// Custom error for retry exhaustion
export class RetryExhaustedError extends Error {
  constructor(
    public readonly originalError: Error,
    public readonly attempts: number,
    public readonly totalDuration: number
  ) {
    super(`Retry exhausted after ${attempts} attempts over ${totalDuration}ms: ${originalError.message}`);
    this.name = 'RetryExhaustedError';
  }
}

export class RetryService {
  /**
   * Execute an operation with retry logic and exponential backoff
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    operationName?: string
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
    const startTime = Date.now();
    let lastError: Error;

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        logger.debug('Executing operation with retry', 'retry', {
          operationName,
          attempt,
          maxAttempts: finalConfig.maxAttempts
        });

        const result = await operation();
        
        if (attempt > 1) {
          logger.info('Operation succeeded after retry', 'retry', {
            operationName,
            attempt,
            totalDuration: Date.now() - startTime
          });
        }

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Check if this error should be retried
        if (!this.shouldRetry(lastError, finalConfig)) {
          logger.warn('Operation failed with non-retryable error', 'retry', {
            operationName,
            attempt,
            error: lastError.message
          });
          throw lastError;
        }

        // Don't sleep on the last attempt
        if (attempt === finalConfig.maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateDelay(attempt, finalConfig);
        
        logger.warn('Operation failed, retrying after delay', 'retry', {
          operationName,
          attempt,
          maxAttempts: finalConfig.maxAttempts,
          delayMs: delay,
          error: lastError.message
        });

        // Call retry callback if provided
        finalConfig.onRetry?.(attempt, lastError);

        // Wait before retry
        await this.sleep(delay);
      }
    }

    // All retries exhausted
    const totalDuration = Date.now() - startTime;
    logger.error('All retry attempts exhausted', lastError!, 'retry', {
      operationName,
      attempts: finalConfig.maxAttempts,
      totalDuration
    });

    throw new RetryExhaustedError(lastError!, finalConfig.maxAttempts, totalDuration);
  }

  /**
   * Execute operation with detailed retry result information
   */
  static async executeWithRetryResult<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    operationName?: string
  ): Promise<RetryResult<T>> {
    const startTime = Date.now();

    try {
      const result = await this.executeWithRetry(operation, config, operationName);
      return {
        success: true,
        result,
        attempts: 1, // If successful on first try
        totalDuration: Date.now() - startTime
      };
    } catch (error) {
      const totalDuration = Date.now() - startTime;
      
      if (error instanceof RetryExhaustedError) {
        return {
          success: false,
          error: error.originalError,
          attempts: error.attempts,
          totalDuration: error.totalDuration
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
        attempts: 1,
        totalDuration
      };
    }
  }

  /**
   * Determine if an error should trigger a retry
   */
  private static shouldRetry(error: Error, config: RetryConfig): boolean {
    // Check for specific error patterns
    const errorMessage = error.message.toLowerCase();
    const errorName = error.name.toLowerCase();

    // Network and connection errors
    if (errorMessage.includes('timeout') || 
        errorMessage.includes('connection') ||
        errorMessage.includes('network') ||
        errorName.includes('timeout')) {
      return true;
    }

    // Database connection errors
    if (errorMessage.includes('database') && 
        (errorMessage.includes('connection') || errorMessage.includes('pool'))) {
      return true;
    }

    // Rate limiting errors
    if (errorMessage.includes('rate limit') || 
        errorMessage.includes('too many requests') ||
        (error as any).status === 429) {
      return true;
    }

    // HTTP 5xx errors (server errors)
    const status = (error as any).status || (error as any).statusCode;
    if (status >= 500 && status < 600) {
      return true;
    }

    // Temporary service unavailable
    if (status === 503 || errorMessage.includes('service unavailable')) {
      return true;
    }

    return false;
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private static calculateDelay(attempt: number, config: RetryConfig): number {
    // Exponential backoff: baseDelay * (multiplier ^ (attempt - 1))
    const exponentialDelay = config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt - 1);
    
    // Cap at maximum delay
    const cappedDelay = Math.min(exponentialDelay, config.maxDelayMs);
    
    // Add jitter to prevent thundering herd
    const jitter = config.jitterMs ? Math.random() * config.jitterMs : 0;
    
    return Math.floor(cappedDelay + jitter);
  }

  /**
   * Sleep for specified milliseconds
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a retry configuration for database operations
   */
  static createDatabaseRetryConfig(): RetryConfig {
    return {
      maxAttempts: 5,
      baseDelayMs: 500,
      maxDelayMs: 10000,
      backoffMultiplier: 2,
      jitterMs: 200,
      onRetry: (attempt, error) => {
        logger.warn('Database operation retry', 'retry', { attempt, error: error.message });
      }
    };
  }

  /**
   * Create a retry configuration for external API calls
   */
  static createAPIRetryConfig(): RetryConfig {
    return {
      maxAttempts: 3,
      baseDelayMs: 1000,
      maxDelayMs: 15000,
      backoffMultiplier: 2,
      jitterMs: 500,
      onRetry: (attempt, error) => {
        logger.warn('API operation retry', 'retry', { attempt, error: error.message });
      }
    };
  }

  /**
   * Create a retry configuration for payment operations
   */
  static createPaymentRetryConfig(): RetryConfig {
    return {
      maxAttempts: 2, // Limited retries for payment operations
      baseDelayMs: 2000,
      maxDelayMs: 5000,
      backoffMultiplier: 1.5,
      jitterMs: 200,
      onRetry: (attempt, error) => {
        logger.warn('Payment operation retry', 'retry', { 
          attempt, 
          error: error.message,
          context: 'critical_payment_operation'
        });
      }
    };
  }
}