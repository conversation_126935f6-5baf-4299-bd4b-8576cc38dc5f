import { cacheService } from './CacheService';
import { logger } from './LoggerService';

/**
 * Token blacklisting service using in-memory cache
 * Provides secure token invalidation without Redis dependency
 */
export class TokenBlacklistService {
  private static readonly BLACKLIST_PREFIX = 'blacklisted_token';
  private static readonly TOKEN_EXPIRY = 7 * 24 * 60 * 60; // 7 days in seconds

  /**
   * Add a token to the blacklist
   */
  static async blacklistToken(token: string): Promise<void> {
    if (!token) {
      logger.warn('Attempted to blacklist null/undefined token', 'security');
      return;
    }
    
    const key = `${this.BLACKLIST_PREFIX}:${token}`;
    await cacheService.set(key, true, this.TOKEN_EXPIRY);
    logger.info('Token blacklisted', 'security', { 
      tokenPrefix: token.length > 10 ? token.substring(0, 10) + '...' : token
    });
  }

  /**
   * Check if a token is blacklisted
   */
  static async isTokenBlacklisted(token: string): Promise<boolean> {
    if (!token) {
      return false; // Null/undefined tokens are not blacklisted
    }
    
    const key = `${this.BLACKLIST_PREFIX}:${token}`;
    return await cacheService.exists(key);
  }

  /**
   * Blacklist all tokens for a specific user (placeholder)
   * In a production system, you'd want to track tokens per user
   */
  static async blacklistUserTokens(userId: string): Promise<void> {
    logger.info('User tokens marked for blacklisting', 'security', { userId });
    // This would require tracking tokens per user in the database
    // For MVP, we'll just log the action
  }

  /**
   * Get statistics about blacklisted tokens
   */
  static async getBlacklistStats(): Promise<{
    count: number;
    memoryUsage: string;
  }> {
    const keys = await cacheService.keys(`${this.BLACKLIST_PREFIX}:*`);
    const stats = cacheService.getStats();
    
    return {
      count: keys.length,
      memoryUsage: stats.memoryUsage
    };
  }

  /**
   * Clear expired tokens (handled automatically by cache service)
   */
  static async clearExpiredTokens(): Promise<void> {
    const keys = await cacheService.keys(`${this.BLACKLIST_PREFIX}:*`);
    logger.info('Token blacklist status', 'security', { 
      activeTokens: keys.length 
    });
  }
}