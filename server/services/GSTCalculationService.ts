import { db } from "../db";
import { 
  gstRecords, 
  gstRateConfigurations,
  GstRecord,
  GstRateConfiguration,
  InsertGstRecord,
  InsertGstRateConfiguration
} from "../../shared/schema";
import { eq, and, lte, gte, isNull, or } from "drizzle-orm";
import { auditLogger } from "./AuditLogger";
import { cacheService } from "./CacheService";

// Indian state codes and GST information
const INDIAN_STATES = {
  'AN': { name: 'Andaman and Nicobar Islands', code: 'AN', gstCode: '35' },
  'AP': { name: 'Andhra Pradesh', code: 'AP', gstCode: '28' },
  'AR': { name: 'Arunachal Pradesh', code: 'AR', gstCode: '12' },
  'AS': { name: 'Assam', code: 'AS', gstCode: '18' },
  'BR': { name: 'Bihar', code: 'BR', gstCode: '10' },
  'CG': { name: 'Chhattisgarh', code: 'CG', gstCode: '22' },
  'CH': { name: 'Chandigarh', code: 'CH', gstCode: '04' },
  'DN': { name: 'Dadra and Nagar Haveli', code: 'DN', gstCode: '26' },
  'DD': { name: 'Daman and Diu', code: 'DD', gstCode: '25' },
  'DL': { name: 'Delhi', code: 'DL', gstCode: '07' },
  'GA': { name: 'Goa', code: 'GA', gstCode: '30' },
  'GJ': { name: 'Gujarat', code: 'GJ', gstCode: '24' },
  'HR': { name: 'Haryana', code: 'HR', gstCode: '06' },
  'HP': { name: 'Himachal Pradesh', code: 'HP', gstCode: '02' },
  'JK': { name: 'Jammu and Kashmir', code: 'JK', gstCode: '01' },
  'JH': { name: 'Jharkhand', code: 'JH', gstCode: '20' },
  'KA': { name: 'Karnataka', code: 'KA', gstCode: '29' },
  'KL': { name: 'Kerala', code: 'KL', gstCode: '32' },
  'LA': { name: 'Ladakh', code: 'LA', gstCode: '38' },
  'LD': { name: 'Lakshadweep', code: 'LD', gstCode: '31' },
  'MP': { name: 'Madhya Pradesh', code: 'MP', gstCode: '23' },
  'MH': { name: 'Maharashtra', code: 'MH', gstCode: '27' },
  'MN': { name: 'Manipur', code: 'MN', gstCode: '14' },
  'ML': { name: 'Meghalaya', code: 'ML', gstCode: '17' },
  'MZ': { name: 'Mizoram', code: 'MZ', gstCode: '15' },
  'NL': { name: 'Nagaland', code: 'NL', gstCode: '13' },
  'OD': { name: 'Odisha', code: 'OD', gstCode: '21' },
  'PY': { name: 'Puducherry', code: 'PY', gstCode: '34' },
  'PB': { name: 'Punjab', code: 'PB', gstCode: '03' },
  'RJ': { name: 'Rajasthan', code: 'RJ', gstCode: '08' },
  'SK': { name: 'Sikkim', code: 'SK', gstCode: '11' },
  'TN': { name: 'Tamil Nadu', code: 'TN', gstCode: '33' },
  'TS': { name: 'Telangana', code: 'TS', gstCode: '36' },
  'TR': { name: 'Tripura', code: 'TR', gstCode: '16' },
  'UP': { name: 'Uttar Pradesh', code: 'UP', gstCode: '09' },
  'UK': { name: 'Uttarakhand', code: 'UK', gstCode: '05' },
  'WB': { name: 'West Bengal', code: 'WB', gstCode: '19' }
};

// Service types and their HSN/SAC codes
const SERVICE_TYPES = {
  'accommodation': {
    hsnSacCode: '996311',
    description: 'Accommodation services in hotels, inns, guest houses, clubs, campsites',
    defaultRate: 18.00
  },
  'food_beverage': {
    hsnSacCode: '996331',
    description: 'Food and beverage serving services',
    defaultRate: 5.00
  },
  'transport': {
    hsnSacCode: '996411',
    description: 'Transport services',
    defaultRate: 18.00
  },
  'entertainment': {
    hsnSacCode: '999220',
    description: 'Entertainment services',
    defaultRate: 18.00
  },
  'conference': {
    hsnSacCode: '994110',
    description: 'Conference and convention services',
    defaultRate: 18.00
  }
};

interface GSTCalculationInput {
  baseAmount: number; // Amount in paise
  supplierState: string; // State code
  recipientState: string; // State code
  serviceType: string; // Service type key
  supplierGstin?: string; // Supplier GSTIN
  recipientGstin?: string; // Recipient GSTIN (for B2B)
  transactionDate?: Date; // Date for rate calculation
  placeOfSupply?: string; // Override place of supply
}

interface GSTCalculationResult {
  baseAmount: number;
  transactionType: 'intrastate' | 'interstate' | 'export' | 'import';
  serviceType: string;
  hsnSacCode: string;
  
  // Intrastate GST
  cgstRate: number;
  sgstRate: number;
  cgstAmount: number;
  sgstAmount: number;
  
  // Interstate GST
  igstRate: number;
  igstAmount: number;
  
  // Additional taxes
  cessRate: number;
  cessAmount: number;
  
  // Totals
  totalGstRate: number;
  totalGstAmount: number;
  totalAmount: number;
  
  // Metadata
  rateConfigId: number | undefined;
  applicableDate: string;
  placeOfSupply: string;
  supplierState: string;
  recipientState: string;
}

export class GSTCalculationService {
  private static instance: GSTCalculationService;
  private readonly cacheTimeout = 60 * 60; // 1 hour in seconds

  private constructor() {
    // Initialize default rate configurations
    this.initializeDefaultRates();
  }

  public static getInstance(): GSTCalculationService {
    if (!GSTCalculationService.instance) {
      GSTCalculationService.instance = new GSTCalculationService();
    }
    return GSTCalculationService.instance;
  }

  // Main GST calculation method
  async calculateGST(input: GSTCalculationInput): Promise<GSTCalculationResult> {
    try {
      // Validate input
      this.validateInput(input);

      // Determine transaction type
      const transactionType = this.getTransactionType(input.supplierState, input.recipientState);

      // Get rate configuration
      const rateConfig = await this.getRateConfiguration(
        input.serviceType,
        input.transactionDate || new Date()
      );

      // Calculate GST amounts
      const result = this.calculateGSTAmounts(input, transactionType, rateConfig);

      // Log calculation
      await auditLogger.logPaymentAction('gst_calculated', {
        actorType: 'system',
        metadata: {
          baseAmount: input.baseAmount,
          transactionType,
          totalGstAmount: result.totalGstAmount,
          serviceType: input.serviceType
        }
      });

      return result;
    } catch (error) {
      console.error('GST calculation failed:', error);
      throw error;
    }
  }

  // Store GST record in database
  async storeGSTRecord(
    bookingId: number,
    gstResult: GSTCalculationResult,
    invoiceNumber?: string
  ): Promise<GstRecord> {
    try {
      const gstRecord: InsertGstRecord = {
        bookingId,
        baseAmount: gstResult.baseAmount,
        transactionType: gstResult.transactionType,
        serviceType: gstResult.serviceType,
        hsnSacCode: gstResult.hsnSacCode,
        supplierState: gstResult.supplierState,
        recipientState: gstResult.recipientState,
        placeOfSupply: gstResult.placeOfSupply,
        gstRateConfigId: gstResult.rateConfigId,
        applicableDate: gstResult.applicableDate,
        
        // Intrastate GST
        cgstRate: gstResult.cgstRate,
        sgstRate: gstResult.sgstRate,
        cgstAmount: gstResult.cgstAmount,
        sgstAmount: gstResult.sgstAmount,
        
        // Interstate GST
        igstRate: gstResult.igstRate,
        igstAmount: gstResult.igstAmount,
        
        // Additional taxes
        cessRate: gstResult.cessRate,
        cessAmount: gstResult.cessAmount,
        
        // Totals
        totalGst: gstResult.totalGstAmount,
        totalAmount: gstResult.totalAmount,
        
        // Compliance
        invoiceNumber,
        invoiceDate: new Date().toISOString().split('T')[0],
      };

      const [record] = await db
        .insert(gstRecords)
        .values(gstRecord)
        .returning();

      await auditLogger.logPaymentAction('gst_record_stored', {
        actorType: 'system',
        metadata: {
          bookingId,
          gstRecordId: record.id,
          totalGstAmount: gstResult.totalGstAmount
        }
      });

      return record;
    } catch (error) {
      console.error('Failed to store GST record:', error);
      throw error;
    }
  }

  // Create or update rate configuration
  async createRateConfiguration(
    serviceType: string,
    hsnSacCode: string,
    rateStructure: any,
    effectiveFrom: Date,
    effectiveTo?: Date,
    createdBy?: number
  ): Promise<GstRateConfiguration> {
    try {
      const configData: InsertGstRateConfiguration = {
        serviceType,
        hsnSacCode,
        rateStructure,
        effectiveFrom: effectiveFrom.toISOString().split('T')[0],
        effectiveTo: effectiveTo?.toISOString().split('T')[0],
        createdBy,
      };

      const [config] = await db
        .insert(gstRateConfigurations)
        .values(configData)
        .returning();

      // Clear cache for this service type
      await this.clearRateCache(serviceType);

      await auditLogger.logPaymentAction('gst_rate_config_created', {
        actorType: 'admin',
        ...(createdBy && { actorId: createdBy }),
        metadata: {
          serviceType,
          hsnSacCode,
          effectiveFrom: effectiveFrom.toISOString(),
          effectiveTo: effectiveTo?.toISOString()
        }
      });

      return config;
    } catch (error) {
      console.error('Failed to create rate configuration:', error);
      throw error;
    }
  }

  // Get GST record for a booking
  async getGSTRecord(bookingId: number): Promise<GstRecord | null> {
    try {
      const [record] = await db
        .select()
        .from(gstRecords)
        .where(eq(gstRecords.bookingId, bookingId));

      return record || null;
    } catch (error) {
      console.error('Failed to get GST record:', error);
      return null;
    }
  }

  // Validate GSTIN format
  validateGSTIN(gstin: string): boolean {
    const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstinRegex.test(gstin);
  }

  // Get state from GSTIN
  getStateFromGSTIN(gstin: string): string | null {
    if (!this.validateGSTIN(gstin)) {
      return null;
    }

    const stateCode = gstin.substring(0, 2);
    
    // Find state by GST code
    for (const [code, state] of Object.entries(INDIAN_STATES)) {
      if (state.gstCode === stateCode) {
        return code;
      }
    }

    return null;
  }

  // Generate invoice number
  generateInvoiceNumber(bookingId: number, gstRecordId: number): string {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    return `INV-${year}${month}-${bookingId}-${gstRecordId}`;
  }

  // Private methods

  private validateInput(input: GSTCalculationInput): void {
    if (!input.baseAmount || input.baseAmount <= 0) {
      throw new Error('Base amount must be positive');
    }

    if (!input.supplierState || !INDIAN_STATES[input.supplierState as keyof typeof INDIAN_STATES]) {
      throw new Error('Invalid supplier state');
    }

    if (!input.recipientState || !INDIAN_STATES[input.recipientState as keyof typeof INDIAN_STATES]) {
      throw new Error('Invalid recipient state');
    }

    if (!input.serviceType || !SERVICE_TYPES[input.serviceType as keyof typeof SERVICE_TYPES]) {
      throw new Error('Invalid service type');
    }

    if (input.supplierGstin && !this.validateGSTIN(input.supplierGstin)) {
      throw new Error('Invalid supplier GSTIN');
    }

    if (input.recipientGstin && !this.validateGSTIN(input.recipientGstin)) {
      throw new Error('Invalid recipient GSTIN');
    }
  }

  private getTransactionType(supplierState: string, recipientState: string): 'intrastate' | 'interstate' | 'export' | 'import' {
    // For now, we only handle domestic transactions
    // Export/Import handling can be added later
    return supplierState === recipientState ? 'intrastate' : 'interstate';
  }

  private async getRateConfiguration(
    serviceType: string,
    transactionDate: Date
  ): Promise<GstRateConfiguration | null> {
    const cacheKey = `gst_rate:${serviceType}_${transactionDate.toISOString().split('T')[0]}`;
    
    // Try to get from cache first
    try {
      const cachedConfig = await cacheService.get<GstRateConfiguration>(cacheKey);
      if (cachedConfig) {
        return cachedConfig;
      }
    } catch (error) {
      // Log cache error but continue with database query
      console.error('Cache service error:', error);
    }

    // Query database
    const dateString = transactionDate.toISOString().split('T')[0];
    
    const [config] = await db
      .select()
      .from(gstRateConfigurations)
      .where(and(
        eq(gstRateConfigurations.serviceType, serviceType),
        lte(gstRateConfigurations.effectiveFrom, dateString),
        or(
          isNull(gstRateConfigurations.effectiveTo),
          gte(gstRateConfigurations.effectiveTo, dateString)
        )
      ))
      .orderBy(gstRateConfigurations.effectiveFrom)
      .limit(1);

    if (config) {
      // Cache the result
      await cacheService.set(cacheKey, config, this.cacheTimeout);
    }

    return config || null;
  }

  private calculateGSTAmounts(
    input: GSTCalculationInput,
    transactionType: 'intrastate' | 'interstate' | 'export' | 'import',
    rateConfig: GstRateConfiguration | null
  ): GSTCalculationResult {
    const serviceInfo = SERVICE_TYPES[input.serviceType as keyof typeof SERVICE_TYPES];
    const placeOfSupply = input.placeOfSupply || input.recipientState;

    // Get GST rates from config or default
    let gstRate = serviceInfo.defaultRate;
    let rateConfigId: number | undefined;

    if (rateConfig) {
      const rateStructure = rateConfig.rateStructure as any;
      gstRate = rateStructure.standardRate || rateStructure.rate || serviceInfo.defaultRate;
      rateConfigId = rateConfig.id;
    }

    let cgstRate = 0;
    let sgstRate = 0;
    let igstRate = 0;
    let cessRate = 0;

    // Calculate based on transaction type
    if (transactionType === 'intrastate') {
      cgstRate = gstRate / 2;
      sgstRate = gstRate / 2;
    } else if (transactionType === 'interstate') {
      igstRate = gstRate;
    }

    // Calculate amounts
    const cgstAmount = Math.round((input.baseAmount * cgstRate) / 100);
    const sgstAmount = Math.round((input.baseAmount * sgstRate) / 100);
    const igstAmount = Math.round((input.baseAmount * igstRate) / 100);
    const cessAmount = Math.round((input.baseAmount * cessRate) / 100);

    const totalGstAmount = cgstAmount + sgstAmount + igstAmount + cessAmount;
    const totalAmount = input.baseAmount + totalGstAmount;

    return {
      baseAmount: input.baseAmount,
      transactionType,
      serviceType: input.serviceType,
      hsnSacCode: serviceInfo.hsnSacCode,
      
      cgstRate,
      sgstRate,
      cgstAmount,
      sgstAmount,
      
      igstRate,
      igstAmount,
      
      cessRate,
      cessAmount,
      
      totalGstRate: gstRate,
      totalGstAmount,
      totalAmount,
      
      rateConfigId,
      applicableDate: (input.transactionDate || new Date()).toISOString().split('T')[0],
      placeOfSupply,
      supplierState: input.supplierState,
      recipientState: input.recipientState
    };
  }

  private async clearRateCache(serviceType: string): Promise<void> {
    try {
      // Get all cache keys matching the pattern
      const pattern = `gst_rate:${serviceType}_*`;
      const keys = await cacheService.keys(pattern);
      
      // Delete all matching keys
      for (const key of keys) {
        await cacheService.delete(key);
      }
    } catch (error) {
      console.error('Failed to clear rate cache:', error);
    }
  }

  private async initializeDefaultRates(): Promise<void> {
    try {
      // Check if default rates exist
      const existingConfigs = await db
        .select()
        .from(gstRateConfigurations)
        .limit(1);

      if (existingConfigs.length === 0) {
        // Create default rate configurations
        await this.createDefaultRateConfigurations();
      }
    } catch (error) {
      console.error('Failed to initialize default rates:', error);
    }
  }

  private async createDefaultRateConfigurations(): Promise<void> {
    const defaultConfigs = [
      {
        serviceType: 'accommodation',
        hsnSacCode: '996311',
        rateStructure: {
          standardRate: 18.0,
          description: 'Accommodation services - Standard rate',
          applicableFrom: '2017-07-01'
        },
        effectiveFrom: '2017-07-01'
      },
      {
        serviceType: 'food_beverage',
        hsnSacCode: '996331',
        rateStructure: {
          standardRate: 5.0,
          description: 'Food and beverage services - Concessional rate',
          applicableFrom: '2017-07-01'
        },
        effectiveFrom: '2017-07-01'
      },
      {
        serviceType: 'transport',
        hsnSacCode: '996411',
        rateStructure: {
          standardRate: 18.0,
          description: 'Transport services - Standard rate',
          applicableFrom: '2017-07-01'
        },
        effectiveFrom: '2017-07-01'
      }
    ];

    for (const config of defaultConfigs) {
      await db
        .insert(gstRateConfigurations)
        .values(config)
        .onConflictDoNothing();
    }

    console.log('Default GST rate configurations created');
  }
}

// Export singleton instance
export const gstCalculationService = GSTCalculationService.getInstance();