export interface OTPRequest {
  identifier: string; // email or phone
  type: 'email' | 'sms';
}

export interface OTPVerification {
  identifier: string;
  code: string;
  type: 'email' | 'sms';
}

export interface OTPResult {
  success: boolean;
  message: string;
  code?: string | undefined; // Only for development/testing
}

export abstract class BaseOTPProvider {
  abstract readonly type: 'email' | 'sms';
  abstract readonly name: string;
  
  abstract isAvailable(): boolean;
  abstract sendOTP(identifier: string, code: string): Promise<OTPResult>;
  
  generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
  
  validateIdentifier(identifier: string): boolean {
    if (this.type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(identifier);
    } else {
      // Phone validation for Indian numbers
      const phoneRegex = /^(\+91)?[6-9]\d{9}$/;
      return phoneRegex.test(identifier.replace(/\s/g, ''));
    }
  }
  
  sanitizeIdentifier(identifier: string): string {
    if (this.type === 'email') {
      return identifier.toLowerCase().trim();
    } else {
      // Normalize phone number
      let phone = identifier.replace(/\D/g, '');
      if (phone.length === 10) {
        return `+91${phone}`;
      } else if (phone.length === 12 && phone.startsWith('91')) {
        return `+${phone}`;
      }
      return identifier;
    }
  }
}