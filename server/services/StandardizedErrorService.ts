import { logger } from './LoggerService';

// Standard error codes for the application
export enum ErrorCode {
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',

  // Business Logic
  BOOKING_CONFLICT = 'BOOKING_CONFLICT',
  INSUFFICIENT_AVAILABILITY = 'INSUFFICIENT_AVAILABILITY',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  RESOURCE_LIMIT_EXCEEDED = 'RESOURCE_LIMIT_EXCEEDED',

  // Database
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  DATABASE_TIMEOUT = 'DATABASE_TIMEOUT',
  DATABASE_CONSTRAINT_VIOLATION = 'DATABASE_CONSTRAINT_VIOLATION',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',

  // External Services
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  PAYMENT_GATEWAY_ERROR = 'PAYMENT_GATEWAY_ERROR',
  NOTIFICATION_SERVICE_ERROR = 'NOTIFICATION_SERVICE_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',

  // System
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  CIRCUIT_BREAKER_OPEN = 'CIRCUIT_BREAKER_OPEN',

  // Recovery
  RETRY_EXHAUSTED = 'RETRY_EXHAUSTED',
  FALLBACK_FAILED = 'FALLBACK_FAILED'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',           // Minor issues that don't affect functionality
  MEDIUM = 'MEDIUM',     // Issues that may affect some functionality
  HIGH = 'HIGH',         // Issues that significantly impact functionality
  CRITICAL = 'CRITICAL'  // Issues that break core functionality
}

// Error categories for metrics and monitoring
export enum ErrorCategory {
  USER_ERROR = 'USER_ERROR',           // Errors caused by user input
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',   // Business rule violations
  SYSTEM_ERROR = 'SYSTEM_ERROR',       // Technical system errors
  EXTERNAL_ERROR = 'EXTERNAL_ERROR',   // External service errors
  SECURITY_ERROR = 'SECURITY_ERROR'    // Security-related errors
}

// Standard error interface
export interface StandardError {
  code: ErrorCode;
  message: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  details?: Record<string, any>;
  userMessage?: string;
  retryable: boolean;
  timestamp: Date;
  requestId?: string;
  userId?: number;
  context?: Record<string, any>;
}

// Base application error class
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly category: ErrorCategory;
  public readonly details: Record<string, any>;
  public readonly userMessage: string;
  public readonly retryable: boolean;
  public readonly timestamp: Date;
  public readonly requestId?: string;
  public readonly userId?: number;
  public readonly context: Record<string, any>;

  constructor(params: {
    code: ErrorCode;
    message: string;
    severity: ErrorSeverity;
    category: ErrorCategory;
    details?: Record<string, any>;
    userMessage?: string;
    retryable?: boolean;
    requestId?: string;
    userId?: number;
    context?: Record<string, any>;
    cause?: Error;
  }) {
    super(params.message);
    
    this.name = 'AppError';
    this.code = params.code;
    this.severity = params.severity;
    this.category = params.category;
    this.details = params.details || {};
    this.userMessage = params.userMessage || this.getDefaultUserMessage(params.code);
    this.retryable = params.retryable || false;
    this.timestamp = new Date();
    if (params.requestId !== undefined) {
      this.requestId = params.requestId;
    }
    if (params.userId !== undefined) {
      this.userId = params.userId;
    }
    this.context = params.context || {};

    // Maintain stack trace
    if (params.cause && params.cause.stack !== undefined) {
      this.stack = params.cause.stack;
    } else if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  private getDefaultUserMessage(code: ErrorCode): string {
    const defaultMessages: Record<ErrorCode, string> = {
      [ErrorCode.UNAUTHORIZED]: 'Authentication required. Please log in.',
      [ErrorCode.FORBIDDEN]: 'You do not have permission to perform this action.',
      [ErrorCode.TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
      [ErrorCode.INVALID_CREDENTIALS]: 'Invalid username or password.',
      [ErrorCode.VALIDATION_ERROR]: 'Please check your input and try again.',
      [ErrorCode.INVALID_INPUT]: 'Invalid input provided.',
      [ErrorCode.MISSING_REQUIRED_FIELD]: 'Required information is missing.',
      [ErrorCode.BOOKING_CONFLICT]: 'This time slot is no longer available.',
      [ErrorCode.INSUFFICIENT_AVAILABILITY]: 'Insufficient availability for your request.',
      [ErrorCode.PAYMENT_FAILED]: 'Payment processing failed. Please try again.',
      [ErrorCode.DUPLICATE_RESOURCE]: 'This resource already exists.',
      [ErrorCode.RESOURCE_LIMIT_EXCEEDED]: 'Resource limit exceeded.',
      [ErrorCode.DATABASE_CONNECTION_ERROR]: 'Service temporarily unavailable. Please try again.',
      [ErrorCode.DATABASE_TIMEOUT]: 'Request timeout. Please try again.',
      [ErrorCode.DATABASE_CONSTRAINT_VIOLATION]: 'Data constraint violation.',
      [ErrorCode.RESOURCE_NOT_FOUND]: 'Requested resource not found.',
      [ErrorCode.EXTERNAL_SERVICE_ERROR]: 'External service error. Please try again.',
      [ErrorCode.PAYMENT_GATEWAY_ERROR]: 'Payment gateway error. Please try again.',
      [ErrorCode.NOTIFICATION_SERVICE_ERROR]: 'Notification service error.',
      [ErrorCode.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please wait and try again.',
      [ErrorCode.INTERNAL_SERVER_ERROR]: 'Internal server error. Please try again.',
      [ErrorCode.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable.',
      [ErrorCode.CONFIGURATION_ERROR]: 'Configuration error.',
      [ErrorCode.CIRCUIT_BREAKER_OPEN]: 'Service temporarily unavailable.',
      [ErrorCode.RETRY_EXHAUSTED]: 'Operation failed after multiple attempts.',
      [ErrorCode.FALLBACK_FAILED]: 'All fallback options failed.'
    };

    return defaultMessages[code] || 'An unexpected error occurred.';
  }

  toJSON(): StandardError {
    const result: any = {
      code: this.code,
      message: this.message,
      severity: this.severity,
      category: this.category,
      details: this.details,
      userMessage: this.userMessage,
      retryable: this.retryable,
      timestamp: this.timestamp,
      context: this.context
    };
    
    if (this.requestId !== undefined) {
      result.requestId = this.requestId;
    }
    
    if (this.userId !== undefined) {
      result.userId = this.userId;
    }
    
    return result;
  }
}

// Specific error classes
export class ValidationError extends AppError {
  constructor(message: string, details?: Record<string, any>, requestId?: string) {
    const params: any = {
      code: ErrorCode.VALIDATION_ERROR,
      message,
      severity: ErrorSeverity.LOW,
      category: ErrorCategory.USER_ERROR,
      retryable: false
    };
    
    if (details !== undefined) params.details = details;
    if (requestId !== undefined) params.requestId = requestId;
    
    super(params);
  }
}

export class BookingConflictError extends AppError {
  constructor(
    message: string, 
    conflictingBookings?: any[], 
    suggestedAlternatives?: Date[], 
    requestId?: string
  ) {
    const params: any = {
      code: ErrorCode.BOOKING_CONFLICT,
      message,
      severity: ErrorSeverity.MEDIUM,
      category: ErrorCategory.BUSINESS_LOGIC,
      details: {
        conflictingBookings,
        suggestedAlternatives
      },
      retryable: false
    };
    
    if (requestId !== undefined) params.requestId = requestId;
    
    super(params);
  }
}

export class PaymentError extends AppError {
  constructor(
    message: string, 
    paymentDetails?: Record<string, any>, 
    retryable: boolean = true,
    requestId?: string
  ) {
    const params: any = {
      code: ErrorCode.PAYMENT_FAILED,
      message,
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.EXTERNAL_ERROR,
      retryable
    };
    
    if (paymentDetails !== undefined) params.details = paymentDetails;
    if (requestId !== undefined) params.requestId = requestId;
    
    super(params);
  }
}

export class DatabaseError extends AppError {
  constructor(
    message: string, 
    operation?: string, 
    details?: Record<string, any>,
    requestId?: string
  ) {
    const params: any = {
      code: ErrorCode.DATABASE_CONNECTION_ERROR,
      message,
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.SYSTEM_ERROR,
      details: { operation, ...details },
      retryable: true
    };
    
    if (requestId !== undefined) params.requestId = requestId;
    
    super(params);
  }
}

export class ExternalServiceError extends AppError {
  constructor(
    message: string, 
    service: string,
    statusCode?: number,
    retryable: boolean = true,
    requestId?: string
  ) {
    const params: any = {
      code: ErrorCode.EXTERNAL_SERVICE_ERROR,
      message,
      severity: ErrorSeverity.MEDIUM,
      category: ErrorCategory.EXTERNAL_ERROR,
      details: { service, statusCode },
      retryable
    };
    
    if (requestId !== undefined) params.requestId = requestId;
    
    super(params);
  }
}

// Error handling service
export class StandardizedErrorService {
  /**
   * Convert unknown error to standardized AppError
   */
  static standardizeError(
    error: unknown, 
    context?: Record<string, any>,
    requestId?: string
  ): AppError {
    // Already a standardized error
    if (error instanceof AppError) {
      return error;
    }

    // Handle common error types
    if (error instanceof Error) {
      return this.categorizeError(error, context, requestId);
    }

    // Handle non-Error objects
    const message = typeof error === 'string' ? error : 'Unknown error occurred';
    const errorParams: any = {
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message,
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.SYSTEM_ERROR
    };
    
    if (context !== undefined) errorParams.context = context;
    if (requestId !== undefined) errorParams.requestId = requestId;
    
    return new AppError(errorParams);
  }

  /**
   * Categorize generic errors into appropriate AppError types
   */
  private static categorizeError(
    error: Error, 
    context?: Record<string, any>,
    requestId?: string
  ): AppError {
    const errorMessage = error.message.toLowerCase();
    const errorName = error.name.toLowerCase();

    // Database errors
    if (errorMessage.includes('database') || 
        errorMessage.includes('connection') ||
        errorMessage.includes('pool') ||
        errorName.includes('database')) {
      return new DatabaseError(error.message, undefined, { originalError: error.name }, requestId);
    }

    // Network/timeout errors
    if (errorMessage.includes('timeout') || 
        errorMessage.includes('network') ||
        errorName.includes('timeout')) {
      return new ExternalServiceError(error.message, 'unknown', undefined, true, requestId);
    }

    // Validation errors
    if (errorMessage.includes('validation') || 
        errorMessage.includes('invalid') ||
        errorName.includes('validation')) {
      return new ValidationError(error.message, { originalError: error.name }, requestId);
    }

    // Payment errors
    if (errorMessage.includes('payment') || 
        errorMessage.includes('razorpay') ||
        errorMessage.includes('gateway')) {
      return new PaymentError(error.message, { originalError: error.name }, true, requestId);
    }

    // Default to system error
    const errorParams: any = {
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message: error.message,
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.SYSTEM_ERROR,
      details: { originalError: error.name },
      cause: error
    };
    
    if (context !== undefined) errorParams.context = context;
    if (requestId !== undefined) errorParams.requestId = requestId;
    
    return new AppError(errorParams);
  }

  /**
   * Log error with appropriate level based on severity
   */
  static logError(error: AppError): void {
    const logData = {
      code: error.code,
      message: error.message,
      severity: error.severity,
      category: error.category,
      details: error.details,
      context: error.context,
      requestId: error.requestId,
      userId: error.userId,
      timestamp: error.timestamp
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error('Critical error occurred', new Error(error.message), 'error-handling', logData);
        break;
      case ErrorSeverity.HIGH:
        logger.error('High severity error', new Error(error.message), 'error-handling', logData);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn('Medium severity error', 'error-handling', logData);
        break;
      case ErrorSeverity.LOW:
        logger.info('Low severity error', 'error-handling', logData);
        break;
    }
  }

  /**
   * Format error for API response
   */
  static formatForResponse(error: AppError): {
    error: {
      code: string;
      message: string;
      details?: Record<string, any>;
      requestId?: string;
    };
  } {
    const errorResponse: any = {
      code: error.code,
      message: error.userMessage
    };
    
    if (error.retryable) {
      errorResponse.details = { retryable: true };
    }
    
    if (error.requestId !== undefined) {
      errorResponse.requestId = error.requestId;
    }
    
    return { error: errorResponse };
  }

  /**
   * Check if error should trigger alert/notification
   */
  static shouldAlert(error: AppError): boolean {
    // Alert on critical errors or high severity system errors
    return error.severity === ErrorSeverity.CRITICAL ||
           (error.severity === ErrorSeverity.HIGH && 
            error.category === ErrorCategory.SYSTEM_ERROR);
  }

  /**
   * Get error metrics data
   */
  static getMetricsData(error: AppError): Record<string, any> {
    return {
      error_code: error.code,
      error_severity: error.severity,
      error_category: error.category,
      retryable: error.retryable,
      user_id: error.userId ? error.userId.toString() : 'anonymous'
    };
  }
}