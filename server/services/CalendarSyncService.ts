import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarBookings, 
  calendarSyncStatus, 
  users, 
  properties,
  CalendarBooking,
  CalendarSyncStatus,
  InsertCalendarBooking
} from '@shared/schema';
import { eq, and, desc, gte, lte, isNull } from 'drizzle-orm';
import { google } from 'googleapis';
import { EventEmitter } from 'events';

export interface ExternalCalendarProvider {
  provider: 'google' | 'outlook' | 'apple' | 'ical';
  credentials?: any;
  calendarId?: string;
  syncToken?: string;
  lastSync?: Date;
}

export interface SyncConflict {
  id: string;
  type: 'duplicate' | 'overlap' | 'modification';
  localBooking?: CalendarBooking;
  externalEvent?: any;
  suggestedResolution: 'keep_local' | 'keep_external' | 'merge' | 'manual';
  details: string;
}

export interface SyncResult {
  success: boolean;
  imported: number;
  exported: number;
  conflicts: SyncConflict[];
  errors: string[];
  lastSyncTime: Date;
}

export class CalendarSyncService extends EventEmitter {
  private syncInProgress = new Map<number, boolean>();
  
  constructor() {
    super();
    logger.info('Calendar Sync Service initialized');
  }

  /**
   * Two-way sync with external calendar providers
   */
  async syncCalendar(
    propertyId: number, 
    provider: ExternalCalendarProvider
  ): Promise<SyncResult> {
    if (this.syncInProgress.get(propertyId)) {
      throw new Error(`Sync already in progress for property ${propertyId}`);
    }

    this.syncInProgress.set(propertyId, true);
    
    try {
      logger.info('Starting calendar sync', {
        propertyId,
        provider: provider.provider
      });

      this.emit('syncStarted', { propertyId, provider: provider.provider });

      const result: SyncResult = {
        success: false,
        imported: 0,
        exported: 0,
        conflicts: [],
        errors: [],
        lastSyncTime: new Date()
      };

      // Get current sync status
      const syncStatus = await this.getSyncStatus(propertyId, provider.provider);
      
      switch (provider.provider) {
        case 'google':
          return await this.syncGoogleCalendar(propertyId, provider, syncStatus, result);
        case 'outlook':
          return await this.syncOutlookCalendar(propertyId, provider, syncStatus, result);
        case 'ical':
          return await this.syncICalCalendar(propertyId, provider, syncStatus, result);
        default:
          throw new Error(`Unsupported provider: ${provider.provider}`);
      }
    } catch (error) {
      logger.error('Calendar sync failed', error as Error, {
        propertyId,
        provider: provider.provider
      });
      throw error;
    } finally {
      this.syncInProgress.set(propertyId, false);
    }
  }

  /**
   * Google Calendar sync implementation
   */
  private async syncGoogleCalendar(
    propertyId: number,
    provider: ExternalCalendarProvider,
    syncStatus: CalendarSyncStatus | null,
    result: SyncResult
  ): Promise<SyncResult> {
    try {
      const auth = new google.auth.OAuth2();
      auth.setCredentials(provider.credentials);
      
      const calendar = google.calendar({ version: 'v3', auth });

      // Import from Google Calendar
      const importResult = await this.importFromGoogleCalendar(
        calendar, 
        propertyId, 
        provider.calendarId!, 
        syncStatus?.lastSync
      );
      
      result.imported = importResult.imported;
      result.conflicts.push(...importResult.conflicts);

      // Export to Google Calendar
      const exportResult = await this.exportToGoogleCalendar(
        calendar,
        propertyId,
        provider.calendarId!,
        syncStatus?.lastSync
      );
      
      result.exported = exportResult.exported;
      result.conflicts.push(...exportResult.conflicts);

      // Update sync status
      await this.updateSyncStatus(propertyId, provider.provider, {
        lastSync: new Date(),
        syncToken: exportResult.syncToken,
        status: 'completed',
        conflicts: result.conflicts.length
      });

      result.success = true;
      this.emit('syncCompleted', { propertyId, result });
      
      return result;
    } catch (error) {
      result.errors.push(`Google Calendar sync failed: ${(error as Error).message}`);
      await this.updateSyncStatus(propertyId, provider.provider, {
        status: 'failed',
        lastError: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Import bookings from Google Calendar
   */
  private async importFromGoogleCalendar(
    calendar: any,
    propertyId: number,
    calendarId: string,
    lastSync?: Date
  ): Promise<{ imported: number; conflicts: SyncConflict[] }> {
    const conflicts: SyncConflict[] = [];
    let imported = 0;

    try {
      const timeMin = lastSync || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const timeMax = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year ahead

      const response = await calendar.events.list({
        calendarId,
        timeMin: timeMin.toISOString(),
        timeMax: timeMax.toISOString(),
        singleEvents: true,
        orderBy: 'startTime'
      });

      const events = response.data.items || [];

      for (const event of events) {
        if (!event.start?.date && !event.start?.dateTime) continue;

        const startDate = event.start.date || event.start.dateTime.split('T')[0];
        const endDate = event.end?.date || event.end?.dateTime.split('T')[0] || startDate;

        // Check for existing booking
        const existingBooking = await db
          .select()
          .from(calendarBookings)
          .where(and(
            eq(calendarBookings.propertyId, propertyId),
            eq(calendarBookings.externalId, event.id!),
            eq(calendarBookings.source, 'google')
          ))
          .limit(1);

        if (existingBooking.length > 0) {
          // Check for modifications
          const existing = existingBooking[0];
          if (existing.updatedAt < new Date(event.updated!)) {
            conflicts.push({
              id: event.id!,
              type: 'modification',
              localBooking: existing,
              externalEvent: event,
              suggestedResolution: 'keep_external',
              details: `Event modified in Google Calendar: ${event.summary}`
            });
          }
          continue;
        }

        // Check for conflicts with existing bookings
        const conflictingBookings = await db
          .select()
          .from(calendarBookings)
          .where(and(
            eq(calendarBookings.propertyId, propertyId),
            gte(calendarBookings.endDate, startDate),
            lte(calendarBookings.startDate, endDate)
          ));

        if (conflictingBookings.length > 0) {
          conflicts.push({
            id: event.id!,
            type: 'overlap',
            externalEvent: event,
            suggestedResolution: 'manual',
            details: `Overlaps with ${conflictingBookings.length} existing booking(s)`
          });
          continue;
        }

        // Import the event
        const bookingData: InsertCalendarBooking = {
          propertyId,
          startDate,
          endDate,
          guestName: this.extractGuestName(event.summary || 'External Booking'),
          guestPhone: this.extractPhoneFromDescription(event.description),
          guestCount: this.extractGuestCount(event.description) || 2,
          bookingType: this.determineBookingType(event),
          status: 'confirmed',
          source: 'google',
          externalId: event.id!,
          notes: event.description || '',
          createdBy: null // External booking
        };

        await db.insert(calendarBookings).values(bookingData);
        imported++;

        logger.info('Imported Google Calendar event', {
          propertyId,
          eventId: event.id,
          summary: event.summary
        });
      }

      return { imported, conflicts };
    } catch (error) {
      logger.error('Google Calendar import failed', error as Error);
      throw error;
    }
  }

  /**
   * Export bookings to Google Calendar
   */
  private async exportToGoogleCalendar(
    calendar: any,
    propertyId: number,
    calendarId: string,
    lastSync?: Date
  ): Promise<{ exported: number; conflicts: SyncConflict[]; syncToken?: string }> {
    const conflicts: SyncConflict[] = [];
    let exported = 0;

    try {
      // Get bookings created/updated since last sync
      const bookingsQuery = db
        .select()
        .from(calendarBookings)
        .where(and(
          eq(calendarBookings.propertyId, propertyId),
          lastSync ? gte(calendarBookings.updatedAt, lastSync) : undefined,
          isNull(calendarBookings.externalId) // Only export bookings not from external sources
        ));

      const bookings = await bookingsQuery;

      for (const booking of bookings) {
        try {
          const event = {
            summary: `${booking.guestName} - ${booking.bookingType}`,
            description: this.buildEventDescription(booking),
            start: {
              date: booking.startDate
            },
            end: {
              date: booking.endDate === booking.startDate 
                ? this.getNextDay(booking.endDate)
                : booking.endDate
            },
            status: booking.status === 'confirmed' ? 'confirmed' : 'tentative'
          };

          const response = await calendar.events.insert({
            calendarId,
            requestBody: event
          });

          // Update booking with external ID
          await db
            .update(calendarBookings)
            .set({ externalId: response.data.id })
            .where(eq(calendarBookings.id, booking.id));

          exported++;

          logger.info('Exported booking to Google Calendar', {
            bookingId: booking.id,
            eventId: response.data.id
          });
        } catch (error) {
          conflicts.push({
            id: booking.id.toString(),
            type: 'duplicate',
            localBooking: booking,
            suggestedResolution: 'keep_local',
            details: `Failed to export: ${(error as Error).message}`
          });
        }
      }

      return { exported, conflicts };
    } catch (error) {
      logger.error('Google Calendar export failed', error as Error);
      throw error;
    }
  }

  /**
   * Outlook Calendar sync (placeholder implementation)
   */
  private async syncOutlookCalendar(
    propertyId: number,
    provider: ExternalCalendarProvider,
    syncStatus: CalendarSyncStatus | null,
    result: SyncResult
  ): Promise<SyncResult> {
    // Microsoft Graph API implementation would go here
    result.errors.push('Outlook sync not yet implemented');
    return result;
  }

  /**
   * iCal sync implementation
   */
  private async syncICalCalendar(
    propertyId: number,
    provider: ExternalCalendarProvider,
    syncStatus: CalendarSyncStatus | null,
    result: SyncResult
  ): Promise<SyncResult> {
    // iCal parsing and sync implementation would go here
    result.errors.push('iCal sync not yet implemented');
    return result;
  }

  /**
   * Get sync status for a property and provider
   */
  private async getSyncStatus(
    propertyId: number, 
    provider: string
  ): Promise<CalendarSyncStatus | null> {
    const status = await db
      .select()
      .from(calendarSyncStatus)
      .where(and(
        eq(calendarSyncStatus.propertyId, propertyId),
        eq(calendarSyncStatus.provider, provider)
      ))
      .limit(1);

    return status[0] || null;
  }

  /**
   * Update sync status
   */
  private async updateSyncStatus(
    propertyId: number,
    provider: string,
    updates: Partial<{
      lastSync: Date;
      syncToken: string;
      status: string;
      lastError: string;
      conflicts: number;
    }>
  ): Promise<void> {
    const existing = await this.getSyncStatus(propertyId, provider);

    if (existing) {
      await db
        .update(calendarSyncStatus)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(calendarSyncStatus.id, existing.id));
    } else {
      await db.insert(calendarSyncStatus).values({
        propertyId,
        provider,
        lastSync: updates.lastSync || new Date(),
        status: updates.status || 'active',
        syncToken: updates.syncToken,
        lastError: updates.lastError,
        conflictCount: updates.conflicts || 0,
        updatedAt: new Date()
      });
    }
  }

  /**
   * Utility methods for parsing event data
   */
  private extractGuestName(summary: string): string {
    const match = summary.match(/^([^-]+)/);
    return match ? match[1].trim() : 'External Guest';
  }

  private extractPhoneFromDescription(description?: string): string | null {
    if (!description) return null;
    const phoneMatch = description.match(/(\+?\d{10,15})/);
    return phoneMatch ? phoneMatch[1] : null;
  }

  private extractGuestCount(description?: string): number | null {
    if (!description) return null;
    const guestMatch = description.match(/(\d+)\s*guest/i);
    return guestMatch ? parseInt(guestMatch[1]) : null;
  }

  private determineBookingType(event: any): string {
    const summary = event.summary?.toLowerCase() || '';
    if (summary.includes('morning') || summary.includes('half')) {
      return 'Morning Visit';
    }
    return 'Full Day';
  }

  private buildEventDescription(booking: CalendarBooking): string {
    return `
Booking Details:
- Guest: ${booking.guestName}
- Phone: ${booking.guestPhone || 'Not provided'}
- Guests: ${booking.guestCount}
- Type: ${booking.bookingType}
- Status: ${booking.status}
${booking.notes ? `- Notes: ${booking.notes}` : ''}

Generated by BookAFarm Calendar System
    `.trim();
  }

  private getNextDay(date: string): string {
    const d = new Date(date);
    d.setDate(d.getDate() + 1);
    return d.toISOString().split('T')[0];
  }

  /**
   * Get all sync statuses for monitoring
   */
  async getAllSyncStatuses(): Promise<(CalendarSyncStatus & { propertyTitle: string })[]> {
    return await db
      .select({
        ...calendarSyncStatus,
        propertyTitle: properties.title
      })
      .from(calendarSyncStatus)
      .leftJoin(properties, eq(properties.id, calendarSyncStatus.propertyId));
  }

  /**
   * Resolve sync conflicts
   */
  async resolveConflict(
    conflictId: string, 
    resolution: 'keep_local' | 'keep_external' | 'merge'
  ): Promise<void> {
    // Implementation for conflict resolution would go here
    logger.info('Resolving sync conflict', { conflictId, resolution });
  }
}

// Export singleton instance
export const calendarSyncService = new CalendarSyncService();