import { db } from "../db";
import { 
  paymentRoles, 
  userPaymentRoles, 
  users,
  PaymentRole,
  UserPaymentRole,
  InsertPaymentRole,
  InsertUserPaymentRole 
} from "../../shared/schema";
import { eq, and } from "drizzle-orm";
import { auditLogger } from "./AuditLogger";

interface PaymentPermissions {
  canViewPayments: boolean;
  canCreatePayments: boolean;
  canCapturePayments: boolean;
  canRefundPayments: boolean;
  canProcessWebhooks: boolean;
  canViewAuditLogs: boolean;
  canManageRoles: boolean;
  canViewFinancialReports: boolean;
  canProcessManualPayments: boolean;
  canAccessPaymentData: boolean;
  canManageGSTSettings: boolean;
  canViewCustomerData: boolean;
  canExportPaymentData: boolean;
  canConfigurePaymentMethods: boolean;
  canManageRefundPolicies: boolean;
}

interface AuthorizationContext {
  userId: number;
  userRole: string;
  ipAddress?: string;
  userAgent?: string;
  requestPath?: string;
  requestMethod?: string;
}

export class PaymentAuthorizationService {
  private static instance: PaymentAuthorizationService;
  private roleCache: Map<string, PaymentRole> = new Map();
  private userRoleCache: Map<number, PaymentPermissions> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly cacheTimeout = 15 * 60 * 1000; // 15 minutes

  private constructor() {
    // Initialize default roles
    this.initializeDefaultRoles();
  }

  public static getInstance(): PaymentAuthorizationService {
    if (!PaymentAuthorizationService.instance) {
      PaymentAuthorizationService.instance = new PaymentAuthorizationService();
    }
    return PaymentAuthorizationService.instance;
  }

  // Check if user has specific permission
  async checkPermission(
    userId: number, 
    permission: keyof PaymentPermissions, 
    context?: AuthorizationContext
  ): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(userId);
      const hasPermission = permissions[permission];

      // Log authorization check
      await auditLogger.logPaymentAction('authorization_check', {
        actorType: 'system',
        actorId: userId,
        actorIp: context?.ipAddress || 'unknown',
        actorUserAgent: context?.userAgent || 'unknown',
        metadata: {
          permission,
          granted: hasPermission,
          requestPath: context?.requestPath,
          requestMethod: context?.requestMethod
        }
      });

      // Log unauthorized access attempts
      if (!hasPermission) {
        await auditLogger.logSuspiciousActivity(
          `Unauthorized access attempt: ${permission}`,
          context?.ipAddress || 'unknown',
          userId,
          {
            permission,
            requestPath: context?.requestPath,
            requestMethod: context?.requestMethod
          }
        );
      }

      return hasPermission;
    } catch (error) {
      console.error('Authorization check failed:', error);
      // Fail secure - deny access on error
      return false;
    }
  }

  // Get all permissions for a user
  async getUserPermissions(userId: number): Promise<PaymentPermissions> {
    try {
      // Check cache first
      if (this.userRoleCache.has(userId)) {
        const cacheKey = `user_${userId}`;
        const expiry = this.cacheExpiry.get(cacheKey);
        if (expiry && expiry > Date.now()) {
          return this.userRoleCache.get(userId)!;
        }
      }

      // Query user roles
      const userRoles = await db
        .select({
          role: paymentRoles,
          userRole: userPaymentRoles
        })
        .from(userPaymentRoles)
        .innerJoin(paymentRoles, eq(userPaymentRoles.roleId, paymentRoles.id))
        .where(and(
          eq(userPaymentRoles.userId, userId),
          eq(userPaymentRoles.isActive, true)
        ));

      // Merge permissions from all roles
      const permissions: PaymentPermissions = {
        canViewPayments: false,
        canCreatePayments: false,
        canCapturePayments: false,
        canRefundPayments: false,
        canProcessWebhooks: false,
        canViewAuditLogs: false,
        canManageRoles: false,
        canViewFinancialReports: false,
        canProcessManualPayments: false,
        canAccessPaymentData: false,
        canManageGSTSettings: false,
        canViewCustomerData: false,
        canExportPaymentData: false,
        canConfigurePaymentMethods: false,
        canManageRefundPolicies: false,
      };

      // Combine permissions from all roles
      for (const { role } of userRoles) {
        const rolePermissions = role.permissions as PaymentPermissions;
        for (const [key, value] of Object.entries(rolePermissions)) {
          if (value === true) {
            (permissions as any)[key] = true;
          }
        }
      }

      // Cache the result
      this.userRoleCache.set(userId, permissions);
      this.cacheExpiry.set(`user_${userId}`, Date.now() + this.cacheTimeout);

      return permissions;
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      // Return minimal permissions on error
      return this.getMinimalPermissions();
    }
  }

  // Create a new payment role
  async createRole(
    name: string, 
    description: string, 
    permissions: PaymentPermissions,
    createdBy: number
  ): Promise<PaymentRole> {
    try {
      const roleData: InsertPaymentRole = {
        name,
        description,
        permissions: permissions as any,
      };

      const [newRole] = await db
        .insert(paymentRoles)
        .values(roleData)
        .returning();

      // Log role creation
      await auditLogger.logPaymentAction('role_created', {
        actorType: 'admin',
        actorId: createdBy,
        metadata: {
          roleName: name,
          permissions
        }
      });

      console.log(`Created new payment role: ${name}`);
      return newRole;
    } catch (error) {
      console.error('Failed to create payment role:', error);
      throw new Error('Role creation failed');
    }
  }

  // Assign role to user
  async assignRoleToUser(
    userId: number, 
    roleId: number, 
    grantedBy: number,
    expiresAt?: Date
  ): Promise<void> {
    try {
      const userRoleData: InsertUserPaymentRole = {
        userId,
        roleId,
        grantedBy,
        expiresAt,
      };

      await db
        .insert(userPaymentRoles)
        .values(userRoleData);

      // Clear user cache
      this.userRoleCache.delete(userId);
      this.cacheExpiry.delete(`user_${userId}`);

      // Log role assignment
      await auditLogger.logPaymentAction('role_assigned', {
        actorType: 'admin',
        actorId: grantedBy,
        metadata: {
          targetUserId: userId,
          roleId,
          expiresAt: expiresAt?.toISOString()
        }
      });

      console.log(`Assigned role ${roleId} to user ${userId}`);
    } catch (error) {
      console.error('Failed to assign role to user:', error);
      throw new Error('Role assignment failed');
    }
  }

  // Remove role from user
  async removeRoleFromUser(
    userId: number, 
    roleId: number, 
    removedBy: number
  ): Promise<void> {
    try {
      await db
        .update(userPaymentRoles)
        .set({ isActive: false })
        .where(and(
          eq(userPaymentRoles.userId, userId),
          eq(userPaymentRoles.roleId, roleId)
        ));

      // Clear user cache
      this.userRoleCache.delete(userId);
      this.cacheExpiry.delete(`user_${userId}`);

      // Log role removal
      await auditLogger.logPaymentAction('role_removed', {
        actorType: 'admin',
        actorId: removedBy,
        metadata: {
          targetUserId: userId,
          roleId
        }
      });

      console.log(`Removed role ${roleId} from user ${userId}`);
    } catch (error) {
      console.error('Failed to remove role from user:', error);
      throw new Error('Role removal failed');
    }
  }

  // Get user's roles
  async getUserRoles(userId: number): Promise<PaymentRole[]> {
    try {
      const userRoles = await db
        .select({
          role: paymentRoles
        })
        .from(userPaymentRoles)
        .innerJoin(paymentRoles, eq(userPaymentRoles.roleId, paymentRoles.id))
        .where(and(
          eq(userPaymentRoles.userId, userId),
          eq(userPaymentRoles.isActive, true)
        ));

      return userRoles.map(ur => ur.role);
    } catch (error) {
      console.error('Failed to get user roles:', error);
      return [];
    }
  }

  // Authorization middleware for Express routes
  authorize(permission: keyof PaymentPermissions) {
    return async (req: any, res: any, next: any) => {
      try {
        const userId = req.user?.userId;
        if (!userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const context: AuthorizationContext = {
          userId,
          userRole: req.user?.role,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          requestPath: req.path,
          requestMethod: req.method
        };

        const hasPermission = await this.checkPermission(userId, permission, context);
        
        if (!hasPermission) {
          return res.status(403).json({ 
            error: 'Insufficient permissions',
            required: permission
          });
        }

        next();
      } catch (error) {
        console.error('Authorization middleware error:', error);
        return res.status(500).json({ error: 'Authorization check failed' });
      }
    };
  }

  // Multi-permission check
  async checkMultiplePermissions(
    userId: number, 
    permissions: (keyof PaymentPermissions)[], 
    requireAll: boolean = true
  ): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      if (requireAll) {
        return permissions.every(permission => userPermissions[permission]);
      } else {
        return permissions.some(permission => userPermissions[permission]);
      }
    } catch (error) {
      console.error('Multiple permissions check failed:', error);
      return false;
    }
  }

  // Clear user cache
  clearUserCache(userId: number): void {
    this.userRoleCache.delete(userId);
    this.cacheExpiry.delete(`user_${userId}`);
  }

  // Clear all caches
  clearAllCaches(): void {
    this.roleCache.clear();
    this.userRoleCache.clear();
    this.cacheExpiry.clear();
  }

  private async initializeDefaultRoles(): Promise<void> {
    try {
      // Check if default roles exist
      const existingRoles = await db
        .select()
        .from(paymentRoles)
        .where(eq(paymentRoles.name, 'payment_admin'));

      if (existingRoles.length === 0) {
        // Create default roles
        await this.createDefaultRoles();
      }
    } catch (error) {
      console.error('Failed to initialize default roles:', error);
    }
  }

  private async createDefaultRoles(): Promise<void> {
    const defaultRoles = [
      {
        name: 'payment_admin',
        description: 'Full payment system administration',
        permissions: {
          canViewPayments: true,
          canCreatePayments: true,
          canCapturePayments: true,
          canRefundPayments: true,
          canProcessWebhooks: true,
          canViewAuditLogs: true,
          canManageRoles: true,
          canViewFinancialReports: true,
          canProcessManualPayments: true,
          canAccessPaymentData: true,
          canManageGSTSettings: true,
          canViewCustomerData: true,
          canExportPaymentData: true,
          canConfigurePaymentMethods: true,
          canManageRefundPolicies: true,
        }
      },
      {
        name: 'payment_operator',
        description: 'Payment processing and customer support',
        permissions: {
          canViewPayments: true,
          canCreatePayments: true,
          canCapturePayments: true,
          canRefundPayments: true,
          canProcessWebhooks: false,
          canViewAuditLogs: true,
          canManageRoles: false,
          canViewFinancialReports: true,
          canProcessManualPayments: false,
          canAccessPaymentData: true,
          canManageGSTSettings: false,
          canViewCustomerData: true,
          canExportPaymentData: false,
          canConfigurePaymentMethods: false,
          canManageRefundPolicies: false,
        }
      },
      {
        name: 'payment_viewer',
        description: 'Read-only access to payment data',
        permissions: {
          canViewPayments: true,
          canCreatePayments: false,
          canCapturePayments: false,
          canRefundPayments: false,
          canProcessWebhooks: false,
          canViewAuditLogs: false,
          canManageRoles: false,
          canViewFinancialReports: true,
          canProcessManualPayments: false,
          canAccessPaymentData: true,
          canManageGSTSettings: false,
          canViewCustomerData: true,
          canExportPaymentData: false,
          canConfigurePaymentMethods: false,
          canManageRefundPolicies: false,
        }
      }
    ];

    for (const role of defaultRoles) {
      await db
        .insert(paymentRoles)
        .values(role)
        .onConflictDoNothing();
    }

    console.log('Default payment roles created');
  }

  private getMinimalPermissions(): PaymentPermissions {
    return {
      canViewPayments: false,
      canCreatePayments: false,
      canCapturePayments: false,
      canRefundPayments: false,
      canProcessWebhooks: false,
      canViewAuditLogs: false,
      canManageRoles: false,
      canViewFinancialReports: false,
      canProcessManualPayments: false,
      canAccessPaymentData: false,
      canManageGSTSettings: false,
      canViewCustomerData: false,
      canExportPaymentData: false,
      canConfigurePaymentMethods: false,
      canManageRefundPolicies: false,
    };
  }
}

// Export singleton instance
export const paymentAuthorizationService = PaymentAuthorizationService.getInstance();