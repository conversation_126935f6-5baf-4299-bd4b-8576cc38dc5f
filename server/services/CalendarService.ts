import { db } from '../utils/database';
import { 
  calendarBookings, 
  calendarSyncStatus,
  properties,
  users,
  CalendarBooking,
  CalendarSyncStatus,
  InsertCalendarBooking,
  InsertCalendarSyncStatus,
  insertCalendarBookingSchema,
  insertCalendarSyncStatusSchema
} from '../../shared/schema';
import { eq, and, gte, lte, desc, asc, or, sql, count } from 'drizzle-orm';
import { logger } from '../utils/structured-logger';
import { webSocketService } from './WebSocketService';
import { z } from 'zod';

export interface CalendarBookingWithDetails extends CalendarBooking {
  property: {
    id: number;
    title: string;
    location: string;
    ownerId: number;
  } | null;
  createdByUser: {
    id: number;
    fullName: string;
    email: string;
  } | null;
}

export interface CalendarFilters {
  propertyId?: number;
  startDate?: string;
  endDate?: string;
  status?: string[];
  source?: string[];
  bookingType?: string[];
  limit?: number;
  offset?: number;
}

export interface AvailabilityCheck {
  isAvailable: boolean;
  conflictingBookings: CalendarBooking[];
  message?: string;
}

export interface SyncStatusWithProperty extends CalendarSyncStatus {
  property: {
    id: number;
    title: string;
    ownerId: number;
  } | null;
}

export class CalendarService {
  
  // Calendar Bookings CRUD Operations
  
  /**
   * Create a new calendar booking
   */
  async createCalendarBooking(data: InsertCalendarBooking): Promise<CalendarBooking> {
    try {
      // Validate input data
      const validatedData = insertCalendarBookingSchema.parse(data);
      
      logger.info('Creating calendar booking', { 
        component: 'CalendarService',
        propertyId: validatedData.propertyId,
        startDate: validatedData.startDate,
        endDate: validatedData.endDate
      });
      
      // Check for conflicts before creating
      const conflicts = await this.checkAvailability(
        validatedData.propertyId,
        validatedData.startDate,
        validatedData.endDate
      );
      
      if (!conflicts.isAvailable && validatedData.status !== 'blocked') {
        throw new Error(`Booking conflicts detected: ${conflicts.message}`);
      }
      
      // Insert the booking
      const [booking] = await db
        .insert(calendarBookings)
        .values({
          ...validatedData,
          updatedAt: new Date()
        })
        .returning();
      
      logger.info('Calendar booking created successfully', {
        component: 'CalendarService',
        bookingId: booking.id,
        propertyId: booking.propertyId
      });

      // Broadcast WebSocket update
      webSocketService.broadcastCalendarUpdate(
        booking.propertyId,
        booking,
        validatedData.createdBy
      );
      
      return booking;
      
    } catch (error) {
      logger.error('Failed to create calendar booking', {
        component: 'CalendarService',
        propertyId: data.propertyId
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Get calendar bookings with optional filters
   */
  async getCalendarBookings(filters: CalendarFilters = {}): Promise<CalendarBookingWithDetails[]> {
    try {
      logger.debug('Fetching calendar bookings', {
        component: 'CalendarService',
        filters
      });
      
      // Build conditions
      const conditions = [];
      
      if (filters.propertyId) {
        conditions.push(eq(calendarBookings.propertyId, filters.propertyId));
      }
      
      if (filters.startDate) {
        conditions.push(gte(calendarBookings.startDate, filters.startDate));
      }
      
      if (filters.endDate) {
        conditions.push(lte(calendarBookings.endDate, filters.endDate));
      }
      
      if (filters.status && filters.status.length > 0) {
        conditions.push(
          or(...filters.status.map(status => eq(calendarBookings.status, status)))
        );
      }
      
      if (filters.source && filters.source.length > 0) {
        conditions.push(
          or(...filters.source.map(source => eq(calendarBookings.source, source)))
        );
      }
      
      if (filters.bookingType && filters.bookingType.length > 0) {
        conditions.push(
          or(...filters.bookingType.map(type => eq(calendarBookings.bookingType, type)))
        );
      }
      
      // Build base query with all parts
      let query = db
        .select({
          // Calendar booking fields
          id: calendarBookings.id,
          propertyId: calendarBookings.propertyId,
          startDate: calendarBookings.startDate,
          endDate: calendarBookings.endDate,
          status: calendarBookings.status,
          bookingType: calendarBookings.bookingType,
          guestName: calendarBookings.guestName,
          guestPhone: calendarBookings.guestPhone,
          guestCount: calendarBookings.guestCount,
          notes: calendarBookings.notes,
          source: calendarBookings.source,
          externalId: calendarBookings.externalId,
          createdBy: calendarBookings.createdBy,
          createdAt: calendarBookings.createdAt,
          updatedAt: calendarBookings.updatedAt,
          // Property details
          property: {
            id: properties.id,
            title: properties.title,
            location: properties.location,
            ownerId: properties.ownerId
          },
          // Created by user details
          createdByUser: {
            id: users.id,
            fullName: users.fullName,
            email: users.email
          }
        })
        .from(calendarBookings)
        .leftJoin(properties, eq(calendarBookings.propertyId, properties.id))
        .leftJoin(users, eq(calendarBookings.createdBy, users.id));

      // Add where conditions if any
      if (conditions.length > 0) {
        query = query.where(and(...conditions)) as any;
      }

      // Add ordering
      query = (query as any).orderBy(desc(calendarBookings.startDate), asc(calendarBookings.createdAt));

      // Add pagination
      if (filters.limit) {
        query = (query as any).limit(filters.limit);
      }
      
      if (filters.offset) {
        query = (query as any).offset(filters.offset);
      }
      
      const bookings = await query;
      
      logger.debug('Calendar bookings fetched successfully', {
        component: 'CalendarService',
        count: bookings.length
      });
      
      return bookings;
      
    } catch (error) {
      logger.error('Failed to fetch calendar bookings', {
        component: 'CalendarService',
        filters
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Get calendar bookings for a specific property in a date range
   */
  async getPropertyCalendar(
    propertyId: number, 
    startDate: string, 
    endDate: string
  ): Promise<CalendarBooking[]> {
    try {
      logger.debug('Fetching property calendar', {
        component: 'CalendarService',
        propertyId,
        startDate,
        endDate
      });
      
      const bookings = await db
        .select()
        .from(calendarBookings)
        .where(
          and(
            eq(calendarBookings.propertyId, propertyId),
            // Get bookings that overlap with the requested date range
            or(
              and(
                gte(calendarBookings.startDate, startDate),
                lte(calendarBookings.startDate, endDate)
              ),
              and(
                gte(calendarBookings.endDate, startDate),
                lte(calendarBookings.endDate, endDate)
              ),
              and(
                lte(calendarBookings.startDate, startDate),
                gte(calendarBookings.endDate, endDate)
              )
            )
          )
        )
        .orderBy(asc(calendarBookings.startDate));
      
      logger.debug('Property calendar fetched successfully', {
        component: 'CalendarService',
        propertyId,
        bookingCount: bookings.length
      });
      
      return bookings;
      
    } catch (error) {
      logger.error('Failed to fetch property calendar', {
        component: 'CalendarService',
        propertyId,
        startDate,
        endDate
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Get a single calendar booking by ID
   */
  async getCalendarBookingById(id: number): Promise<CalendarBookingWithDetails | null> {
    try {
      logger.debug('Fetching calendar booking by ID', {
        component: 'CalendarService',
        bookingId: id
      });
      
      const [booking] = await db
        .select({
          // Calendar booking fields
          id: calendarBookings.id,
          propertyId: calendarBookings.propertyId,
          startDate: calendarBookings.startDate,
          endDate: calendarBookings.endDate,
          status: calendarBookings.status,
          bookingType: calendarBookings.bookingType,
          guestName: calendarBookings.guestName,
          guestPhone: calendarBookings.guestPhone,
          guestCount: calendarBookings.guestCount,
          notes: calendarBookings.notes,
          source: calendarBookings.source,
          externalId: calendarBookings.externalId,
          createdBy: calendarBookings.createdBy,
          createdAt: calendarBookings.createdAt,
          updatedAt: calendarBookings.updatedAt,
          // Property details
          property: {
            id: properties.id,
            title: properties.title,
            location: properties.location,
            ownerId: properties.ownerId
          },
          // Created by user details
          createdByUser: {
            id: users.id,
            fullName: users.fullName,
            email: users.email
          }
        })
        .from(calendarBookings)
        .leftJoin(properties, eq(calendarBookings.propertyId, properties.id))
        .leftJoin(users, eq(calendarBookings.createdBy, users.id))
        .where(eq(calendarBookings.id, id))
        .limit(1);
      
      if (!booking) {
        logger.warn('Calendar booking not found', {
          component: 'CalendarService',
          bookingId: id
        });
        return null;
      }
      
      return booking;
      
    } catch (error) {
      logger.error('Failed to fetch calendar booking by ID', {
        component: 'CalendarService',
        bookingId: id
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Update a calendar booking
   */
  async updateCalendarBooking(
    id: number, 
    data: Partial<InsertCalendarBooking>
  ): Promise<CalendarBooking | null> {
    try {
      logger.info('Updating calendar booking', {
        component: 'CalendarService',
        bookingId: id,
        updateFields: Object.keys(data)
      });
      
      // If dates are being updated, check for conflicts
      if (data.startDate || data.endDate) {
        const existingBooking = await this.getCalendarBookingById(id);
        if (!existingBooking) {
          throw new Error('Calendar booking not found');
        }
        
        const startDate = data.startDate || existingBooking.startDate;
        const endDate = data.endDate || existingBooking.endDate;
        
        const conflicts = await this.checkAvailability(
          existingBooking.propertyId,
          startDate,
          endDate,
          id // Exclude current booking from conflict check
        );
        
        if (!conflicts.isAvailable && data.status !== 'blocked') {
          throw new Error(`Date update conflicts detected: ${conflicts.message}`);
        }
      }
      
      // Update the booking
      const [updatedBooking] = await db
        .update(calendarBookings)
        .set({
          ...data,
          updatedAt: new Date()
        })
        .where(eq(calendarBookings.id, id))
        .returning();
      
      if (!updatedBooking) {
        logger.warn('Calendar booking not found for update', {
          component: 'CalendarService',
          bookingId: id
        });
        return null;
      }
      
      logger.info('Calendar booking updated successfully', {
        component: 'CalendarService',
        bookingId: id
      });

      // Broadcast WebSocket update
      webSocketService.broadcastCalendarUpdate(
        updatedBooking.propertyId,
        updatedBooking
      );
      
      return updatedBooking;
      
    } catch (error) {
      logger.error('Failed to update calendar booking', {
        component: 'CalendarService',
        bookingId: id
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Delete a calendar booking
   */
  async deleteCalendarBooking(id: number): Promise<boolean> {
    try {
      logger.info('Deleting calendar booking', {
        component: 'CalendarService',
        bookingId: id
      });
      
      // Get booking details before deletion for WebSocket broadcast
      const bookingToDelete = await this.getCalendarBookingById(id);
      if (!bookingToDelete) {
        logger.warn('Calendar booking not found for deletion', {
          component: 'CalendarService',
          bookingId: id
        });
        return false;
      }

      const [deletedBooking] = await db
        .delete(calendarBookings)
        .where(eq(calendarBookings.id, id))
        .returning({ id: calendarBookings.id, propertyId: calendarBookings.propertyId });
      
      if (!deletedBooking) {
        return false;
      }
      
      logger.info('Calendar booking deleted successfully', {
        component: 'CalendarService',
        bookingId: id
      });

      // Broadcast WebSocket update
      webSocketService.broadcastCalendarUpdate(
        deletedBooking.propertyId,
        { ...bookingToDelete, deleted: true }
      );
      
      return true;
      
    } catch (error) {
      logger.error('Failed to delete calendar booking', {
        component: 'CalendarService',
        bookingId: id
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Check availability for a date range
   */
  async checkAvailability(
    propertyId: number,
    startDate: string,
    endDate: string,
    excludeBookingId?: number
  ): Promise<AvailabilityCheck> {
    try {
      logger.debug('Checking availability', {
        component: 'CalendarService',
        propertyId,
        startDate,
        endDate,
        excludeBookingId
      });
      
      const baseConditions = [
        eq(calendarBookings.propertyId, propertyId),
        // Check for overlapping bookings
        or(
          and(
            gte(calendarBookings.startDate, startDate),
            lte(calendarBookings.startDate, endDate)
          ),
          and(
            gte(calendarBookings.endDate, startDate),
            lte(calendarBookings.endDate, endDate)
          ),
          and(
            lte(calendarBookings.startDate, startDate),
            gte(calendarBookings.endDate, endDate)
          )
        ),
        // Only consider active bookings
        or(
          eq(calendarBookings.status, 'confirmed'),
          eq(calendarBookings.status, 'tentative'),
          eq(calendarBookings.status, 'blocked')
        )
      ];

      // Add exclude condition if provided
      if (excludeBookingId) {
        baseConditions.push(sql`${calendarBookings.id} != ${excludeBookingId}`);
      }

      const conflictingBookings = await db
        .select()
        .from(calendarBookings)
        .where(and(...baseConditions));
      
      const isAvailable = conflictingBookings.length === 0;
      const message = isAvailable 
        ? 'Dates are available'
        : `${conflictingBookings.length} conflicting booking(s) found`;
      
      logger.debug('Availability check completed', {
        component: 'CalendarService',
        propertyId,
        isAvailable,
        conflictCount: conflictingBookings.length
      });
      
      return {
        isAvailable,
        conflictingBookings,
        message
      };
      
    } catch (error) {
      logger.error('Failed to check availability', {
        component: 'CalendarService',
        propertyId,
        startDate,
        endDate
      }, error as Error);
      throw error;
    }
  }
  
  // Calendar Sync Status CRUD Operations
  
  /**
   * Create a new sync status record
   */
  async createSyncStatus(data: InsertCalendarSyncStatus): Promise<CalendarSyncStatus> {
    try {
      const validatedData = insertCalendarSyncStatusSchema.parse(data);
      
      logger.info('Creating calendar sync status', {
        component: 'CalendarService',
        propertyId: validatedData.propertyId,
        calendarType: validatedData.calendarType
      });
      
      const [syncStatus] = await db
        .insert(calendarSyncStatus)
        .values({
          ...validatedData,
          updatedAt: new Date()
        })
        .returning();
      
      logger.info('Calendar sync status created successfully', {
        component: 'CalendarService',
        syncStatusId: syncStatus.id,
        propertyId: syncStatus.propertyId
      });
      
      return syncStatus;
      
    } catch (error) {
      logger.error('Failed to create calendar sync status', {
        component: 'CalendarService',
        propertyId: data.propertyId
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Get sync status records with optional filters
   */
  async getSyncStatus(filters: {
    propertyId?: number;
    calendarType?: string;
    isActive?: boolean;
  } = {}): Promise<SyncStatusWithProperty[]> {
    try {
      logger.debug('Fetching sync status records', {
        component: 'CalendarService',
        filters
      });
      
      // Build conditions
      const conditions = [];
      
      if (filters.propertyId) {
        conditions.push(eq(calendarSyncStatus.propertyId, filters.propertyId));
      }
      
      if (filters.calendarType) {
        conditions.push(eq(calendarSyncStatus.calendarType, filters.calendarType));
      }
      
      if (filters.isActive !== undefined) {
        conditions.push(eq(calendarSyncStatus.isActive, filters.isActive));
      }

      // Build query
      let query = db
        .select({
          // Sync status fields
          id: calendarSyncStatus.id,
          propertyId: calendarSyncStatus.propertyId,
          calendarType: calendarSyncStatus.calendarType,
          lastSyncAt: calendarSyncStatus.lastSyncAt,
          syncToken: calendarSyncStatus.syncToken,
          isActive: calendarSyncStatus.isActive,
          webhookUrl: calendarSyncStatus.webhookUrl,
          syncSettings: calendarSyncStatus.syncSettings,
          errorMessage: calendarSyncStatus.errorMessage,
          createdAt: calendarSyncStatus.createdAt,
          updatedAt: calendarSyncStatus.updatedAt,
          // Property details
          property: {
            id: properties.id,
            title: properties.title,
            ownerId: properties.ownerId
          }
        })
        .from(calendarSyncStatus)
        .leftJoin(properties, eq(calendarSyncStatus.propertyId, properties.id));

      // Add conditions
      if (conditions.length > 0) {
        query = query.where(and(...conditions)) as any;
      }

      // Add ordering
      query = (query as any).orderBy(desc(calendarSyncStatus.updatedAt));
      
      const syncStatuses = await query;
      
      logger.debug('Sync status records fetched successfully', {
        component: 'CalendarService',
        count: syncStatuses.length
      });
      
      return syncStatuses;
      
    } catch (error) {
      logger.error('Failed to fetch sync status records', {
        component: 'CalendarService',
        filters
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Update sync status record
   */
  async updateSyncStatus(
    id: number,
    data: Partial<InsertCalendarSyncStatus>
  ): Promise<CalendarSyncStatus | null> {
    try {
      logger.info('Updating sync status record', {
        component: 'CalendarService',
        syncStatusId: id,
        updateFields: Object.keys(data)
      });
      
      const [updatedSyncStatus] = await db
        .update(calendarSyncStatus)
        .set({
          ...data,
          updatedAt: new Date()
        })
        .where(eq(calendarSyncStatus.id, id))
        .returning();
      
      if (!updatedSyncStatus) {
        logger.warn('Sync status record not found for update', {
          component: 'CalendarService',
          syncStatusId: id
        });
        return null;
      }
      
      logger.info('Sync status record updated successfully', {
        component: 'CalendarService',
        syncStatusId: id
      });
      
      return updatedSyncStatus;
      
    } catch (error) {
      logger.error('Failed to update sync status record', {
        component: 'CalendarService',
        syncStatusId: id
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Delete/disable sync status record
   */
  async disableSync(id: number): Promise<boolean> {
    try {
      logger.info('Disabling calendar sync', {
        component: 'CalendarService',
        syncStatusId: id
      });
      
      const [updatedSyncStatus] = await db
        .update(calendarSyncStatus)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(calendarSyncStatus.id, id))
        .returning({ id: calendarSyncStatus.id });
      
      if (!updatedSyncStatus) {
        logger.warn('Sync status record not found for disable', {
          component: 'CalendarService',
          syncStatusId: id
        });
        return false;
      }
      
      logger.info('Calendar sync disabled successfully', {
        component: 'CalendarService',
        syncStatusId: id
      });
      
      return true;
      
    } catch (error) {
      logger.error('Failed to disable calendar sync', {
        component: 'CalendarService',
        syncStatusId: id
      }, error as Error);
      throw error;
    }
  }
  
  /**
   * Get calendar statistics for a property
   */
  async getCalendarStats(propertyId: number, year?: number): Promise<{
    totalBookings: number;
    confirmedBookings: number;
    tentativeBookings: number;
    blockedDays: number;
    byMonth: Record<string, number>;
    bySource: Record<string, number>;
  }> {
    try {
      logger.debug('Fetching calendar statistics', {
        component: 'CalendarService',
        propertyId,
        year
      });
      
      // Build conditions
      const conditions = [eq(calendarBookings.propertyId, propertyId)];
      
      // Filter by year if provided
      if (year) {
        conditions.push(gte(calendarBookings.startDate, `${year}-01-01`));
        conditions.push(lte(calendarBookings.startDate, `${year}-12-31`));
      }

      let query = db
        .select({
          count: count(),
          status: calendarBookings.status,
          source: calendarBookings.source,
          startDate: calendarBookings.startDate
        })
        .from(calendarBookings)
        .where(and(...conditions));
      
      const results = await query;
      
      // Process results
      let totalBookings = 0;
      let confirmedBookings = 0;
      let tentativeBookings = 0;
      let blockedDays = 0;
      const byMonth: Record<string, number> = {};
      const bySource: Record<string, number> = {};
      
      results.forEach(result => {
        totalBookings += result.count;
        
        if (result.status === 'confirmed') confirmedBookings += result.count;
        if (result.status === 'tentative') tentativeBookings += result.count;
        if (result.status === 'blocked') blockedDays += result.count;
        
        // Extract month from date
        const month = result.startDate.substring(0, 7); // YYYY-MM
        byMonth[month] = (byMonth[month] || 0) + result.count;
        
        // Count by source
        const source = result.source || 'unknown';
        bySource[source] = (bySource[source] || 0) + result.count;
      });
      
      logger.debug('Calendar statistics fetched successfully', {
        component: 'CalendarService',
        propertyId,
        totalBookings
      });
      
      return {
        totalBookings,
        confirmedBookings,
        tentativeBookings,
        blockedDays,
        byMonth,
        bySource
      };
      
    } catch (error) {
      logger.error('Failed to fetch calendar statistics', {
        component: 'CalendarService',
        propertyId,
        year
      }, error as Error);
      throw error;
    }
  }
}

// Export singleton instance
export const calendarService = new CalendarService();