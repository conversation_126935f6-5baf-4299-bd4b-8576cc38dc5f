import { storage } from '../storage';
import { Property, InsertProperty, PropertyForm } from '../../shared/schema';
import { cloudinaryService } from '../cloudinaryService';

export interface PropertySearchFilters {
  featured?: boolean;
  location?: string;
  date?: Date;
  minPrice?: number;
  maxPrice?: number;
  amenities?: string[];
}

export class PropertyService {
  async getAllProperties(filters: PropertySearchFilters = {}): Promise<Property[]> {
    return await storage.getProperties(
      filters.featured,
      filters.location,
      filters.date,
      filters.minPrice,
      filters.maxPrice,
      filters.amenities
    );
  }

  async getPropertyById(id: number): Promise<Property | null> {
    const property = await storage.getProperty(id);
    return property || null;
  }

  async getPropertiesByOwner(ownerId: number): Promise<Property[]> {
    return await storage.getPropertiesByOwner(ownerId);
  }

  async createProperty(propertyData: any, ownerId: number): Promise<Property> {
    // Validate required fields
    if (!propertyData.title || !propertyData.location) {
      throw new Error('Title and location are required');
    }

    if (propertyData.halfDayPrice <= 0 || propertyData.fullDayPrice <= 0) {
      throw new Error('Prices must be greater than 0');
    }

    // Clean up weekend pricing - convert 0 to null for proper fallback behavior
    const cleanWeekendPrice = (price: number | undefined) => {
      return (price !== undefined && price > 0) ? price : undefined;
    };

    const newProperty: InsertProperty = {
      ownerId,
      title: propertyData.title,
      description: propertyData.description || '',
      location: propertyData.location,
      halfDayPrice: propertyData.halfDayPrice,
      fullDayPrice: propertyData.fullDayPrice,
      weekdayHalfDayPrice: cleanWeekendPrice(propertyData.weekdayHalfDayPrice),
      weekdayFullDayPrice: cleanWeekendPrice(propertyData.weekdayFullDayPrice),
      weekendHalfDayPrice: cleanWeekendPrice(propertyData.weekendHalfDayPrice),
      weekendFullDayPrice: cleanWeekendPrice(propertyData.weekendFullDayPrice),
      bedrooms: propertyData.bedrooms || 1,
      bathrooms: propertyData.bathrooms || 1,
      amenities: propertyData.amenities || [],
      images: propertyData.images || [],
      videos: propertyData.videos || [],
      status: 'active',
      featured: propertyData.featured || false
    };

    return await storage.createProperty(newProperty);
  }

  async updateProperty(id: number, propertyData: any, ownerId: number): Promise<Property | null> {
    // Verify ownership
    const existingProperty = await this.getPropertyById(id);
    if (!existingProperty) {
      throw new Error('Property not found');
    }

    if (existingProperty.ownerId !== ownerId) {
      throw new Error('Not authorized to update this property');
    }

    // Validate prices if provided
    if (propertyData.halfDayPrice && propertyData.halfDayPrice <= 0) {
      throw new Error('Half day price must be greater than 0');
    }

    if (propertyData.fullDayPrice && propertyData.fullDayPrice <= 0) {
      throw new Error('Full day price must be greater than 0');
    }

    const updatedProperty = await storage.updateProperty(id, propertyData);
    return updatedProperty || null;
  }

  async deleteProperty(id: number, ownerId: number): Promise<boolean> {
    // Verify ownership
    const existingProperty = await this.getPropertyById(id);
    if (!existingProperty) {
      throw new Error('Property not found');
    }

    if (existingProperty.ownerId !== ownerId) {
      throw new Error('Not authorized to delete this property');
    }

    // Delete associated images from Cloudinary
    if (existingProperty.images && existingProperty.images.length > 0) {
      try {
        const publicIds = existingProperty.images
          .map(url => this.extractCloudinaryPublicId(url))
          .filter(id => id !== null) as string[];
        
        if (publicIds.length > 0) {
          await cloudinaryService.deleteMultipleImages(publicIds);
        }
      } catch (error) {
        console.error('Error deleting images from Cloudinary:', error);
        // Continue with property deletion even if image deletion fails
      }
    }

    return await storage.deleteProperty(id);
  }

  async getFeaturedProperties(): Promise<Property[]> {
    return await this.getAllProperties({ featured: true });
  }

  async searchProperties(searchParams: PropertySearchFilters): Promise<Property[]> {
    return await this.getAllProperties(searchParams);
  }

  async getPropertiesByLocation(location: string): Promise<Property[]> {
    return await this.getAllProperties({ location });
  }

  async uploadPropertyImages(files: Array<{ buffer: Buffer; filename: string }>): Promise<string[]> {
    if (!cloudinaryService.isInitialized()) {
      throw new Error('Image upload service is not available');
    }

    try {
      const uploadResults = await cloudinaryService.uploadMultipleImages(files);
      return uploadResults.map(result => result.secure_url);
    } catch (error) {
      console.error('Error uploading images:', error);
      throw new Error('Failed to upload images');
    }
  }

  async deletePropertyImage(imageUrl: string): Promise<boolean> {
    if (!cloudinaryService.isInitialized()) {
      throw new Error('Image delete service is not available');
    }

    const publicId = this.extractCloudinaryPublicId(imageUrl);
    if (!publicId) {
      throw new Error('Invalid image URL');
    }

    try {
      return await cloudinaryService.deleteImage(publicId);
    } catch (error) {
      console.error('Error deleting image:', error);
      throw new Error('Failed to delete image');
    }
  }

  private extractCloudinaryPublicId(imageUrl: string): string | null {
    // Extract public ID from Cloudinary URL
    // Format: https://res.cloudinary.com/cloud/image/upload/v123456/public_id.ext
    const match = imageUrl.match(/\/v\d+\/([^\.]+)/);
    return match ? match[1] : null;
  }

  async validatePropertyAvailability(propertyId: number, date: Date): Promise<boolean> {
    const property = await this.getPropertyById(propertyId);
    if (!property || property.status !== 'active') {
      return false;
    }

    // Check if property is available on the given date
    return await storage.checkAvailability(propertyId, date, 'full_day');
  }

  // Helper method to calculate effective pricing with fallback logic
  getEffectivePrice(property: Property, date: Date, duration: '12h' | '24h'): number {
    const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 5 || dayOfWeek === 6; // Fri, Sat, Sun

    if (duration === '12h') {
      if (isWeekend) {
        // Weekend 12h: try weekend price, then weekday price, then base price
        return property.weekendHalfDayPrice || property.weekdayHalfDayPrice || property.halfDayPrice;
      } else {
        // Weekday 12h: try weekday price, then base price
        return property.weekdayHalfDayPrice || property.halfDayPrice;
      }
    } else {
      if (isWeekend) {
        // Weekend 24h: try weekend price, then weekday price, then base price
        return property.weekendFullDayPrice || property.weekdayFullDayPrice || property.fullDayPrice;
      } else {
        // Weekday 24h: try weekday price, then base price
        return property.weekdayFullDayPrice || property.fullDayPrice;
      }
    }
  }
}

export const propertyService = new PropertyService();