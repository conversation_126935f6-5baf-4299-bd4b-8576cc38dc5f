import crypto from 'crypto';
// Import Razorpay dynamically to avoid startup errors if not installed
let Ra<PERSON>pay: any = null;
try {
  Razorpay = require('razorpay');
} catch (error) {
  console.warn('razorpay not available, payment functionality disabled');
}
import { db } from "../db";
import { 
  paymentOrders, 
  paymentTransactions, 
  bookings,
  idempotencyKeys,
  PaymentOrder,
  PaymentTransaction,
  InsertPaymentOrder,
  InsertPaymentTransaction,
  InsertIdempotencyKey
} from "../../shared/schema";
import { eq, and } from "drizzle-orm";
import { auditLogger } from "./AuditLogger";
import { fieldEncryptionService } from "./FieldEncryptionService";

// Circuit Breaker States
enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

// Payment Error Types
enum PaymentErrorType {
  NETWORK_ERROR = 'network_error',
  INVALID_SIGNATURE = 'invalid_signature',
  INSUFFICIENT_FUNDS = 'insufficient_funds',
  GATEWAY_ERROR = 'gateway_error',
  TIMEOUT = 'timeout',
  DUPLICATE_REQUEST = 'duplicate_request',
  VALIDATION_ERROR = 'validation_error',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded'
}

// Payment Status Enum
export enum PaymentStatus {
  INITIATED = 'initiated',
  PENDING = 'pending',
  AUTHORIZED = 'authorized',
  CAPTURED = 'captured',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
  EXPIRED = 'expired'
}

interface PaymentError {
  type: PaymentErrorType;
  message: string;
  code?: string;
  details?: any;
  retryable: boolean;
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  timeout: number;
  resetTimeout: number;
}

interface RetryConfig {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

interface RazorpayPaymentData {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
}

interface PaymentVerificationResult {
  isValid: boolean;
  paymentId?: string;
  orderId?: string;
  error?: PaymentError;
}

interface BookingDetails {
  bookingId: number;
  amount: number;
  currency: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  propertyDetails: any;
}

class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private successCount: number = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await Promise.race([
        fn(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Operation timeout')), this.config.timeout)
        )
      ]);

      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successful operations to close
        this.state = CircuitBreakerState.CLOSED;
      }
    }
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.config.failureThreshold) {
      this.state = CircuitBreakerState.OPEN;
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }
}

export class EnhancedPaymentService {
  private static instance: EnhancedPaymentService;
  private razorpay: any;
  private circuitBreaker: CircuitBreaker;
  private retryConfig: RetryConfig;

  private constructor() {
    // Validate Razorpay environment variables
    const razorpayKeyId = process.env.RAZORPAY_KEY_ID;
    const razorpayKeySecret = process.env.RAZORPAY_KEY_SECRET;
    const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
    
    if (!Razorpay || !razorpayKeyId || !razorpayKeySecret || 
        razorpayKeyId.includes('your_razorpay_key') || 
        razorpayKeySecret.includes('your_razorpay_key')) {
      
      console.warn(
        '⚠️  Payment service using mock mode. ' +
        'Install razorpay package and set RAZORPAY_KEY_ID/RAZORPAY_KEY_SECRET for real payments.'
      );
      // Always use mock in this case
      this.razorpay = this.createMockRazorpay();
    } else {
      try {
        if (Razorpay) {
          this.razorpay = new Razorpay({
            key_id: razorpayKeyId,
            key_secret: razorpayKeySecret
          });
        } else {
          console.warn('⚠️  Razorpay not available, using mock');
          this.razorpay = this.createMockRazorpay();
        }
      } catch (error: any) {
        if (isDevelopment) {
          console.warn(`⚠️  Razorpay initialization failed, using mock: ${error.message}`);
          this.razorpay = this.createMockRazorpay();
        } else {
          throw new Error(`Failed to initialize Razorpay: ${error.message}`);
        }
      }
    }

    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      timeout: 10000, // 10 seconds
      resetTimeout: 60000 // 1 minute
    });

    this.retryConfig = {
      maxAttempts: 3,
      initialDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2
    };
  }

  private createMockRazorpay(): any {
    return {
      orders: {
        create: async (options: any) => {
          console.log('🧪 Mock Razorpay Order Created:', options);
          return {
            id: `order_mock_${Date.now()}`,
            receipt: options.receipt || `receipt_${Date.now()}`,
            amount: options.amount,
            currency: options.currency || 'INR',
            status: 'created'
          };
        }
      },
      payments: {
        capture: async (paymentId: string, amount: number, currency: string = 'INR') => {
          console.log(`🧪 Mock Payment Captured: ${paymentId}, ${amount}, ${currency}`);
          return {
            id: paymentId,
            amount,
            status: 'captured'
          };
        },
        refund: async (paymentId: string, options: any) => {
          console.log(`🧪 Mock Payment Refunded: ${paymentId}`, options);
          return {
            id: `rfnd_mock_${Date.now()}`,
            payment_id: paymentId,
            amount: options.amount,
            status: 'processed'
          };
        }
      }
    };
  }

  public static getInstance(): EnhancedPaymentService {
    if (!EnhancedPaymentService.instance) {
      EnhancedPaymentService.instance = new EnhancedPaymentService();
    }
    return EnhancedPaymentService.instance;
  }

  // Create payment order with idempotency and circuit breaker
  async createPaymentOrder(
    bookingDetails: BookingDetails,
    idempotencyKey: string
  ): Promise<PaymentOrder> {
    try {
      // Check idempotency
      const existingOrder = await this.checkIdempotencyKey(idempotencyKey);
      if (existingOrder) {
        await auditLogger.logPaymentAction('payment_order_duplicate', {
          actorType: 'system',
          metadata: { idempotencyKey, bookingId: bookingDetails.bookingId }
        });
        return existingOrder;
      }

      // Create order with circuit breaker
      const order = await this.circuitBreaker.execute(async () => {
        return await this.createOrderInternal(bookingDetails, idempotencyKey);
      });

      // Store idempotency key
      await this.storeIdempotencyKey(idempotencyKey, 'payment_order', order.id, order);

      await auditLogger.logPaymentOrderAction(
        'payment_order_created',
        order.id,
        { actorType: 'system' },
        null,
        { orderId: order.razorpayOrderId, amount: order.amount }
      );

      return order;
    } catch (error) {
      await this.handlePaymentError(error, { bookingId: bookingDetails.bookingId });
      throw error;
    }
  }

  // Verify payment signature with enhanced security
  async verifyPaymentSignature(
    paymentData: RazorpayPaymentData,
    orderId: number
  ): Promise<PaymentVerificationResult> {
    try {
      const order = await this.getPaymentOrder(orderId);
      if (!order) {
        throw new Error('Payment order not found');
      }

      // Verify signature
      const isValid = await this.verifySignatureInternal(paymentData, order);
      
      if (!isValid) {
        // Log security incident
        await auditLogger.logPaymentFraud(
          'Invalid payment signature detected',
          order.id,
          'unknown',
          undefined,
          { paymentData, orderId }
        );

        return {
          isValid: false,
          error: {
            type: PaymentErrorType.INVALID_SIGNATURE,
            message: 'Payment signature verification failed',
            retryable: false
          }
        };
      }

      // Create payment transaction record
      const transaction = await this.createPaymentTransaction(paymentData, order);

      await auditLogger.logPaymentTransactionAction(
        'payment_signature_verified',
        transaction.id,
        order.id,
        { actorType: 'system' },
        null,
        { paymentId: paymentData.razorpay_payment_id }
      );

      return {
        isValid: true,
        paymentId: paymentData.razorpay_payment_id,
        orderId: paymentData.razorpay_order_id
      };
    } catch (error) {
      await this.handlePaymentError(error, { orderId });
      throw error;
    }
  }

  // Capture payment with retry logic
  async capturePayment(
    paymentId: string,
    amount: number,
    idempotencyKey: string
  ): Promise<PaymentTransaction> {
    return await this.executeWithRetry(async () => {
      return await this.capturePaymentInternal(paymentId, amount, idempotencyKey);
    });
  }

  // Process refund with comprehensive logging
  async processRefund(
    paymentId: string,
    amount: number,
    reason: string,
    idempotencyKey: string
  ): Promise<any> {
    try {
      const existingRefund = await this.checkIdempotencyKey(idempotencyKey);
      if (existingRefund) {
        return existingRefund;
      }

      const refund = await this.circuitBreaker.execute(async () => {
        return await this.razorpay.payments.refund(paymentId, {
          amount: amount,
          notes: {
            reason: reason,
            processed_at: new Date().toISOString()
          }
        });
      });

      // Store idempotency key with hash of refund ID since Razorpay IDs are strings
      const refundIdHash = refund.id ? refund.id.substring(0, 8).split('').reduce((a: number, b: string) => (a << 5) - a + b.charCodeAt(0), 0) : 0;
      await this.storeIdempotencyKey(idempotencyKey, 'refund', refundIdHash, refund);

      await auditLogger.logPaymentAction('payment_refunded', {
        actorType: 'system',
        metadata: { paymentId, amount, reason, refundId: refund.id }
      });

      return refund;
    } catch (error) {
      await this.handlePaymentError(error, { paymentId, amount, reason });
      throw error;
    }
  }

  // Get payment order
  async getPaymentOrder(orderId: number): Promise<PaymentOrder | null> {
    try {
      const [order] = await db
        .select()
        .from(paymentOrders)
        .where(eq(paymentOrders.id, orderId));

      return order || null;
    } catch (error) {
      console.error('Failed to get payment order:', error);
      return null;
    }
  }

  // Get payment transaction
  async getPaymentTransaction(transactionId: number): Promise<PaymentTransaction | null> {
    try {
      const [transaction] = await db
        .select()
        .from(paymentTransactions)
        .where(eq(paymentTransactions.id, transactionId));

      return transaction || null;
    } catch (error) {
      console.error('Failed to get payment transaction:', error);
      return null;
    }
  }

  // Update payment status
  async updatePaymentStatus(
    orderId: number,
    status: PaymentStatus,
    metadata?: any
  ): Promise<void> {
    try {
      await db
        .update(paymentOrders)
        .set({ 
          status: status,
          updatedAt: new Date()
        })
        .where(eq(paymentOrders.id, orderId));

      await auditLogger.logPaymentOrderAction(
        'payment_status_updated',
        orderId,
        { actorType: 'system' },
        null,
        { status, metadata }
      );
    } catch (error) {
      console.error('Failed to update payment status:', error);
      throw error;
    }
  }

  // Private methods

  private async createOrderInternal(
    bookingDetails: BookingDetails,
    idempotencyKey: string
  ): Promise<PaymentOrder> {
    // Create Razorpay order
    const razorpayOrder = await this.razorpay.orders.create({
      amount: bookingDetails.amount,
      currency: bookingDetails.currency,
      receipt: `booking_${bookingDetails.bookingId}_${Date.now()}`,
      notes: {
        booking_id: bookingDetails.bookingId.toString(),
        customer_name: bookingDetails.customerName,
        customer_email: bookingDetails.customerEmail,
        customer_phone: bookingDetails.customerPhone
      }
    });

    // Store in database
    const orderData: InsertPaymentOrder = {
      razorpayOrderId: razorpayOrder.id,
      bookingId: bookingDetails.bookingId,
      idempotencyKey: idempotencyKey,
      amount: bookingDetails.amount,
      currency: bookingDetails.currency,
      receipt: razorpayOrder.receipt,
      status: 'created',
      attempts: 0,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };

    const [order] = await db
      .insert(paymentOrders)
      .values(orderData)
      .returning();

    return order;
  }

  private async verifySignatureInternal(
    paymentData: RazorpayPaymentData,
    order: PaymentOrder
  ): Promise<boolean> {
    try {
      const text = `${paymentData.razorpay_order_id}|${paymentData.razorpay_payment_id}`;
      const signature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
        .update(text)
        .digest('hex');

      // Timing-safe comparison
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(paymentData.razorpay_signature, 'hex')
      );
    } catch (error) {
      console.error('Signature verification failed:', error);
      return false;
    }
  }

  private async createPaymentTransaction(
    paymentData: RazorpayPaymentData,
    order: PaymentOrder
  ): Promise<PaymentTransaction> {
    // Encrypt sensitive signature data
    const encryptedSignature = await fieldEncryptionService.encryptRazorpaySignature(
      paymentData.razorpay_signature
    );

    const transactionData: InsertPaymentTransaction = {
      paymentOrderId: order.id,
      razorpayPaymentId: paymentData.razorpay_payment_id,
      razorpaySignatureHash: fieldEncryptionService.hashSensitiveData(paymentData.razorpay_signature),
      signatureVerificationStatus: 'verified',
      amount: order.amount,
      currency: order.currency,
      status: PaymentStatus.AUTHORIZED,
      encryptionKeyId: encryptedSignature.keyId,
      sensitiveDataHash: fieldEncryptionService.hashSensitiveData(
        JSON.stringify(encryptedSignature)
      ),
      securityFlags: {
        signatureEncrypted: true,
        verificationTimestamp: new Date().toISOString()
      }
    };

    const [transaction] = await db
      .insert(paymentTransactions)
      .values(transactionData)
      .returning();

    return transaction;
  }

  private async capturePaymentInternal(
    paymentId: string,
    amount: number,
    idempotencyKey: string
  ): Promise<PaymentTransaction> {
    // Check idempotency
    const existingCapture = await this.checkIdempotencyKey(idempotencyKey);
    if (existingCapture) {
      return existingCapture;
    }

    // Capture payment through Razorpay
    const captureResult = await this.razorpay.payments.capture(paymentId, amount, 'INR');

    // Update transaction status
    await db
      .update(paymentTransactions)
      .set({
        status: PaymentStatus.CAPTURED,
        gatewayResponse: captureResult,
        updatedAt: new Date()
      })
      .where(eq(paymentTransactions.razorpayPaymentId, paymentId));

    // Get updated transaction
    const [transaction] = await db
      .select()
      .from(paymentTransactions)
      .where(eq(paymentTransactions.razorpayPaymentId, paymentId));

    // Store idempotency key
    await this.storeIdempotencyKey(idempotencyKey, 'capture', transaction.id, transaction);

    return transaction;
  }

  private async checkIdempotencyKey(key: string): Promise<any | null> {
    try {
      const [record] = await db
        .select()
        .from(idempotencyKeys)
        .where(eq(idempotencyKeys.key, key));

      return record ? record.responseData : null;
    } catch (error) {
      console.error('Failed to check idempotency key:', error);
      return null;
    }
  }

  private async storeIdempotencyKey(
    key: string,
    resourceType: string,
    resourceId: number,
    responseData: any
  ): Promise<void> {
    try {
      const keyData: InsertIdempotencyKey = {
        key,
        resourceType,
        resourceId,
        responseData,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };

      await db
        .insert(idempotencyKeys)
        .values(keyData)
        .onConflictDoNothing();
    } catch (error) {
      console.error('Failed to store idempotency key:', error);
    }
  }

  private async executeWithRetry<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error;
    let delay = this.retryConfig.initialDelay;

    for (let attempt = 1; attempt <= this.retryConfig.maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === this.retryConfig.maxAttempts) {
          break;
        }

        // Don't retry non-retryable errors
        if (!this.isRetryableError(error)) {
          break;
        }

        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * this.retryConfig.backoffMultiplier, this.retryConfig.maxDelay);
      }
    }

    throw lastError!;
  }

  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      PaymentErrorType.NETWORK_ERROR,
      PaymentErrorType.TIMEOUT,
      PaymentErrorType.GATEWAY_ERROR
    ];

    return retryableErrors.includes(error.type) || 
           error.code === 'NETWORK_ERROR' ||
           error.code === 'TIMEOUT';
  }

  private async handlePaymentError(error: any, context: any): Promise<void> {
    const errorType = this.classifyError(error);
    
    await auditLogger.logPaymentAction('payment_error', {
      actorType: 'system',
      metadata: {
        error: error.message,
        errorType,
        context,
        stack: error.stack
      }
    });

    // Log security incidents for suspicious errors
    if (errorType === PaymentErrorType.INVALID_SIGNATURE) {
      await auditLogger.logPaymentFraud(
        'Payment signature verification failed',
        context.orderId || 0,
        'unknown',
        undefined,
        { error: error.message, context }
      );
    }
  }

  private classifyError(error: any): PaymentErrorType {
    const message = error.message.toLowerCase();
    if (message.includes('signature')) {
      return PaymentErrorType.INVALID_SIGNATURE;
    }
    if (message.includes('timeout')) {
      return PaymentErrorType.TIMEOUT;
    }
    if (message.includes('network')) {
      return PaymentErrorType.NETWORK_ERROR;
    }
    if (message.includes('funds')) {
      return PaymentErrorType.INSUFFICIENT_FUNDS;
    }
    if (message.includes('duplicate')) {
      return PaymentErrorType.DUPLICATE_REQUEST;
    }
    return PaymentErrorType.GATEWAY_ERROR;
  }
}

// Export singleton instance
export const enhancedPaymentService = EnhancedPaymentService.getInstance();