import WebSocket, { WebSocketServer } from 'ws';
import { Server } from 'http';
import jwt from 'jsonwebtoken';
import { logger } from './LoggerService';
import { config } from '../config';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: number;
  userRole?: string;
  propertyIds?: number[];
  isAlive?: boolean;
}

interface WebSocketMessage {
  type: 'calendar_update' | 'booking_update' | 'property_update' | 'notification' | 'heartbeat' | 'error';
  data: any;
  timestamp: number;
  propertyId?: number;
  userId?: number;
}

export class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients = new Map<number, Set<AuthenticatedWebSocket>>();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  initialize(server: Server): void {
    this.wss = new WebSocketServer({
      server,
      path: '/api/ws',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();

    logger.info('WebSocket server initialized', {
      component: 'WebSocketService',
      path: '/api/ws'
    });
  }

  private verifyClient(info: any): boolean {
    try {
      const url = new URL(info.req.url, `ws://localhost`);
      const token = url.searchParams.get('token') || 
                   info.req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        logger.warn('WebSocket connection rejected: No token provided', {
          component: 'WebSocketService',
          origin: info.origin
        });
        return false;
      }

      // Verify JWT token
      jwt.verify(token, config.jwt.secret);
      return true;
    } catch (error) {
      logger.warn('WebSocket connection rejected: Invalid token', {
        component: 'WebSocketService',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  private handleConnection(ws: AuthenticatedWebSocket, request: any): void {
    try {
      // Extract user info from token
      const url = new URL(request.url, `ws://localhost`);
      const token = url.searchParams.get('token') || 
                   request.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        ws.close(1008, 'No authentication token');
        return;
      }

      const decoded = jwt.verify(token, config.jwt.secret) as any;
      ws.userId = decoded.userId;
      ws.userRole = decoded.role;
      ws.isAlive = true;

      // Add to clients map
      if (!this.clients.has(ws.userId)) {
        this.clients.set(ws.userId, new Set());
      }
      this.clients.get(ws.userId)!.add(ws);

      logger.info('WebSocket client connected', {
        component: 'WebSocketService',
        userId: ws.userId,
        userRole: ws.userRole,
        totalConnections: this.getTotalConnections()
      });

      // Set up message handlers
      ws.on('message', (data) => this.handleMessage(ws, data));
      ws.on('close', () => this.handleDisconnection(ws));
      ws.on('error', (error) => this.handleError(ws, error));
      ws.on('pong', () => { ws.isAlive = true; });

      // Send connection confirmation
      this.sendToClient(ws, {
        type: 'notification',
        data: { message: 'WebSocket connection established' },
        timestamp: Date.now()
      });

    } catch (error) {
      logger.error('Error handling WebSocket connection', {
        component: 'WebSocketService',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      ws.close(1011, 'Internal server error');
    }
  }

  private handleMessage(ws: AuthenticatedWebSocket, data: WebSocket.Data): void {
    try {
      const message: WebSocketMessage = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'heartbeat':
          ws.isAlive = true;
          this.sendToClient(ws, {
            type: 'heartbeat',
            data: { pong: true },
            timestamp: Date.now()
          });
          break;
          
        default:
          logger.debug('Received WebSocket message', {
            component: 'WebSocketService',
            userId: ws.userId,
            messageType: message.type
          });
      }
    } catch (error) {
      logger.error('Error handling WebSocket message', {
        component: 'WebSocketService',
        userId: ws.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private handleDisconnection(ws: AuthenticatedWebSocket): void {
    if (ws.userId) {
      const userClients = this.clients.get(ws.userId);
      if (userClients) {
        userClients.delete(ws);
        if (userClients.size === 0) {
          this.clients.delete(ws.userId);
        }
      }

      logger.info('WebSocket client disconnected', {
        component: 'WebSocketService',
        userId: ws.userId,
        totalConnections: this.getTotalConnections()
      });
    }
  }

  private handleError(ws: AuthenticatedWebSocket, error: Error): void {
    logger.error('WebSocket client error', {
      component: 'WebSocketService',
      userId: ws.userId,
      error: error.message
    });
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (!this.wss) return;

      this.wss.clients.forEach((ws: AuthenticatedWebSocket) => {
        if (!ws.isAlive) {
          logger.debug('Terminating inactive WebSocket connection', {
            component: 'WebSocketService',
            userId: ws.userId
          });
          return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // 30 seconds
  }

  // Public methods for broadcasting messages

  public broadcastCalendarUpdate(propertyId: number, bookingData: any, excludeUserId?: number): void {
    const message: WebSocketMessage = {
      type: 'calendar_update',
      data: {
        propertyId,
        booking: bookingData,
        action: 'updated'
      },
      timestamp: Date.now(),
      propertyId
    };

    this.broadcastToPropertyOwners(propertyId, message, excludeUserId);

    logger.debug('Broadcasted calendar update', {
      component: 'WebSocketService',
      propertyId,
      excludeUserId
    });
  }

  public broadcastBookingUpdate(bookingData: any, action: 'created' | 'updated' | 'deleted', excludeUserId?: number): void {
    const message: WebSocketMessage = {
      type: 'booking_update',
      data: {
        booking: bookingData,
        action
      },
      timestamp: Date.now(),
      propertyId: bookingData.propertyId
    };

    this.broadcastToPropertyOwners(bookingData.propertyId, message, excludeUserId);

    logger.debug('Broadcasted booking update', {
      component: 'WebSocketService',
      propertyId: bookingData.propertyId,
      action,
      excludeUserId
    });
  }

  public sendNotificationToUser(userId: number, notification: any): void {
    const message: WebSocketMessage = {
      type: 'notification',
      data: notification,
      timestamp: Date.now(),
      userId
    };

    this.sendToUser(userId, message);
  }

  // Private helper methods

  private sendToClient(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error('Failed to send WebSocket message', {
          component: 'WebSocketService',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  private sendToUser(userId: number, message: WebSocketMessage): void {
    const userClients = this.clients.get(userId);
    if (userClients) {
      userClients.forEach(ws => this.sendToClient(ws, message));
    }
  }

  private async broadcastToPropertyOwners(propertyId: number, message: WebSocketMessage, excludeUserId?: number): Promise<void> {
    // In a real implementation, you would query the database to get property owners
    // For now, we'll broadcast to all connected clients (they'll filter on the frontend)
    if (!this.wss) return;

    this.wss.clients.forEach((ws: AuthenticatedWebSocket) => {
      if (ws.userId !== excludeUserId && ws.readyState === WebSocket.OPEN) {
        this.sendToClient(ws, message);
      }
    });
  }

  private getTotalConnections(): number {
    return Array.from(this.clients.values())
      .reduce((total, clients) => total + clients.size, 0);
  }

  public getConnectionStats(): any {
    return {
      totalConnections: this.getTotalConnections(),
      connectedUsers: this.clients.size,
      averageConnectionsPerUser: this.clients.size > 0 
        ? Math.round(this.getTotalConnections() / this.clients.size * 100) / 100 
        : 0
    };
  }

  public shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.wss) {
      this.wss.clients.forEach(ws => ws.close(1001, 'Server shutting down'));
      this.wss.close();
      this.wss = null;
    }

    this.clients.clear();

    logger.info('WebSocket service shut down', {
      component: 'WebSocketService'
    });
  }
}

// Singleton instance
export const webSocketService = new WebSocketService();