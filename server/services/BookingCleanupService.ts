// Import node-cron dynamically to avoid startup errors if not installed
let cron: any = null;
try {
  cron = require('node-cron');
} catch (error) {
  console.warn('node-cron not available, cleanup scheduling disabled');
}
import { storage } from '../storage';
import { logger } from './LoggerService';

// Simple Circuit Breaker implementation
class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime: number | null = null;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 3,
    private resetTimeout: number = 30000, // 30 seconds
    private onOpen?: () => void,
    private onClose?: () => void
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (this.lastFailureTime && Date.now() - this.lastFailureTime < this.resetTimeout) {
        throw new Error('Circuit breaker is OPEN - operation blocked');
      }
      // Try to transition to HALF_OPEN
      this.state = 'HALF_OPEN';
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    this.lastFailureTime = null;
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.onClose?.();
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      this.onOpen?.();
    }
  }

  canExecute(): boolean {
    return this.state !== 'OPEN' || 
           (this.lastFailureTime !== null && Date.now() - this.lastFailureTime >= this.resetTimeout);
  }

  getState(): { state: string; failureCount: number; lastFailureTime: number | null } {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime
    };
  }
}

class BookingCleanupService {
  private cleanupJob: any | null = null;
  private isRunning = false;
  private circuitBreaker: CircuitBreaker;

  constructor() {
    // Initialize circuit breaker with callbacks
    this.circuitBreaker = new CircuitBreaker(
      3, // failure threshold
      30000, // 30 second reset timeout
      () => {
        logger.error('Booking cleanup circuit breaker opened - too many failures');
      },
      () => {
        logger.info('Booking cleanup circuit breaker closed - service recovered');
      }
    );
  }

  /**
   * Initialize the cleanup service with scheduled tasks
   */
  public initialize(): void {
    logger.info('Initializing booking cleanup service with circuit breaker protection');
    
    // Run cleanup on startup to handle existing stale bookings
    this.runImmediateCleanup();
    
    // Schedule cleanup to run every 6 hours (only if cron is available)
    if (cron) {
      this.cleanupJob = cron.schedule('0 */6 * * *', () => {
        this.runScheduledCleanup();
      }, {
        timezone: 'Asia/Kolkata' // Set to Indian timezone
      });
      logger.info('Booking cleanup service initialized successfully - scheduled every 6 hours');
    } else {
      logger.warn('Booking cleanup service initialized without scheduling (node-cron not available)');
    }
  }

  /**
   * Run cleanup immediately (called on server startup)
   */
  private async runImmediateCleanup(): Promise<void> {
    if (!this.circuitBreaker.canExecute()) {
      logger.warn('Immediate cleanup skipped - circuit breaker is open');
      return;
    }

    try {
      logger.info('Running immediate cleanup on server startup with circuit breaker protection');
      
      const deletedCount = await this.circuitBreaker.execute(async () => {
        const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
        return await storage.cleanupAllStaleBookings(cutoffTime);
      });
      
      if (deletedCount > 0) {
        logger.info('Startup cleanup completed successfully', 'booking-cleanup', { deletedCount });
      } else {
        logger.info('Startup cleanup completed - no stale bookings found');
      }
    } catch (error) {
      logger.error('Startup cleanup failed', error instanceof Error ? error : new Error(String(error)), 'booking-cleanup', { 
        circuitBreakerState: this.circuitBreaker.getState()
      });
    }
  }

  /**
   * Run scheduled cleanup (called by cron job)
   */
  private async runScheduledCleanup(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Scheduled cleanup already in progress, skipping current execution');
      return;
    }

    if (!this.circuitBreaker.canExecute()) {
      logger.warn('Scheduled cleanup skipped - circuit breaker is open', 'booking-cleanup', {
        circuitBreakerState: this.circuitBreaker.getState()
      });
      return;
    }

    try {
      this.isRunning = true;
      logger.info('Running scheduled cleanup with circuit breaker protection');
      
      const deletedCount = await this.circuitBreaker.execute(async () => {
        const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
        return await storage.cleanupAllStaleBookings(cutoffTime);
      });
      
      if (deletedCount > 0) {
        logger.info('Scheduled cleanup completed successfully', 'booking-cleanup', { deletedCount });
      } else {
        logger.info('Scheduled cleanup completed - no stale bookings found');
      }
    } catch (error) {
      logger.error('Scheduled cleanup failed', error instanceof Error ? error : new Error(String(error)), 'booking-cleanup', { 
        circuitBreakerState: this.circuitBreaker.getState()
      });
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Manual cleanup trigger (for admin/debug purposes)
   */
  public async runManualCleanup(): Promise<number> {
    if (!this.circuitBreaker.canExecute()) {
      const error = new Error('Manual cleanup blocked - circuit breaker is open');
      logger.error('Manual cleanup failed', error, 'booking-cleanup', { 
        circuitBreakerState: this.circuitBreaker.getState()
      });
      throw error;
    }

    try {
      logger.info('Running manual cleanup with circuit breaker protection');
      
      const deletedCount = await this.circuitBreaker.execute(async () => {
        const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
        return await storage.cleanupAllStaleBookings(cutoffTime);
      });
      
      logger.info('Manual cleanup completed successfully', 'booking-cleanup', { deletedCount });
      return deletedCount;
    } catch (error) {
      logger.error('Manual cleanup failed', error instanceof Error ? error : new Error(String(error)), 'booking-cleanup', { 
        circuitBreakerState: this.circuitBreaker.getState()
      });
      throw error;
    }
  }

  /**
   * Stop the cleanup service
   */
  public stop(): void {
    if (this.cleanupJob) {
      this.cleanupJob.stop();
      logger.info('Booking cleanup service stopped');
    }
  }

  /**
   * Get cleanup service status including circuit breaker state
   */
  public getStatus(): { 
    running: boolean; 
    nextRun: string | null;
    circuitBreaker: { state: string; failureCount: number; lastFailureTime: number | null };
    isCleanupRunning: boolean;
  } {
    return {
      running: this.cleanupJob !== null,
      nextRun: this.cleanupJob ? 'Every 6 hours at minute 0' : null,
      circuitBreaker: this.circuitBreaker.getState(),
      isCleanupRunning: this.isRunning
    };
  }
}

export const bookingCleanupService = new BookingCleanupService();