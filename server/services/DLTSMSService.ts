import twilio from 'twilio';
import { config } from '../config';
import { log } from '../utils/logger';
import { templateService } from './TemplateService';
import { cachedTemplateService } from './CachedTemplateService';
import { jobQueue } from './AsyncJobQueue';
import type { SmsTemplate } from '../../shared/schema';

interface DLTTemplate {
  id: string;
  content: string;
  category: 'transactional' | 'promotional';
  variables: string[];
  description: string;
}

interface SMSPayload {
  to: string;
  templateId: string;
  variables: Record<string, string>;
}

interface SMSResult {
  success: boolean;
  messageSid?: string;
  error?: string;
}

class DLTSMSService {
  private client: twilio.Twilio | null = null;
  private templates: Map<string, DLTTemplate>;

  constructor() {
    this.templates = new Map();
    this.initializeTemplates();
    this.initializeTwilioClient();
    this.registerJobHandlers();
  }

  private initializeTwilioClient(): void {
    if (!config.twilio.available) {
      log('⚠️ Twilio not configured - DLT SMS service will use fallback mode', 'dlt-sms');
      return;
    }

    if (!config.dlt.available) {
      log('⚠️ DLT Entity ID not configured - SMS will not be DLT compliant', 'dlt-sms');
    }

    try {
      this.client = twilio(config.twilio.accountSid, config.twilio.authToken);
      log('✅ DLT SMS service initialized successfully', 'dlt-sms');
    } catch (error) {
      log('❌ Failed to initialize Twilio client for DLT SMS service', 'dlt-sms');
      console.error('Twilio initialization error:', error);
    }
  }

  private initializeTemplates(): void {
    // Templates are now loaded from database
    // This method kept for backward compatibility
    log('📝 DLT SMS Templates will be loaded from database', 'dlt-sms');
  }

  private registerJobHandlers(): void {
    // Register SMS sending job handler
    jobQueue.registerHandler('send_sms', async (job) => {
      const startTime = Date.now();
      try {
        const { payload } = job.payload as { payload: SMSPayload };
        const result = await this.sendSMSSync(payload);
        
        return {
          success: result.success,
          data: result,
          duration: Date.now() - startTime
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'SMS job failed',
          duration: Date.now() - startTime
        };
      }
    });

    // Register batch SMS sending job handler
    jobQueue.registerHandler('send_bulk_sms', async (job) => {
      const startTime = Date.now();
      try {
        const { payloads } = job.payload as { payloads: SMSPayload[] };
        const results = await Promise.all(
          payloads.map(payload => this.sendSMSSync(payload))
        );
        
        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;
        
        return {
          success: failed === 0,
          data: { total: results.length, successful, failed, results },
          duration: Date.now() - startTime
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Bulk SMS job failed',
          duration: Date.now() - startTime
        };
      }
    });

    log('✅ SMS job handlers registered', 'dlt-sms');
  }

  private async validateTemplate(templateKey: string, variables: Record<string, string>): Promise<{ valid: boolean; error?: string; template?: SmsTemplate }> {
    try {
      const template = await cachedTemplateService.getActiveTemplateByKey(templateKey);
      
      if (!template) {
        return { valid: false, error: `Template '${templateKey}' not found or not active` };
      }

      // Check template status
      if (template.status !== 'active') {
        return { valid: false, error: `Template '${templateKey}' is not active` };
      }

      const templateVariables = template.variables as string[];
      
      // Validate required variables
      const missingVariables = templateVariables.filter(varName => !variables[varName]);
      if (missingVariables.length > 0) {
        return { valid: false, error: `Missing required variables: ${missingVariables.join(', ')}` };
      }

      // Validate variable values are not empty
      const emptyVariables = templateVariables.filter(varName => !variables[varName]?.trim());
      if (emptyVariables.length > 0) {
        return { valid: false, error: `Empty values for variables: ${emptyVariables.join(', ')}` };
      }

      return { valid: true, template };
    } catch (error) {
      log(`❌ Template validation error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dlt-sms');
      return { valid: false, error: 'Template validation failed' };
    }
  }

  private async formatMessage(template: SmsTemplate, variables: Record<string, string>): Promise<string> {
    try {
      return await cachedTemplateService.replaceTemplateVariables(template, variables);
    } catch (error) {
      throw new Error(`Message formatting failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private normalizePhoneNumber(phone: string): string {
    // Remove any non-digit characters
    const digitsOnly = phone.replace(/\D/g, '');
    
    // Handle Indian phone numbers
    if (digitsOnly.length === 10) {
      return `+91${digitsOnly}`;
    } else if (digitsOnly.length === 12 && digitsOnly.startsWith('91')) {
      return `+${digitsOnly}`;
    } else if (digitsOnly.length === 13 && digitsOnly.startsWith('91')) {
      return `+${digitsOnly.slice(1)}`;
    }
    
    // Return as is for international numbers
    return phone.startsWith('+') ? phone : `+${phone}`;
  }

  // Async SMS sending (non-blocking)
  async sendSMS(payload: SMSPayload): Promise<{ jobId: string; queued: boolean }> {
    try {
      const jobId = await jobQueue.addJob('send_sms', { payload }, {
        priority: 1,
        maxRetries: 3
      });
      
      log(`📬 SMS queued for processing: ${jobId}`, 'dlt-sms');
      return { jobId, queued: true };
    } catch (error) {
      log(`❌ Failed to queue SMS: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dlt-sms');
      throw error;
    }
  }

  // Synchronous SMS sending (for immediate results)
  async sendSMSSync(payload: SMSPayload): Promise<SMSResult> {
    let logId: number | null = null;
    
    try {
      // Validate template and variables
      const validation = await this.validateTemplate(payload.templateId, payload.variables);
      if (!validation.valid || !validation.template) {
        log(`❌ Template validation failed: ${validation.error}`, 'dlt-sms');
        return { success: false, error: validation.error || 'Template validation failed' };
      }

      const template = validation.template;
      
      // Format message with variables
      const message = await this.formatMessage(template, payload.variables);
      
      // Normalize phone number
      const normalizedPhone = this.normalizePhoneNumber(payload.to);

      // Log SMS attempt
      const smsLog = await cachedTemplateService.logSmsMessage({
        templateId: template.id,
        recipientPhone: normalizedPhone,
        messageContent: message,
        status: 'pending'
      });
      logId = smsLog.id;

      // Check if Twilio client is available
      if (!this.client) {
        // Development fallback
        if (config.isDevelopment()) {
          log(`📱 [DEV] DLT SMS fallback for ${normalizedPhone}:`, 'dlt-sms');
          log(`📱 [DEV] Message: ${message}`, 'dlt-sms');
          log(`📱 [DEV] Template ID: ${template.dltTemplateId}`, 'dlt-sms');
          
          // Update log status
          await cachedTemplateService.updateSmsLogStatus(logId, 'sent', 'dev-fallback-' + Date.now());
          
          return { success: true, messageSid: 'dev-fallback-' + Date.now() };
        } else {
          await cachedTemplateService.updateSmsLogStatus(logId, 'failed', undefined, 'SMS service not available');
          return { success: false, error: 'SMS service not available' };
        }
      }

      // Validate messaging service
      if (!config.twilio.messagingServiceSid) {
        await cachedTemplateService.updateSmsLogStatus(logId, 'failed', undefined, 'Twilio messaging service not configured');
        return { success: false, error: 'Twilio messaging service not configured' };
      }

      // Send SMS via Twilio with DLT compliance
      const messageOptions: any = {
        body: message,
        to: normalizedPhone,
        messagingServiceSid: config.twilio.messagingServiceSid,
        statusCallback: `https://your-domain.com/api/twilio/sms-status`,
      };

      // Always use message body for DLT compliance (Content Templates cause issues)
      // The DLT compliance is handled through the messaging service configuration
      log(`📱 Sending DLT compliant SMS via Messaging Service`, 'dlt-sms');
      log(`📱 DLT Template ID (telecom reference): ${template.dltTemplateId}`, 'dlt-sms');
      log(`📱 DLT Entity ID: ${config.dlt.entityId}`, 'dlt-sms');
      log(`📱 Message content: ${message}`, 'dlt-sms');
      log(`📱 Status callback: ${messageOptions.statusCallback}`, 'dlt-sms');

      const result = await this.client.messages.create(messageOptions);

      // Update log with success
      await cachedTemplateService.updateSmsLogStatus(logId, 'sent', result.sid);

      log(`✅ DLT SMS sent successfully: ${result.sid}`, 'dlt-sms');
      log(`📱 Message: ${message}`, 'dlt-sms');
      log(`📱 To: ${normalizedPhone}`, 'dlt-sms');
      log(`📱 Template ID: ${template.dltTemplateId}`, 'dlt-sms');

      return { success: true, messageSid: result.sid };

    } catch (error) {
      log(`❌ DLT SMS sending failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dlt-sms');
      console.error('DLT SMS sending error details:', error);
      
      // Update log with failure
      if (logId) {
        await cachedTemplateService.updateSmsLogStatus(
          logId, 
          'failed', 
          undefined, 
          error instanceof Error ? error.message : 'Unknown error occurred'
        );
      }
      
      // Development fallback on error
      if (config.isDevelopment()) {
        try {
          const template = await cachedTemplateService.getActiveTemplateByKey(payload.templateId);
          if (template) {
            const message = await this.formatMessage(template, payload.variables);
            const normalizedPhone = this.normalizePhoneNumber(payload.to);
            log(`📱 [DEV FALLBACK] SMS for ${normalizedPhone}: ${message}`, 'dlt-sms');
            
            if (logId) {
              await cachedTemplateService.updateSmsLogStatus(logId, 'sent', 'dev-error-fallback-' + Date.now());
            }
            
            return { success: true, messageSid: 'dev-error-fallback-' + Date.now() };
          }
        } catch (fallbackError) {
          log(`❌ Development fallback also failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`, 'dlt-sms');
        }
      }
      
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  async sendBookingConfirmation(phone: string, propertyName: string, bookingDate: string): Promise<{ jobId: string; queued: boolean }> {
    return this.sendSMS({
      to: phone,
      templateId: 'booking_confirmation',
      variables: {
        property_name: propertyName,
        booking_date: bookingDate
      }
    });
  }

  // Sync version for immediate confirmation needs
  async sendBookingConfirmationSync(phone: string, propertyName: string, bookingDate: string): Promise<SMSResult> {
    return this.sendSMSSync({
      to: phone,
      templateId: 'booking_confirmation',
      variables: {
        property_name: propertyName,
        booking_date: bookingDate
      }
    });
  }

  async sendPaymentConfirmation(phone: string, amount: string, propertyName: string): Promise<{ jobId: string; queued: boolean }> {
    return this.sendSMS({
      to: phone,
      templateId: 'payment_confirmation',
      variables: {
        amount: amount,
        property_name: propertyName
      }
    });
  }

  // Sync version for immediate confirmation needs
  async sendPaymentConfirmationSync(phone: string, amount: string, propertyName: string): Promise<SMSResult> {
    return this.sendSMSSync({
      to: phone,
      templateId: 'payment_confirmation',
      variables: {
        amount: amount,
        property_name: propertyName
      }
    });
  }

  // OTP verification methods (priority - send sync for immediate delivery)
  async sendOTPVerification(phone: string, otpCode: string, validityMinutes: string): Promise<SMSResult> {
    return this.sendSMSSync({
      to: phone,
      templateId: 'otp_verification',
      variables: {
        otp_code: otpCode,
        validity_minutes: validityMinutes
      }
    });
  }

  async sendOTP(phone: string, otpCode: string): Promise<SMSResult> {
    return this.sendSMSSync({
      to: phone,
      templateId: 'otp_verification',
      variables: {
        otp_code: otpCode
      }
    });
  }

  // Service availability check
  isAvailable(): boolean {
    return this.client !== null && config.twilio.available;
  }

  // Bulk SMS sending (async)
  async sendBulkSMS(payloads: SMSPayload[]): Promise<{ jobId: string; queued: boolean; count: number }> {
    try {
      const jobId = await jobQueue.addJob('send_bulk_sms', { payloads }, {
        priority: 0, // Lower priority than individual SMS
        maxRetries: 2
      });
      
      log(`📬 Bulk SMS (${payloads.length} messages) queued: ${jobId}`, 'dlt-sms');
      return { jobId, queued: true, count: payloads.length };
    } catch (error) {
      log(`❌ Failed to queue bulk SMS: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dlt-sms');
      throw error;
    }
  }

  // Get SMS job status
  async getSMSJobStatus(jobId: string): Promise<{
    status: 'queued' | 'processing' | 'completed' | 'failed' | 'not_found';
    result?: any;
  }> {
    return jobQueue.getJobStatus(jobId);
  }

  // Wait for SMS job completion
  async waitForSMSJob(jobId: string, timeoutMs: number = 30000): Promise<any> {
    return jobQueue.waitForJob(jobId, timeoutMs);
  }

  // Utility methods
  async getAvailableTemplates(): Promise<string[]> {
    try {
      const templates = await cachedTemplateService.getAllTemplates('active');
      return templates.map(t => t.key);
    } catch (error) {
      log(`❌ Failed to get available templates: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dlt-sms');
      return [];
    }
  }

  async getTemplateInfo(templateKey: string): Promise<SmsTemplate | null> {
    try {
      return await cachedTemplateService.getActiveTemplateByKey(templateKey);
    } catch (error) {
      log(`❌ Failed to get template info: ${error instanceof Error ? error.message : 'Unknown error'}`, 'dlt-sms');
      return null;
    }
  }

  // Development/testing utilities
  async testConnection(): Promise<boolean> {
    if (!this.client) {
      log('⚠️ Cannot test connection - Twilio client not initialized', 'dlt-sms');
      return false;
    }

    try {
      if (!config.twilio.accountSid) {
        log('❌ Twilio Account SID not configured', 'dlt-sms');
        return false;
      }
      
      await this.client.api.accounts(config.twilio.accountSid).fetch();
      log('✅ Twilio connection test successful', 'dlt-sms');
      return true;
    } catch (error) {
      log('❌ Twilio connection test failed', 'dlt-sms');
      console.error('Twilio connection test error:', error);
      return false;
    }
  }

  // Service health check
  async getServiceHealth(): Promise<{
    twilioConnected: boolean;
    dltConfigured: boolean;
    templatesLoaded: number;
    availableTemplates: string[];
    jobQueueMetrics: any;
    cacheMetrics: any;
  }> {
    const availableTemplates = await this.getAvailableTemplates();
    return {
      twilioConnected: this.client !== null,
      dltConfigured: config.dlt.available,
      templatesLoaded: availableTemplates.length,
      availableTemplates: availableTemplates,
      jobQueueMetrics: jobQueue.getMetrics(),
      cacheMetrics: cachedTemplateService.getCacheMetrics()
    };
  }
}

export const dltSMSService = new DLTSMSService();
export { DLTSMSService, SMSResult, SMSPayload };