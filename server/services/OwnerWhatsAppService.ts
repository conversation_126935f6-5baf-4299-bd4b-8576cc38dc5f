import { logger } from './LoggerService';
import { whatsAppService } from './WhatsAppService';
import { whatsAppCalendarService } from './WhatsAppCalendarService';
import { whatsAppMessageParser } from './WhatsAppMessageParser';
import { db } from '../db';
import { properties, users } from '@shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import { calendarService } from './CalendarService';

/**
 * Owner-specific WhatsApp Booking Service
 * Handles bookings directly with property owners
 */

export interface OwnerBookingFlow {
  customerId: string; // Customer's WhatsApp number
  ownerId: number;    // Property owner's ID
  ownerWhatsApp: string; // Owner's WhatsApp number
  step: 'property_inquiry' | 'property_selection' | 'date_selection' | 'stay_type_selection' | 'guest_details' | 'confirmation' | 'completed';
  data: {
    propertyId?: number;
    propertyName?: string;
    checkIn?: string;
    checkOut?: string;
    stayType?: 'morning' | 'full_day';
    guestCount?: number;
    customerName?: string;
    customerPhone?: string;
    specialRequests?: string;
    totalPrice?: number;
    pendingProperties?: any[];
  };
  startedAt: Date;
  lastUpdated: Date;
}

export class OwnerWhatsAppService {
  private activeBookings: Map<string, OwnerBookingFlow> = new Map();

  constructor() {
    logger.info('Owner WhatsApp Service initialized');
  }

  /**
   * Find owner by WhatsApp number
   */
  async findOwnerByWhatsApp(whatsappNumber: string): Promise<any | null> {
    try {
      // Clean the WhatsApp number format
      const cleanNumber = whatsappNumber.replace('whatsapp:', '').replace('+', '');
      
      // Try different number formats
      const numberVariants = [
        cleanNumber,
        `+${cleanNumber}`,
        `91${cleanNumber.slice(-10)}`, // Add India code if not present
        cleanNumber.slice(-10) // Just the 10-digit number
      ];

      for (const variant of numberVariants) {
        // First check WhatsApp number field
        const ownerByWhatsApp = await db.select({
          id: users.id,
          fullName: users.fullName,
          phone: users.phone,
          whatsappNumber: users.whatsappNumber,
          role: users.role
        })
        .from(users)
        .where(and(
          eq(users.whatsappNumber, variant),
          eq(users.role, 'owner')
        ))
        .limit(1);

        if (ownerByWhatsApp.length > 0) {
          return ownerByWhatsApp[0];
        }

        // Fallback to phone field if WhatsApp not set
        const ownerByPhone = await db.select({
          id: users.id,
          fullName: users.fullName,
          phone: users.phone,
          whatsappNumber: users.whatsappNumber,
          role: users.role
        })
        .from(users)
        .where(and(
          eq(users.phone, variant),
          eq(users.role, 'owner')
        ))
        .limit(1);

        if (ownerByPhone.length > 0) {
          return ownerByPhone[0];
        }
      }

      return null;
    } catch (error) {
      logger.error('Error finding owner by WhatsApp:', error as Error);
      return null;
    }
  }

  /**
   * Get owner's properties
   */
  async getOwnerProperties(ownerId: number): Promise<any[]> {
    try {
      const ownerProperties = await db.select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        location: properties.location,
        halfDayPrice: properties.halfDayPrice,
        fullDayPrice: properties.fullDayPrice,
        weekdayHalfDayPrice: properties.weekdayHalfDayPrice,
        weekdayFullDayPrice: properties.weekdayFullDayPrice,
        weekendHalfDayPrice: properties.weekendHalfDayPrice,
        weekendFullDayPrice: properties.weekendFullDayPrice,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        amenities: properties.amenities,
        status: properties.status
      })
      .from(properties)
      .where(and(
        eq(properties.ownerId, ownerId),
        eq(properties.status, 'active')
      ))
      .orderBy(desc(properties.featured), properties.title);

      return ownerProperties;
    } catch (error) {
      logger.error('Error fetching owner properties:', error as Error);
      return [];
    }
  }

  /**
   * Process customer message to owner
   */
  async processCustomerMessage(
    customerWhatsApp: string,
    ownerWhatsApp: string, 
    message: string
  ): Promise<void> {
    try {
      // Find the owner
      const owner = await this.findOwnerByWhatsApp(ownerWhatsApp);
      
      if (!owner) {
        await whatsAppService.sendMessage(
          customerWhatsApp,
          `❌ Sorry, I couldn't find the property owner. Please check the number and try again.`
        );
        return;
      }

      // Parse the message for direct booking commands like "Book August 20 to August 22 for John"
      const parsedCommand = whatsAppMessageParser.parseMessage(message);
      
      if (parsedCommand.intent === 'booking' && 
          parsedCommand.confidence > 0.7 && 
          parsedCommand.entities.dates?.checkIn && 
          parsedCommand.entities.dates?.checkOut &&
          parsedCommand.entities.guestName) {
        
        // Handle direct booking command
        await this.handleDirectBookingCommand(customerWhatsApp, owner, parsedCommand);
        return;
      }

      // Check if there's an active booking flow
      const flowKey = `${customerWhatsApp}_${owner.id}`;
      let bookingFlow = this.activeBookings.get(flowKey);

      if (!bookingFlow) {
        // Start new booking flow
        bookingFlow = await this.startOwnerBookingFlow(customerWhatsApp, owner, ownerWhatsApp);
      }

      // Process the message based on current step
      await this.processBookingStep(bookingFlow, message, customerWhatsApp);

    } catch (error) {
      logger.error('Error processing customer message:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Sorry, something went wrong. Please try again.`
      );
    }
  }

  /**
   * Handle direct booking command like "Book August 20 to August 22 for John"
   */
  private async handleDirectBookingCommand(
    customerWhatsApp: string,
    owner: any,
    parsedCommand: any
  ): Promise<void> {
    try {
      // Get owner's properties
      const properties = await this.getOwnerProperties(owner.id);
      
      if (properties.length === 0) {
        await whatsAppService.sendMessage(
          customerWhatsApp,
          `❌ Sorry, ${owner.fullName} doesn't have any active properties for booking.`
        );
        return;
      }

      // If owner has only one property, proceed with that
      if (properties.length === 1) {
        await this.createDirectBooking(customerWhatsApp, owner, properties[0], parsedCommand);
        return;
      }

      // If multiple properties, ask customer to choose
      let propertyList = `📋 ${owner.fullName} has multiple properties. Please choose one:\n\n`;
      properties.forEach((property, index) => {
        propertyList += `${index + 1}. ${property.title}\n`;
        propertyList += `   📍 ${property.location}\n`;
        if (property.fullDayPrice) {
          propertyList += `   💰 ₹${property.fullDayPrice}/day\n`;
        }
        propertyList += '\n';
      });
      
      propertyList += `💬 Reply with the number of your choice to book "${parsedCommand.entities.guestName}" for ${parsedCommand.entities.dates?.rawDates}.`;

      await whatsAppService.sendMessage(customerWhatsApp, propertyList);

      // Store the pending booking request
      const flowKey = `${customerWhatsApp}_${owner.id}`;
      const bookingFlow = {
        customerId: customerWhatsApp,
        ownerId: owner.id,
        ownerWhatsApp: owner.whatsappNumber || owner.phone,
        step: 'property_selection' as const,
        data: {
          checkIn: parsedCommand.entities.dates?.checkIn?.toISOString().split('T')[0],
          checkOut: parsedCommand.entities.dates?.checkOut?.toISOString().split('T')[0],
          customerName: parsedCommand.entities.guestName,
          pendingProperties: properties
        },
        startedAt: new Date(),
        lastUpdated: new Date()
      };
      
      this.activeBookings.set(flowKey, bookingFlow);

    } catch (error) {
      logger.error('Error handling direct booking command:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Sorry, something went wrong while processing your booking request. Please try again.`
      );
    }
  }

  /**
   * Create a direct booking for a specific property
   */
  private async createDirectBooking(
    customerWhatsApp: string,
    owner: any,
    property: any,
    parsedCommand: any
  ): Promise<void> {
    try {
      const checkIn = parsedCommand.entities.dates?.checkIn;
      const checkOut = parsedCommand.entities.dates?.checkOut;
      const guestName = parsedCommand.entities.guestName;

      // Check availability first
      const availability = await calendarService.checkAvailability(
        property.id,
        checkIn.toISOString().split('T')[0],
        checkOut.toISOString().split('T')[0]
      );

      if (!availability.isAvailable) {
        await whatsAppService.sendMessage(
          customerWhatsApp,
          `❌ Sorry, "${property.title}" is not available from ${checkIn.toDateString()} to ${checkOut.toDateString()}.\n\n` +
          `📅 Please check our calendar for available dates or try different dates.`
        );
        return;
      }

      // Create the booking
      const bookingData = {
        propertyId: property.id,
        startDate: checkIn.toISOString().split('T')[0],
        endDate: checkOut.toISOString().split('T')[0],
        guestName: guestName,
        guestPhone: customerWhatsApp.replace('whatsapp:', ''),
        status: 'confirmed' as const,
        source: 'whatsapp' as const,
        bookingType: 'full_day' as const,
        createdBy: owner.id,
        notes: `Booking created via WhatsApp direct command`
      };

      const booking = await calendarService.createCalendarBooking(bookingData);

      // Send confirmation message
      const confirmationMessage = 
        `✅ Booking Confirmed!\n\n` +
        `🏡 Property: ${property.title}\n` +
        `📅 Dates: ${checkIn.toDateString()} to ${checkOut.toDateString()}\n` +
        `👤 Guest: ${guestName}\n` +
        `📱 Reference: #${booking.id}\n\n` +
        `📞 For any changes, contact ${owner.fullName} directly.\n\n` +
        `🔗 View calendar: https://bookafarm.com/calendar/${property.id}`;

      await whatsAppService.sendMessage(customerWhatsApp, confirmationMessage);

      logger.info('Direct WhatsApp booking created', 'owner-whatsapp', {
        bookingId: booking.id,
        propertyId: property.id,
        customer: customerWhatsApp,
        guestName: guestName,
        dates: `${checkIn.toDateString()} to ${checkOut.toDateString()}`
      });

    } catch (error) {
      logger.error('Error creating direct booking:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Sorry, there was an error creating your booking. Please try again or contact the owner directly.`
      );
    }
  }

  /**
   * Start new booking flow with owner
   */
  private async startOwnerBookingFlow(
    customerWhatsApp: string,
    owner: any,
    ownerWhatsApp: string
  ): Promise<OwnerBookingFlow> {
    const bookingFlow: OwnerBookingFlow = {
      customerId: customerWhatsApp,
      ownerId: owner.id,
      ownerWhatsApp,
      step: 'property_inquiry',
      data: {},
      startedAt: new Date(),
      lastUpdated: new Date()
    };

    const flowKey = `${customerWhatsApp}_${owner.id}`;
    this.activeBookings.set(flowKey, bookingFlow);

    // Send welcome message and property list
    await this.sendPropertyList(customerWhatsApp, owner);

    logger.info('Started owner booking flow', 'owner-whatsapp', {
      customer: customerWhatsApp,
      owner: owner.fullName,
      ownerId: owner.id
    });

    return bookingFlow;
  }

  /**
   * Send property list to customer
   */
  private async sendPropertyList(customerWhatsApp: string, owner: any): Promise<void> {
    const properties = await this.getOwnerProperties(owner.id);

    if (properties.length === 0) {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `🏡 Welcome to ${owner.fullName}'s properties!\n\n` +
        `❌ Sorry, no properties are currently available for booking.\n\n` +
        `Please contact the owner directly for more information.`
      );
      return;
    }

    let message = `🏡 Welcome to ${owner.fullName}'s Farmhouse Rentals!\n`;
    message += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    message += `📋 Available Properties:\n\n`;

    properties.forEach((property, index) => {
      message += `${index + 1}. **${property.title}**\n`;
      message += `   📍 ${property.location}\n`;
      message += `   🛏️ ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms\n`;
      message += `   💰 Morning: ₹${property.halfDayPrice}/day\n`;
      message += `   💰 Full Day: ₹${property.fullDayPrice}/day\n`;
      
      if (property.amenities && Array.isArray(property.amenities)) {
        const amenitiesText = property.amenities.slice(0, 3).join(', ');
        message += `   ✨ ${amenitiesText}${property.amenities.length > 3 ? '...' : ''}\n`;
      }
      message += '\n';
    });

    message += `📝 **To book:**\n`;
    message += `Reply with the property number (1, 2, 3...) you'd like to book\n\n`;
    message += `💡 Or type "info 1" to get more details about a property`;

    await whatsAppService.sendMessage(customerWhatsApp, message);
  }

  /**
   * Process booking step
   */
  private async processBookingStep(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const flowKey = `${customerWhatsApp}_${bookingFlow.ownerId}`;
    
    switch (bookingFlow.step) {
      case 'property_inquiry':
        await this.handlePropertySelection(bookingFlow, message, customerWhatsApp);
        break;
      
      case 'date_selection':
        await this.handleDateSelection(bookingFlow, message, customerWhatsApp);
        break;
      
      case 'stay_type_selection':
        await this.handleStayTypeSelection(bookingFlow, message, customerWhatsApp);
        break;

      case 'guest_details':
        await this.handleGuestDetails(bookingFlow, message, customerWhatsApp);
        break;

      case 'confirmation':
        await this.handleConfirmation(bookingFlow, message, customerWhatsApp);
        break;
      
      default:
        await whatsAppService.sendMessage(
          customerWhatsApp,
          `❓ I didn't understand that. Type "start" to begin booking again.`
        );
    }

    // Update flow
    bookingFlow.lastUpdated = new Date();
    this.activeBookings.set(flowKey, bookingFlow);
  }

  /**
   * Handle property selection
   */
  private async handlePropertySelection(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const ownerProperties = await this.getOwnerProperties(bookingFlow.ownerId);
    
    // Check if asking for more info
    if (message.toLowerCase().startsWith('info ')) {
      const propertyIndex = parseInt(message.split(' ')[1]) - 1;
      if (propertyIndex >= 0 && propertyIndex < ownerProperties.length) {
        await this.sendPropertyDetails(customerWhatsApp, ownerProperties[propertyIndex]);
        return;
      }
    }

    // Check property selection
    const selectedIndex = parseInt(message) - 1;
    
    if (selectedIndex >= 0 && selectedIndex < ownerProperties.length) {
      const selectedProperty = ownerProperties[selectedIndex];
      
      // Store property selection
      bookingFlow.data.propertyId = selectedProperty.id;
      bookingFlow.data.propertyName = selectedProperty.title;
      bookingFlow.step = 'date_selection';

      // Show calendar with property confirmation
      const today = new Date();
      const calendarView = await whatsAppCalendarService.generateCalendarView(
        selectedProperty.id,
        today.getMonth() + 1,
        today.getFullYear()
      );

      let dateMessage = `✅ Great choice! **${selectedProperty.title}**\n\n`;
      dateMessage += `📅 **Select Your Dates**\n`;
      dateMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      dateMessage += calendarView;
      dateMessage += `\n📋 **How to book:**\n`;
      dateMessage += `• Reply with day number (e.g., "15")\n`;
      dateMessage += `• For range: "15-18" or "15 to 18"\n`;
      dateMessage += `• Type "tomorrow" for next day\n`;
      dateMessage += `• Type "weekend" for this weekend\n`;
      dateMessage += `• Type "next" for next month calendar\n`;
      dateMessage += `• Type "prev" for previous month calendar\n\n`;
      dateMessage += `💡 Please select your dates from the calendar above`;

      await whatsAppService.sendMessage(customerWhatsApp, dateMessage);
    } else {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Please select a valid property number (1-${ownerProperties.length}) or type "info X" for details.`
      );
    }
  }

  /**
   * Send detailed property information
   */
  private async sendPropertyDetails(customerWhatsApp: string, property: any): Promise<void> {
    let detailMessage = `🏡 **${property.title}**\n`;
    detailMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    detailMessage += `📍 **Location:** ${property.location}\n`;
    detailMessage += `🛏️ **Accommodation:** ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms\n\n`;
    
    detailMessage += `💰 **Pricing:**\n`;
    detailMessage += `• Morning Visit: ₹${property.halfDayPrice}\n`;
    detailMessage += `• Full Day: ₹${property.fullDayPrice}\n`;
    
    if (property.weekdayHalfDayPrice) {
      detailMessage += `• Weekday Morning: ₹${property.weekdayHalfDayPrice}\n`;
      detailMessage += `• Weekday Full Day: ₹${property.weekdayFullDayPrice}\n`;
    }
    
    detailMessage += `\n📝 **Description:**\n${property.description}\n\n`;
    
    if (property.amenities && Array.isArray(property.amenities)) {
      detailMessage += `✨ **Amenities:**\n`;
      property.amenities.forEach((amenity: string) => {
        detailMessage += `• ${amenity}\n`;
      });
      detailMessage += '\n';
    }
    
    detailMessage += `📞 Reply with the property number to book this farmhouse!`;

    await whatsAppService.sendMessage(customerWhatsApp, detailMessage);
  }

  /**
   * Handle date selection with interactive calendar
   */
  private async handleDateSelection(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const lowerMessage = message.toLowerCase().trim();
    const propertyId = bookingFlow.data.propertyId!;

    // Handle navigation commands
    if (lowerMessage === 'calendar' || lowerMessage === 'cal') {
      const today = new Date();
      const calendarView = await whatsAppCalendarService.generateCalendarView(
        propertyId, 
        today.getMonth() + 1, 
        today.getFullYear()
      );
      await whatsAppService.sendMessage(customerWhatsApp, calendarView);
      return;
    }

    if (lowerMessage === 'next' || lowerMessage === 'next month') {
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      const calendarView = await whatsAppCalendarService.generateCalendarView(
        propertyId,
        nextMonth.getMonth() + 1,
        nextMonth.getFullYear()
      );
      await whatsAppService.sendMessage(customerWhatsApp, calendarView);
      return;
    }

    if (lowerMessage === 'prev' || lowerMessage === 'previous' || lowerMessage === 'prev month') {
      const prevMonth = new Date();
      prevMonth.setMonth(prevMonth.getMonth() - 1);
      const calendarView = await whatsAppCalendarService.generateCalendarView(
        propertyId,
        prevMonth.getMonth() + 1,
        prevMonth.getFullYear()
      );
      await whatsAppService.sendMessage(customerWhatsApp, calendarView);
      return;
    }

    // Handle quick date options
    const quickOption = whatsAppCalendarService.parseQuickOption(lowerMessage);
    if (quickOption.valid) {
      if (quickOption.showCalendar) {
        const today = new Date();
        const calendarView = await whatsAppCalendarService.generateCalendarView(
          propertyId,
          today.getMonth() + 1,
          today.getFullYear()
        );
        await whatsAppService.sendMessage(customerWhatsApp, calendarView);
        return;
      }

      if (quickOption.startDate && quickOption.endDate) {
        // Check availability for quick selection
        const availability = await whatsAppCalendarService.checkAvailability(
          propertyId,
          quickOption.startDate,
          quickOption.endDate
        );

        if (availability.available) {
          // Store dates and move to next step
          bookingFlow.data.checkIn = quickOption.startDate.toISOString().split('T')[0];
          bookingFlow.data.checkOut = quickOption.endDate.toISOString().split('T')[0];
          bookingFlow.step = 'guest_details';
          await this.askForGuestDetails(bookingFlow, customerWhatsApp);
        } else {
          await whatsAppService.sendMessage(customerWhatsApp, availability.message);
        }
        return;
      }
    }

    // Parse date selection from calendar
    const today = new Date();
    const dateSelection = whatsAppCalendarService.parseDateSelection(
      message,
      today.getMonth() + 1,
      today.getFullYear()
    );

    if (!dateSelection.valid) {
      const quickOptions = whatsAppCalendarService.generateQuickDateOptions();
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `${dateSelection.error || '❌ Invalid date format'}\n\n${quickOptions}`
      );
      return;
    }

    // Check availability for selected dates
    const availability = await whatsAppCalendarService.checkAvailability(
      propertyId,
      dateSelection.startDate!,
      dateSelection.endDate!
    );

    if (availability.available) {
      // Store dates and ask for stay type
      bookingFlow.data.checkIn = dateSelection.startDate!.toISOString().split('T')[0];
      bookingFlow.data.checkOut = dateSelection.endDate!.toISOString().split('T')[0];
      
      const stayTypeMessage = `${availability.message}\n\n` +
        `⏰ **Choose your stay type:**\n\n` +
        `1. **Morning Visit** (9 AM - 6 PM)\n` +
        `2. **Full Day** (Check-in 2 PM, checkout next day)\n\n` +
        `Reply with "1" or "2"`;
      
      await whatsAppService.sendMessage(customerWhatsApp, stayTypeMessage);
      
      // Set a temporary step to handle stay type selection
      bookingFlow.step = 'stay_type_selection' as any;
    } else {
      await whatsAppService.sendMessage(customerWhatsApp, availability.message);
      
      // Show calendar again for alternative dates
      const calendarView = await whatsAppCalendarService.generateCalendarView(
        propertyId,
        today.getMonth() + 1,
        today.getFullYear()
      );
      await whatsAppService.sendMessage(customerWhatsApp, 
        `Please choose alternative dates:\n\n${calendarView}`
      );
    }
  }

  /**
   * Handle stay type selection
   */
  private async handleStayTypeSelection(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const choice = message.trim();

    if (choice === '1' || choice.toLowerCase().includes('morning')) {
      bookingFlow.data.stayType = 'morning';
      bookingFlow.step = 'guest_details';
      await this.askForGuestDetails(bookingFlow, customerWhatsApp);
    } else if (choice === '2' || choice.toLowerCase().includes('full')) {
      bookingFlow.data.stayType = 'full_day';
      bookingFlow.step = 'guest_details';
      await this.askForGuestDetails(bookingFlow, customerWhatsApp);
    } else {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Please choose a valid option:\n\n` +
        `1. **Morning Visit** (9 AM - 6 PM)\n` +
        `2. **Full Day** (Check-in 2 PM, checkout next day)\n\n` +
        `Reply with "1" or "2"`
      );
    }
  }

  /**
   * Ask for guest details
   */
  private async askForGuestDetails(
    bookingFlow: OwnerBookingFlow,
    customerWhatsApp: string
  ): Promise<void> {
    let guestMessage = `📅 **Dates confirmed:**\n`;
    guestMessage += `Check-in: ${bookingFlow.data.checkIn}\n`;
    if (bookingFlow.data.checkOut && bookingFlow.data.checkOut !== bookingFlow.data.checkIn) {
      guestMessage += `Check-out: ${bookingFlow.data.checkOut}\n`;
    }
    guestMessage += `Stay type: ${bookingFlow.data.stayType === 'morning' ? 'Morning Visit' : 'Full Day'}\n\n`;

    guestMessage += `👥 **Guest Information needed:**\n`;
    guestMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    guestMessage += `Please provide the following details:\n\n`;
    guestMessage += `📝 **Format:**\n`;
    guestMessage += `Name: [Your full name]\n`;
    guestMessage += `Phone: [Your contact number]\n`;
    guestMessage += `Guests: [Number of guests]\n\n`;
    guestMessage += `💡 **Example:**\n`;
    guestMessage += `Name: Rajesh Kumar\n`;
    guestMessage += `Phone: 9876543210\n`;
    guestMessage += `Guests: 8\n\n`;
    guestMessage += `📞 You can also add any special requests!`;

    await whatsAppService.sendMessage(customerWhatsApp, guestMessage);
  }



  /**
   * Handle guest details
   */
  private async handleGuestDetails(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    // Parse guest information
    const guestInfo = this.parseGuestInfo(message);
    
    if (!guestInfo.name || !guestInfo.phone || !guestInfo.guestCount) {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Please provide all required information:\n\n` +
        `Name: [Your full name]\n` +
        `Phone: [Your contact number]\n` +
        `Guests: [Number of guests]\n\n` +
        `You can also add special requests on separate lines.`
      );
      return;
    }

    // Store guest information
    bookingFlow.data.customerName = guestInfo.name;
    bookingFlow.data.customerPhone = guestInfo.phone;
    bookingFlow.data.guestCount = guestInfo.guestCount;
    bookingFlow.data.specialRequests = guestInfo.specialRequests;

    // Calculate pricing and move to confirmation
    await this.calculatePriceAndConfirm(bookingFlow, customerWhatsApp);
  }

  /**
   * Parse guest information from message
   */
  private parseGuestInfo(message: string): {
    name: string;
    phone: string;
    guestCount: number;
    specialRequests: string;
  } {
    let name = '';
    let phone = '';
    let guestCount = 0;
    let specialRequests = '';

    const lines = message.split('\n');
    let otherLines: string[] = [];

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.toLowerCase().startsWith('name:')) {
        name = trimmedLine.substring(5).trim();
      } else if (trimmedLine.toLowerCase().startsWith('phone:')) {
        phone = trimmedLine.substring(6).trim().replace(/\D/g, ''); // Remove non-digits
      } else if (trimmedLine.toLowerCase().startsWith('guests:')) {
        guestCount = parseInt(trimmedLine.substring(7).trim()) || 0;
      } else if (trimmedLine.length > 0) {
        otherLines.push(trimmedLine);
      }
    }

    // If no structured format, try to extract from free text
    if (!name) {
      const nameMatch = message.match(/([A-Za-z\s]{2,30})/);
      if (nameMatch) name = nameMatch[1].trim();
    }

    if (!phone) {
      const phoneMatch = message.match(/(\d{10,12})/);
      if (phoneMatch) phone = phoneMatch[1];
    }

    if (!guestCount) {
      const guestMatch = message.match(/(\d+)\s*(guest|people|person)/i);
      if (guestMatch) guestCount = parseInt(guestMatch[1]);
    }

    specialRequests = otherLines.join(' ').trim();

    return { name, phone, guestCount, specialRequests };
  }

  /**
   * Calculate price and send confirmation
   */
  private async calculatePriceAndConfirm(
    bookingFlow: OwnerBookingFlow,
    customerWhatsApp: string
  ): Promise<void> {
    try {
      // Get property details for pricing
      const property = await db.select()
        .from(properties)
        .where(eq(properties.id, bookingFlow.data.propertyId!))
        .limit(1);

      if (property.length === 0) {
        throw new Error('Property not found');
      }

      const prop = property[0];
      const isWeekend = this.isWeekendDate(bookingFlow.data.checkIn!);
      
      // Calculate base price
      let basePrice = 0;
      if (bookingFlow.data.stayType === 'morning') {
        basePrice = isWeekend && prop.weekendHalfDayPrice 
          ? prop.weekendHalfDayPrice 
          : prop.weekdayHalfDayPrice || prop.halfDayPrice;
      } else {
        basePrice = isWeekend && prop.weekendFullDayPrice 
          ? prop.weekendFullDayPrice 
          : prop.weekdayFullDayPrice || prop.fullDayPrice;
      }

      // Calculate total days
      const checkInDate = new Date(bookingFlow.data.checkIn!);
      const checkOutDate = new Date(bookingFlow.data.checkOut || bookingFlow.data.checkIn!);
      const days = Math.max(1, Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
      
      const totalPrice = basePrice * days;
      bookingFlow.data.totalPrice = totalPrice;
      bookingFlow.step = 'confirmation';

      // Send confirmation message
      let confirmMessage = `✅ **Booking Summary**\n`;
      confirmMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      confirmMessage += `🏡 **Property:** ${bookingFlow.data.propertyName}\n`;
      confirmMessage += `📅 **Date:** ${bookingFlow.data.checkIn}`;
      if (bookingFlow.data.checkOut && bookingFlow.data.checkOut !== bookingFlow.data.checkIn) {
        confirmMessage += ` to ${bookingFlow.data.checkOut}`;
      }
      confirmMessage += `\n⏰ **Stay:** ${bookingFlow.data.stayType === 'morning' ? 'Morning Visit' : 'Full Day'}\n`;
      confirmMessage += `👤 **Guest:** ${bookingFlow.data.customerName}\n`;
      confirmMessage += `📞 **Phone:** ${bookingFlow.data.customerPhone}\n`;
      confirmMessage += `👥 **Guests:** ${bookingFlow.data.guestCount}\n`;
      
      if (bookingFlow.data.specialRequests) {
        confirmMessage += `📝 **Special Requests:** ${bookingFlow.data.specialRequests}\n`;
      }
      
      confirmMessage += `\n💰 **Total Amount:** ₹${totalPrice}\n`;
      confirmMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      confirmMessage += `✅ **To confirm booking, reply:** "CONFIRM"\n`;
      confirmMessage += `❌ **To cancel, reply:** "CANCEL"\n`;
      confirmMessage += `✏️ **To modify, reply:** "CHANGE"`;

      await whatsAppService.sendMessage(customerWhatsApp, confirmMessage);

    } catch (error) {
      logger.error('Error calculating price:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Error calculating booking price. Please try again.`
      );
    }
  }

  /**
   * Handle confirmation
   */
  private async handleConfirmation(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const lowerMessage = message.toLowerCase().trim();

    if (lowerMessage === 'confirm' || lowerMessage === 'yes') {
      // Create the booking
      await this.createBooking(bookingFlow, customerWhatsApp);
    } else if (lowerMessage === 'cancel' || lowerMessage === 'no') {
      // Cancel the booking flow
      const flowKey = `${customerWhatsApp}_${bookingFlow.ownerId}`;
      this.activeBookings.delete(flowKey);
      
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Booking cancelled. Thank you for your interest! You can start a new booking anytime by messaging again.`
      );
    } else if (lowerMessage === 'change' || lowerMessage === 'modify') {
      // Reset to property selection
      bookingFlow.step = 'property_inquiry';
      bookingFlow.data = {};
      
      const owner = await db.select().from(users).where(eq(users.id, bookingFlow.ownerId)).limit(1);
      if (owner.length > 0) {
        await this.sendPropertyList(customerWhatsApp, owner[0]);
      }
    } else {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `Please reply with:\n• "CONFIRM" to confirm booking\n• "CANCEL" to cancel\n• "CHANGE" to modify details`
      );
    }
  }

  /**
   * Create the actual booking and add to calendar
   */
  private async createBooking(bookingFlow: OwnerBookingFlow, customerWhatsApp: string): Promise<void> {
    try {
      const bookingRef = `WA${Date.now()}`;
      
      // Create or find customer user
      let customerId: number | undefined;
      const existingCustomer = await db.select()
        .from(users)
        .where(eq(users.phone, bookingFlow.data.customerPhone!))
        .limit(1);
      
      if (existingCustomer.length > 0) {
        customerId = existingCustomer[0].id;
      } else {
        // Create new customer user
        const [newCustomer] = await db.insert(users)
          .values({
            username: `customer_${bookingFlow.data.customerPhone}`,
            password: 'temp_password', // Will need to be changed
            email: `${bookingFlow.data.customerPhone}@whatsapp.com`,
            fullName: bookingFlow.data.customerName!,
            phone: bookingFlow.data.customerPhone!,
            role: 'user' as const
          })
          .returning();
        customerId = newCustomer.id;
      }

      // Add booking to calendar
      const calendarBooking = await calendarService.createCalendarBooking({
        propertyId: bookingFlow.data.propertyId!,
        startDate: bookingFlow.data.checkIn!,
        endDate: bookingFlow.data.checkOut || bookingFlow.data.checkIn!,
        guestName: bookingFlow.data.customerName!,
        guestPhone: bookingFlow.data.customerPhone!,
        guestCount: bookingFlow.data.guestCount!,
        bookingType: bookingFlow.data.stayType === 'morning' ? 'Morning Visit' : 'Full Day',
        status: 'confirmed',
        source: 'whatsapp',
        externalId: bookingRef,
        notes: bookingFlow.data.specialRequests,
        createdBy: customerId
      });

      // Send confirmation to customer
      let confirmMessage = `🎉 **Booking Confirmed!**\n`;
      confirmMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      confirmMessage += `📝 **Booking Reference:** ${bookingRef}\n`;
      confirmMessage += `📅 **Calendar ID:** #${calendarBooking.id}\n`;
      confirmMessage += `🏡 **Property:** ${bookingFlow.data.propertyName}\n`;
      confirmMessage += `📅 **Date:** ${bookingFlow.data.checkIn}\n`;
      confirmMessage += `👥 **Guests:** ${bookingFlow.data.guestCount}\n`;
      confirmMessage += `💰 **Total:** ₹${bookingFlow.data.totalPrice}\n\n`;
      confirmMessage += `📞 **Owner will contact you shortly** for:\n`;
      confirmMessage += `• Payment details\n`;
      confirmMessage += `• Property directions\n`;
      confirmMessage += `• Final arrangements\n\n`;
      confirmMessage += `✅ Your booking has been added to the owner's calendar!`;

      await whatsAppService.sendMessage(customerWhatsApp, confirmMessage);

      // Send notification to owner with calendar update info
      const owner = await db.select().from(users).where(eq(users.id, bookingFlow.ownerId)).limit(1);
      if (owner.length > 0) {
        const ownerWhatsApp = owner[0].whatsappNumber || owner[0].phone;
        if (ownerWhatsApp) {
          const ownerNotification = `🔔 **New Booking Alert!**\n\n` +
            `📝 **Reference:** ${bookingRef}\n` +
            `📅 **Calendar Entry:** #${calendarBooking.id}\n` +
            `🏡 **Property:** ${bookingFlow.data.propertyName}\n` +
            `👤 **Customer:** ${bookingFlow.data.customerName}\n` +
            `📞 **Phone:** ${bookingFlow.data.customerPhone}\n` +
            `📅 **Date:** ${bookingFlow.data.checkIn}\n` +
            `👥 **Guests:** ${bookingFlow.data.guestCount}\n` +
            `💰 **Amount:** ₹${bookingFlow.data.totalPrice}\n` +
            `📱 **Customer WhatsApp:** ${customerWhatsApp}\n\n` +
            `✅ **This booking has been automatically added to your calendar.**\n` +
            `Please contact the customer to confirm payment and arrangements.`;

          // Send to owner's WhatsApp
          await whatsAppService.sendMessage(`whatsapp:${ownerWhatsApp}`, ownerNotification);
        }
      }

      // Mark flow as completed
      bookingFlow.step = 'completed';
      const flowKey = `${customerWhatsApp}_${bookingFlow.ownerId}`;
      this.activeBookings.delete(flowKey); // Clean up completed flow

      logger.info('Owner booking created successfully', 'owner-whatsapp', {
        bookingRef,
        customer: customerWhatsApp,
        owner: bookingFlow.ownerId,
        property: bookingFlow.data.propertyName
      });

    } catch (error) {
      logger.error('Error creating booking:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Sorry, there was an error creating your booking. Please try again or contact the owner directly.`
      );
    }
  }

  /**
   * Check if date is weekend
   */
  private isWeekendDate(dateString: string): boolean {
    const date = new Date(dateString);
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  /**
   * Get active booking for customer and owner
   */
  getActiveBooking(customerWhatsApp: string, ownerId: number): OwnerBookingFlow | undefined {
    const flowKey = `${customerWhatsApp}_${ownerId}`;
    return this.activeBookings.get(flowKey);
  }

  /**
   * Cancel active booking flow
   */
  cancelBookingFlow(customerWhatsApp: string, ownerId: number): boolean {
    const flowKey = `${customerWhatsApp}_${ownerId}`;
    return this.activeBookings.delete(flowKey);
  }

  /**
   * Get all active bookings (for admin/monitoring)
   */
  getAllActiveBookings(): OwnerBookingFlow[] {
    return Array.from(this.activeBookings.values());
  }
}

// Export singleton instance
export const ownerWhatsAppService = new OwnerWhatsAppService();