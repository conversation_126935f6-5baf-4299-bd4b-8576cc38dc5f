import { db } from "../db";
import { paymentAuditLogs, securityIncidents, InsertPaymentAuditLog, InsertSecurityIncident } from "../../shared/schema";
import { eq } from "drizzle-orm";
import { Request } from "express";

interface AuditContext {
  userId?: number;
  actorType: 'user' | 'system' | 'admin' | 'webhook';
  actorId?: number;
  actorIp?: string;
  actorUserAgent?: string;
  beforeState?: any;
  afterState?: any;
  metadata?: any;
  securityContext?: any;
}

interface SecurityIncidentData {
  incidentType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  sourceIp?: string;
  userId?: number;
  paymentOrderId?: number;
  incidentData?: any;
}

export class AuditLogger {
  private static instance: AuditLogger;
  
  private constructor() {}
  
  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  async logPaymentAction(
    action: string,
    context: AuditContext,
    paymentOrderId?: number,
    paymentTransactionId?: number
  ): Promise<void> {
    // Log high-priority actions immediately, regardless of database success
    if (this.isHighPriorityAction(action)) {
      console.log(`[AUDIT] High-priority action: ${action}`, {
        actorType: context.actorType,
        actorId: context.actorId,
        paymentOrderId,
        timestamp: new Date().toISOString()
      });
    }

    try {
      const auditLog: InsertPaymentAuditLog = {
        paymentOrderId,
        paymentTransactionId,
        action,
        actorType: context.actorType,
        actorId: context.actorId,
        actorIp: context.actorIp,
        actorUserAgent: context.actorUserAgent,
        beforeState: context.beforeState,
        afterState: context.afterState,
        metadata: context.metadata,
        securityContext: context.securityContext,
      };

      await db.insert(paymentAuditLogs).values(auditLog);
    } catch (error) {
      console.error('Failed to log audit entry:', error);
      // Don't throw - audit failures shouldn't break business operations
    }
  }

  async logSecurityIncident(incidentData: SecurityIncidentData): Promise<void> {
    try {
      const incident: InsertSecurityIncident = {
        incidentType: incidentData.incidentType,
        severity: incidentData.severity,
        description: incidentData.description,
        sourceIp: incidentData.sourceIp,
        userId: incidentData.userId,
        paymentOrderId: incidentData.paymentOrderId,
        incidentData: incidentData.incidentData,
      };

      await db.insert(securityIncidents).values(incident);
      
      // Immediate alerting for critical incidents
      if (incidentData.severity === 'critical') {
        await this.alertCriticalIncident(incident);
      }
      
      console.warn(`[SECURITY] ${incidentData.severity.toUpperCase()} incident:`, {
        type: incidentData.incidentType,
        description: incidentData.description,
        sourceIp: incidentData.sourceIp,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log security incident:', error);
      // Critical: Security incidents must be logged somewhere
      console.error('[SECURITY] CRITICAL: Unable to log security incident to database:', incidentData);
    }
  }

  // Helper method to extract audit context from Express request
  extractAuditContext(req: Request, actorType: 'user' | 'system' | 'admin' | 'webhook' = 'user'): AuditContext {
    return {
      actorType,
      actorId: (req as any).user?.userId,
      actorIp: this.getClientIp(req),
      actorUserAgent: req.get('User-Agent') || 'unknown',
      securityContext: {
        requestId: (req as any).requestId,
        sessionId: (req as any).sessionId,
        timestamp: new Date().toISOString(),
        requestPath: req.path,
        requestMethod: req.method,
      }
    };
  }

  // Convenience method for payment order actions
  async logPaymentOrderAction(
    action: string,
    paymentOrderId: number,
    context: AuditContext,
    beforeState?: any,
    afterState?: any
  ): Promise<void> {
    await this.logPaymentAction(action, {
      ...context,
      beforeState,
      afterState,
      metadata: {
        ...context.metadata,
        paymentOrderId,
      }
    }, paymentOrderId);
  }

  // Convenience method for payment transaction actions
  async logPaymentTransactionAction(
    action: string,
    paymentTransactionId: number,
    paymentOrderId: number,
    context: AuditContext,
    beforeState?: any,
    afterState?: any
  ): Promise<void> {
    await this.logPaymentAction(action, {
      ...context,
      beforeState,
      afterState,
      metadata: {
        ...context.metadata,
        paymentTransactionId,
        paymentOrderId,
      }
    }, paymentOrderId, paymentTransactionId);
  }

  // Security incident shortcuts
  async logSuspiciousActivity(
    description: string,
    sourceIp: string,
    userId?: number,
    additionalData?: any
  ): Promise<void> {
    await this.logSecurityIncident({
      incidentType: 'suspicious_activity',
      severity: 'medium',
      description,
      sourceIp,
      ...(userId && { userId }),
      incidentData: additionalData
    });
  }

  async logAuthenticationFailure(
    description: string,
    sourceIp: string,
    attemptedUser?: string,
    additionalData?: any
  ): Promise<void> {
    await this.logSecurityIncident({
      incidentType: 'authentication_failure',
      severity: 'high',
      description,
      sourceIp,
      incidentData: {
        attemptedUser,
        ...additionalData
      }
    });
  }

  async logPaymentFraud(
    description: string,
    paymentOrderId: number,
    sourceIp: string,
    userId?: number,
    additionalData?: any
  ): Promise<void> {
    await this.logSecurityIncident({
      incidentType: 'payment_fraud',
      severity: 'critical',
      description,
      sourceIp,
      ...(userId && { userId }),
      paymentOrderId,
      incidentData: additionalData
    });
  }

  async logWebhookSecurityViolation(
    description: string,
    sourceIp: string,
    webhookData?: any
  ): Promise<void> {
    await this.logSecurityIncident({
      incidentType: 'webhook_security_violation',
      severity: 'high',
      description,
      sourceIp,
      incidentData: webhookData
    });
  }

  private isHighPriorityAction(action: string): boolean {
    const highPriorityActions = [
      'payment_order_created',
      'payment_captured',
      'payment_failed',
      'payment_refunded',
      'signature_verification_failed',
      'webhook_signature_invalid',
      'rate_limit_exceeded',
      'suspicious_activity_detected'
    ];
    return highPriorityActions.includes(action);
  }

  private getClientIp(req: Request): string {
    return (req.headers['x-forwarded-for'] as string)?.split(',')[0] || 
           req.ip || 
           req.socket.remoteAddress || 
           'unknown';
  }

  private async alertCriticalIncident(incident: InsertSecurityIncident): Promise<void> {
    // In a real implementation, this would send alerts to monitoring systems
    // For now, we'll just log it prominently
    console.error('🚨 CRITICAL SECURITY INCIDENT 🚨', {
      type: incident.incidentType,
      description: incident.description,
      sourceIp: incident.sourceIp,
      timestamp: new Date().toISOString()
    });
    
    // TODO: Integrate with monitoring services like:
    // - Slack alerts
    // - Email notifications
    // - PagerDuty
    // - Monitoring dashboards
  }

  // Method to get audit trail for a specific payment order
  async getPaymentAuditTrail(paymentOrderId: number): Promise<any[]> {
    try {
      const auditLogs = await db
        .select()
        .from(paymentAuditLogs)
        .where(eq(paymentAuditLogs.paymentOrderId, paymentOrderId))
        .orderBy(paymentAuditLogs.createdAt);

      return auditLogs;
    } catch (error) {
      console.error('Failed to retrieve audit trail:', error);
      return [];
    }
  }

  // Method to get security incidents for a specific user
  async getUserSecurityIncidents(userId: number): Promise<any[]> {
    try {
      const incidents = await db
        .select()
        .from(securityIncidents)
        .where(eq(securityIncidents.userId, userId))
        .orderBy(securityIncidents.createdAt);

      return incidents;
    } catch (error) {
      console.error('Failed to retrieve security incidents:', error);
      return [];
    }
  }

  // Method to get recent security incidents for monitoring
  async getRecentSecurityIncidents(limit: number = 50): Promise<any[]> {
    try {
      const incidents = await db
        .select()
        .from(securityIncidents)
        .orderBy(securityIncidents.createdAt)
        .limit(limit);

      return incidents;
    } catch (error) {
      console.error('Failed to retrieve recent security incidents:', error);
      return [];
    }
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance();