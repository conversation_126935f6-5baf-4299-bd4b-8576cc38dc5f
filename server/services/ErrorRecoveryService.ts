import { logger } from './LoggerService';
import { RetryService, RetryConfig } from './RetryService';
import { storage } from '../storage';
import { config } from '../config';

// Types of operations that can have fallback strategies
export enum OperationType {
  PAYMENT_PROCESSING = 'PAYMENT_PROCESSING',
  BOOKING_CREATION = 'BOOKING_CREATION',
  EMAIL_NOTIFICATION = 'EMAIL_NOTIFICATION',
  SMS_NOTIFICATION = 'SMS_NOTIFICATION',
  DATABASE_OPERATION = 'DATABASE_OPERATION',
  EXTERNAL_API_CALL = 'EXTERNAL_API_CALL'
}

// Fallback strategy definitions
export interface FallbackStrategy<T> {
  operationType: OperationType;
  primaryOperation: () => Promise<T>;
  fallbackOperations: Array<() => Promise<T>>;
  retryConfig?: RetryConfig;
  onFallbackUsed?: (fallbackIndex: number, error: Error) => void;
  onAllFailed?: (errors: Error[]) => void;
}

// Error recovery result
export interface RecoveryResult<T> {
  success: boolean;
  result?: T;
  usedFallback: boolean;
  fallbackIndex?: number;
  errors: Error[];
  totalAttempts: number;
  duration: number;
}

// Queue for failed operations that can be retried later
interface FailedOperation {
  id: string;
  operationType: OperationType;
  operation: () => Promise<any>;
  metadata: Record<string, any>;
  failedAt: Date;
  retryCount: number;
  nextRetryAt: Date;
}

export class ErrorRecoveryService {
  private failedOperationsQueue: Map<string, FailedOperation> = new Map();
  private isProcessingQueue = false;
  private queueProcessingInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startQueueProcessing();
  }

  /**
   * Execute operation with comprehensive error recovery and fallback strategies
   */
  async executeWithRecovery<T>(strategy: FallbackStrategy<T>): Promise<RecoveryResult<T>> {
    const startTime = Date.now();
    const errors: Error[] = [];
    let totalAttempts = 0;

    // Try primary operation with retry
    try {
      logger.debug('Executing primary operation with retry', 'error-recovery', {
        operationType: strategy.operationType
      });

      const result = await RetryService.executeWithRetry(
        strategy.primaryOperation,
        strategy.retryConfig || {},
        `primary_${strategy.operationType}`
      );

      return {
        success: true,
        result,
        usedFallback: false,
        errors: [],
        totalAttempts: 1,
        duration: Date.now() - startTime
      };
    } catch (primaryError) {
      errors.push(primaryError instanceof Error ? primaryError : new Error(String(primaryError)));
      totalAttempts++;

      logger.warn('Primary operation failed, trying fallbacks', 'error-recovery', {
        operationType: strategy.operationType,
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });
    }

    // Try fallback operations
    for (let i = 0; i < strategy.fallbackOperations.length; i++) {
      try {
        logger.debug('Executing fallback operation', 'error-recovery', {
          operationType: strategy.operationType,
          fallbackIndex: i
        });

        const fallbackResult = await RetryService.executeWithRetry(
          strategy.fallbackOperations[i],
          strategy.retryConfig || {},
          `fallback_${i}_${strategy.operationType}`
        );

        // Notify that fallback was used
        strategy.onFallbackUsed?.(i, errors[errors.length - 1]);

        logger.info('Fallback operation succeeded', 'error-recovery', {
          operationType: strategy.operationType,
          fallbackIndex: i,
          totalAttempts: totalAttempts + 1,
          duration: Date.now() - startTime
        });

        return {
          success: true,
          result: fallbackResult,
          usedFallback: true,
          fallbackIndex: i,
          errors,
          totalAttempts: totalAttempts + 1,
          duration: Date.now() - startTime
        };
      } catch (fallbackError) {
        errors.push(fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError)));
        totalAttempts++;

        logger.warn('Fallback operation failed', 'error-recovery', {
          operationType: strategy.operationType,
          fallbackIndex: i,
          error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
        });
      }
    }

    // All operations failed
    const duration = Date.now() - startTime;
    logger.error('All operations failed including fallbacks', errors[errors.length - 1] || new Error('All operations failed'), 'error-recovery', {
      operationType: strategy.operationType,
      totalAttempts,
      duration,
      errorCount: errors.length
    });

    strategy.onAllFailed?.(errors);

    return {
      success: false,
      usedFallback: false,
      errors,
      totalAttempts,
      duration
    };
  }

  /**
   * Queue failed operation for later retry
   */
  async queueFailedOperation(
    operationId: string,
    operationType: OperationType,
    operation: () => Promise<any>,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    const now = new Date();
    const nextRetryAt = new Date(now.getTime() + this.calculateRetryDelay(0));

    const failedOp: FailedOperation = {
      id: operationId,
      operationType,
      operation,
      metadata,
      failedAt: now,
      retryCount: 0,
      nextRetryAt
    };

    this.failedOperationsQueue.set(operationId, failedOp);

    logger.info('Operation queued for retry', 'error-recovery', {
      operationId,
      operationType,
      nextRetryAt,
      metadata
    });
  }

  /**
   * Process queued failed operations
   */
  private async processFailedOperationsQueue(): Promise<void> {
    if (this.isProcessingQueue || this.failedOperationsQueue.size === 0) {
      return;
    }

    this.isProcessingQueue = true;
    const now = new Date();

    logger.debug('Processing failed operations queue', 'error-recovery', {
      queueSize: this.failedOperationsQueue.size
    });

    for (const [operationId, failedOp] of this.failedOperationsQueue) {
      if (now < failedOp.nextRetryAt) {
        continue; // Not time to retry yet
      }

      try {
        logger.debug('Retrying queued operation', 'error-recovery', {
          operationId,
          operationType: failedOp.operationType,
          retryCount: failedOp.retryCount + 1
        });

        await failedOp.operation();

        // Operation succeeded, remove from queue
        this.failedOperationsQueue.delete(operationId);

        logger.info('Queued operation retry succeeded', 'error-recovery', {
          operationId,
          operationType: failedOp.operationType,
          retryCount: failedOp.retryCount + 1
        });
      } catch (error) {
        failedOp.retryCount++;

        // Check if we should give up on this operation
        if (failedOp.retryCount >= 5) {
          this.failedOperationsQueue.delete(operationId);
          
          logger.error('Giving up on queued operation after max retries', error instanceof Error ? error : new Error(String(error)), 'error-recovery', {
            operationId,
            operationType: failedOp.operationType,
            retryCount: failedOp.retryCount
          });
        } else {
          // Schedule next retry
          failedOp.nextRetryAt = new Date(now.getTime() + this.calculateRetryDelay(failedOp.retryCount));
          
          logger.warn('Queued operation retry failed, scheduling next attempt', 'error-recovery', {
            operationId,
            operationType: failedOp.operationType,
            retryCount: failedOp.retryCount,
            nextRetryAt: failedOp.nextRetryAt,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    const baseDelay = 60000; // 1 minute base delay
    const maxDelay = 3600000; // 1 hour max delay
    const exponentialDelay = baseDelay * Math.pow(2, retryCount);
    return Math.min(exponentialDelay, maxDelay);
  }

  /**
   * Start periodic processing of failed operations queue
   */
  private startQueueProcessing(): void {
    // Process queue every 30 seconds
    this.queueProcessingInterval = setInterval(() => {
      this.processFailedOperationsQueue().catch(error => {
        logger.error('Error processing failed operations queue', error instanceof Error ? error : new Error(String(error)), 'error-recovery');
      });
    }, 30000);

    logger.info('Error recovery queue processing started', 'error-recovery');
  }

  /**
   * Stop queue processing
   */
  stop(): void {
    if (this.queueProcessingInterval) {
      clearInterval(this.queueProcessingInterval);
      this.queueProcessingInterval = null;
    }
    logger.info('Error recovery queue processing stopped');
  }

  /**
   * Get current queue status
   */
  getQueueStatus(): {
    queueSize: number;
    isProcessing: boolean;
    operations: Array<{
      id: string;
      operationType: OperationType;
      retryCount: number;
      nextRetryAt: Date;
      failedAt: Date;
    }>;
  } {
    return {
      queueSize: this.failedOperationsQueue.size,
      isProcessing: this.isProcessingQueue,
      operations: Array.from(this.failedOperationsQueue.values()).map(op => ({
        id: op.id,
        operationType: op.operationType,
        retryCount: op.retryCount,
        nextRetryAt: op.nextRetryAt,
        failedAt: op.failedAt
      }))
    };
  }

  /**
   * Create payment processing strategy with fallbacks
   */
  static createPaymentStrategy<T>(
    primaryPaymentProcessor: () => Promise<T>,
    fallbackProcessor?: () => Promise<T>
  ): FallbackStrategy<T> {
    const fallbacks: Array<() => Promise<T>> = [];
    
    if (fallbackProcessor) {
      fallbacks.push(fallbackProcessor);
    }

    // Add emergency fallback: queue for manual processing
    fallbacks.push(async () => {
      logger.warn('All payment processors failed, queuing for manual processing');
      throw new Error('Payment queued for manual processing');
    });

    return {
      operationType: OperationType.PAYMENT_PROCESSING,
      primaryOperation: primaryPaymentProcessor,
      fallbackOperations: fallbacks,
      retryConfig: RetryService.createPaymentRetryConfig(),
      onFallbackUsed: (index, error) => {
        logger.warn('Payment fallback used', 'error-recovery', { fallbackIndex: index, originalError: error.message });
      },
      onAllFailed: (errors) => {
        logger.error('Critical: All payment processing options failed', errors[0] || new Error('All payment options failed'), 'error-recovery', {
          errorCount: errors.length,
          errors: errors.map(e => e.message)
        });
      }
    };
  }

  /**
   * Create booking creation strategy with fallbacks
   */
  static createBookingStrategy<T>(
    primaryBookingCreator: () => Promise<T>,
    fallbackCreator?: () => Promise<T>
  ): FallbackStrategy<T> {
    const fallbacks: Array<() => Promise<T>> = [];
    
    if (fallbackCreator) {
      fallbacks.push(fallbackCreator);
    }

    return {
      operationType: OperationType.BOOKING_CREATION,
      primaryOperation: primaryBookingCreator,
      fallbackOperations: fallbacks,
      retryConfig: RetryService.createDatabaseRetryConfig(),
      onFallbackUsed: (index, error) => {
        logger.warn('Booking creation fallback used', 'error-recovery', { fallbackIndex: index, originalError: error.message });
      },
      onAllFailed: (errors) => {
        logger.error('Critical: All booking creation options failed', errors[0] || new Error('All booking options failed'), 'error-recovery', {
          errorCount: errors.length,
          errors: errors.map(e => e.message)
        });
      }
    };
  }

  /**
   * Create notification strategy with multiple channels
   */
  static createNotificationStrategy(
    primaryNotification: () => Promise<void>,
    fallbackNotifications: Array<() => Promise<void>>
  ): FallbackStrategy<void> {
    return {
      operationType: OperationType.EMAIL_NOTIFICATION,
      primaryOperation: primaryNotification,
      fallbackOperations: fallbackNotifications,
      retryConfig: RetryService.createAPIRetryConfig(),
      onFallbackUsed: (index, error) => {
        logger.warn('Notification fallback used', 'error-recovery', { fallbackIndex: index, originalError: error.message });
      },
      onAllFailed: (errors) => {
        logger.error('All notification channels failed', errors[0] || new Error('All notification channels failed'), 'error-recovery', {
          errorCount: errors.length,
          errors: errors.map(e => e.message)
        });
      }
    };
  }
}

// Export singleton instance
export const errorRecoveryService = new ErrorRecoveryService();