// Centralized service exports for clean imports
export { userService, UserService } from './UserService';
export { propertyService, PropertyService } from './PropertyService';
export { bookingService, BookingService } from './BookingService';
export { reviewService, ReviewService } from './ReviewService';
export { templateService, TemplateService } from './TemplateService';
export { calendarService, CalendarService } from './CalendarService';

// Service interfaces for better type safety
export type { PropertySearchFilters } from './PropertyService';
export type { BookingWithProperty } from './BookingService';
export type { ReviewWithUser, ReviewWithProperty } from './ReviewService';
export type { 
  CalendarBookingWithDetails, 
  CalendarFilters, 
  AvailabilityCheck, 
  SyncStatusWithProperty 
} from './CalendarService';