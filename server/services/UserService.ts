import { storage } from '../storage';
import { User, InsertUser, UserRegister } from '../../shared/schema';
import bcrypt from 'bcrypt';

export class UserService {
  async getAllUsers(): Promise<User[]> {
    return await storage.getAllUsers();
  }

  async getUserById(id: number): Promise<User | null> {
    const user = await storage.getUser(id);
    return user || null;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const user = await storage.getUserByEmail(email);
    return user || null;
  }

  async getUserByPhone(phone: string): Promise<User | null> {
    const user = await storage.getUserByPhone(phone);
    return user || null;
  }

  async createUser(userData: any): Promise<User> {
    // Hash password if provided
    let hashedPassword = '';
    if (userData.password) {
      hashedPassword = await bcrypt.hash(userData.password, 10);
    }

    const newUser: InsertUser = {
      username: userData.username,
      email: userData.email,
      password: hashedPassword,
      fullName: userData.fullName,
      phone: userData.phone || null,
      address: userData.address || null,
      bio: userData.bio || null,
      role: userData.role || 'user',
      termsAccepted: userData.termsAccepted || false,
      privacyPolicyAccepted: userData.privacyPolicyAccepted || false,
      cookiePolicyAccepted: userData.cookiePolicyAccepted || false,
      dataProcessingConsent: userData.dataProcessingConsent || false,
      marketingConsent: userData.marketingConsent || false
    };

    return await storage.createUser(newUser);
  }

  async updateUser(id: number, userData: any): Promise<User | null> {
    // Hash password if provided in update
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }

    const updatedUser = await storage.updateUser(id, userData);
    return updatedUser || null;
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    if (!user.password || !password) {
      return false;
    }
    return await bcrypt.compare(password, user.password);
  }

  async findUserByEmailOrPhone(identifier: string): Promise<User | null> {
    // Try email first
    let user = await this.getUserByEmail(identifier);
    if (user) return user;

    // Try phone if email doesn't work
    user = await this.getUserByPhone(identifier);
    return user;
  }

  async isEmailTaken(email: string, excludeUserId?: number): Promise<boolean> {
    const existingUser = await this.getUserByEmail(email);
    if (!existingUser) return false;
    
    // If we're excluding a specific user (for updates), check if it's a different user
    if (excludeUserId && existingUser.id === excludeUserId) {
      return false;
    }
    
    return true;
  }

  async isPhoneTaken(phone: string, excludeUserId?: number): Promise<boolean> {
    const existingUser = await this.getUserByPhone(phone);
    if (!existingUser) return false;
    
    // If we're excluding a specific user (for updates), check if it's a different user
    if (excludeUserId && existingUser.id === excludeUserId) {
      return false;
    }
    
    return true;
  }
}

export const userService = new UserService();