import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarBookings, 
  properties, 
  users,
  CalendarBooking
} from '@shared/schema';
import { eq, and, gte, lte, count, sum, avg, sql, desc, inArray } from 'drizzle-orm';

export interface AnalyticsTimeframe {
  start: string;
  end: string;
  granularity: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

export interface BookingAnalytics {
  timeframe: AnalyticsTimeframe;
  metrics: {
    totalBookings: number;
    totalRevenue: number;
    averageBookingValue: number;
    occupancyRate: number;
    averageStayLength: number;
    cancellationRate: number;
    repeatCustomerRate: number;
  };
  trends: {
    bookingsTrend: TrendData[];
    revenueTrend: TrendData[];
    occupancyTrend: TrendData[];
  };
  breakdown: {
    byProperty: PropertyBreakdown[];
    byBookingType: BookingTypeBreakdown[];
    bySource: SourceBreakdown[];
    byDayOfWeek: DayOfWeekBreakdown[];
    byMonth: MonthBreakdown[];
  };
  forecasting: {
    predictedBookings: number;
    predictedRevenue: number;
    seasonalityFactors: SeasonalityFactor[];
  };
}

export interface TrendData {
  period: string;
  value: number;
  change?: number; // Percentage change from previous period
}

export interface PropertyBreakdown {
  propertyId: number;
  propertyName: string;
  location: string;
  totalBookings: number;
  revenue: number;
  occupancyRate: number;
  averageBookingValue: number;
  ranking: number;
}

export interface BookingTypeBreakdown {
  bookingType: string;
  count: number;
  revenue: number;
  percentage: number;
}

export interface SourceBreakdown {
  source: string;
  count: number;
  revenue: number;
  percentage: number;
  conversionRate?: number;
}

export interface DayOfWeekBreakdown {
  dayOfWeek: string;
  count: number;
  averageRevenue: number;
}

export interface MonthBreakdown {
  month: string;
  year: number;
  count: number;
  revenue: number;
  occupancyRate: number;
}

export interface SeasonalityFactor {
  period: string;
  factor: number; // Multiplier (1.0 = average, >1.0 = above average, <1.0 = below average)
  confidence: number;
}

export interface OwnerAnalytics {
  ownerId: number;
  ownerName: string;
  totalProperties: number;
  analytics: BookingAnalytics;
  propertyComparison: PropertyPerformanceComparison[];
  insights: AnalyticsInsight[];
}

export interface PropertyPerformanceComparison {
  propertyId: number;
  propertyName: string;
  metrics: {
    revenue: number;
    bookings: number;
    occupancyRate: number;
    averageBookingValue: number;
  };
  percentileRanking: number; // 0-100
}

export interface AnalyticsInsight {
  type: 'opportunity' | 'warning' | 'achievement' | 'trend';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
}

export class CalendarAnalyticsService {
  
  /**
   * Get comprehensive booking analytics
   */
  async getBookingAnalytics(
    timeframe: AnalyticsTimeframe,
    propertyIds?: number[],
    ownerId?: number
  ): Promise<BookingAnalytics> {
    try {
      logger.info('Generating booking analytics', {
        timeframe,
        propertyIds: propertyIds?.length || 'all',
        ownerId
      });

      // Base query for bookings in timeframe
      let bookingsQuery = db
        .select()
        .from(calendarBookings)
        .where(and(
          gte(calendarBookings.startDate, timeframe.start),
          lte(calendarBookings.endDate, timeframe.end)
        ));

      // Apply filters
      if (propertyIds && propertyIds.length > 0) {
        bookingsQuery = bookingsQuery.where(inArray(calendarBookings.propertyId, propertyIds));
      }

      if (ownerId) {
        const ownerPropertyIds = await db
          .select({ id: properties.id })
          .from(properties)
          .where(eq(properties.ownerId, ownerId));
        
        const propertyIdsList = ownerPropertyIds.map(p => p.id);
        bookingsQuery = bookingsQuery.where(inArray(calendarBookings.propertyId, propertyIdsList));
      }

      const bookings = await bookingsQuery;

      // Calculate metrics
      const metrics = await this.calculateMetrics(bookings, timeframe);
      
      // Generate trends
      const trends = await this.calculateTrends(timeframe, propertyIds, ownerId);
      
      // Generate breakdowns
      const breakdown = await this.calculateBreakdowns(bookings, timeframe);
      
      // Generate forecasting
      const forecasting = await this.calculateForecasting(bookings, timeframe);

      const analytics: BookingAnalytics = {
        timeframe,
        metrics,
        trends,
        breakdown,
        forecasting
      };

      logger.info('Analytics generated successfully', {
        totalBookings: metrics.totalBookings,
        totalRevenue: metrics.totalRevenue,
        occupancyRate: metrics.occupancyRate
      });

      return analytics;
    } catch (error) {
      logger.error('Error generating booking analytics', error as Error);
      throw error;
    }
  }

  /**
   * Get owner-specific analytics
   */
  async getOwnerAnalytics(
    ownerId: number,
    timeframe: AnalyticsTimeframe
  ): Promise<OwnerAnalytics> {
    try {
      // Get owner info
      const ownerInfo = await db
        .select({ 
          id: users.id, 
          fullName: users.fullName 
        })
        .from(users)
        .where(eq(users.id, ownerId))
        .limit(1);

      if (ownerInfo.length === 0) {
        throw new Error(`Owner ${ownerId} not found`);
      }

      const owner = ownerInfo[0];

      // Get owner's properties
      const ownerProperties = await db
        .select()
        .from(properties)
        .where(eq(properties.ownerId, ownerId));

      // Get analytics for owner's properties
      const analytics = await this.getBookingAnalytics(
        timeframe, 
        ownerProperties.map(p => p.id)
      );

      // Generate property performance comparison
      const propertyComparison = await this.generatePropertyComparison(
        ownerProperties.map(p => p.id), 
        timeframe
      );

      // Generate insights
      const insights = await this.generateAnalyticsInsights(analytics, ownerProperties);

      return {
        ownerId: owner.id,
        ownerName: owner.fullName,
        totalProperties: ownerProperties.length,
        analytics,
        propertyComparison,
        insights
      };
    } catch (error) {
      logger.error('Error generating owner analytics', error as Error);
      throw error;
    }
  }

  /**
   * Get real-time dashboard metrics
   */
  async getDashboardMetrics(ownerId?: number): Promise<any> {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let baseQuery = db.select().from(calendarBookings);
    
    if (ownerId) {
      const ownerPropertyIds = await db
        .select({ id: properties.id })
        .from(properties)
        .where(eq(properties.ownerId, ownerId));
      
      baseQuery = baseQuery.where(
        inArray(calendarBookings.propertyId, ownerPropertyIds.map(p => p.id))
      );
    }

    const [
      todayBookings,
      thisMonthBookings,
      upcomingCheckIns,
      upcomingCheckOuts,
      totalRevenue
    ] = await Promise.all([
      // Today's bookings
      baseQuery.where(
        and(
          lte(calendarBookings.startDate, today.toISOString().split('T')[0]),
          gte(calendarBookings.endDate, today.toISOString().split('T')[0])
        )
      ),
      
      // This month's bookings
      baseQuery.where(
        and(
          gte(calendarBookings.startDate, thisMonth.toISOString().split('T')[0]),
          lte(calendarBookings.startDate, nextMonth.toISOString().split('T')[0])
        )
      ),
      
      // Upcoming check-ins (next 7 days)
      baseQuery.where(
        and(
          gte(calendarBookings.startDate, today.toISOString().split('T')[0]),
          lte(calendarBookings.startDate, 
            new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          )
        )
      ),
      
      // Upcoming check-outs (next 7 days)
      baseQuery.where(
        and(
          gte(calendarBookings.endDate, today.toISOString().split('T')[0]),
          lte(calendarBookings.endDate, 
            new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          )
        )
      ),
      
      // Total revenue this month
      db.select({
        total: sql<number>`COALESCE(SUM(${calendarBookings.totalAmount}), 0)`
      })
      .from(calendarBookings)
      .where(
        and(
          gte(calendarBookings.startDate, thisMonth.toISOString().split('T')[0]),
          lte(calendarBookings.startDate, nextMonth.toISOString().split('T')[0]),
          ownerId ? inArray(calendarBookings.propertyId, 
            (await db.select({ id: properties.id })
              .from(properties)
              .where(eq(properties.ownerId, ownerId))
            ).map(p => p.id)
          ) : undefined
        )
      )
    ]);

    return {
      activeBookings: todayBookings.length,
      monthlyBookings: thisMonthBookings.length,
      upcomingCheckIns: upcomingCheckIns.length,
      upcomingCheckOuts: upcomingCheckOuts.length,
      monthlyRevenue: totalRevenue[0]?.total || 0,
      occupancyRate: await this.calculateCurrentOccupancyRate(ownerId)
    };
  }

  /**
   * Private calculation methods
   */
  private async calculateMetrics(
    bookings: CalendarBooking[], 
    timeframe: AnalyticsTimeframe
  ) {
    const totalBookings = bookings.length;
    const totalRevenue = bookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0);
    const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;

    // Calculate stay lengths
    const stayLengths = bookings.map(b => {
      const start = new Date(b.startDate);
      const end = new Date(b.endDate);
      return Math.max(1, Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)));
    });
    const averageStayLength = stayLengths.length > 0 
      ? stayLengths.reduce((sum, length) => sum + length, 0) / stayLengths.length 
      : 0;

    // Calculate occupancy rate (simplified)
    const totalDays = Math.ceil(
      (new Date(timeframe.end).getTime() - new Date(timeframe.start).getTime()) / (1000 * 60 * 60 * 24)
    );
    const bookedDays = stayLengths.reduce((sum, length) => sum + length, 0);
    const occupancyRate = totalDays > 0 ? (bookedDays / totalDays) * 100 : 0;

    // Calculate cancellation rate
    const cancelledBookings = bookings.filter(b => b.status === 'cancelled').length;
    const cancellationRate = totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0;

    // Calculate repeat customer rate (simplified - based on guest name)
    const guestCounts = new Map<string, number>();
    bookings.forEach(b => {
      const count = guestCounts.get(b.guestName) || 0;
      guestCounts.set(b.guestName, count + 1);
    });
    const repeatGuests = Array.from(guestCounts.values()).filter(count => count > 1).length;
    const repeatCustomerRate = guestCounts.size > 0 ? (repeatGuests / guestCounts.size) * 100 : 0;

    return {
      totalBookings,
      totalRevenue,
      averageBookingValue,
      occupancyRate,
      averageStayLength,
      cancellationRate,
      repeatCustomerRate
    };
  }

  private async calculateTrends(
    timeframe: AnalyticsTimeframe,
    propertyIds?: number[],
    ownerId?: number
  ) {
    // Generate trend data based on granularity
    const periods = this.generatePeriods(timeframe);
    
    const bookingsTrend: TrendData[] = [];
    const revenueTrend: TrendData[] = [];
    const occupancyTrend: TrendData[] = [];

    for (const period of periods) {
      // Get bookings for this period
      let query = db
        .select({
          count: sql<number>`COUNT(*)`,
          revenue: sql<number>`COALESCE(SUM(${calendarBookings.totalAmount}), 0)`
        })
        .from(calendarBookings)
        .where(and(
          gte(calendarBookings.startDate, period.start),
          lte(calendarBookings.startDate, period.end)
        ));

      if (propertyIds && propertyIds.length > 0) {
        query = query.where(inArray(calendarBookings.propertyId, propertyIds));
      }

      const result = await query;
      const count = result[0]?.count || 0;
      const revenue = result[0]?.revenue || 0;

      bookingsTrend.push({
        period: period.label,
        value: count
      });

      revenueTrend.push({
        period: period.label,
        value: revenue
      });

      // Simplified occupancy calculation
      occupancyTrend.push({
        period: period.label,
        value: count > 0 ? Math.min(100, count * 20) : 0 // Simplified calculation
      });
    }

    // Calculate percentage changes
    this.calculateTrendChanges(bookingsTrend);
    this.calculateTrendChanges(revenueTrend);
    this.calculateTrendChanges(occupancyTrend);

    return {
      bookingsTrend,
      revenueTrend,
      occupancyTrend
    };
  }

  private async calculateBreakdowns(
    bookings: CalendarBooking[],
    timeframe: AnalyticsTimeframe
  ) {
    // Property breakdown
    const propertyMap = new Map<number, { bookings: CalendarBooking[], property?: any }>();
    
    for (const booking of bookings) {
      if (!propertyMap.has(booking.propertyId)) {
        const property = await db
          .select()
          .from(properties)
          .where(eq(properties.id, booking.propertyId))
          .limit(1);
        
        propertyMap.set(booking.propertyId, {
          bookings: [],
          property: property[0]
        });
      }
      
      propertyMap.get(booking.propertyId)!.bookings.push(booking);
    }

    const byProperty: PropertyBreakdown[] = Array.from(propertyMap.entries())
      .map(([propertyId, data], index) => ({
        propertyId,
        propertyName: data.property?.title || 'Unknown',
        location: data.property?.location || 'Unknown',
        totalBookings: data.bookings.length,
        revenue: data.bookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0),
        occupancyRate: this.calculatePropertyOccupancy(data.bookings, timeframe),
        averageBookingValue: data.bookings.length > 0 
          ? data.bookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0) / data.bookings.length 
          : 0,
        ranking: index + 1
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .map((item, index) => ({ ...item, ranking: index + 1 }));

    // Booking type breakdown
    const bookingTypeMap = new Map<string, CalendarBooking[]>();
    bookings.forEach(b => {
      const type = b.bookingType || 'Unknown';
      if (!bookingTypeMap.has(type)) {
        bookingTypeMap.set(type, []);
      }
      bookingTypeMap.get(type)!.push(b);
    });

    const byBookingType: BookingTypeBreakdown[] = Array.from(bookingTypeMap.entries())
      .map(([type, typeBookings]) => ({
        bookingType: type,
        count: typeBookings.length,
        revenue: typeBookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0),
        percentage: (typeBookings.length / bookings.length) * 100
      }));

    // Source breakdown
    const sourceMap = new Map<string, CalendarBooking[]>();
    bookings.forEach(b => {
      const source = b.source || 'direct';
      if (!sourceMap.has(source)) {
        sourceMap.set(source, []);
      }
      sourceMap.get(source)!.push(b);
    });

    const bySource: SourceBreakdown[] = Array.from(sourceMap.entries())
      .map(([source, sourceBookings]) => ({
        source,
        count: sourceBookings.length,
        revenue: sourceBookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0),
        percentage: (sourceBookings.length / bookings.length) * 100
      }));

    // Day of week breakdown
    const dayMap = new Map<string, CalendarBooking[]>();
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    bookings.forEach(b => {
      const date = new Date(b.startDate);
      const dayName = dayNames[date.getDay()];
      if (!dayMap.has(dayName)) {
        dayMap.set(dayName, []);
      }
      dayMap.get(dayName)!.push(b);
    });

    const byDayOfWeek: DayOfWeekBreakdown[] = dayNames.map(dayName => {
      const dayBookings = dayMap.get(dayName) || [];
      return {
        dayOfWeek: dayName,
        count: dayBookings.length,
        averageRevenue: dayBookings.length > 0 
          ? dayBookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0) / dayBookings.length 
          : 0
      };
    });

    // Month breakdown
    const monthMap = new Map<string, CalendarBooking[]>();
    bookings.forEach(b => {
      const date = new Date(b.startDate);
      const key = `${date.getFullYear()}-${date.getMonth() + 1}`;
      if (!monthMap.has(key)) {
        monthMap.set(key, []);
      }
      monthMap.get(key)!.push(b);
    });

    const byMonth: MonthBreakdown[] = Array.from(monthMap.entries())
      .map(([key, monthBookings]) => {
        const [year, month] = key.split('-').map(Number);
        const monthName = new Date(year, month - 1).toLocaleString('default', { month: 'long' });
        
        return {
          month: monthName,
          year,
          count: monthBookings.length,
          revenue: monthBookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0),
          occupancyRate: this.calculateMonthOccupancy(monthBookings, year, month)
        };
      });

    return {
      byProperty,
      byBookingType,
      bySource,
      byDayOfWeek,
      byMonth
    };
  }

  private async calculateForecasting(
    bookings: CalendarBooking[],
    timeframe: AnalyticsTimeframe
  ) {
    // Simplified forecasting - in production, you'd use more sophisticated algorithms
    const totalBookings = bookings.length;
    const totalRevenue = bookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0);

    // Simple linear projection based on historical data
    const predictedBookings = Math.round(totalBookings * 1.1); // 10% growth assumption
    const predictedRevenue = totalRevenue * 1.1;

    // Generate seasonality factors based on historical patterns
    const seasonalityFactors: SeasonalityFactor[] = [
      { period: 'Q1', factor: 0.8, confidence: 0.7 },
      { period: 'Q2', factor: 1.2, confidence: 0.8 },
      { period: 'Q3', factor: 1.3, confidence: 0.9 },
      { period: 'Q4', factor: 1.0, confidence: 0.8 }
    ];

    return {
      predictedBookings,
      predictedRevenue,
      seasonalityFactors
    };
  }

  private generatePeriods(timeframe: AnalyticsTimeframe): Array<{ start: string; end: string; label: string }> {
    const periods: Array<{ start: string; end: string; label: string }> = [];
    const start = new Date(timeframe.start);
    const end = new Date(timeframe.end);

    switch (timeframe.granularity) {
      case 'day':
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          const dateStr = d.toISOString().split('T')[0];
          periods.push({
            start: dateStr,
            end: dateStr,
            label: dateStr
          });
        }
        break;
      
      case 'week':
        // Implement weekly periods
        break;
      
      case 'month':
        for (let d = new Date(start); d <= end; d.setMonth(d.getMonth() + 1)) {
          const monthStart = new Date(d.getFullYear(), d.getMonth(), 1);
          const monthEnd = new Date(d.getFullYear(), d.getMonth() + 1, 0);
          periods.push({
            start: monthStart.toISOString().split('T')[0],
            end: monthEnd.toISOString().split('T')[0],
            label: `${d.getFullYear()}-${d.getMonth() + 1}`
          });
        }
        break;
    }

    return periods.slice(0, 12); // Limit to 12 periods for performance
  }

  private calculateTrendChanges(trend: TrendData[]): void {
    for (let i = 1; i < trend.length; i++) {
      const current = trend[i].value;
      const previous = trend[i - 1].value;
      
      if (previous > 0) {
        trend[i].change = ((current - previous) / previous) * 100;
      } else {
        trend[i].change = current > 0 ? 100 : 0;
      }
    }
  }

  private calculatePropertyOccupancy(bookings: CalendarBooking[], timeframe: AnalyticsTimeframe): number {
    // Simplified calculation
    const totalDays = Math.ceil(
      (new Date(timeframe.end).getTime() - new Date(timeframe.start).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    const bookedDays = bookings.reduce((sum, booking) => {
      const bookingDays = Math.ceil(
        (new Date(booking.endDate).getTime() - new Date(booking.startDate).getTime()) / (1000 * 60 * 60 * 24)
      ) + 1;
      return sum + bookingDays;
    }, 0);

    return totalDays > 0 ? Math.min(100, (bookedDays / totalDays) * 100) : 0;
  }

  private calculateMonthOccupancy(bookings: CalendarBooking[], year: number, month: number): number {
    const daysInMonth = new Date(year, month, 0).getDate();
    const bookedDays = bookings.reduce((sum, booking) => {
      // Simplified calculation
      return sum + 1; // Each booking = 1 day for simplicity
    }, 0);

    return (bookedDays / daysInMonth) * 100;
  }

  private async calculateCurrentOccupancyRate(ownerId?: number): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    
    let query = db
      .select({ count: sql<number>`COUNT(*)` })
      .from(calendarBookings)
      .where(and(
        lte(calendarBookings.startDate, today),
        gte(calendarBookings.endDate, today)
      ));

    if (ownerId) {
      const ownerPropertyIds = await db
        .select({ id: properties.id })
        .from(properties)
        .where(eq(properties.ownerId, ownerId));
      
      query = query.where(inArray(calendarBookings.propertyId, ownerPropertyIds.map(p => p.id)));
    }

    const result = await query;
    const activeBookings = result[0]?.count || 0;

    // Get total properties
    let totalPropertiesQuery = db
      .select({ count: sql<number>`COUNT(*)` })
      .from(properties)
      .where(eq(properties.status, 'active'));

    if (ownerId) {
      totalPropertiesQuery = totalPropertiesQuery.where(eq(properties.ownerId, ownerId));
    }

    const totalPropsResult = await totalPropertiesQuery;
    const totalProperties = totalPropsResult[0]?.count || 1;

    return (activeBookings / totalProperties) * 100;
  }

  private async generatePropertyComparison(
    propertyIds: number[],
    timeframe: AnalyticsTimeframe
  ): Promise<PropertyPerformanceComparison[]> {
    // Implementation for property performance comparison
    return [];
  }

  private async generateAnalyticsInsights(
    analytics: BookingAnalytics,
    properties: any[]
  ): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = [];

    // Generate insights based on analytics data
    if (analytics.metrics.occupancyRate > 80) {
      insights.push({
        type: 'achievement',
        title: 'High Occupancy Rate',
        description: `Your properties have an excellent occupancy rate of ${analytics.metrics.occupancyRate.toFixed(1)}%`,
        impact: 'high',
        actionable: false
      });
    }

    if (analytics.metrics.cancellationRate > 20) {
      insights.push({
        type: 'warning',
        title: 'High Cancellation Rate',
        description: `Cancellation rate of ${analytics.metrics.cancellationRate.toFixed(1)}% is above normal`,
        impact: 'medium',
        actionable: true,
        recommendation: 'Review cancellation policies and booking confirmation process'
      });
    }

    // Revenue trend analysis
    const revenueGrowth = analytics.trends.revenueTrend[analytics.trends.revenueTrend.length - 1]?.change;
    if (revenueGrowth && revenueGrowth > 10) {
      insights.push({
        type: 'achievement',
        title: 'Revenue Growth',
        description: `Revenue has grown by ${revenueGrowth.toFixed(1)}% in the latest period`,
        impact: 'high',
        actionable: false
      });
    }

    return insights;
  }
}

// Export singleton instance
export const calendarAnalyticsService = new CalendarAnalyticsService();