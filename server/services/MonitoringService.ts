import { EventEmitter } from 'events';
import { logger, LogContext } from '../utils/structured-logger';
import EnvironmentValidator from '../utils/environment-validator';

export interface AlertRule {
  id: string;
  name: string;
  condition: (metric: Metric) => boolean;
  severity: 'critical' | 'warning' | 'info';
  cooldownMs: number;
  enabled: boolean;
  lastTriggered?: number;
}

export interface Metric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  labels: Record<string, string>;
  context?: LogContext;
}

export interface Alert {
  id: string;
  ruleId: string;
  severity: 'critical' | 'warning' | 'info';
  message: string;
  metric: Metric;
  timestamp: Date;
  resolved?: boolean;
  resolvedAt?: Date;
}

export interface SystemHealthMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  database: {
    connectionCount: number;
    activeQueries: number;
    avgResponseTime: number;
  };
  sms: {
    deliveryRate: number;
    avgProcessingTime: number;
    failureRate: number;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    keyCount: number;
  };
  queue: {
    pendingJobs: number;
    processingJobs: number;
    failedJobs: number;
  };
}

export class MonitoringService extends EventEmitter {
  private static instance: MonitoringService;
  private metrics: Map<string, Metric[]> = new Map();
  private alerts: Map<string, Alert> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private isRunning = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly maxMetricsPerType = 1000; // Prevent memory leaks
  
  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }
  
  constructor() {
    super();
    this.setupDefaultAlertRules();
  }
  
  /**
   * Start monitoring service
   */
  start(intervalMs: number = 60000): void {
    if (this.isRunning) {
      logger.warn('Monitoring service already running');
      return;
    }
    
    this.isRunning = true;
    
    // Start periodic health checks
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics().catch(error => {
        logger.error('Failed to collect system metrics', { component: 'monitoring' }, undefined, error);
      });
    }, intervalMs);
    
    logger.info('Monitoring service started', { component: 'monitoring' }, { intervalMs });
  }
  
  /**
   * Stop monitoring service
   */
  stop(): void {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    logger.info('Monitoring service stopped', { component: 'monitoring' });
  }
  
  /**
   * Record a metric
   */
  recordMetric(metric: Metric): void {
    const key = metric.name;
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const metricList = this.metrics.get(key)!;
    metricList.push(metric);
    
    // Prevent memory leaks by limiting stored metrics
    if (metricList.length > this.maxMetricsPerType) {
      metricList.shift(); // Remove oldest metric
    }
    
    // Check alert rules
    this.checkAlertRules(metric);
    
    // Emit metric event
    this.emit('metric', metric);
  }
  
  /**
   * Get metrics by name
   */
  getMetrics(name: string, limit?: number): Metric[] {
    const metrics = this.metrics.get(name) || [];
    return limit ? metrics.slice(-limit) : metrics;
  }
  
  /**
   * Get all metric names
   */
  getMetricNames(): string[] {
    return Array.from(this.metrics.keys());
  }
  
  /**
   * Get system health summary
   */
  async getSystemHealth(): Promise<SystemHealthMetrics> {
    try {
      // This would integrate with actual system monitoring
      // For now, return mock data based on available metrics
      
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      return {
        cpu: {
          usage: 0, // Would need external library for actual CPU usage
          loadAverage: process.platform === 'linux' ? require('os').loadavg() : [0, 0, 0]
        },
        memory: {
          used: memoryUsage.heapUsed,
          total: memoryUsage.heapTotal,
          percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
        },
        database: {
          connectionCount: this.getLatestMetricValue('db_connections') || 0,
          activeQueries: this.getLatestMetricValue('db_active_queries') || 0,
          avgResponseTime: this.getLatestMetricValue('db_avg_response_time') || 0
        },
        sms: {
          deliveryRate: this.getLatestMetricValue('sms_delivery_rate') || 0,
          avgProcessingTime: this.getLatestMetricValue('sms_avg_processing_time') || 0,
          failureRate: this.getLatestMetricValue('sms_failure_rate') || 0
        },
        cache: {
          hitRate: this.getLatestMetricValue('cache_hit_rate') || 0,
          memoryUsage: this.getLatestMetricValue('cache_memory_usage') || 0,
          keyCount: this.getLatestMetricValue('cache_key_count') || 0
        },
        queue: {
          pendingJobs: this.getLatestMetricValue('queue_pending_jobs') || 0,
          processingJobs: this.getLatestMetricValue('queue_processing_jobs') || 0,
          failedJobs: this.getLatestMetricValue('queue_failed_jobs') || 0
        }
      };
    } catch (error) {
      logger.error('Failed to get system health', { component: 'monitoring' }, undefined, error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
  
  /**
   * Add custom alert rule
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
    logger.info(`Alert rule added: ${rule.name}`, { component: 'monitoring' });
  }
  
  /**
   * Get all alerts
   */
  getAlerts(): Alert[] {
    return Array.from(this.alerts.values()).sort((a, b) => 
      b.timestamp.getTime() - a.timestamp.getTime()
    );
  }
  
  /**
   * Get active (unresolved) alerts
   */
  getActiveAlerts(): Alert[] {
    return this.getAlerts().filter(alert => !alert.resolved);
  }
  
  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.resolved) return false;
    
    alert.resolved = true;
    alert.resolvedAt = new Date();
    
    logger.info(`Alert resolved: ${alertId}`, { component: 'monitoring' });
    this.emit('alertResolved', alert);
    
    return true;
  }
  
  /**
   * Setup default alert rules
   */
  private setupDefaultAlertRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'high_memory_usage',
        name: 'High Memory Usage',
        condition: (metric) => metric.name === 'memory_usage_percent' && metric.value > 85,
        severity: 'critical',
        cooldownMs: 5 * 60 * 1000, // 5 minutes
        enabled: true
      },
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        condition: (metric) => metric.name === 'error_rate' && metric.value > 5,
        severity: 'critical',
        cooldownMs: 2 * 60 * 1000, // 2 minutes
        enabled: true
      },
      {
        id: 'low_sms_delivery_rate',
        name: 'Low SMS Delivery Rate',
        condition: (metric) => metric.name === 'sms_delivery_rate' && metric.value < 95,
        severity: 'warning',
        cooldownMs: 10 * 60 * 1000, // 10 minutes
        enabled: true
      },
      {
        id: 'high_response_time',
        name: 'High API Response Time',
        condition: (metric) => metric.name === 'api_response_time' && metric.value > 2000,
        severity: 'warning',
        cooldownMs: 5 * 60 * 1000, // 5 minutes
        enabled: true
      },
      {
        id: 'database_connection_limit',
        name: 'Database Connection Limit Reached',
        condition: (metric) => metric.name === 'db_connections' && metric.value > 8,
        severity: 'critical',
        cooldownMs: 2 * 60 * 1000, // 2 minutes
        enabled: true
      },
      {
        id: 'queue_backlog',
        name: 'Job Queue Backlog',
        condition: (metric) => metric.name === 'queue_pending_jobs' && metric.value > 100,
        severity: 'warning',
        cooldownMs: 5 * 60 * 1000, // 5 minutes
        enabled: true
      }
    ];
    
    defaultRules.forEach(rule => this.addAlertRule(rule));
  }
  
  /**
   * Check metric against alert rules
   */
  private checkAlertRules(metric: Metric): void {
    for (const rule of Array.from(this.alertRules.values())) {
      if (!rule.enabled) continue;
      
      // Check cooldown
      if (rule.lastTriggered && (Date.now() - rule.lastTriggered) < rule.cooldownMs) {
        continue;
      }
      
      // Check condition
      if (rule.condition(metric)) {
        this.triggerAlert(rule, metric);
      }
    }
  }
  
  /**
   * Trigger an alert
   */
  private triggerAlert(rule: AlertRule, metric: Metric): void {
    const alertId = `${rule.id}_${Date.now()}`;
    
    const alert: Alert = {
      id: alertId,
      ruleId: rule.id,
      severity: rule.severity,
      message: `${rule.name}: ${metric.name} = ${metric.value}${metric.unit}`,
      metric,
      timestamp: new Date(),
      resolved: false
    };
    
    this.alerts.set(alertId, alert);
    rule.lastTriggered = Date.now();
    
    // Log alert
    const logLevel = rule.severity === 'critical' ? 'error' : 'warn';
    logger[logLevel](
      `ALERT: ${alert.message}`,
      { component: 'monitoring', alertId, ruleId: rule.id },
      { metric }
    );
    
    // Emit alert event
    this.emit('alert', alert);
    
    // Send notifications (would integrate with external services)
    this.sendAlertNotification(alert).catch(error => {
      logger.error('Failed to send alert notification', { component: 'monitoring' }, undefined, error);
    });
  }
  
  /**
   * Send alert notification (placeholder for external integrations)
   */
  private async sendAlertNotification(alert: Alert): Promise<void> {
    // This would integrate with:
    // - Slack webhooks
    // - Email services
    // - PagerDuty
    // - Discord webhooks
    // - SMS alerts
    
    if (EnvironmentValidator.isDevelopment()) {
      logger.info(`ALERT NOTIFICATION: ${alert.message}`, { component: 'monitoring' });
    }
    
    // Example Slack webhook integration (commented out)
    /*
    try {
      const webhookUrl = process.env.SLACK_WEBHOOK_URL;
      if (webhookUrl && alert.severity === 'critical') {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 ${alert.message}`,
            attachments: [{
              color: alert.severity === 'critical' ? 'danger' : 'warning',
              fields: [
                { title: 'Metric', value: alert.metric.name, short: true },
                { title: 'Value', value: `${alert.metric.value}${alert.metric.unit}`, short: true },
                { title: 'Time', value: alert.timestamp.toISOString(), short: true }
              ]
            }]
          })
        });
      }
    } catch (error) {
      logger.error('Failed to send Slack notification', { component: 'monitoring' }, undefined, error);
    }
    */
  }
  
  /**
   * Collect system metrics periodically
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      const memoryUsage = process.memoryUsage();
      const timestamp = new Date();
      
      // Memory metrics
      this.recordMetric({
        name: 'memory_usage_percent',
        value: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
        unit: '%',
        timestamp,
        labels: {},
        context: { component: 'system' }
      });
      
      // Process uptime
      this.recordMetric({
        name: 'process_uptime',
        value: process.uptime(),
        unit: 'seconds',
        timestamp,
        labels: {},
        context: { component: 'system' }
      });
      
      // Event loop lag (simplified)
      const start = Date.now();
      setImmediate(() => {
        const lag = Date.now() - start;
        this.recordMetric({
          name: 'event_loop_lag',
          value: lag,
          unit: 'ms',
          timestamp: new Date(),
          labels: {},
          context: { component: 'system' }
        });
      });
      
    } catch (error) {
      logger.error('Failed to collect system metrics', { component: 'monitoring' }, undefined, error instanceof Error ? error : new Error(String(error)));
    }
  }
  
  /**
   * Get latest metric value by name
   */
  private getLatestMetricValue(name: string): number | null {
    const metrics = this.getMetrics(name, 1);
    return metrics.length > 0 ? metrics[0].value : null;
  }
  
  /**
   * Clean up old metrics and alerts
   */
  cleanup(maxAgeMs: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAgeMs;
    
    // Clean up old metrics
    for (const [name, metrics] of Array.from(this.metrics.entries())) {
      const filtered = metrics.filter((m: Metric) => m.timestamp.getTime() > cutoff);
      this.metrics.set(name, filtered);
    }
    
    // Clean up old resolved alerts
    for (const [id, alert] of Array.from(this.alerts.entries())) {
      if (alert.resolved && alert.resolvedAt && alert.resolvedAt.getTime() < cutoff) {
        this.alerts.delete(id);
      }
    }
    
    logger.info('Monitoring data cleanup completed', { component: 'monitoring' });
  }
}

// Singleton instance
export const monitoringService = MonitoringService.getInstance();

// Convenience functions
export function recordMetric(name: string, value: number, unit: string = '', labels?: Record<string, string>): void {
  monitoringService.recordMetric({
    name,
    value,
    unit,
    timestamp: new Date(),
    labels: labels || {}
  });
}

export function startMonitoring(intervalMs?: number): void {
  monitoringService.start(intervalMs);
}

export function stopMonitoring(): void {
  monitoringService.stop();
}

export default MonitoringService;