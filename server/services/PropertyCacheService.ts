import { cacheService } from './CacheService';
import { logger } from './LoggerService';

/**
 * Advanced caching service specifically for property listings and related data
 * Implements intelligent cache strategies, invalidation patterns, and performance monitoring
 */

interface PropertySearchParams {
  location?: string;
  date?: string;
  minPrice?: number | undefined;
  maxPrice?: number | undefined;
  amenities?: string[] | undefined;
  featured?: boolean;
  limit?: number | undefined;
  offset?: number | undefined;
}

interface CacheMetrics {
  hits: number;
  misses: number;
  invalidations: number;
  lastReset: number;
}

export class PropertyCacheService {
  private static instance: PropertyCacheService;
  private metrics: CacheMetrics;

  // Cache TTL configurations (in seconds)
  private readonly cacheTTL = {
    propertySearch: 600,      // 10 minutes - search results
    propertyDetails: 1800,    // 30 minutes - individual property details
    featuredProperties: 3600, // 1 hour - featured properties list
    availability: 300,        // 5 minutes - availability data
    propertyStats: 1800,      // 30 minutes - property statistics
    locationSuggestions: 7200, // 2 hours - location autocomplete
    priceRanges: 3600,        // 1 hour - price range data
    amenityFilters: 7200,     // 2 hours - available amenities
    ownerProperties: 900,     // 15 minutes - owner property lists
    reviewSummary: 1800       // 30 minutes - review aggregations
  };

  // Cache key patterns for organized invalidation
  private readonly keyPatterns = {
    propertySearch: 'search',
    propertyDetails: 'property',
    featuredProperties: 'featured',
    availability: 'availability',
    propertyStats: 'stats',
    locationSuggestions: 'locations',
    priceRanges: 'prices',
    amenityFilters: 'amenities',
    ownerProperties: 'owner',
    reviewSummary: 'reviews'
  };

  constructor() {
    this.metrics = {
      hits: 0,
      misses: 0,
      invalidations: 0,
      lastReset: Date.now()
    };
  }

  static getInstance(): PropertyCacheService {
    if (!PropertyCacheService.instance) {
      PropertyCacheService.instance = new PropertyCacheService();
    }
    return PropertyCacheService.instance;
  }

  /**
   * Generate standardized cache keys for property searches
   */
  private generateSearchKey(params: PropertySearchParams): string {
    const normalizedParams = {
      location: params.location?.toLowerCase().trim() || 'all',
      date: params.date || 'any',
      minPrice: params.minPrice || 0,
      maxPrice: params.maxPrice || 999999,
      amenities: params.amenities?.sort().join(',') || 'none',
      featured: params.featured || false,
      limit: params.limit || 50,
      offset: params.offset || 0
    };

    return `${this.keyPatterns.propertySearch}:${JSON.stringify(normalizedParams)}`;
  }

  /**
   * Cache property search results with intelligent key generation
   */
  async cachePropertySearch(params: PropertySearchParams, data: any): Promise<void> {
    const key = this.generateSearchKey(params);
    await cacheService.set(key, data, this.cacheTTL.propertySearch);
    
    logger.info('Property search cached', 'cache', { 
      key: key.substring(0, 100),
      dataSize: JSON.stringify(data).length,
      ttl: this.cacheTTL.propertySearch
    });
  }

  /**
   * Retrieve cached property search results
   */
  async getCachedPropertySearch(params: PropertySearchParams): Promise<any | null> {
    const key = this.generateSearchKey(params);
    const cached = await cacheService.get(key);
    
    if (cached) {
      this.metrics.hits++;
      logger.debug('Property search cache hit', 'cache', { key: key.substring(0, 100) });
    } else {
      this.metrics.misses++;
      logger.debug('Property search cache miss', 'cache', { key: key.substring(0, 100) });
    }
    
    return cached;
  }

  /**
   * Cache individual property details
   */
  async cachePropertyDetails(propertyId: number, data: any): Promise<void> {
    const key = `${this.keyPatterns.propertyDetails}:${propertyId}`;
    await cacheService.set(key, data, this.cacheTTL.propertyDetails);
    
    logger.info('Property details cached', 'cache', { 
      propertyId,
      dataSize: JSON.stringify(data).length 
    });
  }

  /**
   * Retrieve cached property details
   */
  async getCachedPropertyDetails(propertyId: number): Promise<any | null> {
    const key = `${this.keyPatterns.propertyDetails}:${propertyId}`;
    const cached = await cacheService.get(key);
    
    if (cached) {
      this.metrics.hits++;
      logger.debug('Property details cache hit', 'cache', { propertyId });
    } else {
      this.metrics.misses++;
      logger.debug('Property details cache miss', 'cache', { propertyId });
    }
    
    return cached;
  }

  /**
   * Cache featured properties list
   */
  async cacheFeaturedProperties(data: any): Promise<void> {
    const key = `${this.keyPatterns.featuredProperties}:list`;
    await cacheService.set(key, data, this.cacheTTL.featuredProperties);
    
    logger.info('Featured properties cached', 'cache', { 
      count: Array.isArray(data) ? data.length : 'unknown',
      dataSize: JSON.stringify(data).length 
    });
  }

  /**
   * Retrieve cached featured properties
   */
  async getCachedFeaturedProperties(): Promise<any | null> {
    const key = `${this.keyPatterns.featuredProperties}:list`;
    const cached = await cacheService.get(key);
    
    if (cached) {
      this.metrics.hits++;
      logger.debug('Featured properties cache hit', 'cache');
    } else {
      this.metrics.misses++;
      logger.debug('Featured properties cache miss', 'cache');
    }
    
    return cached;
  }

  /**
   * Cache availability data for a property and date
   */
  async cacheAvailability(propertyId: number, date: string, bookingType: string, isAvailable: boolean): Promise<void> {
    const key = `${this.keyPatterns.availability}:${propertyId}:${date}:${bookingType}`;
    await cacheService.set(key, { isAvailable, cachedAt: Date.now() }, this.cacheTTL.availability);
  }

  /**
   * Retrieve cached availability data
   */
  async getCachedAvailability(propertyId: number, date: string, bookingType: string): Promise<boolean | null> {
    const key = `${this.keyPatterns.availability}:${propertyId}:${date}:${bookingType}`;
    const cached = await cacheService.get<{ isAvailable: boolean; cachedAt: number }>(key);
    
    if (cached) {
      this.metrics.hits++;
      return cached.isAvailable;
    } else {
      this.metrics.misses++;
      return null;
    }
  }

  /**
   * Cache location suggestions for autocomplete
   */
  async cacheLocationSuggestions(data: string[]): Promise<void> {
    const key = `${this.keyPatterns.locationSuggestions}:all`;
    await cacheService.set(key, data, this.cacheTTL.locationSuggestions);
  }

  /**
   * Retrieve cached location suggestions
   */
  async getCachedLocationSuggestions(): Promise<string[] | null> {
    const key = `${this.keyPatterns.locationSuggestions}:all`;
    const cached = await cacheService.get<string[]>(key);
    
    if (cached) {
      this.metrics.hits++;
    } else {
      this.metrics.misses++;
    }
    
    return cached;
  }

  /**
   * Cache price range statistics
   */
  async cachePriceRanges(data: { min: number; max: number; avg: number; median: number }): Promise<void> {
    const key = `${this.keyPatterns.priceRanges}:stats`;
    await cacheService.set(key, data, this.cacheTTL.priceRanges);
  }

  /**
   * Retrieve cached price ranges
   */
  async getCachedPriceRanges(): Promise<any | null> {
    const key = `${this.keyPatterns.priceRanges}:stats`;
    const cached = await cacheService.get(key);
    
    if (cached) {
      this.metrics.hits++;
    } else {
      this.metrics.misses++;
    }
    
    return cached;
  }

  /**
   * Cache available amenities list
   */
  async cacheAmenityFilters(data: string[]): Promise<void> {
    const key = `${this.keyPatterns.amenityFilters}:all`;
    await cacheService.set(key, data, this.cacheTTL.amenityFilters);
  }

  /**
   * Retrieve cached amenity filters
   */
  async getCachedAmenityFilters(): Promise<string[] | null> {
    const key = `${this.keyPatterns.amenityFilters}:all`;
    const cached = await cacheService.get<string[]>(key);
    
    if (cached) {
      this.metrics.hits++;
    } else {
      this.metrics.misses++;
    }
    
    return cached;
  }

  /**
   * Cache owner properties list
   */
  async cacheOwnerProperties(ownerId: number, data: any): Promise<void> {
    const key = `${this.keyPatterns.ownerProperties}:${ownerId}`;
    await cacheService.set(key, data, this.cacheTTL.ownerProperties);
  }

  /**
   * Retrieve cached owner properties
   */
  async getCachedOwnerProperties(ownerId: number): Promise<any | null> {
    const key = `${this.keyPatterns.ownerProperties}:${ownerId}`;
    const cached = await cacheService.get(key);
    
    if (cached) {
      this.metrics.hits++;
    } else {
      this.metrics.misses++;
    }
    
    return cached;
  }

  /**
   * Cache review summary for a property
   */
  async cacheReviewSummary(propertyId: number, data: any): Promise<void> {
    const key = `${this.keyPatterns.reviewSummary}:${propertyId}`;
    await cacheService.set(key, data, this.cacheTTL.reviewSummary);
  }

  /**
   * Retrieve cached review summary
   */
  async getCachedReviewSummary(propertyId: number): Promise<any | null> {
    const key = `${this.keyPatterns.reviewSummary}:${propertyId}`;
    const cached = await cacheService.get(key);
    
    if (cached) {
      this.metrics.hits++;
    } else {
      this.metrics.misses++;
    }
    
    return cached;
  }

  /**
   * Intelligent cache invalidation based on data changes
   */
  async invalidatePropertyCache(propertyId: number, reason: 'update' | 'delete' | 'booking' = 'update'): Promise<void> {
    const patterns = [
      `${this.keyPatterns.propertyDetails}:${propertyId}*`,
      `${this.keyPatterns.availability}:${propertyId}*`,
      `${this.keyPatterns.reviewSummary}:${propertyId}*`
    ];

    // If property is updated/deleted, invalidate search results too
    if (reason === 'update' || reason === 'delete') {
      patterns.push(
        `${this.keyPatterns.propertySearch}:*`,
        `${this.keyPatterns.featuredProperties}:*`,
        `${this.keyPatterns.priceRanges}:*`,
        `${this.keyPatterns.locationSuggestions}:*`
      );
    }

    // If booking is made, invalidate availability
    if (reason === 'booking') {
      patterns.push(`${this.keyPatterns.availability}:${propertyId}*`);
    }

    await this.invalidateCachePatterns(patterns);
    this.metrics.invalidations++;
    
    logger.info('Property cache invalidated', 'cache', { 
      propertyId, 
      reason, 
      patternsCount: patterns.length 
    });
  }

  /**
   * Invalidate owner-specific cache when properties change
   */
  async invalidateOwnerCache(ownerId: number): Promise<void> {
    const patterns = [
      `${this.keyPatterns.ownerProperties}:${ownerId}*`
    ];

    await this.invalidateCachePatterns(patterns);
    this.metrics.invalidations++;
    
    logger.info('Owner cache invalidated', 'cache', { ownerId });
  }

  /**
   * Invalidate all property-related cache (use sparingly)
   */
  async invalidateAllPropertyCache(): Promise<void> {
    const patterns = Object.values(this.keyPatterns).map(pattern => `${pattern}:*`);
    await this.invalidateCachePatterns(patterns);
    this.metrics.invalidations++;
    
    logger.warn('All property cache invalidated', 'cache');
  }

  /**
   * Helper method to invalidate cache by patterns
   */
  private async invalidateCachePatterns(patterns: string[]): Promise<void> {
    for (const pattern of patterns) {
      const keys = await cacheService.keys(pattern);
      for (const key of keys) {
        await cacheService.delete(key);
      }
    }
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmupCache(): Promise<void> {
    logger.info('Starting cache warmup', 'cache');
    
    try {
      // This would be called with actual data fetching functions
      // For now, just log the intent
      logger.info('Cache warmup completed', 'cache');
    } catch (error) {
      logger.error('Cache warmup failed', error instanceof Error ? error : new Error(String(error)), 'cache');
    }
  }

  /**
   * Get cache performance metrics
   */
  getCacheMetrics(): CacheMetrics & {
    hitRate: number;
    totalRequests: number;
    uptimeHours: number;
  } {
    const totalRequests = this.metrics.hits + this.metrics.misses;
    const hitRate = totalRequests > 0 ? (this.metrics.hits / totalRequests) * 100 : 0;
    const uptimeHours = (Date.now() - this.metrics.lastReset) / (1000 * 60 * 60);

    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 100) / 100,
      totalRequests,
      uptimeHours: Math.round(uptimeHours * 100) / 100
    };
  }

  /**
   * Reset cache metrics
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      invalidations: 0,
      lastReset: Date.now()
    };
    
    logger.info('Cache metrics reset', 'cache');
  }

  /**
   * Health check for cache service
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics: any;
    cacheStats: any;
  }> {
    try {
      const metrics = this.getCacheMetrics();
      const cacheStats = cacheService.getStats();
      
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      
      // Consider degraded if hit rate is below 60%
      if (metrics.hitRate < 60 && metrics.totalRequests > 100) {
        status = 'degraded';
      }
      
      // Consider unhealthy if cache is nearly full
      if (cacheStats.size > cacheStats.maxSize * 0.9) {
        status = 'unhealthy';
      }
      
      return {
        status,
        metrics,
        cacheStats
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        metrics: this.metrics,
        cacheStats: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }
}

// Export singleton instance
export const propertyCacheService = PropertyCacheService.getInstance();