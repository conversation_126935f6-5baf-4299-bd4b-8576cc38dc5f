import crypto from 'crypto';
import { db } from "../db";
import { encryption<PERSON><PERSON>s, InsertEncryptionKey, Encryption<PERSON>ey } from "../../shared/schema";
import { eq, and, lt } from "drizzle-orm";

interface EncryptionConfig {
  algorithm: string;
  keyLength: number;
  ivLength: number;
}

interface EncryptedData {
  encryptedValue: string;
  keyId: string;
  algorithm: string;
  iv: string;
  tag?: string; // For authenticated encryption
}

export class FieldEncryptionService {
  private static instance: FieldEncryptionService;
  private readonly config: EncryptionConfig;
  private keyCache: Map<string, EncryptionKey> = new Map();
  private masterKey: string;

  private constructor() {
    this.config = {
      algorithm: 'aes-256-gcm',
      keyLength: 32, // 256 bits
      ivLength: 16   // 128 bits
    };
    
    // In production, this should come from environment variables or key management service
    this.masterKey = process.env.ENCRYPTION_MASTER_KEY || this.generateMasterKey();
    
    if (!process.env.ENCRYPTION_MASTER_KEY) {
      console.warn('WARNING: Using generated master key. Set ENCRYPTION_MASTER_KEY in production.');
    }
  }

  public static getInstance(): FieldEncryptionService {
    if (!FieldEncryptionService.instance) {
      FieldEncryptionService.instance = new FieldEncryptionService();
    }
    return FieldEncryptionService.instance;
  }

  // Encrypt sensitive data
  async encryptSensitiveData(data: string, purpose: string = 'payment'): Promise<EncryptedData> {
    try {
      // Get or create encryption key
      const keyId = await this.getOrCreateEncryptionKey(purpose);
      const encryptionKey = await this.getEncryptionKey(keyId);
      
      if (!encryptionKey) {
        throw new Error('Failed to retrieve encryption key');
      }

      // Decrypt the stored key using master key
      const decryptedKey = this.decryptKey(encryptionKey.encryptedKey);
      
      // Generate random IV
      const iv = crypto.randomBytes(this.config.ivLength);
      
      // Create cipher
      const cipher = crypto.createCipheriv(this.config.algorithm, decryptedKey, iv);
      (cipher as any).setAAD(Buffer.from(keyId)); // Additional authenticated data
      
      // Encrypt the data
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Get authentication tag for GCM mode
      const tag = (cipher as any).getAuthTag();

      return {
        encryptedValue: encrypted,
        keyId: keyId,
        algorithm: this.config.algorithm,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      };
    } catch (error) {
      console.error('Failed to encrypt sensitive data:', error);
      throw new Error('Encryption failed');
    }
  }

  // Decrypt sensitive data
  async decryptSensitiveData(encryptedData: EncryptedData): Promise<string> {
    try {
      // Get encryption key
      const encryptionKey = await this.getEncryptionKey(encryptedData.keyId);
      
      if (!encryptionKey || !encryptionKey.isActive) {
        throw new Error('Encryption key not found or inactive');
      }

      // Decrypt the stored key using master key
      const decryptedKey = this.decryptKey(encryptionKey.encryptedKey);
      
      // Create decipher
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv(encryptedData.algorithm, decryptedKey, iv);
      (decipher as any).setAAD(Buffer.from(encryptedData.keyId));
      
      if (encryptedData.tag) {
        (decipher as any).setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
      }

      // Decrypt the data
      let decrypted = decipher.update(encryptedData.encryptedValue, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Failed to decrypt sensitive data:', error);
      throw new Error('Decryption failed');
    }
  }

  // Hash sensitive data for comparison purposes
  hashSensitiveData(data: string, salt?: string): string {
    const saltToUse = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(data, saltToUse, 10000, 64, 'sha512');
    return `${saltToUse}:${hash.toString('hex')}`;
  }

  // Verify hashed data
  verifySensitiveData(data: string, hashedData: string): boolean {
    try {
      const [salt, hash] = hashedData.split(':');
      const hashToVerify = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512');
      return hash === hashToVerify.toString('hex');
    } catch (error) {
      console.error('Failed to verify sensitive data:', error);
      return false;
    }
  }

  // Create new encryption key
  async createEncryptionKey(purpose: string, keyType: string = 'aes256'): Promise<string> {
    try {
      const keyId = this.generateKeyId(purpose);
      const dataEncryptionKey = crypto.randomBytes(this.config.keyLength);
      
      // Encrypt the DEK with master key
      const encryptedKey = this.encryptKey(dataEncryptionKey);
      
      const keyRecord: InsertEncryptionKey = {
        keyId,
        keyType,
        encryptedKey,
        keyVersion: 1,
        isActive: true,
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      };

      await db.insert(encryptionKeys).values(keyRecord);
      
      // Update cache
      this.keyCache.set(keyId, {
        id: 0, // Will be set by database
        keyType: keyRecord.keyType,
        createdAt: new Date(),
        expiresAt: keyRecord.expiresAt || null,
        isActive: keyRecord.isActive || null,
        keyId: keyRecord.keyId,
        encryptedKey: keyRecord.encryptedKey,
        keyVersion: keyRecord.keyVersion,
      });

      console.log(`Created new encryption key: ${keyId}`);
      return keyId;
    } catch (error) {
      console.error('Failed to create encryption key:', error);
      throw new Error('Key creation failed');
    }
  }

  // Get or create encryption key for a purpose
  async getOrCreateEncryptionKey(purpose: string): Promise<string> {
    const keyId = this.generateKeyId(purpose);
    
    // Check if key already exists
    const existingKey = await this.getEncryptionKey(keyId);
    if (existingKey && existingKey.isActive) {
      return keyId;
    }

    // Create new key
    return await this.createEncryptionKey(purpose);
  }

  // Get encryption key from database or cache
  async getEncryptionKey(keyId: string): Promise<EncryptionKey | null> {
    try {
      // Check cache first
      if (this.keyCache.has(keyId)) {
        const cachedKey = this.keyCache.get(keyId);
        // Verify key is valid and not corrupted
        if (cachedKey && cachedKey.isActive && (!cachedKey.expiresAt || cachedKey.expiresAt > new Date())) {
          return cachedKey;
        }
        // Remove corrupted or invalid cache entry
        this.keyCache.delete(keyId);
      }

      // Query database
      const [key] = await db
        .select()
        .from(encryptionKeys)
        .where(and(
          eq(encryptionKeys.keyId, keyId),
          eq(encryptionKeys.isActive, true)
        ));

      if (key) {
        // Update cache
        this.keyCache.set(keyId, key);
        return key;
      }

      return null;
    } catch (error) {
      console.error('Failed to get encryption key:', error);
      return null;
    }
  }

  // Rotate encryption key
  async rotateEncryptionKey(keyId: string): Promise<string> {
    try {
      // Deactivate old key
      await db
        .update(encryptionKeys)
        .set({ isActive: false })
        .where(eq(encryptionKeys.keyId, keyId));

      // Remove from cache
      this.keyCache.delete(keyId);

      // Extract purpose from keyId
      const purpose = keyId.split('_')[0];
      
      // Create new key
      return await this.createEncryptionKey(purpose);
    } catch (error) {
      console.error('Failed to rotate encryption key:', error);
      throw new Error('Key rotation failed');
    }
  }

  // Encrypt Razorpay signature for secure storage
  async encryptRazorpaySignature(signature: string): Promise<EncryptedData> {
    return await this.encryptSensitiveData(signature, 'razorpay_signature');
  }

  // Decrypt Razorpay signature for verification
  async decryptRazorpaySignature(encryptedData: EncryptedData): Promise<string> {
    return await this.decryptSensitiveData(encryptedData);
  }

  // Encrypt payment method details
  async encryptPaymentMethod(paymentMethodData: any): Promise<EncryptedData> {
    const dataString = JSON.stringify(paymentMethodData);
    return await this.encryptSensitiveData(dataString, 'payment_method');
  }

  // Decrypt payment method details
  async decryptPaymentMethod(encryptedData: EncryptedData): Promise<any> {
    const dataString = await this.decryptSensitiveData(encryptedData);
    return JSON.parse(dataString);
  }

  // Private helper methods
  private generateKeyId(purpose: string): string {
    const timestamp = Date.now().toString();
    const random = crypto.randomBytes(4).toString('hex');
    return `${purpose}_${timestamp}_${random}`;
  }

  private generateMasterKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private encryptKey(key: Buffer): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(this.masterKey, 'hex'), iv);
    let encrypted = cipher.update(key, undefined, 'hex');
    encrypted += cipher.final('hex');
    const tag = cipher.getAuthTag();
    return `${iv.toString('hex')}:${encrypted}:${tag.toString('hex')}`;
  }

  private decryptKey(encryptedKey: string): Buffer {
    const parts = encryptedKey.split(':');
    if (parts.length === 2) {
      // Legacy format without IV (for backwards compatibility)
      const [encrypted, tag] = parts;
      const iv = Buffer.alloc(16); // Use zero IV for legacy keys
      const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(this.masterKey, 'hex'), iv);
      decipher.setAuthTag(Buffer.from(tag, 'hex'));
      let decrypted = decipher.update(encrypted, 'hex');
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      return decrypted;
    } else {
      // New format with IV
      const [ivHex, encrypted, tag] = parts;
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(this.masterKey, 'hex'), iv);
      decipher.setAuthTag(Buffer.from(tag, 'hex'));
      let decrypted = decipher.update(encrypted, 'hex');
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      return decrypted;
    }
  }

  // Cleanup expired keys
  async cleanupExpiredKeys(): Promise<void> {
    try {
      const now = new Date();
      await db
        .update(encryptionKeys)
        .set({ isActive: false })
        .where(lt(encryptionKeys.expiresAt, now));

      // Clear cache of expired keys
      for (const [keyId, key] of Array.from(this.keyCache.entries())) {
        if (key.expiresAt && key.expiresAt < now) {
          this.keyCache.delete(keyId);
        }
      }

      console.log('Cleaned up expired encryption keys');
    } catch (error) {
      console.error('Failed to cleanup expired keys:', error);
    }
  }
}

// Export singleton instance
export const fieldEncryptionService = FieldEncryptionService.getInstance();