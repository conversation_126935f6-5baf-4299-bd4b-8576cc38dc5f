import { db } from '../db';
import { databaseManager } from '../utils/database';
import { config } from '../config';
import { cacheService } from './CacheService';
import { otpService } from './OTPService';

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
}

export interface HealthReport {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: HealthCheck[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}

export class HealthService {
  private static readonly TIMEOUT_MS = 5000; // 5 second timeout

  static async performHealthCheck(): Promise<HealthReport> {
    // Run all health checks in parallel
    const healthChecks = await Promise.all([
      this.checkDatabase(),
      this.checkCache(),
      this.checkOTPServices(),
      this.checkExternalServices(),
      this.checkSystemResources()
    ]);

    const services = healthChecks.flat();
    
    // Calculate summary
    const summary = {
      total: services.length,
      healthy: services.filter(s => s.status === 'healthy').length,
      unhealthy: services.filter(s => s.status === 'unhealthy').length,
      degraded: services.filter(s => s.status === 'degraded').length
    };

    // Determine overall status
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.app.nodeEnv,
      services,
      summary
    };
  }

  private static async checkDatabase(): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = [];

    // Database connectivity check using our enhanced connection test
    const dbCheck = await this.runHealthCheck('database-connection', async () => {
      const startTime = Date.now();
      const success = await databaseManager.testConnection();
      const responseTime = Date.now() - startTime;
      
      if (!success) {
        throw new Error('Database connection test failed');
      }
      
      return {
        responseTime,
        details: { 
          query: 'SELECT 1',
          method: 'testDatabaseConnection',
          connectionPoolStatus: 'active'
        }
      };
    });
    checks.push(dbCheck);

    // Database performance check (optional) - only run in development or when explicitly requested
    if (dbCheck.status === 'healthy' && (process.env.NODE_ENV === 'development' || process.env.ENABLE_DB_PERFORMANCE_CHECK === 'true')) {
      const dbPerformanceCheck = await this.runHealthCheck('database-performance', async () => {
        const startTime = Date.now();
        
        try {
          // Simple performance test - count users
          const result = await db.execute('SELECT COUNT(*) as count FROM users');
          const responseTime = Date.now() - startTime;
          
          return {
            responseTime,
            details: { 
              userCount: (result.rows[0] as any)?.count || 0,
              threshold: 1000, // ms
              performanceStatus: responseTime < 1000 ? 'good' : 'slow'
            }
          };
        } catch (error) {
          // If performance check fails, it's degraded but not unhealthy
          throw new Error(`Database performance check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });
      
      if (dbPerformanceCheck.responseTime > 1000) {
        dbPerformanceCheck.status = 'degraded';
        dbPerformanceCheck.message = 'Database response time is slow (>1000ms)';
      }
      
      checks.push(dbPerformanceCheck);
    } else if (process.env.NODE_ENV !== 'development') {
      // In production, skip performance check to reduce database load
      checks.push({
        name: 'database-performance',
        status: 'healthy',
        responseTime: 0,
        message: 'Skipped in production (enable with ENABLE_DB_PERFORMANCE_CHECK=true)',
        details: { reason: 'production_optimization' }
      });
    }

    return checks;
  }

  private static async checkCache(): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = [];

    // Cache service check
    const cacheCheck = await this.runHealthCheck('cache-service', async () => {
      const startTime = Date.now();
      const testKey = 'health-check-test';
      const testValue = { timestamp: Date.now() };
      
      // Test set and get
      await cacheService.set(testKey, testValue, 5);
      const retrieved = await cacheService.get(testKey);
      await cacheService.delete(testKey);
      
      if (!retrieved || (retrieved as any).timestamp !== testValue.timestamp) {
        throw new Error('Cache set/get test failed');
      }

      const stats = cacheService.getStats();
      
      return {
        responseTime: Date.now() - startTime,
        details: stats
      };
    });
    checks.push(cacheCheck);

    return checks;
  }

  private static async checkOTPServices(): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = [];

    // OTP service status
    const otpCheck = await this.runHealthCheck('otp-services', async () => {
      const startTime = Date.now();
      const status = otpService.getServiceStatus();
      
      return {
        responseTime: Date.now() - startTime,
        details: status
      };
    });

    // Mark as degraded if external services aren't available in production
    if (config.isProduction()) {
      const status = otpService.getServiceStatus();
      if (!status.email.available || !status.sms.available) {
        otpCheck.status = 'degraded';
        otpCheck.message = 'External OTP services not fully available';
      }
    }

    checks.push(otpCheck);

    return checks;
  }

  private static async checkExternalServices(): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = [];

    // Configuration status
    const configCheck = await this.runHealthCheck('configuration', async () => {
      const startTime = Date.now();
      const healthStatus = config.getHealthStatus();
      
      return {
        responseTime: Date.now() - startTime,
        details: healthStatus
      };
    });
    checks.push(configCheck);

    return checks;
  }

  private static async checkSystemResources(): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = [];

    // Memory usage check
    const memoryCheck = await this.runHealthCheck('system-memory', async () => {
      const startTime = Date.now();
      const memUsage = process.memoryUsage();
      
      const memoryUsageMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      };

      return {
        responseTime: Date.now() - startTime,
        details: {
          memoryUsage: memoryUsageMB,
          uptime: process.uptime(),
          nodeVersion: process.version
        }
      };
    });

    // Check if memory usage is too high (warning threshold)
    const heapUsedMB = Math.round(process.memoryUsage().heapUsed / 1024 / 1024);
    if (heapUsedMB > 512) { // 512MB threshold
      memoryCheck.status = 'degraded';
      memoryCheck.message = `High memory usage: ${heapUsedMB}MB`;
    }

    checks.push(memoryCheck);

    return checks;
  }

  private static async runHealthCheck(
    name: string, 
    checkFn: () => Promise<{ responseTime: number; details?: any }>
  ): Promise<HealthCheck> {
    try {
      const result = await Promise.race([
        checkFn(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), this.TIMEOUT_MS)
        )
      ]);

      return {
        name,
        status: 'healthy',
        responseTime: result.responseTime,
        details: result.details
      };
    } catch (error: any) {
      return {
        name,
        status: 'unhealthy',
        responseTime: this.TIMEOUT_MS,
        message: error.message || 'Health check failed',
        details: { error: error.message }
      };
    }
  }

  // Quick health check for load balancers
  static async quickHealthCheck(): Promise<{
    status: 'ok' | 'error';
    timestamp: string;
    uptime: number;
  }> {
    try {
      // In production, use cached result to reduce database load
      if (process.env.NODE_ENV === 'production') {
        // Use database manager's last health check result if recent (within 5 minutes)
        const databaseManager = require('../utils/database').databaseManager;
        const metrics = databaseManager.getMetrics();
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        
        if (metrics.lastHealthCheck && new Date(metrics.lastHealthCheck) > fiveMinutesAgo) {
          return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
          };
        }
      }
      
      // Fallback to database check
      await db.execute('SELECT 1');
      
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };
    }
  }

  // Readiness check (is the service ready to accept traffic?)
  static async readinessCheck(): Promise<{
    ready: boolean;
    checks: string[];
    timestamp: string;
  }> {
    const criticalChecks = [
      'database-connection',
      'cache-service'
    ];

    const results = await Promise.all([
      this.checkDatabase(),
      this.checkCache()
    ]);

    const allChecks = results.flat();
    const failedCriticalChecks = allChecks
      .filter(check => criticalChecks.includes(check.name) && check.status === 'unhealthy')
      .map(check => check.name);

    return {
      ready: failedCriticalChecks.length === 0,
      checks: failedCriticalChecks,
      timestamp: new Date().toISOString()
    };
  }

  // Liveness check (is the service alive?)
  static async livenessCheck(): Promise<{
    alive: boolean;
    timestamp: string;
    uptime: number;
  }> {
    try {
      // Basic checks to ensure the process is functioning
      const uptime = process.uptime();
      
      // If we can get uptime, we're alive
      return {
        alive: true,
        timestamp: new Date().toISOString(),
        uptime
      };
    } catch (error) {
      return {
        alive: false,
        timestamp: new Date().toISOString(),
        uptime: 0
      };
    }
  }
}