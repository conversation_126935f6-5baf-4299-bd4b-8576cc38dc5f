import { db } from '../db';
import { smsTemplates, smsLogs } from '../../shared/schema';
import { eq, and, desc, asc } from 'drizzle-orm';
import type { SmsTemplate, InsertSmsTemplate, SmsLog, InsertSmsLog } from '../../shared/schema';
import { CacheService, cacheService } from './CacheService';
import { withDatabaseErrorBoundary } from '../utils/error-boundary';

interface TemplateCache {
  template: SmsTemplate;
  lastAccessed: number;
  accessCount: number;
}

export class CachedTemplateService {
  private static readonly CACHE_KEYS = {
    TEMPLATE_BY_KEY: 'template:key',
    TEMPLATE_BY_ID: 'template:id',
    ALL_TEMPLATES: 'templates:all',
    ACTIVE_TEMPLATES: 'templates:active',
    TEMPLATE_STATS: 'template:stats'
  };

  private static readonly CACHE_TTL = {
    TEMPLATE: 3600, // 1 hour - templates don't change often
    ALL_TEMPLATES: 1800, // 30 minutes
    STATS: 300 // 5 minutes
  };

  private static accessStats = new Map<string, { count: number; lastAccess: number }>();

  // High-performance cached template retrieval
  async getActiveTemplateByKey(key: string): Promise<SmsTemplate | null> {
    const cacheKey = CacheService.generateKey(CachedTemplateService.CACHE_KEYS.TEMPLATE_BY_KEY, key);
    
    // Track template access for analytics regardless of cache hit/miss
    this.trackTemplateAccess(key);
    
    return CacheService.getOrSet(
      cacheKey,
      async () => {
        return withDatabaseErrorBoundary(
          async () => {
            const templates = await db
              .select()
              .from(smsTemplates)
              .where(and(eq(smsTemplates.key, key), eq(smsTemplates.status, 'active')))
              .orderBy(desc(smsTemplates.updatedAt))
              .limit(1);

            const template = templates[0] || null;
            
            return template;
          },
          'get-template-by-key'
        );
      },
      CachedTemplateService.CACHE_TTL.TEMPLATE
    );
  }

  async getTemplateById(id: number): Promise<SmsTemplate | null> {
    const cacheKey = CacheService.generateKey(CachedTemplateService.CACHE_KEYS.TEMPLATE_BY_ID, id);
    
    return CacheService.getOrSet(
      cacheKey,
      async () => {
        return withDatabaseErrorBoundary(
          async () => {
            const templates = await db
              .select()
              .from(smsTemplates)
              .where(eq(smsTemplates.id, id))
              .limit(1);

            return templates[0] || null;
          },
          'get-template-by-id'
        );
      },
      CachedTemplateService.CACHE_TTL.TEMPLATE
    );
  }

  async getAllTemplates(status?: string): Promise<SmsTemplate[]> {
    const cacheKey = status 
      ? CacheService.generateKey(CachedTemplateService.CACHE_KEYS.ALL_TEMPLATES, status)
      : CachedTemplateService.CACHE_KEYS.ALL_TEMPLATES;
    
    return CacheService.getOrSet(
      cacheKey,
      async () => {
        return withDatabaseErrorBoundary(
          async () => {
            const query = db.select().from(smsTemplates);
            
            if (status) {
              query.where(eq(smsTemplates.status, status));
            }
            
            return await query.orderBy(desc(smsTemplates.createdAt));
          },
          'get-all-templates'
        );
      },
      CachedTemplateService.CACHE_TTL.ALL_TEMPLATES
    );
  }

  // Preload frequently used templates into cache
  async warmCache(): Promise<void> {
    console.log('🔥 Warming template cache...');
    
    try {
      // Get all active templates and cache them
      const activeTemplates = await this.getAllTemplates('active');
      
      // Cache each template by key for faster access
      const cachePromises = activeTemplates.map(async (template) => {
        const keyCache = CacheService.generateKey(
          CachedTemplateService.CACHE_KEYS.TEMPLATE_BY_KEY, 
          template.key
        );
        const idCache = CacheService.generateKey(
          CachedTemplateService.CACHE_KEYS.TEMPLATE_BY_ID, 
          template.id
        );
        
        await Promise.all([
          cacheService.set(keyCache, template, CachedTemplateService.CACHE_TTL.TEMPLATE),
          cacheService.set(idCache, template, CachedTemplateService.CACHE_TTL.TEMPLATE)
        ]);
      });

      await Promise.all(cachePromises);
      
      console.log(`✅ Cached ${activeTemplates.length} templates`);
    } catch (error) {
      console.error('❌ Failed to warm template cache:', error);
    }
  }

  // Invalidate cache when templates are modified
  async invalidateTemplateCache(templateKey?: string, templateId?: number): Promise<void> {
    console.log('🧹 Invalidating template cache...');
    
    const keysToDelete: string[] = [
      CachedTemplateService.CACHE_KEYS.ALL_TEMPLATES,
      CacheService.generateKey(CachedTemplateService.CACHE_KEYS.ALL_TEMPLATES, 'active'),
      CacheService.generateKey(CachedTemplateService.CACHE_KEYS.ALL_TEMPLATES, 'draft')
    ];

    if (templateKey) {
      keysToDelete.push(
        CacheService.generateKey(CachedTemplateService.CACHE_KEYS.TEMPLATE_BY_KEY, templateKey)
      );
    }

    if (templateId) {
      keysToDelete.push(
        CacheService.generateKey(CachedTemplateService.CACHE_KEYS.TEMPLATE_BY_ID, templateId)
      );
    }

    // Delete all related cache entries
    await Promise.all(keysToDelete.map(key => cacheService.delete(key)));
    
    console.log(`🗑️ Invalidated ${keysToDelete.length} cache entries`);
  }

  // Template variable replacement with caching
  async replaceTemplateVariables(template: SmsTemplate, variables: Record<string, string>): Promise<string> {
    // Create cache key for this specific variable combination
    const variableHash = this.hashVariables(variables);
    const cacheKey = CacheService.generateKey('template:rendered', template.id, variableHash);
    
    return CacheService.getOrSet(
      cacheKey,
      async () => {
        let content = template.content;
        
        // Replace variables in the template
        const templateVariables = template.variables as string[];
        for (const varName of templateVariables) {
          const placeholder = `{#var#}`;
          const value = variables[varName] || '';
          
          // Replace only the first occurrence for each variable in order
          const index = content.indexOf(placeholder);
          if (index !== -1) {
            content = content.substring(0, index) + value + content.substring(index + placeholder.length);
          }
        }
        
        return content;
      },
      300 // 5 minutes cache for rendered templates
    );
  }

  // SMS logging with optimized batching
  async logSmsMessage(logData: Omit<InsertSmsLog, 'id' | 'createdAt'>): Promise<SmsLog> {
    return withDatabaseErrorBoundary(
      async () => {
        const logs = await db.insert(smsLogs).values({
          ...logData,
          createdAt: new Date()
        }).returning();

        // Update template usage statistics
        if (logData.templateId) {
          this.updateTemplateStats(logData.templateId, logData.status);
        }

        return logs[0];
      },
      'log-sms-message'
    );
  }

  async updateSmsLogStatus(
    logId: number, 
    status: string, 
    twilioMessageSid?: string, 
    errorMessage?: string
  ): Promise<void> {
    return withDatabaseErrorBoundary(
      async () => {
        await db
          .update(smsLogs)
          .set({
            status,
            ...(twilioMessageSid && { twilioMessageSid }),
            ...(errorMessage && { errorMessage })
          })
          .where(eq(smsLogs.id, logId));
      },
      'update-sms-log-status'
    );
  }

  // Analytics and usage tracking
  async getTemplateUsageStats(templateId: number): Promise<{
    totalSent: number;
    successRate: number;
    recentActivity: SmsLog[];
  }> {
    const cacheKey = CacheService.generateKey(CachedTemplateService.CACHE_KEYS.TEMPLATE_STATS, templateId);
    
    return CacheService.getOrSet(
      cacheKey,
      async () => {
        return withDatabaseErrorBoundary(
          async () => {
            const logs = await db
              .select()
              .from(smsLogs)
              .where(eq(smsLogs.templateId, templateId))
              .orderBy(desc(smsLogs.createdAt))
              .limit(100);

            const totalSent = logs.length;
            const successful = logs.filter(log => log.status === 'sent').length;
            const successRate = totalSent > 0 ? (successful / totalSent) * 100 : 0;

            return {
              totalSent,
              successRate,
              recentActivity: logs.slice(0, 10) // Last 10 logs
            };
          },
          'get-template-usage-stats'
        );
      },
      CachedTemplateService.CACHE_TTL.STATS
    );
  }

  // Get cache performance metrics
  getCacheMetrics(): {
    cacheStats: any;
    templateAccess: Array<{ key: string; count: number; lastAccess: Date }>;
    hitRate: number;
  } {
    const cacheStats = cacheService.getStats();
    
    const templateAccess = Array.from(CachedTemplateService.accessStats.entries())
      .map(([key, stats]) => ({
        key,
        count: stats.count,
        lastAccess: new Date(stats.lastAccess)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 most accessed

    // Calculate approximate hit rate based on access patterns
    const totalAccess = Array.from(CachedTemplateService.accessStats.values())
      .reduce((sum, stats) => sum + stats.count, 0);
    const estimatedHitRate = totalAccess > 0 ? Math.min(90, totalAccess * 0.8) : 0;

    return {
      cacheStats,
      templateAccess,
      hitRate: estimatedHitRate
    };
  }

  // Helper methods
  private trackTemplateAccess(key: string): void {
    const current = CachedTemplateService.accessStats.get(key) || { count: 0, lastAccess: 0 };
    CachedTemplateService.accessStats.set(key, {
      count: current.count + 1,
      lastAccess: Date.now()
    });
  }

  private updateTemplateStats(templateId: number, status: string): void {
    // This would be implemented with a background job in production
    // For now, we'll just invalidate the stats cache
    const cacheKey = CacheService.generateKey(CachedTemplateService.CACHE_KEYS.TEMPLATE_STATS, templateId);
    cacheService.delete(cacheKey).catch(() => {
      // Ignore cache deletion errors
    });
  }

  private hashVariables(variables: Record<string, string>): string {
    const sortedKeys = Object.keys(variables).sort();
    const sortedValues = sortedKeys.map(key => `${key}:${variables[key]}`);
    return Buffer.from(sortedValues.join('|')).toString('base64').slice(0, 16);
  }
}

// Singleton instance
export const cachedTemplateService = new CachedTemplateService();