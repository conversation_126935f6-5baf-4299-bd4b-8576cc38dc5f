import { logger } from './LoggerService';
import { cacheService } from './CacheService';
import { propertyCacheService } from './PropertyCacheService';

/**
 * Performance monitoring service for tracking API performance, cache efficiency, and database query metrics
 */

interface QueryMetric {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  cacheHit?: boolean | undefined;
  errorMessage?: string | undefined;
}

interface PerformanceMetrics {
  // API Performance
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestCount: number;
  errorRate: number;
  
  // Cache Performance
  cacheHitRate: number;
  cacheSize: number;
  cacheMemoryUsage: string;
  
  // Database Performance
  averageQueryTime: number;
  slowQueries: QueryMetric[];
  queryCount: number;
  
  // Business Metrics
  popularSearchTerms: { term: string; count: number }[];
  topLocations: { location: string; searches: number }[];
  priceRangePopularity: { range: string; count: number }[];
  
  // System Health
  uptime: number;
  memoryUsage: NodeJS.MemoryUsage;
  timestamp: number;
}

export class PerformanceMonitorService {
  private static instance: PerformanceMonitorService;
  private queryMetrics: QueryMetric[] = [];
  private searchAnalytics: Map<string, number> = new Map();
  private locationAnalytics: Map<string, number> = new Map();
  private priceRangeAnalytics: Map<string, number> = new Map();
  private startTime: number = Date.now();
  
  // Performance thresholds
  private readonly thresholds = {
    slowQueryTime: 500, // ms
    highErrorRate: 0.05, // 5%
    lowCacheHitRate: 0.6, // 60%
    maxMetricsRetention: 1000, // Keep last 1000 query metrics
    alertCooldown: 300000 // 5 minutes between alerts
  };

  private lastAlert: number = 0;

  constructor() {
    // Clean up old metrics every 10 minutes
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 10 * 60 * 1000);
  }

  static getInstance(): PerformanceMonitorService {
    if (!PerformanceMonitorService.instance) {
      PerformanceMonitorService.instance = new PerformanceMonitorService();
    }
    return PerformanceMonitorService.instance;
  }

  /**
   * Record a database query performance metric
   */
  recordQuery(operation: string, duration: number, success: boolean, cacheHit?: boolean, error?: string): void {
    const metric: QueryMetric = {
      operation,
      duration,
      timestamp: Date.now(),
      success,
      cacheHit: cacheHit,
      errorMessage: error
    };

    this.queryMetrics.push(metric);

    // Alert on slow queries
    if (duration > this.thresholds.slowQueryTime) {
      logger.warn('Slow query detected', 'performance', { 
        operation, 
        duration, 
        threshold: this.thresholds.slowQueryTime 
      });
    }

    // Keep only recent metrics
    if (this.queryMetrics.length > this.thresholds.maxMetricsRetention) {
      this.queryMetrics = this.queryMetrics.slice(-this.thresholds.maxMetricsRetention);
    }
  }

  /**
   * Record search analytics for business intelligence
   */
  recordSearch(params: {
    location?: string;
    priceRange?: string;
    searchTerm?: string;
  }): void {
    if (params.location) {
      this.locationAnalytics.set(
        params.location,
        (this.locationAnalytics.get(params.location) || 0) + 1
      );
    }

    if (params.priceRange) {
      this.priceRangeAnalytics.set(
        params.priceRange,
        (this.priceRangeAnalytics.get(params.priceRange) || 0) + 1
      );
    }

    if (params.searchTerm) {
      this.searchAnalytics.set(
        params.searchTerm,
        (this.searchAnalytics.get(params.searchTerm) || 0) + 1
      );
    }
  }

  /**
   * Get comprehensive performance metrics
   */
  async getMetrics(): Promise<PerformanceMetrics> {
    const now = Date.now();
    const recentMetrics = this.queryMetrics.filter(m => now - m.timestamp < 3600000); // Last hour

    // Calculate response time metrics
    const durations = recentMetrics.map(m => m.duration);
    const averageResponseTime = durations.length > 0 ? 
      durations.reduce((a, b) => a + b, 0) / durations.length : 0;
    
    const sortedDurations = durations.sort((a, b) => a - b);
    const p95ResponseTime = sortedDurations.length > 0 ? 
      sortedDurations[Math.floor(sortedDurations.length * 0.95)] : 0;
    const p99ResponseTime = sortedDurations.length > 0 ? 
      sortedDurations[Math.floor(sortedDurations.length * 0.99)] : 0;

    // Calculate error rate
    const errorCount = recentMetrics.filter(m => !m.success).length;
    const errorRate = recentMetrics.length > 0 ? errorCount / recentMetrics.length : 0;

    // Calculate cache hit rate
    const cacheRequests = recentMetrics.filter(m => m.cacheHit !== undefined);
    const cacheHits = cacheRequests.filter(m => m.cacheHit === true).length;
    const cacheHitRate = cacheRequests.length > 0 ? cacheHits / cacheRequests.length : 0;

    // Get cache stats
    const cacheStats = cacheService.getStats();
    const advancedCacheMetrics = propertyCacheService.getCacheMetrics();

    // Get slow queries
    const slowQueries = recentMetrics
      .filter(m => m.duration > this.thresholds.slowQueryTime)
      .slice(-10); // Last 10 slow queries

    // Get popular search terms
    const popularSearchTerms = Array.from(this.searchAnalytics.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([term, count]) => ({ term, count }));

    // Get top locations
    const topLocations = Array.from(this.locationAnalytics.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([location, searches]) => ({ location, searches }));

    // Get price range popularity
    const priceRangePopularity = Array.from(this.priceRangeAnalytics.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([range, count]) => ({ range, count }));

    return {
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      p95ResponseTime: Math.round(p95ResponseTime * 100) / 100,
      p99ResponseTime: Math.round(p99ResponseTime * 100) / 100,
      requestCount: recentMetrics.length,
      errorRate: Math.round(errorRate * 10000) / 100, // Percentage with 2 decimals
      
      cacheHitRate: Math.round(cacheHitRate * 10000) / 100,
      cacheSize: cacheStats.size,
      cacheMemoryUsage: cacheStats.memoryUsage,
      
      averageQueryTime: averageResponseTime,
      slowQueries,
      queryCount: recentMetrics.length,
      
      popularSearchTerms,
      topLocations,
      priceRangePopularity,
      
      uptime: now - this.startTime,
      memoryUsage: process.memoryUsage(),
      timestamp: now
    };
  }

  /**
   * Check system health and trigger alerts if needed
   */
  async performHealthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: string[];
    metrics: PerformanceMetrics;
  }> {
    const metrics = await this.getMetrics();
    const issues: string[] = [];
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    // Check error rate
    if (metrics.errorRate > this.thresholds.highErrorRate * 100) {
      issues.push(`High error rate: ${metrics.errorRate}%`);
      status = 'degraded';
    }

    // Check cache hit rate
    if (metrics.cacheHitRate < this.thresholds.lowCacheHitRate * 100) {
      issues.push(`Low cache hit rate: ${metrics.cacheHitRate}%`);
      status = 'degraded';
    }

    // Check for slow queries
    if (metrics.slowQueries.length > 5) {
      issues.push(`Multiple slow queries detected: ${metrics.slowQueries.length}`);
      status = 'degraded';
    }

    // Check memory usage
    const memoryUsageMB = metrics.memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > 500) {
      issues.push(`High memory usage: ${Math.round(memoryUsageMB)}MB`);
      if (memoryUsageMB > 1000) {
        status = 'unhealthy';
      } else {
        status = 'degraded';
      }
    }

    // Check response times
    if (metrics.p95ResponseTime > 1000) {
      issues.push(`High P95 response time: ${metrics.p95ResponseTime}ms`);
      status = 'degraded';
    }

    if (metrics.p99ResponseTime > 2000) {
      issues.push(`High P99 response time: ${metrics.p99ResponseTime}ms`);
      status = 'unhealthy';
    }

    // Send alerts if needed (with cooldown)
    const now = Date.now();
    if (status !== 'healthy' && now - this.lastAlert > this.thresholds.alertCooldown) {
      this.sendAlert(status, issues, metrics);
      this.lastAlert = now;
    }

    return { status, issues, metrics };
  }

  /**
   * Send performance alert
   */
  private sendAlert(status: string, issues: string[], metrics: PerformanceMetrics): void {
    logger.warn('Performance alert triggered', 'performance', {
      status,
      issues,
      requestCount: metrics.requestCount,
      errorRate: metrics.errorRate,
      cacheHitRate: metrics.cacheHitRate,
      p95ResponseTime: metrics.p95ResponseTime,
      memoryUsageMB: Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024)
    });
  }

  /**
   * Get performance summary for dashboard
   */
  async getPerformanceSummary(): Promise<{
    status: string;
    responseTime: number;
    cacheHitRate: number;
    errorRate: number;
    uptime: string;
    topLocations: Array<{ location: string; searches: number }>;
  }> {
    const metrics = await this.getMetrics();
    const health = await this.performHealthCheck();
    
    const uptimeDays = Math.floor(metrics.uptime / (1000 * 60 * 60 * 24));
    const uptimeHours = Math.floor((metrics.uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const uptimeString = `${uptimeDays}d ${uptimeHours}h`;

    return {
      status: health.status,
      responseTime: metrics.averageResponseTime,
      cacheHitRate: metrics.cacheHitRate,
      errorRate: metrics.errorRate,
      uptime: uptimeString,
      topLocations: metrics.topLocations.slice(0, 5)
    };
  }

  /**
   * Reset analytics data
   */
  resetAnalytics(): void {
    this.searchAnalytics.clear();
    this.locationAnalytics.clear();
    this.priceRangeAnalytics.clear();
    this.queryMetrics = [];
    
    logger.info('Performance analytics reset', 'performance');
  }

  /**
   * Clean up old metrics to prevent memory leaks
   */
  private cleanupOldMetrics(): void {
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const oldCount = this.queryMetrics.length;
    
    this.queryMetrics = this.queryMetrics.filter(m => m.timestamp > oneDayAgo);
    
    if (oldCount > this.queryMetrics.length) {
      logger.debug('Cleaned up old performance metrics', 'performance', {
        removed: oldCount - this.queryMetrics.length,
        remaining: this.queryMetrics.length
      });
    }
  }

  /**
   * Export metrics for external monitoring systems
   */
  async exportMetrics(): Promise<string> {
    const metrics = await this.getMetrics();
    
    // Export in Prometheus format
    const lines = [
      `# HELP property_api_response_time_ms Average API response time in milliseconds`,
      `# TYPE property_api_response_time_ms gauge`,
      `property_api_response_time_ms ${metrics.averageResponseTime}`,
      ``,
      `# HELP property_api_requests_total Total number of API requests`,
      `# TYPE property_api_requests_total counter`,
      `property_api_requests_total ${metrics.requestCount}`,
      ``,
      `# HELP property_api_error_rate Percentage of failed requests`,
      `# TYPE property_api_error_rate gauge`,
      `property_api_error_rate ${metrics.errorRate}`,
      ``,
      `# HELP property_cache_hit_rate Percentage of cache hits`,
      `# TYPE property_cache_hit_rate gauge`,
      `property_cache_hit_rate ${metrics.cacheHitRate}`,
      ``,
      `# HELP property_uptime_seconds Application uptime in seconds`,
      `# TYPE property_uptime_seconds counter`,
      `property_uptime_seconds ${Math.floor(metrics.uptime / 1000)}`,
      ``
    ];

    return lines.join('\n');
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitorService.getInstance();