import { logger } from "./LoggerService";

interface AuditLogData {
  userId: number;
  userRole: string;
  action: string;
  resource: string;
  resourceId?: number | string;
  details?: Record<string, any>;
  success: boolean;
  ip?: string;
  userAgent?: string;
  timestamp?: Date;
}

/**
 * Comprehensive audit logging service for owner dashboard actions
 */
export class AuditService {
  /**
   * Log owner dashboard actions for security and compliance
   */
  static logOwnerAction(data: AuditLogData): void {
    try {
      const auditEntry = {
        timestamp: data.timestamp || new Date().toISOString(),
        userId: data.userId,
        userRole: data.userRole,
        action: data.action,
        resource: data.resource,
        resourceId: data.resourceId,
        success: data.success,
        details: data.details || {},
        metadata: {
          ip: data.ip,
          userAgent: data.userAgent,
          environment: process.env.NODE_ENV || 'development'
        }
      };

      // Log with appropriate level based on action type
      if (this.isCriticalAction(data.action)) {
        logger.warn(`CRITICAL OWNER ACTION: ${data.action}`, 'audit', auditEntry);
      } else if (this.isSecurityRelevantAction(data.action)) {
        logger.info(`OWNER SECURITY ACTION: ${data.action}`, 'audit', auditEntry);
      } else {
        logger.info(`OWNER ACTION: ${data.action}`, 'audit', auditEntry);
      }

      // Additional logging for failed actions
      if (!data.success) {
        logger.error(`FAILED OWNER ACTION: ${data.action}`, undefined, 'security', {
          ...auditEntry,
          alertLevel: 'high'
        });
      }
    } catch (error) {
      logger.error('Failed to log audit entry', error instanceof Error ? error : undefined, 'system', {
        error: error instanceof Error ? error.message : 'Unknown error',
        originalData: data
      });
    }
  }

  /**
   * Log property-related actions
   */
  static logPropertyAction(
    userId: number,
    action: 'create' | 'read' | 'update' | 'delete',
    propertyId: number | string,
    details?: Record<string, any>,
    req?: any
  ): void {
    this.logOwnerAction({
      userId,
      userRole: 'owner',
      action: `property_${action}`,
      resource: 'property',
      resourceId: propertyId,
      details: details || {},
      success: true,
      ip: req?.ip,
      userAgent: req?.headers['user-agent']
    });
  }

  /**
   * Log booking management actions
   */
  static logBookingAction(
    userId: number,
    action: 'approve' | 'reject' | 'complete' | 'view',
    bookingId: number | string,
    details?: Record<string, any>,
    req?: any
  ): void {
    this.logOwnerAction({
      userId,
      userRole: 'owner',
      action: `booking_${action}`,
      resource: 'booking',
      resourceId: bookingId,
      details: details || {},
      success: true,
      ip: req?.ip,
      userAgent: req?.headers['user-agent']
    });
  }

  /**
   * Log pricing update actions
   */
  static logPricingAction(
    userId: number,
    propertyId: number | string,
    oldPricing: any,
    newPricing: any,
    req?: any
  ): void {
    this.logOwnerAction({
      userId,
      userRole: 'owner',
      action: 'pricing_update',
      resource: 'property',
      resourceId: propertyId,
      details: {
        oldPricing,
        newPricing,
        changes: this.calculatePricingChanges(oldPricing, newPricing)
      },
      success: true,
      ip: req?.ip,
      userAgent: req?.headers['user-agent']
    });
  }

  /**
   * Log media management actions
   */
  static logMediaAction(
    userId: number,
    action: 'upload' | 'delete' | 'reorder',
    propertyId: number | string,
    details?: Record<string, any>,
    req?: any
  ): void {
    this.logOwnerAction({
      userId,
      userRole: 'owner',
      action: `media_${action}`,
      resource: 'property',
      resourceId: propertyId,
      details: details || {},
      success: true,
      ip: req?.ip,
      userAgent: req?.headers['user-agent']
    });
  }

  /**
   * Log authentication and authorization events
   */
  static logAuthAction(
    userId: number | null,
    action: 'login' | 'logout' | 'token_refresh' | 'access_denied' | 'role_escalation_attempt',
    details?: Record<string, any>,
    req?: any,
    success: boolean = true
  ): void {
    this.logOwnerAction({
      userId: userId || 0,
      userRole: details?.userRole || 'unknown',
      action: `auth_${action}`,
      resource: 'authentication',
      details: details || {},
      success,
      ip: req?.ip,
      userAgent: req?.headers['user-agent']
    });
  }

  /**
   * Log failed access attempts for security monitoring
   */
  static logSecurityEvent(
    event: 'unauthorized_access' | 'invalid_token' | 'role_mismatch' | 'ownership_violation',
    userId: number | null,
    details: Record<string, any>,
    req?: any
  ): void {
    this.logOwnerAction({
      userId: userId || 0,
      userRole: details.userRole || 'unknown',
      action: `security_${event}`,
      resource: 'security',
      details: {
        ...details,
        severity: 'high',
        requiresInvestigation: true
      },
      success: false,
      ip: req?.ip,
      userAgent: req?.headers['user-agent']
    });

    // Additional alert for critical security events
    logger.error(`SECURITY ALERT: ${event}`, undefined, 'security', {
      userId,
      event,
      details: details || {},
      ip: req?.ip,
      timestamp: new Date().toISOString(),
      alertLevel: 'critical'
    });
  }

  /**
   * Check if an action is considered critical (requires special attention)
   */
  private static isCriticalAction(action: string): boolean {
    const criticalActions = [
      'property_delete',
      'booking_reject',
      'media_delete',
      'pricing_update',
      'auth_role_escalation_attempt',
      'security_ownership_violation'
    ];
    return criticalActions.includes(action);
  }

  /**
   * Check if an action is security-relevant
   */
  private static isSecurityRelevantAction(action: string): boolean {
    return action.includes('security_') || 
           action.includes('auth_') ||
           action.includes('delete') ||
           action.includes('reject');
  }

  /**
   * Calculate changes between old and new pricing
   */
  private static calculatePricingChanges(oldPricing: any, newPricing: any): Record<string, any> {
    const changes: Record<string, any> = {};
    
    if (!oldPricing || !newPricing) {
      return { error: 'Missing pricing data for comparison' };
    }

    // Compare key pricing fields
    const priceFields = ['halfDayPrice', 'fullDayPrice', 'weekdayHalfDayPrice', 'weekdayFullDayPrice', 'weekendHalfDayPrice', 'weekendFullDayPrice'];
    
    for (const field of priceFields) {
      if (oldPricing[field] !== newPricing[field]) {
        changes[field] = {
          from: oldPricing[field],
          to: newPricing[field],
          change: newPricing[field] - oldPricing[field],
          percentChange: oldPricing[field] ? 
            ((newPricing[field] - oldPricing[field]) / oldPricing[field] * 100).toFixed(2) + '%' : 
            'N/A'
        };
      }
    }

    return changes;
  }

  /**
   * Generate audit report summary for a user
   */
  static generateAuditSummary(userId: number, days: number = 30): void {
    logger.info(`Generating audit summary for user ${userId} (last ${days} days)`, 'audit', {
      userId,
      reportType: 'summary',
      periodDays: days,
      generatedAt: new Date().toISOString()
    });
  }
}

export default AuditService;