/**
 * Enhanced Logger Service Implementation
 * Implements ILogger interface with additional features
 */

import { ILogger, ILogEntry, LogLevel } from '../core/interfaces/ILogger';
import { config } from '../config';

interface LoggingConfig {
  level: LogLevel;
  format: 'json' | 'console';
  destinations: ('console' | 'file' | 'external')[];
}

export class EnhancedLoggerService implements ILogger {
  private static readonly isDevelopment = config.isDevelopment();
  private config: LoggingConfig;

  constructor(loggingConfig?: LoggingConfig) {
    this.config = loggingConfig || {
      level: EnhancedLoggerService.isDevelopment ? 'debug' : 'info',
      format: EnhancedLoggerService.isDevelopment ? 'console' : 'json',
      destinations: ['console']
    };
  }

  info(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('info', message, context, metadata);
  }

  warn(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('warn', message, context, metadata);
  }

  error(message: string, error?: Error, context?: string, metadata?: Record<string, any>): void {
    const errorData = error ? {
      name: error.name,
      message: error.message,
      stack: EnhancedLoggerService.isDevelopment ? error.stack : undefined
    } : undefined;

    this.log('error', message, context, { ...metadata, error: errorData });
  }

  debug(message: string, context?: string, metadata?: Record<string, any>): void {
    if (this.shouldLog('debug')) {
      this.log('debug', message, context, metadata);
    }
  }

  httpRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    requestId?: string,
    userId?: string | number,
    userAgent?: string,
    ip?: string,
    metadata?: Record<string, any>
  ): void {
    const logMetadata = {
      method,
      url,
      statusCode,
      responseTime,
      requestId: requestId || undefined,
      userId: userId || undefined,
      userAgent: userAgent || undefined,
      ip: ip || undefined,
      ...metadata
    };

    const level: LogLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    const message = `${method} ${url} ${statusCode} - ${responseTime}ms`;

    this.log(level, message, 'http', logMetadata);
  }

  performance(
    operation: string,
    startTime: number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    const duration = Date.now() - startTime;
    const level: LogLevel = duration > 1000 ? 'warn' : duration > 500 ? 'info' : 'debug';
    
    this.log(level, `Performance: ${operation} completed in ${duration}ms`, 'performance', {
      operation,
      duration,
      requestId,
      ...metadata
    });
  }

  audit(
    action: string,
    resource: string,
    resourceId: string | number,
    userId?: string | number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.log('info', `Audit: ${action} ${resource}:${resourceId}`, 'audit', {
      action,
      resource,
      resourceId,
      userId,
      requestId,
      ...metadata
    });
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };

    return levels[level] <= levels[this.config.level];
  }

  private log(level: LogLevel, message: string, context?: string, metadata?: Record<string, any>): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const logEntry: ILogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context || undefined,
      metadata: metadata || undefined,
      error: metadata?.error || undefined
    };

    this.writeLog(logEntry);
  }

  private writeLog(entry: ILogEntry): void {
    for (const destination of this.config.destinations) {
      switch (destination) {
        case 'console':
          this.writeToConsole(entry);
          break;
        case 'file':
          this.writeToFile(entry);
          break;
        case 'external':
          this.writeToExternal(entry);
          break;
      }
    }
  }

  private writeToConsole(entry: ILogEntry): void {
    if (this.config.format === 'json') {
      console.log(JSON.stringify(entry));
      return;
    }

    // Console format for development
    const { timestamp, level, message, context, metadata, error } = entry;
    
    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[90m'  // Gray
    };
    const reset = '\x1b[0m';
    
    const color = colors[level] || '';
    const levelStr = level.toUpperCase().padEnd(5);
    
    let logMessage = `${color}[${timestamp}] ${levelStr}${reset}`;
    
    if (context) {
      logMessage += ` [${context}]`;
    }
    
    logMessage += ` ${message}`;
    
    console.log(logMessage);
    
    // Log error details separately
    if (error && error.stack) {
      console.log(`${color}Stack trace:${reset}\n${error.stack}`);
    }
    
    // Log metadata if present
    if (metadata && Object.keys(metadata).length > 0) {
      const filteredMetadata = { ...metadata };
      delete filteredMetadata.error; // Already logged above
      
      if (Object.keys(filteredMetadata).length > 0) {
        console.log(`${color}Metadata:${reset}`, JSON.stringify(filteredMetadata, null, 2));
      }
    }
  }

  private writeToFile(entry: ILogEntry): void {
    // File logging implementation
    // This would write to log files in production
    // For now, just delegate to console in development
    if (EnhancedLoggerService.isDevelopment) {
      this.writeToConsole(entry);
    }
  }

  private writeToExternal(entry: ILogEntry): void {
    // External logging service implementation
    // This would send logs to external services like ELK, Datadog, etc.
    // For now, just delegate to console
    this.writeToConsole(entry);
  }

  /**
   * Create a logger with specific context
   */
  createContextLogger(context: string): ILogger {
    return {
      info: (message: string, ctx?: string, metadata?: Record<string, any>) => 
        this.info(message, ctx || context, metadata),
      
      warn: (message: string, ctx?: string, metadata?: Record<string, any>) => 
        this.warn(message, ctx || context, metadata),
      
      error: (message: string, error?: Error, ctx?: string, metadata?: Record<string, any>) => 
        this.error(message, error, ctx || context, metadata),
      
      debug: (message: string, ctx?: string, metadata?: Record<string, any>) => 
        this.debug(message, ctx || context, metadata),
      
      httpRequest: this.httpRequest.bind(this),
      performance: this.performance.bind(this),
      audit: this.audit.bind(this)
    };
  }

  /**
   * Set logging configuration
   */
  configure(config: Partial<LoggingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  getConfiguration(): LoggingConfig {
    return { ...this.config };
  }
}