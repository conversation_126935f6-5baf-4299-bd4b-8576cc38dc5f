import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarBookings, 
  properties, 
  users,
  CalendarBooking
} from '@shared/schema';
import { eq, and, gte, lte, inArray, desc, sql } from 'drizzle-orm';

export interface MultiPropertyCalendarOptions {
  ownerId?: number;
  propertyIds?: number[];
  startDate: string;
  endDate: string;
  status?: string[];
  source?: string[];
  groupBy?: 'property' | 'date' | 'status' | 'owner';
  includeBlocked?: boolean;
  includeAvailability?: boolean;
}

export interface PropertyCalendarData {
  property: {
    id: number;
    title: string;
    location: string;
    ownerId: number;
    ownerName: string;
  };
  bookings: CalendarBooking[];
  availability: AvailabilitySlot[];
  statistics: {
    totalBookings: number;
    occupancyRate: number;
    revenue: number;
    averageBookingValue: number;
  };
}

export interface AvailabilitySlot {
  date: string;
  available: boolean;
  reason?: string;
  bookedBy?: string;
}

export interface MultiPropertyView {
  dateRange: {
    start: string;
    end: string;
  };
  properties: PropertyCalendarData[];
  summary: {
    totalProperties: number;
    totalBookings: number;
    overallOccupancyRate: number;
    totalRevenue: number;
    conflictingDates: string[];
  };
  timeline: CalendarTimeline[];
}

export interface CalendarTimeline {
  date: string;
  dayOfWeek: string;
  properties: {
    propertyId: number;
    status: 'available' | 'booked' | 'blocked' | 'checkout' | 'checkin';
    booking?: CalendarBooking;
    notes?: string;
  }[];
}

export interface OwnerPortfolioView {
  owner: {
    id: number;
    name: string;
    email: string;
    whatsappNumber?: string;
  };
  properties: PropertyCalendarData[];
  aggregatedStats: {
    totalRevenue: number;
    totalBookings: number;
    averageOccupancyRate: number;
    topPerformingProperty: string;
    upcomingCheckIns: number;
    upcomingCheckOuts: number;
  };
}

export class MultiPropertyCalendarService {
  
  /**
   * Get multi-property calendar view
   */
  async getMultiPropertyView(options: MultiPropertyCalendarOptions): Promise<MultiPropertyView> {
    try {
      logger.info('Generating multi-property calendar view', options);

      // Get properties to include
      const propertiesToInclude = await this.getPropertiesForView(options);
      
      // Get bookings for date range and properties
      const bookings = await this.getBookingsForView(
        propertiesToInclude.map(p => p.id), 
        options.startDate, 
        options.endDate,
        options.status,
        options.source
      );

      // Build property calendar data
      const propertyCalendarData: PropertyCalendarData[] = [];
      
      for (const property of propertiesToInclude) {
        const propertyBookings = bookings.filter(b => b.propertyId === property.id);
        
        const calendarData: PropertyCalendarData = {
          property: {
            id: property.id,
            title: property.title,
            location: property.location,
            ownerId: property.ownerId,
            ownerName: property.ownerName
          },
          bookings: propertyBookings,
          availability: options.includeAvailability 
            ? await this.generateAvailabilitySlots(property.id, options.startDate, options.endDate, propertyBookings)
            : [],
          statistics: this.calculatePropertyStatistics(propertyBookings, options.startDate, options.endDate)
        };

        propertyCalendarData.push(calendarData);
      }

      // Generate timeline view
      const timeline = await this.generateTimeline(
        propertyCalendarData, 
        options.startDate, 
        options.endDate
      );

      // Calculate summary statistics
      const summary = this.calculateSummaryStatistics(propertyCalendarData, options.startDate, options.endDate);

      const result: MultiPropertyView = {
        dateRange: {
          start: options.startDate,
          end: options.endDate
        },
        properties: propertyCalendarData,
        summary,
        timeline
      };

      logger.info('Multi-property view generated', {
        propertiesCount: propertyCalendarData.length,
        totalBookings: summary.totalBookings,
        dateRange: `${options.startDate} to ${options.endDate}`
      });

      return result;
    } catch (error) {
      logger.error('Error generating multi-property view', error as Error);
      throw error;
    }
  }

  /**
   * Get owner portfolio view
   */
  async getOwnerPortfolioView(
    ownerId: number, 
    startDate: string, 
    endDate: string
  ): Promise<OwnerPortfolioView> {
    try {
      // Get owner information
      const ownerInfo = await db
        .select({
          id: users.id,
          name: users.fullName,
          email: users.email,
          whatsappNumber: users.whatsappNumber
        })
        .from(users)
        .where(eq(users.id, ownerId))
        .limit(1);

      if (ownerInfo.length === 0) {
        throw new Error(`Owner ${ownerId} not found`);
      }

      const owner = ownerInfo[0];

      // Get multi-property view for this owner
      const multiView = await this.getMultiPropertyView({
        ownerId,
        startDate,
        endDate,
        includeAvailability: true
      });

      // Calculate aggregated statistics
      const aggregatedStats = this.calculateOwnerStats(multiView);

      return {
        owner,
        properties: multiView.properties,
        aggregatedStats
      };
    } catch (error) {
      logger.error('Error generating owner portfolio view', error as Error);
      throw error;
    }
  }

  /**
   * Get calendar grid view (month/week view)
   */
  async getCalendarGridView(
    propertyIds: number[],
    year: number,
    month: number,
    viewType: 'month' | 'week' = 'month'
  ): Promise<any> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    if (viewType === 'week') {
      // Adjust for week view
      const today = new Date();
      startDate.setDate(today.getDate() - today.getDay()); // Start of week
      endDate.setDate(startDate.getDate() + 6); // End of week
    }

    const options: MultiPropertyCalendarOptions = {
      propertyIds,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      includeAvailability: true
    };

    const multiView = await this.getMultiPropertyView(options);

    // Transform to grid format
    return this.transformToGridView(multiView, viewType);
  }

  /**
   * Private helper methods
   */
  private async getPropertiesForView(options: MultiPropertyCalendarOptions) {
    let query = db
      .select({
        id: properties.id,
        title: properties.title,
        location: properties.location,
        ownerId: properties.ownerId,
        ownerName: users.fullName
      })
      .from(properties)
      .leftJoin(users, eq(users.id, properties.ownerId))
      .where(eq(properties.status, 'active'));

    if (options.ownerId) {
      query = query.where(eq(properties.ownerId, options.ownerId));
    }

    if (options.propertyIds && options.propertyIds.length > 0) {
      query = query.where(inArray(properties.id, options.propertyIds));
    }

    return await query;
  }

  private async getBookingsForView(
    propertyIds: number[],
    startDate: string,
    endDate: string,
    status?: string[],
    source?: string[]
  ): Promise<CalendarBooking[]> {
    let query = db
      .select()
      .from(calendarBookings)
      .where(and(
        inArray(calendarBookings.propertyId, propertyIds),
        gte(calendarBookings.endDate, startDate),
        lte(calendarBookings.startDate, endDate)
      ));

    if (status && status.length > 0) {
      query = query.where(inArray(calendarBookings.status, status));
    }

    if (source && source.length > 0) {
      query = query.where(inArray(calendarBookings.source, source));
    }

    return await query.orderBy(calendarBookings.startDate);
  }

  private async generateAvailabilitySlots(
    propertyId: number,
    startDate: string,
    endDate: string,
    bookings: CalendarBooking[]
  ): Promise<AvailabilitySlot[]> {
    const slots: AvailabilitySlot[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      
      // Check if date is booked
      const booking = bookings.find(b => 
        dateStr >= b.startDate && dateStr <= b.endDate
      );

      slots.push({
        date: dateStr,
        available: !booking,
        reason: booking ? `Booked by ${booking.guestName}` : undefined,
        bookedBy: booking?.guestName
      });
    }

    return slots;
  }

  private calculatePropertyStatistics(
    bookings: CalendarBooking[],
    startDate: string,
    endDate: string
  ) {
    const totalBookings = bookings.length;
    const revenue = bookings.reduce((sum, b) => sum + (b.totalAmount || 0), 0);
    const averageBookingValue = totalBookings > 0 ? revenue / totalBookings : 0;

    // Calculate occupancy rate
    const totalDays = Math.ceil(
      (new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24)
    ) + 1;

    const bookedDays = bookings.reduce((sum, booking) => {
      const bookingDays = Math.ceil(
        (new Date(booking.endDate).getTime() - new Date(booking.startDate).getTime()) / (1000 * 60 * 60 * 24)
      ) + 1;
      return sum + bookingDays;
    }, 0);

    const occupancyRate = totalDays > 0 ? (bookedDays / totalDays) * 100 : 0;

    return {
      totalBookings,
      occupancyRate: Math.min(occupancyRate, 100), // Cap at 100%
      revenue,
      averageBookingValue
    };
  }

  private async generateTimeline(
    propertyData: PropertyCalendarData[],
    startDate: string,
    endDate: string
  ): Promise<CalendarTimeline[]> {
    const timeline: CalendarTimeline[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      const dayOfWeek = dayNames[d.getDay()];

      const timelineEntry: CalendarTimeline = {
        date: dateStr,
        dayOfWeek,
        properties: []
      };

      for (const property of propertyData) {
        const booking = property.bookings.find(b => 
          dateStr >= b.startDate && dateStr <= b.endDate
        );

        let status: CalendarTimeline['properties'][0]['status'] = 'available';
        if (booking) {
          if (dateStr === booking.startDate) {
            status = 'checkin';
          } else if (dateStr === booking.endDate) {
            status = 'checkout';
          } else {
            status = 'booked';
          }
        }

        timelineEntry.properties.push({
          propertyId: property.property.id,
          status,
          booking: booking || undefined
        });
      }

      timeline.push(timelineEntry);
    }

    return timeline;
  }

  private calculateSummaryStatistics(
    propertyData: PropertyCalendarData[],
    startDate: string,
    endDate: string
  ) {
    const totalProperties = propertyData.length;
    const totalBookings = propertyData.reduce((sum, p) => sum + p.statistics.totalBookings, 0);
    const totalRevenue = propertyData.reduce((sum, p) => sum + p.statistics.revenue, 0);
    
    const overallOccupancyRate = totalProperties > 0
      ? propertyData.reduce((sum, p) => sum + p.statistics.occupancyRate, 0) / totalProperties
      : 0;

    // Find conflicting dates (dates with multiple bookings across properties)
    const conflictingDates: string[] = [];
    // This would require more complex logic to detect actual conflicts

    return {
      totalProperties,
      totalBookings,
      overallOccupancyRate: Math.round(overallOccupancyRate * 100) / 100,
      totalRevenue,
      conflictingDates
    };
  }

  private calculateOwnerStats(multiView: MultiPropertyView) {
    const properties = multiView.properties;
    
    const totalRevenue = multiView.summary.totalRevenue;
    const totalBookings = multiView.summary.totalBookings;
    const averageOccupancyRate = multiView.summary.overallOccupancyRate;
    
    // Find top performing property
    const topProperty = properties.reduce((best, current) => 
      current.statistics.revenue > (best?.statistics.revenue || 0) ? current : best
    );

    const topPerformingProperty = topProperty?.property.title || 'N/A';

    // Count upcoming check-ins and check-outs
    const today = new Date().toISOString().split('T')[0];
    const upcomingCheckIns = properties.reduce((count, property) => {
      return count + property.bookings.filter(b => 
        b.startDate >= today && b.status === 'confirmed'
      ).length;
    }, 0);

    const upcomingCheckOuts = properties.reduce((count, property) => {
      return count + property.bookings.filter(b => 
        b.endDate >= today && b.status === 'confirmed'
      ).length;
    }, 0);

    return {
      totalRevenue,
      totalBookings,
      averageOccupancyRate,
      topPerformingProperty,
      upcomingCheckIns,
      upcomingCheckOuts
    };
  }

  private transformToGridView(multiView: MultiPropertyView, viewType: 'month' | 'week') {
    // Transform timeline data into grid format suitable for calendar UI
    const grid: any = {
      viewType,
      dateRange: multiView.dateRange,
      properties: multiView.properties.map(p => ({
        id: p.property.id,
        title: p.property.title
      })),
      dates: multiView.timeline.map(day => ({
        date: day.date,
        dayOfWeek: day.dayOfWeek,
        cells: day.properties.map(prop => ({
          propertyId: prop.propertyId,
          status: prop.status,
          guestName: prop.booking?.guestName,
          bookingType: prop.booking?.bookingType,
          revenue: prop.booking?.totalAmount
        }))
      }))
    };

    return grid;
  }

  /**
   * Export calendar data
   */
  async exportCalendarData(
    options: MultiPropertyCalendarOptions,
    format: 'csv' | 'ical' | 'json'
  ): Promise<string> {
    const multiView = await this.getMultiPropertyView(options);

    switch (format) {
      case 'csv':
        return this.exportToCSV(multiView);
      case 'ical':
        return this.exportToICAL(multiView);
      case 'json':
        return JSON.stringify(multiView, null, 2);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  private exportToCSV(multiView: MultiPropertyView): string {
    const headers = ['Date', 'Property', 'Guest Name', 'Status', 'Booking Type', 'Revenue', 'Source'];
    const rows = [headers.join(',')];

    for (const day of multiView.timeline) {
      for (const property of day.properties) {
        const booking = property.booking;
        const propertyData = multiView.properties.find(p => p.property.id === property.propertyId);
        
        const row = [
          day.date,
          propertyData?.property.title || '',
          booking?.guestName || '',
          property.status,
          booking?.bookingType || '',
          booking?.totalAmount?.toString() || '0',
          booking?.source || ''
        ];
        
        rows.push(row.join(','));
      }
    }

    return rows.join('\n');
  }

  private exportToICAL(multiView: MultiPropertyView): string {
    let ical = 'BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:-//BookAFarm//Calendar//EN\n';

    for (const property of multiView.properties) {
      for (const booking of property.bookings) {
        ical += 'BEGIN:VEVENT\n';
        ical += `UID:booking_${booking.id}@bookafarm.com\n`;
        ical += `DTSTART:${booking.startDate.replace(/-/g, '')}\n`;
        ical += `DTEND:${booking.endDate.replace(/-/g, '')}\n`;
        ical += `SUMMARY:${booking.guestName} - ${property.property.title}\n`;
        ical += `DESCRIPTION:Booking Type: ${booking.bookingType}\\nGuests: ${booking.guestCount}\\nRevenue: ${booking.totalAmount}\n`;
        ical += `LOCATION:${property.property.location}\n`;
        ical += 'END:VEVENT\n';
      }
    }

    ical += 'END:VCALENDAR';
    return ical;
  }
}

// Export singleton instance
export const multiPropertyCalendarService = new MultiPropertyCalendarService();