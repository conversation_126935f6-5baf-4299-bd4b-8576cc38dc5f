import twilio from 'twilio';
import { config } from '../config';
import { logger } from './LoggerService';

interface WhatsAppMessage {
  from: string;
  to: string;
  body: string;
  messageId: string;
  timestamp: Date;
}

interface WhatsAppQuickReply {
  type: 'text' | 'button';
  title: string;
  payload?: string;
}

interface WhatsAppTemplate {
  name: string;
  language: string;
  components?: any[];
}

export class WhatsAppService {
  private client: twilio.Twilio | null = null;
  private isConfigured = false;

  constructor() {
    this.initializeClient();
  }

  private initializeClient(): void {
    const whatsappConfig = config.whatsapp;
    const twilioConfig = config.twilio;

    if (!whatsappConfig.available || !twilioConfig.available) {
      logger.warn('WhatsApp service not configured - running in development mode');
      return;
    }

    try {
      this.client = twilio(twilioConfig.accountSid!, twilioConfig.authToken!);
      this.isConfigured = true;
      logger.info('WhatsApp service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize WhatsApp service', error as Error, 'whatsapp-service');
    }
  }

  /**
   * Send a simple text message via WhatsApp
   */
  async sendMessage(to: string, message: string): Promise<boolean> {
    if (!this.isConfigured || !this.client) {
      logger.warn(`WhatsApp not configured - would send to ${to}: ${message}`);
      return false;
    }

    try {
      const result = await this.client.messages.create({
        from: config.whatsapp.twilioNumber!,
        to: this.formatPhoneNumber(to),
        body: message
      });

      logger.info(`WhatsApp message sent successfully: ${result.sid}`);
      return true;
    } catch (error) {
      logger.error('Failed to send WhatsApp message', error as Error, 'whatsapp-service');
      
      // In development, log the message for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('\n' + '='.repeat(60));
        console.log('📱 WHATSAPP MESSAGE (DEBUG - Failed to send via Twilio)');
        console.log('='.repeat(60));
        console.log(`TO: ${to}`);
        console.log('-'.repeat(60));
        console.log(message);
        console.log('='.repeat(60) + '\n');
      }
      
      return false;
    }
  }

  /**
   * Send a message with quick reply buttons
   */
  async sendMessageWithQuickReplies(
    to: string, 
    message: string, 
    quickReplies: WhatsAppQuickReply[]
  ): Promise<boolean> {
    if (!this.isConfigured || !this.client) {
      logger.warn(`WhatsApp not configured - would send interactive message to ${to}`);
      return false;
    }

    try {
      // For now, send as regular message with options listed
      // WhatsApp Business API templates would be needed for true quick replies
      const formattedMessage = this.formatMessageWithOptions(message, quickReplies);
      return await this.sendMessage(to, formattedMessage);
    } catch (error) {
      logger.error('Failed to send WhatsApp message with quick replies', error as Error, 'whatsapp-service');
      return false;
    }
  }

  /**
   * Send a pre-approved WhatsApp template message
   */
  async sendTemplateMessage(
    to: string, 
    template: WhatsAppTemplate, 
    parameters?: string[]
  ): Promise<boolean> {
    if (!this.isConfigured || !this.client) {
      logger.warn(`WhatsApp not configured - would send template ${template.name} to ${to}`);
      return false;
    }

    try {
      // Template messages require pre-approval from WhatsApp
      // This is a placeholder for when templates are set up
      logger.info(`Template message functionality not yet implemented: ${template.name}`);
      return false;
    } catch (error) {
      logger.error('Failed to send WhatsApp template message', error as Error, 'whatsapp-service');
      return false;
    }
  }

  /**
   * Parse incoming WhatsApp webhook payload
   */
  parseIncomingMessage(payload: any): WhatsAppMessage | null {
    try {
      const body = payload.Body || '';
      const from = payload.From || '';
      const messageId = payload.MessageSid || '';
      const timestamp = new Date();

      if (!from || !body) {
        logger.warn('Invalid WhatsApp message payload received');
        return null;
      }

      return {
        from: this.cleanPhoneNumber(from),
        to: payload.To || '',
        body: body.trim(),
        messageId,
        timestamp
      };
    } catch (error) {
      logger.error('Failed to parse WhatsApp message', error as Error, 'whatsapp-service');
      return null;
    }
  }

  /**
   * Validate webhook signature from Twilio
   */
  validateWebhookSignature(payload: string, signature: string, url: string): boolean {
    if (!this.isConfigured) {
      logger.warn('WhatsApp webhook validation skipped - not configured', 'whatsapp-service');
      return true; // Allow in development
    }

    try {
      const authToken = config.twilio.authToken;
      if (!authToken) {
        logger.error('Twilio auth token not available for signature validation', undefined, 'whatsapp-service');
        return false;
      }

      return twilio.validateRequest(authToken, signature, url, payload);
    } catch (error) {
      logger.error('Failed to validate WhatsApp webhook signature', error as Error, 'whatsapp-service');
      return false;
    }
  }

  /**
   * Format phone number for WhatsApp (ensure it starts with whatsapp:)
   */
  private formatPhoneNumber(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Add country code if missing (assuming India +91 as default)
    let formatted = cleaned;
    if (!cleaned.startsWith('91') && cleaned.length === 10) {
      formatted = '91' + cleaned;
    }
    
    return `whatsapp:+${formatted}`;
  }

  /**
   * Clean phone number from WhatsApp format
   */
  private cleanPhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace('whatsapp:+', '').replace(/\D/g, '');
  }

  /**
   * Format message with quick reply options
   */
  private formatMessageWithOptions(message: string, options: WhatsAppQuickReply[]): string {
    if (options.length === 0) return message;

    let formatted = message + '\n\nPlease reply with:\n';
    options.forEach((option, index) => {
      formatted += `${index + 1}. ${option.title}\n`;
    });

    return formatted;
  }

  /**
   * Check if WhatsApp service is available and configured
   */
  isAvailable(): boolean {
    return this.isConfigured && this.client !== null;
  }

  /**
   * Get service status for health checks
   */
  getStatus() {
    return {
      configured: this.isConfigured,
      available: this.isAvailable(),
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const whatsAppService = new WhatsAppService();