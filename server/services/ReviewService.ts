import { storage } from '../storage';
import { Review, InsertReview, ReviewForm, User, Property } from '../../shared/schema';
import { userService } from './UserService';
import { propertyService } from './PropertyService';

export interface ReviewWithUser extends Review {
  user: User;
}

export interface ReviewWithProperty extends Review {
  property: Property;
}

export class ReviewService {
  async getPropertyReviews(propertyId: number): Promise<ReviewWithUser[]> {
    return await storage.getReviews(propertyId);
  }

  async getUserReviews(userId: number): Promise<ReviewWithProperty[]> {
    return await storage.getUserReviews(userId);
  }

  async getReviewById(id: number): Promise<Review | null> {
    const review = await storage.getReview(id);
    return review || null;
  }

  async createReview(reviewData: any, userId: number): Promise<Review> {
    // Validate required fields
    if (!reviewData.propertyId || !reviewData.rating) {
      throw new Error('Property and rating are required');
    }

    // Verify property exists
    const property = await propertyService.getPropertyById(reviewData.propertyId);
    if (!property) {
      throw new Error('Property not found');
    }

    // Check if user has already reviewed this property
    const existingReviews = await this.getUserReviews(userId);
    const hasReviewed = existingReviews.some(review => review.propertyId === reviewData.propertyId);
    
    if (hasReviewed) {
      throw new Error('You have already reviewed this property');
    }

    // Verify user has actually booked this property (optional business rule)
    // This could be implemented if we want to restrict reviews to actual guests

    const newReview: InsertReview = {
      propertyId: reviewData.propertyId,
      userId,
      rating: reviewData.rating,
      comment: reviewData.comment || null
    };

    return await storage.createReview(newReview);
  }

  async updateReview(
    reviewId: number, 
    reviewData: Partial<ReviewForm>, 
    userId: number
  ): Promise<Review | null> {
    const existingReview = await this.getReviewById(reviewId);
    if (!existingReview) {
      throw new Error('Review not found');
    }

    // Verify ownership
    if (existingReview.userId !== userId) {
      throw new Error('Not authorized to update this review');
    }

    // Validate rating if provided
    if (reviewData.rating) {
      const validRatings = ['1', '2', '3', '4', '5'];
      if (!validRatings.includes(reviewData.rating)) {
        throw new Error('Rating must be between 1 and 5');
      }
    }

    const updatedReview = await storage.updateReview(reviewId, reviewData);
    return updatedReview || null;
  }

  async deleteReview(reviewId: number, userId: number, userRole: string): Promise<boolean> {
    const existingReview = await this.getReviewById(reviewId);
    if (!existingReview) {
      throw new Error('Review not found');
    }

    // Users can delete their own reviews, owners can delete reviews on their properties
    if (userRole === 'owner') {
      const property = await propertyService.getPropertyById(existingReview.propertyId);
      if (!property || property.ownerId !== userId) {
        throw new Error('Not authorized to delete this review');
      }
    } else if (existingReview.userId !== userId) {
      throw new Error('Not authorized to delete this review');
    }

    return await storage.deleteReview(reviewId);
  }

  async addOwnerResponse(
    reviewId: number, 
    response: string, 
    ownerId: number
  ): Promise<Review | null> {
    const existingReview = await this.getReviewById(reviewId);
    if (!existingReview) {
      throw new Error('Review not found');
    }

    // Verify owner owns the property
    const property = await propertyService.getPropertyById(existingReview.propertyId);
    if (!property || property.ownerId !== ownerId) {
      throw new Error('Not authorized to respond to this review');
    }

    if (!response || response.trim().length === 0) {
      throw new Error('Response cannot be empty');
    }

    const updatedReview = await storage.addOwnerResponse(reviewId, response.trim());
    return updatedReview || null;
  }

  async getPropertyAverageRating(propertyId: number): Promise<number> {
    return await storage.getPropertyAverageRating(propertyId);
  }

  async getPropertyRatingBreakdown(propertyId: number): Promise<{
    averageRating: number;
    totalReviews: number;
    ratingCounts: { [key: string]: number };
  }> {
    const reviews = await this.getPropertyReviews(propertyId);
    const totalReviews = reviews.length;
    
    if (totalReviews === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingCounts: { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 }
      };
    }

    const ratingCounts = { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 };
    let totalRating = 0;

    reviews.forEach(review => {
      ratingCounts[review.rating]++;
      totalRating += parseInt(review.rating);
    });

    const averageRating = Math.round((totalRating / totalReviews) * 10) / 10;

    return {
      averageRating,
      totalReviews,
      ratingCounts
    };
  }

  async getRecentReviews(limit: number = 10): Promise<ReviewWithUser[]> {
    // This would need to be implemented in storage if we want to get recent reviews across all properties
    // For now, we'll return an empty array as this would require a new storage method
    return [];
  }

  async canUserReview(userId: number, propertyId: number): Promise<boolean> {
    // Check if user has already reviewed this property
    const userReviews = await this.getUserReviews(userId);
    const hasReviewed = userReviews.some(review => review.propertyId === propertyId);
    
    if (hasReviewed) {
      return false;
    }

    // Additional business rules could be added here:
    // - User must have completed a booking at this property
    // - User cannot review their own property
    // etc.

    return true;
  }

  async getOwnerReviewStats(ownerId: number): Promise<{
    totalReviews: number;
    averageRating: number;
    responseRate: number;
  }> {
    const properties = await propertyService.getPropertiesByOwner(ownerId);
    let totalReviews = 0;
    let totalRating = 0;
    let reviewsWithResponses = 0;

    for (const property of properties) {
      const reviews = await this.getPropertyReviews(property.id);
      totalReviews += reviews.length;
      
      reviews.forEach(review => {
        totalRating += parseInt(review.rating);
        if (review.response) {
          reviewsWithResponses++;
        }
      });
    }

    const averageRating = totalReviews > 0 ? Math.round((totalRating / totalReviews) * 10) / 10 : 0;
    const responseRate = totalReviews > 0 ? Math.round((reviewsWithResponses / totalReviews) * 100) : 0;

    return {
      totalReviews,
      averageRating,
      responseRate
    };
  }
}

export const reviewService = new ReviewService();