import { storage } from '../storage';
import { Booking, InsertBooking, Property } from '../../shared/schema';
import { emailService } from '../emailService';
import { propertyService } from './PropertyService';
import { userService } from './UserService';
import { dltSMSService } from './DLTSMSService';

export interface BookingWithProperty extends Booking {
  property: Property;
}

export interface BookingWithPropertyAndGuest extends Booking {
  property: Property;
  guest: {
    id: number;
    fullName: string;
    username: string;
    email: string;
    phone: string | null;
  };
}

export class BookingService {
  async getUserBookings(userId: number): Promise<BookingWithProperty[]> {
    return await storage.getBookingsWithProperty(userId);
  }

  async getOwnerBookings(ownerId: number): Promise<BookingWithPropertyAndGuest[]> {
    return await storage.getBookingsByOwner(ownerId);
  }

  async getOwnerBookingsWithReviews(ownerId: number): Promise<BookingWithPropertyAndGuest[]> {
    const bookings = await storage.getBookingsByOwner(ownerId);
    
    // Add review status for completed bookings
    for (const booking of bookings) {
      if (booking.status === 'completed') {
        const review = await storage.getReviewByBookingId(booking.id);
        (booking as any).hasReview = !!review;
        if (review) {
          (booking as any).reviewRating = review.rating;
          (booking as any).reviewComment = review.comment;
        }
      }
    }
    
    return bookings;
  }

  async getPropertyBookings(propertyId: number): Promise<Booking[]> {
    return await storage.getBookingsByProperty(propertyId);
  }

  async getBookingById(id: number): Promise<Booking | null> {
    const booking = await storage.getBooking(id);
    return booking || null;
  }

  async createBooking(bookingData: any, userId: number): Promise<Booking> {
    // Validate required fields
    if (!bookingData.propertyId || !bookingData.bookingDate || !bookingData.bookingType) {
      throw new Error('Property, date, and booking type are required');
    }

    if (bookingData.guests < 1) {
      throw new Error('At least 1 guest is required');
    }

    // Verify property exists and is available
    const property = await propertyService.getPropertyById(bookingData.propertyId);
    if (!property) {
      throw new Error('Property not found');
    }

    if (property.status !== 'active') {
      throw new Error('Property is not available for booking');
    }

    // Check availability for the specific date and time
    const bookingDate = new Date(bookingData.bookingDate);
    const isAvailable = await storage.checkAvailability(
      bookingData.propertyId,
      bookingDate,
      bookingData.bookingType
    );

    if (!isAvailable) {
      throw new Error('Property is not available for the selected date and time');
    }

    // Calculate total price using dynamic pricing logic
    const duration = bookingData.bookingType === 'morning' ? '12h' : '24h';
    const effectivePrice = propertyService.getEffectivePrice(property, bookingDate, duration);
    
    const totalPrice = effectivePrice * bookingData.guests;

    const newBooking: InsertBooking = {
      propertyId: bookingData.propertyId,
      userId,
      bookingDate: bookingData.bookingDate,
      bookingType: bookingData.bookingType,
      guests: bookingData.guests,
      totalPrice,
      status: 'pending',
      paymentStatus: 'pending',
      specialRequests: bookingData.specialRequests || null
    };

    const createdBooking = await storage.createBooking(newBooking);

    // Send booking confirmation emails
    try {
      await this.sendBookingNotifications(createdBooking, property);
    } catch (error) {
      console.error('Failed to send booking notifications:', error);
      // Don't fail the booking if email fails
    }

    return createdBooking;
  }

  async updateBookingStatus(
    bookingId: number, 
    status: string, 
    userId: number, 
    userRole: string
  ): Promise<Booking | null> {
    const booking = await this.getBookingById(bookingId);
    if (!booking) {
      throw new Error('Booking not found');
    }

    // Verify authorization
    if (userRole !== 'owner' && booking.userId !== userId) {
      throw new Error('Not authorized to update this booking');
    }

    // If user is owner, verify they own the property
    if (userRole === 'owner') {
      const property = await propertyService.getPropertyById(booking.propertyId);
      if (!property || property.ownerId !== userId) {
        throw new Error('Not authorized to update this booking');
      }
    }

    // Validate status transitions
    const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed'];
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid booking status');
    }

    // Business rules for status changes
    if (booking.status === 'completed' && status !== 'completed') {
      throw new Error('Cannot change status of completed booking');
    }

    if (booking.status === 'cancelled' && status !== 'cancelled') {
      throw new Error('Cannot change status of cancelled booking');
    }

    const updatedBooking = await storage.updateBookingStatus(bookingId, status);
    return updatedBooking || null;
  }

  async cancelBooking(bookingId: number, userId: number, userRole: string): Promise<Booking | null> {
    return await this.updateBookingStatus(bookingId, 'cancelled', userId, userRole);
  }

  async confirmBooking(bookingId: number, ownerId: number): Promise<Booking | null> {
    return await this.updateBookingStatus(bookingId, 'confirmed', ownerId, 'owner');
  }

  async checkAvailability(
    propertyId: number, 
    date: Date, 
    bookingType: 'morning' | 'full_day'
  ): Promise<boolean> {
    return await storage.checkAvailability(propertyId, date, bookingType);
  }

  async getUpcomingBookings(userId: number): Promise<BookingWithProperty[]> {
    const bookings = await this.getUserBookings(userId);
    const now = new Date();
    
    return bookings.filter(booking => {
      const bookingDate = new Date(booking.bookingDate);
      return bookingDate > now && booking.status !== 'cancelled';
    });
  }

  async getPastBookings(userId: number): Promise<BookingWithProperty[]> {
    const bookings = await this.getUserBookings(userId);
    const now = new Date();
    
    return bookings.filter(booking => {
      const bookingDate = new Date(booking.bookingDate);
      return bookingDate <= now || booking.status === 'completed';
    });
  }

  private async sendBookingNotifications(booking: Booking, property: Property): Promise<void> {
    try {
      // Get guest and owner details
      const guest = await userService.getUserById(booking.userId);
      const owner = await userService.getUserById(property.ownerId);

      if (!guest || !owner) {
        throw new Error('Unable to find user details for notification');
      }

      const emailData = {
        bookingId: booking.id,
        propertyTitle: property.title,
        guestName: guest.fullName,
        guestEmail: guest.email,
        ownerEmail: owner.email,
        bookingDate: booking.bookingDate,
        bookingType: booking.bookingType,
        guests: booking.guests,
        totalPrice: booking.totalPrice,
        propertyLocation: property.location
      };

      // Send email notification
      await emailService.sendBookingEmails(emailData);

      // Send DLT-compliant SMS notification to guest
      if (guest.phone) {
        try {
          const smsResult = await dltSMSService.sendBookingConfirmation(
            guest.phone,
            property.title,
            booking.bookingDate
          );
          
          if (smsResult.queued) {
            console.log(`Booking confirmation SMS queued for ${guest.phone}: ${smsResult.jobId}`);
          } else {
            console.error(`Failed to queue booking confirmation SMS for ${guest.phone}`);
          }
        } catch (smsError) {
          console.error('SMS notification failed:', smsError);
          // Don't fail the booking if SMS fails
        }
      }
    } catch (error) {
      console.error('Error sending booking notifications:', error);
      throw error;
    }
  }

  async getBookingStats(ownerId: number): Promise<{
    totalBookings: number;
    pendingBookings: number;
    confirmedBookings: number;
    totalRevenue: number;
  }> {
    const bookings = await this.getOwnerBookings(ownerId);
    
    const stats = {
      totalBookings: bookings.length,
      pendingBookings: bookings.filter(b => b.status === 'pending').length,
      confirmedBookings: bookings.filter(b => b.status === 'confirmed').length,
      totalRevenue: bookings
        .filter(b => b.status === 'confirmed' || b.status === 'completed')
        .reduce((sum, b) => sum + b.totalPrice, 0)
    };

    return stats;
  }
}

export const bookingService = new BookingService();