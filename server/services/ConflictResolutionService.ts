import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarBookings, 
  properties,
  users,
  CalendarBooking
} from '@shared/schema';
import { eq, and, gte, lte, ne } from 'drizzle-orm';
import { EventEmitter } from 'events';

export interface BookingConflict {
  id: string;
  type: 'overlap' | 'duplicate' | 'modification' | 'cancellation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  bookings: CalendarBooking[];
  suggestedResolution: ConflictResolution;
  autoResolvable: boolean;
  description: string;
  impact: string;
  detectedAt: Date;
}

export interface ConflictResolution {
  action: 'keep_first' | 'keep_last' | 'merge' | 'split' | 'manual' | 'reject_new';
  parameters?: {
    splitDate?: string;
    mergeStrategy?: 'extend_dates' | 'combine_guests' | 'prefer_confirmed';
    priority?: 'source' | 'amount' | 'date_created' | 'guest_type';
  };
  reason: string;
  confidence: number; // 0-1 score
}

export interface ConflictResolutionResult {
  success: boolean;
  resolvedConflicts: string[];
  newBookings?: CalendarBooking[];
  updatedBookings?: CalendarBooking[];
  deletedBookings?: number[];
  errors: string[];
}

export class ConflictResolutionService extends EventEmitter {
  private conflictQueue: BookingConflict[] = [];
  private resolutionRules: ResolutionRule[] = [];

  constructor() {
    super();
    this.initializeDefaultRules();
    logger.info('Conflict Resolution Service initialized');
  }

  /**
   * Detect conflicts for a booking
   */
  async detectConflicts(
    propertyId: number,
    startDate: string,
    endDate: string,
    excludeBookingId?: number
  ): Promise<BookingConflict[]> {
    const conflicts: BookingConflict[] = [];

    try {
      // Find overlapping bookings
      const overlappingBookings = await db
        .select()
        .from(calendarBookings)
        .where(and(
          eq(calendarBookings.propertyId, propertyId),
          // Date overlap: (start1 <= end2) AND (end1 >= start2)
          lte(calendarBookings.startDate, endDate),
          gte(calendarBookings.endDate, startDate),
          excludeBookingId ? ne(calendarBookings.id, excludeBookingId) : undefined
        ));

      if (overlappingBookings.length === 0) {
        return conflicts;
      }

      // Categorize conflicts
      for (const booking of overlappingBookings) {
        const conflictType = this.determineConflictType(
          { startDate, endDate, propertyId } as any,
          booking
        );

        const conflict: BookingConflict = {
          id: `conflict_${Date.now()}_${booking.id}`,
          type: conflictType,
          severity: this.calculateSeverity(conflictType, booking),
          bookings: [booking],
          suggestedResolution: this.suggestResolution(conflictType, [booking]),
          autoResolvable: this.isAutoResolvable(conflictType, booking),
          description: this.generateConflictDescription(conflictType, booking),
          impact: this.calculateImpact(conflictType, booking),
          detectedAt: new Date()
        };

        conflicts.push(conflict);
      }

      // Check for complex multi-booking conflicts
      if (overlappingBookings.length > 1) {
        const multiConflict = this.analyzeMultiBookingConflict(
          overlappingBookings,
          { startDate, endDate, propertyId } as any
        );
        if (multiConflict) {
          conflicts.push(multiConflict);
        }
      }

      logger.info('Conflicts detected', {
        propertyId,
        dateRange: `${startDate} to ${endDate}`,
        conflictCount: conflicts.length
      });

      return conflicts;
    } catch (error) {
      logger.error('Error detecting conflicts', error as Error);
      throw error;
    }
  }

  /**
   * Auto-resolve conflicts based on rules
   */
  async autoResolveConflicts(conflicts: BookingConflict[]): Promise<ConflictResolutionResult> {
    const result: ConflictResolutionResult = {
      success: true,
      resolvedConflicts: [],
      newBookings: [],
      updatedBookings: [],
      deletedBookings: [],
      errors: []
    };

    for (const conflict of conflicts) {
      if (!conflict.autoResolvable) {
        continue;
      }

      try {
        const resolution = await this.executeResolution(conflict, conflict.suggestedResolution);
        
        if (resolution.success) {
          result.resolvedConflicts.push(conflict.id);
          
          if (resolution.newBookings) {
            result.newBookings!.push(...resolution.newBookings);
          }
          if (resolution.updatedBookings) {
            result.updatedBookings!.push(...resolution.updatedBookings);
          }
          if (resolution.deletedBookings) {
            result.deletedBookings!.push(...resolution.deletedBookings);
          }

          this.emit('conflictResolved', { conflict, resolution });
        } else {
          result.errors.push(...resolution.errors);
        }
      } catch (error) {
        result.errors.push(`Failed to resolve conflict ${conflict.id}: ${(error as Error).message}`);
      }
    }

    result.success = result.errors.length === 0;
    return result;
  }

  /**
   * Manually resolve conflict
   */
  async resolveConflict(
    conflictId: string,
    resolution: ConflictResolution
  ): Promise<ConflictResolutionResult> {
    const conflict = this.conflictQueue.find(c => c.id === conflictId);
    if (!conflict) {
      throw new Error(`Conflict ${conflictId} not found`);
    }

    const result = await this.executeResolution(conflict, resolution);
    
    if (result.success) {
      // Remove from queue
      this.conflictQueue = this.conflictQueue.filter(c => c.id !== conflictId);
      this.emit('conflictManuallyResolved', { conflict, resolution });
    }

    return result;
  }

  /**
   * Execute conflict resolution
   */
  private async executeResolution(
    conflict: BookingConflict,
    resolution: ConflictResolution
  ): Promise<ConflictResolutionResult> {
    const result: ConflictResolutionResult = {
      success: false,
      resolvedConflicts: [],
      errors: []
    };

    try {
      switch (resolution.action) {
        case 'keep_first':
          result = await this.resolveKeepFirst(conflict);
          break;
        case 'keep_last':
          result = await this.resolveKeepLast(conflict);
          break;
        case 'merge':
          result = await this.resolveMerge(conflict, resolution.parameters);
          break;
        case 'split':
          result = await this.resolveSplit(conflict, resolution.parameters);
          break;
        case 'reject_new':
          result = await this.resolveRejectNew(conflict);
          break;
        default:
          result.errors.push(`Unknown resolution action: ${resolution.action}`);
      }

      logger.info('Conflict resolution executed', {
        conflictId: conflict.id,
        action: resolution.action,
        success: result.success
      });

    } catch (error) {
      result.errors.push(`Resolution execution failed: ${(error as Error).message}`);
    }

    return result;
  }

  /**
   * Resolution strategies
   */
  private async resolveKeepFirst(conflict: BookingConflict): Promise<ConflictResolutionResult> {
    // Keep the earliest booking, delete others
    const sortedBookings = conflict.bookings.sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    const toDelete = sortedBookings.slice(1);
    const deletedIds: number[] = [];

    for (const booking of toDelete) {
      await db.delete(calendarBookings).where(eq(calendarBookings.id, booking.id));
      deletedIds.push(booking.id);
    }

    return {
      success: true,
      resolvedConflicts: [conflict.id],
      deletedBookings: deletedIds,
      errors: []
    };
  }

  private async resolveKeepLast(conflict: BookingConflict): Promise<ConflictResolutionResult> {
    // Keep the latest booking, delete others
    const sortedBookings = conflict.bookings.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const toDelete = sortedBookings.slice(1);
    const deletedIds: number[] = [];

    for (const booking of toDelete) {
      await db.delete(calendarBookings).where(eq(calendarBookings.id, booking.id));
      deletedIds.push(booking.id);
    }

    return {
      success: true,
      resolvedConflicts: [conflict.id],
      deletedBookings: deletedIds,
      errors: []
    };
  }

  private async resolveMerge(
    conflict: BookingConflict,
    parameters?: any
  ): Promise<ConflictResolutionResult> {
    const bookings = conflict.bookings;
    
    // Determine merge strategy
    const strategy = parameters?.mergeStrategy || 'extend_dates';
    
    if (strategy === 'extend_dates') {
      const earliestStart = bookings.reduce((earliest, booking) => 
        booking.startDate < earliest ? booking.startDate : earliest,
        bookings[0].startDate
      );
      
      const latestEnd = bookings.reduce((latest, booking) => 
        booking.endDate > latest ? booking.endDate : latest,
        bookings[0].endDate
      );

      // Keep the first booking, extend its dates
      const primaryBooking = bookings[0];
      await db
        .update(calendarBookings)
        .set({
          startDate: earliestStart,
          endDate: latestEnd,
          updatedAt: new Date()
        })
        .where(eq(calendarBookings.id, primaryBooking.id));

      // Delete other bookings
      const deletedIds: number[] = [];
      for (const booking of bookings.slice(1)) {
        await db.delete(calendarBookings).where(eq(calendarBookings.id, booking.id));
        deletedIds.push(booking.id);
      }

      return {
        success: true,
        resolvedConflicts: [conflict.id],
        updatedBookings: [{...primaryBooking, startDate: earliestStart, endDate: latestEnd}],
        deletedBookings: deletedIds,
        errors: []
      };
    }

    return {
      success: false,
      resolvedConflicts: [],
      errors: [`Unsupported merge strategy: ${strategy}`]
    };
  }

  private async resolveSplit(
    conflict: BookingConflict,
    parameters?: any
  ): Promise<ConflictResolutionResult> {
    // Split overlapping bookings at conflict point
    // This is a complex operation that would require specific business logic
    return {
      success: false,
      resolvedConflicts: [],
      errors: ['Split resolution not yet implemented']
    };
  }

  private async resolveRejectNew(conflict: BookingConflict): Promise<ConflictResolutionResult> {
    // This would be handled at the booking creation level
    return {
      success: true,
      resolvedConflicts: [conflict.id],
      errors: []
    };
  }

  /**
   * Utility methods
   */
  private determineConflictType(
    newBooking: Partial<CalendarBooking>,
    existingBooking: CalendarBooking
  ): BookingConflict['type'] {
    // Exact date match
    if (newBooking.startDate === existingBooking.startDate && 
        newBooking.endDate === existingBooking.endDate) {
      return 'duplicate';
    }

    // Overlapping dates
    return 'overlap';
  }

  private calculateSeverity(
    type: BookingConflict['type'],
    booking: CalendarBooking
  ): BookingConflict['severity'] {
    switch (type) {
      case 'duplicate':
        return booking.status === 'confirmed' ? 'high' : 'medium';
      case 'overlap':
        return 'high';
      case 'modification':
        return 'medium';
      default:
        return 'low';
    }
  }

  private suggestResolution(
    type: BookingConflict['type'],
    bookings: CalendarBooking[]
  ): ConflictResolution {
    switch (type) {
      case 'duplicate':
        return {
          action: 'keep_last',
          reason: 'Keep the most recent duplicate booking',
          confidence: 0.9
        };
      case 'overlap':
        return {
          action: 'manual',
          reason: 'Overlapping bookings require manual review',
          confidence: 0.3
        };
      default:
        return {
          action: 'manual',
          reason: 'Unknown conflict type requires manual resolution',
          confidence: 0.1
        };
    }
  }

  private isAutoResolvable(
    type: BookingConflict['type'],
    booking: CalendarBooking
  ): boolean {
    return type === 'duplicate' && booking.status !== 'confirmed';
  }

  private generateConflictDescription(
    type: BookingConflict['type'],
    booking: CalendarBooking
  ): string {
    switch (type) {
      case 'duplicate':
        return `Duplicate booking for ${booking.guestName} on ${booking.startDate}`;
      case 'overlap':
        return `Booking overlap detected with ${booking.guestName}'s reservation`;
      default:
        return `Booking conflict detected with ${booking.guestName}`;
    }
  }

  private calculateImpact(
    type: BookingConflict['type'],
    booking: CalendarBooking
  ): string {
    const baseImpact = `Affects booking ${booking.id} (${booking.guestName})`;
    
    switch (type) {
      case 'duplicate':
        return `${baseImpact} - May cause double-booking`;
      case 'overlap':
        return `${baseImpact} - Property unavailable for requested dates`;
      default:
        return baseImpact;
    }
  }

  private analyzeMultiBookingConflict(
    bookings: CalendarBooking[],
    newBooking: Partial<CalendarBooking>
  ): BookingConflict | null {
    if (bookings.length < 2) return null;

    return {
      id: `multi_conflict_${Date.now()}`,
      type: 'overlap',
      severity: 'critical',
      bookings,
      suggestedResolution: {
        action: 'manual',
        reason: 'Multiple booking conflicts require careful manual review',
        confidence: 0.1
      },
      autoResolvable: false,
      description: `Multiple booking conflicts detected (${bookings.length} bookings affected)`,
      impact: `Critical: ${bookings.length} existing bookings conflict with new request`,
      detectedAt: new Date()
    };
  }

  /**
   * Initialize default resolution rules
   */
  private initializeDefaultRules(): void {
    this.resolutionRules = [
      {
        name: 'Auto-delete duplicate test bookings',
        condition: (conflict) => 
          conflict.type === 'duplicate' && 
          conflict.bookings.some(b => b.guestName.includes('Test')),
        resolution: { action: 'keep_first', reason: 'Remove test bookings', confidence: 1.0 }
      },
      {
        name: 'Prefer confirmed bookings',
        condition: (conflict) => 
          conflict.type === 'overlap' &&
          conflict.bookings.some(b => b.status === 'confirmed'),
        resolution: { action: 'manual', reason: 'Confirmed bookings need review', confidence: 0.2 }
      }
    ];
  }
}

interface ResolutionRule {
  name: string;
  condition: (conflict: BookingConflict) => boolean;
  resolution: ConflictResolution;
}

// Export singleton instance
export const conflictResolutionService = new ConflictResolutionService();