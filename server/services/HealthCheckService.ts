import { logger } from './LoggerService';
import { metricsService, METRICS } from './MetricsService';
import { bookingCleanupService } from './BookingCleanupService';
import { errorRecoveryService } from './ErrorRecoveryService';
import { db } from '../db';
import { config } from '../config';
import { sql } from 'drizzle-orm';

// Health check status levels
export enum HealthStatus {
  HEALTHY = 'HEALTHY',
  DEGRADED = 'DEGRADED',    // Some non-critical issues
  UNHEALTHY = 'UNHEALTHY'   // Critical issues affecting functionality
}

// Individual component health
export interface ComponentHealth {
  name: string;
  status: HealthStatus;
  message: string;
  details?: Record<string, any>;
  responseTime?: number;
  lastChecked: Date;
}

// Overall system health
export interface SystemHealth {
  status: HealthStatus;
  version: string;
  uptime: number;
  timestamp: Date;
  components: ComponentHealth[];
  metrics?: {
    totalRequests: number;
    errorRate: number;
    averageResponseTime: number;
    activeConnections: number;
  };
}

// Health check configuration
interface HealthCheckConfig {
  timeout: number;
  critical: boolean;
}

export class HealthCheckService {
  private startTime: Date;
  private healthChecks: Map<string, {
    check: () => Promise<ComponentHealth>;
    config: HealthCheckConfig;
  }> = new Map();

  constructor() {
    this.startTime = new Date();
    this.initializeHealthChecks();
  }

  /**
   * Initialize all health checks
   */
  private initializeHealthChecks(): void {
    // Database health check
    this.healthChecks.set('database', {
      check: this.checkDatabase.bind(this),
      config: { timeout: 5000, critical: true }
    });

    // Booking cleanup service health check
    this.healthChecks.set('booking_cleanup', {
      check: this.checkBookingCleanup.bind(this),
      config: { timeout: 3000, critical: false }
    });

    // Error recovery service health check
    this.healthChecks.set('error_recovery', {
      check: this.checkErrorRecovery.bind(this),
      config: { timeout: 3000, critical: false }
    });

    // Memory health check
    this.healthChecks.set('memory', {
      check: this.checkMemory.bind(this),
      config: { timeout: 1000, critical: false }
    });

    // External services health check
    this.healthChecks.set('external_services', {
      check: this.checkExternalServices.bind(this),
      config: { timeout: 10000, critical: false }
    });

    logger.info('Health checks initialized', 'health', {
      totalChecks: this.healthChecks.size
    });
  }

  /**
   * Perform all health checks and return system health
   */
  async checkSystemHealth(): Promise<SystemHealth> {
    const startTime = Date.now();
    const components: ComponentHealth[] = [];

    logger.debug('Starting system health check');

    // Execute all health checks
    const healthCheckPromises = Array.from(this.healthChecks.entries()).map(
      async ([name, { check, config }]) => {
        try {
          const componentHealth = await this.executeWithTimeout(
            check,
            config.timeout,
            name
          );
          components.push(componentHealth);
          return componentHealth;
        } catch (error) {
          const errorHealth: ComponentHealth = {
            name,
            status: HealthStatus.UNHEALTHY,
            message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`,
            lastChecked: new Date(),
            responseTime: Date.now() - startTime
          };
          components.push(errorHealth);
          return errorHealth;
        }
      }
    );

    await Promise.all(healthCheckPromises);

    // Determine overall system health
    const overallStatus = this.determineOverallStatus(components);

    // Collect metrics
    const metrics = this.collectHealthMetrics();

    const systemHealth: SystemHealth = {
      status: overallStatus,
      version: process.env.npm_package_version || '1.0.0',
      uptime: Date.now() - this.startTime.getTime(),
      timestamp: new Date(),
      components,
      metrics
    };

    // Record health check metrics
    metricsService.incrementCounter('health_checks_total', 1, {
      status: overallStatus
    });

    metricsService.recordHistogram(
      'health_check_duration_seconds',
      (Date.now() - startTime) / 1000
    );

    logger.debug('System health check completed', 'health', {
      status: overallStatus,
      componentCount: components.length,
      duration: Date.now() - startTime
    });

    return systemHealth;
  }

  /**
   * Quick health check for liveness probe
   */
  async quickHealthCheck(): Promise<{ status: HealthStatus; uptime: number }> {
    try {
      // Just check if we can connect to database
      const dbHealth = await this.executeWithTimeout(
        this.checkDatabase.bind(this),
        2000,
        'database_quick'
      );

      return {
        status: dbHealth.status === HealthStatus.HEALTHY ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        uptime: Date.now() - this.startTime.getTime()
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        uptime: Date.now() - this.startTime.getTime()
      };
    }
  }

  /**
   * Check database connectivity and performance
   */
  private async checkDatabase(): Promise<ComponentHealth> {
    const startTime = Date.now();
    
    try {
      // Test basic connectivity
      await db.execute(sql`SELECT 1 as health_check`);
      
      // Test transaction
      await db.transaction(async (tx) => {
        await tx.execute(sql`SELECT NOW() as transaction_test`);
      });

      const responseTime = Date.now() - startTime;

      // Check if response time is reasonable
      const status = responseTime > 1000 ? HealthStatus.DEGRADED : HealthStatus.HEALTHY;
      const message = status === HealthStatus.DEGRADED 
        ? `Database responding slowly (${responseTime}ms)`
        : 'Database connection healthy';

      return {
        name: 'database',
        status,
        message,
        responseTime,
        lastChecked: new Date(),
        details: {
          connectionTest: 'passed',
          transactionTest: 'passed',
          responseTimeMs: responseTime
        }
      };
    } catch (error) {
      return {
        name: 'database',
        status: HealthStatus.UNHEALTHY,
        message: `Database connection failed: ${error instanceof Error ? error.message : String(error)}`,
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        details: {
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * Check booking cleanup service health
   */
  private async checkBookingCleanup(): Promise<ComponentHealth> {
    try {
      const status = bookingCleanupService.getStatus();
      
      const isHealthy = status.running && 
                       status.circuitBreaker.state !== 'OPEN';

      return {
        name: 'booking_cleanup',
        status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        message: isHealthy 
          ? 'Booking cleanup service operational'
          : `Booking cleanup service issues: ${status.circuitBreaker.state}`,
        lastChecked: new Date(),
        details: {
          running: status.running,
          circuitBreakerState: status.circuitBreaker.state,
          failureCount: status.circuitBreaker.failureCount,
          isCleanupRunning: status.isCleanupRunning
        }
      };
    } catch (error) {
      return {
        name: 'booking_cleanup',
        status: HealthStatus.UNHEALTHY,
        message: `Booking cleanup check failed: ${error instanceof Error ? error.message : String(error)}`,
        lastChecked: new Date()
      };
    }
  }

  /**
   * Check error recovery service health
   */
  private async checkErrorRecovery(): Promise<ComponentHealth> {
    try {
      const queueStatus = errorRecoveryService.getQueueStatus();
      
      // Consider degraded if too many operations in queue
      const isHealthy = queueStatus.queueSize < 10;
      const status = isHealthy ? HealthStatus.HEALTHY : HealthStatus.DEGRADED;

      return {
        name: 'error_recovery',
        status,
        message: isHealthy 
          ? 'Error recovery service operational'
          : `Error recovery queue has ${queueStatus.queueSize} pending operations`,
        lastChecked: new Date(),
        details: {
          queueSize: queueStatus.queueSize,
          isProcessing: queueStatus.isProcessing,
          pendingOperations: queueStatus.operations.length
        }
      };
    } catch (error) {
      return {
        name: 'error_recovery',
        status: HealthStatus.UNHEALTHY,
        message: `Error recovery check failed: ${error instanceof Error ? error.message : String(error)}`,
        lastChecked: new Date()
      };
    }
  }

  /**
   * Check memory usage
   */
  private async checkMemory(): Promise<ComponentHealth> {
    try {
      const memUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
      const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
      const heapUsagePercent = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);

      // Consider degraded if heap usage > 85%
      const status = heapUsagePercent > 85 ? HealthStatus.DEGRADED : HealthStatus.HEALTHY;
      const message = status === HealthStatus.DEGRADED
        ? `High memory usage: ${heapUsagePercent}%`
        : `Memory usage normal: ${heapUsagePercent}%`;

      return {
        name: 'memory',
        status,
        message,
        lastChecked: new Date(),
        details: {
          heapUsedMB,
          heapTotalMB,
          heapUsagePercent,
          rssMB: Math.round(memUsage.rss / 1024 / 1024),
          externalMB: Math.round(memUsage.external / 1024 / 1024)
        }
      };
    } catch (error) {
      return {
        name: 'memory',
        status: HealthStatus.UNHEALTHY,
        message: `Memory check failed: ${error instanceof Error ? error.message : String(error)}`,
        lastChecked: new Date()
      };
    }
  }

  /**
   * Check external services availability
   */
  private async checkExternalServices(): Promise<ComponentHealth> {
    const checks = [];
    const details: Record<string, any> = {};

    // For now, just check if required environment variables are set
    // In a real implementation, you might ping external APIs
    
    // Check payment gateway configuration
    const hasPaymentConfig = !!(process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET);
    details.paymentGateway = hasPaymentConfig ? 'configured' : 'not_configured';
    
    // Check email service configuration
    const hasEmailConfig = !!process.env.SENDGRID_API_KEY;
    details.emailService = hasEmailConfig ? 'configured' : 'not_configured';
    
    // Check SMS service configuration
    const hasSMSConfig = !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN);
    details.smsService = hasSMSConfig ? 'configured' : 'not_configured';

    const configuredServices = Object.values(details).filter(status => status === 'configured').length;
    const totalServices = Object.keys(details).length;

    const status = configuredServices === totalServices 
      ? HealthStatus.HEALTHY 
      : configuredServices > 0 
        ? HealthStatus.DEGRADED 
        : HealthStatus.UNHEALTHY;

    return {
      name: 'external_services',
      status,
      message: `${configuredServices}/${totalServices} external services configured`,
      lastChecked: new Date(),
      details
    };
  }

  /**
   * Execute health check with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeoutMs: number,
    name: string
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Health check timeout: ${name} took longer than ${timeoutMs}ms`));
      }, timeoutMs);

      fn()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Determine overall system status based on component health
   */
  private determineOverallStatus(components: ComponentHealth[]): HealthStatus {
    const criticalComponents = ['database']; // Components that are critical for system operation
    
    // Check if any critical components are unhealthy
    const criticalUnhealthy = components.some(
      component => criticalComponents.includes(component.name) && 
                  component.status === HealthStatus.UNHEALTHY
    );

    if (criticalUnhealthy) {
      return HealthStatus.UNHEALTHY;
    }

    // Check if any component is unhealthy or if many are degraded
    const unhealthyCount = components.filter(c => c.status === HealthStatus.UNHEALTHY).length;
    const degradedCount = components.filter(c => c.status === HealthStatus.DEGRADED).length;

    if (unhealthyCount > 0 || degradedCount > components.length / 2) {
      return HealthStatus.DEGRADED;
    }

    return HealthStatus.HEALTHY;
  }

  /**
   * Collect health-related metrics
   */
  private collectHealthMetrics(): {
    totalRequests: number;
    errorRate: number;
    averageResponseTime: number;
    activeConnections: number;
  } {
    const metrics = metricsService.getAllMetrics();
    
    // Extract relevant metrics (simplified for example)
    const totalRequests = metrics.counters.find(m => m.name === METRICS.HTTP_REQUESTS_TOTAL)?.value || 0;
    const activeConnections = metrics.gauges.find(m => m.name === METRICS.ACTIVE_CONNECTIONS)?.value || 0;

    return {
      totalRequests,
      errorRate: 0, // Would calculate from error metrics
      averageResponseTime: 0, // Would calculate from response time histogram
      activeConnections
    };
  }

  /**
   * Get detailed diagnostics for troubleshooting
   */
  async getDiagnostics(): Promise<Record<string, any>> {
    const systemHealth = await this.checkSystemHealth();
    const metrics = metricsService.getAllMetrics();

    return {
      health: systemHealth,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        cwd: process.cwd()
      },
      configuration: {
        isDevelopment: config.isDevelopment(),
        port: process.env.PORT || 3000,
        // Don't expose sensitive config values
        hasDbUrl: !!process.env.DATABASE_URL,
        hasJwtSecret: !!process.env.JWT_SECRET
      },
      metrics: {
        countersCount: metrics.counters.length,
        gaugesCount: metrics.gauges.length,
        histogramsCount: metrics.histograms.length
      }
    };
  }
}

// Export singleton instance
export const healthCheckService = new HealthCheckService();