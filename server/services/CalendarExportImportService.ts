import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarBookings, 
  properties, 
  users,
  CalendarBooking
} from '@shared/schema';
import { eq, and, gte, lte, inArray } from 'drizzle-orm';
import * as ICAL from 'ical.js';

export interface ExportOptions {
  propertyIds?: number[];
  ownerId?: number;
  startDate: string;
  endDate: string;
  format: 'ical' | 'csv' | 'json' | 'excel' | 'pdf';
  includeGuest?: boolean;
  includeRevenue?: boolean;
  includeNotes?: boolean;
  timezone?: string;
}

export interface ImportOptions {
  propertyId: number;
  format: 'ical' | 'csv' | 'json';
  source: string;
  conflictResolution: 'skip' | 'overwrite' | 'merge';
  validateOnly?: boolean;
}

export interface ExportResult {
  success: boolean;
  format: string;
  filename: string;
  data?: string | Buffer;
  downloadUrl?: string;
  recordCount: number;
  errors: string[];
}

export interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: string[];
  conflicts: ImportConflict[];
  preview?: any[];
}

export interface ImportConflict {
  rowIndex: number;
  data: any;
  reason: string;
  suggestion: string;
}

export class CalendarExportImportService {
  
  /**
   * Export calendar data in various formats
   */
  async exportCalendar(options: ExportOptions): Promise<ExportResult> {
    try {
      logger.info('Starting calendar export', {
        format: options.format,
        propertyIds: options.propertyIds?.length || 'all',
        ownerId: options.ownerId,
        dateRange: `${options.startDate} to ${options.endDate}`
      });

      // Get bookings to export
      const bookings = await this.getBookingsForExport(options);
      
      if (bookings.length === 0) {
        return {
          success: true,
          format: options.format,
          filename: '',
          recordCount: 0,
          errors: ['No bookings found for the specified criteria']
        };
      }

      // Generate export data based on format
      let exportData: string | Buffer;
      let filename: string;

      switch (options.format) {
        case 'ical':
          exportData = await this.exportToICAL(bookings, options);
          filename = `calendar-export-${Date.now()}.ics`;
          break;
        
        case 'csv':
          exportData = await this.exportToCSV(bookings, options);
          filename = `calendar-export-${Date.now()}.csv`;
          break;
        
        case 'json':
          exportData = await this.exportToJSON(bookings, options);
          filename = `calendar-export-${Date.now()}.json`;
          break;
        
        case 'excel':
          exportData = await this.exportToExcel(bookings, options);
          filename = `calendar-export-${Date.now()}.xlsx`;
          break;
        
        case 'pdf':
          exportData = await this.exportToPDF(bookings, options);
          filename = `calendar-export-${Date.now()}.pdf`;
          break;
        
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      logger.info('Calendar export completed', {
        format: options.format,
        recordCount: bookings.length,
        filename
      });

      return {
        success: true,
        format: options.format,
        filename,
        data: exportData,
        recordCount: bookings.length,
        errors: []
      };
    } catch (error) {
      logger.error('Calendar export failed', error as Error);
      return {
        success: false,
        format: options.format,
        filename: '',
        recordCount: 0,
        errors: [(error as Error).message]
      };
    }
  }

  /**
   * Import calendar data from various formats
   */
  async importCalendar(
    data: string | Buffer,
    options: ImportOptions
  ): Promise<ImportResult> {
    try {
      logger.info('Starting calendar import', {
        propertyId: options.propertyId,
        format: options.format,
        source: options.source,
        validateOnly: options.validateOnly
      });

      // Validate property exists
      const property = await db
        .select()
        .from(properties)
        .where(eq(properties.id, options.propertyId))
        .limit(1);

      if (property.length === 0) {
        throw new Error(`Property ${options.propertyId} not found`);
      }

      // Parse data based on format
      let parsedData: any[];
      switch (options.format) {
        case 'ical':
          parsedData = await this.parseICAL(data.toString());
          break;
        case 'csv':
          parsedData = await this.parseCSV(data.toString());
          break;
        case 'json':
          parsedData = JSON.parse(data.toString());
          break;
        default:
          throw new Error(`Unsupported import format: ${options.format}`);
      }

      const result: ImportResult = {
        success: true,
        imported: 0,
        skipped: 0,
        errors: [],
        conflicts: []
      };

      // Validate and process each record
      for (const [index, record] of parsedData.entries()) {
        try {
          const processResult = await this.processImportRecord(
            record,
            index,
            options
          );

          if (processResult.conflict) {
            result.conflicts.push(processResult.conflict);
            result.skipped++;
          } else if (processResult.booking) {
            if (!options.validateOnly) {
              await db.insert(calendarBookings).values(processResult.booking);
            }
            result.imported++;
          } else {
            result.skipped++;
          }
        } catch (error) {
          result.errors.push(`Row ${index + 1}: ${(error as Error).message}`);
          result.skipped++;
        }
      }

      // For validation mode, return preview
      if (options.validateOnly) {
        result.preview = parsedData.slice(0, 10);
      }

      result.success = result.errors.length < parsedData.length * 0.5;

      logger.info('Calendar import completed', {
        propertyId: options.propertyId,
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors.length
      });

      return result;
    } catch (error) {
      logger.error('Calendar import failed', error as Error);
      return {
        success: false,
        imported: 0,
        skipped: 0,
        errors: [(error as Error).message],
        conflicts: []
      };
    }
  }

  /**
   * Export to iCal format
   */
  private async exportToICAL(
    bookings: (CalendarBooking & { propertyTitle?: string; propertyLocation?: string })[],
    options: ExportOptions
  ): Promise<string> {
    let ical = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//BookAFarm//Calendar Export//EN
CALSCALE:GREGORIAN
METHOD:PUBLISH
X-WR-CALNAME:BookAFarm Calendar
X-WR-TIMEZONE:${options.timezone || 'UTC'}
X-WR-CALDESC:Exported from BookAFarm Calendar System
`;

    for (const booking of bookings) {
      const startDate = booking.startDate.replace(/-/g, '');
      const endDate = booking.endDate.replace(/-/g, '');
      
      // For all-day events, we need to add one day to the end date
      const endDatePlusOne = this.addDaysToDateString(endDate, 1);

      ical += `BEGIN:VEVENT
UID:booking-${booking.id}@bookafarm.com
DTSTART;VALUE=DATE:${startDate}
DTEND;VALUE=DATE:${endDatePlusOne}
DTSTAMP:${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}Z
SUMMARY:${this.escapeICalText(booking.guestName)} - ${booking.bookingType}
DESCRIPTION:${this.buildICalDescription(booking, options)}
LOCATION:${this.escapeICalText(booking.propertyLocation || '')}
STATUS:${booking.status?.toUpperCase() === 'CONFIRMED' ? 'CONFIRMED' : 'TENTATIVE'}
CATEGORIES:BOOKING,${booking.source?.toUpperCase() || 'DIRECT'}
END:VEVENT
`;
    }

    ical += 'END:VCALENDAR';
    return ical;
  }

  /**
   * Export to CSV format
   */
  private async exportToCSV(
    bookings: (CalendarBooking & { propertyTitle?: string; propertyLocation?: string })[],
    options: ExportOptions
  ): Promise<string> {
    const headers = [
      'Date',
      'Start Date',
      'End Date',
      'Property',
      'Location',
      'Guest Name',
      ...(options.includeGuest ? ['Guest Phone', 'Guest Count'] : []),
      'Booking Type',
      'Status',
      'Source',
      ...(options.includeRevenue ? ['Revenue'] : []),
      ...(options.includeNotes ? ['Notes'] : [])
    ];

    const rows = [headers];

    for (const booking of bookings) {
      const row = [
        booking.startDate,
        booking.startDate,
        booking.endDate,
        this.escapeCsvField(booking.propertyTitle || ''),
        this.escapeCsvField(booking.propertyLocation || ''),
        this.escapeCsvField(booking.guestName || ''),
        ...(options.includeGuest ? [
          booking.guestPhone || '',
          booking.guestCount?.toString() || ''
        ] : []),
        booking.bookingType || '',
        booking.status || '',
        booking.source || '',
        ...(options.includeRevenue ? [booking.totalAmount?.toString() || '0'] : []),
        ...(options.includeNotes ? [this.escapeCsvField(booking.notes || '')] : [])
      ];

      rows.push(row);
    }

    return rows.map(row => row.join(',')).join('\n');
  }

  /**
   * Export to JSON format
   */
  private async exportToJSON(
    bookings: (CalendarBooking & { propertyTitle?: string; propertyLocation?: string })[],
    options: ExportOptions
  ): Promise<string> {
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        format: 'json',
        recordCount: bookings.length,
        dateRange: {
          start: options.startDate,
          end: options.endDate
        },
        options: {
          includeGuest: options.includeGuest,
          includeRevenue: options.includeRevenue,
          includeNotes: options.includeNotes
        }
      },
      bookings: bookings.map(booking => ({
        id: booking.id,
        propertyId: booking.propertyId,
        propertyTitle: booking.propertyTitle,
        propertyLocation: booking.propertyLocation,
        startDate: booking.startDate,
        endDate: booking.endDate,
        guestName: booking.guestName,
        ...(options.includeGuest && {
          guestPhone: booking.guestPhone,
          guestCount: booking.guestCount
        }),
        bookingType: booking.bookingType,
        status: booking.status,
        source: booking.source,
        ...(options.includeRevenue && {
          totalAmount: booking.totalAmount
        }),
        ...(options.includeNotes && {
          notes: booking.notes
        }),
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Export to Excel format (placeholder)
   */
  private async exportToExcel(
    bookings: (CalendarBooking & { propertyTitle?: string; propertyLocation?: string })[],
    options: ExportOptions
  ): Promise<Buffer> {
    // Would use a library like 'xlsx' to create Excel files
    throw new Error('Excel export not yet implemented');
  }

  /**
   * Export to PDF format (placeholder)
   */
  private async exportToPDF(
    bookings: (CalendarBooking & { propertyTitle?: string; propertyLocation?: string })[],
    options: ExportOptions
  ): Promise<Buffer> {
    // Would use a library like 'puppeteer' or 'pdfkit' to create PDF
    throw new Error('PDF export not yet implemented');
  }

  /**
   * Parse iCal data
   */
  private async parseICAL(icalData: string): Promise<any[]> {
    try {
      const jcalData = ICAL.parse(icalData);
      const comp = new ICAL.Component(jcalData);
      const events = comp.getAllSubcomponents('vevent');

      return events.map(event => {
        const summary = event.getFirstPropertyValue('summary') || '';
        const dtstart = event.getFirstPropertyValue('dtstart');
        const dtend = event.getFirstPropertyValue('dtend');
        const description = event.getFirstPropertyValue('description') || '';
        const uid = event.getFirstPropertyValue('uid') || '';

        // Extract guest name from summary
        const guestName = summary.split(' - ')[0] || 'Imported Guest';
        const bookingType = summary.includes('Morning') ? 'Morning Visit' : 'Full Day';

        return {
          guestName,
          startDate: dtstart ? dtstart.toJSDate().toISOString().split('T')[0] : null,
          endDate: dtend ? dtend.toJSDate().toISOString().split('T')[0] : null,
          bookingType,
          notes: description,
          externalId: uid,
          source: 'ical',
          status: 'confirmed'
        };
      });
    } catch (error) {
      throw new Error(`Invalid iCal format: ${(error as Error).message}`);
    }
  }

  /**
   * Parse CSV data
   */
  private async parseCSV(csvData: string): Promise<any[]> {
    const lines = csvData.trim().split('\n');
    if (lines.length < 2) {
      throw new Error('CSV must have at least a header and one data row');
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const records: any[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      const record: any = {};

      headers.forEach((header, index) => {
        const value = values[index]?.trim() || '';
        
        // Map common CSV headers to our schema
        switch (header) {
          case 'start date':
          case 'checkin':
          case 'check-in':
            record.startDate = value;
            break;
          case 'end date':
          case 'checkout':
          case 'check-out':
            record.endDate = value;
            break;
          case 'guest':
          case 'guest name':
          case 'name':
            record.guestName = value;
            break;
          case 'phone':
          case 'guest phone':
            record.guestPhone = value;
            break;
          case 'guests':
          case 'guest count':
            record.guestCount = parseInt(value) || 1;
            break;
          case 'booking type':
          case 'type':
            record.bookingType = value || 'Full Day';
            break;
          case 'status':
            record.status = value || 'confirmed';
            break;
          case 'source':
            record.source = value || 'csv';
            break;
          case 'total':
          case 'amount':
          case 'revenue':
            record.totalAmount = parseFloat(value) || 0;
            break;
          case 'notes':
          case 'description':
            record.notes = value;
            break;
          default:
            record[header] = value;
        }
      });

      records.push(record);
    }

    return records;
  }

  /**
   * Process individual import record
   */
  private async processImportRecord(
    record: any,
    index: number,
    options: ImportOptions
  ): Promise<{
    booking?: any;
    conflict?: ImportConflict;
  }> {
    // Validate required fields
    if (!record.startDate) {
      return {
        conflict: {
          rowIndex: index,
          data: record,
          reason: 'Missing start date',
          suggestion: 'Ensure start date is provided in YYYY-MM-DD format'
        }
      };
    }

    if (!record.guestName) {
      return {
        conflict: {
          rowIndex: index,
          data: record,
          reason: 'Missing guest name',
          suggestion: 'Provide guest name for the booking'
        }
      };
    }

    // Set default end date if not provided
    if (!record.endDate) {
      record.endDate = record.startDate;
    }

    // Check for existing conflicts
    const existingBookings = await db
      .select()
      .from(calendarBookings)
      .where(and(
        eq(calendarBookings.propertyId, options.propertyId),
        gte(calendarBookings.endDate, record.startDate),
        lte(calendarBookings.startDate, record.endDate)
      ));

    if (existingBookings.length > 0 && options.conflictResolution === 'skip') {
      return {
        conflict: {
          rowIndex: index,
          data: record,
          reason: `Conflicts with existing booking on ${record.startDate}`,
          suggestion: 'Change conflict resolution to overwrite or choose different dates'
        }
      };
    }

    // Create booking object
    const booking = {
      propertyId: options.propertyId,
      startDate: record.startDate,
      endDate: record.endDate,
      guestName: record.guestName,
      guestPhone: record.guestPhone || null,
      guestCount: record.guestCount || 2,
      bookingType: record.bookingType || 'Full Day',
      totalAmount: record.totalAmount || null,
      status: record.status || 'confirmed',
      source: record.source || options.format,
      externalId: record.externalId || null,
      notes: record.notes || '',
      createdBy: null // Import
    };

    return { booking };
  }

  /**
   * Get bookings for export
   */
  private async getBookingsForExport(options: ExportOptions) {
    let query = db
      .select({
        ...calendarBookings,
        propertyTitle: properties.title,
        propertyLocation: properties.location
      })
      .from(calendarBookings)
      .leftJoin(properties, eq(properties.id, calendarBookings.propertyId))
      .where(and(
        gte(calendarBookings.startDate, options.startDate),
        lte(calendarBookings.endDate, options.endDate)
      ));

    if (options.propertyIds && options.propertyIds.length > 0) {
      query = query.where(inArray(calendarBookings.propertyId, options.propertyIds));
    }

    if (options.ownerId) {
      query = query.where(eq(properties.ownerId, options.ownerId));
    }

    return await query.orderBy(calendarBookings.startDate);
  }

  /**
   * Utility methods
   */
  private escapeICalText(text: string): string {
    return text
      .replace(/\\/g, '\\\\')
      .replace(/,/g, '\\,')
      .replace(/;/g, '\\;')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '');
  }

  private escapeCsvField(text: string): string {
    if (text.includes(',') || text.includes('"') || text.includes('\n')) {
      return `"${text.replace(/"/g, '""')}"`;
    }
    return text;
  }

  private buildICalDescription(
    booking: CalendarBooking,
    options: ExportOptions
  ): string {
    let description = `Booking Details:\\n`;
    description += `Guest: ${booking.guestName}\\n`;
    
    if (options.includeGuest) {
      if (booking.guestPhone) {
        description += `Phone: ${booking.guestPhone}\\n`;
      }
      description += `Guests: ${booking.guestCount || 1}\\n`;
    }
    
    description += `Type: ${booking.bookingType}\\n`;
    description += `Status: ${booking.status}\\n`;
    
    if (options.includeRevenue && booking.totalAmount) {
      description += `Revenue: $${booking.totalAmount}\\n`;
    }
    
    if (options.includeNotes && booking.notes) {
      description += `Notes: ${booking.notes}\\n`;
    }
    
    description += `\\nSource: ${booking.source || 'direct'}`;
    description += `\\nGenerated by BookAFarm Calendar System`;
    
    return this.escapeICalText(description);
  }

  private addDaysToDateString(dateStr: string, days: number): string {
    const date = new Date(
      parseInt(dateStr.substring(0, 4)),
      parseInt(dateStr.substring(4, 6)) - 1,
      parseInt(dateStr.substring(6, 8))
    );
    date.setDate(date.getDate() + days);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}${month}${day}`;
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current);
    return result;
  }
}

// Export singleton instance
export const calendarExportImportService = new CalendarExportImportService();