import { EmailOTPProvider } from './EmailOTPProvider';
import { SMSOTPProvider } from './SMSOTPProvider';
import { OTPStorageService } from './OTPStorageService';
import { BaseOTPProvider } from './BaseOTPProvider';
import { otpRateLimitService } from './OTPRateLimitService';
import type { OTPRequest, OTPVerification, OTPResult } from './BaseOTPProvider';
import { config } from '../config';

export interface OTPServiceResult {
  success: boolean;
  message: string;
  code?: string | undefined; // Only for development/testing
  attemptsRemaining?: number | undefined;
}

export class OTPService {
  private emailProvider: EmailOTPProvider;
  private smsProvider: SMSOTPProvider;
  private storageService: OTPStorageService;

  constructor() {
    this.emailProvider = new EmailOTPProvider();
    this.smsProvider = new SMSOTPProvider();
    this.storageService = new OTPStorageService();
  }

  async sendOTP(request: OTPRequest): Promise<OTPServiceResult> {
    const { identifier, type } = request;
    
    // Get the appropriate provider
    const provider = this.getProvider(type);
    if (!provider) {
      return {
        success: false,
        message: `${type.toUpperCase()} service is not available`
      };
    }

    // Validate identifier format
    if (!provider.validateIdentifier(identifier)) {
      return {
        success: false,
        message: `Invalid ${type} format`
      };
    }

    // Sanitize identifier
    const sanitizedIdentifier = provider.sanitizeIdentifier(identifier);

    // Check rate limiting using the advanced rate limit service
    const rateLimitCheck = await otpRateLimitService.canRequestOTP(sanitizedIdentifier);
    if (!rateLimitCheck.allowed) {
      const timeStr = rateLimitCheck.waitTime ? ` Please wait ${Math.ceil(rateLimitCheck.waitTime / 60)} minutes.` : '';
      return {
        success: false,
        message: `${rateLimitCheck.reason}.${timeStr}`,
        attemptsRemaining: rateLimitCheck.attemptsLeft
      };
    }

    // Check existing OTP and clean up if needed
    const stats = await this.storageService.getOTPStats(sanitizedIdentifier, type);
    if (stats.exists) {
      await this.storageService.deleteOTP(sanitizedIdentifier, type);
    }

    // Generate OTP code
    const code = provider.generateOTP();

    try {
      // Store OTP in database first
      await this.storageService.storeOTP(sanitizedIdentifier, code, type);

      // Send OTP via provider
      const result = await provider.sendOTP(sanitizedIdentifier, code);

      if (result.success) {
        // Record successful OTP request in rate limiter
        await otpRateLimitService.recordOTPRequest(sanitizedIdentifier);
      } else {
        // If sending failed, clean up the stored OTP
        await this.storageService.deleteOTP(sanitizedIdentifier, type);
      }

      return {
        success: result.success,
        message: result.message,
        code: result.code || undefined,
        attemptsRemaining: rateLimitCheck.attemptsLeft
      };
    } catch (error) {
      console.error(`Error sending ${type} OTP:`, error);
      
      // Clean up on error
      try {
        await this.storageService.deleteOTP(sanitizedIdentifier, type);
      } catch (cleanupError) {
        console.error('Error cleaning up failed OTP:', cleanupError);
      }

      return {
        success: false,
        message: `Failed to send ${type.toUpperCase()}. Please try again.`
      };
    }
  }

  async verifyOTP(verification: OTPVerification): Promise<OTPServiceResult> {
    const { identifier, code, type } = verification;

    // Get the appropriate provider for validation
    const provider = this.getProvider(type);
    if (!provider) {
      return {
        success: false,
        message: `${type.toUpperCase()} service is not available`
      };
    }

    // Validate identifier format
    if (!provider.validateIdentifier(identifier)) {
      return {
        success: false,
        message: `Invalid ${type} format`
      };
    }

    // Sanitize identifier
    const sanitizedIdentifier = provider.sanitizeIdentifier(identifier);

    // Check verification rate limiting
    const verifyRateCheck = await otpRateLimitService.canVerifyOTP(sanitizedIdentifier);
    if (!verifyRateCheck.allowed) {
      const timeStr = verifyRateCheck.waitTime ? ` Please wait ${Math.ceil(verifyRateCheck.waitTime / 60)} minutes.` : '';
      return {
        success: false,
        message: `${verifyRateCheck.reason}.${timeStr}`,
        attemptsRemaining: verifyRateCheck.attemptsLeft
      };
    }

    // Check for master codes in development for SMS and Email
    if (type === 'sms' && this.smsProvider.isMasterCode(code)) {
      console.log(`🔐 [DEV] SMS Master code used for ${sanitizedIdentifier}`);
      return {
        success: true,
        message: 'OTP verified successfully (SMS master code)'
      };
    }

    if (type === 'email' && this.emailProvider.isMasterCode(code)) {
      console.log(`🔐 [DEV] Email Master code used for ${sanitizedIdentifier}`);
      return {
        success: true,
        message: 'OTP verified successfully (Email master code)'
      };
    }

    // Verify OTP using storage service
    const result = await this.storageService.verifyOTP(sanitizedIdentifier, code, type);

    // Record verification attempt in rate limiter
    await otpRateLimitService.recordOTPVerificationAttempt(sanitizedIdentifier, result.success);

    return {
      success: result.success,
      message: result.message,
      attemptsRemaining: result.attemptsRemaining || verifyRateCheck.attemptsLeft
    };
  }

  async getOTPStatus(identifier: string, type: 'email' | 'sms'): Promise<{
    canRequest: boolean;
    timeRemaining?: number;
    attemptsRemaining?: number;
    message: string;
  }> {
    const provider = this.getProvider(type);
    if (!provider) {
      return {
        canRequest: false,
        message: `${type.toUpperCase()} service is not available`
      };
    }

    const sanitizedIdentifier = provider.sanitizeIdentifier(identifier);
    const stats = await this.storageService.getOTPStats(sanitizedIdentifier, type);

    if (!stats.exists) {
      return {
        canRequest: true,
        message: 'Ready to send OTP'
      };
    }

    if (stats.expiresAt && new Date() < stats.expiresAt) {
      const timeRemaining = Math.ceil((stats.expiresAt.getTime() - Date.now()) / 1000 / 60);
      return {
        canRequest: false,
        timeRemaining,
        attemptsRemaining: stats.attemptsRemaining,
        message: `OTP already sent. ${timeRemaining} minutes remaining.`
      };
    }

    return {
      canRequest: true,
      message: 'Previous OTP expired, ready to send new one'
    };
  }

  getServiceStatus(): {
    email: { available: boolean; provider: string };
    sms: { available: boolean; provider: string };
    rateLimiting: { enabled: boolean; service: string };
    environment: string;
  } {
    return {
      email: {
        available: this.emailProvider.isAvailable(),
        provider: this.emailProvider.name
      },
      sms: {
        available: this.smsProvider.isAvailable(),
        provider: this.smsProvider.name
      },
      rateLimiting: {
        enabled: true,
        service: 'OTPRateLimitService'
      },
      environment: config.app.nodeEnv
    };
  }

  private getProvider(type: 'email' | 'sms'): BaseOTPProvider | null {
    switch (type) {
      case 'email':
        return this.emailProvider;
      case 'sms':
        return this.smsProvider;
      default:
        return null;
    }
  }

  // Cleanup method for maintenance
  async performMaintenance(): Promise<void> {
    console.log('🧹 Performing OTP service maintenance...');
    try {
      await this.storageService.cleanupExpiredTokens();
      console.log('✅ OTP maintenance completed');
    } catch (error) {
      console.error('❌ OTP maintenance failed:', error);
    }
  }
}

// Export singleton instance
export const otpService = new OTPService();

// Export types for backward compatibility
export type { OTPRequest, OTPVerification, OTPResult };