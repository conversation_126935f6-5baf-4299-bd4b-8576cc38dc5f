import { db, withHealthyConnection } from '../db';
import { otpTokens } from '../../shared/schema';
import { eq, and, lt } from 'drizzle-orm';
import crypto from 'crypto';

export interface StoredOTP {
  id: number;
  identifier: string;
  code: string;
  type: 'email' | 'sms';
  attempts: number;
  expiresAt: Date;
  createdAt: Date;
}

export interface OTPVerificationResult {
  success: boolean;
  message: string;
  attemptsRemaining?: number;
}

interface OTPStats {
  totalSent: number;
  verifiedCount: number;
  sentLast24h: number;
  lastSentAt: Date | null;
}

export class OTPStorageService {
  private readonly MAX_ATTEMPTS = 5;
  private readonly EXPIRY_MINUTES = 10;

  async storeOTP(identifier: string, code: string, type: 'email' | 'sms'): Promise<void> {
    return withHealthyConnection(async () => {
      // Clean up expired tokens first
      await this.cleanupExpiredTokens();

      // Delete any existing OTP for this identifier and type
      await db
        .delete(otpTokens)
        .where(
          and(
            eq(otpTokens.identifier, identifier),
            eq(otpTokens.type, type)
          )
        );

      // Create expiry time
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + this.EXPIRY_MINUTES);

      // Hash the code for security
      const hashedCode = this.hashCode(code);

      // Store new OTP
      await db.insert(otpTokens).values({
        identifier,
        code: hashedCode,
        type,
        attempts: 0,
        expiresAt,
      });
    }, 'OTP storage');
  }

  async verifyOTP(identifier: string, code: string, type: 'email' | 'sms'): Promise<OTPVerificationResult> {
    // Clean up expired tokens first
    await this.cleanupExpiredTokens();

    // Find the OTP record
    const [otpRecord] = await db
      .select()
      .from(otpTokens)
      .where(
        and(
          eq(otpTokens.identifier, identifier),
          eq(otpTokens.type, type)
        )
      );

    if (!otpRecord) {
      return {
        success: false,
        message: 'No OTP found. Please request a new one.'
      };
    }

    // Check if expired
    if (new Date() > otpRecord.expiresAt) {
      await this.deleteOTP(identifier, type);
      return {
        success: false,
        message: 'OTP has expired. Please request a new one.'
      };
    }

    // Check attempts limit
    if (otpRecord.attempts >= this.MAX_ATTEMPTS) {
      await this.deleteOTP(identifier, type);
      return {
        success: false,
        message: 'Maximum verification attempts exceeded. Please request a new OTP.'
      };
    }

    // Verify the code
    const hashedCode = this.hashCode(code);
    const isValidCode = otpRecord.code === hashedCode;

    if (isValidCode) {
      // Success - delete the OTP
      await this.deleteOTP(identifier, type);
      return {
        success: true,
        message: 'OTP verified successfully.'
      };
    } else {
      // Increment attempts
      const newAttempts = otpRecord.attempts + 1;
      await db
        .update(otpTokens)
        .set({ attempts: newAttempts })
        .where(
          and(
            eq(otpTokens.identifier, identifier),
            eq(otpTokens.type, type)
          )
        );

      const attemptsRemaining = this.MAX_ATTEMPTS - newAttempts;

      if (attemptsRemaining <= 0) {
        await this.deleteOTP(identifier, type);
        return {
          success: false,
          message: 'Invalid OTP. Maximum attempts exceeded. Please request a new OTP.'
        };
      }

      return {
        success: false,
        message: `Invalid OTP. ${attemptsRemaining} attempts remaining.`,
        attemptsRemaining
      };
    }
  }

  async deleteOTP(identifier: string, type: 'email' | 'sms'): Promise<void> {
    await db
      .delete(otpTokens)
      .where(
        and(
          eq(otpTokens.identifier, identifier),
          eq(otpTokens.type, type)
        )
      );
  }

  async cleanupExpiredTokens(): Promise<void> {
    try {
      await db
        .delete(otpTokens)
        .where(lt(otpTokens.expiresAt, new Date()));
    } catch (error) {
      console.error('Error cleaning up expired OTP tokens:', error);
    }
  }

  async getOTPStats(identifier: string, type: 'email' | 'sms'): Promise<{
    exists: boolean;
    attempts: number;
    expiresAt: Date | null;
    attemptsRemaining: number;
  }> {
    const [otpRecord] = await db
      .select()
      .from(otpTokens)
      .where(
        and(
          eq(otpTokens.identifier, identifier),
          eq(otpTokens.type, type)
        )
      );

    if (!otpRecord) {
      return {
        exists: false,
        attempts: 0,
        expiresAt: null,
        attemptsRemaining: this.MAX_ATTEMPTS
      };
    }

    return {
      exists: true,
      attempts: otpRecord.attempts,
      expiresAt: otpRecord.expiresAt,
      attemptsRemaining: Math.max(0, this.MAX_ATTEMPTS - otpRecord.attempts)
    };
  }

  private hashCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex');
  }
}