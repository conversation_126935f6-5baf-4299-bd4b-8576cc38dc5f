import { MailService } from '@sendgrid/mail';
import { BaseOTPProvider, OTPResult } from './BaseOTPProvider';
import { config } from '../config';

export class EmailOTPProvider extends BaseOTPProvider {
  readonly type = 'email' as const;
  readonly name = 'SendGrid Email';
  
  private mailService: MailService | null = null;
  private initialized = false;

  constructor() {
    super();
    this.initialize();
  }

  private initialize(): void {
    if (this.initialized) return;

    if (config.sendgrid.available && config.sendgrid.apiKey) {
      try {
        this.mailService = new MailService();
        this.mailService.setApiKey(config.sendgrid.apiKey);
        console.log('✅ SendGrid Email service initialized');
      } catch (error) {
        console.warn('⚠️ Failed to initialize SendGrid:', error);
        this.mailService = null;
      }
    } else {
      console.log('⚠️ SendGrid not configured - email OTPs will be logged to console');
    }

    this.initialized = true;
  }

  isAvailable(): boolean {
    return this.mailService !== null;
  }

  // Master codes that work for any email in development (same as SMS)
  isMasterCode(code: string): boolean {
    const masterCodes = ['123456', '999999'];
    return config.isDevelopment() && masterCodes.includes(code);
  }

  async sendOTP(identifier: string, code: string): Promise<OTPResult> {
    if (!this.validateIdentifier(identifier)) {
      return {
        success: false,
        message: 'Invalid email address format'
      };
    }

    const sanitizedEmail = this.sanitizeIdentifier(identifier);

    if (!this.isAvailable()) {
      // Development fallback
      if (config.isDevelopment()) {
        // Log to server only - do not expose OTP code in console
        console.log(`📧 [DEV] Email OTP sent to ${sanitizedEmail} - check server logs for code`);
        // Log OTP code only to server logs for development testing
        require('fs').appendFileSync('server.log', `[${new Date().toISOString()}] Email OTP for ${sanitizedEmail}: ${code}\n`);
      }
      const result: OTPResult = {
        success: true,
        message: 'OTP sent successfully (development mode)'
      };
      return result;
    }

    try {
      const msg = {
        to: sanitizedEmail,
        from: { 
          email: '<EMAIL>', 
          name: 'BookAFarm Verification' 
        },
        replyTo: {
          email: '<EMAIL>',
          name: 'BookAFarm Support'
        },
        subject: 'Your BookAFarm Verification Code',
        text: `Your verification code is: ${code}. This code will expire in 10 minutes.`,
        html: this.generateEmailTemplate(code)
      };

      await this.mailService!.send(msg);
      
      return {
        success: true,
        message: 'OTP sent successfully to your email'
      };
    } catch (error: any) {
      console.error('Email sending error:', error);
      
      // Fallback to server logging in development
      if (config.isDevelopment()) {
        console.log(`📧 [FALLBACK] Email OTP fallback for ${sanitizedEmail} - check server logs for code`);
        // Log OTP code only to server logs for development testing
        require('fs').appendFileSync('server.log', `[${new Date().toISOString()}] Email OTP FALLBACK for ${sanitizedEmail}: ${code}\n`);
        const fallbackResult: OTPResult = {
          success: true,
          message: 'OTP sent successfully (fallback mode)'
        };
        return fallbackResult;
      }
      
      return {
        success: false,
        message: 'Failed to send email. Please try again.'
      };
    }
  }

  private generateEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>BookAFarm Verification Code</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4A6741; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .code { font-size: 32px; font-weight: bold; text-align: center; 
                  background: white; padding: 20px; margin: 20px 0; 
                  border: 2px dashed #4A6741; border-radius: 8px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>BookAFarm</h1>
          </div>
          <div class="content">
            <h2>Email Verification</h2>
            <p>Your verification code is:</p>
            <div class="code">${code}</div>
            <p>This code will expire in <strong>10 minutes</strong>.</p>
            <p>If you didn't request this code, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>© 2024 BookAFarm. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}