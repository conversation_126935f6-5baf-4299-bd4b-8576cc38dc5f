import { cacheService } from './CacheService';
import { log } from '../utils/logger';
import { CircuitBreakerRegistry } from './CircuitBreaker';

// Create dedicated circuit breaker for OTP rate limiting
const OTPRateLimitCircuitBreaker = CircuitBreakerRegistry.getOrCreate('otp-rate-limit', {
  failureThreshold: 3,        // Fail fast for security
  recoveryTimeout: 30000,     // 30 seconds recovery
  timeout: 3000,              // 3 second timeout
  monitoringPeriod: 60000     // 1 minute monitoring window
});

interface OTPAttempt {
  count: number;
  lastAttempt: number;
  blocked: boolean;
  blockUntil?: number;
}

interface RateLimitConfig {
  maxAttempts: number;
  windowSeconds: number;
  blockDurationSeconds: number;
  cooldownSeconds: number;
}

export class OTPRateLimitService {
  private static readonly DEFAULT_CONFIG: RateLimitConfig = {
    maxAttempts: 3, // 3 OTP requests per window
    windowSeconds: 300, // 5 minutes window
    blockDurationSeconds: 900, // 15 minutes block
    cooldownSeconds: 60 // 1 minute cooldown between requests
  };

  private static readonly CACHE_KEYS = {
    OTP_ATTEMPTS: 'otp:attempts',
    OTP_SENT: 'otp:sent',
    OTP_VERIFY_ATTEMPTS: 'otp:verify',
    DAILY_LIMIT: 'otp:daily'
  };

  // Check if user can request OTP
  async canRequestOTP(identifier: string, config: Partial<RateLimitConfig> = {}): Promise<{
    allowed: boolean;
    reason?: string;
    waitTime?: number;
    attemptsLeft?: number;
  }> {
    const rateLimitConfig = { ...OTPRateLimitService.DEFAULT_CONFIG, ...config };
    const now = Date.now();

    try {
      // Check daily limit (100 OTPs per day per identifier)
      const dailyKey = `${OTPRateLimitService.CACHE_KEYS.DAILY_LIMIT}:${identifier}:${this.getDayKey()}`;
      const dailyCount = await cacheService.get<number>(dailyKey) || 0;
      if (dailyCount >= 100) {
        return {
          allowed: false,
          reason: 'Daily OTP limit exceeded',
          waitTime: this.getSecondsUntilMidnight()
        };
      }

      // Check if currently blocked
      const attemptsKey = `${OTPRateLimitService.CACHE_KEYS.OTP_ATTEMPTS}:${identifier}`;
      const attempts = await cacheService.get<OTPAttempt>(attemptsKey);

      if (attempts?.blocked && attempts.blockUntil && now < attempts.blockUntil) {
        const waitTime = Math.ceil((attempts.blockUntil - now) / 1000);
        return {
          allowed: false,
          reason: 'Temporarily blocked due to too many attempts',
          waitTime
        };
      }

      // Check cooldown period
      const lastSentKey = `${OTPRateLimitService.CACHE_KEYS.OTP_SENT}:${identifier}`;
      const lastSent = await cacheService.get<number>(lastSentKey);
      if (lastSent && (now - lastSent) < (rateLimitConfig.cooldownSeconds * 1000)) {
        const waitTime = Math.ceil((rateLimitConfig.cooldownSeconds * 1000 - (now - lastSent)) / 1000);
        return {
          allowed: false,
          reason: 'Please wait before requesting another OTP',
          waitTime
        };
      }

      // Check rate limit window
      if (attempts) {
        const windowStart = now - (rateLimitConfig.windowSeconds * 1000);
        if (attempts.lastAttempt > windowStart) {
          if (attempts.count >= rateLimitConfig.maxAttempts) {
            // Block the user
            const blockUntil = now + (rateLimitConfig.blockDurationSeconds * 1000);
            await cacheService.set(attemptsKey, {
              ...attempts,
              blocked: true,
              blockUntil
            }, rateLimitConfig.blockDurationSeconds);

            return {
              allowed: false,
              reason: 'Too many OTP requests',
              waitTime: rateLimitConfig.blockDurationSeconds
            };
          }
        }
      }

      // Calculate attempts left
      const currentAttempts = (attempts?.lastAttempt && attempts.lastAttempt > (now - rateLimitConfig.windowSeconds * 1000))
        ? attempts.count 
        : 0;
      const attemptsLeft = rateLimitConfig.maxAttempts - currentAttempts;

      return {
        allowed: true,
        attemptsLeft
      };

    } catch (error) {
      log(`❌ Rate limit check failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
      
      // SECURITY: Fail closed for rate limiting - deny request on cache failure
      log(`🚨 SECURITY: OTP rate limit check failed - denying request for security`, 'otp-rate-limit');
      return { 
        allowed: false,
        reason: 'Rate limiting service temporarily unavailable',
        waitTime: 60 // Wait 1 minute before retrying
      };
    }
  }

  // Record OTP request
  async recordOTPRequest(identifier: string): Promise<void> {
    const now = Date.now();

    try {
      // Update attempts counter
      const attemptsKey = `${OTPRateLimitService.CACHE_KEYS.OTP_ATTEMPTS}:${identifier}`;
      const attempts = await cacheService.get<OTPAttempt>(attemptsKey) || {
        count: 0,
        lastAttempt: 0,
        blocked: false
      };

      const windowStart = now - (OTPRateLimitService.DEFAULT_CONFIG.windowSeconds * 1000);
      const newCount = attempts.lastAttempt > windowStart ? attempts.count + 1 : 1;

      await cacheService.set(attemptsKey, {
        count: newCount,
        lastAttempt: now,
        blocked: false
      }, OTPRateLimitService.DEFAULT_CONFIG.windowSeconds);

      // Record last sent time
      const lastSentKey = `${OTPRateLimitService.CACHE_KEYS.OTP_SENT}:${identifier}`;
      await cacheService.set(lastSentKey, now, OTPRateLimitService.DEFAULT_CONFIG.cooldownSeconds);

      // Update daily counter
      const dailyKey = `${OTPRateLimitService.CACHE_KEYS.DAILY_LIMIT}:${identifier}:${this.getDayKey()}`;
      const dailyCount = await cacheService.get<number>(dailyKey) || 0;
      await cacheService.set(dailyKey, dailyCount + 1, 86400); // 24 hours

      log(`📊 OTP request recorded for ${identifier} (${newCount}/${OTPRateLimitService.DEFAULT_CONFIG.maxAttempts})`, 'otp-rate-limit');

    } catch (error) {
      log(`❌ Failed to record OTP request: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
    }
  }

  // Check OTP verification rate limit
  async canVerifyOTP(identifier: string): Promise<{
    allowed: boolean;
    reason?: string;
    waitTime?: number;
    attemptsLeft?: number;
  }> {
    const now = Date.now();
    const maxVerifyAttempts = 5;
    const verifyWindowSeconds = 300; // 5 minutes
    const blockDurationSeconds = 600; // 10 minutes

    try {
      const verifyKey = `${OTPRateLimitService.CACHE_KEYS.OTP_VERIFY_ATTEMPTS}:${identifier}`;
      const verifyAttempts = await cacheService.get<OTPAttempt>(verifyKey);

      // Check if blocked
      if (verifyAttempts?.blocked && verifyAttempts.blockUntil && now < verifyAttempts.blockUntil) {
        const waitTime = Math.ceil((verifyAttempts.blockUntil - now) / 1000);
        return {
          allowed: false,
          reason: 'Too many verification attempts',
          waitTime
        };
      }

      // Check rate limit
      if (verifyAttempts) {
        const windowStart = now - (verifyWindowSeconds * 1000);
        if (verifyAttempts.lastAttempt > windowStart && verifyAttempts.count >= maxVerifyAttempts) {
          // Block verification attempts
          const blockUntil = now + (blockDurationSeconds * 1000);
          await cacheService.set(verifyKey, {
            ...verifyAttempts,
            blocked: true,
            blockUntil
          }, blockDurationSeconds);

          return {
            allowed: false,
            reason: 'Too many verification attempts',
            waitTime: blockDurationSeconds
          };
        }
      }

      const currentAttempts = (verifyAttempts?.lastAttempt && verifyAttempts.lastAttempt > (now - verifyWindowSeconds * 1000))
        ? verifyAttempts.count 
        : 0;
      const attemptsLeft = maxVerifyAttempts - currentAttempts;

      return {
        allowed: true,
        attemptsLeft
      };

    } catch (error) {
      log(`❌ Verification rate limit check failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
      
      // SECURITY: Fail closed for OTP verification rate limiting
      log(`🚨 SECURITY: OTP verification rate limit check failed - denying verification for security`, 'otp-rate-limit');
      return { 
        allowed: false,
        reason: 'Verification rate limiting service temporarily unavailable',
        waitTime: 60
      };
    }
  }

  // Record OTP verification attempt
  async recordOTPVerificationAttempt(identifier: string, success: boolean): Promise<void> {
    const now = Date.now();

    try {
      const verifyKey = `${OTPRateLimitService.CACHE_KEYS.OTP_VERIFY_ATTEMPTS}:${identifier}`;
      
      if (success) {
        // Clear verification attempts on success
        await cacheService.delete(verifyKey);
        log(`✅ OTP verification successful for ${identifier} - attempts cleared`, 'otp-rate-limit');
      } else {
        // Increment failed attempts
        const verifyAttempts = await cacheService.get<OTPAttempt>(verifyKey) || {
          count: 0,
          lastAttempt: 0,
          blocked: false
        };

        const windowStart = now - (300 * 1000); // 5 minutes
        const newCount = verifyAttempts.lastAttempt > windowStart ? verifyAttempts.count + 1 : 1;

        await cacheService.set(verifyKey, {
          count: newCount,
          lastAttempt: now,
          blocked: false
        }, 300); // 5 minutes

        log(`❌ OTP verification failed for ${identifier} (${newCount}/5)`, 'otp-rate-limit');
      }

    } catch (error) {
      log(`❌ Failed to record verification attempt: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
    }
  }

  // Clear rate limits for identifier (admin function)
  async clearRateLimits(identifier: string): Promise<void> {
    try {
      const keysToDelete = [
        `${OTPRateLimitService.CACHE_KEYS.OTP_ATTEMPTS}:${identifier}`,
        `${OTPRateLimitService.CACHE_KEYS.OTP_SENT}:${identifier}`,
        `${OTPRateLimitService.CACHE_KEYS.OTP_VERIFY_ATTEMPTS}:${identifier}`
      ];

      await Promise.all(keysToDelete.map(key => cacheService.delete(key)));
      log(`🧹 Rate limits cleared for ${identifier}`, 'otp-rate-limit');

    } catch (error) {
      log(`❌ Failed to clear rate limits: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
    }
  }

  // Get rate limit status for identifier
  async getRateLimitStatus(identifier: string): Promise<{
    otpRequests: { count: number; maxAttempts: number; blocked: boolean; waitTime?: number };
    verifyAttempts: { count: number; maxAttempts: number; blocked: boolean; waitTime?: number };
    dailyCount: number;
    lastOTPSent?: Date;
  }> {
    const now = Date.now();

    try {
      const [otpAttempts, verifyAttempts, dailyCount, lastSent] = await Promise.all([
        cacheService.get<OTPAttempt>(`${OTPRateLimitService.CACHE_KEYS.OTP_ATTEMPTS}:${identifier}`),
        cacheService.get<OTPAttempt>(`${OTPRateLimitService.CACHE_KEYS.OTP_VERIFY_ATTEMPTS}:${identifier}`),
        cacheService.get<number>(`${OTPRateLimitService.CACHE_KEYS.DAILY_LIMIT}:${identifier}:${this.getDayKey()}`),
        cacheService.get<number>(`${OTPRateLimitService.CACHE_KEYS.OTP_SENT}:${identifier}`)
      ]);

      const otpWindowStart = now - (OTPRateLimitService.DEFAULT_CONFIG.windowSeconds * 1000);
      const verifyWindowStart = now - (300 * 1000);

      return {
        otpRequests: {
          count: (otpAttempts?.lastAttempt && otpAttempts.lastAttempt > otpWindowStart) ? otpAttempts.count : 0,
          maxAttempts: OTPRateLimitService.DEFAULT_CONFIG.maxAttempts,
          blocked: otpAttempts?.blocked && otpAttempts.blockUntil ? now < otpAttempts.blockUntil : false,
          ...(otpAttempts?.blockUntil && now < otpAttempts.blockUntil 
            ? { waitTime: Math.ceil((otpAttempts.blockUntil - now) / 1000) }
            : {})
        },
        verifyAttempts: {
          count: (verifyAttempts?.lastAttempt && verifyAttempts.lastAttempt > verifyWindowStart) ? verifyAttempts.count : 0,
          maxAttempts: 5,
          blocked: verifyAttempts?.blocked && verifyAttempts.blockUntil ? now < verifyAttempts.blockUntil : false,
          ...(verifyAttempts?.blockUntil && now < verifyAttempts.blockUntil 
            ? { waitTime: Math.ceil((verifyAttempts.blockUntil - now) / 1000) }
            : {})
        },
        dailyCount: dailyCount || 0,
        ...(lastSent ? { lastOTPSent: new Date(lastSent) } : {})
      };

    } catch (error) {
      log(`❌ Failed to get rate limit status: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
      
      // SECURITY: When status check fails, indicate system is unavailable rather than allowing everything
      return {
        otpRequests: { count: 999, maxAttempts: 3, blocked: true, waitTime: 60 },
        verifyAttempts: { count: 999, maxAttempts: 5, blocked: true, waitTime: 60 },
        dailyCount: 999
      };
    }
  }

  // Helper methods
  private getDayKey(): string {
    return new Date().toISOString().split('T')[0];
  }

  private getSecondsUntilMidnight(): number {
    const now = new Date();
    const midnight = new Date(now);
    midnight.setHours(24, 0, 0, 0);
    return Math.ceil((midnight.getTime() - now.getTime()) / 1000);
  }

  // Cache cleanup for old entries
  async cleanup(): Promise<void> {
    try {
      // Get all OTP-related cache keys
      const otpKeys = await cacheService.keys('otp:*');
      const now = Date.now();
      let cleaned = 0;

      for (const key of otpKeys) {
        // Clean up old daily entries (older than 7 days)
        if (key.includes(':daily:')) {
          const keyDate = key.split(':').pop();
          if (keyDate) {
            const entryDate = new Date(keyDate);
            const daysDiff = (now - entryDate.getTime()) / (1000 * 60 * 60 * 24);
            if (daysDiff > 7) {
              await cacheService.delete(key);
              cleaned++;
            }
          }
        }
      }

      if (cleaned > 0) {
        log(`🧹 Cleaned up ${cleaned} old OTP rate limit entries`, 'otp-rate-limit');
      }

    } catch (error) {
      log(`❌ OTP rate limit cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'otp-rate-limit');
    }
  }
}

export const otpRateLimitService = new OTPRateLimitService();