import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarBookings, 
  properties,
  users,
  InsertCalendarBooking
} from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import * as ICAL from 'ical.js';
import * as csv from 'csv-parser';
import { Readable } from 'stream';

export interface BulkImportOptions {
  propertyId: number;
  format: 'csv' | 'ical' | 'json' | 'excel';
  source: 'airbnb' | 'booking.com' | 'vrbo' | 'custom' | 'manual';
  conflictResolution: 'skip' | 'overwrite' | 'merge';
  dryRun?: boolean;
}

export interface ImportResult {
  success: boolean;
  totalProcessed: number;
  imported: number;
  skipped: number;
  errors: string[];
  conflicts: ImportConflict[];
  preview?: any[];
}

export interface ImportConflict {
  rowIndex: number;
  data: any;
  reason: string;
  suggestion: string;
}

export class CalendarBulkImportService {
  
  /**
   * Bulk import bookings from various sources
   */
  async importBookings(
    data: string | Buffer,
    options: BulkImportOptions
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      totalProcessed: 0,
      imported: 0,
      skipped: 0,
      errors: [],
      conflicts: []
    };

    try {
      logger.info('Starting bulk import', {
        propertyId: options.propertyId,
        format: options.format,
        source: options.source,
        dryRun: options.dryRun
      });

      // Validate property exists and user has permission
      await this.validateProperty(options.propertyId);

      // Parse data based on format
      let parsedData: any[];
      switch (options.format) {
        case 'csv':
          parsedData = await this.parseCSV(data.toString());
          break;
        case 'ical':
          parsedData = await this.parseICAL(data.toString());
          break;
        case 'json':
          parsedData = JSON.parse(data.toString());
          break;
        case 'excel':
          parsedData = await this.parseExcel(data);
          break;
        default:
          throw new Error(`Unsupported format: ${options.format}`);
      }

      result.totalProcessed = parsedData.length;

      // Process each record
      for (const [index, record] of parsedData.entries()) {
        try {
          const processResult = await this.processRecord(
            record, 
            index, 
            options
          );

          if (processResult.conflict) {
            result.conflicts.push(processResult.conflict);
            result.skipped++;
          } else if (processResult.booking) {
            if (!options.dryRun) {
              await db.insert(calendarBookings).values(processResult.booking);
            }
            result.imported++;
          } else {
            result.skipped++;
          }
        } catch (error) {
          result.errors.push(`Row ${index + 1}: ${(error as Error).message}`);
          result.skipped++;
        }
      }

      // For dry run, return preview
      if (options.dryRun) {
        result.preview = parsedData.slice(0, 10); // First 10 records
      }

      result.success = result.errors.length < result.totalProcessed * 0.5; // Success if < 50% errors

      logger.info('Bulk import completed', {
        propertyId: options.propertyId,
        totalProcessed: result.totalProcessed,
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors.length
      });

      return result;
    } catch (error) {
      logger.error('Bulk import failed', error as Error);
      result.errors.push(`Import failed: ${(error as Error).message}`);
      return result;
    }
  }

  /**
   * Parse CSV data
   */
  private async parseCSV(csvData: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const stream = Readable.from([csvData]);
      
      stream
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', reject);
    });
  }

  /**
   * Parse iCal data
   */
  private async parseICAL(icalData: string): Promise<any[]> {
    try {
      const jcalData = ICAL.parse(icalData);
      const comp = new ICAL.Component(jcalData);
      const events = comp.getAllSubcomponents('vevent');

      return events.map(event => {
        const summary = event.getFirstPropertyValue('summary');
        const dtstart = event.getFirstPropertyValue('dtstart');
        const dtend = event.getFirstPropertyValue('dtend');
        const description = event.getFirstPropertyValue('description');
        const uid = event.getFirstPropertyValue('uid');

        return {
          summary: summary || 'Imported Event',
          startDate: dtstart ? dtstart.toJSDate().toISOString().split('T')[0] : null,
          endDate: dtend ? dtend.toJSDate().toISOString().split('T')[0] : null,
          description: description || '',
          externalId: uid || null,
          source: 'ical'
        };
      });
    } catch (error) {
      throw new Error(`Invalid iCal format: ${(error as Error).message}`);
    }
  }

  /**
   * Parse Excel data (placeholder)
   */
  private async parseExcel(excelData: Buffer): Promise<any[]> {
    // Would use a library like 'xlsx' to parse Excel files
    throw new Error('Excel import not yet implemented');
  }

  /**
   * Process individual record
   */
  private async processRecord(
    record: any,
    index: number,
    options: BulkImportOptions
  ): Promise<{
    booking?: InsertCalendarBooking;
    conflict?: ImportConflict;
  }> {
    try {
      // Normalize data based on source
      const normalized = this.normalizeRecord(record, options.source);

      // Validate required fields
      const validation = this.validateRecord(normalized, index);
      if (!validation.valid) {
        return {
          conflict: {
            rowIndex: index,
            data: record,
            reason: validation.error!,
            suggestion: 'Fix the data and try again'
          }
        };
      }

      // Check for conflicts
      const existing = await this.checkForConflicts(
        options.propertyId,
        normalized.startDate,
        normalized.endDate
      );

      if (existing.length > 0) {
        if (options.conflictResolution === 'skip') {
          return {
            conflict: {
              rowIndex: index,
              data: record,
              reason: `Conflicts with existing booking on ${normalized.startDate}`,
              suggestion: 'Change conflict resolution to overwrite or merge'
            }
          };
        }
        // Handle overwrite/merge logic here
      }

      // Create booking object
      const booking: InsertCalendarBooking = {
        propertyId: options.propertyId,
        startDate: normalized.startDate,
        endDate: normalized.endDate,
        guestName: normalized.guestName,
        guestPhone: normalized.guestPhone,
        guestCount: normalized.guestCount,
        bookingType: normalized.bookingType,
        totalAmount: normalized.totalAmount,
        status: normalized.status || 'confirmed',
        source: options.source,
        externalId: normalized.externalId,
        notes: normalized.notes,
        createdBy: null // Bulk import
      };

      return { booking };
    } catch (error) {
      return {
        conflict: {
          rowIndex: index,
          data: record,
          reason: `Processing error: ${(error as Error).message}`,
          suggestion: 'Check data format and try again'
        }
      };
    }
  }

  /**
   * Normalize record data based on source
   */
  private normalizeRecord(record: any, source: string): any {
    switch (source) {
      case 'airbnb':
        return this.normalizeAirbnbRecord(record);
      case 'booking.com':
        return this.normalizeBookingComRecord(record);
      case 'vrbo':
        return this.normalizeVrboRecord(record);
      case 'custom':
      case 'manual':
      default:
        return this.normalizeCustomRecord(record);
    }
  }

  /**
   * Normalize Airbnb export format
   */
  private normalizeAirbnbRecord(record: any): any {
    return {
      startDate: this.parseDate(record['Start Date'] || record.checkin || record.start_date),
      endDate: this.parseDate(record['End Date'] || record.checkout || record.end_date),
      guestName: record['Guest Name'] || record.guest || record.guest_name || 'Airbnb Guest',
      guestPhone: record['Phone'] || record.phone || null,
      guestCount: parseInt(record['Guests'] || record.guests || record.guest_count || '2'),
      bookingType: 'Full Day',
      totalAmount: this.parseAmount(record['Total'] || record.total || record.amount),
      status: this.normalizeStatus(record['Status'] || record.status),
      externalId: record['Confirmation Code'] || record.confirmation || record.id,
      notes: record['Notes'] || record.notes || record.message || ''
    };
  }

  /**
   * Normalize Booking.com export format
   */
  private normalizeBookingComRecord(record: any): any {
    return {
      startDate: this.parseDate(record['Check-in'] || record.arrival),
      endDate: this.parseDate(record['Check-out'] || record.departure),
      guestName: record['Guest name'] || record.guest_name || 'Booking.com Guest',
      guestPhone: record['Phone'] || record.phone,
      guestCount: parseInt(record['Number of guests'] || record.guests || '2'),
      bookingType: 'Full Day',
      totalAmount: this.parseAmount(record['Total price'] || record.total),
      status: this.normalizeStatus(record['Status'] || record.booking_status),
      externalId: record['Booking ID'] || record.reservation_id,
      notes: record['Special requests'] || record.notes || ''
    };
  }

  /**
   * Normalize VRBO export format
   */
  private normalizeVrboRecord(record: any): any {
    return {
      startDate: this.parseDate(record['Arrival'] || record.checkin),
      endDate: this.parseDate(record['Departure'] || record.checkout),
      guestName: record['Guest Name'] || 'VRBO Guest',
      guestPhone: record['Phone'],
      guestCount: parseInt(record['Guests'] || '2'),
      bookingType: 'Full Day',
      totalAmount: this.parseAmount(record['Total']),
      status: this.normalizeStatus(record['Status']),
      externalId: record['Confirmation'],
      notes: record['Notes'] || ''
    };
  }

  /**
   * Normalize custom/manual format
   */
  private normalizeCustomRecord(record: any): any {
    return {
      startDate: this.parseDate(record.startDate || record.start_date || record.checkin),
      endDate: this.parseDate(record.endDate || record.end_date || record.checkout),
      guestName: record.guestName || record.guest_name || record.name || 'Guest',
      guestPhone: record.guestPhone || record.guest_phone || record.phone,
      guestCount: parseInt(record.guestCount || record.guest_count || record.guests || '2'),
      bookingType: record.bookingType || record.booking_type || 'Full Day',
      totalAmount: this.parseAmount(record.totalAmount || record.total_amount || record.total),
      status: this.normalizeStatus(record.status),
      externalId: record.externalId || record.external_id || record.id,
      notes: record.notes || record.description || ''
    };
  }

  /**
   * Utility methods
   */
  private parseDate(dateStr: any): string {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? '' : date.toISOString().split('T')[0];
  }

  private parseAmount(amountStr: any): number | null {
    if (!amountStr) return null;
    const amount = parseFloat(amountStr.toString().replace(/[^\d.-]/g, ''));
    return isNaN(amount) ? null : amount;
  }

  private normalizeStatus(status: any): string {
    if (!status) return 'confirmed';
    const statusStr = status.toString().toLowerCase();
    
    if (statusStr.includes('confirm')) return 'confirmed';
    if (statusStr.includes('pending')) return 'pending';
    if (statusStr.includes('cancel')) return 'cancelled';
    if (statusStr.includes('block')) return 'blocked';
    
    return 'confirmed';
  }

  /**
   * Validate record data
   */
  private validateRecord(record: any, index: number): { valid: boolean; error?: string } {
    if (!record.startDate) {
      return { valid: false, error: 'Missing start date' };
    }
    
    if (!record.endDate) {
      return { valid: false, error: 'Missing end date' };
    }
    
    if (new Date(record.startDate) > new Date(record.endDate)) {
      return { valid: false, error: 'Start date is after end date' };
    }
    
    if (!record.guestName) {
      return { valid: false, error: 'Missing guest name' };
    }
    
    return { valid: true };
  }

  /**
   * Check for booking conflicts
   */
  private async checkForConflicts(
    propertyId: number,
    startDate: string,
    endDate: string
  ): Promise<any[]> {
    return await db
      .select()
      .from(calendarBookings)
      .where(and(
        eq(calendarBookings.propertyId, propertyId),
        // Check for date overlap
        eq(calendarBookings.startDate, startDate) // Simplified conflict check
      ));
  }

  /**
   * Validate property exists and user has access
   */
  private async validateProperty(propertyId: number): Promise<void> {
    const property = await db
      .select()
      .from(properties)
      .where(eq(properties.id, propertyId))
      .limit(1);

    if (property.length === 0) {
      throw new Error(`Property ${propertyId} not found`);
    }
  }

  /**
   * Generate import template
   */
  generateTemplate(format: 'csv' | 'json', source: string): string {
    const fields = {
      startDate: 'YYYY-MM-DD',
      endDate: 'YYYY-MM-DD',
      guestName: 'Guest Full Name',
      guestPhone: '+1234567890',
      guestCount: '2',
      bookingType: 'Full Day or Morning Visit',
      totalAmount: '150.00',
      status: 'confirmed',
      externalId: 'External Booking ID',
      notes: 'Special requests or notes'
    };

    if (format === 'csv') {
      const headers = Object.keys(fields).join(',');
      const example = Object.values(fields).join(',');
      return `${headers}\n${example}`;
    } else {
      return JSON.stringify([fields], null, 2);
    }
  }
}

// Export singleton instance
export const calendarBulkImportService = new CalendarBulkImportService();