import { logger } from './LoggerService';
import { whatsAppService } from './WhatsAppService';
import { db } from '../db';
import { properties, bookings } from '@shared/schema';
import { eq, and, gte, lte, sql } from 'drizzle-orm';

/**
 * WhatsApp Flow Types
 */
export interface FlowScreen {
  id: string;
  title: string;
  data: Record<string, any>;
  layout: {
    type: 'SingleColumnLayout' | 'DoubleColumnLayout';
    children: FlowComponent[];
  };
}

export interface FlowComponent {
  type: string;
  props: Record<string, any>;
}

export interface FlowDefinition {
  id: string;
  name: string;
  screens: FlowScreen[];
  version: string;
}

export interface FlowState {
  userId: string;
  flowId: string;
  currentScreen: string;
  data: {
    propertyId?: number;
    checkIn?: Date;
    checkOut?: Date;
    guestName?: string;
    guestPhone?: string;
    guestCount?: number;
    specialRequests?: string;
  };
  startedAt: Date;
  lastUpdated: Date;
}

/**
 * WhatsApp Flow Service
 * Manages interactive flows for property booking
 */
export class WhatsAppFlowService {
  private flowStates: Map<string, FlowState> = new Map();
  private flows: Map<string, FlowDefinition> = new Map();

  constructor() {
    this.initializeFlows();
    logger.info('WhatsApp Flow Service initialized', 'whatsapp-flow', {
      totalFlows: this.flows.size
    });
  }

  /**
   * Initialize all flow definitions
   */
  private initializeFlows() {
    // Property Booking Flow - Complete booking experience
    this.flows.set('property_booking', {
      id: 'property_booking',
      name: 'Property Booking Flow',
      version: '1.0.0',
      screens: [
        this.createPropertySelectionScreen(),
        this.createDateSelectionScreen(),
        this.createGuestDetailsScreen(),
        this.createSpecialRequestsScreen(),
        this.createConfirmationScreen()
      ]
    });

    // Date Picker Flow - Quick date selection
    this.flows.set('date_picker', {
      id: 'date_picker',
      name: 'Date Picker Flow',
      version: '1.0.0',
      screens: [
        this.createQuickDateScreen()
      ]
    });

    // Guest Info Flow - Collect guest information
    this.flows.set('guest_info', {
      id: 'guest_info',
      name: 'Guest Information Flow',
      version: '1.0.0',
      screens: [
        this.createGuestFormScreen()
      ]
    });
  }

  /**
   * Create property selection screen
   */
  private createPropertySelectionScreen(): FlowScreen {
    return {
      id: 'property_selection',
      title: '🏡 Select Your Farmhouse',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: 'Available Properties',
              level: 'h2'
            }
          },
          {
            type: 'TextBody',
            props: {
              text: 'Choose from our premium farmhouse collection:'
            }
          },
          {
            type: 'RadioButtonsGroup',
            props: {
              name: 'property_id',
              required: true,
              data_source: [
                {
                  id: '1',
                  title: '🌅 Sunset Villa',
                  description: 'Luxury villa with pool - ₹15,000/day',
                  metadata: {
                    price: 15000,
                    capacity: 10
                  }
                },
                {
                  id: '2',
                  title: '🌿 Green Meadows',
                  description: 'Peaceful retreat - ₹10,000/day',
                  metadata: {
                    price: 10000,
                    capacity: 8
                  }
                },
                {
                  id: '3',
                  title: '🏰 Heritage Manor',
                  description: 'Historic farmhouse - ₹20,000/day',
                  metadata: {
                    price: 20000,
                    capacity: 15
                  }
                },
                {
                  id: '4',
                  title: '🌻 Sunflower Ranch',
                  description: 'Family-friendly farm - ₹12,000/day',
                  metadata: {
                    price: 12000,
                    capacity: 12
                  }
                }
              ]
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Continue',
              on_click_action: {
                name: 'navigate',
                next: {
                  type: 'screen',
                  name: 'date_selection'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Create date selection screen
   */
  private createDateSelectionScreen(): FlowScreen {
    return {
      id: 'date_selection',
      title: '📅 Select Your Dates',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: 'Choose Your Stay Dates',
              level: 'h2'
            }
          },
          {
            type: 'DatePicker',
            props: {
              name: 'check_in',
              label: 'Check-in Date',
              required: true,
              min_date: new Date().toISOString().split('T')[0],
              helper_text: 'Select your arrival date'
            }
          },
          {
            type: 'DatePicker',
            props: {
              name: 'check_out',
              label: 'Check-out Date',
              required: true,
              min_date: new Date().toISOString().split('T')[0],
              helper_text: 'Select your departure date'
            }
          },
          {
            type: 'Dropdown',
            props: {
              name: 'stay_type',
              label: 'Stay Type',
              required: true,
              data_source: [
                { id: 'day', title: 'Day Visit (9 AM - 6 PM)' },
                { id: 'night', title: 'Night Stay (Check-in 2 PM)' },
                { id: 'extended', title: 'Extended Stay (2+ nights)' }
              ]
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Continue',
              on_click_action: {
                name: 'navigate',
                next: {
                  type: 'screen',
                  name: 'guest_details'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Create guest details screen
   */
  private createGuestDetailsScreen(): FlowScreen {
    return {
      id: 'guest_details',
      title: '👥 Guest Information',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: 'Tell Us About Your Group',
              level: 'h2'
            }
          },
          {
            type: 'TextInput',
            props: {
              name: 'guest_name',
              label: 'Primary Guest Name',
              required: true,
              input_type: 'text',
              helper_text: 'Enter your full name'
            }
          },
          {
            type: 'TextInput',
            props: {
              name: 'guest_phone',
              label: 'Contact Number',
              required: true,
              input_type: 'phone',
              helper_text: 'We\'ll use this for booking confirmation'
            }
          },
          {
            type: 'TextInput',
            props: {
              name: 'guest_email',
              label: 'Email Address',
              required: false,
              input_type: 'email',
              helper_text: 'Optional - for booking details'
            }
          },
          {
            type: 'Dropdown',
            props: {
              name: 'guest_count',
              label: 'Number of Guests',
              required: true,
              data_source: Array.from({ length: 20 }, (_, i) => ({
                id: String(i + 1),
                title: `${i + 1} ${i === 0 ? 'Guest' : 'Guests'}`
              }))
            }
          },
          {
            type: 'RadioButtonsGroup',
            props: {
              name: 'purpose',
              label: 'Purpose of Visit',
              required: true,
              data_source: [
                { id: 'family', title: '👨‍👩‍👧‍👦 Family Gathering' },
                { id: 'friends', title: '👫 Friends Reunion' },
                { id: 'corporate', title: '💼 Corporate Event' },
                { id: 'celebration', title: '🎉 Celebration/Party' },
                { id: 'retreat', title: '🧘 Retreat/Relaxation' }
              ]
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Continue',
              on_click_action: {
                name: 'navigate',
                next: {
                  type: 'screen',
                  name: 'special_requests'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Create special requests screen
   */
  private createSpecialRequestsScreen(): FlowScreen {
    return {
      id: 'special_requests',
      title: '✨ Special Requests',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: 'Any Special Requirements?',
              level: 'h2'
            }
          },
          {
            type: 'CheckboxGroup',
            props: {
              name: 'amenities',
              label: 'Additional Amenities',
              data_source: [
                { id: 'bonfire', title: '🔥 Bonfire Setup' },
                { id: 'bbq', title: '🍖 BBQ Equipment' },
                { id: 'decoration', title: '🎊 Event Decoration' },
                { id: 'catering', title: '🍽️ Catering Service' },
                { id: 'music', title: '🎵 Music System' }
              ]
            }
          },
          {
            type: 'TextArea',
            props: {
              name: 'special_requests',
              label: 'Additional Notes',
              required: false,
              helper_text: 'Let us know if you have any special requirements'
            }
          },
          {
            type: 'OptIn',
            props: {
              name: 'terms_accepted',
              label: 'I agree to the terms and conditions',
              required: true
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Review Booking',
              on_click_action: {
                name: 'navigate',
                next: {
                  type: 'screen',
                  name: 'confirmation'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Create confirmation screen
   */
  private createConfirmationScreen(): FlowScreen {
    return {
      id: 'confirmation',
      title: '✅ Booking Confirmation',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: '🎉 Booking Summary',
              level: 'h2'
            }
          },
          {
            type: 'TextBody',
            props: {
              text: 'Please review your booking details:'
            }
          },
          {
            type: 'DataTable',
            props: {
              data_source: '${form}' // This would be populated with form data
            }
          },
          {
            type: 'TextSubheading',
            props: {
              text: 'Total Amount: ₹${calculated_total}'
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Confirm Booking',
              on_click_action: {
                name: 'complete_flow',
                payload: {
                  action: 'create_booking'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Create quick date picker screen
   */
  private createQuickDateScreen(): FlowScreen {
    return {
      id: 'quick_date',
      title: '📅 Quick Date Selection',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: 'When would you like to visit?',
              level: 'h2'
            }
          },
          {
            type: 'RadioButtonsGroup',
            props: {
              name: 'quick_date',
              required: true,
              data_source: [
                { id: 'today', title: '📅 Today' },
                { id: 'tomorrow', title: '📅 Tomorrow' },
                { id: 'weekend', title: '📅 This Weekend' },
                { id: 'next_weekend', title: '📅 Next Weekend' },
                { id: 'custom', title: '📅 Choose Specific Dates' }
              ]
            }
          },
          {
            type: 'Dropdown',
            props: {
              name: 'guest_count',
              label: 'Number of Guests',
              required: true,
              data_source: Array.from({ length: 20 }, (_, i) => ({
                id: String(i + 1),
                title: `${i + 1} ${i === 0 ? 'Guest' : 'Guests'}`
              }))
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Check Availability',
              on_click_action: {
                name: 'complete_flow',
                payload: {
                  action: 'check_availability'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Create guest information form screen
   */
  private createGuestFormScreen(): FlowScreen {
    return {
      id: 'guest_form',
      title: '👤 Guest Registration',
      data: {},
      layout: {
        type: 'SingleColumnLayout',
        children: [
          {
            type: 'TextHeading',
            props: {
              text: 'Guest Information',
              level: 'h2'
            }
          },
          {
            type: 'Form',
            props: {
              name: 'guest_form',
              children: [
                {
                  type: 'TextInput',
                  props: {
                    name: 'full_name',
                    label: 'Full Name',
                    required: true,
                    input_type: 'text'
                  }
                },
                {
                  type: 'TextInput',
                  props: {
                    name: 'phone',
                    label: 'Phone Number',
                    required: true,
                    input_type: 'phone'
                  }
                },
                {
                  type: 'TextInput',
                  props: {
                    name: 'email',
                    label: 'Email',
                    required: false,
                    input_type: 'email'
                  }
                },
                {
                  type: 'TextInput',
                  props: {
                    name: 'id_number',
                    label: 'ID Number (Aadhaar/Passport)',
                    required: true,
                    input_type: 'text'
                  }
                },
                {
                  type: 'Dropdown',
                  props: {
                    name: 'id_type',
                    label: 'ID Type',
                    required: true,
                    data_source: [
                      { id: 'aadhaar', title: 'Aadhaar Card' },
                      { id: 'passport', title: 'Passport' },
                      { id: 'driving', title: 'Driving License' },
                      { id: 'pan', title: 'PAN Card' }
                    ]
                  }
                }
              ]
            }
          },
          {
            type: 'Footer',
            props: {
              label: 'Submit',
              on_click_action: {
                name: 'complete_flow',
                payload: {
                  action: 'save_guest_info'
                }
              }
            }
          }
        ]
      }
    };
  }

  /**
   * Start a new flow for a user
   */
  public async startFlow(userId: string, flowId: string): Promise<FlowScreen | null> {
    const flow = this.flows.get(flowId);
    
    if (!flow) {
      logger.warn('Flow not found', 'whatsapp-flow', { flowId });
      return null;
    }

    // Initialize flow state
    const state: FlowState = {
      userId,
      flowId,
      currentScreen: flow.screens[0].id,
      data: {},
      startedAt: new Date(),
      lastUpdated: new Date()
    };

    this.flowStates.set(userId, state);

    logger.info('Started flow for user', 'whatsapp-flow', {
      userId,
      flowId,
      screen: state.currentScreen
    });

    return flow.screens[0];
  }

  /**
   * Process flow response from user
   */
  public async processFlowResponse(
    userId: string,
    screenId: string,
    data: Record<string, any>
  ): Promise<{ nextScreen?: FlowScreen; result?: any }> {
    const state = this.flowStates.get(userId);
    
    if (!state) {
      logger.warn('No active flow state for user', 'whatsapp-flow', { userId });
      return {};
    }

    const flow = this.flows.get(state.flowId);
    if (!flow) {
      return {};
    }

    // Update state with new data
    state.data = { ...state.data, ...data };
    state.lastUpdated = new Date();

    // Find next screen
    const currentIndex = flow.screens.findIndex(s => s.id === screenId);
    
    if (currentIndex === -1) {
      logger.warn('Screen not found in flow', 'whatsapp-flow', { screenId, flowId: state.flowId });
      return {};
    }

    // Check if this is the last screen
    if (currentIndex === flow.screens.length - 1) {
      // Flow completed
      const result = await this.completeFlow(userId, state);
      this.flowStates.delete(userId);
      return { result };
    }

    // Move to next screen
    const nextScreen = flow.screens[currentIndex + 1];
    state.currentScreen = nextScreen.id;
    
    logger.info('Flow progressed', 'whatsapp-flow', {
      userId,
      flowId: state.flowId,
      from: screenId,
      to: nextScreen.id
    });

    return { nextScreen };
  }

  /**
   * Complete a flow and process the final action
   */
  private async completeFlow(userId: string, state: FlowState): Promise<any> {
    logger.info('Completing flow', 'whatsapp-flow', {
      userId,
      flowId: state.flowId,
      data: state.data
    });

    switch (state.flowId) {
      case 'property_booking':
        return await this.createBooking(userId, state.data);
      
      case 'date_picker':
        return await this.checkAvailability(state.data);
      
      case 'guest_info':
        return await this.saveGuestInfo(userId, state.data);
      
      default:
        return { success: true, message: 'Flow completed' };
    }
  }

  /**
   * Create a booking from flow data
   */
  private async createBooking(userId: string, data: any): Promise<any> {
    try {
      // Generate booking reference
      const bookingRef = `WA${Date.now()}`;
      
      // Create booking in database (simplified for demo)
      const booking = {
        propertyId: parseInt(data.property_id),
        guestName: data.guest_name,
        guestPhone: data.guest_phone,
        guestCount: parseInt(data.guest_count),
        checkIn: new Date(data.check_in),
        checkOut: new Date(data.check_out),
        specialRequests: data.special_requests,
        bookingRef,
        createdAt: new Date()
      };

      logger.info('Booking created from WhatsApp flow', 'whatsapp-flow', {
        bookingRef,
        userId
      });

      // Send confirmation message
      const message = `🎉 Booking Confirmed!\n\n` +
        `Reference: ${bookingRef}\n` +
        `Property: ${data.property_id}\n` +
        `Check-in: ${data.check_in}\n` +
        `Check-out: ${data.check_out}\n` +
        `Guests: ${data.guest_count}\n\n` +
        `We'll send you payment details shortly. Thank you for choosing us!`;

      await whatsAppService.sendMessage(userId, message);

      return {
        success: true,
        bookingRef,
        message: 'Booking created successfully'
      };
    } catch (error) {
      logger.error('Error creating booking from flow', error as Error);
      return {
        success: false,
        message: 'Failed to create booking'
      };
    }
  }

  /**
   * Check availability for dates
   */
  private async checkAvailability(data: any): Promise<any> {
    try {
      // Convert quick date selection to actual dates
      let checkIn: Date;
      let checkOut: Date;
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      switch (data.quick_date) {
        case 'today':
          checkIn = today;
          checkOut = new Date(today);
          checkOut.setDate(checkOut.getDate() + 1);
          break;
        
        case 'tomorrow':
          checkIn = new Date(today);
          checkIn.setDate(checkIn.getDate() + 1);
          checkOut = new Date(checkIn);
          checkOut.setDate(checkOut.getDate() + 1);
          break;
        
        case 'weekend':
          // Get next Saturday
          checkIn = new Date(today);
          checkIn.setDate(today.getDate() + (6 - today.getDay()));
          checkOut = new Date(checkIn);
          checkOut.setDate(checkOut.getDate() + 2);
          break;
        
        default:
          checkIn = new Date(data.check_in || today);
          checkOut = new Date(data.check_out || today);
      }

      // Query available properties
      const availableProperties = await db.select()
        .from(properties)
        .where(
          and(
            eq(properties.status, 'active'),
            gte(properties.maxGuests, parseInt(data.guest_count || '1'))
          )
        )
        .limit(5);

      const message = `📅 Available Properties\n` +
        `Dates: ${checkIn.toDateString()} - ${checkOut.toDateString()}\n` +
        `Guests: ${data.guest_count || 1}\n\n` +
        availableProperties.map((p, i) => 
          `${i + 1}. ${p.name} - ₹${p.pricePerDay}/day`
        ).join('\n');

      return {
        success: true,
        availableProperties,
        message
      };
    } catch (error) {
      logger.error('Error checking availability', error as Error);
      return {
        success: false,
        message: 'Failed to check availability'
      };
    }
  }

  /**
   * Save guest information
   */
  private async saveGuestInfo(userId: string, data: any): Promise<any> {
    try {
      logger.info('Saving guest information', 'whatsapp-flow', {
        userId,
        guestName: data.full_name
      });

      // In a real implementation, this would save to database
      const guestId = `GUEST${Date.now()}`;

      return {
        success: true,
        guestId,
        message: 'Guest information saved successfully'
      };
    } catch (error) {
      logger.error('Error saving guest info', error as Error);
      return {
        success: false,
        message: 'Failed to save guest information'
      };
    }
  }

  /**
   * Get flow definition for external use
   */
  public getFlow(flowId: string): FlowDefinition | undefined {
    return this.flows.get(flowId);
  }

  /**
   * Get all available flows
   */
  public getAllFlows(): FlowDefinition[] {
    return Array.from(this.flows.values());
  }

  /**
   * Get user's current flow state
   */
  public getUserFlowState(userId: string): FlowState | undefined {
    return this.flowStates.get(userId);
  }

  /**
   * Cancel user's active flow
   */
  public cancelFlow(userId: string): boolean {
    return this.flowStates.delete(userId);
  }
}

// Export singleton instance
export const whatsAppFlowService = new WhatsAppFlowService();