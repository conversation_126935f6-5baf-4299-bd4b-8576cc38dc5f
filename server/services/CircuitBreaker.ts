import { logger } from './LoggerService';

/**
 * Circuit breaker states
 */
enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Failures detected, circuit is open
  HALF_OPEN = 'HALF_OPEN' // Testing if service has recovered
}

/**
 * Circuit breaker configuration
 */
interface CircuitBreakerConfig {
  failureThreshold: number;      // Number of failures before opening circuit
  recoveryTimeout: number;       // Time to wait before attempting recovery (ms)
  timeout: number;               // Timeout for service calls (ms)
  monitoringPeriod: number;      // Time window for monitoring failures (ms)
}

/**
 * Circuit breaker options for execution
 */
interface ExecutionOptions<T> {
  fallback?: () => T | Promise<T>;
  timeout?: number;
}

/**
 * Circuit breaker implementation for handling service failures
 * Prevents cascading failures by failing fast when a service is unavailable
 */
export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private nextAttemptTime: number = 0;
  private readonly config: CircuitBreakerConfig;
  private readonly name: string;

  constructor(name: string, config: Partial<CircuitBreakerConfig> = {}) {
    this.name = name;
    this.config = {
      failureThreshold: config.failureThreshold || 5,
      recoveryTimeout: config.recoveryTimeout || 60000, // 1 minute
      timeout: config.timeout || 5000, // 5 seconds
      monitoringPeriod: config.monitoringPeriod || 300000, // 5 minutes
      ...config
    };

    logger.info(`Circuit breaker initialized: ${name}`, 'circuit-breaker', {
      config: this.config
    });
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(
    fn: () => Promise<T>,
    options: ExecutionOptions<T> = {}
  ): Promise<T> {
    const startTime = Date.now();

    try {
      // Check if circuit is open
      if (this.state === CircuitState.OPEN) {
        if (Date.now() < this.nextAttemptTime) {
          logger.warn(`Circuit breaker ${this.name} is OPEN - failing fast`, 'circuit-breaker', {
            state: this.state,
            failureCount: this.failureCount,
            nextAttemptTime: new Date(this.nextAttemptTime).toISOString()
          });
          
          if (options.fallback) {
            return await options.fallback();
          }
          throw new Error(`Circuit breaker ${this.name} is OPEN`);
        } else {
          // Transition to HALF_OPEN to test recovery
          this.state = CircuitState.HALF_OPEN;
          logger.info(`Circuit breaker ${this.name} transitioning to HALF_OPEN`, 'circuit-breaker');
        }
      }

      // Execute the function with timeout
      const timeout = options.timeout || this.config.timeout;
      const result = await this.executeWithTimeout(fn, timeout);

      // Success - reset failure count and close circuit if necessary
      this.onSuccess();
      
      const duration = Date.now() - startTime;
      logger.debug(`Circuit breaker ${this.name} - successful execution`, 'circuit-breaker', {
        duration,
        state: this.state
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Circuit breaker ${this.name} - execution failed`, error instanceof Error ? error : undefined, 'circuit-breaker', {
        duration,
        state: this.state,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Handle failure
      this.onFailure();

      // Use fallback if available
      if (options.fallback) {
        logger.info(`Circuit breaker ${this.name} - using fallback`, 'circuit-breaker');
        return await options.fallback();
      }

      throw error;
    }
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(fn: () => Promise<T>, timeout: number): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Circuit breaker ${this.name} - operation timed out after ${timeout}ms`));
      }, timeout);

      fn()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Handle successful execution
   */
  private onSuccess(): void {
    // Reset failure count
    this.failureCount = 0;
    
    // Close circuit if it was half-open
    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.CLOSED;
      logger.info(`Circuit breaker ${this.name} - recovered and closed`, 'circuit-breaker');
    }
  }

  /**
   * Handle failed execution
   */
  private onFailure(): void {
    const now = Date.now();
    this.failureCount++;
    this.lastFailureTime = now;

    // Reset failure count if outside monitoring period
    if (now - this.lastFailureTime > this.config.monitoringPeriod) {
      this.failureCount = 1;
    }

    // Open circuit if failure threshold exceeded
    if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = now + this.config.recoveryTimeout;
      
      logger.error(`Circuit breaker ${this.name} - OPENED due to failures`, undefined, 'circuit-breaker', {
        failureCount: this.failureCount,
        threshold: this.config.failureThreshold,
        nextAttemptTime: new Date(this.nextAttemptTime).toISOString()
      });
    }
  }

  /**
   * Get current circuit breaker status
   */
  getStatus(): {
    name: string;
    state: CircuitState;
    failureCount: number;
    config: CircuitBreakerConfig;
    nextAttemptTime?: string | undefined;
  } {
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      config: this.config,
      nextAttemptTime: this.nextAttemptTime > 0 ? new Date(this.nextAttemptTime).toISOString() : undefined
    };
  }

  /**
   * Manually reset the circuit breaker
   */
  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.nextAttemptTime = 0;
    
    logger.info(`Circuit breaker ${this.name} - manually reset`, 'circuit-breaker');
  }

  /**
   * Force open the circuit breaker (for maintenance)
   */
  forceOpen(): void {
    this.state = CircuitState.OPEN;
    this.nextAttemptTime = Date.now() + this.config.recoveryTimeout;
    
    logger.warn(`Circuit breaker ${this.name} - manually opened`, 'circuit-breaker');
  }
}

/**
 * Circuit breaker registry for managing multiple circuit breakers
 */
export class CircuitBreakerRegistry {
  private static breakers: Map<string, CircuitBreaker> = new Map();

  /**
   * Get or create a circuit breaker
   */
  static getOrCreate(name: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (!this.breakers.has(name)) {
      this.breakers.set(name, new CircuitBreaker(name, config));
    }
    return this.breakers.get(name)!;
  }

  /**
   * Get all circuit breaker statuses
   */
  static getAllStatuses(): Array<ReturnType<CircuitBreaker['getStatus']>> {
    return Array.from(this.breakers.values()).map(breaker => breaker.getStatus());
  }

  /**
   * Reset all circuit breakers
   */
  static resetAll(): void {
    this.breakers.forEach(breaker => breaker.reset());
    logger.info('All circuit breakers reset', 'circuit-breaker');
  }

  /**
   * Get circuit breaker by name
   */
  static get(name: string): CircuitBreaker | undefined {
    return this.breakers.get(name);
  }
}

// Pre-configured circuit breakers for common services
export const TokenBlacklistCircuitBreaker = CircuitBreakerRegistry.getOrCreate('token-blacklist', {
  failureThreshold: 3,        // Fail fast for security
  recoveryTimeout: 30000,     // 30 seconds recovery
  timeout: 2000,              // 2 second timeout
  monitoringPeriod: 60000     // 1 minute monitoring window
});

export const DatabaseCircuitBreaker = CircuitBreakerRegistry.getOrCreate('database', {
  failureThreshold: 5,
  recoveryTimeout: 60000,     // 1 minute recovery
  timeout: 5000,              // 5 second timeout
  monitoringPeriod: 300000    // 5 minute monitoring window
});