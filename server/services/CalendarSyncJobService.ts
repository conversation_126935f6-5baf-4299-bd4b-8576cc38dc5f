import { logger } from './LoggerService';
import { db } from '../db';
import { 
  calendarSyncStatus,
  properties,
  users,
  CalendarSyncStatus
} from '@shared/schema';
import { eq, and, lt, lte } from 'drizzle-orm';
import { calendarSyncService } from './CalendarSyncService';
import { EventEmitter } from 'events';
import cron from 'node-cron';

export interface SyncJobConfig {
  id: string;
  propertyId: number;
  provider: 'google' | 'outlook' | 'ical';
  credentials?: any;
  calendarId?: string;
  schedule: string; // Cron expression
  enabled: boolean;
  syncDirection: 'import' | 'export' | 'bidirectional';
  retryCount: number;
  maxRetries: number;
  lastRun?: Date;
  nextRun?: Date;
  status: 'idle' | 'running' | 'failed' | 'disabled';
}

export interface SyncJobResult {
  jobId: string;
  success: boolean;
  startTime: Date;
  endTime: Date;
  imported: number;
  exported: number;
  conflicts: number;
  errors: string[];
}

export interface SyncJobStats {
  totalJobs: number;
  activeJobs: number;
  successfulRuns: number;
  failedRuns: number;
  averageRunTime: number;
  nextScheduledRun?: Date;
}

export class CalendarSyncJobService extends EventEmitter {
  private syncJobs: Map<string, SyncJobConfig> = new Map();
  private cronJobs: Map<string, cron.ScheduledTask> = new Map();
  private runningJobs: Set<string> = new Set();
  private jobStats: Map<string, SyncJobResult[]> = new Map();

  constructor() {
    super();
    logger.info('Calendar Sync Job Service initialized');
    
    // Load existing sync jobs from database on startup
    this.loadSyncJobsFromDatabase();
    
    // Set up monitoring job that runs every hour
    this.setupMonitoringJob();
  }

  /**
   * Create a new sync job
   */
  async createSyncJob(
    propertyId: number,
    provider: 'google' | 'outlook' | 'ical',
    config: {
      calendarId?: string;
      credentials?: any;
      schedule: string;
      syncDirection?: 'import' | 'export' | 'bidirectional';
      enabled?: boolean;
    }
  ): Promise<SyncJobConfig> {
    try {
      // Validate property exists
      const property = await db
        .select()
        .from(properties)
        .where(eq(properties.id, propertyId))
        .limit(1);

      if (property.length === 0) {
        throw new Error(`Property ${propertyId} not found`);
      }

      // Validate cron expression
      if (!cron.validate(config.schedule)) {
        throw new Error(`Invalid cron expression: ${config.schedule}`);
      }

      const jobId = `sync_${propertyId}_${provider}_${Date.now()}`;

      const syncJob: SyncJobConfig = {
        id: jobId,
        propertyId,
        provider,
        credentials: config.credentials,
        calendarId: config.calendarId,
        schedule: config.schedule,
        enabled: config.enabled !== false,
        syncDirection: config.syncDirection || 'bidirectional',
        retryCount: 0,
        maxRetries: 3,
        status: 'idle',
        nextRun: this.calculateNextRun(config.schedule)
      };

      // Store job configuration
      this.syncJobs.set(jobId, syncJob);
      this.jobStats.set(jobId, []);

      // Schedule the job if enabled
      if (syncJob.enabled) {
        this.scheduleJob(syncJob);
      }

      // Persist to database
      await this.saveSyncJobToDatabase(syncJob);

      logger.info('Sync job created', {
        jobId,
        propertyId,
        provider,
        schedule: config.schedule,
        enabled: syncJob.enabled
      });

      this.emit('jobCreated', { job: syncJob });

      return syncJob;
    } catch (error) {
      logger.error('Failed to create sync job', error as Error);
      throw error;
    }
  }

  /**
   * Update sync job configuration
   */
  async updateSyncJob(
    jobId: string,
    updates: Partial<Omit<SyncJobConfig, 'id'>>
  ): Promise<SyncJobConfig> {
    const job = this.syncJobs.get(jobId);
    if (!job) {
      throw new Error(`Sync job ${jobId} not found`);
    }

    // Validate schedule if being updated
    if (updates.schedule && !cron.validate(updates.schedule)) {
      throw new Error(`Invalid cron expression: ${updates.schedule}`);
    }

    // Update job configuration
    const updatedJob = { ...job, ...updates };
    
    if (updates.schedule) {
      updatedJob.nextRun = this.calculateNextRun(updates.schedule);
    }

    this.syncJobs.set(jobId, updatedJob);

    // Reschedule if schedule or enabled status changed
    if (updates.schedule || updates.enabled !== undefined) {
      this.unscheduleJob(jobId);
      if (updatedJob.enabled) {
        this.scheduleJob(updatedJob);
      }
    }

    // Update in database
    await this.saveSyncJobToDatabase(updatedJob);

    logger.info('Sync job updated', { jobId, updates });

    this.emit('jobUpdated', { job: updatedJob });

    return updatedJob;
  }

  /**
   * Delete sync job
   */
  async deleteSyncJob(jobId: string): Promise<void> {
    const job = this.syncJobs.get(jobId);
    if (!job) {
      throw new Error(`Sync job ${jobId} not found`);
    }

    // Unschedule the job
    this.unscheduleJob(jobId);

    // Remove from memory
    this.syncJobs.delete(jobId);
    this.jobStats.delete(jobId);

    // Remove from database
    await this.deleteSyncJobFromDatabase(jobId);

    logger.info('Sync job deleted', { jobId });

    this.emit('jobDeleted', { jobId });
  }

  /**
   * Enable/disable sync job
   */
  async toggleSyncJob(jobId: string, enabled: boolean): Promise<SyncJobConfig> {
    return await this.updateSyncJob(jobId, { enabled });
  }

  /**
   * Manually trigger sync job
   */
  async triggerSyncJob(jobId: string): Promise<SyncJobResult> {
    const job = this.syncJobs.get(jobId);
    if (!job) {
      throw new Error(`Sync job ${jobId} not found`);
    }

    if (this.runningJobs.has(jobId)) {
      throw new Error(`Sync job ${jobId} is already running`);
    }

    logger.info('Manually triggering sync job', { jobId });

    return await this.executeSyncJob(job);
  }

  /**
   * Get sync job status
   */
  getSyncJob(jobId: string): SyncJobConfig | undefined {
    return this.syncJobs.get(jobId);
  }

  /**
   * Get all sync jobs
   */
  getAllSyncJobs(): SyncJobConfig[] {
    return Array.from(this.syncJobs.values());
  }

  /**
   * Get sync jobs for property
   */
  getSyncJobsForProperty(propertyId: number): SyncJobConfig[] {
    return Array.from(this.syncJobs.values())
      .filter(job => job.propertyId === propertyId);
  }

  /**
   * Get sync job statistics
   */
  getSyncJobStats(): SyncJobStats {
    const jobs = Array.from(this.syncJobs.values());
    const activeJobs = jobs.filter(job => job.enabled).length;
    
    let totalRuns = 0;
    let successfulRuns = 0;
    let failedRuns = 0;
    let totalRunTime = 0;

    for (const results of this.jobStats.values()) {
      totalRuns += results.length;
      successfulRuns += results.filter(r => r.success).length;
      failedRuns += results.filter(r => !r.success).length;
      totalRunTime += results.reduce((sum, r) => 
        sum + (r.endTime.getTime() - r.startTime.getTime()), 0
      );
    }

    const averageRunTime = totalRuns > 0 ? totalRunTime / totalRuns : 0;

    // Find next scheduled run
    const nextScheduledRun = jobs
      .filter(job => job.enabled && job.nextRun)
      .reduce((earliest, job) => {
        if (!earliest || (job.nextRun && job.nextRun < earliest)) {
          return job.nextRun!;
        }
        return earliest;
      }, null as Date | null);

    return {
      totalJobs: jobs.length,
      activeJobs,
      successfulRuns,
      failedRuns,
      averageRunTime,
      nextScheduledRun: nextScheduledRun || undefined
    };
  }

  /**
   * Get job execution history
   */
  getJobHistory(jobId: string, limit: number = 10): SyncJobResult[] {
    const history = this.jobStats.get(jobId) || [];
    return history
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * Private methods
   */
  private scheduleJob(job: SyncJobConfig): void {
    try {
      const cronJob = cron.schedule(job.schedule, async () => {
        await this.executeSyncJob(job);
      }, {
        scheduled: false,
        timezone: 'UTC'
      });

      cronJob.start();
      this.cronJobs.set(job.id, cronJob);

      logger.info('Sync job scheduled', {
        jobId: job.id,
        schedule: job.schedule,
        nextRun: job.nextRun
      });
    } catch (error) {
      logger.error('Failed to schedule sync job', error as Error, { jobId: job.id });
    }
  }

  private unscheduleJob(jobId: string): void {
    const cronJob = this.cronJobs.get(jobId);
    if (cronJob) {
      cronJob.destroy();
      this.cronJobs.delete(jobId);
      logger.info('Sync job unscheduled', { jobId });
    }
  }

  private async executeSyncJob(job: SyncJobConfig): Promise<SyncJobResult> {
    const startTime = new Date();
    const jobResult: SyncJobResult = {
      jobId: job.id,
      success: false,
      startTime,
      endTime: startTime,
      imported: 0,
      exported: 0,
      conflicts: 0,
      errors: []
    };

    try {
      if (this.runningJobs.has(job.id)) {
        throw new Error(`Job ${job.id} is already running`);
      }

      this.runningJobs.add(job.id);
      
      // Update job status
      job.status = 'running';
      job.lastRun = startTime;
      job.retryCount = 0;
      
      logger.info('Executing sync job', { jobId: job.id, propertyId: job.propertyId });

      this.emit('jobStarted', { job });

      // Execute the sync based on provider and direction
      const syncResult = await calendarSyncService.syncCalendar(
        job.propertyId,
        {
          provider: job.provider,
          credentials: job.credentials,
          calendarId: job.calendarId
        }
      );

      // Update job result
      jobResult.success = syncResult.success;
      jobResult.imported = syncResult.imported;
      jobResult.exported = syncResult.exported;
      jobResult.conflicts = syncResult.conflicts.length;
      jobResult.errors = syncResult.errors;
      jobResult.endTime = new Date();

      if (syncResult.success) {
        job.status = 'idle';
        job.retryCount = 0;
        logger.info('Sync job completed successfully', {
          jobId: job.id,
          imported: syncResult.imported,
          exported: syncResult.exported,
          conflicts: syncResult.conflicts.length
        });
      } else {
        throw new Error(`Sync failed: ${syncResult.errors.join(', ')}`);
      }

    } catch (error) {
      jobResult.success = false;
      jobResult.errors.push((error as Error).message);
      jobResult.endTime = new Date();

      job.status = 'failed';
      job.retryCount++;

      logger.error('Sync job failed', error as Error, {
        jobId: job.id,
        retryCount: job.retryCount,
        maxRetries: job.maxRetries
      });

      // Schedule retry if within retry limit
      if (job.retryCount < job.maxRetries && job.enabled) {
        setTimeout(() => {
          this.executeSyncJob(job);
        }, Math.min(1000 * 60 * job.retryCount, 1000 * 60 * 30)); // Exponential backoff, max 30 minutes
      }
    } finally {
      this.runningJobs.delete(job.id);
      
      // Update next run time
      job.nextRun = this.calculateNextRun(job.schedule);
      
      // Store job result
      const history = this.jobStats.get(job.id) || [];
      history.push(jobResult);
      
      // Keep only last 50 results per job
      if (history.length > 50) {
        history.splice(0, history.length - 50);
      }
      
      this.jobStats.set(job.id, history);

      // Persist job state
      await this.saveSyncJobToDatabase(job);

      this.emit('jobCompleted', { job, result: jobResult });
    }

    return jobResult;
  }

  private calculateNextRun(cronExpression: string): Date {
    try {
      const task = cron.schedule(cronExpression, () => {}, { scheduled: false });
      // This is a simplified calculation - in production you'd use a proper cron parser
      return new Date(Date.now() + 24 * 60 * 60 * 1000); // Default to 24 hours from now
    } catch {
      return new Date(Date.now() + 24 * 60 * 60 * 1000);
    }
  }

  private async loadSyncJobsFromDatabase(): Promise<void> {
    try {
      // In a real implementation, you'd load sync job configurations from database
      logger.info('Loading sync jobs from database');
      
      const syncStatuses = await db
        .select()
        .from(calendarSyncStatus)
        .where(eq(calendarSyncStatus.status, 'active'));

      // Convert sync statuses to job configs (simplified)
      for (const status of syncStatuses) {
        // This is simplified - you'd have a separate table for job configs
        const jobConfig: SyncJobConfig = {
          id: `existing_${status.id}`,
          propertyId: status.propertyId,
          provider: status.provider as any,
          schedule: '0 */6 * * *', // Default: every 6 hours
          enabled: true,
          syncDirection: 'bidirectional',
          retryCount: 0,
          maxRetries: 3,
          status: 'idle'
        };

        this.syncJobs.set(jobConfig.id, jobConfig);
        this.jobStats.set(jobConfig.id, []);

        if (jobConfig.enabled) {
          this.scheduleJob(jobConfig);
        }
      }

      logger.info('Loaded sync jobs from database', { count: syncStatuses.length });
    } catch (error) {
      logger.error('Failed to load sync jobs from database', error as Error);
    }
  }

  private async saveSyncJobToDatabase(job: SyncJobConfig): Promise<void> {
    try {
      // In a real implementation, you'd save to a sync_jobs table
      logger.debug('Saving sync job to database', { jobId: job.id });
      
      // Update or create sync status record
      const existing = await db
        .select()
        .from(calendarSyncStatus)
        .where(and(
          eq(calendarSyncStatus.propertyId, job.propertyId),
          eq(calendarSyncStatus.provider, job.provider)
        ))
        .limit(1);

      const statusData = {
        propertyId: job.propertyId,
        provider: job.provider,
        status: job.enabled ? 'active' : 'disabled',
        lastSync: job.lastRun,
        syncToken: job.calendarId,
        updatedAt: new Date()
      };

      if (existing.length > 0) {
        await db
          .update(calendarSyncStatus)
          .set(statusData)
          .where(eq(calendarSyncStatus.id, existing[0].id));
      } else {
        await db.insert(calendarSyncStatus).values({
          ...statusData,
          conflictCount: 0
        });
      }
    } catch (error) {
      logger.error('Failed to save sync job to database', error as Error);
    }
  }

  private async deleteSyncJobFromDatabase(jobId: string): Promise<void> {
    try {
      // In a real implementation, you'd delete from sync_jobs table
      logger.debug('Deleting sync job from database', { jobId });
    } catch (error) {
      logger.error('Failed to delete sync job from database', error as Error);
    }
  }

  private setupMonitoringJob(): void {
    // Run monitoring every hour
    cron.schedule('0 * * * *', async () => {
      await this.monitorSyncJobs();
    });

    logger.info('Sync job monitoring scheduled');
  }

  private async monitorSyncJobs(): Promise<void> {
    try {
      const now = new Date();
      const stats = this.getSyncJobStats();
      
      logger.info('Sync job monitoring check', {
        totalJobs: stats.totalJobs,
        activeJobs: stats.activeJobs,
        runningJobs: this.runningJobs.size
      });

      // Check for stuck jobs
      for (const [jobId, job] of this.syncJobs.entries()) {
        if (job.status === 'running' && this.runningJobs.has(jobId)) {
          const history = this.jobStats.get(jobId) || [];
          const lastRun = history[history.length - 1];
          
          if (lastRun && now.getTime() - lastRun.startTime.getTime() > 60 * 60 * 1000) {
            // Job has been running for more than 1 hour - consider it stuck
            logger.warn('Detected stuck sync job', { jobId });
            this.runningJobs.delete(jobId);
            job.status = 'failed';
            job.retryCount++;
          }
        }
      }

      // Emit monitoring stats
      this.emit('monitoringCheck', { stats, timestamp: now });
    } catch (error) {
      logger.error('Error during sync job monitoring', error as Error);
    }
  }
}

// Export singleton instance
export const calendarSyncJobService = new CalendarSyncJobService();