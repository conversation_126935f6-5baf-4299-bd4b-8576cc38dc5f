import { db } from '../db';
import { CacheService, cacheService } from './CacheService';
import { withDatabaseErrorBoundary } from '../utils/error-boundary';
import { log } from '../utils/logger';

interface QueryCacheOptions {
  ttl?: number;
  key?: string;
  tags?: string[];
  skipCache?: boolean;
}

interface CacheableQuery<T> {
  execute: () => Promise<T>;
  options?: QueryCacheOptions;
}

export class CachedQueryService {
  private static readonly DEFAULT_TTL = 300; // 5 minutes
  private static readonly CACHE_KEYS = {
    QUERY: 'query',
    USER_STATS: 'user:stats',
    PROPERTY_STATS: 'property:stats',
    BOOKING_STATS: 'booking:stats',
    SYSTEM_METRICS: 'system:metrics'
  };

  private static queryStats = new Map<string, {
    executions: number;
    cacheHits: number;
    totalTime: number;
    avgTime: number;
  }>();

  // Generic cached query executor
  async executeQuery<T>(
    queryKey: string,
    query: CacheableQuery<T>
  ): Promise<T> {
    const startTime = Date.now();
    const { options = {} } = query;
    const { ttl = CachedQueryService.DEFAULT_TTL, skipCache = false } = options;

    // Generate cache key
    const cacheKey = options.key || CacheService.generateKey(
      CachedQueryService.CACHE_KEYS.QUERY,
      queryKey
    );

    try {
      // Skip cache if requested
      if (skipCache) {
        const result = await this.executeWithStats(queryKey, query.execute, startTime, false);
        return result;
      }

      // Try to get from cache
      const cached = await cacheService.get<T>(cacheKey);
      if (cached !== null) {
        this.updateQueryStats(queryKey, startTime, true);
        log(`📦 Cache hit for query: ${queryKey}`, 'cached-query');
        return cached;
      }

      // Execute query and cache result
      const result = await this.executeWithStats(queryKey, query.execute, startTime, false);
      
      // Cache the result
      await cacheService.set(cacheKey, result, ttl);
      
      return result;

    } catch (error) {
      this.updateQueryStats(queryKey, startTime, false);
      log(`❌ Cached query failed: ${queryKey}`, 'cached-query');
      throw error;
    }
  }

  // Frequently used cached queries
  async getUserStats(userId: number, ttl: number = 600): Promise<{
    totalBookings: number;
    totalSpent: number;
    favoriteProperties: number;
    lastBookingDate?: Date;
  }> {
    return this.executeQuery(`user_stats_${userId}`, {
      execute: async () => {
        return withDatabaseErrorBoundary(
          async () => {
            // This would be implemented with actual user queries
            // Placeholder implementation
            return {
              totalBookings: 0,
              totalSpent: 0,
              favoriteProperties: 0
            };
          },
          'get-user-stats'
        );
      },
      options: { ttl }
    });
  }

  async getPropertyStats(propertyId: number, ttl: number = 900): Promise<{
    totalBookings: number;
    averageRating: number;
    occupancyRate: number;
    revenue: number;
  }> {
    return this.executeQuery(`property_stats_${propertyId}`, {
      execute: async () => {
        return withDatabaseErrorBoundary(
          async () => {
            // This would be implemented with actual property queries
            // Placeholder implementation
            return {
              totalBookings: 0,
              averageRating: 0,
              occupancyRate: 0,
              revenue: 0
            };
          },
          'get-property-stats'
        );
      },
      options: { ttl }
    });
  }

  async getSystemMetrics(ttl: number = 300): Promise<{
    totalUsers: number;
    totalProperties: number;
    totalBookings: number;
    activeBookings: number;
    systemHealth: string;
  }> {
    return this.executeQuery('system_metrics', {
      execute: async () => {
        return withDatabaseErrorBoundary(
          async () => {
            // This would be implemented with actual system queries
            // Placeholder implementation - would use actual table counts
            try {
              const healthCheck = await db.execute('SELECT 1 as health');
              return {
                totalUsers: 0,
                totalProperties: 0,
                totalBookings: 0,
                activeBookings: 0,
                systemHealth: 'healthy'
              };
            } catch (error) {
              return {
                totalUsers: 0,
                totalProperties: 0,
                totalBookings: 0,
                activeBookings: 0,
                systemHealth: 'degraded'
              };
            }
          },
          'get-system-metrics'
        );
      },
      options: { ttl }
    });
  }

  // Cache invalidation methods
  async invalidateUserStats(userId: number): Promise<void> {
    const cacheKey = CacheService.generateKey(
      CachedQueryService.CACHE_KEYS.QUERY,
      `user_stats_${userId}`
    );
    await cacheService.delete(cacheKey);
    log(`🗑️ Invalidated user stats cache for user ${userId}`, 'cached-query');
  }

  async invalidatePropertyStats(propertyId: number): Promise<void> {
    const cacheKey = CacheService.generateKey(
      CachedQueryService.CACHE_KEYS.QUERY,
      `property_stats_${propertyId}`
    );
    await cacheService.delete(cacheKey);
    log(`🗑️ Invalidated property stats cache for property ${propertyId}`, 'cached-query');
  }

  async invalidateSystemMetrics(): Promise<void> {
    const cacheKey = CacheService.generateKey(
      CachedQueryService.CACHE_KEYS.QUERY,
      'system_metrics'
    );
    await cacheService.delete(cacheKey);
    log(`🗑️ Invalidated system metrics cache`, 'cached-query');
  }

  // Batch invalidation by pattern
  async invalidateByPattern(pattern: string): Promise<void> {
    try {
      const keys = await cacheService.keys(`*${pattern}*`);
      await Promise.all(keys.map(key => cacheService.delete(key)));
      log(`🧹 Invalidated ${keys.length} cache entries matching pattern: ${pattern}`, 'cached-query');
    } catch (error) {
      log(`❌ Failed to invalidate by pattern: ${error instanceof Error ? error.message : 'Unknown error'}`, 'cached-query');
    }
  }

  // Cache warming for frequently accessed data
  async warmCache(): Promise<void> {
    log('🔥 Warming query cache...', 'cached-query');
    
    try {
      // Warm system metrics cache
      await this.getSystemMetrics();
      
      // Could add more cache warming operations here
      // await this.getPopularProperties();
      // await this.getRecentBookings();
      
      log('✅ Query cache warmed successfully', 'cached-query');
    } catch (error) {
      log(`❌ Failed to warm query cache: ${error instanceof Error ? error.message : 'Unknown error'}`, 'cached-query');
    }
  }

  // Performance monitoring
  getQueryStats(): Array<{
    query: string;
    executions: number;
    cacheHits: number;
    hitRate: number;
    avgTime: number;
  }> {
    return Array.from(CachedQueryService.queryStats.entries())
      .map(([query, stats]) => ({
        query,
        executions: stats.executions,
        cacheHits: stats.cacheHits,
        hitRate: stats.executions > 0 ? (stats.cacheHits / stats.executions) * 100 : 0,
        avgTime: stats.avgTime
      }))
      .sort((a, b) => b.executions - a.executions);
  }

  getCacheMetrics(): {
    cacheStats: any;
    queryPerformance: any[];
    topQueries: any[];
  } {
    const cacheStats = cacheService.getStats();
    const queryPerformance = this.getQueryStats();
    const topQueries = queryPerformance.slice(0, 10);

    return {
      cacheStats,
      queryPerformance,
      topQueries
    };
  }

  // Helper methods
  private async executeWithStats<T>(
    queryKey: string,
    queryFn: () => Promise<T>,
    startTime: number,
    cacheHit: boolean
  ): Promise<T> {
    try {
      const result = await queryFn();
      this.updateQueryStats(queryKey, startTime, cacheHit);
      return result;
    } catch (error) {
      this.updateQueryStats(queryKey, startTime, cacheHit);
      throw error;
    }
  }

  private updateQueryStats(queryKey: string, startTime: number, cacheHit: boolean): void {
    const duration = Date.now() - startTime;
    const current = CachedQueryService.queryStats.get(queryKey) || {
      executions: 0,
      cacheHits: 0,
      totalTime: 0,
      avgTime: 0
    };

    const newStats = {
      executions: current.executions + 1,
      cacheHits: current.cacheHits + (cacheHit ? 1 : 0),
      totalTime: current.totalTime + duration,
      avgTime: 0
    };
    
    newStats.avgTime = newStats.totalTime / newStats.executions;
    
    CachedQueryService.queryStats.set(queryKey, newStats);
  }

  // Cleanup old statistics
  async cleanup(): Promise<void> {
    try {
      // Keep only top 100 most executed queries in stats
      const allStats = Array.from(CachedQueryService.queryStats.entries())
        .sort(([, a], [, b]) => b.executions - a.executions)
        .slice(0, 100);

      CachedQueryService.queryStats.clear();
      allStats.forEach(([key, stats]) => {
        CachedQueryService.queryStats.set(key, stats);
      });

      log('🧹 Query stats cleanup completed', 'cached-query');
    } catch (error) {
      log(`❌ Query stats cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'cached-query');
    }
  }

  // Cache health check
  async healthCheck(): Promise<{
    cacheAvailable: boolean;
    queryStatsCount: number;
    avgCacheHitRate: number;
  }> {
    try {
      // Test cache availability
      const testKey = 'health_check_test';
      await cacheService.set(testKey, 'test', 1);
      const testValue = await cacheService.get(testKey);
      await cacheService.delete(testKey);

      const cacheAvailable = testValue === 'test';

      // Calculate average hit rate
      const stats = this.getQueryStats();
      const avgHitRate = stats.length > 0 
        ? stats.reduce((sum, stat) => sum + stat.hitRate, 0) / stats.length 
        : 0;

      return {
        cacheAvailable,
        queryStatsCount: CachedQueryService.queryStats.size,
        avgCacheHitRate: avgHitRate
      };

    } catch (error) {
      return {
        cacheAvailable: false,
        queryStatsCount: 0,
        avgCacheHitRate: 0
      };
    }
  }
}

export const cachedQueryService = new CachedQueryService();