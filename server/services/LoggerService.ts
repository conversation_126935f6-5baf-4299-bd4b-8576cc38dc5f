import { config } from '../config';
import { logger as structuredLogger } from '../utils/structured-logger';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  requestId: string | undefined;
  userId: string | number | undefined;
  method: string | undefined;
  url: string | undefined;
  statusCode: number | undefined;
  responseTime: number | undefined;
  userAgent: string | undefined;
  ip: string | undefined;
  context: string | undefined;
  metadata: Record<string, any> | undefined;
  error: {
    name: string;
    message: string;
    stack: string | undefined;
  } | undefined;
}

export class LoggerService {
  private static get isDevelopment() {
    return config.isDevelopment();
  }

  static info(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('info', message, context, metadata);
  }

  static warn(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('warn', message, context, metadata);
  }

  static error(message: string, error?: Error, context?: string, metadata?: Record<string, any>): void {
    const errorData = error ? {
      name: error.name,
      message: error.message,
      stack: this.isDevelopment ? error.stack : undefined
    } : undefined;

    this.log('error', message, context, { ...metadata, error: errorData });
  }

  static debug(message: string, context?: string, metadata?: Record<string, any>): void {
    // Use structured logger which respects LOG_LEVEL environment variable
    const logContext: any = {};
    if (context !== undefined) {
      logContext.component = context;
    }
    structuredLogger.debug(message, logContext, metadata);
  }

  // HTTP request logging
  static httpRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    requestId?: string,
    userId?: string | number,
    userAgent?: string,
    ip?: string,
    metadata?: Record<string, any>
  ): void {
    const logMetadata = {
      method,
      url,
      statusCode,
      responseTime,
      requestId: requestId || undefined,
      userId: userId || undefined,
      userAgent: userAgent || undefined,
      ip: ip || undefined,
      ...metadata
    };

    const level: LogLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    const message = `${method} ${url} ${statusCode} - ${responseTime}ms`;

    this.log(level, message, 'http', logMetadata);
  }

  // Database operation logging
  static database(
    operation: string,
    table: string,
    duration: number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.log('debug', `DB ${operation} on ${table} - ${duration}ms`, 'database', {
      operation,
      table,
      duration,
      requestId,
      ...metadata
    });
  }

  // Cache operation logging
  static cache(
    operation: 'hit' | 'miss' | 'set' | 'delete' | 'clear',
    key: string,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.log('debug', `Cache ${operation}: ${key}`, 'cache', {
      operation,
      key,
      requestId,
      ...metadata
    });
  }

  // Security events logging
  static security(
    event: string,
    userId?: string | number,
    ip?: string,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.log('warn', `Security event: ${event}`, 'security', {
      event,
      userId,
      ip,
      requestId,
      ...metadata
    });
  }

  // Authentication events
  static auth(
    event: 'login' | 'logout' | 'register' | 'failed_login' | 'token_refresh',
    userId?: string | number,
    ip?: string,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    const level: LogLevel = event === 'failed_login' ? 'warn' : 'info';
    this.log(level, `Auth event: ${event}`, 'auth', {
      event,
      userId,
      ip,
      requestId,
      ...metadata
    });
  }

  // Business logic events
  static business(
    event: string,
    userId?: string | number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.log('info', `Business event: ${event}`, 'business', {
      event,
      userId,
      requestId,
      ...metadata
    });
  }

  private static log(level: LogLevel, message: string, context?: string, metadata?: Record<string, any>): void {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context || undefined,
      requestId: undefined,
      userId: undefined,
      method: undefined,
      url: undefined,
      statusCode: undefined,
      responseTime: undefined,
      userAgent: undefined,
      ip: undefined,
      metadata: metadata || undefined,
      error: undefined,
      ...metadata
    };

    if (this.isDevelopment) {
      this.logToConsole(logEntry);
    } else {
      this.logStructured(logEntry);
    }
  }

  private static logToConsole(entry: LogEntry): void {
    const { timestamp, level, message, context, requestId, userId, error } = entry;
    
    // Color codes for different log levels
    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[90m'  // Gray
    };
    const reset = '\x1b[0m';
    
    const color = colors[level] || '';
    const levelStr = level.toUpperCase().padEnd(5);
    
    let logMessage = `${color}[${timestamp}] ${levelStr}${reset}`;
    
    if (context) {
      logMessage += ` [${context}]`;
    }
    
    if (requestId) {
      logMessage += ` [req:${requestId.slice(0, 8)}]`;
    }
    
    if (userId) {
      logMessage += ` [user:${userId}]`;
    }
    
    logMessage += ` ${message}`;
    
    console.log(logMessage);
    
    // Log error details separately for better readability
    if (error && error.stack) {
      console.log(`${color}Stack trace:${reset}\n${error.stack}`);
    }
    
    // Log metadata if present (in development)
    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      console.log(`${color}Metadata:${reset}`, JSON.stringify(entry.metadata, null, 2));
    }
  }

  private static logStructured(entry: LogEntry): void {
    // In production, output structured JSON logs
    console.log(JSON.stringify(entry));
  }

  // Performance logging utility
  static performance(operation: string, startTime: number, requestId?: string, metadata?: Record<string, any>): void {
    const duration = Date.now() - startTime;
    const level: LogLevel = duration > 1000 ? 'warn' : duration > 500 ? 'info' : 'debug';
    
    this.log(level, `Performance: ${operation} completed in ${duration}ms`, 'performance', {
      operation,
      duration,
      requestId,
      ...metadata
    });
  }

  // Audit logging for important business events
  static audit(
    action: string,
    resource: string,
    resourceId: string | number,
    userId?: string | number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.log('info', `Audit: ${action} ${resource}:${resourceId}`, 'audit', {
      action,
      resource,
      resourceId,
      userId,
      requestId,
      ...metadata
    });
  }

  // Health check logging
  static health(
    check: string,
    status: 'healthy' | 'unhealthy' | 'degraded',
    responseTime: number,
    metadata?: Record<string, any>
  ): void {
    const level: LogLevel = status === 'unhealthy' ? 'error' : status === 'degraded' ? 'warn' : 'debug';
    
    this.log(level, `Health check: ${check} is ${status} (${responseTime}ms)`, 'health', {
      check,
      status,
      responseTime,
      ...metadata
    });
  }
}

// Convenience function for request context
export function createRequestLogger(requestId: string, userId?: string | number) {
  return {
    info: (message: string, context?: string, metadata?: Record<string, any>) => 
      LoggerService.info(message, context, { ...metadata, requestId, userId }),
    
    warn: (message: string, context?: string, metadata?: Record<string, any>) => 
      LoggerService.warn(message, context, { ...metadata, requestId, userId }),
    
    error: (message: string, error?: Error, context?: string, metadata?: Record<string, any>) => 
      LoggerService.error(message, error, context, { ...metadata, requestId, userId }),
    
    debug: (message: string, context?: string, metadata?: Record<string, any>) => 
      LoggerService.debug(message, context, { ...metadata, requestId, userId }),
    
    audit: (action: string, resource: string, resourceId: string | number, metadata?: Record<string, any>) => 
      LoggerService.audit(action, resource, resourceId, userId, requestId, metadata),
    
    business: (event: string, metadata?: Record<string, any>) => 
      LoggerService.business(event, userId, requestId, metadata),
      
    performance: (operation: string, startTime: number, metadata?: Record<string, any>) => 
      LoggerService.performance(operation, startTime, requestId, metadata)
  };
}

// Export singleton logger
export const logger = LoggerService;