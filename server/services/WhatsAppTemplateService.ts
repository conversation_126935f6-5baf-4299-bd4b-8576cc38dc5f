import { whatsAppService } from './WhatsAppService';
import { logger } from './LoggerService';
import type { ParsedCommand } from './WhatsAppMessageParser';

export interface BookingQuickReply {
  id: string;
  title: string;
  payload: string;
}

export interface WhatsAppTemplate {
  id: string;
  name: string;
  category: 'booking' | 'availability' | 'confirmation' | 'cancellation';
  message: string;
  quickReplies?: BookingQuickReply[];
}

export class WhatsAppTemplateService {
  private templates: Map<string, WhatsAppTemplate> = new Map();

  constructor() {
    this.initializeTemplates();
  }

  /**
   * Initialize predefined templates
   */
  private initializeTemplates(): void {
    const templates: WhatsAppTemplate[] = [
      {
        id: 'booking_start',
        name: 'Booking Initiation',
        category: 'booking',
        message: `Welcome to Farmhouse Rentals! 🏡

I'll help you find the perfect farmhouse. Let's start:

What information do you have ready?`,
        quickReplies: [
          { id: 'dates_ready', title: '📅 I have dates', payload: 'DATES_READY' },
          { id: 'just_browsing', title: '👀 Just looking', payload: 'BROWSING' },
          { id: 'need_help', title: '❓ Need help', payload: 'HELP_BOOKING' }
        ]
      },
      {
        id: 'date_collection',
        name: 'Date Collection',
        category: 'booking',
        message: `Great! 📅 Please share your dates in any of these formats:

• "Dec 15 to Dec 17"
• "15/12 to 17/12"
• "This weekend"
• "Next month"

What dates work for you?`,
        quickReplies: [
          { id: 'this_weekend', title: 'This Weekend', payload: 'THIS_WEEKEND' },
          { id: 'next_weekend', title: 'Next Weekend', payload: 'NEXT_WEEKEND' },
          { id: 'flexible_dates', title: 'I\'m Flexible', payload: 'FLEXIBLE_DATES' }
        ]
      },
      {
        id: 'guest_count',
        name: 'Guest Count Collection',
        category: 'booking',
        message: `Perfect! 👥 How many guests will be joining you?

This helps me recommend the right-sized farmhouse.`,
        quickReplies: [
          { id: 'small_group', title: '2-5 people', payload: 'GUESTS_2_5' },
          { id: 'medium_group', title: '6-15 people', payload: 'GUESTS_6_15' },
          { id: 'large_group', title: '16+ people', payload: 'GUESTS_16_PLUS' }
        ]
      },
      {
        id: 'location_selection',
        name: 'Location Selection',
        category: 'booking',
        message: `Excellent! 📍 Which area would you prefer?

I have beautiful properties in these popular locations:`,
        quickReplies: [
          { id: 'gurgaon', title: 'Gurgaon', payload: 'LOCATION_GURGAON' },
          { id: 'delhi_ncr', title: 'Delhi NCR', payload: 'LOCATION_DELHI_NCR' },
          { id: 'manesar', title: 'Manesar', payload: 'LOCATION_MANESAR' },
          { id: 'sohna', title: 'Sohna', payload: 'LOCATION_SOHNA' },
          { id: 'anywhere', title: 'Anywhere good', payload: 'LOCATION_FLEXIBLE' }
        ]
      },
      {
        id: 'availability_found',
        name: 'Availability Results',
        category: 'availability',
        message: `🎉 Great news! I found {count} available farmhouses for your dates.

Here are your options:

{property_list}

Would you like more details about any of these?`,
        quickReplies: [
          { id: 'view_details', title: '📋 View Details', payload: 'VIEW_PROPERTY_DETAILS' },
          { id: 'book_now', title: '✅ Book Now', payload: 'BOOK_SELECTED' },
          { id: 'more_options', title: '🔍 More Options', payload: 'MORE_OPTIONS' }
        ]
      },
      {
        id: 'booking_confirmation',
        name: 'Booking Confirmation',
        category: 'confirmation',
        message: `✅ Booking Confirmed! 

📋 Booking Details:
• Reference: {booking_ref}
• Property: {property_name}
• Dates: {dates}
• Guests: {guests}
• Total: ₹{total_amount}

📞 Contact property owner: {owner_contact}

Need help with anything else?`,
        quickReplies: [
          { id: 'directions', title: '🗺️ Get Directions', payload: 'GET_DIRECTIONS' },
          { id: 'owner_contact', title: '📞 Contact Owner', payload: 'CONTACT_OWNER' },
          { id: 'new_booking', title: '🏠 New Booking', payload: 'START_NEW_BOOKING' }
        ]
      },
      {
        id: 'cancellation_help',
        name: 'Cancellation Assistance',
        category: 'cancellation',
        message: `I'll help you cancel your booking. 

Please provide either:
• Your booking reference (e.g., "REF123")
• Phone number used for booking

What would you like to share?`,
        quickReplies: [
          { id: 'have_ref', title: 'I have reference', payload: 'HAVE_BOOKING_REF' },
          { id: 'have_phone', title: 'I have phone', payload: 'HAVE_PHONE_NUMBER' },
          { id: 'need_help', title: 'Need help finding', payload: 'HELP_FIND_BOOKING' }
        ]
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });

    logger.info(`Initialized ${templates.length} WhatsApp templates`);
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): WhatsAppTemplate | undefined {
    return this.templates.get(templateId);
  }

  /**
   * Send template message with quick replies
   */
  async sendTemplate(
    to: string, 
    templateId: string, 
    variables?: Record<string, string>
  ): Promise<boolean> {
    const template = this.templates.get(templateId);
    
    if (!template) {
      logger.error(`Template not found: ${templateId}`);
      return false;
    }

    try {
      // Replace variables in message
      let message = template.message;
      if (variables) {
        Object.entries(variables).forEach(([key, value]) => {
          message = message.replace(new RegExp(`{${key}}`, 'g'), value);
        });
      }

      // Send message with quick replies if available
      if (template.quickReplies && template.quickReplies.length > 0) {
        const quickReplies = template.quickReplies.map(reply => ({
          type: 'text' as const,
          title: reply.title,
          payload: reply.payload
        }));

        return await whatsAppService.sendMessageWithQuickReplies(to, message, quickReplies);
      } else {
        return await whatsAppService.sendMessage(to, message);
      }

    } catch (error) {
      logger.error(`Failed to send template ${templateId}`, error as Error, 'whatsapp-template');
      return false;
    }
  }

  /**
   * Get appropriate template based on parsed command
   */
  getTemplateForCommand(command: ParsedCommand): string | null {
    const { intent, entities } = command;

    switch (intent) {
      case 'booking':
        // Determine which booking step we're in
        if (!entities.dates) {
          return 'date_collection';
        } else if (!entities.guests) {
          return 'guest_count';
        } else if (!entities.location) {
          return 'location_selection';
        } else {
          return 'availability_found';
        }

      case 'availability':
        return 'availability_found';

      case 'cancel':
        return 'cancellation_help';

      case 'help':
        return 'booking_start';

      default:
        return null;
    }
  }

  /**
   * Generate contextual quick replies based on current conversation state
   */
  generateContextualReplies(command: ParsedCommand): BookingQuickReply[] {
    const baseReplies: BookingQuickReply[] = [
      { id: 'help', title: '❓ Help', payload: 'HELP' },
      { id: 'start_over', title: '🔄 Start Over', payload: 'START_OVER' }
    ];

    switch (command.intent) {
      case 'booking':
        return [
          { id: 'continue_booking', title: '✅ Continue Booking', payload: 'CONTINUE_BOOKING' },
          { id: 'change_dates', title: '📅 Change Dates', payload: 'CHANGE_DATES' },
          ...baseReplies
        ];

      case 'availability':
        return [
          { id: 'book_property', title: '🏠 Book Property', payload: 'BOOK_PROPERTY' },
          { id: 'view_more', title: '👀 View More', payload: 'VIEW_MORE' },
          ...baseReplies
        ];

      default:
        return baseReplies;
    }
  }

  /**
   * Format property list for display in messages
   */
  formatPropertyList(properties: any[]): string {
    if (!properties || properties.length === 0) {
      return 'No properties found for your criteria.';
    }

    return properties.map((property, index) => {
      return `${index + 1}. 🏡 ${property.name}\n   📍 ${property.location}\n   💰 ₹${property.price}/night\n   👥 Up to ${property.maxGuests} guests`;
    }).join('\n\n');
  }

  /**
   * Get all templates for a specific category
   */
  getTemplatesByCategory(category: WhatsAppTemplate['category']): WhatsAppTemplate[] {
    return Array.from(this.templates.values()).filter(template => template.category === category);
  }

  /**
   * Update template message
   */
  updateTemplate(templateId: string, updates: Partial<WhatsAppTemplate>): boolean {
    const existing = this.templates.get(templateId);
    if (!existing) {
      return false;
    }

    const updated = { ...existing, ...updates };
    this.templates.set(templateId, updated);
    
    logger.info(`Updated template: ${templateId}`);
    return true;
  }
}

// Export singleton instance
export const whatsAppTemplateService = new WhatsAppTemplateService();