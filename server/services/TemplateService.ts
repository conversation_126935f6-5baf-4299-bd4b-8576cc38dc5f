import { db } from '../db';
import { smsTemplates, smsLogs } from '../../shared/schema';
import { eq, and, desc, asc } from 'drizzle-orm';
import type { SmsTemplate, InsertSmsTemplate, SmsLog, InsertSmsLog } from '../../shared/schema';

export class TemplateService {
  async getActiveTemplateByKey(key: string): Promise<SmsTemplate | null> {
    try {
      const templates = await db
        .select()
        .from(smsTemplates)
        .where(and(eq(smsTemplates.key, key), eq(smsTemplates.status, 'active')))
        .orderBy(desc(smsTemplates.updatedAt))
        .limit(1);

      return templates[0] || null;
    } catch (error) {
      console.error('Error fetching template by key:', error);
      throw new Error('Failed to fetch template');
    }
  }

  async getTemplateById(id: number): Promise<SmsTemplate | null> {
    try {
      const templates = await db
        .select()
        .from(smsTemplates)
        .where(eq(smsTemplates.id, id))
        .limit(1);

      return templates[0] || null;
    } catch (error) {
      console.error('Error fetching template by ID:', error);
      throw new Error('Failed to fetch template');
    }
  }

  async getAllTemplates(status?: string): Promise<SmsTemplate[]> {
    try {
      const query = db.select().from(smsTemplates);
      
      if (status) {
        query.where(eq(smsTemplates.status, status));
      }
      
      return await query.orderBy(desc(smsTemplates.createdAt));
    } catch (error) {
      console.error('Error fetching templates:', error);
      throw new Error('Failed to fetch templates');
    }
  }

  async createTemplate(template: InsertSmsTemplate): Promise<SmsTemplate> {
    try {
      const newTemplates = await db
        .insert(smsTemplates)
        .values(template)
        .returning();

      return newTemplates[0];
    } catch (error) {
      console.error('Error creating template:', error);
      if (error instanceof Error && error.message.includes('unique constraint')) {
        throw new Error('Template key already exists');
      }
      throw new Error('Failed to create template');
    }
  }

  async updateTemplate(id: number, updates: Partial<InsertSmsTemplate>): Promise<SmsTemplate | null> {
    try {
      const updatedTemplates = await db
        .update(smsTemplates)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(smsTemplates.id, id))
        .returning();

      return updatedTemplates[0] || null;
    } catch (error) {
      console.error('Error updating template:', error);
      throw new Error('Failed to update template');
    }
  }

  async deleteTemplate(id: number): Promise<boolean> {
    try {
      const result = await db
        .delete(smsTemplates)
        .where(eq(smsTemplates.id, id))
        .returning();

      return result.length > 0;
    } catch (error) {
      console.error('Error deleting template:', error);
      throw new Error('Failed to delete template');
    }
  }

  async deactivateTemplate(id: number): Promise<SmsTemplate | null> {
    return this.updateTemplate(id, { status: 'inactive' });
  }

  async activateTemplate(id: number): Promise<SmsTemplate | null> {
    return this.updateTemplate(id, { status: 'active' });
  }

  async logSmsMessage(logData: InsertSmsLog): Promise<SmsLog> {
    try {
      const logs = await db
        .insert(smsLogs)
        .values(logData)
        .returning();

      return logs[0];
    } catch (error) {
      console.error('Error logging SMS message:', error);
      throw new Error('Failed to log SMS message');
    }
  }

  async updateSmsLogStatus(
    id: number, 
    status: string, 
    twilioMessageSid?: string, 
    errorMessage?: string
  ): Promise<SmsLog | null> {
    try {
      const updateData: any = { status };
      
      if (twilioMessageSid) {
        updateData.twilioMessageSid = twilioMessageSid;
      }
      
      if (errorMessage) {
        updateData.errorMessage = errorMessage;
      }

      const updatedLogs = await db
        .update(smsLogs)
        .set(updateData)
        .where(eq(smsLogs.id, id))
        .returning();

      return updatedLogs[0] || null;
    } catch (error) {
      console.error('Error updating SMS log status:', error);
      throw new Error('Failed to update SMS log status');
    }
  }

  async getSmsLogsByTemplate(templateId: number, limit: number = 50): Promise<SmsLog[]> {
    try {
      return await db
        .select()
        .from(smsLogs)
        .where(eq(smsLogs.templateId, templateId))
        .orderBy(desc(smsLogs.createdAt))
        .limit(limit);
    } catch (error) {
      console.error('Error fetching SMS logs by template:', error);
      throw new Error('Failed to fetch SMS logs');
    }
  }

  async getSmsLogsByPhone(phone: string, limit: number = 20): Promise<SmsLog[]> {
    try {
      return await db
        .select()
        .from(smsLogs)
        .where(eq(smsLogs.recipientPhone, phone))
        .orderBy(desc(smsLogs.createdAt))
        .limit(limit);
    } catch (error) {
      console.error('Error fetching SMS logs by phone:', error);
      throw new Error('Failed to fetch SMS logs');
    }
  }

  async replaceTemplateVariables(template: SmsTemplate, variables: Record<string, string>): Promise<string> {
    try {
      let content = template.content;
      const templateVariables = template.variables as string[];

      if (!Array.isArray(templateVariables)) {
        throw new Error('Invalid template variables format');
      }

      for (let i = 0; i < templateVariables.length; i++) {
        const variableName = templateVariables[i];
        const variableValue = variables[variableName];

        if (variableValue === undefined || variableValue === null) {
          throw new Error(`Missing required variable: ${variableName}`);
        }

        content = content.replace('{#var#}', variableValue);
      }

      if (content.includes('{#var#}')) {
        throw new Error('Not all template variables were replaced');
      }

      return content;
    } catch (error) {
      console.error('Error replacing template variables:', error);
      throw error;
    }
  }

  async getTemplateAnalytics(templateId?: number): Promise<any> {
    try {
      let query = `
        SELECT 
          st.id,
          st.key,
          st.name,
          st.status,
          COUNT(sl.id) as total_sends,
          COUNT(CASE WHEN sl.status = 'sent' THEN 1 END) as successful_sends,
          COUNT(CASE WHEN sl.status = 'failed' THEN 1 END) as failed_sends,
          ROUND(
            (COUNT(CASE WHEN sl.status = 'sent' THEN 1 END) * 100.0 / NULLIF(COUNT(sl.id), 0)), 2
          ) as success_rate,
          MAX(sl.created_at) as last_sent_at,
          MIN(sl.created_at) as first_sent_at
        FROM sms_templates st
        LEFT JOIN sms_logs sl ON st.id = sl.template_id
      `;

      if (templateId) {
        query += ` WHERE st.id = ${templateId}`;
      }

      query += `
        GROUP BY st.id, st.key, st.name, st.status
        ORDER BY total_sends DESC
      `;

      const result = await db.execute(query);
      return result.rows;
    } catch (error) {
      console.error('Error fetching template analytics:', error);
      throw new Error('Failed to fetch template analytics');
    }
  }

  async validateTemplate(template: Partial<InsertSmsTemplate>): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (!template.key || template.key.trim().length === 0) {
      errors.push('Template key is required');
    }

    if (!template.name || template.name.trim().length === 0) {
      errors.push('Template name is required');
    }

    if (!template.content || template.content.trim().length === 0) {
      errors.push('Template content is required');
    }

    if (!template.dltTemplateId || template.dltTemplateId.trim().length === 0) {
      errors.push('DLT template ID is required');
    }

    if (template.content && template.variables) {
      const variableCount = (template.content.match(/{#var#}/g) || []).length;
      const expectedVariables = Array.isArray(template.variables) ? template.variables.length : 0;

      if (variableCount !== expectedVariables) {
        errors.push(
          `Template content has ${variableCount} variable placeholders but ${expectedVariables} variables defined`
        );
      }
    }

    if (template.status && !['active', 'inactive', 'draft'].includes(template.status)) {
      errors.push('Invalid template status');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  async getTemplateUsageStats(days: number = 30): Promise<any> {
    try {
      const query = `
        SELECT 
          DATE(sl.created_at) as date,
          COUNT(sl.id) as total_messages,
          COUNT(CASE WHEN sl.status = 'sent' THEN 1 END) as successful_messages,
          COUNT(CASE WHEN sl.status = 'failed' THEN 1 END) as failed_messages,
          COUNT(DISTINCT sl.template_id) as unique_templates_used
        FROM sms_logs sl
        WHERE sl.created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY DATE(sl.created_at)
        ORDER BY date DESC
      `;

      const result = await db.execute(query);
      return result.rows;
    } catch (error) {
      console.error('Error fetching template usage stats:', error);
      throw new Error('Failed to fetch template usage stats');
    }
  }
}

export const templateService = new TemplateService();