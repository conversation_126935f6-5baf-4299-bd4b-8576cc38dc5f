import twilio from 'twilio';
import { BaseOTPProvider, OTPResult } from './BaseOTPProvider';
import { config } from '../config';
import { dltSMSService, DLTSMSService } from './DLTSMSService';

export class SMSOTPProvider extends BaseOTPProvider {
  readonly type = 'sms' as const;
  readonly name = 'DLT SMS via Template';
  
  private twilioClient: twilio.Twilio | null = null;
  private initialized = false;
  private dltSMSService: DLTSMSService;

  // Test phone numbers that bypass actual SMS sending
  private readonly testPhoneNumbers = [
    '+************',
    '+************'
    ];

  // Master codes that work for any phone number in development
  private readonly masterCodes = ['123456', '999999'];

  constructor() {
    super();
    this.dltSMSService = dltSMSService;
    this.initialize();
  }

  private initialize(): void {
    if (this.initialized) return;

    if (config.twilio.available && config.twilio.accountSid && config.twilio.authToken) {
      try {
        this.twilioClient = twilio(
          config.twilio.accountSid,
          config.twilio.authToken
        );
        console.log('✅ Twilio SMS service initialized');
      } catch (error) {
        console.warn('⚠️ Failed to initialize Twilio:', error);
        this.twilioClient = null;
      }
    } else {
      console.log('⚠️ Twilio not configured - SMS OTPs will be logged to console');
    }

    this.initialized = true;
  }

  isAvailable(): boolean {
    return this.dltSMSService.isAvailable();
  }

  isTestPhoneNumber(phone: string): boolean {
    return this.testPhoneNumbers.includes(phone);
  }

  isMasterCode(code: string): boolean {
    return config.isDevelopment() && this.masterCodes.includes(code);
  }

  async sendOTP(identifier: string, code: string): Promise<OTPResult> {
    if (!this.validateIdentifier(identifier)) {
      return {
        success: false,
        message: 'Invalid phone number format. Please use a valid Indian mobile number.'
      };
    }

    const sanitizedPhone = this.sanitizeIdentifier(identifier);

    // Handle test phone numbers in any environment
    if (this.isTestPhoneNumber(sanitizedPhone)) {
      console.log(`📱 [TEST] SMS OTP sent to ${sanitizedPhone}`);
      const testResult: OTPResult = {
        success: true,
        message: 'OTP sent successfully (test mode)'
      };
      return testResult;
    }

    // In development mode, always use fallback for non-test numbers too
    if (config.isDevelopment()) {
      // Log to server only - do not expose OTP code in console
      console.log(`📱 [DEVELOPMENT] SMS OTP sent to ${sanitizedPhone} - check server logs for code`);
      // Log OTP code only to server logs for development testing
      require('fs').appendFileSync('server.log', `[${new Date().toISOString()}] SMS OTP for ${sanitizedPhone}: ${code}\n`);
      return {
        success: true,
        message: 'OTP sent successfully to your phone'
      };
    }

    // Use DLT SMS service for production SMS sending
    try {
      const result = await this.dltSMSService.sendOTP(sanitizedPhone, code);
      
      if (result.success) {
        return {
          success: true,
          message: 'OTP sent successfully to your phone'
        };
      } else {
        return {
          success: false,
          message: result.error || 'Failed to send SMS. Please try again or use email verification.'
        };
      }
    } catch (error: any) {
      console.error('SMS sending error:', error);
      
      return {
        success: false,
        message: 'Failed to send SMS. Please try again or use email verification.'
      };
    }
  }
}