import { v2 as cloudinary } from 'cloudinary';
import { log } from './utils/logger';
import { config } from './config';

export interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  bytes: number;
  duration?: number; // For videos
  video?: {
    codec: string;
    bit_rate?: number;
  };
  audio?: {
    codec: string;
    bit_rate?: number;
  };
  eager?: Array<{
    transformation: string;
    width: number;
    height: number;
    bytes: number;
    format: string;
    url: string;
    secure_url: string;
  }>;
}

export class CloudinaryService {
  private initialized = false;

  constructor() {
    // Delay initialization until first use
  }

  private initializeCloudinary() {
    if (this.initialized) return;

    if (!config.cloudinary.available) {
      log('Cloudinary credentials not found. Image upload will be disabled.', 'cloudinary');
      return;
    }

    cloudinary.config({
      cloud_name: config.cloudinary.cloudName!,
      api_key: config.cloudinary.apiKey!,
      api_secret: config.cloudinary.apiSecret!,
      secure: true
    });

    this.initialized = true;
    log('Cloudinary initialized successfully', 'cloudinary');
  }

  async uploadMedia(
    fileBuffer: Buffer,
    filename: string,
    folder: string = 'farmhouse-properties'
  ): Promise<CloudinaryUploadResult> {
    this.initializeCloudinary();
    
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    // Determine if file is video or image based on filename extension
    const extension = filename.split('.').pop()?.toLowerCase();
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'qt', 'webm'];
    const isVideo = videoExtensions.includes(extension || '');
    
    try {
      const result = await new Promise<CloudinaryUploadResult>((resolve, reject) => {
        const uploadOptions: any = {
          folder,
          public_id: `${folder}/${Date.now()}-${filename.replace(/\.[^/.]+$/, "")}`,
          resource_type: isVideo ? 'video' : 'image',
        };

        // Add transformations based on file type
        if (isVideo) {
          uploadOptions.transformation = [
            { quality: 'auto:good' },
            { width: 1280, height: 720, crop: 'limit' },
            { format: 'mp4' } // Convert to mp4 for better web compatibility
          ];
          // Enhanced video settings for streaming
          uploadOptions.eager = [
            { 
              quality: 'auto:good',
              format: 'mp4',
              video_codec: 'h264',
              audio_codec: 'aac',
              streaming_profile: 'hd'
            },
            {
              quality: 'auto:low', 
              format: 'mp4',
              video_codec: 'h264',
              audio_codec: 'aac',
              streaming_profile: 'sd'
            }
          ];
          uploadOptions.eager_async = true;
        } else {
          uploadOptions.transformation = [
            { quality: 'auto:good' },
            { fetch_format: 'auto' },
            { width: 1200, height: 800, crop: 'limit' }
          ];
        }

        cloudinary.uploader.upload_stream(
          uploadOptions,
          (error, result) => {
            if (error) {
              log(`Cloudinary upload error: ${error.message}`, 'cloudinary');
              reject(error);
            } else if (result) {
              log(`${isVideo ? 'Video' : 'Image'} uploaded successfully: ${result.secure_url}`, 'cloudinary');
              resolve(result as CloudinaryUploadResult);
            } else {
              reject(new Error('Upload failed: No result returned'));
            }
          }
        ).end(fileBuffer);
      });

      return result;
    } catch (error) {
      log(`Failed to upload ${isVideo ? 'video' : 'image'}: ${error}`, 'cloudinary');
      throw error;
    }
  }

  async uploadVideo(
    fileBuffer: Buffer,
    filename: string,
    folder: string = 'farmhouse-videos'
  ): Promise<CloudinaryUploadResult> {
    this.initializeCloudinary();
    
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    try {
      const result = await new Promise<CloudinaryUploadResult>((resolve, reject) => {
        const uploadOptions = {
          folder,
          public_id: `${folder}/${Date.now()}-${filename.replace(/\.[^/.]+$/, "")}`,
          resource_type: 'video' as const,
          quality: 'auto:good',
          format: 'mp4',
          video_codec: 'h264',
          audio_codec: 'aac',
          transformation: [
            { 
              quality: 'auto:good',
              width: 1280, 
              height: 720, 
              crop: 'limit',
              format: 'mp4'
            }
          ],
          eager: [
            { 
              quality: 'auto:good',
              format: 'mp4',
              video_codec: 'h264',
              audio_codec: 'aac',
              streaming_profile: 'hd',
              width: 1280,
              height: 720,
              crop: 'limit'
            },
            {
              quality: 'auto:low', 
              format: 'mp4',
              video_codec: 'h264',
              audio_codec: 'aac',
              streaming_profile: 'sd',
              width: 640,
              height: 360,
              crop: 'limit'
            }
          ],
          eager_async: true
        };

        cloudinary.uploader.upload_stream(
          uploadOptions,
          (error, result) => {
            if (error) {
              log(`Cloudinary video upload error: ${error.message}`, 'cloudinary');
              reject(error);
            } else if (result) {
              log(`Video uploaded successfully with streaming: ${result.secure_url}`, 'cloudinary');
              resolve(result as CloudinaryUploadResult);
            } else {
              reject(new Error('Video upload failed: No result returned'));
            }
          }
        ).end(fileBuffer);
      });

      return result;
    } catch (error) {
      log(`Failed to upload video: ${error}`, 'cloudinary');
      throw error;
    }
  }

  // Keep the old method name for backward compatibility
  async uploadImage(
    fileBuffer: Buffer,
    filename: string,
    folder: string = 'farmhouse-properties'
  ): Promise<CloudinaryUploadResult> {
    return this.uploadMedia(fileBuffer, filename, folder);
  }

  async uploadMultipleImages(
    files: Array<{ buffer: Buffer; filename: string }>,
    folder: string = 'farmhouse-properties'
  ): Promise<CloudinaryUploadResult[]> {
    this.initializeCloudinary();
    
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    const uploadPromises = files.map(file => 
      this.uploadMedia(file.buffer, file.filename, folder)
    );

    try {
      const results = await Promise.all(uploadPromises);
      log(`Successfully uploaded ${results.length} media files`, 'cloudinary');
      return results;
    } catch (error) {
      log(`Failed to upload multiple media files: ${error}`, 'cloudinary');
      throw error;
    }
  }

  async deleteImage(publicId: string): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    try {
      const result = await cloudinary.uploader.destroy(publicId);
      const success = result.result === 'ok';
      log(`Image deletion ${success ? 'successful' : 'failed'}: ${publicId}`, 'cloudinary');
      return success;
    } catch (error) {
      log(`Failed to delete image: ${error}`, 'cloudinary');
      throw error;
    }
  }

  async deleteMultipleImages(publicIds: string[]): Promise<{ success: string[]; failed: string[] }> {
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    const results = { success: [] as string[], failed: [] as string[] };

    for (const publicId of publicIds) {
      try {
        const deleted = await this.deleteImage(publicId);
        if (deleted) {
          results.success.push(publicId);
        } else {
          results.failed.push(publicId);
        }
      } catch (error) {
        results.failed.push(publicId);
      }
    }

    return results;
  }

  async deleteVideo(publicId: string): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    try {
      const result = await cloudinary.uploader.destroy(publicId, { 
        resource_type: 'video' 
      });
      const success = result.result === 'ok';
      log(`Video deletion ${success ? 'successful' : 'failed'}: ${publicId}`, 'cloudinary');
      return success;
    } catch (error) {
      log(`Failed to delete video: ${error}`, 'cloudinary');
      throw error;
    }
  }

  generateStreamingUrl(publicId: string, quality: 'hd' | 'sd' = 'hd'): string {
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    const transformations = quality === 'hd' 
      ? { 
          quality: 'auto:good',
          format: 'mp4',
          video_codec: 'h264',
          streaming_profile: 'hd'
        }
      : { 
          quality: 'auto:low',
          format: 'mp4', 
          video_codec: 'h264',
          streaming_profile: 'sd'
        };

    return cloudinary.url(publicId, {
      resource_type: 'video',
      ...transformations,
      secure: true
    });
  }

  generateVideoThumbnail(publicId: string, options: { 
    width?: number; 
    height?: number; 
    start_offset?: string 
  } = {}): string {
    if (!this.initialized) {
      throw new Error('Cloudinary not initialized. Please check your credentials.');
    }

    const { width = 400, height = 300, start_offset = '2' } = options;

    return cloudinary.url(publicId, {
      resource_type: 'video',
      format: 'jpg',
      transformation: [
        {
          width,
          height,
          crop: 'fill',
          start_offset,
          quality: 'auto:good'
        }
      ],
      secure: true
    });
  }

  isInitialized(): boolean {
    return this.initialized;
  }
}

export const cloudinaryService = new CloudinaryService();