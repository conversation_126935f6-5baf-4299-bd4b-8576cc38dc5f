import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer, createLogger } from "vite";
import { type Server } from "http";
import { nanoid } from "nanoid";

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupCustomVite(app: Express, server: Server) {
  const viteConfig = await import("../vite.config.ts");
  
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    host: "0.0.0.0",
    allowedHosts: [
      "8431c529-785e-49ca-bc58-db4a8088e69e-00-ym2lusveylxf.pike.replit.dev",
      ".replit.dev",
      ".repl.co", 
      ".replit.app",
      "localhost"
    ]
  };

  const vite = await createViteServer({
    ...viteConfig.default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      },
    },
    server: serverOptions,
    appType: "custom",
  });

  app.use(vite.middlewares);
  app.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    
    // Skip API routes - let them be handled by the API middleware
    if (url.startsWith('/api/')) {
      return next();
    }

    try {
      const __dirname = path.dirname(new URL(import.meta.url).pathname);
      const clientTemplate = path.resolve(
        __dirname,
        "..",
        "client",
        "index.html",
      );

      // always reload the index.html file from disk incase it changes
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`,
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e as Error);
      next(e);
    }
  });
}

export function serveStatic(app: Express) {
  const __dirname = path.dirname(new URL(import.meta.url).pathname);
  const distPath = path.resolve(__dirname, "..", "dist", "public");

  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`,
    );
  }

  // Serve assets with /assets prefix
  app.use("/assets", express.static(path.join(distPath, "assets")));
  
  // Serve other static files from root
  app.use(express.static(distPath));

  // fall through to index.html if the file doesn't exist (should come after API routes)
  app.use("*", (_req, res) => {
    res.sendFile(path.resolve(distPath, "index.html"));
  });
}