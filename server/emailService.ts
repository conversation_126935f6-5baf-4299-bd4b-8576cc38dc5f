import sgMail from '@sendgrid/mail';
import { config } from './config';

// Lazy initialization of SendGrid
let sgMailInitialized = false;

function initializeSendGrid() {
  if (!sgMailInitialized && config.sendgrid.available && config.sendgrid.apiKey) {
    sgMail.setApiKey(config.sendgrid.apiKey);
    sgMailInitialized = true;
  }
}

interface BookingEmailData {
  bookingId: number;
  propertyTitle: string;
  guestName: string;
  guestEmail: string;
  ownerEmail: string;
  bookingDate: string;
  bookingType: string;
  guests: number;
  totalPrice: number;
  propertyLocation: string;
}

export class EmailService {
  async sendBookingConfirmation(data: BookingEmailData): Promise<boolean> {
    // Initialize SendGrid on first use
    initializeSendGrid();
    
    // Skip email sending if SendGrid is not configured
    if (!config.sendgrid.available) {
      console.log('SendGrid not configured, skipping booking confirmation email');
      return true;
    }

    try {
      const msg = {
        to: data.guestEmail,
        from: { email: '<EMAIL>', name: 'BookAFarm' },
        replyTo: { email: '<EMAIL>', name: 'BookAFarm Support' },
        subject: `Booking Confirmed - ${data.propertyTitle}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4A6741;">Booking Confirmation</h2>
            
            <p>Dear ${data.guestName},</p>
            
            <p>Thank you for booking with us! Your reservation has been confirmed.</p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #2D3C2D; margin-top: 0;">Booking Details</h3>
              <p><strong>Property:</strong> ${data.propertyTitle}</p>
              <p><strong>Location:</strong> ${data.propertyLocation}</p>
              <p><strong>Date:</strong> ${new Date(data.bookingDate).toLocaleDateString()}</p>
              <p><strong>Duration:</strong> ${data.bookingType === 'morning' ? '12-Hour Access (8am - 8pm)' : '24-Hour Access'}</p>
              <p><strong>Guests:</strong> ${data.guests}</p>
              <p><strong>Total Amount:</strong> ₹${data.totalPrice}</p>
              <p><strong>Booking ID:</strong> #${data.bookingId}</p>
            </div>
            
            <p>We hope you have a wonderful stay at our farmhouse!</p>
            
            <p>If you have any questions, please don't hesitate to contact us.</p>
            
            <p>Best regards,<br>The BookAFarm Team</p>
          </div>
        `
      };

      await sgMail.send(msg);
      return true;
    } catch (error) {
      console.error('Error sending booking confirmation email:', error);
      return false;
    }
  }

  async sendOwnerNotification(data: BookingEmailData): Promise<boolean> {
    // Initialize SendGrid on first use
    initializeSendGrid();
    
    // Skip email sending if SendGrid is not configured
    if (!config.sendgrid.available) {
      console.log('SendGrid not configured, skipping owner notification email');
      return true;
    }

    try {
      const msg = {
        to: data.ownerEmail,
        from: { email: '<EMAIL>', name: 'BookAFarm' },
        replyTo: { email: '<EMAIL>', name: 'BookAFarm Support' },
        subject: `New Booking Received - ${data.propertyTitle}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4A6741;">New Booking Notification</h2>
            
            <p>Hello,</p>
            
            <p>You have received a new booking for your property!</p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #2D3C2D; margin-top: 0;">Booking Details</h3>
              <p><strong>Property:</strong> ${data.propertyTitle}</p>
              <p><strong>Guest:</strong> ${data.guestName}</p>
              <p><strong>Guest Email:</strong> ${data.guestEmail}</p>
              <p><strong>Date:</strong> ${new Date(data.bookingDate).toLocaleDateString()}</p>
              <p><strong>Duration:</strong> ${data.bookingType === 'morning' ? '12-Hour Access (8am - 8pm)' : '24-Hour Access'}</p>
              <p><strong>Guests:</strong> ${data.guests}</p>
              <p><strong>Total Amount:</strong> ₹${data.totalPrice}</p>
              <p><strong>Booking ID:</strong> #${data.bookingId}</p>
            </div>
            
            <p>Please ensure your property is ready for the guest's arrival.</p>
            
            <p>Best regards,<br>The BookAFarm Team</p>
          </div>
        `
      };

      await sgMail.send(msg);
      return true;
    } catch (error) {
      console.error('Error sending owner notification email:', error);
      return false;
    }
  }

  async sendBookingEmails(data: BookingEmailData): Promise<{ guestEmailSent: boolean; ownerEmailSent: boolean }> {
    const guestEmailSent = await this.sendBookingConfirmation(data);
    const ownerEmailSent = await this.sendOwnerNotification(data);
    
    return {
      guestEmailSent,
      ownerEmailSent
    };
  }
}

export const emailService = new EmailService();