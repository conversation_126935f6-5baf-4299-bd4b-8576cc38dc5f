/**
 * Enhanced Database Seeding with Realistic Data Scenarios
 * Provides sophisticated seeding capabilities for development and testing
 */

import { UserFactory, PropertyFactory, BookingFactory } from '../../tests/factories';
import { storage } from '../storage';
import { log } from '../utils/logger';

export interface SeedingScenario {
  name: string;
  description: string;
  users: number;
  owners: number;
  admins: number;
  propertiesPerOwner: number;
  bookingsPerProperty: number;
  reviewPercentage: number;
  timeRange: {
    pastMonths: number;
    futureMonths: number;
  };
  features: {
    includePastBookings: boolean;
    includeReviews: boolean;
    includeCancellations: boolean;
    includeAnalyticsData: boolean;
    includeSeasonalPricing: boolean;
    includePromotions: boolean;
  };
}

export interface SeedingOptions {
  scenario?: string;
  customScenario?: Partial<SeedingScenario>;
  clearExisting?: boolean;
  outputCredentials?: boolean;
  logProgress?: boolean;
}

export interface SeedingResult {
  scenario: string;
  timestamp: Date;
  duration: number;
  stats: {
    users: number;
    properties: number;
    bookings: number;
    reviews: number;
  };
  credentials: Array<{
    email: string;
    password: string;
    role: string;
    id: number;
  }>;
  sampleData: {
    sampleUser: any;
    sampleProperty: any;
    sampleBooking: any;
  };
}

export class EnhancedDatabaseSeeder {
  private static instance: EnhancedDatabaseSeeder;
  private scenarios: Map<string, SeedingScenario> = new Map();

  constructor() {
    this.initializeScenarios();
  }

  static getInstance(): EnhancedDatabaseSeeder {
    if (!this.instance) {
      this.instance = new EnhancedDatabaseSeeder();
    }
    return this.instance;
  }

  private initializeScenarios(): void {
    // Development scenario - lightweight for daily development
    this.scenarios.set('development', {
      name: 'Development',
      description: 'Lightweight setup for daily development work',
      users: 5,
      owners: 2,
      admins: 1,
      propertiesPerOwner: 3,
      bookingsPerProperty: 2,
      reviewPercentage: 60,
      timeRange: { pastMonths: 2, futureMonths: 3 },
      features: {
        includePastBookings: true,
        includeReviews: true,
        includeCancellations: false,
        includeAnalyticsData: false,
        includeSeasonalPricing: false,
        includePromotions: false
      }
    });

    // Demo scenario - perfect for demonstrations
    this.scenarios.set('demo', {
      name: 'Demo',
      description: 'Rich data perfect for product demonstrations',
      users: 15,
      owners: 4,
      admins: 1,
      propertiesPerOwner: 4,
      bookingsPerProperty: 5,
      reviewPercentage: 80,
      timeRange: { pastMonths: 6, futureMonths: 6 },
      features: {
        includePastBookings: true,
        includeReviews: true,
        includeCancellations: true,
        includeAnalyticsData: true,
        includeSeasonalPricing: true,
        includePromotions: true
      }
    });

    // Testing scenario - comprehensive for integration testing
    this.scenarios.set('testing', {
      name: 'Testing',
      description: 'Comprehensive dataset for integration testing',
      users: 20,
      owners: 5,
      admins: 2,
      propertiesPerOwner: 3,
      bookingsPerProperty: 4,
      reviewPercentage: 70,
      timeRange: { pastMonths: 12, futureMonths: 3 },
      features: {
        includePastBookings: true,
        includeReviews: true,
        includeCancellations: true,
        includeAnalyticsData: true,
        includeSeasonalPricing: false,
        includePromotions: false
      }
    });

    // Performance scenario - large dataset for performance testing
    this.scenarios.set('performance', {
      name: 'Performance',
      description: 'Large dataset for performance testing and optimization',
      users: 100,
      owners: 20,
      admins: 3,
      propertiesPerOwner: 5,
      bookingsPerProperty: 8,
      reviewPercentage: 50,
      timeRange: { pastMonths: 24, futureMonths: 6 },
      features: {
        includePastBookings: true,
        includeReviews: true,
        includeCancellations: true,
        includeAnalyticsData: true,
        includeSeasonalPricing: true,
        includePromotions: true
      }
    });

    // Analytics scenario - focused on business intelligence
    this.scenarios.set('analytics', {
      name: 'Analytics',
      description: 'Rich historical data for analytics and reporting',
      users: 50,
      owners: 8,
      admins: 2,
      propertiesPerOwner: 6,
      bookingsPerProperty: 12,
      reviewPercentage: 85,
      timeRange: { pastMonths: 36, futureMonths: 3 },
      features: {
        includePastBookings: true,
        includeReviews: true,
        includeCancellations: true,
        includeAnalyticsData: true,
        includeSeasonalPricing: true,
        includePromotions: true
      }
    });

    // Minimal scenario - bare minimum for quick setup
    this.scenarios.set('minimal', {
      name: 'Minimal',
      description: 'Bare minimum data for quick development setup',
      users: 2,
      owners: 1,
      admins: 1,
      propertiesPerOwner: 1,
      bookingsPerProperty: 1,
      reviewPercentage: 100,
      timeRange: { pastMonths: 1, futureMonths: 1 },
      features: {
        includePastBookings: false,
        includeReviews: false,
        includeCancellations: false,
        includeAnalyticsData: false,
        includeSeasonalPricing: false,
        includePromotions: false
      }
    });

    // Business scenario - realistic business data patterns
    this.scenarios.set('business', {
      name: 'Business',
      description: 'Realistic business patterns and seasonal trends',
      users: 80,
      owners: 12,
      admins: 2,
      propertiesPerOwner: 4,
      bookingsPerProperty: 15,
      reviewPercentage: 75,
      timeRange: { pastMonths: 18, futureMonths: 12 },
      features: {
        includePastBookings: true,
        includeReviews: true,
        includeCancellations: true,
        includeAnalyticsData: true,
        includeSeasonalPricing: true,
        includePromotions: true
      }
    });
  }

  /**
   * Seed database with specified scenario
   */
  async seedDatabase(options: SeedingOptions = {}): Promise<SeedingResult> {
    const startTime = Date.now();
    const scenario = this.getScenario(options.scenario || 'development', options.customScenario);
    
    if (options.logProgress) {
      log(`🌱 Starting database seeding with scenario: ${scenario.name}`, 'seeder');
      log(`📝 ${scenario.description}`, 'seeder');
    }

    try {
      // Clear existing data if requested
      if (options.clearExisting) {
        await this.clearDatabase(options.logProgress);
      }

      // Reset factory counters for consistent IDs
      UserFactory.resetCounter();
      PropertyFactory.resetCounter();
      BookingFactory.resetCounter();

      // Seed data progressively
      const users = await this.seedUsers(scenario, options.logProgress);
      const properties = await this.seedProperties(scenario, users.owners, options.logProgress);
      const bookings = await this.seedBookings(scenario, users.all, properties, options.logProgress);
      const reviews = await this.seedReviews(scenario, bookings, options.logProgress);

      // Add seasonal pricing if enabled
      if (scenario.features.includeSeasonalPricing) {
        await this.addSeasonalPricing(properties, options.logProgress);
      }

      // Add promotions if enabled
      if (scenario.features.includePromotions) {
        await this.addPromotions(properties, options.logProgress);
      }

      // Seed analytics data if enabled
      if (scenario.features.includeAnalyticsData) {
        await this.seedAnalyticsData(scenario, options.logProgress);
      }

      const duration = Date.now() - startTime;
      const result: SeedingResult = {
        scenario: scenario.name,
        timestamp: new Date(),
        duration,
        stats: {
          users: users.all.length,
          properties: properties.length,
          bookings: bookings.length,
          reviews: reviews.length
        },
        credentials: users.all.map(user => ({
          email: user.email,
          password: 'TestPassword123!',
          role: user.role,
          id: user.id
        })),
        sampleData: {
          sampleUser: users.all[0],
          sampleProperty: properties[0],
          sampleBooking: bookings[0]
        }
      };

      if (options.logProgress) {
        this.logSeedingResults(result);
      }

      if (options.outputCredentials) {
        this.outputCredentials(result.credentials);
      }

      return result;

    } catch (error) {
      log(`❌ Database seeding failed: ${error}`, 'seeder');
      throw error;
    }
  }

  private getScenario(scenarioName: string, customScenario?: Partial<SeedingScenario>): SeedingScenario {
    const baseScenario = this.scenarios.get(scenarioName);
    if (!baseScenario) {
      throw new Error(`Unknown seeding scenario: ${scenarioName}. Available: ${Array.from(this.scenarios.keys()).join(', ')}`);
    }

    if (customScenario) {
      return { ...baseScenario, ...customScenario };
    }

    return baseScenario;
  }

  private async clearDatabase(logProgress?: boolean): Promise<void> {
    if (logProgress) {
      log('🗑️ Clearing existing database data...', 'seeder');
    }

    try {
      // Clear in reverse dependency order
      // Note: These would need to be implemented in storage or use direct DB operations
      // For now, we'll skip clearing and rely on unique constraints
      
      if (logProgress) {
        log('✅ Database clearing skipped (no clear methods implemented)', 'seeder');
      }
    } catch (error) {
      log(`⚠️ Database clearing warning: ${error}`, 'seeder');
    }
  }

  private async seedUsers(scenario: SeedingScenario, logProgress?: boolean) {
    if (logProgress) {
      log(`👥 Creating ${scenario.users + scenario.owners + scenario.admins} users...`, 'seeder');
    }

    const users = [];
    const owners = [];
    const admins = [];

    // Create admin users
    for (let i = 0; i < scenario.admins; i++) {
      const adminData = UserFactory.createAdmin({
        email: `admin${i + 1}@farmhouse-dev.com`,
        username: `admin${i + 1}`,
        firstName: `Admin`,
        lastName: `User ${i + 1}`
      });
      
      const admin = await this.createUserInDatabase(adminData);
      admins.push(admin);
      users.push(admin);
    }

    // Create owner users with realistic profiles
    for (let i = 0; i < scenario.owners; i++) {
      const ownerData = UserFactory.createOwner({
        email: `owner${i + 1}@farmhouse-dev.com`,
        username: `owner${i + 1}`,
        firstName: UserFactory.generateRealisticName().firstName,
        lastName: UserFactory.generateRealisticName().lastName
      });
      
      const owner = await this.createUserInDatabase(ownerData);
      owners.push(owner);
      users.push(owner);
    }

    // Create regular users with diverse profiles
    for (let i = 0; i < scenario.users; i++) {
      const userData = UserFactory.createWithRealisticProfile({
        email: `user${i + 1}@farmhouse-dev.com`,
        username: `user${i + 1}`,
        role: 'user'
      });
      
      const user = await this.createUserInDatabase(userData);
      users.push(user);
    }

    return { all: users, owners, admins };
  }

  private async createUserInDatabase(userData: any) {
    try {
      const user = await storage.createUser({
        ...userData,
        password: 'TestPassword123!'
      });
      return user;
    } catch (error) {
      log(`Failed to create user: ${userData.email}`, 'seeder');
      throw error;
    }
  }

  private async seedProperties(scenario: SeedingScenario, owners: any[], logProgress?: boolean) {
    if (logProgress) {
      log(`🏠 Creating ${owners.length * scenario.propertiesPerOwner} properties...`, 'seeder');
    }

    const properties = [];

    for (const owner of owners) {
      // Create diverse property types for each owner
      const ownerProperties = [
        // Luxury properties (30%)
        ...PropertyFactory.createMany(Math.floor(scenario.propertiesPerOwner * 0.3), {
          ownerId: owner.id,
          status: 'active',
          featured: true,
          category: 'luxury'
        }),
        // Standard properties (50%)
        ...PropertyFactory.createMany(Math.floor(scenario.propertiesPerOwner * 0.5), {
          ownerId: owner.id,
          status: 'active',
          category: 'standard'
        }),
        // Budget properties (20%)
        ...PropertyFactory.createMany(Math.ceil(scenario.propertiesPerOwner * 0.2), {
          ownerId: owner.id,
          status: 'active',
          category: 'budget'
        })
      ];

      for (const propertyData of ownerProperties) {
        try {
          const property = await storage.createProperty({
            ...propertyData,
            owner
          });
          properties.push(property);
        } catch (error) {
          log(`Failed to create property for owner ${owner.id}`, 'seeder');
        }
      }
    }

    return properties;
  }

  private async seedBookings(
    scenario: SeedingScenario,
    users: any[],
    properties: any[],
    logProgress?: boolean
  ) {
    if (logProgress) {
      log(`📅 Creating bookings with ${scenario.timeRange.pastMonths} months history...`, 'seeder');
    }

    const bookings = [];
    const now = new Date();

    for (const property of properties) {
      const propertyBookings = [];

      // Create past bookings if enabled
      if (scenario.features.includePastBookings) {
        const pastBookings = this.generateBookingsForTimeRange(
          property.id,
          users,
          now,
          -scenario.timeRange.pastMonths,
          0,
          Math.floor(scenario.bookingsPerProperty * 0.6)
        );
        propertyBookings.push(...pastBookings);
      }

      // Create future bookings
      const futureBookings = this.generateBookingsForTimeRange(
        property.id,
        users,
        now,
        0,
        scenario.timeRange.futureMonths,
        Math.ceil(scenario.bookingsPerProperty * 0.4)
      );
      propertyBookings.push(...futureBookings);

      // Create bookings in database
      for (const bookingData of propertyBookings) {
        try {
          const booking = await storage.createBooking({
            ...bookingData,
            property,
            user: users.find(u => u.id === bookingData.userId)
          });
          bookings.push(booking);
        } catch (error) {
          log(`Failed to create booking for property ${property.id}`, 'seeder');
        }
      }
    }

    return bookings;
  }

  private generateBookingsForTimeRange(
    propertyId: number,
    users: any[],
    baseDate: Date,
    startMonthOffset: number,
    endMonthOffset: number,
    count: number
  ) {
    const bookings = [];
    const startDate = new Date(baseDate);
    startDate.setMonth(startDate.getMonth() + startMonthOffset);
    
    const endDate = new Date(baseDate);
    endDate.setMonth(endDate.getMonth() + endMonthOffset);

    for (let i = 0; i < count; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const isPast = endMonthOffset <= 0;
      
      const bookingData = isPast
        ? BookingFactory.createPast({
            propertyId,
            userId: user.id,
            status: Math.random() > 0.2 ? 'completed' : 'cancelled'
          })
        : BookingFactory.createFuture({
            propertyId,
            userId: user.id,
            status: Math.random() > 0.8 ? 'pending' : 'confirmed'
          });

      bookings.push(bookingData);
    }

    return bookings;
  }

  private async seedReviews(scenario: SeedingScenario, bookings: any[], logProgress?: boolean) {
    if (!scenario.features.includeReviews) {
      return [];
    }

    if (logProgress) {
      log(`⭐ Creating reviews for completed bookings...`, 'seeder');
    }

    const reviews = [];
    const completedBookings = bookings.filter(b => b.status === 'completed');

    for (const booking of completedBookings) {
      // Create review based on percentage chance
      if (Math.random() * 100 <= scenario.reviewPercentage) {
        try {
          const reviewData = {
            propertyId: booking.propertyId,
            userId: booking.userId,
            bookingId: booking.id,
            rating: this.generateRealisticRating(),
            comment: this.generateRealisticReviewComment(),
            createdAt: new Date(booking.updatedAt.getTime() + 24 * 60 * 60 * 1000)
          };

          const review = await storage.createReview?.(reviewData);
          if (review) {
            reviews.push(review);
          }
        } catch (error) {
          log(`Failed to create review for booking ${booking.id}`, 'seeder');
        }
      }
    }

    return reviews;
  }

  private generateRealisticRating(): number {
    // Weighted towards higher ratings (realistic review distribution)
    const rand = Math.random();
    if (rand < 0.5) return 5;
    if (rand < 0.75) return 4;
    if (rand < 0.9) return 3;
    if (rand < 0.97) return 2;
    return 1;
  }

  private generateRealisticReviewComment(): string {
    const positiveComments = [
      'Absolutely stunning property with breathtaking views. The amenities were top-notch and the host was incredibly responsive.',
      'Perfect getaway spot! Clean, comfortable, and exactly as described. Would definitely book again.',
      'Beautiful location with easy access to local attractions. The property was well-maintained and spacious.',
      'Exceeded our expectations in every way. The attention to detail was impressive and the value was excellent.',
      'Fantastic experience from start to finish. The property was pristine and the booking process was seamless.',
      'Wonderful retreat for our family vacation. Kids loved the pool and we enjoyed the peaceful surroundings.',
      'Highly recommend this property! Great amenities, beautiful setting, and excellent communication from the host.',
      'The perfect blend of luxury and comfort. Every detail was thoughtfully considered.',
      'Outstanding property with modern amenities in a traditional setting. Truly unforgettable stay.',
      'Exceptional value for the quality provided. Will definitely be returning in the future.'
    ];

    const neutralComments = [
      'Good property overall. A few minor issues but nothing that significantly impacted our stay.',
      'Decent place to stay. Property was as described, though could use some minor updates.',
      'Satisfactory experience. The location was convenient and the basic amenities were adequate.',
      'Reasonable value for the price. Property was clean and functional for our needs.',
      'Average stay. Nothing spectacular but met our basic requirements for accommodation.'
    ];

    const criticalComments = [
      'Property had potential but several maintenance issues affected our stay. Hope these get addressed.',
      'Location was great but the property needs some attention to details and cleanliness.',
      'Booking process was smooth but the actual property had some discrepancies from the listing.',
      'Decent location but the amenities were not as described. Communication could be improved.'
    ];

    const rating = this.generateRealisticRating();
    
    if (rating >= 4) {
      return positiveComments[Math.floor(Math.random() * positiveComments.length)];
    } else if (rating === 3) {
      return neutralComments[Math.floor(Math.random() * neutralComments.length)];
    } else {
      return criticalComments[Math.floor(Math.random() * criticalComments.length)];
    }
  }

  private async addSeasonalPricing(properties: any[], logProgress?: boolean) {
    if (logProgress) {
      log('💰 Adding seasonal pricing variations...', 'seeder');
    }

    // Implementation would depend on your pricing model
    // This is a placeholder for seasonal pricing logic
    for (const property of properties) {
      try {
        // Add peak season multipliers (summer months)
        // Add off-season discounts (winter months)
        // Add holiday premiums
        // This would integrate with your pricing service
      } catch (error) {
        log(`Failed to add seasonal pricing for property ${property.id}`, 'seeder');
      }
    }
  }

  private async addPromotions(properties: any[], logProgress?: boolean) {
    if (logProgress) {
      log('🎯 Adding promotional campaigns...', 'seeder');
    }

    // Add sample promotions for some properties
    const promotionTypes = [
      'Early Bird Discount',
      'Last Minute Deal',
      'Extended Stay Discount',
      'Weekend Special',
      'First Time Guest Offer'
    ];

    for (const property of properties.slice(0, Math.floor(properties.length * 0.3))) {
      try {
        // Implementation would depend on your promotion system
        // This is a placeholder for promotion logic
      } catch (error) {
        log(`Failed to add promotion for property ${property.id}`, 'seeder');
      }
    }
  }

  private async seedAnalyticsData(scenario: SeedingScenario, logProgress?: boolean) {
    if (logProgress) {
      log('📊 Seeding analytics and reporting data...', 'seeder');
    }

    // Create historical analytics data for dashboards
    try {
      // Revenue data
      // Occupancy rates
      // Customer acquisition metrics
      // Performance indicators
      // This would integrate with your analytics service
    } catch (error) {
      log(`Failed to seed analytics data: ${error}`, 'seeder');
    }
  }

  private logSeedingResults(result: SeedingResult): void {
    log(`✅ Database seeding completed successfully!`, 'seeder');
    log(`📊 Scenario: ${result.scenario}`, 'seeder');
    log(`⏱️ Duration: ${(result.duration / 1000).toFixed(2)}s`, 'seeder');
    log(`📈 Statistics:`, 'seeder');
    log(`   👥 Users: ${result.stats.users}`, 'seeder');
    log(`   🏠 Properties: ${result.stats.properties}`, 'seeder');
    log(`   📅 Bookings: ${result.stats.bookings}`, 'seeder');
    log(`   ⭐ Reviews: ${result.stats.reviews}`, 'seeder');
  }

  private outputCredentials(credentials: Array<{ email: string; password: string; role: string; id: number }>): void {
    log('\n🔑 Test Account Credentials:', 'seeder');
    log('━'.repeat(60), 'seeder');
    
    const adminUsers = credentials.filter(c => c.role === 'admin');
    const ownerUsers = credentials.filter(c => c.role === 'owner');
    const regularUsers = credentials.filter(c => c.role === 'user').slice(0, 3);

    if (adminUsers.length > 0) {
      log('👑 Admin Accounts:', 'seeder');
      adminUsers.forEach(user => {
        log(`   📧 ${user.email} | 🔑 ${user.password}`, 'seeder');
      });
    }

    if (ownerUsers.length > 0) {
      log('🏠 Property Owner Accounts:', 'seeder');
      ownerUsers.forEach(user => {
        log(`   📧 ${user.email} | 🔑 ${user.password}`, 'seeder');
      });
    }

    if (regularUsers.length > 0) {
      log('👤 Sample User Accounts:', 'seeder');
      regularUsers.forEach(user => {
        log(`   📧 ${user.email} | 🔑 ${user.password}`, 'seeder');
      });
    }

    log('━'.repeat(60), 'seeder');
    log('💡 All accounts use the same password for development convenience', 'seeder');
  }

  /**
   * List available seeding scenarios
   */
  getAvailableScenarios(): Array<{ name: string; description: string }> {
    return Array.from(this.scenarios.entries()).map(([name, scenario]) => ({
      name,
      description: scenario.description
    }));
  }

  /**
   * Get detailed information about a specific scenario
   */
  getScenarioDetails(scenarioName: string): SeedingScenario | null {
    return this.scenarios.get(scenarioName) || null;
  }
}

// Singleton instance
export const enhancedSeeder = EnhancedDatabaseSeeder.getInstance();

// Convenience functions for common use cases
export async function seedForDevelopment(options: Partial<SeedingOptions> = {}) {
  return enhancedSeeder.seedDatabase({
    scenario: 'development',
    clearExisting: true,
    outputCredentials: true,
    logProgress: true,
    ...options
  });
}

export async function seedForDemo(options: Partial<SeedingOptions> = {}) {
  return enhancedSeeder.seedDatabase({
    scenario: 'demo',
    clearExisting: true,
    outputCredentials: true,
    logProgress: true,
    ...options
  });
}

export async function seedForTesting(options: Partial<SeedingOptions> = {}) {
  return enhancedSeeder.seedDatabase({
    scenario: 'testing',
    clearExisting: true,
    outputCredentials: false,
    logProgress: false,
    ...options
  });
}

export default EnhancedDatabaseSeeder;