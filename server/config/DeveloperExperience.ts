/**
 * Developer Experience Configuration
 * Integrates all developer experience enhancements for development environment
 */

import { Express } from 'express';
import { getConfigHotReloader, createConfigReloadMiddleware } from './ConfigHotReload';
// DatabaseSeeder is imported dynamically to avoid importing dev dependencies in production
import { getApiDocumentationGenerator, createApiDocMiddleware } from './ApiDocumentationGenerator';
import { log } from '../utils/logger';
import { config } from '../config';

export interface DeveloperExperienceConfig {
  hotReload: {
    enabled: boolean;
    watchEnvironmentFiles: boolean;
    watchConfigFiles: boolean;
    debounceMs: number;
  };
  seeding: {
    autoSeedOnStartup: boolean;
    defaultScenario: string;
    outputCredentials: boolean;
  };
  documentation: {
    enabled: boolean;
    autoGenerate: boolean;
    serveOnRoute: string;
    includeExamples: boolean;
  };
  middleware: {
    enableReloadEndpoints: boolean;
    enableSeedingEndpoints: boolean;
    enableDocumentationEndpoints: boolean;
  };
}

export class DeveloperExperience {
  private config: DeveloperExperienceConfig;
  private app?: Express;
  private isInitialized = false;

  constructor(config: Partial<DeveloperExperienceConfig> = {}) {
    this.config = {
      hotReload: {
        enabled: process.env.NODE_ENV === 'development',
        watchEnvironmentFiles: true,
        watchConfigFiles: true,
        debounceMs: 500
      },
      seeding: {
        autoSeedOnStartup: false,
        defaultScenario: 'development',
        outputCredentials: true
      },
      documentation: {
        enabled: true,
        autoGenerate: true,
        serveOnRoute: '/docs',
        includeExamples: true
      },
      middleware: {
        enableReloadEndpoints: true,
        enableSeedingEndpoints: true,
        enableDocumentationEndpoints: true
      },
      ...config
    };
  }

  /**
   * Initialize developer experience features with Express app
   */
  async initialize(app: Express): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.app = app;
    
    if (process.env.NODE_ENV !== 'development') {
      // Silently skip in production - no need to log this
      return;
    }

    log('🚀 Initializing developer experience features...', 'dev-experience');

    await this.setupHotReloading();
    await this.setupDatabaseSeeding();
    await this.setupApiDocumentation();
    await this.setupDeveloperMiddleware();
    
    this.isInitialized = true;
    this.logFeatureStatus();
  }

  private async setupHotReloading(): Promise<void> {
    if (!this.config.hotReload.enabled) {
      return;
    }

    const hotReloader = getConfigHotReloader({
      enabled: this.config.hotReload.enabled,
      envFiles: this.config.hotReload.watchEnvironmentFiles 
        ? ['.env', '.env.local', '.env.development']
        : [],
      configFiles: this.config.hotReload.watchConfigFiles
        ? ['server/config.ts', 'server/config/index.ts']
        : [],
      debounceMs: this.config.hotReload.debounceMs,
      logChanges: true
    });

    // Listen for configuration changes
    hotReloader.on('envChanged', (event) => {
      log(`🔄 Environment configuration changed: ${event.file}`, 'dev-experience');
    });

    hotReloader.on('configChanged', (event) => {
      log(`⚙️ Configuration file changed: ${event.file}`, 'dev-experience');
    });

    hotReloader.on('error', (event) => {
      log(`❌ Configuration reload error: ${event.error}`, 'dev-experience');
    });

    log('🔥 Configuration hot reloading enabled', 'dev-experience');
  }

  private async setupDatabaseSeeding(): Promise<void> {
    if (this.config.seeding.autoSeedOnStartup) {
      try {
        log('🌱 Auto-seeding database with development data...', 'dev-experience');
        
        const { seedForDevelopment } = await import('./DatabaseSeeder');
        const result = await seedForDevelopment({
          scenario: this.config.seeding.defaultScenario,
          outputCredentials: this.config.seeding.outputCredentials,
          logProgress: true
        });

        log(`✅ Database seeded successfully with ${result.stats.users} users and ${result.stats.properties} properties`, 'dev-experience');
      } catch (error) {
        log(`⚠️ Auto-seeding failed: ${error}`, 'dev-experience');
      }
    }
  }

  private async setupApiDocumentation(): Promise<void> {
    if (!this.config.documentation.enabled) {
      return;
    }

    const apiDocGenerator = getApiDocumentationGenerator({
      title: 'Farmhouse API',
      description: 'Property booking and management platform API',
      version: '1.0.0',
      outputDir: 'docs/api',
      generateMarkdown: true,
      generatePostman: true,
      generateOpenApi: true,
      includeExamples: this.config.documentation.includeExamples
    });

    if (this.config.documentation.autoGenerate) {
      try {
        await apiDocGenerator.generateDocumentation();
        log('📚 API documentation generated successfully', 'dev-experience');
      } catch (error) {
        log(`⚠️ API documentation generation failed: ${error}`, 'dev-experience');
      }
    }
  }

  private async setupDeveloperMiddleware(): Promise<void> {
    if (!this.app) {
      return;
    }

    // Configuration reload middleware
    if (this.config.middleware.enableReloadEndpoints) {
      this.app.use(createConfigReloadMiddleware());
      log('🔄 Configuration reload endpoints enabled', 'dev-experience');
    }

    // Database seeding middleware
    if (this.config.middleware.enableSeedingEndpoints) {
      this.setupSeedingEndpoints();
      log('🌱 Database seeding endpoints enabled', 'dev-experience');
    }

    // API documentation middleware
    if (this.config.middleware.enableDocumentationEndpoints) {
      this.setupDocumentationEndpoints();
      log('📚 API documentation endpoints enabled', 'dev-experience');
    }

    // Developer dashboard endpoint
    this.setupDeveloperDashboard();
  }

  private setupSeedingEndpoints(): void {
    if (!this.app) return;

    // Seed with specific scenario
    this.app.post('/__dev/seed/:scenario', async (req, res) => {
      try {
        const scenario = req.params.scenario;
        const clearExisting = req.query.clear === 'true';
        
        const { enhancedSeeder } = await import('./DatabaseSeeder');
        const result = await enhancedSeeder.seedDatabase({
          scenario,
          clearExisting,
          outputCredentials: true,
          logProgress: true
        });

        res.json({
          success: true,
          message: `Database seeded with ${scenario} scenario`,
          result: {
            scenario: result.scenario,
            duration: result.duration,
            stats: result.stats
          }
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Seeding failed'
        });
      }
    });

    // Get available scenarios
    this.app.get('/__dev/seed/scenarios', async (req, res) => {
      try {
        const { enhancedSeeder } = await import('./DatabaseSeeder');
        const scenarios = enhancedSeeder.getAvailableScenarios();
        res.json({ success: true, scenarios });
      } catch (error) {
        res.status(500).json({ error: 'Failed to load seeding scenarios' });
      }
    });

    // Clear database
    this.app.delete('/__dev/seed', async (req, res) => {
      try {
        const { enhancedSeeder } = await import('./DatabaseSeeder');
        await enhancedSeeder.seedDatabase({
          scenario: 'minimal',
          clearExisting: true,
          outputCredentials: false,
          logProgress: false
        });

        res.json({
          success: true,
          message: 'Database cleared successfully'
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Clear failed'
        });
      }
    });
  }

  private setupDocumentationEndpoints(): void {
    if (!this.app) return;

    const apiDocMiddleware = createApiDocMiddleware();

    // Serve documentation
    this.app.use(this.config.documentation.serveOnRoute, apiDocMiddleware.documentationMiddleware);

    // Regenerate documentation
    this.app.post('/__dev/docs/generate', async (req, res) => {
      try {
        await apiDocMiddleware.generateDocs();
        res.json({
          success: true,
          message: 'API documentation regenerated successfully'
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Documentation generation failed'
        });
      }
    });

    // Documentation status
    this.app.get('/__dev/docs/status', (req, res) => {
      const generator = getApiDocumentationGenerator();
      const status = generator.getGenerationStatus();
      res.json({ success: true, status });
    });
  }

  private setupDeveloperDashboard(): void {
    if (!this.app) return;

    this.app.get('/__dev/dashboard', (req, res) => {
      const hotReloader = getConfigHotReloader();
      const apiDocGenerator = getApiDocumentationGenerator();

      const dashboard = {
        title: 'Farmhouse Developer Dashboard',
        timestamp: new Date().toISOString(),
        features: {
          hotReload: {
            enabled: this.config.hotReload.enabled,
            status: hotReloader.getWatchStatus()
          },
          seeding: {
            enabled: true,
            scenarios: [], // Available scenarios loaded dynamically
            defaultScenario: this.config.seeding.defaultScenario
          },
          documentation: {
            enabled: this.config.documentation.enabled,
            status: apiDocGenerator.getGenerationStatus(),
            url: this.config.documentation.serveOnRoute
          }
        },
        endpoints: {
          config: {
            reload: 'POST /__config/reload',
            status: 'GET /__config/status'
          },
          seeding: {
            seed: 'POST /__dev/seed/:scenario',
            scenarios: 'GET /__dev/seed/scenarios',
            clear: 'DELETE /__dev/seed'
          },
          documentation: {
            generate: 'POST /__dev/docs/generate',
            status: 'GET /__dev/docs/status',
            view: `GET ${this.config.documentation.serveOnRoute}`
          }
        }
      };

      res.json(dashboard);
    });

    // HTML dashboard
    this.app.get('/__dev', (req, res) => {
      const html = this.generateDashboardHtml();
      res.setHeader('Content-Type', 'text/html');
      res.send(html);
    });
  }

  private generateDashboardHtml(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farmhouse Developer Dashboard</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0; padding: 20px; background: #f5f5f5; 
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card h3 { margin-top: 0; color: #333; }
        .button { 
            display: inline-block; padding: 8px 16px; background: #007bff; color: white; 
            text-decoration: none; border-radius: 4px; border: none; cursor: pointer; margin: 5px;
        }
        .button:hover { background: #0056b3; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status.enabled { background: #d4edda; color: #155724; }
        .status.disabled { background: #f8d7da; color: #721c24; }
        .endpoint { font-family: monospace; background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Farmhouse Developer Dashboard</h1>
            <p>Development tools and utilities for enhanced developer experience</p>
        </div>

        <div class="grid">
            <!-- Configuration Hot Reload -->
            <div class="card">
                <h3>🔥 Configuration Hot Reload</h3>
                <div class="status ${this.config.hotReload.enabled ? 'enabled' : 'disabled'}">
                    ${this.config.hotReload.enabled ? 'Enabled' : 'Disabled'}
                </div>
                <p>Automatically reloads configuration when files change</p>
                <button class="button" onclick="reloadConfig()">Reload Config</button>
                <button class="button" onclick="checkConfigStatus()">Check Status</button>
            </div>

            <!-- Database Seeding -->
            <div class="card">
                <h3>🌱 Database Seeding</h3>
                <p>Seed database with realistic test data</p>
                <select id="scenarioSelect">
                    <option value="development">Development</option>
                    <option value="demo">Demo</option>
                    <option value="testing">Testing</option>
                    <option value="minimal">Minimal</option>
                </select>
                <br><br>
                <button class="button" onclick="seedDatabase()">Seed Database</button>
                <button class="button danger" onclick="clearDatabase()">Clear Database</button>
            </div>

            <!-- API Documentation -->
            <div class="card">
                <h3>📚 API Documentation</h3>
                <div class="status ${this.config.documentation.enabled ? 'enabled' : 'disabled'}">
                    ${this.config.documentation.enabled ? 'Enabled' : 'Disabled'}
                </div>
                <p>Generate and serve interactive API documentation</p>
                <a href="${this.config.documentation.serveOnRoute}" class="button" target="_blank">View Docs</a>
                <button class="button" onclick="generateDocs()">Regenerate</button>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3>⚡ Quick Actions</h3>
                <p>Common development tasks</p>
                <button class="button" onclick="seedDemo()">Setup Demo Data</button>
                <button class="button" onclick="resetAndSeed()">Reset & Seed</button>
                <a href="/__dev/dashboard" class="button" target="_blank">API Dashboard</a>
            </div>
        </div>

        <div class="card" style="margin-top: 20px;">
            <h3>🔧 Developer Endpoints</h3>
            <div class="grid">
                <div>
                    <h4>Configuration</h4>
                    <div class="endpoint">POST /__config/reload</div>
                    <div class="endpoint">GET /__config/status</div>
                </div>
                <div>
                    <h4>Database Seeding</h4>
                    <div class="endpoint">POST /__dev/seed/:scenario</div>
                    <div class="endpoint">GET /__dev/seed/scenarios</div>
                    <div class="endpoint">DELETE /__dev/seed</div>
                </div>
                <div>
                    <h4>Documentation</h4>
                    <div class="endpoint">POST /__dev/docs/generate</div>
                    <div class="endpoint">GET /__dev/docs/status</div>
                    <div class="endpoint">GET ${this.config.documentation.serveOnRoute}</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function reloadConfig() {
            try {
                const response = await fetch('/__config/reload', { method: 'POST' });
                const result = await response.json();
                alert(result.success ? 'Configuration reloaded!' : 'Reload failed: ' + result.error);
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function checkConfigStatus() {
            try {
                const response = await fetch('/__config/status');
                const result = await response.json();
                console.log('Config Status:', result);
                alert('Config status logged to console');
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function seedDatabase() {
            const scenario = document.getElementById('scenarioSelect').value;
            try {
                const response = await fetch(\`/__dev/seed/\${scenario}?clear=true\`, { method: 'POST' });
                const result = await response.json();
                alert(result.success ? \`Database seeded with \${scenario} scenario!\` : 'Seeding failed: ' + result.error);
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function clearDatabase() {
            if (confirm('Are you sure you want to clear the database?')) {
                try {
                    const response = await fetch('/__dev/seed', { method: 'DELETE' });
                    const result = await response.json();
                    alert(result.success ? 'Database cleared!' : 'Clear failed: ' + result.error);
                } catch (error) {
                    alert('Error: ' + error.message);
                }
            }
        }

        async function generateDocs() {
            try {
                const response = await fetch('/__dev/docs/generate', { method: 'POST' });
                const result = await response.json();
                alert(result.success ? 'Documentation regenerated!' : 'Generation failed: ' + result.error);
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function seedDemo() {
            try {
                const response = await fetch('/__dev/seed/demo?clear=true', { method: 'POST' });
                const result = await response.json();
                alert(result.success ? 'Demo data seeded!' : 'Seeding failed: ' + result.error);
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function resetAndSeed() {
            if (confirm('This will clear the database and seed with development data. Continue?')) {
                try {
                    const response = await fetch('/__dev/seed/development?clear=true', { method: 'POST' });
                    const result = await response.json();
                    alert(result.success ? 'Database reset and seeded!' : 'Operation failed: ' + result.error);
                } catch (error) {
                    alert('Error: ' + error.message);
                }
            }
        }
    </script>
</body>
</html>`;
  }

  private logFeatureStatus(): void {
    log('✅ Developer experience features initialized:', 'dev-experience');
    log(`   🔥 Hot Reload: ${this.config.hotReload.enabled ? 'Enabled' : 'Disabled'}`, 'dev-experience');
    log(`   🌱 Auto Seeding: ${this.config.seeding.autoSeedOnStartup ? 'Enabled' : 'Disabled'}`, 'dev-experience');
    log(`   📚 API Docs: ${this.config.documentation.enabled ? 'Enabled' : 'Disabled'}`, 'dev-experience');
    log('   🎛️ Developer Dashboard: http://localhost:5000/__dev', 'dev-experience');
  }

  /**
   * Get current configuration
   */
  getConfig(): DeveloperExperienceConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<DeveloperExperienceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    log('⚙️ Developer experience configuration updated', 'dev-experience');
  }

  /**
   * Get feature status
   */
  getStatus(): {
    initialized: boolean;
    features: Record<string, boolean>;
    endpoints: string[];
  } {
    return {
      initialized: this.isInitialized,
      features: {
        hotReload: this.config.hotReload.enabled,
        seeding: true,
        documentation: this.config.documentation.enabled,
        dashboard: true
      },
      endpoints: [
        '/__dev - Developer Dashboard',
        '/__config/reload - Reload Configuration',
        '/__dev/seed/:scenario - Seed Database',
        '/__dev/docs/generate - Generate Documentation',
        `${this.config.documentation.serveOnRoute} - API Documentation`
      ]
    };
  }
}

// Singleton instance
let _devExperience: DeveloperExperience | null = null;

export function getDeveloperExperience(config?: Partial<DeveloperExperienceConfig>): DeveloperExperience {
  if (!_devExperience) {
    _devExperience = new DeveloperExperience(config);
  }
  return _devExperience;
}

// Convenience function for Express integration
export async function setupDeveloperExperience(
  app: Express, 
  config?: Partial<DeveloperExperienceConfig>
): Promise<DeveloperExperience> {
  const devExperience = getDeveloperExperience(config);
  await devExperience.initialize(app);
  return devExperience;
}

export default DeveloperExperience;