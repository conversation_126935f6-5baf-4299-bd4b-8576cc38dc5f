/**
 * Configuration Hot Reloading for Development
 * Automatically reloads configuration when files change during development
 */

import { watchFile, unwatchFile, readFileSync } from 'fs';
import { EventEmitter } from 'events';
import { join } from 'path';
import { config as loadEnv } from 'dotenv';
import { log } from '../utils/logger';
import { config as appConfig } from '../config';

export interface ConfigWatchOptions {
  enabled: boolean;
  envFiles: string[];
  configFiles: string[];
  debounceMs: number;
  logChanges: boolean;
}

export interface ConfigChangeEvent {
  type: 'env' | 'config';
  file: string;
  timestamp: Date;
  changes: Record<string, { old: any; new: any }>;
}

export class ConfigHotReloader extends EventEmitter {
  private options: ConfigWatchOptions;
  private originalEnv: Record<string, string | undefined>;
  private watchedFiles: Set<string> = new Set();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private isEnabled = false;

  constructor(options: Partial<ConfigWatchOptions> = {}) {
    super();
    
    this.options = {
      enabled: process.env.NODE_ENV === 'development',
      envFiles: ['.env', '.env.local', '.env.development'],
      configFiles: ['server/config.ts', 'server/config/index.ts'],
      debounceMs: 500,
      logChanges: true,
      ...options
    };

    this.originalEnv = { ...process.env };
    
    if (this.options.enabled) {
      this.initialize();
    }
  }

  private initialize(): void {
    if (appConfig.isDevelopment()) {
      log('🔥 Configuration hot reloading enabled', 'config-reload');
    }
    
    // Watch environment files
    this.watchEnvironmentFiles();
    
    // Watch configuration files
    this.watchConfigurationFiles();
    
    this.isEnabled = true;
    
    // Handle process cleanup
    process.on('exit', () => this.cleanup());
    process.on('SIGINT', () => this.cleanup());
    process.on('SIGTERM', () => this.cleanup());
  }

  private watchEnvironmentFiles(): void {
    for (const envFile of this.options.envFiles) {
      const fullPath = join(process.cwd(), envFile);
      
      try {
        // Check if file exists before watching
        readFileSync(fullPath, 'utf8');
        this.watchFile(fullPath, 'env');
        if (appConfig.isDevelopment()) {
          log(`📋 Watching environment file: ${envFile}`, 'config-reload');
        }
      } catch (error) {
        // File doesn't exist, skip watching
        if (appConfig.isDevelopment()) {
          log(`⚠️ Environment file not found: ${envFile}`, 'config-reload');
        }
      }
    }
  }

  private watchConfigurationFiles(): void {
    for (const configFile of this.options.configFiles) {
      const fullPath = join(process.cwd(), configFile);
      
      try {
        readFileSync(fullPath, 'utf8');
        this.watchFile(fullPath, 'config');
        if (appConfig.isDevelopment()) {
          log(`⚙️ Watching configuration file: ${configFile}`, 'config-reload');
        }
      } catch (error) {
        if (appConfig.isDevelopment()) {
          log(`⚠️ Configuration file not found: ${configFile}`, 'config-reload');
        }
      }
    }
  }

  private watchFile(filePath: string, type: 'env' | 'config'): void {
    if (this.watchedFiles.has(filePath)) {
      return;
    }

    this.watchedFiles.add(filePath);
    
    watchFile(filePath, { interval: 1000 }, (curr, prev) => {
      if (curr.mtime !== prev.mtime) {
        this.handleFileChange(filePath, type);
      }
    });
  }

  private handleFileChange(filePath: string, type: 'env' | 'config'): void {
    // Clear existing debounce timer
    const existingTimer = this.debounceTimers.get(filePath);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new debounce timer
    const timer = setTimeout(() => {
      this.processFileChange(filePath, type);
      this.debounceTimers.delete(filePath);
    }, this.options.debounceMs);

    this.debounceTimers.set(filePath, timer);
  }

  private processFileChange(filePath: string, type: 'env' | 'config'): void {
    try {
      if (type === 'env') {
        this.reloadEnvironmentFile(filePath);
      } else {
        this.reloadConfigurationFile(filePath);
      }
    } catch (error) {
      log(`❌ Error reloading ${type} file ${filePath}: ${error}`, 'config-reload');
      this.emit('error', { type, file: filePath, error });
    }
  }

  private reloadEnvironmentFile(filePath: string): void {
    const oldEnv = { ...process.env };
    
    // Reload environment variables
    loadEnv({ path: filePath, override: true });
    
    // Detect changes
    const changes = this.detectEnvironmentChanges(oldEnv, process.env);
    
    if (Object.keys(changes).length > 0) {
      const changeEvent: ConfigChangeEvent = {
        type: 'env',
        file: filePath,
        timestamp: new Date(),
        changes
      };

      if (this.options.logChanges) {
        this.logEnvironmentChanges(filePath, changes);
      }

      this.emit('envChanged', changeEvent);
      this.emit('change', changeEvent);
    }
  }

  private reloadConfigurationFile(filePath: string): void {
    // Clear module cache for configuration files
    const modulePath = require.resolve(filePath);
    delete require.cache[modulePath];
    
    // Clear related module caches
    Object.keys(require.cache).forEach(key => {
      if (key.includes('config') && key.includes(process.cwd())) {
        delete require.cache[key];
      }
    });

    const changeEvent: ConfigChangeEvent = {
      type: 'config',
      file: filePath,
      timestamp: new Date(),
      changes: {} // Configuration changes are harder to detect, emit generic event
    };

    if (this.options.logChanges) {
      log(`🔄 Configuration file reloaded: ${filePath}`, 'config-reload');
    }

    this.emit('configChanged', changeEvent);
    this.emit('change', changeEvent);
  }

  private detectEnvironmentChanges(
    oldEnv: Record<string, string | undefined>,
    newEnv: Record<string, string | undefined>
  ): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    
    // Check for changed/added variables
    for (const [key, newValue] of Object.entries(newEnv)) {
      const oldValue = oldEnv[key];
      if (oldValue !== newValue) {
        changes[key] = { old: oldValue, new: newValue };
      }
    }
    
    // Check for removed variables
    for (const [key, oldValue] of Object.entries(oldEnv)) {
      if (!(key in newEnv)) {
        changes[key] = { old: oldValue, new: undefined };
      }
    }
    
    return changes;
  }

  private logEnvironmentChanges(
    filePath: string,
    changes: Record<string, { old: any; new: any }>
  ): void {
    log(`🔄 Environment file reloaded: ${filePath}`, 'config-reload');
    
    for (const [key, { old: oldValue, new: newValue }] of Object.entries(changes)) {
      // Don't log sensitive values
      const isSensitive = this.isSensitiveKey(key);
      const displayOld = isSensitive ? '[REDACTED]' : oldValue;
      const displayNew = isSensitive ? '[REDACTED]' : newValue;
      
      if (newValue === undefined) {
        log(`  ❌ ${key}: ${displayOld} → (removed)`, 'config-reload');
      } else if (oldValue === undefined) {
        log(`  ✅ ${key}: (new) → ${displayNew}`, 'config-reload');
      } else {
        log(`  🔄 ${key}: ${displayOld} → ${displayNew}`, 'config-reload');
      }
    }
  }

  private isSensitiveKey(key: string): boolean {
    const sensitivePatterns = [
      'SECRET', 'PASSWORD', 'TOKEN', 'KEY', 'AUTH', 'PRIVATE',
      'CREDENTIAL', 'API_KEY', 'JWT', 'COOKIE'
    ];
    
    const upperKey = key.toUpperCase();
    return sensitivePatterns.some(pattern => upperKey.includes(pattern));
  }

  /**
   * Add custom file to watch
   */
  addWatchFile(filePath: string, type: 'env' | 'config' = 'config'): void {
    if (!this.isEnabled) {
      return;
    }
    
    this.watchFile(filePath, type);
    log(`👀 Added custom watch file: ${filePath}`, 'config-reload');
  }

  /**
   * Remove file from watch
   */
  removeWatchFile(filePath: string): void {
    if (this.watchedFiles.has(filePath)) {
      unwatchFile(filePath);
      this.watchedFiles.delete(filePath);
      log(`🚫 Removed watch file: ${filePath}`, 'config-reload');
    }
  }

  /**
   * Get current watch status
   */
  getWatchStatus(): {
    enabled: boolean;
    watchedFiles: string[];
    options: ConfigWatchOptions;
  } {
    return {
      enabled: this.isEnabled,
      watchedFiles: Array.from(this.watchedFiles),
      options: this.options
    };
  }

  /**
   * Temporarily disable hot reloading
   */
  disable(): void {
    this.isEnabled = false;
    this.cleanup();
    log('🛑 Configuration hot reloading disabled', 'config-reload');
  }

  /**
   * Re-enable hot reloading
   */
  enable(): void {
    if (this.options.enabled && process.env.NODE_ENV === 'development') {
      this.initialize();
      log('🔥 Configuration hot reloading re-enabled', 'config-reload');
    }
  }

  /**
   * Manually trigger configuration reload
   */
  async reloadConfiguration(): Promise<void> {
    try {
      // Reload all environment files
      for (const envFile of this.options.envFiles) {
        const fullPath = join(process.cwd(), envFile);
        try {
          this.reloadEnvironmentFile(fullPath);
        } catch (error) {
          // File might not exist, continue
        }
      }
      
      log('🔄 Manual configuration reload completed', 'config-reload');
      this.emit('manualReload', { timestamp: new Date() });
    } catch (error) {
      log(`❌ Manual configuration reload failed: ${error}`, 'config-reload');
      throw error;
    }
  }

  /**
   * Clean up watchers and timers
   */
  private cleanup(): void {
    // Clear all file watchers
    for (const filePath of Array.from(this.watchedFiles)) {
      unwatchFile(filePath);
    }
    this.watchedFiles.clear();
    
    // Clear all debounce timers
    for (const timer of Array.from(this.debounceTimers.values())) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();
    
    this.isEnabled = false;
  }

  /**
   * Get configuration change history (useful for debugging)
   */
  private changeHistory: ConfigChangeEvent[] = [];
  
  getChangeHistory(limit: number = 50): ConfigChangeEvent[] {
    return this.changeHistory.slice(-limit);
  }

  private recordChange(event: ConfigChangeEvent): void {
    this.changeHistory.push(event);
    
    // Keep only recent changes
    if (this.changeHistory.length > 100) {
      this.changeHistory = this.changeHistory.slice(-50);
    }
  }
}

// Singleton instance for development use
let _hotReloader: ConfigHotReloader | null = null;

export function getConfigHotReloader(options?: Partial<ConfigWatchOptions>): ConfigHotReloader {
  if (!_hotReloader) {
    _hotReloader = new ConfigHotReloader(options);
  }
  return _hotReloader;
}

// Development middleware integration
export function createConfigReloadMiddleware() {
  const hotReloader = getConfigHotReloader();
  
  return (req: any, res: any, next: any) => {
    // Add reload endpoint for development
    if (req.path === '/__config/reload' && req.method === 'POST') {
      hotReloader.reloadConfiguration()
        .then(() => {
          res.json({ 
            success: true, 
            message: 'Configuration reloaded',
            timestamp: new Date().toISOString()
          });
        })
        .catch(error => {
          res.status(500).json({ 
            success: false, 
            error: error.message 
          });
        });
      return;
    }
    
    // Add status endpoint
    if (req.path === '/__config/status' && req.method === 'GET') {
      res.json({
        status: hotReloader.getWatchStatus(),
        history: hotReloader.getChangeHistory(10)
      });
      return;
    }
    
    next();
  };
}

export default ConfigHotReloader;