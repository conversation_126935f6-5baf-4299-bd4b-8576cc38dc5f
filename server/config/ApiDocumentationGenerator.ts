/**
 * Enhanced API Documentation Generation System
 * Automatically generates comprehensive API documentation with interactive features
 */

import { Express, Request, Response, Router } from 'express';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { log } from '../utils/logger';
import { config } from '../config';

export interface ApiEndpoint {
  path: string;
  method: string;
  summary: string;
  description: string;
  tags: string[];
  parameters: ApiParameter[];
  requestBody?: ApiRequestBody;
  responses: Record<string, ApiResponse>;
  security?: ApiSecurity[];
  examples: ApiExample[];
  deprecated?: boolean;
}

export interface ApiParameter {
  name: string;
  in: 'path' | 'query' | 'header' | 'cookie';
  required: boolean;
  schema: ApiSchema;
  description: string;
  example?: any;
}

export interface ApiRequestBody {
  description: string;
  required: boolean;
  content: Record<string, { schema: ApiSchema; examples?: Record<string, ApiExample> }>;
}

export interface ApiResponse {
  description: string;
  content?: Record<string, { schema: ApiSchema; examples?: Record<string, ApiExample> }>;
  headers?: Record<string, ApiParameter>;
}

export interface ApiSchema {
  type?: string;
  properties?: Record<string, ApiSchema>;
  items?: ApiSchema;
  required?: string[];
  enum?: any[];
  format?: string;
  example?: any;
  description?: string;
  $ref?: string;
}

export interface ApiExample {
  summary: string;
  description?: string;
  value: any;
}

export interface ApiSecurity {
  type: 'bearer' | 'apiKey' | 'oauth2';
  scheme?: string;
  bearerFormat?: string;
  in?: 'header' | 'query' | 'cookie';
  name?: string;
}

export interface ApiDocumentationConfig {
  title: string;
  description: string;
  version: string;
  servers: Array<{ url: string; description: string }>;
  contact: {
    name: string;
    email: string;
    url?: string;
  };
  license: {
    name: string;
    url?: string;
  };
  outputDir: string;
  generateMarkdown: boolean;
  generatePostman: boolean;
  generateOpenApi: boolean;
  includeExamples: boolean;
  autoDetectRoutes: boolean;
}

export class ApiDocumentationGenerator {
  private endpoints: Map<string, ApiEndpoint> = new Map();
  private config: ApiDocumentationConfig;
  private app?: Express;

  constructor(config: Partial<ApiDocumentationConfig> = {}) {
    this.config = {
      title: 'Farmhouse API',
      description: 'Property booking and management platform API',
      version: '1.0.0',
      servers: [
        { url: 'http://localhost:5000', description: 'Development server' },
        { url: 'https://api.farmhouse.com', description: 'Production server' }
      ],
      contact: {
        name: 'Farmhouse API Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      },
      outputDir: 'docs/api',
      generateMarkdown: true,
      generatePostman: true,
      generateOpenApi: true,
      includeExamples: true,
      autoDetectRoutes: false,
      ...config
    };

    this.initializeEndpoints();
  }

  private initializeEndpoints(): void {
    // Authentication endpoints
    this.addEndpoint({
      path: '/api/auth/register',
      method: 'POST',
      summary: 'Register new user',
      description: 'Create a new user account with email verification',
      tags: ['Authentication'],
      parameters: [],
      requestBody: {
        description: 'User registration data',
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['email', 'password', 'firstName', 'lastName'],
              properties: {
                email: { type: 'string', format: 'email', description: 'User email address' },
                password: { type: 'string', description: 'Password (min 8 characters)' },
                firstName: { type: 'string', description: 'User first name' },
                lastName: { type: 'string', description: 'User last name' },
                phone: { type: 'string', description: 'Phone number (optional)' },
                role: { type: 'string', enum: ['user', 'owner'], description: 'User role' }
              }
            },
            examples: {
              user: {
                summary: 'Regular user registration',
                value: {
                  email: '<EMAIL>',
                  password: 'SecurePassword123!',
                  firstName: 'John',
                  lastName: 'Doe',
                  phone: '+1234567890',
                  role: 'user'
                }
              },
              owner: {
                summary: 'Property owner registration',
                value: {
                  email: '<EMAIL>',
                  password: 'SecurePassword123!',
                  firstName: 'Jane',
                  lastName: 'Owner',
                  phone: '+1234567891',
                  role: 'owner'
                }
              }
            }
          }
        }
      },
      responses: {
        '201': {
          description: 'User registered successfully',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  data: {
                    type: 'object',
                    properties: {
                      user: { $ref: '#/components/schemas/User' },
                      token: { type: 'string', description: 'JWT authentication token' }
                    }
                  },
                  message: { type: 'string' }
                }
              }
            }
          }
        },
        '400': { description: 'Validation error' },
        '409': { description: 'Email already exists' }
      },
      examples: []
    });

    this.addEndpoint({
      path: '/api/auth/login',
      method: 'POST',
      summary: 'User login',
      description: 'Authenticate user and return JWT token',
      tags: ['Authentication'],
      parameters: [],
      requestBody: {
        description: 'Login credentials',
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['email', 'password'],
              properties: {
                email: { type: 'string', format: 'email' },
                password: { type: 'string' }
              }
            }
          }
        }
      },
      responses: {
        '200': {
          description: 'Login successful',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  data: {
                    type: 'object',
                    properties: {
                      user: { $ref: '#/components/schemas/User' },
                      token: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        '401': { description: 'Invalid credentials' }
      },
      examples: []
    });

    // Properties endpoints
    this.addEndpoint({
      path: '/api/properties',
      method: 'GET',
      summary: 'List properties',
      description: 'Get a paginated list of properties with optional filtering',
      tags: ['Properties'],
      parameters: [
        {
          name: 'page',
          in: 'query',
          required: false,
          schema: { type: 'integer', example: 1 },
          description: 'Page number for pagination'
        },
        {
          name: 'limit',
          in: 'query',
          required: false,
          schema: { type: 'integer', example: 10 },
          description: 'Number of items per page'
        },
        {
          name: 'location',
          in: 'query',
          required: false,
          schema: { type: 'string', example: 'Goa' },
          description: 'Filter by location'
        },
        {
          name: 'minPrice',
          in: 'query',
          required: false,
          schema: { type: 'number', example: 1000 },
          description: 'Minimum price filter'
        },
        {
          name: 'maxPrice',
          in: 'query',
          required: false,
          schema: { type: 'number', example: 10000 },
          description: 'Maximum price filter'
        },
        {
          name: 'featured',
          in: 'query',
          required: false,
          schema: { type: 'boolean', example: true },
          description: 'Filter featured properties only'
        }
      ],
      responses: {
        '200': {
          description: 'Properties list retrieved successfully',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/Property' }
                  },
                  pagination: { $ref: '#/components/schemas/Pagination' }
                }
              }
            }
          }
        }
      },
      examples: []
    });

    this.addEndpoint({
      path: '/api/properties',
      method: 'POST',
      summary: 'Create property',
      description: 'Create a new property listing (owner only)',
      tags: ['Properties'],
      security: [{ type: 'bearer', bearerFormat: 'JWT' }],
      parameters: [],
      requestBody: {
        description: 'Property data',
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/PropertyInput' }
          }
        }
      },
      responses: {
        '201': {
          description: 'Property created successfully',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  data: { $ref: '#/components/schemas/Property' }
                }
              }
            }
          }
        },
        '401': { description: 'Authentication required' },
        '403': { description: 'Owner role required' }
      },
      examples: []
    });

    // Bookings endpoints
    this.addEndpoint({
      path: '/api/bookings',
      method: 'POST',
      summary: 'Create booking',
      description: 'Create a new property booking',
      tags: ['Bookings'],
      security: [{ type: 'bearer', bearerFormat: 'JWT' }],
      parameters: [],
      requestBody: {
        description: 'Booking data',
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/BookingInput' }
          }
        }
      },
      responses: {
        '201': {
          description: 'Booking created successfully',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  data: { $ref: '#/components/schemas/Booking' }
                }
              }
            }
          }
        },
        '400': { description: 'Invalid booking data' },
        '409': { description: 'Booking conflict' }
      },
      examples: []
    });
  }

  addEndpoint(endpoint: ApiEndpoint): void {
    const key = `${endpoint.method.toUpperCase()} ${endpoint.path}`;
    this.endpoints.set(key, endpoint);
    if (config.isDevelopment()) {
      log(`📝 Added API endpoint documentation: ${key}`, 'api-docs');
    }
  }

  /**
   * Generate all documentation formats
   */
  async generateDocumentation(): Promise<void> {
    const outputDir = this.config.outputDir;
    
    // Ensure output directory exists
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    if (config.isDevelopment()) {
      log('📚 Generating API documentation...', 'api-docs');
    }

    if (this.config.generateOpenApi) {
      await this.generateOpenApiSpec();
    }

    if (this.config.generateMarkdown) {
      await this.generateMarkdownDocs();
    }

    if (this.config.generatePostman) {
      await this.generatePostmanCollection();
    }

    await this.generateInteractiveHtml();
    
    if (config.isDevelopment()) {
      log('✅ API documentation generated successfully', 'api-docs');
      log(`📂 Documentation available in: ${outputDir}`, 'api-docs');
    }
  }

  private async generateOpenApiSpec(): Promise<void> {
    const openApiSpec = {
      openapi: '3.0.3',
      info: {
        title: this.config.title,
        description: this.config.description,
        version: this.config.version,
        contact: this.config.contact,
        license: this.config.license
      },
      servers: this.config.servers,
      paths: this.generatePaths(),
      components: {
        schemas: this.generateSchemas(),
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      },
      tags: this.generateTags()
    };

    const outputPath = join(this.config.outputDir, 'openapi.json');
    writeFileSync(outputPath, JSON.stringify(openApiSpec, null, 2));
    if (config.isDevelopment()) {
      log(`📄 OpenAPI specification: ${outputPath}`, 'api-docs');
    }
  }

  private generatePaths(): Record<string, any> {
    const paths: Record<string, any> = {};

    for (const [key, endpoint] of Array.from(this.endpoints.entries())) {
      const [method, path] = key.split(' ', 2);
      
      if (!paths[path]) {
        paths[path] = {};
      }

      paths[path][method.toLowerCase()] = {
        tags: endpoint.tags,
        summary: endpoint.summary,
        description: endpoint.description,
        parameters: endpoint.parameters,
        requestBody: endpoint.requestBody,
        responses: endpoint.responses,
        security: endpoint.security,
        deprecated: endpoint.deprecated
      };
    }

    return paths;
  }

  private generateSchemas(): Record<string, any> {
    return {
      User: {
        type: 'object',
        properties: {
          id: { type: 'integer' },
          email: { type: 'string', format: 'email' },
          firstName: { type: 'string' },
          lastName: { type: 'string' },
          phone: { type: 'string' },
          role: { type: 'string', enum: ['user', 'owner', 'admin'] },
          verified: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      Property: {
        type: 'object',
        properties: {
          id: { type: 'integer' },
          title: { type: 'string' },
          description: { type: 'string' },
          location: { type: 'string' },
          halfDayPrice: { type: 'number' },
          fullDayPrice: { type: 'number' },
          bedrooms: { type: 'integer' },
          bathrooms: { type: 'integer' },
          maxGuests: { type: 'integer' },
          amenities: { type: 'array', items: { type: 'string' } },
          images: { type: 'array', items: { type: 'string' } },
          featured: { type: 'boolean' },
          status: { type: 'string', enum: ['active', 'inactive', 'draft'] },
          ownerId: { type: 'integer' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      PropertyInput: {
        type: 'object',
        required: ['title', 'description', 'location', 'halfDayPrice', 'fullDayPrice'],
        properties: {
          title: { type: 'string', minLength: 1, maxLength: 200 },
          description: { type: 'string', minLength: 1 },
          location: { type: 'string' },
          halfDayPrice: { type: 'number', minimum: 0 },
          fullDayPrice: { type: 'number', minimum: 0 },
          bedrooms: { type: 'integer', minimum: 1 },
          bathrooms: { type: 'integer', minimum: 1 },
          maxGuests: { type: 'integer', minimum: 1 },
          amenities: { type: 'array', items: { type: 'string' } },
          images: { type: 'array', items: { type: 'string' } }
        }
      },
      Booking: {
        type: 'object',
        properties: {
          id: { type: 'integer' },
          propertyId: { type: 'integer' },
          userId: { type: 'integer' },
          checkIn: { type: 'string', format: 'date' },
          checkOut: { type: 'string', format: 'date' },
          guests: { type: 'integer' },
          totalAmount: { type: 'number' },
          status: { type: 'string', enum: ['pending', 'confirmed', 'cancelled', 'completed'] },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      BookingInput: {
        type: 'object',
        required: ['propertyId', 'checkIn', 'checkOut', 'guests'],
        properties: {
          propertyId: { type: 'integer' },
          checkIn: { type: 'string', format: 'date' },
          checkOut: { type: 'string', format: 'date' },
          guests: { type: 'integer', minimum: 1 }
        }
      },
      Pagination: {
        type: 'object',
        properties: {
          page: { type: 'integer' },
          limit: { type: 'integer' },
          total: { type: 'integer' },
          totalPages: { type: 'integer' },
          hasNext: { type: 'boolean' },
          hasPrev: { type: 'boolean' }
        }
      },
      Error: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          error: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' }
            }
          }
        }
      }
    };
  }

  private generateTags(): Array<{ name: string; description: string }> {
    const tags = new Set<string>();
    for (const endpoint of Array.from(this.endpoints.values())) {
      endpoint.tags.forEach((tag: string) => tags.add(tag));
    }

    return Array.from(tags).map((tag: string) => ({
      name: tag,
      description: this.getTagDescription(tag)
    }));
  }

  private getTagDescription(tag: string): string {
    const descriptions: Record<string, string> = {
      'Authentication': 'User authentication and authorization',
      'Properties': 'Property listing management',
      'Bookings': 'Booking creation and management',
      'Reviews': 'Property reviews and ratings',
      'Users': 'User profile management',
      'Admin': 'Administrative operations'
    };

    return descriptions[tag] || `${tag} related operations`;
  }

  private async generateMarkdownDocs(): Promise<void> {
    let markdown = `# ${this.config.title}\n\n`;
    markdown += `${this.config.description}\n\n`;
    markdown += `**Version:** ${this.config.version}\n\n`;

    // Table of contents
    markdown += '## Table of Contents\n\n';
    const tags = this.generateTags();
    for (const tag of tags) {
      markdown += `- [${tag.name}](#${tag.name.toLowerCase().replace(/\s+/g, '-')})\n`;
    }
    markdown += '\n';

    // Base URL
    markdown += '## Base URLs\n\n';
    for (const server of this.config.servers) {
      markdown += `- **${server.description}**: \`${server.url}\`\n`;
    }
    markdown += '\n';

    // Authentication
    markdown += '## Authentication\n\n';
    markdown += 'This API uses JWT Bearer token authentication. Include the token in the Authorization header:\n\n';
    markdown += '```\nAuthorization: Bearer <your-jwt-token>\n```\n\n';

    // Endpoints by tag
    for (const tag of tags) {
      markdown += `## ${tag.name}\n\n`;
      markdown += `${tag.description}\n\n`;

      const tagEndpoints = Array.from(this.endpoints.entries())
        .filter(([, endpoint]) => endpoint.tags.includes(tag.name));

      for (const [key, endpoint] of tagEndpoints) {
        const [method, path] = key.split(' ', 2);
        markdown += `### ${method} ${path}\n\n`;
        markdown += `${endpoint.description}\n\n`;

        if (endpoint.parameters.length > 0) {
          markdown += '#### Parameters\n\n';
          markdown += '| Name | Type | In | Required | Description |\n';
          markdown += '|------|------|----|---------|--------------|\n';
          for (const param of endpoint.parameters) {
            markdown += `| ${param.name} | ${param.schema.type} | ${param.in} | ${param.required ? 'Yes' : 'No'} | ${param.description} |\n`;
          }
          markdown += '\n';
        }

        if (endpoint.requestBody) {
          markdown += '#### Request Body\n\n';
          markdown += `${endpoint.requestBody.description}\n\n`;
          markdown += '```json\n';
          if (endpoint.requestBody.content['application/json']?.examples) {
            const examples = endpoint.requestBody.content['application/json'].examples!;
            const firstExample = Object.values(examples)[0];
            markdown += JSON.stringify(firstExample.value, null, 2);
          }
          markdown += '\n```\n\n';
        }

        markdown += '#### Responses\n\n';
        for (const [status, response] of Object.entries(endpoint.responses)) {
          markdown += `**${status}**: ${response.description}\n\n`;
        }
        markdown += '\n';
      }
    }

    const outputPath = join(this.config.outputDir, 'README.md');
    writeFileSync(outputPath, markdown);
    if (config.isDevelopment()) {
      log(`📄 Markdown documentation: ${outputPath}`, 'api-docs');
    }
  }

  private async generatePostmanCollection(): Promise<void> {
    const collection = {
      info: {
        name: this.config.title,
        description: this.config.description,
        version: this.config.version,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      variable: [
        {
          key: 'baseUrl',
          value: this.config.servers[0].url,
          type: 'string'
        },
        {
          key: 'authToken',
          value: '',
          type: 'string'
        }
      ],
      auth: {
        type: 'bearer',
        bearer: [
          {
            key: 'token',
            value: '{{authToken}}',
            type: 'string'
          }
        ]
      },
      item: this.generatePostmanItems()
    };

    const outputPath = join(this.config.outputDir, 'postman-collection.json');
    writeFileSync(outputPath, JSON.stringify(collection, null, 2));
    if (config.isDevelopment()) {
      log(`📄 Postman collection: ${outputPath}`, 'api-docs');
    }
  }

  private generatePostmanItems(): any[] {
    const items: any[] = [];
    const tagGroups: Record<string, any[]> = {};

    for (const [key, endpoint] of Array.from(this.endpoints.entries())) {
      const [method, path] = key.split(' ', 2);
      
      const item: any = {
        name: endpoint.summary,
        request: {
          method: method.toUpperCase(),
          header: [],
          url: {
            raw: `{{baseUrl}}${path}`,
            host: ['{{baseUrl}}'],
            path: path.split('/').filter((p: string) => p)
          }
        },
        response: []
      };

      // Add parameters
      if (endpoint.parameters.length > 0) {
        const queryParams = endpoint.parameters.filter((p: ApiParameter) => p.in === 'query');
        if (queryParams.length > 0) {
          item.request.url.query = queryParams.map((p: ApiParameter) => ({
            key: p.name,
            value: p.example || '',
            description: p.description,
            disabled: !p.required
          }));
        }
      }

      // Add request body
      if (endpoint.requestBody) {
        item.request.body = {
          mode: 'raw',
          raw: '',
          options: {
            raw: {
              language: 'json'
            }
          }
        };

        const content = endpoint.requestBody.content['application/json'];
        if (content?.examples) {
          const firstExample = Object.values(content.examples)[0] as ApiExample;
          item.request.body.raw = JSON.stringify(firstExample.value, null, 2);
        }
      }

      // Group by tags
      const tag = endpoint.tags[0] || 'General';
      if (!tagGroups[tag]) {
        tagGroups[tag] = [];
      }
      tagGroups[tag].push(item);
    }

    // Convert tag groups to Postman folders
    for (const [tag, tagItems] of Object.entries(tagGroups)) {
      items.push({
        name: tag,
        item: tagItems
      });
    }

    return items;
  }

  private async generateInteractiveHtml(): Promise<void> {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.config.title} - API Documentation</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        body { margin: 0; padding: 0; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script>
        SwaggerUIBundle({
            url: './openapi.json',
            dom_id: '#swagger-ui',
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
            ],
            layout: "StandaloneLayout",
            deepLinking: true,
            showExtensions: true,
            showCommonExtensions: true,
            tryItOutEnabled: true
        });
    </script>
</body>
</html>`;

    const outputPath = join(this.config.outputDir, 'index.html');
    writeFileSync(outputPath, html);
    if (config.isDevelopment()) {
      log(`📄 Interactive documentation: ${outputPath}`, 'api-docs');
    }
  }

  /**
   * Create Express middleware for serving documentation
   */
  createDocumentationMiddleware(): (req: Request, res: Response, next: Function) => void {
    return (req: Request, res: Response, next: Function) => {
      if (req.path.startsWith('/docs')) {
        const filePath = req.path.replace('/docs', '');
        const fullPath = join(this.config.outputDir, filePath || 'index.html');
        
        try {
          res.sendFile(fullPath);
        } catch (error) {
          res.status(404).json({ error: 'Documentation not found' });
        }
        return;
      }
      
      next();
    };
  }

  /**
   * Auto-discover routes from Express app
   */
  discoverRoutes(app: Express): void {
    if (!this.config.autoDetectRoutes) {
      return;
    }

    this.app = app;
    if (config.isDevelopment()) {
      log('🔍 Auto-discovering API routes...', 'api-docs');
    }

    // This is a simplified route discovery
    // In a real implementation, you'd need to traverse the Express router stack
    const routes = this.extractRoutesFromApp(app);
    
    for (const route of routes) {
      if (!this.endpoints.has(`${route.method.toUpperCase()} ${route.path}`)) {
        this.addDiscoveredEndpoint(route);
      }
    }
  }

  private extractRoutesFromApp(app: Express): Array<{ method: string; path: string }> {
    // This is a placeholder - actual implementation would inspect Express router
    const routes: Array<{ method: string; path: string }> = [];
    
    // You would need to traverse app._router.stack to get actual routes
    // For now, return empty array
    
    return routes;
  }

  private addDiscoveredEndpoint(route: { method: string; path: string }): void {
    // Create basic endpoint documentation for discovered routes
    this.addEndpoint({
      path: route.path,
      method: route.method.toUpperCase(),
      summary: `${route.method.toUpperCase()} ${route.path}`,
      description: `Auto-discovered endpoint for ${route.path}`,
      tags: [this.inferTagFromPath(route.path)],
      parameters: [],
      responses: {
        '200': { description: 'Success' },
        '400': { description: 'Bad Request' },
        '500': { description: 'Internal Server Error' }
      },
      examples: []
    });
  }

  private inferTagFromPath(path: string): string {
    if (path.includes('/auth')) return 'Authentication';
    if (path.includes('/properties')) return 'Properties';
    if (path.includes('/bookings')) return 'Bookings';
    if (path.includes('/reviews')) return 'Reviews';
    if (path.includes('/users')) return 'Users';
    return 'General';
  }

  /**
   * Get documentation generation status
   */
  getGenerationStatus(): {
    endpoints: number;
    tags: string[];
    lastGenerated?: Date;
    outputDir: string;
  } {
    return {
      endpoints: this.endpoints.size,
      tags: Array.from(new Set(Array.from(this.endpoints.values()).flatMap(e => e.tags))),
      outputDir: this.config.outputDir
    };
  }
}

// Singleton instance
let _apiDocGenerator: ApiDocumentationGenerator | null = null;

export function getApiDocumentationGenerator(config?: Partial<ApiDocumentationConfig>): ApiDocumentationGenerator {
  if (!_apiDocGenerator) {
    _apiDocGenerator = new ApiDocumentationGenerator(config);
  }
  return _apiDocGenerator;
}

// Development middleware for automatic documentation generation
export function createApiDocMiddleware(config?: Partial<ApiDocumentationConfig>) {
  const generator = getApiDocumentationGenerator(config);
  
  return {
    documentationMiddleware: generator.createDocumentationMiddleware(),
    generateDocs: () => generator.generateDocumentation(),
    addEndpoint: (endpoint: ApiEndpoint) => generator.addEndpoint(endpoint),
    discoverRoutes: (app: Express) => generator.discoverRoutes(app)
  };
}

export default ApiDocumentationGenerator;