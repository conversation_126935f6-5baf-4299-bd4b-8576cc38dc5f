import { log } from '../utils/logger';

interface SecurityConfig {
  secrets: {
    jwtSecret: string;
    cookieSecret: string;
    sessionSecret: string;
  };
  database: {
    url: string;
    host: string;
    port: number;
    user: string;
    password: string;
    name: string;
  };
  external: {
    twilio: {
      accountSid: string;
      authToken: string;
      messagingServiceSid: string;
    };
    sendgrid: {
      apiKey: string;
    };
    cloudinary: {
      apiKey: string;
      apiSecret: string;
      cloudName: string;
    };
    dlt: {
      entityId: string;
    };
  };
  rateLimit: {
    enabled: boolean;
    api: { maxRequests: number; windowMs: number };
    auth: { maxRequests: number; windowMs: number };
    otp: { maxRequests: number; windowMs: number };
  };
  security: {
    headers: boolean;
    csrf: boolean;
    cors: boolean;
    helmet: boolean;
  };
}

class SimpleSecurityConfig {
  private static instance: SimpleSecurityConfig;
  private securityConfig: SecurityConfig | null = null;

  static getInstance(): SimpleSecurityConfig {
    if (!SimpleSecurityConfig.instance) {
      SimpleSecurityConfig.instance = new SimpleSecurityConfig();
    }
    return SimpleSecurityConfig.instance;
  }

  getConfig(): SecurityConfig {
    if (!this.securityConfig) {
      this.securityConfig = this.loadFromEnvironment();
    }
    return this.securityConfig;
  }

  private loadFromEnvironment(): SecurityConfig {
    // Load directly from environment variables
    const requiredSecrets = {
      jwtSecret: process.env.JWT_SECRET,
      cookieSecret: process.env.COOKIE_SECRET,
      sessionSecret: process.env.SESSION_SECRET,
      databaseUrl: process.env.DATABASE_URL
    };

    // Validate required secrets
    for (const [key, value] of Object.entries(requiredSecrets)) {
      if (!value) {
        const error = `Required environment variable ${key.toUpperCase()} is not set`;
        log(`❌ ${error}`, 'security-config');
        throw new Error(error);
      }
    }

    return {
      secrets: {
        jwtSecret: requiredSecrets.jwtSecret!,
        cookieSecret: requiredSecrets.cookieSecret!,
        sessionSecret: requiredSecrets.sessionSecret!
      },
      database: {
        url: requiredSecrets.databaseUrl!,
        host: process.env.PGHOST || 'localhost',
        port: parseInt(process.env.PGPORT || '5432'),
        user: process.env.PGUSER || 'postgres',
        password: process.env.PGPASSWORD || '',
        name: process.env.PGDATABASE || 'farmhouse'
      },
      external: {
        twilio: {
          accountSid: process.env.TWILIO_ACCOUNT_SID || '',
          authToken: process.env.TWILIO_AUTH_TOKEN || '',
          messagingServiceSid: process.env.TWILIO_MESSAGING_SID || ''
        },
        sendgrid: {
          apiKey: process.env.SENDGRID_API_KEY || ''
        },
        cloudinary: {
          apiKey: process.env.CLOUDINARY_API_KEY || '',
          apiSecret: process.env.CLOUDINARY_API_SECRET || '',
          cloudName: process.env.CLOUDINARY_CLOUD_NAME || ''
        },
        dlt: {
          entityId: process.env.DLT_ENTITY_ID || ''
        }
      },
      rateLimit: {
        enabled: process.env.ENABLE_RATE_LIMITING !== 'false',
        api: {
          maxRequests: parseInt(process.env.API_RATE_LIMIT_MAX || '100'),
          windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW || '60000')
        },
        auth: {
          maxRequests: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5'),
          windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW || '900000')
        },
        otp: {
          maxRequests: parseInt(process.env.OTP_RATE_LIMIT_MAX || '3'),
          windowMs: parseInt(process.env.OTP_RATE_LIMIT_WINDOW || '300000')
        }
      },
      security: {
        headers: process.env.ENABLE_SECURITY_HEADERS !== 'false',
        csrf: process.env.ENABLE_CSRF !== 'false',
        cors: process.env.ENABLE_CORS !== 'false',
        helmet: process.env.ENABLE_HELMET !== 'false'
      }
    };
  }

  // Get specific configuration sections
  getSecrets() {
    return this.getConfig().secrets;
  }

  getDatabaseConfig() {
    return this.getConfig().database;
  }

  getExternalConfig() {
    return this.getConfig().external;
  }

  getRateLimitConfig() {
    return this.getConfig().rateLimit;
  }

  getSecurityConfig() {
    return this.getConfig().security;
  }

  // Validate configuration
  validateConfig(): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const securityConfig = this.getConfig();

      // Validate secrets
      if (!securityConfig.secrets.jwtSecret || securityConfig.secrets.jwtSecret.length < 32) {
        errors.push('JWT secret must be at least 32 characters long');
      }

      if (!securityConfig.secrets.cookieSecret || securityConfig.secrets.cookieSecret.length < 32) {
        errors.push('Cookie secret must be at least 32 characters long');
      }

      if (!securityConfig.secrets.sessionSecret || securityConfig.secrets.sessionSecret.length < 32) {
        errors.push('Session secret must be at least 32 characters long');
      }

      // Validate database configuration
      if (!securityConfig.database.url) {
        errors.push('Database URL is required');
      }

      // Validate external services (warnings only)
      if (!securityConfig.external.twilio.accountSid) {
        warnings.push('Twilio Account SID not configured - SMS functionality will be limited');
      }

      if (!securityConfig.external.twilio.authToken) {
        warnings.push('Twilio Auth Token not configured - SMS functionality will be limited');
      }

      if (!securityConfig.external.sendgrid.apiKey) {
        warnings.push('SendGrid API Key not configured - email functionality will be limited');
      }

      if (!securityConfig.external.cloudinary.apiKey) {
        warnings.push('Cloudinary API Key not configured - image upload functionality will be limited');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  // Health check
  healthCheck(): {
    healthy: boolean;
    configValid: boolean;
    errors: string[];
  } {
    try {
      const validation = this.validateConfig();
      
      return {
        healthy: validation.valid,
        configValid: validation.valid,
        errors: validation.errors
      };
    } catch (error) {
      return {
        healthy: false,
        configValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }
}

// Export singleton instance
export const simpleSecurityConfig = SimpleSecurityConfig.getInstance();

// Helper function to initialize and validate
export function initializeSimpleSecurityConfig(): void {
  const validation = simpleSecurityConfig.validateConfig();
  
  if (!validation.valid) {
    log(`❌ Security configuration validation failed: ${validation.errors.join(', ')}`, 'security-config');
    throw new Error(`Security configuration validation failed: ${validation.errors.join(', ')}`);
  }
  
  if (validation.warnings.length > 0) {
    log(`⚠️ Security configuration warnings: ${validation.warnings.join(', ')}`, 'security-config');
  }

  log('✅ Simple security configuration loaded successfully', 'security-config');
}

export default simpleSecurityConfig;