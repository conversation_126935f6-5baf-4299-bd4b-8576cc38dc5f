import express from "express";
import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import multer from "multer";
import { fileURLToPath } from "url";
import { dirname, join, extname } from "path";
import fs from "fs";
import crypto from "crypto";
import { emailService } from "./emailService";
import { cloudinaryService } from "./cloudinaryService";
import rateLimit from "express-rate-limit";
import cors from "cors";
import { config } from "./config";
import { 
  sanitizeSearchQuery, 
  sanitizeIdParam, 
  rateLimit as customRateLimit,
  preventSQLInjection 
} from "./middlewares/sanitization";
import {
  userRegisterSchema,
  userLoginSchema,
  propertyFormSchema,
  bookingFormSchema,
  reviewFormSchema,
  reviewResponseSchema,
} from "../shared/schema.ts";
import { ZodError } from "zod";
import { fromZodError } from "zod-validation-error";
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendAuthenticationError,
  sendAuthorizationError,
  sendNotFoundError,
  sendConflictError,
  sendInternalError,
  sendRateLimitError,
  getRequestId,
  withErrorHandling
} from "../shared/api-response-utils";
import { ERROR_CODES } from "../shared/api-response-types";
const UPLOADS_DIR = join(process.cwd(), "uploads");

// Rate limiting configuration for security
const MAX_LOGIN_ATTEMPTS = 5;
const loginAttemptsMap = new Map<string, number>();
const blacklistedTokens = new Set<string>(); // For storing revoked tokens

// Rate limiting middleware configurations
const generalApiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: "Too many requests from this IP, please try again later.",
    retryAfter: 15 * 60
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Skip successful requests to /api/health
  skip: (req) => req.path === '/api/health'
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit auth requests to 20 per windowMs
  message: {
    error: "Too many authentication attempts from this IP, please try again later.",
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const bookingLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // Limit booking requests to 5 per minute
  message: {
    error: "Too many booking attempts from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Lazy rate limiter factory functions
function createOtpSendLimiter() {
  return rateLimit({
    windowMs: config.isDevelopment() ? 60 * 1000 : 10 * 60 * 1000, // 1 min dev, 10 min prod
    max: config.isDevelopment() ? 50 : 5, // 50 requests in dev, 5 in prod
    message: {
      error: config.isDevelopment() 
        ? "Too many OTP requests. Please wait 1 minute before trying again."
        : "Too many OTP requests from this IP. Please wait 10 minutes before trying again.",
      retryAfter: config.isDevelopment() ? 60 : 10 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Rate limit by IP + identifier to prevent abuse of different phone/email
      const identifier = req.body?.identifier || req.ip;
      return `${req.ip}:${identifier}`;
    }
  });
}

function createOtpVerifyLimiter() {
  return rateLimit({
    windowMs: config.isDevelopment() ? 60 * 1000 : 5 * 60 * 1000, // 1 min dev, 5 min prod
    max: config.isDevelopment() ? 100 : 10, // 100 attempts in dev, 10 in prod
    message: {
      error: config.isDevelopment()
        ? "Too many verification attempts. Please wait 1 minute before trying again."
        : "Too many verification attempts from this IP. Please wait 5 minutes before trying again.",
      retryAfter: config.isDevelopment() ? 60 : 5 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Rate limit by IP + identifier for verification attempts
      const identifier = req.body?.identifier || req.ip;
      return `${req.ip}:${identifier}`;
    }
  });
}

// Lazy initialization of rate limiters
let otpSendLimiter: any = null;
let otpVerifyLimiter: any = null;

function getOtpSendLimiter() {
  if (!otpSendLimiter) {
    otpSendLimiter = createOtpSendLimiter();
  }
  return otpSendLimiter;
}

function getOtpVerifyLimiter() {
  if (!otpVerifyLimiter) {
    otpVerifyLimiter = createOtpVerifyLimiter();
  }
  return otpVerifyLimiter;
}

const searchLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute  
  max: 30, // Limit search requests to 30 per minute
  message: {
    error: "Too many search requests from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const imageLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 200, // Allow many image requests per minute
  message: {
    error: "Too many image requests from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Ensure uploads directory exists
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

// Configure multer for memory storage (for Cloudinary upload)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max file size
    files: 10 // Maximum 10 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Check MIME type for both images and videos
    const allowedImageMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoMimes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/quicktime'];
    const allowedMimes = [...allowedImageMimes, ...allowedVideoMimes];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Only JPEG, PNG, GIF, WebP images and MP4, AVI, MOV, WMV videos are allowed"));
    }
  },
});

// Authentication middleware with enhanced security
const authenticate = (req: Request, res: Response, next: NextFunction) => {
  // Try to get token from Authorization header first, then from cookie
  const authHeader = req.headers.authorization;
  const cookieToken = req.cookies?.auth_token;

  // No token found
  if (!authHeader && !cookieToken) {
    return sendAuthenticationError(res, "Authentication required", getRequestId(req));
  }

  // Get token from appropriate source
  const token = authHeader ? authHeader.split(" ")[1] : cookieToken;

  try {
    // Check if token is blacklisted
    if (blacklistedTokens.has(token)) {
      return sendAuthenticationError(res, "Token has been revoked", getRequestId(req));
    }

    // Verify token with full options
    const decoded = jwt.verify(token, config.jwt.secret, {
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithms: ['HS256'] // Explicitly limit algorithms
    }) as { userId: number; role: string; jti?: string };

    // Assign user data to request
    req.user = {
      userId: decoded.userId,
      role: decoded.role
    };

    return next();
  } catch (error: any) {
    // Provide limited information about the error
    const requestId = getRequestId(req);
    if (error instanceof jwt.TokenExpiredError) {
      return sendAuthenticationError(res, "Session expired, please log in again", requestId);
    } else if (error instanceof jwt.JsonWebTokenError) {
      return sendAuthenticationError(res, "Invalid token", requestId);
    }

    return sendAuthenticationError(res, "Authentication failed", requestId);
  }
};

// Role-based access control middleware
const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return sendAuthenticationError(res, "Authentication required", getRequestId(req));
    }

    if (roles.includes(req.user.role)) {
      return next();
    } else {
      return sendAuthorizationError(res, "Insufficient permissions", getRequestId(req));
    }
  };
};

// Validation middleware
const validate = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = schema.parse(req.body);
      req.body = result; // Update req.body with parsed/transformed data
      return next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationError = fromZodError(error);
        return sendValidationError(
          res, 
          "Validation error", 
          validationError.details,
          getRequestId(req)
        );
      }
      return next(error);
    }
  };
};

export async function registerRoutes(app: Express): Promise<Server> {

  // CORS configuration with dynamic origin checking
  app.use(cors({
    origin: (origin, callback) => {
      // Always allow requests with no origin (like mobile apps, Postman, same-origin)
      if (!origin) {
        return callback(null, true);
      }

      // In development, be more permissive
      if (process.env.NODE_ENV === 'development') {
        // Allow common development origins
        const devOrigins = [
          'http://localhost:3000',
          'http://localhost:5000',
          'http://127.0.0.1:3000',
          'http://127.0.0.1:5000'
        ];

        // Allow Replit domains and dev origins
        if (origin.includes('.replit.dev') || origin.includes('.repl.co') || origin.includes('.replit.app') || devOrigins.includes(origin)) {
          return callback(null, true);
        }

        // In development, also allow any localhost or 127.0.0.1 origin
        if (origin.startsWith('http://localhost') || origin.startsWith('http://127.0.0.1')) {
          return callback(null, true);
        }
      }

      // Check for Replit domains first (works for both dev and prod)
      if (origin.includes('.replit.app') || origin.includes('.replit.dev') || origin.includes('.repl.co')) {
        return callback(null, true);
      }

      // Additional allowed origins based on environment
      const allowedOrigins = config.isDevelopment() ? [
        // Development origins - more permissive
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:5000',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:5000'
      ] : [
        // Production origins - strict whitelist
        'https://bookafarm.com'
      ];

      // Check against additional allowed origins
      for (const allowedOrigin of allowedOrigins) {
        if (origin === allowedOrigin) {
          return callback(null, true);
        }
      }

      // For safety, log rejected origins but don't crash the server
      console.warn(`CORS: Rejected origin: ${origin}`);
      return callback(null, false);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
    exposedHeaders: ['Set-Cookie']
  }));

  // Apply general rate limiting to all API routes
  app.use("/api", generalApiLimiter);

  // Security headers middleware for API routes
  app.use("/api", (req, res, next) => {
    // Ensure JSON content type for API responses
    res.header("Content-Type", "application/json");

    // Additional security headers
    res.header("X-Content-Type-Options", "nosniff");
    res.header("X-Frame-Options", "DENY");
    res.header("X-XSS-Protection", "1; mode=block");
    res.header("Referrer-Policy", "strict-origin-when-cross-origin");

    // Prevent caching of sensitive API responses
    if (req.path.includes('/auth') || req.path.includes('/profile')) {
      res.header("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");
      res.header("Pragma", "no-cache");
      res.header("Expires", "0");
      res.header("Surrogate-Control", "no-store");
    }

    next();
  });

  // Serve uploaded files
  app.use("/uploads", express.static(UPLOADS_DIR));

  // Development helper endpoint for authentication info
  if (config.isDevelopment()) {
    app.get("/api/dev/auth-info", (req: Request, res: Response) => {
      res.json({
        environment: "development",
        testNumbers: [
          "+919999999999",
          "+919000999888", 
          "+919391010188"
        ],
        masterCode: "999999",
        rateLimits: {
          otpSend: "50 requests per minute",
          otpVerify: "100 attempts per minute"
        },
        instructions: [
          "Use any of the test phone numbers for development",
          "These numbers will automatically use the master code: 999999",
          "No SMS charges will be incurred for test numbers",
          "Rate limits are relaxed in development mode"
        ]
      });
    });

    // Create test users for development (auto-creates if not exists)
    app.post("/api/dev/create-test-users", async (req: Request, res: Response) => {
      try {
        const testUsers = [
          {
            phone: "+919999999999",
            email: "<EMAIL>",
            fullName: "Test User One",
            username: "testuser1",
            role: "user"
          },
          {
            phone: "+919000999888",
            email: "<EMAIL>", 
            fullName: "Test User Two",
            username: "testuser2",
            role: "user"
          },
          {
            phone: "+919391010188",
            email: "<EMAIL>",
            fullName: "Test Property Owner",
            username: "testowner1",
            role: "owner"
          }
        ];

        const createdUsers = [];
        for (const userData of testUsers) {
          try {
            // Check if user already exists
            const existingUser = await storage.getUserByPhone(userData.phone);
            if (!existingUser) {
              const user = await storage.createUser(userData);
              createdUsers.push(user);
            }
          } catch (error) {
            console.log(`Test user ${userData.phone} already exists or creation failed`);
          }
        }

        res.json({
          message: "Test users setup completed",
          createdCount: createdUsers.length,
          availableTestNumbers: testUsers.map(u => u.phone),
          masterCode: "999999"
        });
      } catch (error) {
        console.error("Error creating test users:", error);
        res.status(500).json({ error: "Failed to create test users" });
      }
    });
  }

  // Image proxy to handle external images blocked by network policies
  app.get("/api/proxy-image", imageLimiter, async (req: Request, res: Response) => {
    try {
      const { url } = req.query;
      
      if (!url || typeof url !== 'string') {
        return res.status(400).json({ error: "URL parameter is required" });
      }

      // Validate that it's a trusted image source
      const allowedDomains = [
        'images.unsplash.com',
        'unsplash.com',
        'res.cloudinary.com'
      ];
      
      const urlObj = new URL(url);
      if (!allowedDomains.includes(urlObj.hostname)) {
        return res.status(403).json({ error: "Domain not allowed" });
      }

      // Fetch the image
      const response = await fetch(url);
      
      if (!response.ok) {
        return res.status(response.status).json({ error: "Failed to fetch image" });
      }

      // Get the content type and ensure it's an image
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        return res.status(400).json({ error: "Invalid image content type" });
      }

      // Set appropriate headers for image serving
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
      res.setHeader('Access-Control-Allow-Origin', '*');
      
      // Stream the image data
      const imageBuffer = await response.arrayBuffer();
      return res.send(Buffer.from(imageBuffer));
      
    } catch (error) {
      console.error('Image proxy error:', error);
      return res.status(500).json({ error: "Internal server error" });
    }
  });

  // Profile Routes
  app.get("/api/profile", authenticate, async (req, res) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Return user profile without password
      const { password, ...profile } = user;
      return res.json(profile);
    } catch (error) {
      console.error("Error fetching profile:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  });

  app.patch("/api/profile", authenticate, async (req, res) => {
    // Explicitly set response headers to ensure JSON response
    res.setHeader('Content-Type', 'application/json; charset=utf-8');

    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401);
        return res.end(JSON.stringify({ error: "Unauthorized" }));
      }

      const { phone, address, bio } = req.body;

      // Only include editable fields in the update
      const updateData: any = {};
      if (phone !== undefined) updateData.phone = phone || null;
      if (address !== undefined) updateData.address = address || null;
      if (bio !== undefined) updateData.bio = bio || null;


      const updatedUser = await storage.updateUser(userId, updateData);

      if (!updatedUser) {
        res.status(404);
        return res.end(JSON.stringify({ error: "User not found" }));
      }

      // Return updated profile without password
      const { password, ...profile } = updatedUser;

      res.status(200);
      return res.end(JSON.stringify(profile));
    } catch (error) {
      console.error("Error updating profile:", error);
      res.status(500);
      return res.end(JSON.stringify({ error: "Internal server error", details: (error as Error).message }));
    }
  });

  // Authentication Routes
  app.post("/api/auth/register", authLimiter, validate(userRegisterSchema), withErrorHandling(async (req, res) => {
    const requestId = getRequestId(req);
    const { email, consentData } = req.body;

    // Check if user already exists
    const existingUser = await storage.getUserByEmail(email);
    if (existingUser) {
      return sendConflictError(res, "User with this email already exists", requestId);
    }

      // Prepare user data with consent information
      const userData = {
        ...req.body,
        termsAccepted: consentData?.termsAccepted || false,
        privacyPolicyAccepted: consentData?.termsAccepted || false, // Same checkbox covers all legal docs
        cookiePolicyAccepted: consentData?.termsAccepted || false, // Same checkbox covers all legal docs
        dataProcessingConsent: consentData?.dataProcessingConsent || false,
        marketingConsent: consentData?.marketingConsent || false,
        consentTimestamp: new Date()
      };

      // Remove the consent data object to avoid database schema conflicts
      delete userData.consentData;

      // Create new user with consent tracking
      const user = await storage.createUser(userData);

      // Generate token
      const token = jwt.sign(
        { userId: user.id, role: user.role },
        config.jwt.secret,
        { expiresIn: "7d" }
      );

      // Set the token as an HTTP-only cookie for better security
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

    const responseData = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role
      },
      token 
    };
    
    return sendSuccess(res, responseData, "User registered successfully", 201, requestId);
  }));

  app.post("/api/auth/login", authLimiter, validate(userLoginSchema), withErrorHandling(async (req, res) => {
    const requestId = getRequestId(req);
    // Implement rate limiting for login attempts
    // This is a simple implementation. In production, use a more robust solution like rate-limit-redis
    const clientIP = typeof req.ip === 'string' ? req.ip : 'unknown';
    const loginAttempts = loginAttemptsMap.get(clientIP) || 0;

    if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
      return sendRateLimitError(
        res, 
        "Too many login attempts. Please try again later.", 
        requestId,
        15 * 60 // 15 minutes in seconds
      );
    }

    const { email, password } = req.body;

    // Find user by email
    const user = await storage.getUserByEmail(email);
    if (!user) {
      // Increment failed attempts
      loginAttemptsMap.set(clientIP, loginAttempts + 1);

      // Schedule cleanup of attempts after the cooldown period
      setTimeout(() => {
        loginAttemptsMap.delete(clientIP);
      }, 15 * 60 * 1000); // 15 minutes

      return sendAuthenticationError(res, "Invalid email or password", requestId);
    }

    // Verify password with constant time comparison to prevent timing attacks
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      // Increment failed attempts
      loginAttemptsMap.set(clientIP, loginAttempts + 1);

      // Schedule cleanup of attempts after the cooldown period
      setTimeout(() => {
        loginAttemptsMap.delete(clientIP);
      }, 15 * 60 * 1000); // 15 minutes

      return sendAuthenticationError(res, "Invalid email or password", requestId);
    }

      // Reset login attempts on successful login
      loginAttemptsMap.delete(clientIP);

      // Generate token with shorter expiration time
      const token = jwt.sign(
        { 
          userId: user.id, 
          role: user.role,
          // Add random jitter to prevent token reuse
          jti: crypto.randomBytes(16).toString('hex')
        },
        config.jwt.secret,
        { 
          expiresIn: "1d", // Shorter expiration time for better security
          audience: 'farmhouse-rental-app',
          issuer: 'farmhouse-rental-api'
        }
      );

      // Set token in HTTP-only cookie for better security
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 1 day
      });

    // Also return token in response for API compatibility
    const responseData = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role
      },
      token 
    };
    
    return sendSuccess(res, responseData, "Login successful", 200, requestId);
  }));

  app.get("/api/auth/me", authenticate, withErrorHandling(async (req, res) => {
    const requestId = getRequestId(req);
    const user = await storage.getUser(req.user!.userId);
    if (!user) {
      return sendNotFoundError(res, "User", requestId);
    }


    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      phone: user.phone
    };
    
    return sendSuccess(res, userData, null, 200, requestId);
  }));

  // Logout endpoint to invalidate tokens
  app.post("/api/auth/logout", authenticate, (req, res) => {
    try {
      // Get the token
      const authHeader = req.headers.authorization;
      const cookieToken = req.cookies?.auth_token;
      const token = authHeader ? authHeader.split(" ")[1] : cookieToken;

      if (token) {
        // Add token to blacklist
        blacklistedTokens.add(token);

        // Set a timeout to remove the token from blacklist after it expires
        // This prevents the blacklist from growing indefinitely
        try {
          const decoded = jwt.decode(token) as { exp?: number };
          if (decoded && decoded.exp) {
            const expiryTime = decoded.exp * 1000 - Date.now() + 10000; // Add 10s buffer
            setTimeout(() => {
              blacklistedTokens.delete(token);
            }, expiryTime > 0 ? expiryTime : 0);
          }
        } catch (e) {
          // If decoding fails, set a default expiry of 24 hours
          setTimeout(() => {
            blacklistedTokens.delete(token);
          }, 24 * 60 * 60 * 1000);
        }
      }

      // Clear the cookie
      res.clearCookie('auth_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/'
      });

      return res.json({ message: "Successfully logged out" });
    } catch (error) {
      console.error("Logout error:", error);
      return res.status(500).json({ message: "Server error during logout" });
    }
  });

  // Property Routes
  app.get("/api/properties", searchLimiter, sanitizeSearchQuery, async (req, res) => {
    try {
      // Only filter by featured if explicitly requested (middleware already transforms string to boolean)
      const featured = req.query.featured === 'true' ? true : req.query.featured === 'false' ? false : undefined;
      const location = req.query.location as string | undefined;
      const date = req.query.date ? new Date(req.query.date as string) : undefined;

      // Handle new advanced search parameters
      const minPrice = req.query.minPrice ? parseFloat(req.query.minPrice as string) : undefined;
      const maxPrice = req.query.maxPrice ? parseFloat(req.query.maxPrice as string) : undefined;
      const amenities = req.query.amenities ? (req.query.amenities as string).split(',') : undefined;


      const properties = await storage.getProperties(
        featured, 
        location, 
        date, 
        minPrice, 
        maxPrice, 
        amenities
      );

      return res.json(properties);
    } catch (error) {
      console.error("Get properties error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/properties/:id", sanitizeIdParam, async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id || '0');
      if (isNaN(propertyId) || propertyId <= 0) {
        return res.status(400).json({ message: "Invalid property ID" });
      }
      const property = await storage.getProperty(propertyId);

      if (!property) {
        return res.status(404).json({ message: "Property not found" });
      }

      return res.json(property);
    } catch (error) {
      console.error("Get property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/properties", authenticate, authorize(["owner"]), validate(propertyFormSchema), async (req, res) => {
    try {
      const property = {
        ...req.body,
        ownerId: req.user!.userId
      };

      const newProperty = await storage.createProperty(property);
      return res.status(201).json(newProperty);
    } catch (error) {
      console.error("Create property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.put("/api/properties/:id", authenticate, authorize(["owner"]), sanitizeIdParam, async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id);

      // Verify ownership
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return res.status(404).json({ message: "Property not found" });
      }

      if (property.ownerId !== req.user!.userId) {
        return res.status(403).json({ message: "You don't have permission to update this property" });
      }

      const updatedProperty = await storage.updateProperty(propertyId, req.body);
      return res.json(updatedProperty);
    } catch (error) {
      console.error("Update property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/properties/:id", authenticate, authorize(["owner"]), async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id);

      // Verify ownership
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return res.status(404).json({ message: "Property not found" });
      }

      if (property.ownerId !== req.user!.userId) {
        return res.status(403).json({ message: "You don't have permission to delete this property" });
      }

      await storage.deleteProperty(propertyId);
      return res.status(204).end();
    } catch (error) {
      console.error("Delete property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/properties/owner/me", authenticate, authorize(["owner"]), async (req, res) => {
    try {
      const properties = await storage.getPropertiesByOwner(req.user!.userId);
      return res.json(properties);
    } catch (error) {
      console.error("Get owner properties error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Cloudinary Image Upload Routes
  app.post("/api/upload/images", authenticate, upload.array('images', 10), async (req, res) => {
    try {
      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        return res.status(400).json({ 
          success: false,
          message: "No files provided for upload" 
        });
      }

      // Check if Cloudinary is properly configured
      if (!cloudinaryService.isInitialized()) {
        console.error("Cloudinary service not initialized");
        return res.status(503).json({ 
          success: false,
          message: "Image upload service is temporarily unavailable. Please check Cloudinary configuration." 
        });
      }

      // Convert multer files to the format expected by cloudinaryService
      const files = (req.files as Express.Multer.File[]).map(file => ({
        buffer: file.buffer,
        filename: file.originalname
      }));

      console.log(`Starting upload of ${files.length} files to Cloudinary`);

      const results = await cloudinaryService.uploadMultipleImages(files);

      const imageUrls = results.map(result => ({
        url: result.secure_url,
        publicId: result.public_id,
        width: result.width,
        height: result.height,
        format: result.format,
        bytes: result.bytes
      }));

      console.log(`Successfully uploaded ${imageUrls.length} images`);

      return res.json({ 
        success: true,
        images: imageUrls,
        count: imageUrls.length
      });
    } catch (error) {
      console.error("Cloudinary upload error:", error);
      return res.status(500).json({ 
        success: false,
        message: "Failed to upload images",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Delete images from Cloudinary
  app.delete("/api/upload/images", authenticate, async (req, res) => {
    try {
      const { publicIds } = req.body;

      if (!publicIds || !Array.isArray(publicIds) || publicIds.length === 0) {
        return res.status(400).json({ message: "No image IDs provided" });
      }

      if (!cloudinaryService.isInitialized()) {
        return res.status(503).json({ 
          message: "Image service unavailable. Please check configuration." 
        });
      }

      const results = await cloudinaryService.deleteMultipleImages(publicIds);

      return res.json({
        success: true,
        deleted: results.success,
        failed: results.failed,
        deletedCount: results.success.length,
        failedCount: results.failed.length
      });
    } catch (error) {
      console.error("Cloudinary delete error:", error);
      return res.status(500).json({ 
        message: "Failed to delete images",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Booking Routes
  app.get("/api/bookings", authenticate, async (req, res) => {
    try {
      const allBookings = await storage.getBookings(req.user!.userId);
      
      // Filter out bookings with 'pending_payment' status to hide incomplete transactions
      const bookings = allBookings.filter(booking => booking.status !== 'pending_payment');

      // Get property details for each booking
      const bookingsWithProperties = await Promise.all(
        bookings.map(async (booking) => {
          const property = await storage.getProperty(booking.propertyId);
          return {
            ...booking,
            property: property ? {
              id: property.id,
              title: property.title,
              location: property.location,
              images: property.images
            } : null
          };
        })
      );

      return res.json(bookingsWithProperties);
    } catch (error) {
      console.error("Get user bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });


  app.get("/api/bookings/user", authenticate, async (req, res) => {
    try {
      const allBookings = await storage.getBookings(req.user!.userId);
      
      // Filter out bookings with 'pending_payment' status to hide incomplete transactions
      const confirmedBookings = allBookings.filter(booking => booking.status !== 'pending_payment');
      
      return res.json(confirmedBookings);
    } catch (error) {
      console.error("Get user bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/user/:userId", authenticate, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      // Check if user is requesting their own bookings or is an admin
      if (req.user!.userId !== userId && req.user!.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }

      const bookings = await storage.getBookingsWithProperty(userId);
      return res.json(bookings);
    } catch (error) {
      console.error("Get user bookings with property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/owner", authenticate, authorize(["owner"]), async (req, res) => {
    try {
      const bookings = await storage.getBookingsByOwner(req.user!.userId);
      return res.json(bookings);
    } catch (error) {
      console.error("Get owner bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/property/:id", async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id);
      const bookings = await storage.getBookingsByProperty(propertyId);

      // For privacy, only return the dates, booking types, and status - not user information
      const bookingDates = bookings.map(booking => ({
        date: booking.bookingDate,
        type: booking.bookingType,
        status: booking.status
      }));

      return res.json(bookingDates);
    } catch (error) {
      console.error("Get property bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/bookings", bookingLimiter, authenticate, validate(bookingFormSchema), async (req, res) => {
    try {
      const { propertyId, bookingDate, bookingType, guests, paymentMethod } = req.body;

      // Check property exists
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return res.status(404).json({ message: "Property not found" });
      }

      // Convert date string to Date object
      const dateObj = new Date(bookingDate);

      // Validate the date
      if (isNaN(dateObj.getTime())) {
        return res.status(400).json({ message: "Invalid date format" });
      }

      // Check if the date is in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (dateObj < today) {
        return res.status(400).json({ message: "Cannot book dates in the past" });
      }

      // Format date for availability check
      const formattedDate = dateObj.toISOString().split('T')[0];

      // Calculate price based on booking type
      const price = bookingType === 'morning' ? property.halfDayPrice : property.fullDayPrice;

      // Set booking status based on payment method
      // Both 'advance' and 'full_amount' require online payment, so status is 'pending_payment'
      const bookingStatus = (paymentMethod === 'advance' || paymentMethod === 'full_amount') ? 'pending_payment' : 'confirmed';
      
      // Clean up any stale pending_payment bookings for this user (older than 1 hour)
      if (paymentMethod === 'advance' || paymentMethod === 'full_amount') {
        try {
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
          await storage.cleanupStaleBookings(req.user!.userId, oneHourAgo);
        } catch (error) {
          console.error('Failed to cleanup stale bookings:', error);
          // Don't fail the booking creation if cleanup fails
        }
      }
      
      // Create booking with atomic availability check + creation
      const bookingData = {
        propertyId,
        userId: req.user!.userId,
        bookingDate: formattedDate,
        bookingType,
        guests,
        totalPrice: price,
        status: bookingStatus
      };

      // Use atomic booking creation to prevent race conditions
      let booking;
      try {
        booking = await storage.createBookingAtomic(bookingData);
      } catch (error) {
        if (error instanceof Error && error.message.includes('no longer available')) {
          return res.status(409).json({ 
            message: error.message,
            code: 'BOOKING_CONFLICT',
            suggestion: 'Please refresh the page and select a different date or time slot.'
          });
        }
        throw error; // Re-throw if it's not a booking conflict
      }

      // Only send email notifications for confirmed bookings (pay at venue)
      if (bookingStatus === 'confirmed') {
        // Get user information for emails
        const guest = await storage.getUser(req.user!.userId);
        const owner = await storage.getUser(property.ownerId);

        if (guest && owner) {
          // Send email notifications
          const emailData = {
            bookingId: booking.id,
            propertyTitle: property.title,
            guestName: guest.fullName || guest.username,
            guestEmail: guest.email,
            ownerEmail: owner.email,
            bookingDate: bookingDate,
            bookingType: bookingType,
            guests: req.body.guests,
            totalPrice: price + 15, // Including cleaning fee
            propertyLocation: property.location
          };

          // Send emails asynchronously (don't wait for email sending to complete)
          emailService.sendBookingEmails(emailData).then(result => {
            console.log('Email notifications sent:', result);
          }).catch(error => {
            console.error('Failed to send email notifications:', error);
          });
        }
      }

      return res.status(201).json(booking);
    } catch (error) {
      console.error("Create booking error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/check-availability/:propertyId", async (req, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      const date = new Date(req.query.date as string);
      const type = req.query.type as 'morning' | 'full_day';

      if (!date || !type) {
        return res.status(400).json({ message: "Date and booking type are required" });
      }

      const isAvailable = await storage.checkAvailability(propertyId, date, type);
      return res.json({ available: isAvailable });
    } catch (error) {
      console.error("Check availability error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Reviews Routes
  app.get("/api/properties/:propertyId/reviews", async (req, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      const reviews = await storage.getReviews(propertyId);

      // Get property average rating
      const averageRating = await storage.getPropertyAverageRating(propertyId);

      return res.json({ 
        reviews, 
        averageRating, 
        totalReviews: reviews.length 
      });
    } catch (error) {
      console.error("Get property reviews error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/users/:userId/reviews", authenticate, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      // Make sure users can only access their own reviews unless they are an owner
      if (req.user?.userId !== userId && req.user?.role !== 'owner') {
        return res.status(403).json({ message: "Not authorized to access these reviews" });
      }

      const reviews = await storage.getUserReviews(userId);
      return res.json(reviews);
    } catch (error) {
      console.error("Get user reviews error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/reviews", authenticate, validate(reviewFormSchema), async (req, res) => {
    try {
      const { propertyId, rating, comment, bookingId } = req.body;
      const userId = req.user!.userId;

      // Check if the user has already reviewed this property
      const propertyReviews = await storage.getReviews(propertyId);
      const existingReview = propertyReviews.find(r => r.userId === userId);

      if (existingReview) {
        return res.status(400).json({ 
          message: "You have already reviewed this property. You can edit your existing review." 
        });
      }

      // Create the review
      const review = await storage.createReview({
        propertyId,
        userId,
        rating,
        comment,
        bookingId
      });

      return res.status(201).json(review);
    } catch (error) {
      console.error("Create review error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.put("/api/reviews/:id", authenticate, validate(reviewFormSchema), async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const { rating, comment } = req.body;
      const userId = req.user!.userId;

      // Get existing review to check ownership
      const existingReview = await storage.getReview(reviewId);

      if (!existingReview) {
        return res.status(404).json({ message: "Review not found" });
      }

      // Check if user is allowed to edit this review
      if (existingReview.userId !== userId) {
        return res.status(403).json({ message: "Not authorized to edit this review" });
      }

      // Update the review
      const updatedReview = await storage.updateReview(reviewId, { rating, comment });
      return res.json(updatedReview);
    } catch (error) {
      console.error("Update review error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Owner only middleware for owner-specific operations
  const ownerOnly = (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== 'owner') {
      return res.status(403).json({ message: "Owner access required" });
    }

    return next();
  };

  // Admin routes for user consent management
  app.get("/api/admin/users/consent", authenticate, ownerOnly, async (req, res) => {
    try {
      // Fetch all users with their consent information
      // Use the database storage layer
      const allUsers = await storage.getAllUsers();

      // Map user data to the expected format
      const rows = allUsers.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        termsAccepted: user.termsAccepted || false,
        privacyPolicyAccepted: user.privacyPolicyAccepted || false,
        cookiePolicyAccepted: user.cookiePolicyAccepted || false,
        dataProcessingConsent: user.dataProcessingConsent || false,
        marketingConsent: user.marketingConsent || false,
        consentTimestamp: user.consentTimestamp
      }));

      return res.json(rows);
    } catch (error) {
      console.error("Error fetching user consent data:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/reviews/:id/response", authenticate, ownerOnly, validate(reviewResponseSchema), async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const { response } = req.body;

      // Get the review
      const review = await storage.getReview(reviewId);

      if (!review) {
        return res.status(404).json({ message: "Review not found" });
      }

      // Get the property to check ownership
      const property = await storage.getProperty(review.propertyId);

      if (!property) {
        return res.status(404).json({ message: "Property not found" });
      }

      // Check if the owner is the one responding
      if (property.ownerId !== req.user!.userId) {
        return res.status(403).json({ message: "Only the property owner can respond to reviews" });
      }

      // Add owner response
      const updatedReview = await storage.addOwnerResponse(reviewId, response);
      return res.json(updatedReview);
    } catch (error) {
      console.error("Add review response error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/reviews/:id", authenticate, async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const userId = req.user!.userId;

      // Get existing review to check ownership
      const existingReview = await storage.getReview(reviewId);

      if (!existingReview) {
        return res.status(404).json({ message: "Review not found" });
      }

      // Check if user is allowed to delete this review (owner of review or owner of property)
      const property = await storage.getProperty(existingReview.propertyId);

      if (existingReview.userId !== userId && property?.ownerId !== userId) {
        return res.status(403).json({ message: "Not authorized to delete this review" });
      }

      // Delete the review
      await storage.deleteReview(reviewId);
      return res.status(204).send();
    } catch (error) {
      console.error("Delete review error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Owner Interest Form Route
  app.post("/api/owner-interest", generalApiLimiter, async (req: Request, res: Response) => {
    try {
      // Validate request body using Zod schema
      const ownerInterestData = req.body;

      // Basic validation
      if (!ownerInterestData.fullName || !ownerInterestData.email || !ownerInterestData.phone || 
          !ownerInterestData.propertyLocation || !ownerInterestData.propertyType) {
        return res.status(400).json({ 
          message: "Missing required fields: fullName, email, phone, propertyLocation, propertyType" 
        });
      }

      // Create owner interest request
      const newOwnerInterest = await storage.createOwnerInterest({
        fullName: ownerInterestData.fullName,
        email: ownerInterestData.email,
        phone: ownerInterestData.phone,
        propertyLocation: ownerInterestData.propertyLocation,
        propertyType: ownerInterestData.propertyType,
        propertySize: ownerInterestData.propertySize || null,
        expectedRevenue: ownerInterestData.expectedRevenue || null,
        currentOccupancy: ownerInterestData.currentOccupancy || null,
        amenities: ownerInterestData.amenities || [],
        propertyDescription: ownerInterestData.propertyDescription || null,
        experience: ownerInterestData.experience || null,
        availability: ownerInterestData.availability || null,
        additionalInfo: ownerInterestData.additionalInfo || null,
        status: 'pending'
      });

      // Send acknowledgment email to the property owner
      try {
        if (config.sendgrid.available) {
          // Email implementation can be added here in the future
          console.log(`Owner interest request received from ${ownerInterestData.email}`);
        }
      } catch (emailError) {
        console.log("Email notification failed, but form submission was successful:", emailError);
      }

      return res.status(201).json({
        message: "Thank you for your interest! We'll contact you within 24 hours to discuss listing your property.",
        id: newOwnerInterest.id
      });
    } catch (error) {
      console.error("Owner interest form error:", error);
      return res.status(500).json({ message: "Server error processing your request" });
    }
  });

  // Audit Events Route - Log security and payment events
  app.post("/api/audit/events", generalApiLimiter, async (req: Request, res: Response) => {
    try {
      const { events, clientTimestamp } = req.body;

      if (!Array.isArray(events) || events.length === 0) {
        return res.status(400).json({ error: "No events provided" });
      }

      // Log audit events to server logs (in production, this would go to a proper audit database)
      console.log(`[AUDIT] Received ${events.length} events from client at ${clientTimestamp}`);
      
      for (const event of events) {
        console.log(`[AUDIT] ${event.type}:`, {
          eventId: event.id,
          timestamp: event.timestamp,
          userId: event.userId,
          details: event.details,
          metadata: event.metadata,
          riskScore: event.riskScore
        });
      }

      // In production, you would:
      // 1. Validate events structure
      // 2. Store in audit database
      // 3. Trigger alerts for critical events
      // 4. Update risk scores

      return res.status(200).json({ 
        success: true, 
        message: "Audit events logged successfully",
        eventsReceived: events.length 
      });
    } catch (error) {
      console.error("Audit events error:", error);
      return res.status(500).json({ error: "Failed to log audit events" });
    }
  });

  // Monitoring Error Route - Log frontend errors for monitoring
  app.post("/api/monitoring/error", generalApiLimiter, async (req: Request, res: Response) => {
    try {
      const { errorId, error, errorInfo, context, metadata } = req.body;

      if (!error || !errorId) {
        return res.status(400).json({ error: "Error details required" });
      }

      // Log error for monitoring (in production, this would go to error monitoring service like Sentry)
      console.error(`[MONITORING] Frontend error ${errorId}:`, {
        errorId,
        errorName: error.name,
        errorMessage: error.message,
        context,
        userAgent: metadata?.userAgent,
        url: metadata?.url,
        timestamp: metadata?.timestamp,
        retryCount: metadata?.retryCount
      });

      // Log stack trace separately for better readability
      if (error.stack) {
        console.error(`[MONITORING] Stack trace for ${errorId}:`, error.stack);
      }

      // In production, you would:
      // 1. Send to error monitoring service (Sentry, LogRocket, etc.)
      // 2. Store in error database
      // 3. Trigger alerts for critical errors
      // 4. Track error patterns

      return res.status(200).json({ 
        success: true, 
        message: "Error logged successfully",
        errorId 
      });
    } catch (error) {
      console.error("Monitoring error:", error);
      return res.status(500).json({ error: "Failed to log monitoring error" });
    }
  });

  // Import and add OTP authentication routes
  const otpRoutes = await import("./otpRoutes");
  app.use("/api/auth/otp", otpRoutes.default);

  const httpServer = createServer(app);

  return httpServer;
}