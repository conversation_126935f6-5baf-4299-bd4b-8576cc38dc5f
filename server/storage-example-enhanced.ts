/**
 * EXAMPLE: Enhanced Storage Layer with Standardized Error Handling
 * 
 * This example shows how to implement error handling in the storage/data layer
 * using the new standardized system with automatic retries and error conversion.
 */

import { db } from './db';
import { users, properties, bookings } from '../shared/schema';
import { eq, and, desc, sql } from 'drizzle-orm';
import { 
  wrapStorageMethod, 
  createStorageMethod,
  wrapBatchOperation 
} from './utils/storageErrorWrapper';
import { 
  NotFoundError, 
  DatabaseError, 
  ConflictError,
  ValidationError 
} from './errors';

/**
 * Enhanced Storage Class with Standardized Error Handling
 */
export class EnhancedStorage {
  /**
   * ✅ GOOD: Wrapped method with automatic retry and error conversion
   */
  getUser = wrapStorageMethod(
    'getUser',
    async (id: number) => {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);
      
      return user;
    },
    { 
      throwNotFound: true, // Automatically throws NotFoundError if null
      retryConfig: {
        maxAttempts: 3,
        delayMs: 100
      }
    }
  );

  /**
   * ✅ GOOD: Method with logging and sensitive data sanitization
   */
  createUser = createStorageMethod(
    'createUser',
    async (userData: any) => {
      // Validate required fields
      if (!userData.email || !userData.password) {
        throw new ValidationError('Email and password are required');
      }
      
      // Check for duplicates (will be converted to ConflictError by wrapper)
      const [newUser] = await db
        .insert(users)
        .values(userData)
        .returning();
      
      return newUser;
    },
    {
      logArgs: true,
      logResult: true,
      sensitiveFields: ['password', 'email'] // Will be redacted in logs
    }
  );

  /**
   * ✅ GOOD: Complex method with business logic errors
   */
  async createBooking(bookingData: any): Promise<any> {
    // Start transaction
    return await db.transaction(async (tx) => {
      try {
        // Check property exists
        const [property] = await tx
          .select()
          .from(properties)
          .where(eq(properties.id, bookingData.propertyId))
          .limit(1);
        
        if (!property) {
          throw new NotFoundError('Property');
        }
        
        // Check availability using raw SQL for complex date logic
        const conflictingBookings = await tx.execute(sql`
          SELECT id FROM bookings 
          WHERE property_id = ${bookingData.propertyId}
          AND booking_date = ${bookingData.bookingDate}
          AND status NOT IN ('cancelled', 'rejected')
          AND (
            booking_type = 'full_day' 
            OR ${bookingData.bookingType} = 'full_day'
            OR (booking_type = 'morning' AND ${bookingData.bookingType} = 'morning')
          )
        `);
        
        if (conflictingBookings.rows.length > 0) {
          throw new ConflictError('Property is not available for the selected date and time');
        }
        
        // Create booking
        const [newBooking] = await tx
          .insert(bookings)
          .values({
            ...bookingData,
            status: 'pending',
            createdAt: new Date()
          })
          .returning();
        
        return newBooking;
      } catch (error) {
        // Transaction will be rolled back automatically
        // Re-throw the error for proper handling
        throw error;
      }
    });
  }

  /**
   * ✅ GOOD: Batch operation with partial success handling
   */
  async updateMultipleBookings(
    updates: Array<{ id: number; status: string }>
  ): Promise<any> {
    const operations = updates.map(update => async () => {
      // Validate status
      const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed'];
      if (!validStatuses.includes(update.status)) {
        throw new ValidationError(`Invalid status: ${update.status}`);
      }
      
      const [updated] = await db
        .update(bookings)
        .set({ 
          status: update.status
        })
        .where(eq(bookings.id, update.id))
        .returning();
      
      if (!updated) {
        throw new NotFoundError(`Booking ${update.id}`);
      }
      
      return updated;
    });
    
    // Use batch wrapper for better error handling
    return await wrapBatchOperation(
      'updateMultipleBookings',
      operations,
      { stopOnError: false } // Continue even if some fail
    );
  }

  /**
   * ✅ GOOD: Method with custom error handling for specific scenarios
   */
  async getPropertyWithStats(propertyId: number): Promise<any> {
    try {
      // Complex query with joins and aggregations
      const result = await db.execute(sql`
        SELECT 
          p.*,
          COUNT(DISTINCT b.id) as total_bookings,
          COUNT(DISTINCT CASE WHEN b.status = 'confirmed' THEN b.id END) as confirmed_bookings,
          AVG(r.rating) as average_rating,
          COUNT(DISTINCT r.id) as total_reviews
        FROM properties p
        LEFT JOIN bookings b ON b.property_id = p.id
        LEFT JOIN reviews r ON r.property_id = p.id
        WHERE p.id = ${propertyId}
        GROUP BY p.id
      `);
      
      if (!result.rows || result.rows.length === 0) {
        throw new NotFoundError('Property');
      }
      
      return result.rows[0];
    } catch (error) {
      // Check for specific database errors
      if ((error as any)?.code === 'TIMEOUT') {
        throw new DatabaseError(
          'Query timeout while fetching property statistics',
          'getPropertyWithStats',
          error,
          { metadata: { propertyId } }
        );
      }
      
      // Re-throw if it's already our error type
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      // Convert to DatabaseError for unknown errors
      throw new DatabaseError(
        'Failed to fetch property statistics',
        'getPropertyWithStats',
        error as Error,
        { metadata: { propertyId } }
      );
    }
  }

  /**
   * ❌ BAD: Don't catch and hide errors (DON'T DO THIS)
   */
  async badExample1(id: number): Promise<any> {
    try {
      const result = await db.select().from(users).where(eq(users.id, id));
      return result[0];
    } catch (error) {
      // Don't log and return null/undefined!
      console.error('Error:', error);
      return null;
    }
  }

  /**
   * ❌ BAD: Don't use generic errors (DON'T DO THIS)
   */
  async badExample2(data: any): Promise<any> {
    if (!data.name) {
      // Use ValidationError instead!
      throw new Error('Name is required');
    }
    
    // Example: Check if the user exists (implement your own logic)
    const [existing] = await db
      .select()
      .from(users)
      .where(eq(users.email, data.name))
      .limit(1);
    if (existing) {
      // Use ConflictError instead!
      throw new Error('Already exists');
    }
  }

  /**
   * ❌ BAD: Don't return error objects (DON'T DO THIS)
   */
  async badExample3(id: number): Promise<any> {
    try {
      const [result] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);
      if (!result) {
        // Don't return errors, throw them!
        return { error: 'Not found' };
      }
      return { data: result };
    } catch (error) {
      return { error: (error as Error).message || 'Unknown error' };
    }
  }
}

// Export singleton instance
export const enhancedStorage = new EnhancedStorage();