import { Router } from "express";
import rateLimit from "express-rate-limit";
import { storage } from "../storage";
import { config } from "../config";
import { authenticate } from "./auth";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError 
} from "../middlewares/errorHandler";
import performanceRoutes from "./performance";
import { TIMEOUTS } from "../../shared/constants";

const router = Router();

// Rate limiting for image proxy
const imageLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 200, // Allow many image requests per minute
  message: {
    error: "Too many image requests from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Image proxy endpoint to handle blocked external images
router.get("/proxy-image", imageLimiter, asyncHandler(async (req, res) => {
  const { url } = req.query;

  if (!url || typeof url !== 'string') {
    return res.status(400).json({ error: "URL parameter is required" });
  }

  try {
    // Validate URL format
    const imageUrl = new URL(url);
    
    // Security: Only allow specific domains to prevent SSRF attacks
    const allowedDomains = [
      'images.unsplash.com',
      'source.unsplash.com',
      'unsplash.com',
      'res.cloudinary.com',
      'via.placeholder.com',
      'picsum.photos',
      'placeholder.com',
      'loremflickr.com'
    ];

    if (!allowedDomains.includes(imageUrl.hostname)) {
      return res.status(400).json({ error: "Domain not allowed" });
    }

    // Fetch the image with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUTS.API_REQUEST_SHORT);
    
    const response = await fetch(imageUrl.toString(), { 
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageProxy/1.0)'
      }
    });
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      // Silently return 404 for missing images to reduce log noise
      return res.status(404).json({ error: "Image not found" });
    }

    // Get content type
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.startsWith('image/')) {
      return res.status(400).json({ error: "Invalid image type" });
    }

    // Set appropriate headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Stream the image data
    const buffer = await response.arrayBuffer();
    return res.send(Buffer.from(buffer));

  } catch (error) {
    // Silently handle all image proxy errors to avoid log spam
    return res.status(404).json({ error: "Image not found" });
  }
}));

// Development utilities (only available in development)
if (config.isDevelopment()) {
  // Get authentication info for debugging
  router.get("/dev/auth-info", (req: any, res) => {
    const token = req.cookies.auth_token || req.headers.authorization?.replace('Bearer ', '');
    
    return sendSuccess(res, {
      hasToken: !!token,
      token: token ? token.substring(0, 20) + '...' : null,
      user: req.user || null,
      environment: config.app.nodeEnv,
      timestamp: new Date().toISOString()
    });
  });

  // Create test users for development
  router.post("/dev/create-test-users", asyncHandler(async (req, res) => {
    const testUsers = [
      {
        username: "testuser",
        email: "<EMAIL>",
        password: "password123",
        fullName: "Test User",
        role: "user"
      },
      {
        username: "testowner",
        email: "<EMAIL>", 
        password: "password123",
        fullName: "Test Owner",
        role: "owner"
      }
    ];

    const createdUsers = [];

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const existingUser = await storage.getUserByEmail(userData.email);
        if (!existingUser) {
          const newUser = await storage.createUser(userData);
          createdUsers.push({
            id: newUser.id,
            email: newUser.email,
            role: newUser.role
          });
        }
      } catch (error) {
        console.log(`User ${userData.email} might already exist`);
      }
    }

    return sendSuccess(res, {
      created: createdUsers,
      message: "Test users created (existing users skipped)"
    });
  }));
}

// Owner interest request
router.post("/owner-interest", asyncHandler(async (req, res) => {
  const {
    fullName,
    email,
    phone,
    propertyLocation,
    propertyType,
    propertySize,
    expectedRevenue,
    currentOccupancy,
    amenities,
    propertyDescription,
    experience,
    availability,
    additionalInfo
  } = req.body;

  // Basic validation
  if (!fullName || !email || !phone || !propertyLocation || !propertyType) {
    throw new ValidationError("Required fields: fullName, email, phone, propertyLocation, propertyType");
  }

  const ownerInterest = await storage.createOwnerInterest({
    fullName,
    email,
    phone,
    propertyLocation,
    propertyType,
    propertySize,
    expectedRevenue,
    currentOccupancy,
    amenities: amenities || [],
    propertyDescription,
    experience,
    availability,
    additionalInfo,
    status: 'pending'
  });

  return sendSuccess(res, ownerInterest, "Owner interest request submitted successfully", 201);
}));

// Get owner interest requests (admin only - placeholder for future admin role)
router.get("/owner-interest", authenticate, asyncHandler(async (req: any, res) => {
  // For now, only allow owners to see all requests
  // In the future, this should be admin-only
  if (req.user.role !== 'owner') {
    throw new ValidationError("Insufficient permissions");
  }

  const requests = await storage.getOwnerInterests();
  return sendSuccess(res, requests);
}));

// Twilio SMS delivery status webhook
router.post("/twilio/sms-status", asyncHandler(async (req, res) => {
  const {
    MessageSid,
    MessageStatus,
    To,
    From,
    ErrorCode,
    ErrorMessage
  } = req.body;

  console.log(`📱 [SMS Status] Message ${MessageSid} to ${To} status: ${MessageStatus}`);
  
  // Log delivery status details
  if (MessageStatus === 'delivered') {
    console.log(`✅ SMS delivered successfully to ${To}`);
  } else if (MessageStatus === 'failed') {
    console.log(`❌ SMS failed to ${To}. Error: ${ErrorCode} - ${ErrorMessage}`);
  } else if (MessageStatus === 'undelivered') {
    console.log(`⚠️ SMS undelivered to ${To}. Error: ${ErrorCode} - ${ErrorMessage}`);
  }
  
  // You can store this in database for tracking
  // await storage.updateSMSDeliveryStatus(MessageSid, MessageStatus, ErrorCode, ErrorMessage);
  
  // Respond with 200 OK to acknowledge receipt
  res.status(200).send('OK');
}));

// Performance monitoring routes
router.use("/performance", performanceRoutes);

export default router;