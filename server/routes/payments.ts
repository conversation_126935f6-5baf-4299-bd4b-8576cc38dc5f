import { Router } from "express";
import rateLimit from "express-rate-limit";
import { z } from "zod";
import { enhancedPaymentService, PaymentStatus } from "../services/EnhancedPaymentService";
import { gstCalculationService } from "../services/GSTCalculationService";
import { authenticate } from "./auth";
import { sanitizeIdParam } from "../middlewares/sanitization";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError,
  NotFoundError,
  AuthorizationError,
  AuthenticationError
} from "../middlewares/errorHandler";
import { 
  StandardizedErrorService,
  AppError,
  ErrorCode,
  ErrorSeverity,
  ErrorCategory,
  ValidationError,
  PaymentError,
  DatabaseError
} from "../services/StandardizedErrorService";
import { errorRecoveryService, OperationType } from "../services/ErrorRecoveryService";
import { metricsService, METRICS } from "../services/MetricsService";
import { storage } from "../storage";
import { db } from "../db";
import { securitySessions, paymentOrders, InsertPaymentOrder } from "../../shared/schema";
import { eq, and, desc } from "drizzle-orm";
import { auditLogger } from "../services/AuditLogger";
import { sendDeltaUpdateToUser } from "./events";
import { config } from "../config";
import { logger } from "../services/LoggerService";

// SECURITY: Server-side amount calculation function
async function calculateBookingAmount(booking: any): Promise<number> {
  // Get property details to determine pricing
  const property = await storage.getProperty(booking.propertyId);
  if (!property) {
    throw new AppError({
      code: ErrorCode.RESOURCE_NOT_FOUND,
      message: 'Property not found for booking amount calculation',
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.BUSINESS_LOGIC,
      details: { propertyId: booking.propertyId, bookingId: booking.id }
    });
  }
  
  // Calculate base amount based on booking type
  let baseAmount = 0;
  if (booking.bookingType === 'morning') {
    baseAmount = property.halfDayPrice || property.fullDayPrice / 2;
  } else if (booking.bookingType === 'full_day') {
    baseAmount = property.fullDayPrice;
  } else {
    baseAmount = property.fullDayPrice; // default to full day price
  }
  
  // Add cleaning fee (fixed at ₹15 as per frontend config)
  const cleaningFee = 15;
  
  // Add any additional fees based on guest count
  const additionalGuestFee = Math.max(0, (booking.guests || 1) - 4) * 100; // ₹100 per additional guest after 4
  
  const totalAmount = baseAmount + cleaningFee + additionalGuestFee;
  
  // Audit the calculation without exposing sensitive amounts
  await auditLogger.logPaymentAction('BOOKING_AMOUNT_CALCULATED', {
    userId: booking.userId,
    actorType: 'system',
    actorId: 0,
    metadata: {
      bookingId: booking.id,
      propertyId: booking.propertyId,
      hasBaseAmount: baseAmount > 0,
      hasCleaningFee: cleaningFee > 0,
      hasAdditionalGuestFee: additionalGuestFee > 0,
      additionalGuests: booking.guests > 2 ? booking.guests - 2 : 0
    }
  });
  
  return totalAmount;
}

const router = Router();

// Rate limiting for payment endpoints (disabled in development)
const paymentLimiter = config.isDevelopment() 
  ? (req: any, res: any, next: any) => next() // Skip rate limiting in development
  : rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10, // Limit payment requests to 10 per 15 minutes
      message: {
        error: "Too many payment attempts from this IP, please try again later.",
        retryAfter: 900
      },
      standardHeaders: true,
      legacyHeaders: false,
    });

// Stricter rate limiting for payment creation (disabled in development)
const createPaymentLimiter = config.isDevelopment()
  ? (req: any, res: any, next: any) => next() // Skip rate limiting in development
  : rateLimit({
      windowMs: 5 * 60 * 1000, // 5 minutes
      max: 3, // Limit to 3 payment creation attempts per 5 minutes
      message: {
        error: "Too many payment creation attempts from this IP, please try again later.",
        retryAfter: 300
      },
      standardHeaders: true,
      legacyHeaders: false,
    });

// Input validation schemas - SECURITY: Never accept amount from client
const createOrderSchema = z.object({
  bookingId: z.number().int().positive(),
  currency: z.string().default("INR"),
  customerDetails: z.object({
    name: z.string().min(1).max(100),
    email: z.string().email().max(100),
    phone: z.string().max(15).refine(
      (phone) => phone === "" || phone.length >= 10,
      { message: "Phone number must be at least 10 digits if provided" }
    )
  }),
  gstDetails: z.object({
    supplierState: z.string().min(2).max(2),
    recipientState: z.string().min(2).max(2),
    serviceType: z.string().default("accommodation"),
    supplierGstin: z.string().optional(),
    recipientGstin: z.string().optional()
  })
});

const verifyPaymentSchema = z.object({
  razorpay_order_id: z.string().min(1),
  razorpay_payment_id: z.string().min(1),
  razorpay_signature: z.string().min(1),
  orderId: z.number().int().positive()
});

const capturePaymentSchema = z.object({
  paymentId: z.string().min(1),
  amount: z.number().int().positive(),
  idempotencyKey: z.string().min(1)
});

const refundPaymentSchema = z.object({
  paymentId: z.string().min(1),
  amount: z.number().int().positive(),
  reason: z.string().min(1).max(255),
  idempotencyKey: z.string().min(1)
});

// SECURITY: Consolidated payment processing schema
const processPaymentSchema = z.object({
  razorpay_order_id: z.string().min(1),
  razorpay_payment_id: z.string().min(1),
  razorpay_signature: z.string().min(1),
  orderId: z.string().min(1),
  bookingId: z.number().int().positive(),
  idempotencyKey: z.string().min(1),
  verificationToken: z.string().optional() // For 2FA verified transactions
});

// Input validation middleware
function validate(schema: z.ZodSchema) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      const errorMessage = error.errors?.map((e: any) => e.message).join(", ") || error.message;
      const validationError = new ValidationError(errorMessage, { zodErrors: error.errors });
      return sendError(res, validationError, req);
    }
  };
}

// Create payment order
router.post("/create-order", 
  authenticate,
  createPaymentLimiter,
  validate(createOrderSchema),
  asyncHandler(async (req: any, res) => {
    const { bookingId, currency, customerDetails, gstDetails } = req.body;
    
    // Record payment creation attempt
    metricsService.incrementCounter(METRICS.PAYMENTS_PROCESSED_TOTAL, 1, {
      payment_type: 'order_creation',
      user_id: req.user.userId.toString()
    });
    
    // Start timer to track payment order creation duration
    const paymentTimerId = metricsService.startTimer('payment_order_duration');
    
    // Verify booking exists and belongs to user
    const booking = await storage.getBooking(bookingId);
    if (!booking) {
      throw new AppError({
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Booking not found for payment order creation',
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.BUSINESS_LOGIC,
        details: { bookingId, userId: req.user.userId }
      });
    }
    
    if (booking.userId !== req.user.userId) {
      throw new AppError({
        code: ErrorCode.FORBIDDEN,
        message: 'You can only create payment orders for your own bookings',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SECURITY_ERROR,
        details: { bookingId, requestingUserId: req.user.userId, bookingUserId: booking.userId }
      });
    }
    
    // Check if booking is in correct state
    if (booking.status !== 'pending' && booking.status !== 'pending_payment') {
      throw new ValidationError('Booking is not in a valid state for payment', {
        bookingId,
        currentStatus: booking.status,
        validStatuses: ['pending', 'pending_payment']
      });
    }
    
    // SECURITY: Calculate amount server-side only - never trust client
    const fullAmount = await calculateBookingAmount(booking);
    logger.debug('Payment amount calculation completed', 'payment', {
      bookingId,
      paymentType: booking.status === 'pending_payment' ? 'advance' : 'full',
      userId: req.user.userId
    });
    
    // For advance payments (booking status is 'pending_payment'), calculate 30% of full amount
    const baseAmount = booking.status === 'pending_payment' ? Math.round(fullAmount * 0.30) : fullAmount;
    logger.debug('Payment base amount calculated', 'payment', {
      bookingId,
      isAdvancePayment: booking.status === 'pending_payment',
      advancePercentage: booking.status === 'pending_payment' ? 30 : 100
    });
    
    // Calculate GST based on server-calculated amount
    // Convert to paise for GST calculation if needed
    const baseAmountInPaise = Math.round(baseAmount * 100); // Convert rupees to paise
    logger.debug('Payment amount converted to paise for GST calculation', 'payment', { bookingId });
    
    const gstCalculation = await gstCalculationService.calculateGST({
      baseAmount: baseAmountInPaise,
      supplierState: gstDetails.supplierState,
      recipientState: gstDetails.recipientState,
      serviceType: gstDetails.serviceType,
      supplierGstin: gstDetails.supplierGstin,
      recipientGstin: gstDetails.recipientGstin
    });
    logger.debug('GST calculation completed', 'payment', {
      bookingId,
      supplierState: gstDetails.supplierState,
      recipientState: gstDetails.recipientState,
      serviceType: gstDetails.serviceType,
      hasSupplierGstin: !!gstDetails.supplierGstin,
      hasRecipientGstin: !!gstDetails.recipientGstin
    });
    
    // If GST service works in paise, convert back to rupees for display
    if (gstCalculation.totalAmount < baseAmount) {
      logger.debug('Converting GST amounts from paise to rupees', 'payment', { bookingId });
      gstCalculation.baseAmount = Math.round(gstCalculation.baseAmount / 100);
      gstCalculation.totalGstAmount = Math.round(gstCalculation.totalGstAmount / 100);
      gstCalculation.totalAmount = Math.round(gstCalculation.totalAmount / 100);
    }
    
    // Generate idempotency key
    const idempotencyKey = `payment_order_${bookingId}_${req.user.userId}_${Date.now()}`;
    
    // Development bypass for testing
    if (config.isDevelopment()) {
      logger.debug('Payment order creation bypassed for development testing', 'payment-dev-bypass');
      
      // Create mock payment order in database for development consistency
      const mockRazorpayOrderId = `order_dev_${Date.now()}`;
      const orderData: InsertPaymentOrder = {
        razorpayOrderId: mockRazorpayOrderId,
        bookingId: bookingId,
        idempotencyKey: idempotencyKey,
        amount: gstCalculation.totalAmount,
        currency: currency || 'INR',
        receipt: `booking_${bookingId}_dev_${Date.now()}`,
        status: 'created',
        attempts: 0,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };
      
      const [mockPaymentOrder] = await db
        .insert(paymentOrders)
        .values(orderData)
        .returning();
      
      // Store GST record
      await gstCalculationService.storeGSTRecord(bookingId, gstCalculation);
      
      // Update booking status to pending_payment
      await storage.updateBookingStatus(bookingId, 'pending_payment');
      
      return sendSuccess(res, {
        paymentOrder: mockPaymentOrder,
        gstCalculation,
        totalAmount: gstCalculation.totalAmount,
        breakdown: {
          baseAmount: gstCalculation.baseAmount,
          gstAmount: gstCalculation.totalGstAmount,
          totalAmount: gstCalculation.totalAmount
        }
      }, "Payment order created successfully (development mode)", 201);
    }
    
    // Create booking details for payment service
    const bookingDetails = {
      bookingId,
      amount: gstCalculation.totalAmount, // Include GST
      currency,
      customerName: customerDetails.name,
      customerEmail: customerDetails.email,
      customerPhone: customerDetails.phone,
      propertyDetails: {
        propertyId: booking.propertyId,
        bookingDate: booking.bookingDate,
        bookingType: booking.bookingType
      }
    };
    
    try {
      // Create payment order
      const paymentOrder = await enhancedPaymentService.createPaymentOrder(
        bookingDetails,
        idempotencyKey
      );
      
      // Store GST record
      await gstCalculationService.storeGSTRecord(bookingId, gstCalculation);
      
      // Update booking status to pending_payment
      const updatedBooking = await storage.updateBookingStatus(bookingId, 'pending_payment');
      
      // Send delta update for booking status change
      if (updatedBooking) {
        // Send to booking owner
        sendDeltaUpdateToUser(
          req.user.userId,
          'booking',
          bookingId,
          updatedBooking,
          {
            source: 'payment_initiated',
            userId: req.user.userId,
            priority: 'medium'
          }
        );
        
        // Also send to property owner if different
        if (booking.propertyId) {
          const property = await storage.getProperty(booking.propertyId);
          if (property && property.ownerId !== req.user.userId) {
            sendDeltaUpdateToUser(
              property.ownerId,
              'booking',
              bookingId,
              updatedBooking,
              {
                source: 'payment_initiated',
                userId: req.user.userId,
                priority: 'medium'
              }
            );
          }
        }
      }
      
      // Stop timer on successful completion
      metricsService.stopTimer(paymentTimerId);
      
      return sendSuccess(res, {
        paymentOrder,
        gstCalculation,
        totalAmount: gstCalculation.totalAmount,
        breakdown: {
          baseAmount: gstCalculation.baseAmount,
          gstAmount: gstCalculation.totalGstAmount,
          totalAmount: gstCalculation.totalAmount
        }
      }, "Payment order created successfully", 201);
      
    } catch (error: any) {
      metricsService.stopTimer(paymentTimerId);
      metricsService.incrementCounter(METRICS.PAYMENT_FAILURES_TOTAL, 1, {
        reason: 'creation_error',
        user_id: req.user.userId.toString()
      });
      
      const standardizedError = StandardizedErrorService.standardizeError(
        error,
        { bookingId, userId: req.user.userId, operation: 'create_payment_order' },
        req.requestId
      );
      
      StandardizedErrorService.logError(standardizedError);
      throw standardizedError;
    }
  })
);

// Verify payment signature
router.post("/verify",
  authenticate,
  paymentLimiter,
  validate(verifyPaymentSchema),
  asyncHandler(async (req: any, res) => {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature, orderId } = req.body;
    
    // Development bypass for testing
    if (config.isDevelopment() && razorpay_payment_id?.startsWith('pay_dev_')) {
      logger.debug('Payment verification bypassed for development testing', 'payment-dev-bypass');
      
      // Still verify order belongs to user for security
      const paymentOrder = await enhancedPaymentService.getPaymentOrder(orderId);
      if (!paymentOrder) {
        throw new AppError({
          code: ErrorCode.RESOURCE_NOT_FOUND,
          message: 'Payment order not found for verification',
          severity: ErrorSeverity.HIGH,
          category: ErrorCategory.BUSINESS_LOGIC,
          details: { orderId, userId: req.user.userId }
        });
      }
      
      if (!paymentOrder.bookingId) {
        throw new ValidationError('Payment order has no associated booking', {
          orderId,
          paymentOrderId: paymentOrder.id
        });
      }
      const booking = await storage.getBooking(paymentOrder.bookingId);
      if (!booking || booking.userId !== req.user.userId) {
        throw new AuthorizationError("You can only verify payments for your own bookings");
      }
      
      // Mock successful verification
      await enhancedPaymentService.updatePaymentStatus(orderId, PaymentStatus.AUTHORIZED);
      
      return sendSuccess(res, {
        verified: true,
        paymentId: razorpay_payment_id,
        orderId: orderId,
        message: "Payment verified successfully (development mode)"
      });
    }
    
    // Verify the payment order belongs to the user
    const paymentOrder = await enhancedPaymentService.getPaymentOrder(orderId);
    if (!paymentOrder) {
      throw new NotFoundError("Payment order not found");
    }
    
    // Verify booking belongs to user
    if (!paymentOrder.bookingId) {
      throw new ValidationError("Payment order has no associated booking");
    }
    const booking = await storage.getBooking(paymentOrder.bookingId);
    if (!booking || booking.userId !== req.user.userId) {
      throw new AuthorizationError("You can only verify payments for your own bookings");
    }
    
    try {
      // Verify payment signature
      const verificationResult = await enhancedPaymentService.verifyPaymentSignature(
        { razorpay_order_id, razorpay_payment_id, razorpay_signature },
        orderId
      );
      
      if (!verificationResult.isValid) {
        throw new PaymentError("Payment verification failed");
      }
      
      // Update payment status
      await enhancedPaymentService.updatePaymentStatus(orderId, PaymentStatus.AUTHORIZED);
      
      return sendSuccess(res, {
        verified: true,
        paymentId: verificationResult.paymentId,
        orderId: verificationResult.orderId,
        message: "Payment verified successfully"
      });
      
    } catch (error: any) {
      console.error("Payment verification failed:", error);
      throw new PaymentError(error.message || "Payment verification failed");
    }
  })
);

// Capture payment
router.post("/capture",
  authenticate,
  paymentLimiter,
  validate(capturePaymentSchema),
  asyncHandler(async (req: any, res) => {
    const { paymentId, amount, idempotencyKey } = req.body;
    
    try {
      // Capture payment
      const captureResult = await enhancedPaymentService.capturePayment(
        paymentId,
        amount,
        idempotencyKey
      );
      
      // Update booking status to confirmed
      if (captureResult.paymentOrderId) {
        const paymentOrder = await enhancedPaymentService.getPaymentOrder(captureResult.paymentOrderId);
        if (paymentOrder && paymentOrder.bookingId) {
          const updatedBooking = await storage.updateBookingStatus(paymentOrder.bookingId, 'confirmed');
          
          // Send delta updates for booking confirmation
          if (updatedBooking) {
            const booking = await storage.getBooking(paymentOrder.bookingId);
            if (booking) {
              // Send to booking owner
              sendDeltaUpdateToUser(
                booking.userId,
                'booking',
                paymentOrder.bookingId,
                updatedBooking,
                {
                  source: 'payment_captured',
                  userId: booking.userId,
                  priority: 'high'
                }
              );
              
              // Send to property owner
              const property = await storage.getProperty(booking.propertyId);
              if (property && property.ownerId !== booking.userId) {
                sendDeltaUpdateToUser(
                  property.ownerId,
                  'booking',
                  paymentOrder.bookingId,
                  updatedBooking,
                  {
                    source: 'payment_captured',
                    userId: booking.userId,
                    priority: 'high'
                  }
                );
              }
            }
          }
        }
      }
      
      return sendSuccess(res, {
        captured: true,
        transaction: captureResult,
        message: "Payment captured successfully"
      });
      
    } catch (error: any) {
      console.error("Payment capture failed:", error);
      throw new PaymentError(error.message || "Payment capture failed");
    }
  })
);

// Process refund
router.post("/refund",
  authenticate,
  paymentLimiter,
  validate(refundPaymentSchema),
  asyncHandler(async (req: any, res) => {
    const { paymentId, amount, reason, idempotencyKey } = req.body;
    
    try {
      // Process refund
      const refundResult = await enhancedPaymentService.processRefund(
        paymentId,
        amount,
        reason,
        idempotencyKey
      );
      
      return sendSuccess(res, {
        refunded: true,
        refund: refundResult,
        message: "Refund processed successfully"
      });
      
    } catch (error: any) {
      console.error("Refund processing failed:", error);
      throw new PaymentError(error.message || "Refund processing failed");
    }
  })
);

// SECURITY: Consolidated payment processing endpoint
router.post("/process",
  authenticate,
  paymentLimiter,
  validate(processPaymentSchema),
  asyncHandler(async (req: any, res) => {
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature, 
      orderId, 
      bookingId, 
      idempotencyKey,
      verificationToken 
    } = req.body;
    
    // Development bypass for testing
    if (config.isDevelopment() && razorpay_payment_id?.startsWith('pay_dev_')) {
      logger.debug('Payment processing bypassed for development testing', 'payment-dev-bypass');
      
      // Still verify order and booking belong to user for security
      const paymentOrder = await enhancedPaymentService.getPaymentOrder(parseInt(orderId));
      if (!paymentOrder) {
        throw new NotFoundError("Payment order not found");
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking || booking.userId !== req.user.userId) {
        throw new AuthorizationError("You can only process payments for your own bookings");
      }
      
      // Mock successful payment processing
      logger.debug('Development payment bypass: updating booking status', 'payment', {
        bookingId,
        newStatus: 'confirmed',
        userId: req.user.userId,
        isDevelopment: true
      });
      const statusUpdateResult = await storage.updateBookingStatus(bookingId, 'confirmed');
      logger.debug('Development payment bypass: booking status updated', 'payment', {
        bookingId,
        success: !!statusUpdateResult,
        isDevelopment: true
      });
      
      await enhancedPaymentService.updatePaymentStatus(parseInt(orderId), PaymentStatus.CAPTURED);
      
      // Audit log for development
      await auditLogger.logPaymentAction('PAYMENT_PROCESSED_SUCCESS', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { 
          bookingId, 
          paymentId: razorpay_payment_id, 
          orderId, 
          amount: paymentOrder.amount,
          developmentMode: true
        }
      }, parseInt(orderId));
      
      return sendSuccess(res, {
        verified: true,
        captured: true,
        paymentId: razorpay_payment_id,
        orderId: parseInt(orderId),
        bookingStatus: 'confirmed',
        verificationData: {
          paymentId: razorpay_payment_id,
          orderId: parseInt(orderId),
          amount: paymentOrder.amount,
          captureId: `capture_dev_${Date.now()}`,
          timestamp: new Date().toISOString()
        }
      }, "Payment processed successfully (development mode)");
    }
    
    // Verify the payment order belongs to the user
    const paymentOrder = await enhancedPaymentService.getPaymentOrder(parseInt(orderId));
    if (!paymentOrder) {
      throw new NotFoundError("Payment order not found");
    }
    
    // Verify booking belongs to user
    const booking = await storage.getBooking(bookingId);
    if (!booking || booking.userId !== req.user.userId) {
      throw new AuthorizationError("You can only process payments for your own bookings");
    }
    
    // Check if booking amount matches payment order (server-side validation)
    const fullBookingAmount = await calculateBookingAmount(booking);
    logger.debug('Payment processing: booking amount calculated', 'payment', {
      bookingId,
      userId: req.user.userId,
      paymentType: booking.status === 'pending_payment' ? 'advance' : 'full'
    });
    
    // For advance payments (booking status is 'pending_payment'), calculate 30% of full amount
    const calculatedAmount = booking.status === 'pending_payment' ? Math.round(fullBookingAmount * 0.30) : fullBookingAmount;
    logger.debug('Payment processing: calculated payment amount', 'payment', {
      bookingId,
      isAdvancePayment: booking.status === 'pending_payment',
      advancePercentage: booking.status === 'pending_payment' ? 30 : 100
    });
    
    const gstCalculation = await gstCalculationService.getGSTRecord(bookingId);
    logger.debug('Payment processing: GST calculation retrieved', 'payment', {
      bookingId,
      hasGstCalculation: !!gstCalculation,
      hasSupplierGstin: gstCalculation ? !!gstCalculation.supplierGstin : false,
      hasRecipientGstin: gstCalculation ? !!gstCalculation.recipientGstin : false
    });
    
    let expectedTotalAmount = gstCalculation ? gstCalculation.totalAmount : calculatedAmount;
    
    // Convert from paise to rupees if needed (if amount is suspiciously small)
    if (gstCalculation && gstCalculation.totalAmount < calculatedAmount) {
      logger.debug('Payment processing: converting GST amount from paise to rupees', 'payment', { bookingId });
      expectedTotalAmount = Math.round(gstCalculation.totalAmount / 100);
    }
    
    logger.debug('Payment processing: validating payment amount match', 'payment', {
      bookingId,
      amountValidation: 'comparing_expected_vs_order',
      hasPaymentOrder: !!paymentOrder
    });
    
    if (Math.abs(paymentOrder.amount - expectedTotalAmount) > 1) { // Allow 1 rupee tolerance for rounding
      throw new ValidationError(`Payment amount mismatch detected. Expected: ${expectedTotalAmount}, Got: ${paymentOrder.amount}`);
    }
    
    // For high-value transactions, verify 2FA token  
    if (expectedTotalAmount >= 10000 && !verificationToken) {
      throw new AuthenticationError("Two-factor authentication required for this transaction");
    }
    
    if (verificationToken) {
      // Verify 2FA token
      const twoFASession = await db.select()
        .from(securitySessions)
        .where(
          and(
            eq(securitySessions.userId, req.user.userId),
            eq(securitySessions.sessionType, '2fa_payment')
          )
        )
        .orderBy(desc(securitySessions.createdAt))
        .limit(1);
      
      const sessionInfo = twoFASession[0]?.sessionData as { sessionId: string, verificationToken?: string };
      if (!twoFASession.length || 
          sessionInfo?.verificationToken !== verificationToken ||
          twoFASession[0].expiresAt < new Date()) {
        throw new AuthenticationError("Invalid or expired 2FA verification");
      }
    }
    
    try {
      // Verify payment signature
      const verificationResult = await enhancedPaymentService.verifyPaymentSignature(
        { razorpay_order_id, razorpay_payment_id, razorpay_signature },
        parseInt(orderId)
      );
      
      if (!verificationResult.isValid) {
        throw new PaymentError("Payment signature verification failed");
      }
      
      // Capture payment automatically
      const captureResult = await enhancedPaymentService.capturePayment(
        razorpay_payment_id,
        paymentOrder.amount,
        idempotencyKey
      );
      
      // Update booking status to confirmed
      logger.info('Payment processed successfully: updating booking status', 'payment', {
        bookingId,
        newStatus: 'confirmed',
        userId: req.user.userId,
        paymentId: razorpay_payment_id
      });
      
      const updatedBooking = await storage.updateBookingStatus(bookingId, 'confirmed');
      
      logger.info('Booking status updated after successful payment', 'payment', {
        bookingId,
        success: !!updatedBooking,
        finalStatus: 'confirmed'
      });
      
      // Send delta updates for booking confirmation
      if (updatedBooking) {
        // Send to booking owner
        sendDeltaUpdateToUser(
          req.user.userId,
          'booking',
          bookingId,
          updatedBooking,
          {
            source: 'payment_confirmed',
            userId: req.user.userId,
            priority: 'high'
          }
        );
        
        // Send to property owner if different
        const property = await storage.getProperty(booking.propertyId);
        if (property && property.ownerId !== req.user.userId) {
          sendDeltaUpdateToUser(
            property.ownerId,
            'booking',
            bookingId,
            updatedBooking,
            {
              source: 'payment_confirmed',
              userId: req.user.userId,
              priority: 'high'
            }
          );
        }
      }
      
      // Update payment status
      await enhancedPaymentService.updatePaymentStatus(parseInt(orderId), PaymentStatus.CAPTURED);
      
      // Audit log successful payment
      await auditLogger.logPaymentAction('PAYMENT_PROCESSED_SUCCESS', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { 
          bookingId, 
          paymentId: razorpay_payment_id, 
          orderId, 
          amount: expectedTotalAmount,
          had2FA: !!verificationToken 
        }
      }, parseInt(orderId));
      
      return sendSuccess(res, {
        verified: true,
        captured: true,
        paymentId: razorpay_payment_id,
        orderId: parseInt(orderId),
        bookingStatus: 'confirmed',
        verificationData: {
          paymentId: verificationResult.paymentId,
          orderId: verificationResult.orderId,
          amount: expectedTotalAmount,
          captureId: captureResult.id,
          timestamp: new Date().toISOString()
        }
      }, "Payment processed successfully");
      
    } catch (error: any) {
      console.error("Payment processing failed:", error);
      
      // Audit log failed payment
      await auditLogger.logPaymentAction('PAYMENT_PROCESSED_FAILED', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { 
          bookingId, 
          paymentId: razorpay_payment_id, 
          orderId, 
          error: error.message,
          had2FA: !!verificationToken 
        }
      }, parseInt(orderId));
      
      throw new PaymentError(error.message || "Payment processing failed");
    }
  })
);

// Get payment order status
router.get("/order/:id",
  authenticate,
  sanitizeIdParam,
  asyncHandler(async (req: any, res) => {
    const orderId = parseInt(req.params.id);
    
    const paymentOrder = await enhancedPaymentService.getPaymentOrder(orderId);
    if (!paymentOrder) {
      throw new NotFoundError("Payment order not found");
    }
    
    // Verify booking belongs to user  
    if (!paymentOrder.bookingId) {
      throw new ValidationError("Payment order has no associated booking");
    }
    const booking = await storage.getBooking(paymentOrder.bookingId);
    if (!booking || booking.userId !== req.user.userId) {
      throw new AuthorizationError("You can only view payment orders for your own bookings");
    }
    
    return sendSuccess(res, paymentOrder);
  })
);

// Get payment transaction
router.get("/transaction/:id",
  authenticate,
  sanitizeIdParam,
  asyncHandler(async (req: any, res) => {
    const transactionId = parseInt(req.params.id);
    
    const transaction = await enhancedPaymentService.getPaymentTransaction(transactionId);
    if (!transaction) {
      throw new NotFoundError("Payment transaction not found");
    }
    
    // Verify transaction belongs to user's booking
    if (transaction.paymentOrderId) {
      const paymentOrder = await enhancedPaymentService.getPaymentOrder(transaction.paymentOrderId);
      if (paymentOrder && paymentOrder.bookingId) {
        const booking = await storage.getBooking(paymentOrder.bookingId);
        if (!booking || booking.userId !== req.user.userId) {
          throw new AuthorizationError("You can only view payment transactions for your own bookings");
        }
      }
    }
    
    return sendSuccess(res, transaction);
  })
);

// Get GST record for booking
router.get("/gst/:bookingId",
  authenticate,
  sanitizeIdParam,
  asyncHandler(async (req: any, res) => {
    const bookingId = parseInt(req.params.bookingId);
    
    // Verify booking belongs to user
    const booking = await storage.getBooking(bookingId);
    if (!booking || booking.userId !== req.user.userId) {
      throw new AuthorizationError("You can only view GST records for your own bookings");
    }
    
    const gstRecord = await gstCalculationService.getGSTRecord(bookingId);
    if (!gstRecord) {
      throw new NotFoundError("GST record not found");
    }
    
    return sendSuccess(res, gstRecord);
  })
);

export default router;