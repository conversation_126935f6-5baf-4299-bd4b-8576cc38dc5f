import { Router } from "express";
import rateLimit from "express-rate-limit";
import { z } from "zod";
import crypto from "crypto";
import { authenticate } from "./auth";
import { storage } from "../storage";
import { db } from "../db";
import { securitySessions, deviceVerifications, InsertSecuritySession, InsertDeviceVerification } from "../../shared/schema";
import { otpService } from "../otpService";
import { auditLogger } from "../services/AuditLogger";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError,
  AuthenticationError 
} from "../middlewares/errorHandler";
import { eq, and, gt } from "drizzle-orm";

const router = Router();

// Rate limiting for 2FA endpoints
const twoFALimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Max 3 2FA requests per hour
  message: {
    error: "Too many 2FA requests from this IP, please try again later.",
    retryAfter: 3600
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schemas
const send2FASchema = z.object({
  method: z.enum(['sms', 'email', 'totp']),
  sessionId: z.string().min(1),
  bookingId: z.number().int().positive(),
  amount: z.number().int().positive()
});

const verify2FASchema = z.object({
  code: z.string().length(6).regex(/^\d{6}$/),
  method: z.enum(['sms', 'email', 'totp']),
  sessionId: z.string().min(1),
  bookingId: z.number().int().positive()
});

const deviceVerifySchema = z.object({
  deviceFingerprint: z.string().min(1),
  requestVerification: z.boolean().default(true)
});

const deviceAuthSchema = z.object({
  deviceFingerprint: z.string().min(1),
  verificationCode: z.string().length(6).regex(/^\d{6}$/)
});

// Input validation middleware
function validate(schema: z.ZodSchema) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      const errorMessage = error.errors?.map((e: any) => e.message).join(", ") || error.message;
      return sendError(res, new ValidationError(errorMessage), req);
    }
  };
}

// Send 2FA code endpoint
router.post("/2fa/send",
  authenticate,
  twoFALimiter,
  validate(send2FASchema),
  asyncHandler(async (req: any, res) => {
    const { method, sessionId, bookingId, amount } = req.body;
    
    // Verify booking exists and belongs to user
    const booking = await storage.getBooking(bookingId);
    if (!booking || booking.userId !== req.user.userId) {
      throw new ValidationError("Invalid booking");
    }
    
    // Check if 2FA is required for this amount (≥ ₹10,000)
    if (amount < 10000) {
      throw new ValidationError("2FA not required for this transaction amount");
    }
    
    // Generate 6-digit code
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const expiryTime = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    
    // Store 2FA session
    const twoFASession: InsertSecuritySession = {
      userId: req.user.userId,
      sessionType: '2fa_payment',
      sessionData: {
        sessionId, // Store session ID in sessionData instead
        bookingId,
        amount,
        method,
        code: crypto.createHash('sha256').update(code).digest('hex') // Hash the code
      },
      expiresAt: expiryTime
    };
    
    await db.insert(securitySessions).values(twoFASession);
    
    // Send code via selected method
    let maskedContact = '';
    if (method === 'sms') {
      await otpService.sendOTP({ identifier: req.user.phone, type: 'sms' });
      maskedContact = `***-***-${req.user.phone.slice(-4)}`;
    } else if (method === 'email') {
      await otpService.sendOTP({ identifier: req.user.email, type: 'email' });
      maskedContact = `${req.user.email.slice(0, 2)}***@${req.user.email.split('@')[1]}`;
    }
    
    // Audit log
    await auditLogger.logPaymentAction('2FA_CODE_SENT', {
      userId: req.user.userId,
      actorType: 'user',
      actorId: req.user.userId,
      actorIp: req.ip,
      metadata: { method, bookingId, amount, sessionId }
    });
    
    return sendSuccess(res, {
      maskedContact,
      expiresIn: 300 // 5 minutes in seconds
    }, "Verification code sent successfully");
  })
);

// Verify 2FA code endpoint
router.post("/2fa/verify",
  authenticate,
  validate(verify2FASchema),
  asyncHandler(async (req: any, res) => {
    const { code, method, sessionId, bookingId } = req.body;
    
    // Find 2FA session
    const session = await db.select()
      .from(securitySessions)
      .where(
        and(
          eq(securitySessions.userId, req.user.userId),
          eq(securitySessions.sessionType, '2fa_payment'),
          gt(securitySessions.expiresAt, new Date())
        )
      )
      .limit(1);
    
    if (!session.length) {
      await auditLogger.logPaymentAction('2FA_VERIFICATION_FAILED', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { reason: 'session_not_found', sessionId, bookingId }
      });
      throw new AuthenticationError("Invalid or expired verification session");
    }
    
    const sessionData = session[0];
    const sessionInfo = sessionData.sessionData as { sessionId: string, bookingId: number, amount: number, method: string, code: string };
    const hashedCode = crypto.createHash('sha256').update(code).digest('hex');
    
    // Verify code
    if (sessionInfo?.code !== hashedCode) {
      await auditLogger.logPaymentAction('2FA_VERIFICATION_FAILED', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { reason: 'invalid_code', sessionId, bookingId }
      });
      throw new AuthenticationError("Invalid verification code");
    }
    
    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    
    // Update session with verification token
    await db.update(securitySessions)
      .set({ 
        sessionData: { 
          ...sessionInfo, 
          verified: true, 
          verificationToken 
        }
      })
      .where(eq(securitySessions.id, sessionData.id));
    
    // Audit log
    await auditLogger.logPaymentAction('2FA_VERIFICATION_SUCCESS', {
      userId: req.user.userId,
      actorType: 'user',
      actorId: req.user.userId,
      actorIp: req.ip,
      metadata: { method, sessionId, bookingId }
    });
    
    return sendSuccess(res, {
      verificationToken,
      expiresIn: 600 // 10 minutes to use the token
    }, "2FA verification successful");
  })
);

// Device verification endpoint
router.post("/device/verify",
  authenticate,
  validate(deviceVerifySchema),
  asyncHandler(async (req: any, res) => {
    const { deviceFingerprint, requestVerification } = req.body;
    
    // Check if device is already verified
    const existingDevice = await db.select()
      .from(deviceVerifications)
      .where(
        and(
          eq(deviceVerifications.userId, req.user.userId),
          eq(deviceVerifications.deviceFingerprint, deviceFingerprint),
          eq(deviceVerifications.isVerified, true)
        )
      )
      .limit(1);
    
    if (existingDevice.length > 0) {
      return sendSuccess(res, {
        deviceVerified: true,
        message: "Device already verified"
      });
    }
    
    if (requestVerification) {
      // Generate verification code
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const expiryTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      
      // Store device verification request
      const deviceVerification: InsertDeviceVerification = {
        userId: req.user.userId,
        deviceId: `device_${Date.now()}`, // Generate a device ID
        deviceFingerprint,
        deviceInfo: {
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'] || '',
          verificationCode: crypto.createHash('sha256').update(code).digest('hex')
        },
        verificationMethod: 'sms',
        expiresAt: expiryTime,
        isVerified: false
      };
      
      await db.insert(deviceVerifications).values(deviceVerification);
      
      // Send verification code
      await otpService.sendOTP({ identifier: req.user.phone, type: 'sms' });
      
      // Audit log
      await auditLogger.logPaymentAction('DEVICE_VERIFICATION_REQUESTED', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { deviceFingerprint }
      });
      
      return sendSuccess(res, {
        verificationRequested: true,
        maskedContact: `***-***-${req.user.phone.slice(-4)}`,
        expiresIn: 600
      }, "Device verification code sent");
    }
    
    return sendSuccess(res, {
      deviceVerified: false,
      message: "Device not verified"
    });
  })
);

// Device authentication endpoint
router.post("/device/authenticate",
  authenticate,
  validate(deviceAuthSchema),
  asyncHandler(async (req: any, res) => {
    const { deviceFingerprint, verificationCode } = req.body;
    
    // Find device verification request
    const deviceVerification = await db.select()
      .from(deviceVerifications)
      .where(
        and(
          eq(deviceVerifications.userId, req.user.userId),
          eq(deviceVerifications.deviceFingerprint, deviceFingerprint),
          gt(deviceVerifications.expiresAt, new Date()),
          eq(deviceVerifications.isVerified, false)
        )
      )
      .limit(1);
    
    if (!deviceVerification.length) {
      throw new AuthenticationError("Invalid or expired device verification request");
    }
    
    const hashedCode = crypto.createHash('sha256').update(verificationCode).digest('hex');
    const deviceInfo = deviceVerification[0].deviceInfo as { verificationCode: string, ipAddress?: string, userAgent?: string };
    
    if (deviceInfo?.verificationCode !== hashedCode) {
      await auditLogger.logPaymentAction('DEVICE_VERIFICATION_FAILED', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: { deviceFingerprint, reason: 'invalid_code' }
      });
      throw new AuthenticationError("Invalid verification code");
    }
    
    // Mark device as verified
    await db.update(deviceVerifications)
      .set({ isVerified: true, verifiedAt: new Date() })
      .where(eq(deviceVerifications.id, deviceVerification[0].id));
    
    // Generate device token
    const deviceToken = crypto.randomBytes(32).toString('hex');
    
    // Audit log
    await auditLogger.logPaymentAction('DEVICE_VERIFICATION_SUCCESS', {
      userId: req.user.userId,
      actorType: 'user',
      actorId: req.user.userId,
      actorIp: req.ip,
      metadata: { deviceFingerprint }
    });
    
    return sendSuccess(res, {
      deviceToken,
      verified: true
    }, "Device verification successful");
  })
);

export default router;