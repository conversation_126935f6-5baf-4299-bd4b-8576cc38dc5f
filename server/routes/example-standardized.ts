/**
 * EXAMPLE: Standardized Error Handling in Route Files
 * 
 * This example demonstrates how to properly implement error handling
 * in route files using the new standardized system.
 */

import { Router } from 'express';
import { 
  asyncHandler, 
  sendSuccess,
  ValidationError,
  NotFoundError,
  ConflictError,
  AuthorizationError
} from '../middlewares/errorHandler';
import { authenticate, authorize } from './auth';
import { storage } from '../storage';
import { z } from 'zod';

const router = Router();

// Helper to ensure authenticated user exists
const ensureAuthenticatedUser = (req: any) => {
  if (!req.user) {
    throw new AuthorizationError('Authentication required');
  }
  return req.user;
};

// Example validation schema
const createResourceSchema = z.object({
  name: z.string().min(3).max(100),
  description: z.string().optional(),
  price: z.number().positive(),
  category: z.enum(['basic', 'premium', 'enterprise'])
});

/**
 * ✅ GOOD: GET endpoint with proper error handling
 */
router.get('/resources/:id', authenticate, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Validate input
  const resourceId = parseInt(id);
  if (isNaN(resourceId)) {
    throw new ValidationError('Invalid resource ID format');
  }
  
  // Fetch resource (assuming storage throws NotFoundError if not found)
  const resource = await storage.getProperty(resourceId); // Using actual storage method
  
  // Additional authorization check
  const user = ensureAuthenticatedUser(req);
  if (resource && resource.ownerId !== user.userId && user.role !== 'admin') {
    throw new AuthorizationError('You do not have permission to view this resource');
  }
  
  return sendSuccess(res, resource);
}));

/**
 * ✅ GOOD: POST endpoint with validation and business logic errors
 */
router.post('/resources', 
  authenticate, 
  asyncHandler(async (req, res) => {
    // Validate request body
    let validatedData;
    try {
      validatedData = createResourceSchema.parse(req.body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError('Invalid request data', error.errors);
      }
      throw error;
    }
    
    // Check business rules
    const user = ensureAuthenticatedUser(req);
    const userProperties = await storage.getPropertiesByOwner(user.userId);
    const userResourceCount = userProperties.length;
    const maxResources = user.role === 'premium' ? 100 : 10;
    
    if (userResourceCount >= maxResources) {
      throw new ConflictError(`You have reached the maximum of ${maxResources} resources`);
    }
    
    // Check for duplicates - using property title as name equivalent
    const existingProperties = await storage.getPropertiesByOwner(user.userId);
    const existing = existingProperties.find(p => p.title === validatedData.name);
    if (existing) {
      throw new ConflictError(`Resource with name '${validatedData.name}' already exists`);
    }
    
    // Create resource - mapping to property creation
    const newResource = await storage.createProperty({
      title: validatedData.name,
      description: validatedData.description || '',
      location: 'Example Location',
      halfDayPrice: validatedData.price,
      fullDayPrice: validatedData.price * 1.5,
      images: [],
      amenities: [],
      maxGuests: 10,
      status: 'active',
      featured: false,
      ownerId: user.userId
    });
    
    return sendSuccess(res, newResource, 'Resource created successfully', 201);
  })
);

/**
 * ✅ GOOD: PUT endpoint with complex error scenarios
 */
router.put('/resources/:id', 
  authenticate,
  asyncHandler(async (req, res) => {
    const resourceId = parseInt(req.params.id);
    
    // Get existing resource
    const existing = await storage.getProperty(resourceId);
    if (!existing) {
      throw new NotFoundError('Resource');
    }
    
    // Check ownership
    const user = ensureAuthenticatedUser(req);
    if (existing.ownerId !== user.userId && user.role !== 'admin') {
      throw new AuthorizationError('You can only update your own resources');
    }
    
    // Validate update data
    const updateSchema = createResourceSchema.partial();
    const updates = updateSchema.parse(req.body);
    
    // Business rule: Cannot change status to inactive if there are active bookings
    // Note: updates doesn't have status property in this schema, this is example code
    // if (updates.status === 'inactive') {
    //   throw new ConflictError('Resource status cannot be changed when there are active bookings');
    // }
    
    // Apply updates - mapping to property update
    const updated = await storage.updateProperty(resourceId, {
      title: updates.name,
      description: updates.description,
      halfDayPrice: updates.price,
      fullDayPrice: updates.price ? updates.price * 1.5 : undefined
    });
    
    return sendSuccess(res, updated, 'Resource updated successfully');
  })
);

/**
 * ✅ GOOD: DELETE endpoint with proper authorization
 */
router.delete('/resources/:id',
  authenticate,
  authorize(['admin', 'owner']),
  asyncHandler(async (req, res) => {
    const resourceId = parseInt(req.params.id);
    
    const resource = await storage.getProperty(resourceId);
    if (!resource) {
      throw new NotFoundError('Resource');
    }
    
    // Additional ownership check for non-admins
    const user = ensureAuthenticatedUser(req);
    if (user.role !== 'admin' && resource.ownerId !== user.userId) {
      throw new AuthorizationError('You can only delete your own resources');
    }
    
    // Check if resource can be deleted - check for active bookings
    const bookings = await storage.getBookingsByProperty(resourceId);
    const hasActiveBookings = bookings.some(b => ['confirmed', 'pending'].includes(b.status));
    if (hasActiveBookings) {
      throw new ConflictError('Cannot delete resource with active bookings');
    }
    
    const deleted = await storage.deleteProperty(resourceId);
    
    return sendSuccess(res, null, 'Resource deleted successfully');
  })
);

/**
 * ✅ GOOD: Batch operation with proper error handling
 */
router.post('/resources/batch',
  authenticate,
  authorize(['admin']),
  asyncHandler(async (req, res) => {
    const { operations } = req.body;
    
    if (!Array.isArray(operations) || operations.length === 0) {
      throw new ValidationError('Operations must be a non-empty array');
    }
    
    if (operations.length > 100) {
      throw new ValidationError('Cannot process more than 100 operations at once');
    }
    
    const results: {
      successful: Array<{ index: number; result: any }>;
      failed: Array<{ index: number; operation: any; error: { code: any; message: any } }>;
    } = {
      successful: [],
      failed: []
    };
    
    // Process each operation
    for (const [index, operation] of operations.entries()) {
      try {
        // Validate operation
        if (!['create', 'update', 'delete'].includes(operation.type)) {
          throw new ValidationError(`Invalid operation type: ${operation.type}`);
        }
        
        // Process based on type
        let result;
        switch (operation.type) {
          case 'create':
            result = await storage.createProperty({
              title: operation.data.name || 'Batch Resource',
              description: operation.data.description || '',
              location: 'Batch Location',
              halfDayPrice: operation.data.price || 1000,
              fullDayPrice: (operation.data.price || 1000) * 1.5,
              images: [],
              amenities: [],
              maxGuests: 10,
              status: 'active',
              featured: false,
              ownerId: operation.data.ownerId || 1
            });
            break;
          case 'update':
            result = await storage.updateProperty(operation.id, {
              title: operation.data.name,
              description: operation.data.description,
              halfDayPrice: operation.data.price
            });
            break;
          case 'delete':
            await storage.deleteProperty(operation.id);
            result = { id: operation.id, deleted: true };
            break;
        }
        
        results.successful.push({ index, result });
      } catch (error) {
        // Collect errors but continue processing
        results.failed.push({
          index,
          operation: operation.type,
          error: {
            code: (error as any)?.code || 'UNKNOWN_ERROR',
            message: (error as any)?.message || 'Unknown error occurred'
          }
        });
      }
    }
    
    // Return partial success
    const statusCode = results.failed.length === 0 ? 200 : 207; // 207 = Multi-Status
    return sendSuccess(res, results, 'Batch operation completed', statusCode);
  })
);

/**
 * ❌ BAD: Manual try-catch (DON'T DO THIS)
 */
router.get('/bad-example', async (req, res) => {
  try {
    const data = await Promise.resolve({ example: 'data' }); // someAsyncOperation();
    res.json({ success: true, data });
  } catch (error) {
    // This bypasses our global error handler!
    console.error(error);
    res.status(500).json({ error: 'Something went wrong' });
  }
});

/**
 * ❌ BAD: Inconsistent error responses (DON'T DO THIS)
 */
router.post('/another-bad-example', authenticate, asyncHandler(async (req, res) => {
  const result = await Promise.resolve(null); // someOperation();
  
  if (!result) {
    // Don't send custom error formats!
    return res.status(400).json({ 
      message: 'Operation failed',
      status: 'error' 
    });
  }
  
  // Should use sendSuccess helper
  return res.json(result);
}));

export default router;