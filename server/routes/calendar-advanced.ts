import { Router, Request, Response } from 'express';
import { enhancedJWTValidation } from '../middlewares/enhancedAuth';
import { asyncHandler } from '../utils/asyncHandler';
import { sendSuccess, sendError } from '@shared/api-response-utils';
import { ERROR_CODES } from '@shared/api-response-types';
import { calendarSyncService } from '../services/CalendarSyncService';
import { calendarBulkImportService } from '../services/CalendarBulkImportService';
import { conflictResolutionService } from '../services/ConflictResolutionService';
import { multiPropertyCalendarService } from '../services/MultiPropertyCalendarService';
import { calendarAnalyticsService } from '../services/CalendarAnalyticsService';
import { calendarExportImportService } from '../services/CalendarExportImportService';
import { calendarSyncJobService } from '../services/CalendarSyncJobService';
import { logger } from '../services/LoggerService';
import multer from 'multer';

const router = Router();

// Configure multer for file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

/**
 * Calendar Sync Routes
 */

// Sync calendar with external provider
router.post('/sync/:propertyId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { propertyId } = req.params;
  const { provider, credentials, calendarId } = req.body;

  if (!provider) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Provider is required', 400);
  }

  try {
    const result = await calendarSyncService.syncCalendar(parseInt(propertyId), {
      provider,
      credentials,
      calendarId
    });

    return sendSuccess(res, {
      message: 'Calendar sync completed',
      result
    });
  } catch (error) {
    logger.error('Calendar sync failed', error as Error);
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Calendar sync failed', 500);
  }
}));

// Get sync statuses
router.get('/sync/status', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  try {
    const statuses = await calendarSyncService.getAllSyncStatuses();
    return sendSuccess(res, { statuses });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get sync statuses', 500);
  }
}));

/**
 * Bulk Import Routes
 */

// Bulk import bookings
router.post('/import/:propertyId', 
  enhancedJWTValidation, 
  upload.single('file'), 
  asyncHandler(async (req: Request, res: Response) => {
    const { propertyId } = req.params;
    const { format, source, conflictResolution, dryRun } = req.body;
    
    if (!req.file) {
      return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'File is required', 400);
    }

    try {
      const result = await calendarBulkImportService.importBookings(
        req.file.buffer,
        {
          propertyId: parseInt(propertyId),
          format,
          source: source || 'manual',
          conflictResolution: conflictResolution || 'skip',
          dryRun: dryRun === 'true'
        }
      );

      return sendSuccess(res, {
        message: dryRun === 'true' ? 'Import validation completed' : 'Import completed',
        result
      });
    } catch (error) {
      logger.error('Bulk import failed', error as Error);
      return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Import failed', 500);
    }
  })
);

// Generate import template
router.get('/import/template', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { format, source } = req.query;
  
  if (!format) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Format is required', 400);
  }

  try {
    const template = calendarBulkImportService.generateTemplate(
      format as 'csv' | 'json',
      source as string || 'custom'
    );

    res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="import-template.${format}"`);
    
    return res.send(template);
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to generate template', 500);
  }
}));

/**
 * Conflict Resolution Routes
 */

// Detect conflicts for date range
router.post('/conflicts/detect', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { propertyId, startDate, endDate, excludeBookingId } = req.body;

  if (!propertyId || !startDate || !endDate) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Property ID, start date, and end date are required', 400);
  }

  try {
    const conflicts = await conflictResolutionService.detectConflicts(
      propertyId,
      startDate,
      endDate,
      excludeBookingId
    );

    return sendSuccess(res, { conflicts });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to detect conflicts', 500);
  }
}));

// Auto-resolve conflicts
router.post('/conflicts/auto-resolve', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { conflicts } = req.body;

  if (!conflicts || !Array.isArray(conflicts)) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Conflicts array is required', 400);
  }

  try {
    const result = await conflictResolutionService.autoResolveConflicts(conflicts);
    return sendSuccess(res, { result });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to auto-resolve conflicts', 500);
  }
}));

// Manually resolve conflict
router.post('/conflicts/:conflictId/resolve', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { conflictId } = req.params;
  const { resolution } = req.body;

  if (!resolution) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Resolution is required', 400);
  }

  try {
    const result = await conflictResolutionService.resolveConflict(conflictId, resolution);
    return sendSuccess(res, { result });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to resolve conflict', 500);
  }
}));

/**
 * Multi-Property Calendar Routes
 */

// Get multi-property calendar view
router.post('/multi-property/view', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const options = req.body;

  if (!options.startDate || !options.endDate) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Start date and end date are required', 400);
  }

  try {
    const view = await multiPropertyCalendarService.getMultiPropertyView(options);
    return sendSuccess(res, { view });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get multi-property view', 500);
  }
}));

// Get owner portfolio view
router.get('/portfolio/:ownerId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { ownerId } = req.params;
  const { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Start date and end date are required', 400);
  }

  try {
    const portfolio = await multiPropertyCalendarService.getOwnerPortfolioView(
      parseInt(ownerId),
      startDate as string,
      endDate as string
    );
    return sendSuccess(res, { portfolio });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get owner portfolio', 500);
  }
}));

// Get calendar grid view
router.get('/grid', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { propertyIds, year, month, viewType } = req.query;

  if (!propertyIds) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Property IDs are required', 400);
  }

  try {
    const propertyIdList = (propertyIds as string).split(',').map(id => parseInt(id));
    const grid = await multiPropertyCalendarService.getCalendarGridView(
      propertyIdList,
      parseInt(year as string) || new Date().getFullYear(),
      parseInt(month as string) || new Date().getMonth() + 1,
      viewType as 'month' | 'week'
    );
    return sendSuccess(res, { grid });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get calendar grid', 500);
  }
}));

/**
 * Analytics Routes
 */

// Get booking analytics
router.post('/analytics', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { timeframe, propertyIds, ownerId } = req.body;

  if (!timeframe || !timeframe.start || !timeframe.end) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Timeframe with start and end dates is required', 400);
  }

  try {
    const analytics = await calendarAnalyticsService.getBookingAnalytics(
      timeframe,
      propertyIds,
      ownerId
    );
    return sendSuccess(res, { analytics });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get analytics', 500);
  }
}));

// Get owner analytics
router.get('/analytics/owner/:ownerId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { ownerId } = req.params;
  const { timeframe } = req.query;

  if (!timeframe) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Timeframe is required', 400);
  }

  try {
    const parsedTimeframe = JSON.parse(timeframe as string);
    const analytics = await calendarAnalyticsService.getOwnerAnalytics(
      parseInt(ownerId),
      parsedTimeframe
    );
    return sendSuccess(res, { analytics });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get owner analytics', 500);
  }
}));

// Get dashboard metrics
router.get('/analytics/dashboard', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { ownerId } = req.query;

  try {
    const metrics = await calendarAnalyticsService.getDashboardMetrics(
      ownerId ? parseInt(ownerId as string) : undefined
    );
    return sendSuccess(res, { metrics });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get dashboard metrics', 500);
  }
}));

/**
 * Export/Import Routes
 */

// Export calendar
router.post('/export', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const options = req.body;

  if (!options.startDate || !options.endDate || !options.format) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Start date, end date, and format are required', 400);
  }

  try {
    const result = await calendarExportImportService.exportCalendar(options);

    if (!result.success) {
      return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Export failed', 500, { errors: result.errors });
    }

    // Set appropriate headers for file download
    const contentType = getContentTypeForFormat(options.format);
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
    
    return res.send(result.data);
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Export failed', 500);
  }
}));

// Import calendar
router.post('/import-advanced/:propertyId',
  enhancedJWTValidation,
  upload.single('file'),
  asyncHandler(async (req: Request, res: Response) => {
    const { propertyId } = req.params;
    const { format, source, conflictResolution, validateOnly } = req.body;
    
    if (!req.file) {
      return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'File is required', 400);
    }

    try {
      const result = await calendarExportImportService.importCalendar(
        req.file.buffer,
        {
          propertyId: parseInt(propertyId),
          format,
          source,
          conflictResolution: conflictResolution || 'skip',
          validateOnly: validateOnly === 'true'
        }
      );

      return sendSuccess(res, { result });
    } catch (error) {
      return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Import failed', 500);
    }
  })
);

/**
 * Sync Job Routes
 */

// Create sync job
router.post('/sync-jobs', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { propertyId, provider, config } = req.body;

  if (!propertyId || !provider || !config) {
    return sendError(res, ERROR_CODES.MISSING_REQUIRED_FIELD, 'Property ID, provider, and config are required', 400);
  }

  try {
    const job = await calendarSyncJobService.createSyncJob(propertyId, provider, config);
    return sendSuccess(res, { job });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to create sync job', 500);
  }
}));

// Get all sync jobs
router.get('/sync-jobs', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  try {
    const jobs = calendarSyncJobService.getAllSyncJobs();
    const stats = calendarSyncJobService.getSyncJobStats();
    return sendSuccess(res, { jobs, stats });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get sync jobs', 500);
  }
}));

// Get sync job by ID
router.get('/sync-jobs/:jobId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { jobId } = req.params;
  
  try {
    const job = calendarSyncJobService.getSyncJob(jobId);
    if (!job) {
      return sendError(res, ERROR_CODES.NOT_FOUND, 'Sync job not found', 404);
    }
    
    const history = calendarSyncJobService.getJobHistory(jobId);
    return sendSuccess(res, { job, history });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get sync job', 500);
  }
}));

// Update sync job
router.put('/sync-jobs/:jobId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { jobId } = req.params;
  const updates = req.body;

  try {
    const job = await calendarSyncJobService.updateSyncJob(jobId, updates);
    return sendSuccess(res, { job });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to update sync job', 500);
  }
}));

// Delete sync job
router.delete('/sync-jobs/:jobId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { jobId } = req.params;

  try {
    await calendarSyncJobService.deleteSyncJob(jobId);
    return sendSuccess(res, { message: 'Sync job deleted' });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to delete sync job', 500);
  }
}));

// Toggle sync job
router.post('/sync-jobs/:jobId/toggle', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { jobId } = req.params;
  const { enabled } = req.body;

  try {
    const job = await calendarSyncJobService.toggleSyncJob(jobId, enabled);
    return sendSuccess(res, { job });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to toggle sync job', 500);
  }
}));

// Trigger sync job manually
router.post('/sync-jobs/:jobId/trigger', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { jobId } = req.params;

  try {
    const result = await calendarSyncJobService.triggerSyncJob(jobId);
    return sendSuccess(res, { result });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to trigger sync job', 500);
  }
}));

// Get sync jobs for property
router.get('/sync-jobs/property/:propertyId', enhancedJWTValidation, asyncHandler(async (req: Request, res: Response) => {
  const { propertyId } = req.params;

  try {
    const jobs = calendarSyncJobService.getSyncJobsForProperty(parseInt(propertyId));
    return sendSuccess(res, { jobs });
  } catch (error) {
    return sendError(res, ERROR_CODES.INTERNAL_ERROR, 'Failed to get property sync jobs', 500);
  }
}));

/**
 * Utility function to get content type for export formats
 */
function getContentTypeForFormat(format: string): string {
  switch (format) {
    case 'ical': return 'text/calendar';
    case 'csv': return 'text/csv';
    case 'json': return 'application/json';
    case 'excel': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'pdf': return 'application/pdf';
    default: return 'application/octet-stream';
  }
}

export default router;