import { Router } from "express";
import { storage } from "../storage";
import { authenticate } from "./auth";
import { requireOwnerRole } from "../middlewares/ownership";
import { ownerJWTValidation } from "../middlewares/enhancedAuth";
import AuditService from "../services/AuditService";
import { 
  asyncHandler, 
  sendSuccess, 
  ValidationError 
} from "../middlewares/errorHandler";
import rateLimit from "express-rate-limit";

const router = Router();

// Rate limiting for dashboard endpoints
const dashboardLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // Limit dashboard requests to 30 per minute
  message: {
    error: "Too many dashboard requests, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: any) => `dashboard:${req.user?.userId || req.ip}`,
});

interface DashboardSummary {
  properties: {
    total: number;
    active: number;
    featured: number;
  };
  bookings: {
    total: number;
    pending: number;
    confirmed: number;
    completed: number;
    cancelled: number;
    thisMonth: number;
    lastMonth: number;
  };
  revenue: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    averagePerBooking: number;
    averagePerProperty: number;
  };
  analytics: {
    topPerformingProperty?: {
      id: number;
      title: string;
      bookings: number;
      revenue: number;
    };
    occupancyRate: number;
    averageRating: number;
    responseRate: number;
  };
  recent: {
    newBookings: number; // Last 7 days
    pendingActions: number;
    recentMessages: number;
  };
  lastUpdated: string;
}

// Get comprehensive dashboard summary for instant load
router.get("/summary", 
  ownerJWTValidation,
  requireOwnerRole,
  dashboardLimiter,
  asyncHandler(async (req: any, res) => {
    const ownerId = req.user.userId;
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Log dashboard access for audit
    AuditService.logPropertyAction(
      ownerId,
      'read',
      'dashboard_summary',
      { action: 'load_dashboard' },
      req
    );

    try {
      // Fetch all data in parallel for better performance
      const [
        properties,
        allBookings
      ] = await Promise.all([
        storage.getPropertiesByOwner(ownerId),
        storage.getBookingsByOwner(ownerId)
      ]);

      // Filter bookings by date ranges
      const thisMonthBookings = allBookings.filter(b => {
        const bookingDate = new Date(b.bookingDate);
        return bookingDate >= startOfMonth && bookingDate <= now;
      });
      
      const lastMonthBookings = allBookings.filter(b => {
        const bookingDate = new Date(b.bookingDate);
        return bookingDate >= startOfLastMonth && bookingDate <= endOfLastMonth;
      });
      
      const recentBookings = allBookings.filter(b => {
        const bookingDate = new Date(b.bookingDate);
        return bookingDate >= sevenDaysAgo && bookingDate <= now;
      });

      // Calculate property metrics
      const propertyMetrics = {
        total: properties.length,
        active: properties.filter((p: any) => p.isActive !== false).length,
        featured: properties.filter((p: any) => p.featured === true).length
      };

      // Calculate booking metrics
      const bookingsByStatus = allBookings.reduce((acc: any, booking: any) => {
        acc[booking.status] = (acc[booking.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const bookingMetrics = {
        total: allBookings.length,
        pending: bookingsByStatus.pending || 0,
        confirmed: bookingsByStatus.confirmed || 0,
        completed: bookingsByStatus.completed || 0,
        cancelled: bookingsByStatus.cancelled || 0,
        thisMonth: thisMonthBookings.length,
        lastMonth: lastMonthBookings.length
      };

      // Calculate revenue metrics
      const confirmedBookings = allBookings.filter((b: any) => ['confirmed', 'completed'].includes(b.status));
      const totalRevenue = confirmedBookings.reduce((sum: any, b: any) => sum + (b.totalPrice || 0), 0);
      
      const thisMonthRevenue = thisMonthBookings
        .filter((b: any) => ['confirmed', 'completed'].includes(b.status))
        .reduce((sum: any, b: any) => sum + (b.totalPrice || 0), 0);
        
      const lastMonthRevenue = lastMonthBookings
        .filter((b: any) => ['confirmed', 'completed'].includes(b.status))
        .reduce((sum: any, b: any) => sum + (b.totalPrice || 0), 0);

      const revenueMetrics = {
        total: totalRevenue,
        thisMonth: thisMonthRevenue,
        lastMonth: lastMonthRevenue,
        averagePerBooking: confirmedBookings.length > 0 ? totalRevenue / confirmedBookings.length : 0,
        averagePerProperty: propertyMetrics.total > 0 ? totalRevenue / propertyMetrics.total : 0
      };

      // Calculate analytics
      let topPerformingProperty = null;
      if (properties.length > 0) {
        const propertyBookings = properties.map((property: any) => {
          const propBookings = confirmedBookings.filter((b: any) => b.propertyId === property.id);
          const propRevenue = propBookings.reduce((sum: any, b: any) => sum + (b.totalPrice || 0), 0);
          return {
            id: property.id,
            title: property.title,
            bookings: propBookings.length,
            revenue: propRevenue
          };
        });

        topPerformingProperty = propertyBookings.reduce((top: any, current: any) => 
          current.revenue > (top?.revenue || 0) ? current : top
        , null);
      }

      // Calculate occupancy rate (simplified - based on confirmed bookings vs total days)
      const totalDaysThisMonth = now.getDate();
      const totalPossibleBookingDays = propertyMetrics.total * totalDaysThisMonth;
      const actualBookingDays = thisMonthBookings
        .filter((b: any) => ['confirmed', 'completed'].includes(b.status)).length;
      const occupancyRate = totalPossibleBookingDays > 0 
        ? (actualBookingDays / totalPossibleBookingDays) * 100 
        : 0;

      const analytics = {
        topPerformingProperty,
        occupancyRate: Math.round(occupancyRate * 100) / 100,
        averageRating: 4.2, // Placeholder - would calculate from reviews
        responseRate: 95 // Placeholder - would calculate from response times
      };

      // Recent activity metrics
      const recent = {
        newBookings: recentBookings.length,
        pendingActions: bookingMetrics.pending,
        recentMessages: 0 // Placeholder - would fetch from messages system
      };

      const summary: DashboardSummary = {
        properties: propertyMetrics,
        bookings: bookingMetrics,
        revenue: revenueMetrics,
        analytics,
        recent,
        lastUpdated: now.toISOString()
      };

      return sendSuccess(res, summary, "Dashboard summary loaded successfully");

    } catch (error) {
      console.error('Dashboard summary error:', error);
      throw new Error('Failed to load dashboard summary');
    }
  })
);

// Get paginated properties for owner
router.get("/properties",
  ownerJWTValidation,
  requireOwnerRole,
  dashboardLimiter,
  asyncHandler(async (req: any, res) => {
    const ownerId = req.user.userId;
    const page = parseInt(req.query.page || '1');
    const limit = Math.min(parseInt(req.query.limit || '10'), 50); // Max 50 per page
    const search = req.query.search as string;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'desc';

    if (page < 1 || limit < 1) {
      throw new ValidationError("Page and limit must be positive numbers");
    }

    const offset = (page - 1) * limit;

    try {
      const allProperties = await storage.getPropertiesByOwner(ownerId);
      
      // Filter properties based on search
      let filteredProperties = allProperties;
      if (search) {
        filteredProperties = allProperties.filter(prop => 
          prop.title.toLowerCase().includes(search.toLowerCase()) ||
          prop.location.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Sort properties
      filteredProperties.sort((a: any, b: any) => {
        let aValue = (a as any)[sortBy as string];
        let bValue = (b as any)[sortBy as string];
        
        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();
        
        if (sortOrder === 'desc') {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        }
      });

      const total = filteredProperties.length;
      const properties = filteredProperties.slice(offset, offset + limit);

      const totalPages = Math.ceil(total / limit);
      
      return sendSuccess(res, {
        properties,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });

    } catch (error) {
      console.error('Paginated properties error:', error);
      throw new Error('Failed to load properties');
    }
  })
);

// Get paginated bookings for owner
router.get("/bookings",
  ownerJWTValidation,
  requireOwnerRole,
  dashboardLimiter,
  asyncHandler(async (req: any, res) => {
    const ownerId = req.user.userId;
    const page = parseInt(req.query.page || '1');
    const limit = Math.min(parseInt(req.query.limit || '20'), 100); // Max 100 per page
    const status = req.query.status as string;
    const propertyId = req.query.propertyId ? parseInt(req.query.propertyId as string) : undefined;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'desc';

    if (page < 1 || limit < 1) {
      throw new ValidationError("Page and limit must be positive numbers");
    }

    const offset = (page - 1) * limit;

    try {
      const allBookings = await storage.getBookingsByOwner(ownerId);
      
      // Filter bookings
      let filteredBookings = allBookings;
      if (status) {
        filteredBookings = filteredBookings.filter(booking => booking.status === status);
      }
      if (propertyId) {
        filteredBookings = filteredBookings.filter(booking => booking.propertyId === propertyId);
      }

      // Sort bookings
      filteredBookings.sort((a: any, b: any) => {
        let aValue = (a as any)[sortBy as string];
        let bValue = (b as any)[sortBy as string];
        
        if (aValue instanceof Date) aValue = aValue.getTime();
        if (bValue instanceof Date) bValue = bValue.getTime();
        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();
        
        if (sortOrder === 'desc') {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        }
      });

      const total = filteredBookings.length;
      const bookings = filteredBookings.slice(offset, offset + limit);

      const totalPages = Math.ceil(total / limit);

      return sendSuccess(res, {
        bookings,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });

    } catch (error) {
      console.error('Paginated bookings error:', error);
      throw new Error('Failed to load bookings');
    }
  })
);

// Get analytics data
router.get("/analytics",
  ownerJWTValidation,
  requireOwnerRole,
  dashboardLimiter,
  asyncHandler(async (req: any, res) => {
    const ownerId = req.user.userId;
    const timeframe = req.query.timeframe || '30d'; // 7d, 30d, 90d, 1y
    const propertyId = req.query.propertyId ? parseInt(req.query.propertyId as string) : undefined;

    try {
      // Create a simple analytics implementation
      const allBookings = await storage.getBookingsByOwner(ownerId);
      const properties = await storage.getPropertiesByOwner(ownerId);
      
      // Filter by property if specified
      let filteredBookings = allBookings;
      if (propertyId) {
        filteredBookings = allBookings.filter(b => b.propertyId === propertyId);
      }
      
      // Calculate basic analytics
      const analytics = {
        totalRevenue: filteredBookings
          .filter(b => ['confirmed', 'completed'].includes(b.status))
          .reduce((sum, b) => sum + (b.totalPrice || 0), 0),
        totalBookings: filteredBookings.length,
        confirmationRate: filteredBookings.length > 0 
          ? (filteredBookings.filter(b => b.status === 'confirmed').length / filteredBookings.length) * 100 
          : 0,
        averageBookingValue: filteredBookings.length > 0 
          ? filteredBookings.reduce((sum, b) => sum + (b.totalPrice || 0), 0) / filteredBookings.length 
          : 0
      };

      return sendSuccess(res, analytics);

    } catch (error) {
      console.error('Analytics error:', error);
      throw new Error('Failed to load analytics');
    }
  })
);

// Get recent activity feed
router.get("/activity",
  ownerJWTValidation,
  requireOwnerRole,
  dashboardLimiter,
  asyncHandler(async (req: any, res) => {
    const ownerId = req.user.userId;
    const limit = Math.min(parseInt(req.query.limit || '20'), 50);

    try {
      // Create a simple recent activity implementation
      const allBookings = await storage.getBookingsByOwner(ownerId);
      
      // Create activity items from recent bookings
      const activities = allBookings
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, limit)
        .map(booking => ({
          id: `booking_${booking.id}`,
          type: 'booking',
          action: `New booking: ${booking.status}`,
          description: `Booking for ${booking.property.title}`,
          timestamp: booking.createdAt,
          metadata: {
            bookingId: booking.id,
            propertyId: booking.propertyId,
            guestName: booking.guest.fullName
          }
        }));

      return sendSuccess(res, activities);

    } catch (error) {
      console.error('Activity feed error:', error);
      throw new Error('Failed to load recent activity');
    }
  })
);

export default router;