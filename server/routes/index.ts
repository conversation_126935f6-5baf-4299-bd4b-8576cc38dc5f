import express from "express";
import type { Express } from "express";
import { createServer, type Server } from "http";
import rateLimit from "express-rate-limit";
import cors from "cors";
import { config } from "../config";
import {
  globalError<PERSON><PERSON><PERSON>,
  not<PERSON>ound<PERSON><PERSON><PERSON>,
  as<PERSON><PERSON><PERSON><PERSON>,
  addRequestId
} from "../middlewares/errorHandler";
import { requestLoggingMiddleware } from "../middlewares/logging";
import { getAllowedOrigins } from "../middlewares/security";
import { errorBoundaryMiddleware } from "../utils/error-boundary";

// Import route modules
import authRoutes from "./auth";
import propertyRoutes from "./properties";
import bookingRoutes from "./bookings";
import reviewRoutes from "./reviews";
import uploadRoutes from "./uploads";
import cloudinaryRoutes from "./cloudinary";
import utilityRoutes from "./utilities";
import paymentRoutes from "./payments";
import webhookRoutes from "./webhooks";
import whatsappRoutes from "./whatsapp";
import calendarRoutes from "./calendar";
// import calendarAdvancedRoutes from "./calendar-advanced"; // Temporarily disabled due to service dependency issues
import websocketRoutes from "./websocket";
import otpRoutes from "../otpRoutes";
import swaggerRoutes from "../middlewares/swagger";
import healthRoutes from "./health";

// General rate limiter for API routes
const generalApiLimiter = rateLimit({
  windowMs: config.isDevelopment() ? 1 * 60 * 1000 : 15 * 60 * 1000, // 1 minute in dev, 15 minutes in prod
  max: config.isDevelopment() ? 1000 : 100, // 1000 in dev, 100 in prod
  message: {
    error: config.isDevelopment() 
      ? "Too many requests from this IP in development, please try again later."
      : "Too many requests from this IP, please try again later.",
    retryAfter: config.isDevelopment() ? 60 : 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => req.path === '/api/health'
});

// Enhanced CORS configuration for API routes
function configureCors() {
  return cors({
    origin: function (origin, callback) {
      const allowedOrigins = getAllowedOrigins();

      // Add specific origins for the application
      const replitDomain = process.env.REPLIT_DOMAINS;
      const appOrigins = config.isDevelopment() 
        ? [
            'http://localhost:5173',
            'http://localhost:3000', 
            'http://localhost:5000',
            'https://farmhouse.replit.app',
            replitDomain ? `https://${replitDomain}` : null,
            undefined // Allow requests with no origin (mobile apps, curl, etc.)
          ].filter(Boolean)
        : [
            'http://localhost:5173', // Allow localhost for production testing
            'http://localhost:3000', // Allow localhost for production testing
            'http://localhost:5000', // Allow localhost for production testing
            'https://farmhouse.replit.app',
            replitDomain ? `https://${replitDomain}` : null,
            'https://bookafarm.com',
            'https://www.bookafarm.com'
          ].filter(Boolean);

      const allAllowedOrigins = [...allowedOrigins, ...appOrigins];

      if (!origin || allAllowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.warn(`🚫 CORS blocked origin: ${origin} (Environment: ${config.app.nodeEnv})`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'X-Requested-With', 
      'Accept', 
      'Origin',
      'X-Request-ID',
      'Cache-Control',
      'Pragma',
      'X-Razorpay-Signature',
      'X-Razorpay-Event-Id',
      'X-Razorpay-Timestamp'
    ],
    exposedHeaders: [
      'X-Request-ID',
      'X-Cache',
      'X-Cache-Key',
      'X-Security-Enhanced'
    ],
    optionsSuccessStatus: 200,
    maxAge: 86400 // 24 hours
  });
}

export function registerRoutes(app: Express): Promise<Server> {
  const server = createServer(app);

  // Add request ID to all requests
  app.use("/api", addRequestId);

  // Apply structured logging to API routes
  app.use("/api", requestLoggingMiddleware);

  // Apply CORS to API routes
  app.use("/api", configureCors());

  // Apply rate limiting to API routes
  app.use("/api", generalApiLimiter);

  // Health check endpoints
  app.use("/api/health", healthRoutes);

  // API Documentation
  app.use("/api/docs", swaggerRoutes);

  // Mount route modules with versioning
  app.use("/api/v1/auth", authRoutes);
  app.use("/api/v1/users", authRoutes); // User management under /users for RESTful design
  app.use("/api/v1/properties", propertyRoutes);
  app.use("/api/v1/bookings", bookingRoutes);
  app.use("/api/v1/reviews", reviewRoutes);
  app.use("/api/v1/media", uploadRoutes); // Changed from uploads to media
  app.use("/api/v1/cloudinary", cloudinaryRoutes);
  app.use("/api/v1/utils", utilityRoutes);
  app.use("/api/v1/payments", paymentRoutes);
  app.use("/api/v1/webhooks", webhookRoutes);
  app.use("/api/v1/whatsapp", whatsappRoutes);
  app.use("/api/v1/calendar", calendarRoutes);
  // app.use("/api/v1/calendar-advanced", calendarAdvancedRoutes); // Temporarily disabled
  app.use("/api/v1/websocket", websocketRoutes);
  app.use("/api/v1/auth/otp", otpRoutes);

  // Legacy routes (for backward compatibility)
  app.use("/api/auth", authRoutes);
  app.use("/api/properties", propertyRoutes);
  app.use("/api/bookings", bookingRoutes);
  app.use("/api/reviews", reviewRoutes);
  app.use("/api/uploads", uploadRoutes);
  app.use("/api/cloudinary", cloudinaryRoutes);
  app.use("/api/payments", paymentRoutes);
  app.use("/api/webhooks", webhookRoutes);
  app.use("/api/whatsapp", whatsappRoutes);
  app.use("/api/calendar", calendarRoutes);
  // app.use("/api/calendar-advanced", calendarAdvancedRoutes); // Temporarily disabled
  app.use("/api/websocket", websocketRoutes);
  app.use("/api", utilityRoutes);
  app.use("/api/auth/otp", otpRoutes);

  // Direct profile route for convenience
  app.get("/api/profile", asyncHandler(async (req: any, res) => {
    // Import and use the same logic as auth profile
    const { authenticate } = await import("./auth");
    authenticate(req, res, async () => {
      res.redirect(307, "/api/auth/profile");
    });
  }));

  // Development endpoint to test routing
  if (config.isDevelopment()) {
    app.get("/api/dev/test", asyncHandler(async (req: any, res) => {
      res.json({ message: "Development endpoint is working directly", timestamp: new Date().toISOString() });
    }));
  }

  // Return server without adding error handlers here
  // Error handlers will be added in the main server file after static serving
  return Promise.resolve(server);
}