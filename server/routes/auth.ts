import { Router } from "express";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import rateLimit from "express-rate-limit";
import { storage } from "../storage";
import { config } from "../config";
import { db } from "../db";
import { users, properties, bookings, reviews, otpTokens } from "../../shared/schema";
import { eq, desc } from "drizzle-orm";
import { TokenBlacklistService } from "../services/TokenBlacklistService";
import { enhancedJWTValidation } from "../middlewares/enhancedAuth";
import AuditService from "../services/AuditService";
import { 
  userRegisterSchema,
} from "../../shared/schema";
import { 
  userLoginSchema,
} from "../../shared/validations";
import { 
  asyncHandler, 
  ValidationError,
  AuthenticationError,
  ConflictError 
} from "../middlewares/errorHandler";
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendAuthenticationError,
  sendAuthorizationError,
  sendConflictError,
  sendRateLimitError,
  getRequestId,
  withErrorHandling
} from "../../shared/api-response-utils";
import { ERROR_CODES, AuthResponse } from "../../shared/api-response-types";

const router = Router();

// Authentication middleware exports are at the bottom after function definitions

// Rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: config.isDevelopment() ? 1 * 60 * 1000 : 15 * 60 * 1000, // 1 minute in dev, 15 minutes in prod
  max: config.isDevelopment() ? 100 : 20, // 100 in dev, 20 in prod
  message: {
    error: config.isDevelopment() 
      ? "Too many authentication attempts from this IP in development, please try again later."
      : "Too many authentication attempts from this IP, please try again later.",
    retryAfter: config.isDevelopment() ? 60 : 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const MAX_LOGIN_ATTEMPTS = 5;
const loginAttemptsMap = new Map<string, number>();

// Input validation middleware
function validate(schema: any) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      return sendValidationError(res, error.message, undefined, getRequestId(req));
    }
  };
}

// Authentication middleware - Enhanced version with comprehensive security checks  
async function authenticate(req: any, res: any, next: any): Promise<any> {
  // Use enhanced JWT validation for improved security
  return enhancedJWTValidation(req, res, next);
}

// Legacy authentication middleware (kept for backward compatibility)
export async function basicAuthenticate(req: any, res: any, next: any): Promise<any> {
  let token = req.cookies.auth_token;
  
  // Extract token from Authorization header if not in cookies
  if (!token && req.headers.authorization) {
    const authHeader = req.headers.authorization;
    if (authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }
  }
  
  // Check if token exists and is not empty
  if (!token || token.trim() === '') {
    return sendAuthenticationError(res, "Authentication required", getRequestId(req));
  }

  try {
    // Check if token is blacklisted using cache service
    const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return sendAuthenticationError(res, "Token has been revoked", getRequestId(req));
    }

    // Verify JWT token
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    req.user = { userId: decoded.userId, role: decoded.role };
    return next();
  } catch (error) {
    return sendAuthenticationError(res, "Invalid or expired token", getRequestId(req));
  }
}

// Authorization middleware
function authorize(roles: string[]) {
  return (req: any, res: any, next: any) => {
    if (!req.user) {
      return sendAuthenticationError(res, "Authentication required", getRequestId(req));
    }

    if (!roles.includes(req.user.role)) {
      return sendAuthorizationError(res, "Insufficient permissions", getRequestId(req));
    }

    return next();
  };
}

// Register endpoint
router.post("/register", authLimiter, validate(userRegisterSchema), asyncHandler(async (req, res) => {
  const { username, email, password, fullName, role, consentData } = req.body;

  // Check if user already exists
  const existingUser = await storage.getUserByEmail(email);
  if (existingUser) {
    throw new ConflictError("User with this email already exists");
  }

  // Create new user
  const newUser = await storage.createUser({
    username,
    email,
    password,
    fullName,
    role: role || 'user',
    termsAccepted: consentData?.termsAccepted || true,
    privacyPolicyAccepted: consentData?.dataProcessingConsent || true,
    cookiePolicyAccepted: true,
    dataProcessingConsent: consentData?.dataProcessingConsent || true,
    marketingConsent: consentData?.marketingConsent || false,
    consentTimestamp: consentData?.consentTimestamp 
      ? new Date(consentData.consentTimestamp) 
      : new Date()
  });

  // Generate JWT token with enhanced security claims
  const now = Math.floor(Date.now() / 1000);
  const token = jwt.sign(
    { 
      userId: newUser.id, 
      role: newUser.role,
      iat: now,
      exp: now + (7 * 24 * 60 * 60) // 7 days
    },
    config.jwt.secret,
    { 
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithm: 'HS256'
    }
  );

  // Log successful registration for audit
  AuditService.logAuthAction(
    newUser.id,
    'login',
    {
      userRole: newUser.role,
      registrationMethod: 'email',
      ip: req.ip
    },
    req,
    true
  );

  // Set secure cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });

  const authResponse: AuthResponse = {
    user: {
      id: newUser.id,
      email: newUser.email,
      fullName: newUser.fullName,
      role: newUser.role
    },
    token,
    expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
  };

  return sendSuccess(res, authResponse, "Registration successful", 201, getRequestId(req));
}));

// Login endpoint
router.post("/login", authLimiter, validate(userLoginSchema), asyncHandler(async (req, res) => {
  const { identifier, password } = req.body;

  // Check login attempts
  const attempts = loginAttemptsMap.get(identifier) || 0;
  if (attempts >= MAX_LOGIN_ATTEMPTS) {
    throw new AuthenticationError("Too many failed login attempts. Please try again later.");
  }

  // Find user by email or phone
  const user = await storage.getUserByEmail(identifier) || await storage.getUserByPhone?.(identifier);
  if (!user) {
    loginAttemptsMap.set(identifier, attempts + 1);
    throw new AuthenticationError("Invalid email/phone or password");
  }

  // Verify password
  const validPassword = await bcrypt.compare(password, user.password);
  if (!validPassword) {
    loginAttemptsMap.set(identifier, attempts + 1);
    throw new AuthenticationError("Invalid email/phone or password");
  }

  // Reset login attempts on successful login
  loginAttemptsMap.delete(identifier);

  // Generate JWT token with enhanced security claims
  const now = Math.floor(Date.now() / 1000);
  const token = jwt.sign(
    { 
      userId: user.id, 
      role: user.role,
      iat: now,
      exp: now + (7 * 24 * 60 * 60) // 7 days
    },
    config.jwt.secret,
    { 
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithm: 'HS256'
    }
  );

  // Log successful login for audit
  AuditService.logAuthAction(
    user.id,
    'login',
    {
      userRole: user.role,
      loginMethod: 'email',
      ip: req.ip
    },
    req,
    true
  );

  // Set secure cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });

  return sendSuccess(res, {
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    },
    token
  }, "Login successful");
}));

// Get current user
router.get("/me", authenticate, asyncHandler(async (req: any, res) => {
  const user = await storage.getUser(req.user.userId);
  if (!user) {
    throw new AuthenticationError("User not found");
  }

  return sendSuccess(res, {
    id: user.id,
    username: user.username,
    email: user.email,
    fullName: user.fullName,
    role: user.role
  });
}));

// Logout endpoint
router.post("/logout", authenticate, asyncHandler(async (req: any, res) => {
  const token = req.cookies.auth_token || req.headers.authorization?.replace('Bearer ', '');
  
  if (token) {
    await TokenBlacklistService.blacklistToken(token);
  }

  // Log logout action for audit
  AuditService.logAuthAction(
    req.user.userId,
    'logout',
    {
      userRole: req.user.role,
      ip: req.ip
    },
    req,
    true
  );

  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict'
  });

  return sendSuccess(res, null, "Logout successful");
}));

// Logout from all devices endpoint
router.post("/logout-all", authenticate, asyncHandler(async (req: any, res) => {
  const token = req.cookies.auth_token || req.headers.authorization?.replace('Bearer ', '');
  
  if (token) {
    await TokenBlacklistService.blacklistToken(token);
  }

  // In a production system, you'd want to blacklist all tokens for this user
  // This would require tracking tokens per user in the database
  await TokenBlacklistService.blacklistUserTokens(req.user.userId);

  // Log logout from all devices action for audit
  AuditService.logAuthAction(
    req.user.userId,
    'logout',
    {
      userRole: req.user.role,
      logoutType: 'all_devices',
      ip: req.ip
    },
    req,
    true
  );

  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict'
  });

  return sendSuccess(res, null, "Logged out from all devices");
}));

// Get user profile
router.get("/profile", authenticate, asyncHandler(async (req: any, res) => {
  const user = await storage.getUser(req.user.userId);
  if (!user) {
    throw new AuthenticationError("User not found");
  }

  return sendSuccess(res, {
    id: user.id,
    username: user.username,
    email: user.email,
    fullName: user.fullName,
    phone: user.phone,
    address: user.address,
    bio: user.bio,
    role: user.role
  });
}));

// Update user profile
router.patch("/profile", authenticate, asyncHandler(async (req: any, res) => {
  const { fullName, phone, address, bio } = req.body;

  const updatedUser = await storage.updateUser(req.user.userId, {
    fullName,
    phone,
    address,
    bio
  });

  if (!updatedUser) {
    throw new AuthenticationError("User not found");
  }

  return sendSuccess(res, {
    id: updatedUser.id,
    username: updatedUser.username,
    email: updatedUser.email,
    fullName: updatedUser.fullName,
    phone: updatedUser.phone,
    address: updatedUser.address,
    bio: updatedUser.bio,
    role: updatedUser.role
  }, "Profile updated successfully");
}));

// RESTful User Management Routes (for /api/v1/users endpoint)
// GET /users/me - Get current user profile
router.get("/me", authenticate, asyncHandler(async (req: any, res) => {
  const user = await storage.getUser(req.user.userId);
  if (!user) {
    return sendError(res, "NOT_FOUND", "User not found", undefined, undefined, getRequestId(req));
  }

  const userData = {
    id: user.id,
    username: user.username,
    email: user.email,
    fullName: user.fullName,
    phone: user.phone,
    address: user.address,
    bio: user.bio,
    role: user.role
  };
  
  return sendSuccess(res, userData, "User profile retrieved successfully", 200, getRequestId(req));
}));

// PUT /users/me - Update current user profile
router.put("/me", authenticate, asyncHandler(async (req: any, res) => {
  const { phone, address, bio, fullName } = req.body;

  // Only allow updating specific fields
  const updateData: any = {};
  if (phone !== undefined) updateData.phone = phone || null;
  if (address !== undefined) updateData.address = address || null;
  if (bio !== undefined) updateData.bio = bio || null;
  if (fullName !== undefined) updateData.fullName = fullName;

  const updatedUser = await storage.updateUser(req.user.userId, updateData);

  if (!updatedUser) {
    return sendError(res, "NOT_FOUND", "User not found", undefined, undefined, getRequestId(req));
  }

  return sendSuccess(res, {
    id: updatedUser.id,
    username: updatedUser.username,
    email: updatedUser.email,
    fullName: updatedUser.fullName,
    phone: updatedUser.phone,
    address: updatedUser.address,
    bio: updatedUser.bio,
    role: updatedUser.role
  }, "Profile updated successfully", 200, getRequestId(req));
}));

// GET /users/me/properties - Get current user's properties
router.get("/me/properties", authenticate, authorize(["owner"]), asyncHandler(async (req: any, res) => {
  const properties = await storage.getPropertiesByOwner(req.user.userId);
  return sendSuccess(res, properties, "Properties retrieved successfully", 200, getRequestId(req));
}));

// GET /users/me/bookings - Get current user's bookings
router.get("/me/bookings", authenticate, asyncHandler(async (req: any, res) => {
  const allBookings = await storage.getBookings(req.user.userId);
  
  // Filter out bookings with 'pending_payment' status to hide incomplete transactions
  const bookings = allBookings.filter(booking => booking.status !== 'pending_payment');

  // Get property details for each booking
  const bookingsWithProperties = await Promise.all(
    bookings.map(async (booking) => {
      const property = await storage.getProperty(booking.propertyId);
      return {
        ...booking,
        property: property ? {
          id: property.id,
          title: property.title,
          location: property.location,
          images: property.images
        } : null
      };
    })
  );

  return sendSuccess(res, bookingsWithProperties, "Bookings retrieved successfully", 200, getRequestId(req));
}));

// GET /users/me/reviews - Get current user's reviews
router.get("/me/reviews", authenticate, asyncHandler(async (req: any, res) => {
  const reviews = await storage.getUserReviews(req.user.userId);
  return sendSuccess(res, reviews, "Reviews retrieved successfully", 200, getRequestId(req));
}));

// Admin endpoints for security management
router.get("/admin/security/blacklisted-tokens", authenticate, authorize(['admin']), asyncHandler(async (req: any, res) => {
  const stats = await TokenBlacklistService.getBlacklistStats();
  return sendSuccess(res, stats);
}));

// Force cleanup of expired tokens
router.post("/admin/security/cleanup-tokens", authenticate, authorize(['admin']), asyncHandler(async (req: any, res) => {
  await TokenBlacklistService.clearExpiredTokens();
  return sendSuccess(res, null, "Token cleanup completed");
}));

// Development endpoint to delete all users and related data
if (config.isDevelopment()) {
  
  // Add a simple test endpoint first
  router.get("/dev/test", asyncHandler(async (req: any, res) => {
    return res.json({ message: "Development endpoint is working", timestamp: new Date().toISOString() });
  }));
  
  // Development endpoint to view current users
  router.get("/dev/users", asyncHandler(async (req: any, res) => {
    const allUsers = await db.select().from(users);
    return res.json({ users: allUsers, count: allUsers.length });
  }));
  
  // Development endpoint to upgrade user to owner
  router.post("/dev/upgrade-to-owner", asyncHandler(async (req: any, res) => {
    const { email } = req.body;
    if (!email) {
      return sendError(res, "VALIDATION_ERROR", "Email is required", undefined, undefined, getRequestId(req));
    }
    
    const user = await storage.getUserByEmail(email);
    if (!user) {
      return sendError(res, "NOT_FOUND", "User not found", undefined, undefined, getRequestId(req));
    }
    
    const updatedUser = await storage.updateUser(user.id, { role: 'owner' });
    return sendSuccess(res, { user: updatedUser }, "User upgraded to owner successfully", 200, getRequestId(req));
  }));
  
  router.post("/dev/reset-all-users", asyncHandler(async (req: any, res) => {
    try {
      console.log('🚨 [DEV] Starting user reset (users and auth data only) - UPDATED VERSION...');
      
      // Delete only user-related data in order (foreign key dependencies)
      // 1. OTP tokens (user authentication)
      const deletedOtpTokens = await db.delete(otpTokens);
      console.log(`🗑️ Deleted all OTP tokens`);
      
      // 2. Users (main table)
      const deletedUsers = await db.delete(users);
      console.log(`🗑️ Deleted all users`);
      
      // Clear token blacklist (user authentication)
      await TokenBlacklistService.clearExpiredTokens();
      console.log(`🗑️ Cleared token blacklist`);
      
      console.log('✅ [DEV] User reset completed successfully (properties, bookings, reviews preserved)');
      
      return sendSuccess(res, {
        message: "All users deleted successfully",
        details: {
          otpTokens: "deleted",
          users: "deleted",
          tokenBlacklist: "cleared",
          preserved: {
            properties: "kept",
            bookings: "kept", 
            reviews: "kept"
          }
        }
      }, "User reset completed", 200, getRequestId(req));
      
    } catch (error) {
      console.error('❌ [DEV] Error during user data reset:', error);
      return sendError(res, "INTERNAL_ERROR", "Failed to reset user data", undefined, undefined, getRequestId(req));
    }
  }));
  
  // Development endpoint to manually trigger booking cleanup
  router.post("/dev/cleanup-bookings", asyncHandler(async (req: any, res) => {
    try {
      // Import the cleanup service dynamically to avoid circular imports
      const { bookingCleanupService } = await import('../services/BookingCleanupService');
      const deletedCount = await bookingCleanupService.runManualCleanup();
      
      return res.json({ 
        success: true, 
        message: `Cleaned up ${deletedCount} stale pending_payment bookings`,
        deletedCount 
      });
    } catch (error) {
      console.error('Manual cleanup failed:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Cleanup failed', 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }));
  
  // Development endpoint to get cleanup service status
  router.get("/dev/cleanup-status", asyncHandler(async (req: any, res) => {
    try {
      const { bookingCleanupService } = await import('../services/BookingCleanupService');
      const status = bookingCleanupService.getStatus();
      
      return res.json({ 
        success: true, 
        status,
        message: 'Cleanup service status retrieved successfully'
      });
    } catch (error) {
      console.error('Failed to get cleanup status:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to get cleanup status', 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }));
  
  // Development endpoint to check pending payment bookings
  router.get("/dev/pending-bookings", asyncHandler(async (req: any, res) => {
    try {
      const { storage } = await import('../storage');
      
      // Get all pending_payment bookings directly from database
      const result = await db.select()
        .from(bookings)
        .where(eq(bookings.status, 'pending_payment'))
        .orderBy(desc(bookings.createdAt));
      
      const now = new Date();
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      
      const pendingBookings = result.map(booking => ({
        id: booking.id,
        userId: booking.userId, 
        status: booking.status,
        createdAt: booking.createdAt,
        bookingDate: booking.bookingDate,
        isStale: booking.createdAt < oneHourAgo,
        ageInMinutes: Math.round((now.getTime() - booking.createdAt.getTime()) / (1000 * 60))
      }));
      
      return res.json({ 
        success: true,
        currentTime: now.toISOString(),
        oneHourAgo: oneHourAgo.toISOString(),
        totalPendingBookings: pendingBookings.length,
        staleBookings: pendingBookings.filter(b => b.isStale).length,
        bookings: pendingBookings
      });
    } catch (error) {
      console.error('Failed to get pending bookings:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to get pending bookings', 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }));
  
  // Development endpoint to test user booking filtering
  router.get("/dev/user-bookings/:userId", asyncHandler(async (req: any, res) => {
    try {
      const userId = parseInt(req.params.userId);
      
      // Get all bookings for user (including pending_payment)
      const allBookings = await db.select()
        .from(bookings)
        .where(eq(bookings.userId, userId))
        .orderBy(desc(bookings.createdAt));
      
      // Filter out pending_payment bookings (as the current system does)
      const filteredBookings = allBookings.filter(booking => booking.status !== 'pending_payment');
      
      return res.json({ 
        success: true,
        userId,
        totalBookings: allBookings.length,
        filteredBookingsCount: filteredBookings.length,
        hiddenPendingPayments: allBookings.length - filteredBookings.length,
        allBookings: allBookings.map(b => ({
          id: b.id,
          status: b.status,
          createdAt: b.createdAt,
          bookingDate: b.bookingDate
        })),
        filteredBookings: filteredBookings.map(b => ({
          id: b.id,
          status: b.status,
          createdAt: b.createdAt,
          bookingDate: b.bookingDate
        }))
      });
    } catch (error) {
      console.error('Failed to get user bookings:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to get user bookings', 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }));
  
  // Development endpoint to clean up duplicate reviews
  router.post("/dev/cleanup-duplicate-reviews", asyncHandler(async (req: any, res) => {
    try {
      console.log('🧹 [DEV] Starting duplicate review cleanup...');
      
      // Get all reviews
      const { reviews } = await import('../../shared/schema');
      const allReviews = await db.select().from(reviews).orderBy(desc(reviews.createdAt));
      
      // Group reviews by user and property
      const reviewGroups = new Map<string, any[]>();
      
      for (const review of allReviews) {
        const key = `${review.userId}-${review.propertyId}`;
        if (!reviewGroups.has(key)) {
          reviewGroups.set(key, []);
        }
        reviewGroups.get(key)!.push(review);
      }
      
      // Find and remove duplicates (keep most recent)
      let duplicatesRemoved = 0;
      const duplicateIds: number[] = [];
      
      reviewGroups.forEach((userPropertyReviews, key) => {
        if (userPropertyReviews.length > 1) {
          // Sort by createdAt descending (most recent first)
          userPropertyReviews.sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          
          // Keep the first (most recent), mark others for deletion
          const toDelete = userPropertyReviews.slice(1);
          for (const duplicate of toDelete) {
            duplicateIds.push(duplicate.id);
            duplicatesRemoved++;
          }
          
          console.log(`🔍 Found ${userPropertyReviews.length} reviews for user ${userPropertyReviews[0].userId} on property ${userPropertyReviews[0].propertyId}, keeping most recent`);
        }
      });
      
      // Delete duplicate reviews
      if (duplicateIds.length > 0) {
        const { inArray } = await import('drizzle-orm');
        await db.delete(reviews).where(inArray(reviews.id, duplicateIds));
        console.log(`🗑️ Removed ${duplicatesRemoved} duplicate reviews`);
      }
      
      return res.json({
        success: true,
        message: `Cleaned up ${duplicatesRemoved} duplicate reviews`,
        duplicatesRemoved,
        reviewGroupsProcessed: reviewGroups.size
      });
      
    } catch (error) {
      console.error('❌ [DEV] Error during duplicate review cleanup:', error);
      return res.status(500).json({
        success: false,
        message: 'Duplicate review cleanup failed',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }));
}

// Export authentication functions for other modules
export { authenticate, authorize };

export default router;