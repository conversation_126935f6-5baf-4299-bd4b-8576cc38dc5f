import { Router } from "express";
import { HealthService } from "../services/HealthService";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError,
  AppError
} from "../middlewares/errorHandler";
import { cacheService } from "../services/CacheService";
import { dltSMSService } from "../services/DLTSMSService";
import { EmailOTPProvider } from "../services/EmailOTPProvider";
import { config } from "../config";
import { databaseManager } from "../utils/database";
import { getErrorBoundaryHealth } from "../utils/error-boundary";
// Import new enhanced services
import { healthCheckService } from "../services/HealthCheckService";
import { metricsService } from "../services/MetricsService";
import { errorRecoveryService } from "../services/ErrorRecoveryService";
import { logger } from "../services/LoggerService";

const router = Router();

// Basic health check for load balancers
router.get("/", asyncHandler(async (req, res) => {
  const health = await HealthService.quickHealthCheck();
  
  if (health.status === 'ok') {
    return sendSuccess(res, health);
  } else {
    return res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Service is unhealthy'
      },
      data: health,
      timestamp: new Date().toISOString()
    });
  }
}));

// Comprehensive health check
router.get("/detailed", asyncHandler(async (req, res) => {
  const healthReport = await HealthService.performHealthCheck();
  
  let statusCode = 200;
  if (healthReport.status === 'unhealthy') {
    statusCode = 503;
  } else if (healthReport.status === 'degraded') {
    statusCode = 200; // Still operational but with warnings
  }

  return res.status(statusCode).json({
    success: healthReport.status !== 'unhealthy',
    data: healthReport,
    timestamp: new Date().toISOString()
  });
}));

// Kubernetes/Docker readiness probe
router.get("/ready", asyncHandler(async (req, res) => {
  const readiness = await HealthService.readinessCheck();
  
  if (readiness.ready) {
    return sendSuccess(res, readiness);
  } else {
    return res.status(503).json({
      success: false,
      error: {
        code: 'NOT_READY',
        message: 'Service is not ready to accept traffic',
        details: readiness.checks
      },
      data: readiness,
      timestamp: new Date().toISOString()
    });
  }
}));

// Kubernetes/Docker liveness probe
router.get("/live", asyncHandler(async (req, res) => {
  const liveness = await HealthService.livenessCheck();
  
  if (liveness.alive) {
    return sendSuccess(res, liveness);
  } else {
    return res.status(503).json({
      success: false,
      error: {
        code: 'NOT_ALIVE',
        message: 'Service is not alive'
      },
      data: liveness,
      timestamp: new Date().toISOString()
    });
  }
}));

// Cache statistics endpoint
router.get("/cache", asyncHandler(async (req, res) => {
  const stats = cacheService.getStats();
  return sendSuccess(res, {
    ...stats,
    timestamp: new Date().toISOString()
  });
}));

// System metrics endpoint
router.get("/metrics", asyncHandler(async (req, res) => {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  const metrics = {
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
      arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024)
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    process: {
      uptime: process.uptime(),
      pid: process.pid,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    },
    cache: cacheService.getStats(),
    timestamp: new Date().toISOString()
  };

  return sendSuccess(res, metrics);
}));

// Environment information (non-sensitive)
router.get("/info", asyncHandler(async (req, res) => {
  const info = {
    service: 'Farmhouse Rental API',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    features: {
      compression: true,
      caching: true,
      rateLimit: true,
      cors: true,
      helmet: true
    }
  };

  return sendSuccess(res, info);
}));

// Database stability health check
router.get("/database", asyncHandler(async (req, res) => {
  const dbHealth = {
    connection: await databaseManager.testConnection(),
    metrics: databaseManager.getMetrics(),
    poolStats: databaseManager.getPoolStats(),
    errorBoundary: getErrorBoundaryHealth(),
    timestamp: new Date().toISOString()
  };

  const isHealthy = dbHealth.connection && 
                   dbHealth.errorBoundary.status !== 'critical' &&
                   dbHealth.metrics.connectionErrors < 10;

  const statusCode = isHealthy ? 200 : 503;

  return res.status(statusCode).json({
    success: isHealthy,
    data: dbHealth,
    timestamp: new Date().toISOString()
  });
}));

// Connection pool metrics
router.get("/database/pool", asyncHandler(async (req, res) => {
  const poolMetrics = {
    stats: databaseManager.getPoolStats(),
    metrics: databaseManager.getMetrics(),
    health: await databaseManager.testConnection(),
    recommendations: [] as string[],
    timestamp: new Date().toISOString()
  };

  // Generate recommendations based on metrics
  const { total, idle, waiting } = poolMetrics.stats;
  const { connectionErrors } = poolMetrics.metrics;

  if (waiting > 5) {
    poolMetrics.recommendations.push('High connection wait queue - consider increasing pool size');
  }
  if (connectionErrors > 50) {
    poolMetrics.recommendations.push('High error rate - check database connectivity');
  }
  if (idle === 0 && total > 5) {
    poolMetrics.recommendations.push('No idle connections - pool may be under pressure');
  }

  return sendSuccess(res, poolMetrics);
}));

// Force cache cleanup (admin endpoint)
router.post("/cache/cleanup", asyncHandler(async (req, res) => {
  // Simple cleanup trigger
  await cacheService.clear();
  
  return sendSuccess(res, {
    message: 'Cache cleared successfully',
    timestamp: new Date().toISOString()
  });
}));

// DLT SMS Service Health Check (development endpoint)
router.get("/dlt-sms", asyncHandler(async (req, res) => {
  const health = dltSMSService.getServiceHealth();
  const connectionTest = await dltSMSService.testConnection();
  
  const smsHealth = {
    ...health,
    connectionTest,
    templateInfo: dltSMSService.getTemplateInfo('booking_confirmation'),
    environment: config.app.nodeEnv,
    configStatus: {
      twilioAvailable: config.twilio.available,
      dltAvailable: config.dlt.available,
      messagingServiceConfigured: !!config.twilio.messagingServiceSid
    },
    timestamp: new Date().toISOString()
  };
  
  return sendSuccess(res, smsHealth);
}));

// Test DLT SMS sending (development only)
router.post("/dlt-sms/test", asyncHandler(async (req, res) => {
  if (!config.isDevelopment()) {
    return sendError(res, new AppError(
      'This endpoint is only available in development environment',
      403,
      'FORBIDDEN'
    ));
  }

  const { phone, amount, propertyName } = req.body;
  
  if (!phone || !amount || !propertyName) {
    return sendError(res, new AppError(
      'Missing required fields: phone, amount, propertyName',
      400,
      'VALIDATION_ERROR'
    ));
  }

  try {
    const result = await dltSMSService.sendBookingConfirmation(
      phone,
      amount.toString(),
      propertyName
    );

    return sendSuccess(res, {
      smsResult: result,
      testInfo: {
        phone,
        amount,
        propertyName,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return sendError(res, new AppError(
      `Failed to test DLT SMS service: ${errorMessage}`,
      500,
      'SMS_TEST_ERROR'
    ));
  }
}));

// Email Service Health Check (development endpoint)
router.get("/email", asyncHandler(async (req, res) => {
  const emailProvider = new EmailOTPProvider();
  
  const emailHealth = {
    available: emailProvider.isAvailable(),
    sendgridConfigured: config.sendgrid.available,
    environment: config.app.nodeEnv,
    timestamp: new Date().toISOString()
  };
  
  return sendSuccess(res, emailHealth);
}));

// Test Email sending (development only)
router.post("/email/test", asyncHandler(async (req, res) => {
  if (!config.isDevelopment()) {
    return sendError(res, new AppError(
      'This endpoint is only available in development environment',
      403,
      'FORBIDDEN'
    ));
  }

  const { email } = req.body;
  
  if (!email) {
    return sendError(res, new AppError(
      'Missing required field: email',
      400,
      'VALIDATION_ERROR'
    ));
  }

  try {
    const emailProvider = new EmailOTPProvider();
    const testCode = '123456';
    
    const result = await emailProvider.sendOTP(email, testCode);

    return sendSuccess(res, {
      emailResult: result,
      testInfo: {
        email,
        code: testCode,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Email test error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return sendError(res, new AppError(
      `Failed to test email service: ${errorMessage}`,
      500,
      'EMAIL_TEST_ERROR'
    ));
  }
}));

// Enhanced metrics endpoint with Prometheus format support
router.get("/metrics/prometheus", asyncHandler(async (req, res) => {
  try {
    const prometheusMetrics = metricsService.getPrometheusMetrics();
    
    // Record request
    metricsService.incrementCounter('metrics_endpoint_requests_total', 1, {
      format: 'prometheus'
    });

    res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    return res.status(200).send(prometheusMetrics);
  } catch (error) {
    logger.error('Prometheus metrics endpoint failed', error instanceof Error ? error : new Error(String(error)), 'health');

    return sendError(res, new AppError(
      'Failed to generate Prometheus metrics',
      500,
      'METRICS_ERROR'
    ));
  }
}));

// Enhanced system health with new health check service
router.get("/system", asyncHandler(async (req, res) => {
  try {
    const systemHealth = await healthCheckService.checkSystemHealth();
    
    // Record health check
    metricsService.incrementCounter('enhanced_health_checks_total', 1, {
      status: systemHealth.status
    });

    const statusCode = systemHealth.status === 'HEALTHY' ? 200 : 
                      systemHealth.status === 'DEGRADED' ? 200 : 503;

    return res.status(statusCode).json({
      success: systemHealth.status !== 'UNHEALTHY',
      data: {
        ...systemHealth,
        legacy: {
          // Include legacy health service data for backward compatibility
          legacyHealth: await HealthService.performHealthCheck()
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Enhanced system health check failed', error instanceof Error ? error : new Error(String(error)), 'health');

    return sendError(res, new AppError(
      'Enhanced system health check failed',
      500,
      'HEALTH_CHECK_ERROR'
    ));
  }
}));

// Error recovery service status
router.get("/error-recovery", asyncHandler(async (req, res) => {
  try {
    const recoveryStatus = errorRecoveryService.getQueueStatus();
    
    metricsService.incrementCounter('error_recovery_status_requests_total', 1);

    return sendSuccess(res, {
      ...recoveryStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error recovery status check failed', error instanceof Error ? error : new Error(String(error)), 'health');

    return sendError(res, new AppError(
      'Failed to get error recovery status',
      500,
      'RECOVERY_STATUS_ERROR'
    ));
  }
}));

// Comprehensive diagnostics endpoint
router.get("/diagnostics", asyncHandler(async (req, res) => {
  // Protect in production with authentication
  if (process.env.NODE_ENV === 'production') {
    const authHeader = req.headers.authorization;
    const diagnosticToken = process.env.DIAGNOSTIC_TOKEN;
    
    if (!authHeader || !diagnosticToken || authHeader !== `Bearer ${diagnosticToken}`) {
      return sendError(res, new AppError(
        'Unauthorized access to diagnostics endpoint',
        401,
        'UNAUTHORIZED'
      ));
    }
  }

  try {
    const diagnostics = await healthCheckService.getDiagnostics();
    
    metricsService.incrementCounter('diagnostics_requests_total', 1, {
      authenticated: process.env.NODE_ENV === 'production' ? 'true' : 'false'
    });

    return sendSuccess(res, diagnostics);
  } catch (error) {
    logger.error('Diagnostics endpoint failed', error instanceof Error ? error : new Error(String(error)), 'health');

    return sendError(res, new AppError(
      'Failed to generate diagnostics',
      500,
      'DIAGNOSTICS_ERROR'
    ));
  }
}));

// Application metrics in structured format
router.get("/metrics/structured", asyncHandler(async (req, res) => {
  try {
    const allMetrics = metricsService.getAllMetrics();
    
    metricsService.incrementCounter('metrics_endpoint_requests_total', 1, {
      format: 'structured'
    });

    return sendSuccess(res, {
      ...allMetrics,
      timestamp: new Date().toISOString(),
      collectionDuration: Date.now() - Date.now() // Would be calculated properly in real implementation
    });
  } catch (error) {
    logger.error('Structured metrics endpoint failed', error instanceof Error ? error : new Error(String(error)), 'health');

    return sendError(res, new AppError(
      'Failed to generate structured metrics',
      500,
      'METRICS_ERROR'
    ));
  }
}));

export default router;