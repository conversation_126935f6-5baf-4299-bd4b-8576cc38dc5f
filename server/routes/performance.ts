import { Router } from 'express';
import { asyncHandler } from '../utils/asyncHandler';
import { cachedTemplateService } from '../services/CachedTemplateService';
import { jobQueue } from '../services/AsyncJobQueue';
import { otpRateLimitService } from '../services/OTPRateLimitService';
import { cachedQueryService } from '../services/CachedQueryService';
import { cacheService } from '../services/CacheService';
import { dltSMSService } from '../services/DLTSMSService';
import { log } from '../utils/logger';

const router = Router();

// Cache performance metrics
router.get('/cache', asyncHandler(async (req, res) => {
  try {
    const [
      cacheStats,
      templateMetrics,
      queryMetrics
    ] = await Promise.all([
      cacheService.getStats(),
      cachedTemplateService.getCacheMetrics(),
      cachedQueryService.getCacheMetrics()
    ]);

    res.json({
      timestamp: new Date().toISOString(),
      cacheService: cacheStats,
      templateCache: templateMetrics,
      queryCache: queryMetrics,
      summary: {
        totalCacheSize: cacheStats.size,
        memoryUsage: cacheStats.memoryUsage,
        templateHitRate: templateMetrics.hitRate,
        queryAvgHitRate: queryMetrics.queryPerformance.length > 0 
          ? queryMetrics.queryPerformance.reduce((sum, q) => sum + q.hitRate, 0) / queryMetrics.queryPerformance.length 
          : 0
      }
    });
  } catch (error) {
    log(`❌ Cache metrics error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to get cache metrics' });
  }
}));

// Job queue performance metrics
router.get('/jobs', asyncHandler(async (req, res) => {
  try {
    const metrics = jobQueue.getMetrics();
    
    res.json({
      timestamp: new Date().toISOString(),
      jobQueue: metrics,
      performance: {
        throughput: metrics.completedJobs > 0 ? metrics.totalJobs / (Date.now() / 1000 / 60) : 0, // jobs per minute
        successRate: metrics.totalJobs > 0 ? (metrics.completedJobs / metrics.totalJobs) * 100 : 0,
        failureRate: metrics.totalJobs > 0 ? (metrics.failedJobs / metrics.totalJobs) * 100 : 0,
        avgProcessingTime: metrics.averageProcessingTime
      }
    });
  } catch (error) {
    log(`❌ Job metrics error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to get job metrics' });
  }
}));

// SMS service performance
router.get('/sms', asyncHandler(async (req, res) => {
  try {
    const serviceHealth = await dltSMSService.getServiceHealth();
    
    res.json({
      timestamp: new Date().toISOString(),
      smsService: serviceHealth,
      performance: {
        templatesLoaded: serviceHealth.templatesLoaded,
        twilioConnected: serviceHealth.twilioConnected,
        dltConfigured: serviceHealth.dltConfigured,
        jobQueueHealthy: (serviceHealth.jobQueueMetrics?.queueLength || 0) < 100,
        cacheHealthy: (serviceHealth.cacheMetrics?.hitRate || 0) > 50
      }
    });
  } catch (error) {
    log(`❌ SMS metrics error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to get SMS metrics' });
  }
}));

// Rate limiting metrics
router.get('/rate-limits', asyncHandler(async (req, res) => {
  try {
    // This would show aggregate rate limiting stats
    // For privacy, we don't show individual user data
    
    res.json({
      timestamp: new Date().toISOString(),
      rateLimiting: {
        service: 'active',
        cacheHealthy: await cacheService.exists('rate_limit_test'),
        features: {
          otpRequestLimiting: true,
          otpVerificationLimiting: true,
          dailyLimits: true,
          cooldownPeriods: true
        }
      }
    });
  } catch (error) {
    log(`❌ Rate limit metrics error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to get rate limit metrics' });
  }
}));

// Overall system performance summary
router.get('/summary', asyncHandler(async (req, res) => {
  try {
    const [
      cacheHealth,
      queryHealth,
      jobMetrics,
      smsHealth
    ] = await Promise.all([
      cacheService.getStats(),
      cachedQueryService.healthCheck(),
      jobQueue.getMetrics(),
      dltSMSService.getServiceHealth()
    ]);

    // Calculate overall performance score
    const scores = {
      cache: cacheHealth.size > 0 ? 100 : 0,
      queries: queryHealth.cacheAvailable ? 100 : 0,
      jobs: jobMetrics.totalJobs > 0 ? Math.min(100, (jobMetrics.completedJobs / jobMetrics.totalJobs) * 100) : 100,
      sms: smsHealth.twilioConnected ? 100 : 50,
      templates: smsHealth.templatesLoaded > 0 ? 100 : 0
    };

    const overallScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.keys(scores).length;

    res.json({
      timestamp: new Date().toISOString(),
      overallPerformance: {
        score: Math.round(overallScore),
        status: overallScore >= 90 ? 'excellent' : overallScore >= 70 ? 'good' : overallScore >= 50 ? 'fair' : 'poor',
        components: scores
      },
      caching: {
        enabled: true,
        healthy: queryHealth.cacheAvailable,
        hitRate: queryHealth.avgCacheHitRate,
        size: cacheHealth.size,
        memoryUsage: cacheHealth.memoryUsage
      },
      asyncProcessing: {
        enabled: true,
        queueLength: jobMetrics.queueLength,
        processing: jobMetrics.processingJobs,
        avgTime: jobMetrics.averageProcessingTime,
        throughput: jobMetrics.completedJobs
      },
      smsService: {
        operational: smsHealth.twilioConnected,
        templatesReady: smsHealth.templatesLoaded > 0,
        dltCompliant: smsHealth.dltConfigured,
        asyncEnabled: true
      },
      recommendations: generateRecommendations(overallScore, {
        cacheHealth,
        queryHealth,
        jobMetrics,
        smsHealth
      })
    });
  } catch (error) {
    log(`❌ Performance summary error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to get performance summary' });
  }
}));

// Cache warming endpoint
router.post('/cache/warm', asyncHandler(async (req, res) => {
  try {
    await Promise.all([
      cachedTemplateService.warmCache(),
      cachedQueryService.warmCache()
    ]);

    res.json({
      success: true,
      message: 'Cache warming completed',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    log(`❌ Cache warming error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to warm cache' });
  }
}));

// Cache cleanup endpoint
router.post('/cache/cleanup', asyncHandler(async (req, res) => {
  try {
    await Promise.all([
      otpRateLimitService.cleanup(),
      cachedQueryService.cleanup(),
      jobQueue.cleanup()
    ]);

    res.json({
      success: true,
      message: 'Cache cleanup completed',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    log(`❌ Cache cleanup error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    res.status(500).json({ error: 'Failed to cleanup cache' });
  }
}));

// Performance test endpoint
router.post('/test', asyncHandler(async (req, res) => {
  const { testType = 'basic', iterations = 10 } = req.body;
  
  try {
    const results = await runPerformanceTest(testType, iterations);
    
    // Check if results contain an error that should return 500 (e.g., unknown test type)
    if (results.error && results.error.includes('Unknown test type')) {
      return res.status(500).json({ error: 'Performance test failed' });
    }
    
    // For other errors (execution errors), return 200 with detailed error info
    return res.json({
      testType,
      iterations,
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    log(`❌ Performance test error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'performance');
    return res.status(500).json({ error: 'Performance test failed' });
  }
}));

// Helper functions
function generateRecommendations(score: number, metrics: any): string[] {
  const recommendations: string[] = [];

  if (score < 70) {
    recommendations.push('Overall performance is below optimal. Consider reviewing system resources.');
  }

  if (metrics.cacheHealth.size === 0) {
    recommendations.push('Cache is empty. Run cache warming to improve performance.');
  }

  if (metrics.jobMetrics.queueLength > 50) {
    recommendations.push('Job queue is growing. Consider increasing processing capacity.');
  }

  if (metrics.jobMetrics.averageProcessingTime > 5000) {
    recommendations.push('Job processing time is high. Optimize job handlers.');
  }

  if (!metrics.smsHealth.twilioConnected) {
    recommendations.push('SMS service is not connected. Check Twilio configuration.');
  }

  if (metrics.queryHealth.avgCacheHitRate < 50) {
    recommendations.push('Query cache hit rate is low. Review caching strategy.');
  }

  if (recommendations.length === 0) {
    recommendations.push('System performance is optimal. All metrics are within acceptable ranges.');
  }

  return recommendations;
}

async function runPerformanceTest(testType: string, iterations: number): Promise<any> {
  const startTime = Date.now();
  const results: any = {
    testType,
    iterations,
    startTime: new Date(startTime).toISOString(),
    operations: []
  };

  try {
    switch (testType) {
      case 'basic':
      case 'cache':
        for (let i = 0; i < iterations; i++) {
          const opStart = Date.now();
          await cacheService.set(`test_${i}`, { data: `test_data_${i}` }, 60);
          const value = await cacheService.get(`test_${i}`);
          await cacheService.delete(`test_${i}`);
          
          results.operations.push({
            operation: i,
            duration: Date.now() - opStart,
            success: value !== null
          });
        }
        break;

      case 'template':
        for (let i = 0; i < iterations; i++) {
          const opStart = Date.now();
          await cachedTemplateService.getAllTemplates('active');
          
          results.operations.push({
            operation: i,
            duration: Date.now() - opStart,
            success: true
          });
        }
        break;

      case 'jobs':
        const jobIds: string[] = [];
        for (let i = 0; i < Math.min(iterations, 5); i++) { // Limit job tests
          const opStart = Date.now();
          const jobId = await jobQueue.addJob('test_job', { test: `data_${i}` });
          jobIds.push(jobId);
          
          results.operations.push({
            operation: i,
            duration: Date.now() - opStart,
            success: true,
            jobId
          });
        }
        break;

      default:
        throw new Error(`Unknown test type: ${testType}`);
    }

    results.endTime = new Date().toISOString();
    results.totalDuration = Date.now() - startTime;
    results.avgOperationTime = results.operations.reduce((sum: number, op: any) => sum + op.duration, 0) / results.operations.length;
    results.successRate = (results.operations.filter((op: any) => op.success).length / results.operations.length) * 100;

    return results;

  } catch (error) {
    results.error = error instanceof Error ? error.message : 'Unknown error';
    results.endTime = new Date().toISOString();
    results.totalDuration = Math.max(Date.now() - startTime, 1); // Ensure duration is at least 1ms
    return results;
  }
}

export default router;