import { Router } from "express";
import { storage } from "../storage";
import { reviewFormSchema, reviewResponseSchema } from "../../shared/schema";
import { authenticate, authorize } from "./auth";
import { sanitizeIdParam } from "../middlewares/sanitization";
import { 
  asyncHandler, 
  ValidationError,
  NotFoundError,
  AuthorizationError 
} from "../middlewares/errorHandler";
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendAuthorizationError,
  getRequestId
} from "../../shared/api-response-utils";

const router = Router();

// Input validation middleware
function validate(schema: any) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      return sendValidationError(res, error.message, undefined, getRequestId(req));
    }
  };
}

// Get reviews for a property
router.get("/property/:id", sanitizeIdParam, asyncHandler(async (req, res) => {
  const propertyId = parseInt(req.params.id);
  
  const property = await storage.getProperty(propertyId);
  if (!property) {
    throw new NotFoundError("Property");
  }

  const reviews = await storage.getReviews(propertyId);
  return sendSuccess(res, reviews);
}));

// Get user's reviews
router.get("/user/me", authenticate, asyncHandler(async (req: any, res) => {
  const reviews = await storage.getUserReviews(req.user.userId);
  return sendSuccess(res, reviews);
}));

// Get single review
router.get("/:id", sanitizeIdParam, asyncHandler(async (req, res) => {
  const review = await storage.getReview(parseInt(req.params.id));
  
  if (!review) {
    throw new NotFoundError("Review");
  }

  return sendSuccess(res, review);
}));

// Create new review
router.post("/", 
  authenticate, 
  validate(reviewFormSchema), 
  asyncHandler(async (req: any, res) => {
    const { propertyId, rating, comment } = req.body;
    let { bookingId } = req.body;

    // Check if property exists
    const property = await storage.getProperty(propertyId);
    if (!property) {
      return sendNotFoundError(res, "Property not found", getRequestId(req));
    }

    // Validate that user has past bookings for this property
    const userBookings = await storage.getBookings(req.user.userId);
    const propertyBookings = userBookings.filter((booking: any) => 
      booking.propertyId === propertyId && 
      (booking.status === 'confirmed' || booking.status === 'completed') &&
      new Date(booking.bookingDate) < new Date() // Past bookings only
    );

    if (propertyBookings.length === 0) {
      return sendAuthorizationError(
        res, 
        "You can only review properties you have stayed at. Please complete a booking first.",
        getRequestId(req)
      );
    }

    // Check if user has already reviewed this property (one review per user per property)
    const existingReviews = await storage.getReviews(propertyId);
    const hasAlreadyReviewed = existingReviews.some((review: any) => review.userId === req.user.userId);
    
    if (hasAlreadyReviewed) {
      return sendAuthorizationError(
        res,
        "You have already reviewed this property. You can edit your existing review instead.",
        getRequestId(req)
      );
    }

    // If bookingId is provided, verify it's one of the user's valid bookings
    if (bookingId) {
      const specificBooking = propertyBookings.find((booking: any) => booking.id === bookingId);
      if (!specificBooking) {
        return sendValidationError(
          res,
          "Invalid booking ID or booking not eligible for review",
          undefined,
          getRequestId(req)
        );
      }
    } else {
      // If no bookingId provided, use the most recent valid booking
      const mostRecentBooking = propertyBookings.sort((a: any, b: any) => 
        new Date(b.bookingDate).getTime() - new Date(a.bookingDate).getTime()
      )[0];
      bookingId = mostRecentBooking.id;
    }

    // Create review
    const newReview = await storage.createReview({
      propertyId,
      userId: req.user.userId,
      bookingId,
      rating,
      comment
    });

    return sendSuccess(res, newReview, "Review created successfully", 201);
  })
);

// Update review (users can only update their own reviews)
router.put("/:id", 
  authenticate, 
  sanitizeIdParam, 
  validate(reviewFormSchema), 
  asyncHandler(async (req: any, res) => {
    const reviewId = parseInt(req.params.id);
    const { rating, comment } = req.body;

    // Get existing review
    const existingReview = await storage.getReview(reviewId);
    if (!existingReview) {
      throw new NotFoundError("Review");
    }

    // Check if user owns the review
    if (existingReview.userId !== req.user.userId) {
      throw new AuthorizationError("You can only update your own reviews");
    }

    const updatedReview = await storage.updateReview(reviewId, {
      rating,
      comment
    });

    if (!updatedReview) {
      throw new Error("Failed to update review");
    }

    return sendSuccess(res, updatedReview, "Review updated successfully");
  })
);

// Add owner response to review (owners only)
router.post("/:id/response", 
  authenticate, 
  authorize(["owner"]), 
  sanitizeIdParam, 
  validate(reviewResponseSchema), 
  asyncHandler(async (req: any, res) => {
    const reviewId = parseInt(req.params.id);
    const { response } = req.body;

    // Get review and verify ownership of property
    const review = await storage.getReview(reviewId);
    if (!review) {
      throw new NotFoundError("Review");
    }

    // Check if user owns the property
    const property = await storage.getProperty(review.propertyId);
    if (!property || property.ownerId !== req.user.userId) {
      throw new AuthorizationError("You can only respond to reviews for your own properties");
    }

    const updatedReview = await storage.addOwnerResponse(reviewId, response);
    
    if (!updatedReview) {
      throw new Error("Failed to add owner response");
    }

    return sendSuccess(res, updatedReview, "Owner response added successfully");
  })
);

// Delete review (users can only delete their own reviews)
router.delete("/:id", 
  authenticate, 
  sanitizeIdParam, 
  asyncHandler(async (req: any, res) => {
    const reviewId = parseInt(req.params.id);

    // Get existing review
    const existingReview = await storage.getReview(reviewId);
    if (!existingReview) {
      throw new NotFoundError("Review");
    }

    // Check if user owns the review
    if (existingReview.userId !== req.user.userId) {
      throw new AuthorizationError("You can only delete your own reviews");
    }

    const deleted = await storage.deleteReview(reviewId);
    
    if (!deleted) {
      throw new Error("Failed to delete review");
    }

    return sendSuccess(res, null, "Review deleted successfully");
  })
);

// Get average rating for a property
router.get("/property/:id/average", sanitizeIdParam, asyncHandler(async (req, res) => {
  const propertyId = parseInt(req.params.id);
  
  const property = await storage.getProperty(propertyId);
  if (!property) {
    throw new NotFoundError("Property");
  }

  const averageRating = await storage.getPropertyAverageRating(propertyId);
  return sendSuccess(res, { averageRating });
}));

// Cleanup duplicate reviews endpoint
router.post("/cleanup-duplicates", asyncHandler(async (req: any, res) => {
  try {
    console.log('🧹 Starting duplicate review cleanup...');
    
    // Import required modules
    const { reviews } = await import('../../shared/schema');
    const { db } = await import('../db');
    const { desc, inArray } = await import('drizzle-orm');
    
    // Get all reviews
    const allReviews = await db.select().from(reviews).orderBy(desc(reviews.createdAt));
    
    // Group reviews by user and property
    const reviewGroups = new Map<string, any[]>();
    
    for (const review of allReviews) {
      const key = `${review.userId}-${review.propertyId}`;
      if (!reviewGroups.has(key)) {
        reviewGroups.set(key, []);
      }
      reviewGroups.get(key)!.push(review);
    }
    
    // Find and remove duplicates (keep most recent)
    let duplicatesRemoved = 0;
    const duplicateIds: number[] = [];
    
    reviewGroups.forEach((userPropertyReviews, key) => {
      if (userPropertyReviews.length > 1) {
        // Sort by createdAt descending (most recent first)
        userPropertyReviews.sort((a: any, b: any) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        
        // Keep the first (most recent), mark others for deletion
        const toDelete = userPropertyReviews.slice(1);
        for (const duplicate of toDelete) {
          duplicateIds.push(duplicate.id);
          duplicatesRemoved++;
        }
        
        console.log(`🔍 Found ${userPropertyReviews.length} reviews for user ${userPropertyReviews[0].userId} on property ${userPropertyReviews[0].propertyId}, keeping most recent`);
      }
    });
    
    // Delete duplicate reviews
    if (duplicateIds.length > 0) {
      await db.delete(reviews).where(inArray(reviews.id, duplicateIds));
      console.log(`🗑️ Removed ${duplicatesRemoved} duplicate reviews`);
    }
    
    return sendSuccess(res, {
      duplicatesRemoved,
      reviewGroupsProcessed: reviewGroups.size,
      duplicateIds
    }, `Cleaned up ${duplicatesRemoved} duplicate reviews`);
    
  } catch (error) {
    console.error('❌ Error during duplicate review cleanup:', error);
    return sendError(
      res,
      "CLEANUP_ERROR",
      "Failed to cleanup duplicate reviews",
      500,
      error instanceof Error ? { error: error.message } : undefined,
      getRequestId(req)
    );
  }
}));

export default router;