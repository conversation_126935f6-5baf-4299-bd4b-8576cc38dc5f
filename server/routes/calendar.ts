import { Router } from "express";
import rateLimit from "express-rate-limit";
import { authenticate, authorize } from "./auth";
import { 
  verifyPropertyOwnership,
  requireOwnerRole,
  validateOwnerOperation
} from "../middlewares/ownership";
import { sanitizeIdParam } from "../middlewares/sanitization";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError,
  NotFoundError,
  AuthorizationError,
  ValidationError
} from "../middlewares/errorHandler";
import {
  sendPaginatedResponse,
  withErrorHandling
} from "../../shared/api-response-utils";
import { calendarService } from "../services/CalendarService";
import { storage } from "../storage";
import { 
  insertCalendarBookingSchema,
  insertCalendarSyncStatusSchema,
  calendarBookingFormSchema
} from "../../shared/schema";
import { z } from "zod";
import { logger } from "../utils/structured-logger";

const router = Router();

// Rate limiting for calendar read operations
const calendarReadLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute for read operations
  message: {
    error: "Too many calendar read requests, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for calendar write operations
const calendarWriteLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 write operations per minute
  message: {
    error: "Too many calendar write operations, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: any) => `calendar_write:${req.user?.userId || req.ip}`,
});

// Rate limiting for availability checks
const availabilityLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 availability checks per minute
  message: {
    error: "Too many availability check requests, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Input validation middleware
function validate(schema: z.ZodSchema<any>) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      const errorMessage = error.errors?.map((e: any) => e.message).join(", ") || error.message;
      const validationError = new ValidationError(errorMessage, { zodErrors: error.errors });
      return sendError(res, validationError, req);
    }
  };
}

// Query parameter validation schemas
const calendarFiltersSchema = z.object({
  propertyId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  status: z.string().optional().transform(val => val ? val.split(',') : undefined),
  source: z.string().optional().transform(val => val ? val.split(',') : undefined),
  bookingType: z.string().optional().transform(val => val ? val.split(',') : undefined),
  limit: z.string().optional().transform(val => val ? Math.min(parseInt(val), 100) : 20),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
});

const availabilityCheckSchema = z.object({
  propertyId: z.number().min(1, "Property ID is required"),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
});

const propertyCalendarSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
});

// Partial schemas for updates (without the refine validation)
const updateCalendarBookingSchema = z.object({
  propertyId: z.number().optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format").optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format").optional(),
  status: z.enum(['confirmed', 'tentative', 'blocked', 'cancelled']).optional(),
  bookingType: z.string().optional(),
  guestName: z.string().optional(),
  guestPhone: z.string().optional(),
  guestCount: z.number().int().positive().optional(),
  notes: z.string().optional(),
  source: z.string().optional(),
  externalId: z.string().optional(),
});

const updateCalendarSyncStatusSchema = z.object({
  propertyId: z.number().optional(),
  calendarType: z.string().min(1, "Calendar type is required").optional(),
  lastSyncAt: z.date().optional(),
  syncToken: z.string().optional(),
  isActive: z.boolean().optional(),
  webhookUrl: z.string().url("Invalid webhook URL").optional(),
  syncSettings: z.record(z.any()).optional(),
  errorMessage: z.string().optional()
});

// Utility function to check property ownership
const checkPropertyOwnership = async (userId: number, propertyId: number): Promise<boolean> => {
  try {
    const property = await storage.getProperty(propertyId);
    return property ? property.ownerId === userId : false;
  } catch (error) {
    logger.error('Failed to check property ownership', {
      component: 'CalendarAPI',
      userId: userId.toString(),
      propertyId: propertyId.toString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
};

// Middleware to validate property access for calendar operations
const validateCalendarAccess = asyncHandler(async (req: any, res: any, next: any) => {
  const { propertyId } = req.params;
  const { propertyId: bodyPropertyId } = req.body;
  const targetPropertyId = propertyId || bodyPropertyId;

  if (targetPropertyId) {
    // Check if user owns the property or has access to it
    const hasAccess = await checkPropertyOwnership(req.user.userId, parseInt(targetPropertyId));
    if (!hasAccess && req.user.role !== 'owner') {
      throw new AuthorizationError("You don't have access to manage this property's calendar");
    }
  }
  
  next();
});

// =====================================================
// CALENDAR BOOKINGS ENDPOINTS
// =====================================================

/**
 * @route GET /api/v1/calendar/bookings
 * @desc Get calendar bookings with optional filters
 * @access Private (Owner/Manager)
 */
router.get("/bookings", 
  authenticate, 
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      // Validate and parse query parameters
      const filters = calendarFiltersSchema.parse(req.query);
      
      // For non-admin users, filter bookings based on property ownership
      if (req.user.role !== 'owner') {
        // Additional filtering could be added here if needed
      }
      
      logger.info('Fetching calendar bookings', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        filters
      });
      
      // Clean filters to match CalendarFilters interface
      const cleanFilters: any = {};
      if (filters.propertyId !== undefined) cleanFilters.propertyId = filters.propertyId;
      if (filters.startDate !== undefined) cleanFilters.startDate = filters.startDate;
      if (filters.endDate !== undefined) cleanFilters.endDate = filters.endDate;
      if (filters.status !== undefined) cleanFilters.status = filters.status;
      if (filters.source !== undefined) cleanFilters.source = filters.source;
      if (filters.bookingType !== undefined) cleanFilters.bookingType = filters.bookingType;
      if (filters.limit !== undefined) cleanFilters.limit = filters.limit;
      if (filters.offset !== undefined) cleanFilters.offset = filters.offset;
      
      const bookings = await calendarService.getCalendarBookings(cleanFilters);
      
      // Return paginated response
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;
      const page = Math.floor(offset / limit) + 1;
      
      return sendPaginatedResponse(
        res,
        bookings,
        {
          page,
          limit,
          total: bookings.length // In production, you might want to get actual total count
        },
        "Calendar bookings fetched successfully"
      );
      
    } catch (error) {
      logger.error('Failed to fetch calendar bookings', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route POST /api/v1/calendar/:propertyId/bookings
 * @desc Create a new calendar booking for a property
 * @access Private (Owner/Manager)
 */
router.post("/:propertyId/bookings", 
  authenticate, 
  sanitizeIdParam,
  calendarWriteLimiter,
  validate(insertCalendarBookingSchema),
  validateCalendarAccess,
  asyncHandler(async (req: any, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      
      logger.info('Creating calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId
      });
      
      // Add the current user as the creator and use propertyId from params
      const bookingData = {
        ...req.body,
        propertyId,
        createdBy: req.user.userId
      };
      
      const booking = await calendarService.createCalendarBooking(bookingData);
      
      logger.info('Calendar booking created successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId: booking.id
      });
      
      return sendSuccess(res, booking, "Calendar booking created successfully", 201);
      
    } catch (error) {
      logger.error('Failed to create calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId: req.params.propertyId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route POST /api/v1/calendar/bookings
 * @desc Create a new calendar booking
 * @access Private (Owner/Manager)
 */
router.post("/bookings", 
  authenticate, 
  calendarWriteLimiter,
  validate(insertCalendarBookingSchema),
  validateCalendarAccess,
  asyncHandler(async (req: any, res) => {
    try {
      logger.info('Creating calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId: req.body.propertyId
      });
      
      // Add the current user as the creator
      const bookingData = {
        ...req.body,
        createdBy: req.user.userId
      };
      
      const booking = await calendarService.createCalendarBooking(bookingData);
      
      logger.info('Calendar booking created successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId: booking.id
      });
      
      return sendSuccess(res, booking, "Calendar booking created successfully", 201);
      
    } catch (error) {
      logger.error('Failed to create calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId: req.body.propertyId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route GET /api/v1/calendar/bookings/:id
 * @desc Get a single calendar booking by ID
 * @access Private (Owner/Manager)
 */
router.get("/bookings/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      logger.debug('Fetching calendar booking by ID', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId
      });
      
      const booking = await calendarService.getCalendarBookingById(bookingId);
      
      if (!booking) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      // Check if user has access to this booking
      if (booking.property && req.user.role !== 'owner') {
        const hasAccess = await checkPropertyOwnership(req.user.userId, booking.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to this calendar booking");
        }
      }
      
      return sendSuccess(res, booking);
      
    } catch (error) {
      logger.error('Failed to fetch calendar booking by ID', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route PATCH /api/v1/calendar/bookings/:id
 * @desc Update a calendar booking (FullCalendar compatible)
 * @access Private (Owner/Manager)
 */
router.patch("/bookings/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarWriteLimiter,
  validate(updateCalendarBookingSchema),
  asyncHandler(async (req: any, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      logger.info('Updating calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId,
        updateFields: Object.keys(req.body)
      });
      
      // First check if booking exists and user has access
      const existingBooking = await calendarService.getCalendarBookingById(bookingId);
      if (!existingBooking) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      if (req.user.role !== 'owner') {
        const hasAccess = await checkPropertyOwnership(req.user.userId, existingBooking.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to modify this calendar booking");
        }
      }
      
      const updatedBooking = await calendarService.updateCalendarBooking(bookingId, req.body);
      
      if (!updatedBooking) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      logger.info('Calendar booking updated successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId
      });
      
      return sendSuccess(res, updatedBooking, "Calendar booking updated successfully");
      
    } catch (error) {
      logger.error('Failed to update calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route PUT /api/v1/calendar/bookings/:id
 * @desc Update a calendar booking
 * @access Private (Owner/Manager)
 */
router.put("/bookings/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarWriteLimiter,
  validate(updateCalendarBookingSchema),
  asyncHandler(async (req: any, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      logger.info('Updating calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId,
        updateFields: Object.keys(req.body)
      });
      
      // First check if booking exists and user has access
      const existingBooking = await calendarService.getCalendarBookingById(bookingId);
      if (!existingBooking) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      if (req.user.role !== 'owner') {
        const hasAccess = await checkPropertyOwnership(req.user.userId, existingBooking.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to modify this calendar booking");
        }
      }
      
      const updatedBooking = await calendarService.updateCalendarBooking(bookingId, req.body);
      
      if (!updatedBooking) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      logger.info('Calendar booking updated successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId
      });
      
      return sendSuccess(res, updatedBooking, "Calendar booking updated successfully");
      
    } catch (error) {
      logger.error('Failed to update calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route DELETE /api/v1/calendar/bookings/:id
 * @desc Delete a calendar booking
 * @access Private (Owner/Manager)
 */
router.delete("/bookings/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarWriteLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      logger.info('Deleting calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId
      });
      
      // First check if booking exists and user has access
      const existingBooking = await calendarService.getCalendarBookingById(bookingId);
      if (!existingBooking) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      if (req.user.role !== 'owner') {
        const hasAccess = await checkPropertyOwnership(req.user.userId, existingBooking.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to delete this calendar booking");
        }
      }
      
      const deleted = await calendarService.deleteCalendarBooking(bookingId);
      
      if (!deleted) {
        throw new NotFoundError("Calendar booking not found");
      }
      
      logger.info('Calendar booking deleted successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId
      });
      
      return sendSuccess(res, { deleted: true }, "Calendar booking deleted successfully");
      
    } catch (error) {
      logger.error('Failed to delete calendar booking', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        bookingId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

// =====================================================
// PROPERTY CALENDAR ENDPOINTS
// =====================================================

/**
 * @route GET /api/v1/calendar/:propertyId
 * @desc Get calendar bookings for a specific property (FullCalendar compatible)
 * @access Private (Owner/Manager) or Public (for availability)
 */
router.get("/:propertyId", 
  sanitizeIdParam,
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      
      // Optional query parameters for date filtering
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;
      
      logger.debug('Fetching property calendar', {
        component: 'CalendarAPI',
        propertyId,
        dateRange: startDate && endDate ? `${startDate} to ${endDate}` : 'all'
      });
      
      const bookings = await calendarService.getPropertyCalendar(
        propertyId,
        startDate,
        endDate
      );
      
      // Format response for FullCalendar compatibility
      const calendarResponse = {
        bookings: bookings.map(booking => ({
          id: booking.id,
          property_id: booking.propertyId,
          start_date: booking.startDate,
          end_date: booking.endDate,
          guest_name: booking.guestName,
          guest_phone: booking.guestPhone,
          guest_count: booking.guestCount,
          status: booking.status,
          booking_type: booking.bookingType,
          notes: booking.notes,
          source: booking.source,
          created_at: booking.createdAt,
          updated_at: booking.updatedAt
        }))
      };
      
      return sendSuccess(res, calendarResponse);
      
    } catch (error) {
      logger.error('Failed to fetch property calendar', {
        component: 'CalendarAPI',
        propertyId: req.params.propertyId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route GET /api/v1/calendar/properties/:id
 * @desc Get calendar bookings for a specific property
 * @access Private (Owner/Manager) or Public (for availability)
 */
router.get("/properties/:id", 
  sanitizeIdParam,
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const propertyId = parseInt(req.params.id);
      const queryParams = propertyCalendarSchema.parse(req.query);
      
      logger.debug('Fetching property calendar', {
        component: 'CalendarAPI',
        propertyId,
        dateRange: `${queryParams.startDate} to ${queryParams.endDate}`
      });
      
      const calendar = await calendarService.getPropertyCalendar(
        propertyId,
        queryParams.startDate,
        queryParams.endDate
      );
      
      // If no authentication, return only basic availability info
      if (!req.user) {
        const publicCalendar = calendar.map(booking => ({
          id: booking.id,
          startDate: booking.startDate,
          endDate: booking.endDate,
          status: booking.status,
          bookingType: booking.bookingType
        }));
        return sendSuccess(res, publicCalendar);
      }
      
      // For authenticated users, check if they have access to detailed info
      if (req.user.role !== 'owner') {
        const hasAccess = await checkPropertyOwnership(req.user.userId, propertyId);
        if (!hasAccess) {
          // Return limited info for non-owners
          const limitedCalendar = calendar.map(booking => ({
            id: booking.id,
            startDate: booking.startDate,
            endDate: booking.endDate,
            status: booking.status,
            bookingType: booking.bookingType
          }));
          return sendSuccess(res, limitedCalendar);
        }
      }
      
      // Return full calendar for property owners
      return sendSuccess(res, calendar);
      
    } catch (error) {
      logger.error('Failed to fetch property calendar', {
        component: 'CalendarAPI',
        propertyId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route POST /api/v1/calendar/availability
 * @desc Check availability for specific dates
 * @access Public
 */
router.post("/availability", 
  availabilityLimiter,
  validate(availabilityCheckSchema),
  asyncHandler(async (req: any, res) => {
    try {
      const { propertyId, startDate, endDate } = req.body;
      
      logger.debug('Checking calendar availability', {
        component: 'CalendarAPI',
        propertyId,
        dateRange: `${startDate} to ${endDate}`
      });
      
      const availability = await calendarService.checkAvailability(
        propertyId,
        startDate,
        endDate
      );
      
      return sendSuccess(res, availability);
      
    } catch (error) {
      logger.error('Failed to check calendar availability', {
        component: 'CalendarAPI',
        propertyId: req.body.propertyId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route GET /api/v1/calendar/stats/:propertyId
 * @desc Get calendar statistics for a property
 * @access Private (Owner/Manager)
 */
router.get("/stats/:propertyId", 
  authenticate,
  sanitizeIdParam,
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      const year = req.query.year ? parseInt(req.query.year as string) : undefined;
      
      logger.debug('Fetching calendar statistics', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId,
        year
      });
      
      // Check if user has access to this property
      if (req.user.role !== 'owner') {
        const hasAccess = await checkPropertyOwnership(req.user.userId, propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to this property's statistics");
        }
      }
      
      const stats = await calendarService.getCalendarStats(propertyId, year);
      
      return sendSuccess(res, stats);
      
    } catch (error) {
      logger.error('Failed to fetch calendar statistics', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId: req.params.propertyId
      }, error as Error);
      throw error;
    }
  })
);

// =====================================================
// CALENDAR SYNC STATUS ENDPOINTS
// =====================================================

/**
 * @route GET /api/v1/calendar/sync
 * @desc Get calendar sync status configurations
 * @access Private (Owner/Manager)
 */
router.get("/sync", 
  authenticate, 
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const filters: any = {};
      
      // Parse query parameters
      if (req.query.propertyId) {
        filters.propertyId = parseInt(req.query.propertyId as string);
      }
      if (req.query.calendarType) {
        filters.calendarType = req.query.calendarType as string;
      }
      if (req.query.isActive !== undefined) {
        filters.isActive = req.query.isActive === 'true';
      }
      
      logger.debug('Fetching calendar sync status', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        filters
      });
      
      const syncStatuses = await calendarService.getSyncStatus(filters);
      
      // Filter results based on user access
      let filteredSyncStatuses = syncStatuses;
      if (req.user.role !== 'owner') {
        // For non-owners, only show sync statuses for properties they own
        filteredSyncStatuses = [];
        for (const syncStatus of syncStatuses) {
          if (syncStatus.propertyId) {
            const hasAccess = await checkPropertyOwnership(req.user.userId, syncStatus.propertyId);
            if (hasAccess) {
              filteredSyncStatuses.push(syncStatus);
            }
          }
        }
      }
      
      return sendSuccess(res, filteredSyncStatuses);
      
    } catch (error) {
      logger.error('Failed to fetch calendar sync status', {
        component: 'CalendarAPI',
        userId: req.user.userId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route POST /api/v1/calendar/sync
 * @desc Create a new calendar sync configuration
 * @access Private (Owner/Manager)
 */
router.post("/sync", 
  authenticate, 
  calendarWriteLimiter,
  validate(insertCalendarSyncStatusSchema),
  validateCalendarAccess,
  asyncHandler(async (req: any, res) => {
    try {
      logger.info('Creating calendar sync configuration', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId: req.body.propertyId,
        calendarType: req.body.calendarType
      });
      
      const syncStatus = await calendarService.createSyncStatus(req.body);
      
      logger.info('Calendar sync configuration created successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncStatusId: syncStatus.id
      });
      
      return sendSuccess(res, syncStatus, "Calendar sync configuration created successfully", 201);
      
    } catch (error) {
      logger.error('Failed to create calendar sync configuration', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        propertyId: req.body.propertyId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route GET /api/v1/calendar/sync/:id
 * @desc Get a single calendar sync configuration by ID
 * @access Private (Owner/Manager)
 */
router.get("/sync/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarReadLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const syncId = parseInt(req.params.id);
      
      logger.debug('Fetching calendar sync status by ID', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId
      });
      
      const syncStatuses = await calendarService.getSyncStatus();
      const syncStatus = syncStatuses.find(s => s.id === syncId);
      
      if (!syncStatus) {
        throw new NotFoundError("Calendar sync configuration not found");
      }
      
      // Check if user has access to this sync configuration
      if (req.user.role !== 'owner' && syncStatus.propertyId) {
        const hasAccess = await checkPropertyOwnership(req.user.userId, syncStatus.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to this calendar sync configuration");
        }
      }
      
      return sendSuccess(res, syncStatus);
      
    } catch (error) {
      logger.error('Failed to fetch calendar sync status by ID', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route PUT /api/v1/calendar/sync/:id
 * @desc Update a calendar sync configuration
 * @access Private (Owner/Manager)
 */
router.put("/sync/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarWriteLimiter,
  validate(updateCalendarSyncStatusSchema),
  asyncHandler(async (req: any, res) => {
    try {
      const syncId = parseInt(req.params.id);
      
      logger.info('Updating calendar sync configuration', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId,
        updateFields: Object.keys(req.body)
      });
      
      // First check if sync configuration exists and user has access
      const syncStatuses = await calendarService.getSyncStatus();
      const existingSyncStatus = syncStatuses.find(s => s.id === syncId);
      
      if (!existingSyncStatus) {
        throw new NotFoundError("Calendar sync configuration not found");
      }
      
      if (req.user.role !== 'owner' && existingSyncStatus.propertyId) {
        const hasAccess = await checkPropertyOwnership(req.user.userId, existingSyncStatus.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to modify this calendar sync configuration");
        }
      }
      
      const updatedSyncStatus = await calendarService.updateSyncStatus(syncId, req.body);
      
      if (!updatedSyncStatus) {
        throw new NotFoundError("Calendar sync configuration not found");
      }
      
      logger.info('Calendar sync configuration updated successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId
      });
      
      return sendSuccess(res, updatedSyncStatus, "Calendar sync configuration updated successfully");
      
    } catch (error) {
      logger.error('Failed to update calendar sync configuration', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route DELETE /api/v1/calendar/sync/:id
 * @desc Disable a calendar sync configuration
 * @access Private (Owner/Manager)
 */
router.delete("/sync/:id", 
  authenticate, 
  sanitizeIdParam,
  calendarWriteLimiter,
  asyncHandler(async (req: any, res) => {
    try {
      const syncId = parseInt(req.params.id);
      
      logger.info('Disabling calendar sync configuration', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId
      });
      
      // First check if sync configuration exists and user has access
      const syncStatuses = await calendarService.getSyncStatus();
      const existingSyncStatus = syncStatuses.find(s => s.id === syncId);
      
      if (!existingSyncStatus) {
        throw new NotFoundError("Calendar sync configuration not found");
      }
      
      if (req.user.role !== 'owner' && existingSyncStatus.propertyId) {
        const hasAccess = await checkPropertyOwnership(req.user.userId, existingSyncStatus.propertyId);
        if (!hasAccess) {
          throw new AuthorizationError("You don't have access to disable this calendar sync configuration");
        }
      }
      
      const disabled = await calendarService.disableSync(syncId);
      
      if (!disabled) {
        throw new NotFoundError("Calendar sync configuration not found");
      }
      
      logger.info('Calendar sync configuration disabled successfully', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId
      });
      
      return sendSuccess(res, { disabled: true }, "Calendar sync configuration disabled successfully");
      
    } catch (error) {
      logger.error('Failed to disable calendar sync configuration', {
        component: 'CalendarAPI',
        userId: req.user.userId,
        syncId: req.params.id
      }, error as Error);
      throw error;
    }
  })
);

export default router;