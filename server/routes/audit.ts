import { Router } from "express";
import rateLimit from "express-rate-limit";
import { z } from "zod";
import { authenticate } from "./auth";
import { auditLogger } from "../services/AuditLogger";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError 
} from "../middlewares/errorHandler";

const router = Router();

// Rate limiting for audit endpoints
const auditLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // Max 60 audit events per minute per IP
  message: {
    error: "Too many audit events from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schema for audit events from frontend
const auditEventSchema = z.object({
  id: z.string().min(1),
  type: z.string().min(1),
  timestamp: z.string().datetime(),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
  details: z.record(z.any()),
  metadata: z.object({
    ipAddress: z.string().optional(),
    userAgent: z.string().optional(),
    url: z.string().url(),
    referrer: z.string().optional()
  }),
  riskScore: z.number().min(0).max(100).optional()
});

const auditEventsSchema = z.object({
  events: z.array(auditEventSchema).min(1).max(50), // Max 50 events per batch
  clientTimestamp: z.string().datetime()
});

// Input validation middleware
function validate(schema: z.ZodSchema) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      const errorMessage = error.errors?.map((e: any) => e.message).join(", ") || error.message;
      return sendError(res, new ValidationError(errorMessage), req);
    }
  };
}

// Sanitize audit data to prevent XSS and injection attacks
function sanitizeAuditData(data: any): any {
  if (typeof data === 'string') {
    return data.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '[SCRIPT_REMOVED]')
               .replace(/javascript:/gi, 'javascript_removed:')
               .slice(0, 10000); // Limit string length
  }
  
  if (Array.isArray(data)) {
    return data.slice(0, 100).map(sanitizeAuditData); // Limit array size
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {};
    const allowedKeys = 50; // Limit object keys
    let keyCount = 0;
    
    for (const key in data) {
      if (keyCount >= allowedKeys) break;
      
      // Sanitize key names
      const sanitizedKey = key.replace(/[^a-zA-Z0-9_-]/g, '').slice(0, 100);
      if (sanitizedKey) {
        sanitized[sanitizedKey] = sanitizeAuditData(data[key]);
        keyCount++;
      }
    }
    return sanitized;
  }
  
  return data;
}

// Receive audit events from frontend
router.post("/events",
  auditLimiter,
  validate(auditEventsSchema),
  asyncHandler(async (req: any, res) => {
    const { events, clientTimestamp } = req.body;
    const serverTimestamp = new Date().toISOString();
    const processingResults: any[] = [];
    
    // Process each event
    for (const event of events) {
      try {
        // Sanitize event data
        const sanitizedEvent = {
          ...event,
          details: sanitizeAuditData(event.details),
          metadata: {
            ...event.metadata,
            serverProcessedAt: serverTimestamp,
            clientTimestamp,
            processingDelay: Date.now() - new Date(event.timestamp).getTime()
          }
        };
        
        // Extract user ID from authenticated request if available
        let userId = req.user?.userId;
        if (!userId && event.userId) {
          userId = parseInt(event.userId);
        }
        
        // Create audit context
        const auditContext = {
          userId,
          actorType: 'user' as const,
          actorId: userId,
          actorIp: req.ip,
          actorUserAgent: req.headers['user-agent'],
          metadata: sanitizedEvent.metadata
        };
        
        // Determine if this is a payment-related event
        if (event.type.includes('PAYMENT') || event.type.includes('payment')) {
          await auditLogger.logPaymentAction(
            event.type,
            auditContext,
            event.details.paymentOrderId,
            event.details.paymentTransactionId
          );
        } else {
          // Log as security incident for non-payment events
          if (shouldLogAsSecurityIncident(event.type)) {
            await auditLogger.logSecurityIncident({
              incidentType: event.type,
              severity: getSeverityFromEvent(event),
              description: getDescriptionFromEvent(event),
              sourceIp: req.ip,
              userId,
              incidentData: sanitizedEvent.details
            });
          } else {
            // Log as general audit event
            await auditLogger.logPaymentAction(event.type, auditContext);
          }
        }
        
        processingResults.push({
          eventId: event.id,
          processed: true,
          timestamp: serverTimestamp
        });
        
      } catch (error: any) {
        console.error(`Failed to process audit event ${event.id}:`, error);
        processingResults.push({
          eventId: event.id,
          processed: false,
          error: 'Processing failed',
          timestamp: serverTimestamp
        });
      }
    }
    
    // Log the batch processing itself
    if (req.user) {
      await auditLogger.logPaymentAction('AUDIT_EVENTS_RECEIVED', {
        userId: req.user.userId,
        actorType: 'user',
        actorId: req.user.userId,
        actorIp: req.ip,
        metadata: {
          eventCount: events.length,
          successCount: processingResults.filter(r => r.processed).length,
          clientTimestamp,
          serverTimestamp
        }
      });
    }
    
    return sendSuccess(res, {
      processed: processingResults.filter(r => r.processed).length,
      failed: processingResults.filter(r => !r.processed).length,
      results: processingResults,
      serverTimestamp
    }, "Audit events processed successfully");
  })
);

// Helper functions
function shouldLogAsSecurityIncident(eventType: string): boolean {
  const securityEventTypes = [
    'SUSPICIOUS_ACTIVITY',
    'RATE_LIMIT_EXCEEDED',
    'FRAUD_DETECTION',
    'TWO_FA_FAILURE',
    'SESSION_TIMEOUT',
    'AUTHENTICATION_FAILED',
    'AUTHORIZATION_FAILED'
  ];
  return securityEventTypes.some(type => eventType.includes(type));
}

function getSeverityFromEvent(event: any): 'low' | 'medium' | 'high' | 'critical' {
  const riskScore = event.riskScore || 0;
  
  if (riskScore >= 80 || event.type.includes('FRAUD')) return 'critical';
  if (riskScore >= 60 || event.type.includes('SUSPICIOUS')) return 'high';
  if (riskScore >= 40 || event.type.includes('RATE_LIMIT')) return 'medium';
  return 'low';
}

function getDescriptionFromEvent(event: any): string {
  return `Frontend security event: ${event.type} - ${JSON.stringify(event.details).slice(0, 500)}`;
}

// Get audit statistics (for monitoring dashboard)
router.get("/stats",
  authenticate,
  asyncHandler(async (req: any, res) => {
    // Only allow admin users to view audit stats
    if (!req.user.isAdmin) {
      return sendError(res, new Error("Unauthorized"), req);
    }
    
    const stats = {
      message: "Audit statistics endpoint - implementation depends on your specific audit table structure",
      timestamp: new Date().toISOString()
    };
    
    return sendSuccess(res, stats);
  })
);

export default router;