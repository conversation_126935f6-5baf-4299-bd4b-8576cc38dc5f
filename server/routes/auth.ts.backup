import { Router } from "express";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import rateLimit from "express-rate-limit";
import { storage } from "../storage";
import { config } from "../config";
import { cacheService } from "../services/CacheService";
import { TokenBlacklistService } from "../services/TokenBlacklistService";
import { 
  userRegisterSchema,
  userLoginSchema,
} from "../../shared/schema";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError,
  AuthenticationError,
  ConflictError 
} from "../middlewares/errorHandler";

const router = Router();

// Rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit auth requests to 20 per windowMs
  message: {
    error: "Too many authentication attempts from this IP, please try again later.",
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const MAX_LOGIN_ATTEMPTS = 5;
const loginAttemptsMap = new Map<string, number>();

// Token blacklisting service using cache
class TokenBlacklistService {
  private static readonly BLACKLIST_PREFIX = 'blacklisted_token';
  private static readonly TOKEN_EXPIRY = 7 * 24 * 60 * 60; // 7 days in seconds

  static async blacklistToken(token: string): Promise<void> {
    const key = `${this.BLACKLIST_PREFIX}:${token}`;
    await cacheService.set(key, true, this.TOKEN_EXPIRY);
  }

  static async isTokenBlacklisted(token: string): Promise<boolean> {
    const key = `${this.BLACKLIST_PREFIX}:${token}`;
    return await cacheService.exists(key);
  }

  static async blacklistUserTokens(userId: string): Promise<void> {
    // Pattern to match all tokens for a user (would need user-specific token tracking)
    // For now, we'll implement single token blacklisting
    console.log(`User ${userId} tokens should be blacklisted`);
  }

  static async clearExpiredTokens(): Promise<void> {
    // The cache service automatically handles cleanup
    const keys = await cacheService.keys(`${this.BLACKLIST_PREFIX}:*`);
    console.log(`Token blacklist contains ${keys.length} entries`);
  }
}

// Enhanced token verification with secret rotation support
class TokenVerificationService {
  static async verifyWithRotation(token: string): Promise<any> {
    const secrets = await SecretRotationService.getVerificationSecrets();
    
    let lastError: any;
    
    // Try each valid secret
    for (const secret of secrets) {
      try {
        return jwt.verify(token, secret);
      } catch (error) {
        lastError = error;
        continue;
      }
    }
    
    // If all secrets failed, throw the last error
    throw lastError;
  }
  
  static async signToken(payload: any, options?: jwt.SignOptions): Promise<string> {
    const signingSecret = await SecretRotationService.getSigningSecret();
    return jwt.sign(payload, signingSecret, options);
  }
}

// Input validation middleware
function validate(schema: any) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      return sendError(res, new ValidationError(error.message), req);
    }
  };
}

// Authentication middleware
export async function authenticate(req: any, res: any, next: any) {
  const token = req.cookies.auth_token || req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return sendError(res, new AuthenticationError("Authentication required"), req);
  }

  // Check if token is blacklisted using cache service
  if (await TokenBlacklistService.isTokenBlacklisted(token)) {
    return sendError(res, new AuthenticationError("Token has been revoked"), req);
  }

  try {
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    req.user = { userId: decoded.userId, role: decoded.role };
    return next();
  } catch (error) {
    return sendError(res, new AuthenticationError("Invalid or expired token"), req);
  }
}

// Authorization middleware
export function authorize(roles: string[]) {
  return (req: any, res: any, next: any) => {
    if (!req.user) {
      return sendError(res, new AuthenticationError("Authentication required"), req);
    }

    if (!roles.includes(req.user.role)) {
      return sendError(res, new AuthenticationError("Insufficient permissions"), req);
    }

    return next();
  };
}

// Register endpoint
router.post("/register", authLimiter, validate(userRegisterSchema), asyncHandler(async (req, res) => {
  const { username, email, password, fullName, role, consentData } = req.body;

  // Check if user already exists
  const existingUser = await storage.getUserByEmail(email);
  if (existingUser) {
    throw new ConflictError("User with this email already exists");
  }

  // Create new user
  const newUser = await storage.createUser({
    username,
    email,
    password,
    fullName,
    role: role || 'user',
    termsAccepted: consentData?.termsAccepted || true,
    privacyPolicyAccepted: consentData?.dataProcessingConsent || true,
    cookiePolicyAccepted: true,
    dataProcessingConsent: consentData?.dataProcessingConsent || true,
    marketingConsent: consentData?.marketingConsent || false,
    consentTimestamp: consentData?.consentTimestamp 
      ? new Date(consentData.consentTimestamp) 
      : new Date()
  });

  // Generate JWT token
  const token = jwt.sign(
    { userId: newUser.id, role: newUser.role },
    config.jwt.secret,
    { 
      expiresIn: '7d',
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithm: 'HS256'
    }
  );

  // Set secure cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });

  return sendSuccess(res, {
    user: {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      fullName: newUser.fullName,
      role: newUser.role
    },
    token
  }, "Registration successful", 201);
}));

// Login endpoint
router.post("/login", authLimiter, validate(userLoginSchema), asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Check login attempts
  const attempts = loginAttemptsMap.get(email) || 0;
  if (attempts >= MAX_LOGIN_ATTEMPTS) {
    throw new AuthenticationError("Too many failed login attempts. Please try again later.");
  }

  // Find user
  const user = await storage.getUserByEmail(email);
  if (!user) {
    loginAttemptsMap.set(email, attempts + 1);
    throw new AuthenticationError("Invalid email or password");
  }

  // Verify password
  const validPassword = await bcrypt.compare(password, user.password);
  if (!validPassword) {
    loginAttemptsMap.set(email, attempts + 1);
    throw new AuthenticationError("Invalid email or password");
  }

  // Reset login attempts on successful login
  loginAttemptsMap.delete(email);

  // Generate JWT token
  const token = jwt.sign(
    { userId: user.id, role: user.role },
    config.jwt.secret,
    { 
      expiresIn: '7d',
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithm: 'HS256'
    }
  );

  // Set secure cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });

  return sendSuccess(res, {
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    },
    token
  }, "Login successful");
}));

// Get current user
router.get("/me", authenticate, asyncHandler(async (req: any, res) => {
  const user = await storage.getUser(req.user.userId);
  if (!user) {
    throw new AuthenticationError("User not found");
  }

  return sendSuccess(res, {
    id: user.id,
    username: user.username,
    email: user.email,
    fullName: user.fullName,
    role: user.role
  });
}));

// Logout endpoint
router.post("/logout", authenticate, asyncHandler(async (req: any, res) => {
  const token = req.cookies.auth_token || req.headers.authorization?.replace('Bearer ', '');
  
  if (token) {
    await TokenBlacklistService.blacklistToken(token);
  }

  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict'
  });

  return sendSuccess(res, null, "Logout successful");
}));

// Logout from all devices endpoint
router.post("/logout-all", authenticate, asyncHandler(async (req: any, res) => {
  const token = req.cookies.auth_token || req.headers.authorization?.replace('Bearer ', '');
  
  if (token) {
    await TokenBlacklistService.blacklistToken(token);
  }

  // In a production system, you'd want to blacklist all tokens for this user
  // This would require tracking tokens per user in the database
  await TokenBlacklistService.blacklistUserTokens(req.user.userId);

  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: config.app.useSecureCookies,
    sameSite: 'strict'
  });

  return sendSuccess(res, null, "Logged out from all devices");
}));

// Get user profile
router.get("/profile", authenticate, asyncHandler(async (req: any, res) => {
  const user = await storage.getUser(req.user.userId);
  if (!user) {
    throw new AuthenticationError("User not found");
  }

  return sendSuccess(res, {
    id: user.id,
    username: user.username,
    email: user.email,
    fullName: user.fullName,
    phone: user.phone,
    address: user.address,
    bio: user.bio,
    role: user.role
  });
}));

// Update user profile
router.patch("/profile", authenticate, asyncHandler(async (req: any, res) => {
  const { fullName, phone, address, bio } = req.body;

  const updatedUser = await storage.updateUser(req.user.userId, {
    fullName,
    phone,
    address,
    bio
  });

  if (!updatedUser) {
    throw new AuthenticationError("User not found");
  }

  return sendSuccess(res, {
    id: updatedUser.id,
    username: updatedUser.username,
    email: updatedUser.email,
    fullName: updatedUser.fullName,
    phone: updatedUser.phone,
    address: updatedUser.address,
    bio: updatedUser.bio,
    role: updatedUser.role
  }, "Profile updated successfully");
}));

// Admin endpoints for security management
router.get("/admin/security/blacklisted-tokens", authenticate, authorize(['admin']), asyncHandler(async (req: any, res) => {
  const stats = await TokenBlacklistService.getBlacklistStats();
  return sendSuccess(res, stats);
}));

// Force cleanup of expired tokens
router.post("/admin/security/cleanup-tokens", authenticate, authorize(['admin']), asyncHandler(async (req: any, res) => {
  await TokenBlacklistService.clearExpiredTokens();
  return sendSuccess(res, null, "Token cleanup completed");
}));

export default router;