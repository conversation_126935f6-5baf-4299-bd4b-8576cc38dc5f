import { Router } from "express";
import multer from "multer";
import { join, extname } from "path";
import fs from "fs";
import crypto from "crypto";
import { cloudinaryService } from "../cloudinaryService";
import { config } from "../config";
import { authenticate, authorize } from "./auth";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError 
} from "../middlewares/errorHandler";

const router = Router();

const UPLOADS_DIR = join(process.cwd(), "uploads");

// Ensure uploads directory exists
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOADS_DIR);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const hash = crypto.createHash('md5').update(file.originalname + uniqueSuffix).digest('hex');
    cb(null, file.fieldname + '-' + hash + extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Maximum 10 files
  },
  fileFilter: function (req, file, cb) {
    // Check file type
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(file.originalname.toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed (jpeg, jpg, png, gif, webp)'));
    }
  }
});

// Upload single image
router.post("/image", 
  authenticate, 
  authorize(["owner"]), 
  upload.single('image'), 
  asyncHandler(async (req: any, res) => {
    if (!req.file) {
      throw new ValidationError("No image file provided");
    }

    try {
      // Upload to Cloudinary if available
      if (cloudinaryService.isInitialized() || config.cloudinary.available) {
        const fileBuffer = fs.readFileSync(req.file.path);
        const result = await cloudinaryService.uploadImage(fileBuffer, req.file.filename);
        
        // Clean up local file
        fs.unlinkSync(req.file.path);
        
        return sendSuccess(res, {
          url: result.secure_url,
          publicId: result.public_id,
          provider: 'cloudinary'
        }, "Image uploaded successfully");
      } else {
        // Fallback to local storage
        const imageUrl = `/uploads/${req.file.filename}`;
        
        return sendSuccess(res, {
          url: imageUrl,
          filename: req.file.filename,
          provider: 'local'
        }, "Image uploaded successfully");
      }
    } catch (error) {
      // Clean up file on error
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      throw error;
    }
  })
);

// Upload multiple images
router.post("/images", 
  authenticate, 
  authorize(["owner"]), 
  upload.array('images', 10), 
  asyncHandler(async (req: any, res) => {
    if (!req.files || req.files.length === 0) {
      throw new ValidationError("No image files provided");
    }

    const uploadResults = [];

    try {
      for (const file of req.files) {
        if (cloudinaryService.isInitialized() || config.cloudinary.available) {
          const fileBuffer = fs.readFileSync(file.path);
          const result = await cloudinaryService.uploadImage(fileBuffer, file.filename);
          
          // Clean up local file
          fs.unlinkSync(file.path);
          
          uploadResults.push({
            url: result.secure_url,
            publicId: result.public_id,
            provider: 'cloudinary'
          });
        } else {
          // Fallback to local storage
          const imageUrl = `/uploads/${file.filename}`;
          
          uploadResults.push({
            url: imageUrl,
            filename: file.filename,
            provider: 'local'
          });
        }
      }

      return sendSuccess(res, uploadResults, "Images uploaded successfully");
    } catch (error) {
      // Clean up files on error
      req.files.forEach((file: any) => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
      throw error;
    }
  })
);

// Delete image (Cloudinary only)
router.delete("/image/:publicId", 
  authenticate, 
  authorize(["owner"]), 
  asyncHandler(async (req, res) => {
    const { publicId } = req.params;

    if (!cloudinaryService.isInitialized() && !config.cloudinary.available) {
      throw new ValidationError("Image deletion is only available with Cloudinary configuration");
    }

    try {
      await cloudinaryService.deleteImage(publicId);
      return sendSuccess(res, null, "Image deleted successfully");
    } catch (error) {
      throw new Error("Failed to delete image");
    }
  })
);

export default router;
      format: result.format,
      resource_type: result.resource_type,
      bytes: result.bytes
    }));

    res.json({
      success: true,
      data: processedResults
    });
  } catch (error) {
    log(`Multiple upload failed: ${error}`, 'upload');
    res.status(500).json({ 
      success: false, 
      message: 'Upload failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete file
router.delete('/:publicId', authenticateToken, async (req, res) => {
  try {
    const { publicId } = req.params;
    
    // Decode the public ID (it may be URL encoded)
    const decodedPublicId = decodeURIComponent(publicId);
    
    log(`Deleting file with public ID: ${decodedPublicId}`, 'upload');
    
    const success = await cloudinaryService.deleteImage(decodedPublicId);
    
    if (success) {
      res.json({
        success: true,
        message: 'File deleted successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'File not found or already deleted'
      });
    }
  } catch (error) {
    log(`Delete failed: ${error}`, 'upload');
    res.status(500).json({ 
      success: false, 
      message: 'Delete failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get upload status/health
router.get('/status', (req, res) => {
  res.json({
    success: true,
    data: {
      cloudinary_initialized: cloudinaryService.isInitialized(),
      max_file_size: '10MB',
      allowed_types: ['jpeg', 'jpg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'wmv']
    }
  });
});

export default router;
