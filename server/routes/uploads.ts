import { Router } from "express";
import multer from "multer";
import { join, extname } from "path";
import fs from "fs";
import crypto from "crypto";
import { cloudinaryService } from "../cloudinaryService";
import { config } from "../config";
import { authenticate, authorize } from "./auth";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError 
} from "../middlewares/errorHandler";

const router = Router();

const UPLOADS_DIR = join(process.cwd(), "uploads");

// Ensure uploads directory exists
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOADS_DIR);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const hash = crypto.createHash('md5').update(file.originalname + uniqueSuffix).digest('hex');
    cb(null, file.fieldname + '-' + hash + extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Maximum 10 files
  },
  fileFilter: function (req, file, cb) {
    // Check file type for both images and videos
    const allowedImageTypes = /jpeg|jpg|png|gif|webp/;
    const allowedVideoTypes = /mp4|webm|ogg|mov|avi/;
    
    const isImageExtension = allowedImageTypes.test(file.originalname.toLowerCase());
    const isVideoExtension = allowedVideoTypes.test(file.originalname.toLowerCase());
    const isImageMimetype = file.mimetype.startsWith('image/');
    const isVideoMimetype = file.mimetype.startsWith('video/');

    if ((isImageMimetype && isImageExtension) || (isVideoMimetype && isVideoExtension)) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (jpeg, jpg, png, gif, webp) and video files (mp4, webm, ogg, mov, avi) are allowed'));
    }
  }
});

// Upload multiple images (backward compatibility)
router.post("/images", 
  authenticate, 
  authorize(["owner"]), 
  upload.array("images", 10),
  asyncHandler(async (req, res) => {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      throw new ValidationError("No images provided");
    }

    try {
      if (cloudinaryService.isInitialized()) {
        // Use Cloudinary if available
        const uploadPromises = files.map(file => ({
          buffer: fs.readFileSync(file.path),
          filename: file.originalname
        }));
        
        const results = await cloudinaryService.uploadMultipleImages(uploadPromises);
        
        // Clean up local files
        files.forEach(file => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });

        const imageUrls = results.map(result => result.secure_url);
        return sendSuccess(res, { images: imageUrls }, "Images uploaded successfully");
      } else {
        // Use local storage
        const imageUrls = files.map(file => `/uploads/${file.filename}`);
        return sendSuccess(res, { images: imageUrls }, "Images uploaded successfully");
      }
    } catch (error) {
      // Clean up files on error
      files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
      throw error;
    }
  })
);

// Upload multiple media files (images and videos)
router.post("/media", 
  authenticate, 
  authorize(["owner"]), 
  upload.array("media", 10),
  asyncHandler(async (req, res) => {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      throw new ValidationError("No media files provided");
    }

    try {
      if (cloudinaryService.isInitialized()) {
        // Use Cloudinary if available
        const uploadPromises = files.map(file => ({
          buffer: fs.readFileSync(file.path),
          filename: file.originalname
        }));
        
        const results = await cloudinaryService.uploadMultipleImages(uploadPromises);
        
        // Clean up local files
        files.forEach(file => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });

        const mediaUrls = results.map(result => result.secure_url);
        return sendSuccess(res, { images: mediaUrls }, "Media uploaded successfully");
      } else {
        // Use local storage
        const mediaUrls = files.map(file => `/uploads/${file.filename}`);
        return sendSuccess(res, { images: mediaUrls }, "Media uploaded successfully");
      }
    } catch (error) {
      // Clean up files on error
      files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
      throw error;
    }
  })
);

// Upload single image
router.post("/image", 
  authenticate, 
  authorize(["owner"]), 
  upload.single("image"),
  asyncHandler(async (req, res) => {
    const file = req.file;
    
    if (!file) {
      throw new ValidationError("No image provided");
    }

    try {
      if (cloudinaryService.isInitialized()) {
        // Use Cloudinary if available
        const result = await cloudinaryService.uploadImage(
          fs.readFileSync(file.path),
          file.originalname
        );
        
        // Clean up local file
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }

        return sendSuccess(res, { 
          url: result.secure_url,
          publicId: result.public_id 
        }, "Image uploaded successfully");
      } else {
        // Use local storage
        const imageUrl = `/uploads/${file.filename}`;
        return sendSuccess(res, { url: imageUrl }, "Image uploaded successfully");
      }
    } catch (error) {
      // Clean up file on error
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw error;
    }
  })
);

// Delete image (Cloudinary only)
router.delete("/image/:publicId", 
  authenticate, 
  authorize(["owner"]), 
  asyncHandler(async (req, res) => {
    const { publicId } = req.params;

    if (!cloudinaryService.isInitialized() && !config.cloudinary.available) {
      throw new ValidationError("Image deletion is only available with Cloudinary configuration");
    }

    try {
      await cloudinaryService.deleteImage(publicId);
      return sendSuccess(res, null, "Image deleted successfully");
    } catch (error) {
      throw new Error("Failed to delete image");
    }
  })
);

export default router;