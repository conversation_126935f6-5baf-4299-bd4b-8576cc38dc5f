import { Router } from 'express';
import { webSocketService } from '../services/WebSocketService';
import { authenticate } from './auth';
import { 
  asyncHandler, 
  sendSuccess, 
  sendError 
} from '../middlewares/errorHandler';
import { logger } from '../utils/structured-logger';

const router = Router();

/**
 * @route GET /api/v1/websocket/stats
 * @desc Get WebSocket connection statistics
 * @access Private (Owner/Admin)
 */
router.get('/stats', 
  authenticate,
  asyncHandler(async (req: any, res) => {
    try {
      const stats = webSocketService.getConnectionStats();
      
      logger.debug('WebSocket stats requested', {
        component: 'WebSocketAPI',
        userId: req.user.userId,
        stats
      });
      
      return sendSuccess(res, stats);
      
    } catch (error) {
      logger.error('Failed to get WebSocket stats', {
        component: 'WebSocketAPI',
        userId: req.user.userId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route POST /api/v1/websocket/broadcast/calendar
 * @desc Manually trigger calendar update broadcast (for testing)
 * @access Private (Owner/Admin)
 */
router.post('/broadcast/calendar',
  authenticate,
  asyncHandler(async (req: any, res) => {
    try {
      const { propertyId, bookingData, action } = req.body;
      
      if (!propertyId || !bookingData) {
        return sendError(res, new Error('PropertyId and bookingData are required'), req);
      }
      
      // Broadcast calendar update
      if (action === 'booking_update') {
        webSocketService.broadcastBookingUpdate(
          bookingData, 
          action, 
          req.user.userId
        );
      } else {
        webSocketService.broadcastCalendarUpdate(
          propertyId, 
          bookingData, 
          req.user.userId
        );
      }
      
      logger.info('Manual WebSocket broadcast triggered', {
        component: 'WebSocketAPI',
        userId: req.user.userId,
        propertyId,
        action
      });
      
      return sendSuccess(res, { 
        message: 'Broadcast sent successfully',
        propertyId,
        action 
      });
      
    } catch (error) {
      logger.error('Failed to broadcast WebSocket message', {
        component: 'WebSocketAPI',
        userId: req.user.userId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route POST /api/v1/websocket/notify/:userId
 * @desc Send notification to specific user
 * @access Private (Owner/Admin)
 */
router.post('/notify/:userId',
  authenticate,
  asyncHandler(async (req: any, res) => {
    try {
      const { userId } = req.params;
      const { notification } = req.body;
      
      if (!notification) {
        return sendError(res, new Error('Notification data is required'), req);
      }
      
      webSocketService.sendNotificationToUser(
        parseInt(userId),
        {
          ...notification,
          sentBy: req.user.userId,
          timestamp: Date.now()
        }
      );
      
      logger.info('WebSocket notification sent', {
        component: 'WebSocketAPI',
        senderId: req.user.userId,
        targetUserId: userId
      });
      
      return sendSuccess(res, { 
        message: 'Notification sent successfully',
        targetUserId: userId
      });
      
    } catch (error) {
      logger.error('Failed to send WebSocket notification', {
        component: 'WebSocketAPI',
        userId: req.user.userId,
        targetUserId: req.params.userId
      }, error as Error);
      throw error;
    }
  })
);

/**
 * @route GET /api/v1/websocket/health
 * @desc Check WebSocket service health
 * @access Public
 */
router.get('/health',
  asyncHandler(async (req: any, res) => {
    try {
      const stats = webSocketService.getConnectionStats();
      
      const health = {
        status: 'healthy',
        timestamp: Date.now(),
        connections: stats,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      };
      
      return sendSuccess(res, health);
      
    } catch (error) {
      logger.error('WebSocket health check failed', {
        component: 'WebSocketAPI'
      }, error as Error);
      
      return sendError(res, error as Error, req);
    }
  })
);

export default router;