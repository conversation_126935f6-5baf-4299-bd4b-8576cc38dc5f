import { Router, Request, Response } from "express";
import { authenticate } from "./auth";
import { asyncHand<PERSON> } from "../middlewares/errorHandler";
import AuditService from "../services/AuditService";
import { deltaEngine, type DeltaMessage, type DeltaOperation } from "../utils/deltaUtils";
import { DELAYS } from '../../shared/constants';

/**
 * ✅ REAL-TIME OPTIMIZATION: Server-Sent Events (SSE) Endpoints
 * 
 * This module provides real-time updates for the Owner Dashboard using Server-Sent Events.
 * It's optimized for performance and scalability.
 * 
 * Benefits:
 * - 🚀 Real-time updates without polling overhead
 * - 🚀 Efficient connection management
 * - 🚀 Automatic client reconnection
 * - 🚀 Memory-efficient event streaming
 */

const router = Router();

// Store active SSE connections
const activeConnections = new Map<number, Set<Response>>();

// Store client version states for delta synchronization
interface ClientVersionState {
  userId: number;
  lastSeen: number;
  entityVersions: Map<string, number>; // entityType:entityId -> version
  connectionId: string;
}

const clientVersions = new Map<string, ClientVersionState>();

// Message compression and batching
interface QueuedMessage {
  userId: number;
  eventType: string;
  deltaMessage: DeltaMessage;
  priority: 'high' | 'medium' | 'low';
  timestamp: number;
}

const messageQueue = new Map<number, QueuedMessage[]>();
let flushTimer: NodeJS.Timeout | null = null;

// Helper function to add connection
const addConnection = (userId: number, res: Response) => {
  if (!activeConnections.has(userId)) {
    activeConnections.set(userId, new Set());
  }
  activeConnections.get(userId)!.add(res);
  
  console.log(`📡 SSE connection added for user ${userId}. Total: ${activeConnections.get(userId)!.size}`);
};

// Helper function to remove connection
const removeConnection = (userId: number, res: Response) => {
  const connections = activeConnections.get(userId);
  if (connections) {
    connections.delete(res);
    if (connections.size === 0) {
      activeConnections.delete(userId);
    }
    console.log(`📡 SSE connection removed for user ${userId}. Remaining: ${connections.size}`);
  }
};

// ✅ DELTA OPTIMIZATION: Send delta updates instead of full payloads
export const sendDeltaUpdateToUser = (
  userId: number,
  entityType: 'booking' | 'property' | 'user',
  entityId: number,
  newData: any,
  metadata: {
    source: string;
    userId: number;
    priority?: 'high' | 'medium' | 'low';
  }
) => {
  // Generate delta message
  const deltaMessage = deltaEngine.updateEntity(entityType, entityId, newData, metadata);
  
  if (!deltaMessage) {
    // No changes detected, skip sending
    return;
  }

  // Queue message for batching (unless high priority)
  if (metadata.priority === 'high') {
    sendDeltaMessageImmediately(userId, deltaMessage);
  } else {
    queueDeltaMessage(userId, deltaMessage);
  }
};

// Send delta message immediately (for high-priority updates)
const sendDeltaMessageImmediately = (userId: number, deltaMessage: DeltaMessage) => {
  const connections = activeConnections.get(userId);
  if (!connections || connections.size === 0) return;

  const eventData = JSON.stringify(deltaMessage);
  const originalSize = JSON.stringify(deltaMessage.operations).length;
  
  connections.forEach(res => {
    try {
      res.write(`event: delta-update\n`);
      res.write(`data: ${eventData}\n\n`);
    } catch (error) {
      console.error(`Failed to send delta update to user ${userId}:`, error);
      removeConnection(userId, res);
    }
  });

  console.log(`🚀 Sent delta update (${originalSize}B) to ${connections.size} connections for user ${userId}`);
};

// Queue delta message for batching
const queueDeltaMessage = (userId: number, deltaMessage: DeltaMessage) => {
  if (!messageQueue.has(userId)) {
    messageQueue.set(userId, []);
  }

  messageQueue.get(userId)!.push({
    userId,
    eventType: 'delta-update',
    deltaMessage,
    priority: deltaMessage.metadata?.priority || 'medium',
    timestamp: Date.now()
  });

  // Set up batch flush timer
  if (!flushTimer) {
    flushTimer = setTimeout(flushMessageQueue, DELAYS.SOUND_SEQUENCE_SHORT);
  }
};

// Flush queued messages in batches
const flushMessageQueue = () => {
  if (messageQueue.size === 0) {
    flushTimer = null;
    return;
  }

  const totalMessages = Array.from(messageQueue.values())
    .reduce((sum, messages) => sum + messages.length, 0);

  messageQueue.forEach((messages, userId) => {
    if (messages.length === 0) return;

    const connections = activeConnections.get(userId);
    if (!connections || connections.size === 0) {
      messageQueue.delete(userId);
      return;
    }

    // Create batched delta message
    const batchedMessage = createBatchedDeltaMessage(messages);
    const eventData = JSON.stringify(batchedMessage);

    connections.forEach(res => {
      try {
        res.write(`event: delta-batch\n`);
        res.write(`data: ${eventData}\n\n`);
      } catch (error) {
        console.error(`Failed to send batched delta to user ${userId}:`, error);
        removeConnection(userId, res);
      }
    });

    console.log(`📦 Sent batched delta (${messages.length} ops) to ${connections.size} connections for user ${userId}`);
  });

  messageQueue.clear();
  flushTimer = null;

  console.log(`🚀 Flushed ${totalMessages} queued delta messages`);
};

// Create batched delta message
const createBatchedDeltaMessage = (messages: QueuedMessage[]) => {
  const sortedMessages = messages.sort((a, b) => b.timestamp - a.timestamp);
  
  return {
    messageId: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'batch',
    timestamp: Date.now(),
    count: messages.length,
    messages: sortedMessages.map(msg => msg.deltaMessage),
    metadata: {
      batchSize: messages.length,
      oldestMessage: Math.min(...messages.map(m => m.timestamp)),
      newestMessage: Math.max(...messages.map(m => m.timestamp))
    }
  };
};

// Legacy function for backwards compatibility - now with delta optimization
const sendEventToUser = (
  userId: number, 
  eventType: string, 
  data: any
) => {
  // Try to extract entity information for delta updates
  if (data.entityType && data.entityId && data.newState) {
    sendDeltaUpdateToUser(
      userId,
      data.entityType,
      data.entityId,
      data.newState,
      {
        source: 'legacy',
        userId: data.userId || userId,
        priority: data.priority || 'medium'
      }
    );
    return;
  }

  // Fallback to full payload for non-entity updates
  const connections = activeConnections.get(userId);
  if (connections && connections.size > 0) {
    const eventData = JSON.stringify({
      ...data,
      timestamp: new Date().toISOString(),
      type: 'full-payload' // Mark as non-optimized
    });
    
    connections.forEach(res => {
      try {
        res.write(`event: ${eventType}\n`);
        res.write(`data: ${eventData}\n\n`);
      } catch (error) {
        console.error(`Failed to send SSE event to user ${userId}:`, error);
        removeConnection(userId, res);
      }
    });
    
    console.log(`📡 Sent ${eventType} event (full payload) to ${connections.size} connections for user ${userId}`);
  }
};

// Helper function to broadcast event to all users
const broadcastEvent = (eventType: string, data: any) => {
  const eventData = JSON.stringify({
    ...data,
    timestamp: new Date().toISOString()
  });
  
  let totalSent = 0;
  activeConnections.forEach((connections, userId) => {
    connections.forEach(res => {
      try {
        res.write(`event: ${eventType}\n`);
        res.write(`data: ${eventData}\n\n`);
        totalSent++;
      } catch (error) {
        console.error(`Failed to broadcast event to user ${userId}:`, error);
        removeConnection(userId, res);
      }
    });
  });
  
  console.log(`📡 Broadcasted ${eventType} event to ${totalSent} connections`);
};

/**
 * ✅ DELTA OPTIMIZED: SSE endpoint for real-time updates with delta support
 */
router.get("/stream", 
  authenticate,
  asyncHandler(async (req: any, res: Response) => {
    const userId = req.user.userId;
    const connectionId = `${userId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Extract client version info from query params
    const clientVersions = req.query.versions ? 
      JSON.parse(decodeURIComponent(req.query.versions as string)) : {};
    
    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Initialize client version state
    const clientState: ClientVersionState = {
      userId,
      lastSeen: Date.now(),
      entityVersions: new Map(Object.entries(clientVersions)),
      connectionId
    };
    
    clientVersions.set(connectionId, clientState);

    // Send initial connection confirmation with delta capabilities
    res.write(`event: connected\n`);
    res.write(`data: ${JSON.stringify({ 
      message: 'Delta-optimized SSE connection established',
      userId,
      connectionId,
      deltaSupport: true,
      supportedFormats: ['delta-update', 'delta-batch', 'full-payload'],
      timestamp: new Date().toISOString(),
      serverVersion: '2.0.0'
    })}\n\n`);

    // Add connection to active connections
    addConnection(userId, res);

    // Send any missed updates since last connection
    await sendMissedUpdates(userId, connectionId, clientState);

    // Set up enhanced heartbeat with delta stats
    const heartbeatInterval = setInterval(() => {
      try {
        const deltaStats = deltaEngine.getStats();
        res.write(`event: heartbeat\n`);
        res.write(`data: ${JSON.stringify({ 
          timestamp: new Date().toISOString(),
          connectionId,
          deltaStats: {
            trackedEntities: deltaStats.trackedEntities,
            queuedUpdates: messageQueue.get(userId)?.length || 0
          }
        })}\n\n`);
      } catch (error) {
        console.error(`Heartbeat failed for user ${userId}:`, error);
        clearInterval(heartbeatInterval);
        removeConnection(userId, res);
        clientVersions.delete(connectionId);
      }
    }, 30000); // 30 seconds

    // Handle client disconnect
    req.on('close', () => {
      console.log(`📡 Delta SSE connection closed for user ${userId}`);
      clearInterval(heartbeatInterval);
      removeConnection(userId, res);
      clientVersions.delete(connectionId);
    });

    req.on('error', (error: any) => {
      console.error(`Delta SSE connection error for user ${userId}:`, error);
      clearInterval(heartbeatInterval);
      removeConnection(userId, res);
      clientVersions.delete(connectionId);
    });

    // Log connection for audit
    AuditService.logOwnerAction({
      userId,
      userRole: 'owner',
      action: 'delta_sse_connect',
      resource: 'real_time_updates',
      success: true,
      details: { 
        action: 'establish_delta_sse_connection',
        connectionId,
        clientVersionCount: Object.keys(clientVersions).length
      },
      ip: req.ip,
      userAgent: req.get('user-agent')
    });
  })
);

/**
 * Send missed updates to newly connected clients
 */
async function sendMissedUpdates(userId: number, connectionId: string, clientState: ClientVersionState) {
  const connections = activeConnections.get(userId);
  if (!connections || connections.size === 0) return;

  const missedUpdates: DeltaMessage[] = [];
  
  // Check for missed updates for each entity the client knows about
  for (const [entityKey, clientVersion] of clientState.entityVersions) {
    const [entityType, entityId] = entityKey.split(':');
    const serverEntity = deltaEngine.getEntityVersion(entityType, parseInt(entityId));
    
    if (serverEntity && serverEntity.version > clientVersion) {
      // Client is behind, need to send full state or catch-up deltas
      const catchUpDelta: DeltaMessage = {
        messageId: `catchup-${Date.now()}`,
        version: serverEntity.version,
        timestamp: Date.now(),
        entityType: entityType as any,
        entityId: parseInt(entityId),
        operations: [{
          op: 'set',
          path: '',
          value: serverEntity.data
        }],
        checksum: serverEntity.checksum,
        metadata: {
          source: 'catchup',
          userId,
          priority: 'high'
        }
      };
      
      missedUpdates.push(catchUpDelta);
    }
  }

  if (missedUpdates.length > 0) {
    const res = Array.from(connections)[0]; // Send to first connection
    if (res) {
      try {
        res.write(`event: missed-updates\n`);
        res.write(`data: ${JSON.stringify({
          type: 'missed-updates',
          count: missedUpdates.length,
          updates: missedUpdates,
          timestamp: Date.now()
        })}\n\n`);
        
        console.log(`📮 Sent ${missedUpdates.length} missed updates to user ${userId}`);
      } catch (error) {
        console.error(`Failed to send missed updates to user ${userId}:`, error);
      }
    }
  }
}

/**
 * Get SSE connection statistics (admin only)
 */
router.get("/stats", 
  authenticate,
  asyncHandler(async (req: any, res: Response) => {
    // Only allow owners and admins to view stats
    if (req.user.role !== 'owner' && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const stats = {
      totalConnections: Array.from(activeConnections.values())
        .reduce((total, connections) => total + connections.size, 0),
      connectedUsers: activeConnections.size,
      connectionsByUser: Array.from(activeConnections.entries()).map(([userId, connections]) => ({
        userId,
        connectionCount: connections.size
      })),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };

    return res.json({ data: stats });
  })
);

/**
 * Test endpoint for triggering events (development only)
 */
if (process.env.NODE_ENV === 'development') {
  router.post("/test-event",
    authenticate,
    asyncHandler(async (req: any, res: Response) => {
      const { eventType, targetUserId, data } = req.body;
      
      if (!eventType || !data) {
        return res.status(400).json({ error: 'eventType and data are required' });
      }

      if (targetUserId) {
        sendEventToUser(targetUserId, eventType, data);
      } else {
        broadcastEvent(eventType, data);
      }

      return res.json({ 
        message: 'Test event sent',
        eventType,
        targetUserId: targetUserId || 'all',
        timestamp: new Date().toISOString()
      });
    })
  );
}

// Export functions for use in other modules
export { sendEventToUser, broadcastEvent };
export default router;