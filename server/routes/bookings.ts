import { Router } from "express";
import rateLimit from "express-rate-limit";
import { storage } from "../storage";
import { bookingFormSchema } from "../../shared/schema";
import { authenticate, authorize } from "./auth";
import { ownerJWTValidation } from "../middlewares/enhancedAuth";
import { 
  verifyBookingAccess,
  verifyBookingManagement,
  requireOwnerRole,
  validateOwnerOperation
} from "../middlewares/ownership";
import AuditService from "../services/AuditService";
import { sanitizeIdParam } from "../middlewares/sanitization";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError,
  NotFoundError,
  AuthorizationError
} from "../middlewares/errorHandler";
import { sendDeltaUpdateToUser } from "./events";
import { 
  StandardizedErrorService,
  AppError,
  ErrorCode,
  ErrorSeverity,
  ErrorCategory,
  ValidationError,
  BookingConflictError
} from "../services/StandardizedErrorService";
import { errorRecoveryService, OperationType, ErrorRecoveryService } from "../services/ErrorRecoveryService";
import { metricsService, METRICS } from "../services/MetricsService";

const router = Router();

// Rate limiting for booking endpoints
const bookingLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // Limit booking requests to 5 per minute
  message: {
    error: "Too many booking attempts from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for booking management (owner actions)
const bookingManagementLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit booking management actions to 20 per 5 minutes
  message: {
    error: "Too many booking management actions, please slow down.",
    retryAfter: 5 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: any) => `booking_mgmt:${req.user?.userId || req.ip}`,
});

// Input validation middleware
function validate(schema: any) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      const errorMessage = error.errors?.map((e: any) => e.message).join(", ") || error.message;
      const validationError = new ValidationError(errorMessage, { zodErrors: error.errors });
      return sendError(res, validationError, req);
    }
  };
}

// Get user's bookings
router.get("/", authenticate, asyncHandler(async (req: any, res) => {
  const bookings = await storage.getBookingsWithProperty(req.user.userId);
  return sendSuccess(res, bookings);
}));

// Get single booking
router.get("/:id", authenticate, sanitizeIdParam, asyncHandler(async (req: any, res) => {
  const booking = await storage.getBooking(parseInt(req.params.id));
  
  if (!booking) {
    throw new AppError({
      code: ErrorCode.RESOURCE_NOT_FOUND,
      message: 'Booking not found',
      severity: ErrorSeverity.MEDIUM,
      category: ErrorCategory.BUSINESS_LOGIC,
      details: { bookingId: parseInt(req.params.id), userId: req.user.userId }
    });
  }

  // Users can only see their own bookings, owners can see bookings for their properties
  if (booking.userId !== req.user.userId) {
    // Check if user owns the property
    const property = await storage.getProperty(booking.propertyId);
    if (!property || property.ownerId !== req.user.userId) {
      throw new AppError({
        code: ErrorCode.FORBIDDEN,
        message: 'You can only view your own bookings or bookings for properties you own',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SECURITY_ERROR,
        details: { 
          bookingId: booking.id, 
          requestingUserId: req.user.userId, 
          bookingUserId: booking.userId,
          propertyId: booking.propertyId
        }
      });
    }
  }

  return sendSuccess(res, booking);
}));

// Create new booking
router.post("/", 
  authenticate, 
  bookingLimiter, 
  validate(bookingFormSchema), 
  asyncHandler(async (req: any, res) => {
    const { propertyId, bookingDate, bookingType, guests, totalPrice, specialRequests } = req.body;

    // Record booking creation attempt
    metricsService.incrementCounter(METRICS.BOOKINGS_CREATED_TOTAL, 1, {
      booking_type: bookingType,
      user_id: req.user.userId.toString()
    });

    // Check if property exists
    const property = await storage.getProperty(propertyId);
    if (!property) {
      throw new AppError({
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Property not found for booking',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.BUSINESS_LOGIC,
        details: { propertyId, userId: req.user.userId }
      });
    }

    // Check availability
    const isAvailable = await storage.checkAvailability(
      propertyId, 
      new Date(bookingDate), 
      bookingType
    );

    if (!isAvailable) {
      // Record booking conflict
      metricsService.incrementCounter(METRICS.BOOKING_CONFLICTS_TOTAL, 1, {
        property_id: propertyId.toString(),
        booking_type: bookingType,
        user_id: req.user.userId.toString()
      });
      
      throw new BookingConflictError(
        'Property is not available for the selected date and time',
        [], // No conflicting bookings data available
        [], // TODO: Add suggested alternatives
        req.requestId
      );
    }

    // Create booking with error recovery
    const bookingTimerId = metricsService.startTimer(METRICS.BOOKING_CLEANUP_DURATION, {
      operation: 'create_booking',
      user_id: req.user.userId.toString()
    });
    
    const bookingStrategy = ErrorRecoveryService.createBookingStrategy(
      // Primary booking creation
      async () => {
        return await storage.createBooking({
        propertyId,
        userId: req.user.userId,
        bookingDate,
        bookingType,
        guests,
        totalPrice,
        specialRequests,
        status: 'pending_payment'
      });
    }
    // No fallback processor needed for booking creation - it either succeeds or fails
    );
    
    try {
      const recoveryResult = await errorRecoveryService.executeWithRecovery(bookingStrategy);
      
      metricsService.stopTimer(bookingTimerId);
      
      if (!recoveryResult.success) {
        throw new AppError({
          code: ErrorCode.DATABASE_CONNECTION_ERROR,
          message: 'Booking creation failed after all retry attempts',
          severity: ErrorSeverity.HIGH,
          category: ErrorCategory.SYSTEM_ERROR,
          details: {
            propertyId,
            bookingDate,
            totalAttempts: recoveryResult.totalAttempts,
            errors: recoveryResult.errors.map(e => e.message)
          },
          retryable: true
        });
      }
      
      const newBooking = recoveryResult.result;

      if (!newBooking) {
        throw new Error('Booking creation failed - no result returned');
      }

      // Send delta update to property owner about new booking
      if (property.ownerId !== req.user.userId) {
        sendDeltaUpdateToUser(
          property.ownerId,
          'booking',
          newBooking.id,
          newBooking,
          {
            source: 'booking_created',
            userId: req.user.userId,
            priority: 'high'
          }
        );
      }

      // Send delta update to the booking guest
      sendDeltaUpdateToUser(
        req.user.userId,
        'booking',
        newBooking.id,
        newBooking,
        {
          source: 'booking_created',
          userId: req.user.userId,
          priority: 'high'
        }
      );
      
      return sendSuccess(res, {
        ...newBooking,
        message: "Booking created successfully. Please complete payment to confirm.",
        paymentRequired: true,
        advanceAmount: Math.round(totalPrice * 0.30), // 30% advance payment
      }, "Booking created successfully", 201);
      
    } catch (error: any) {
      metricsService.stopTimer(bookingTimerId);
      
      const standardizedError = StandardizedErrorService.standardizeError(
        error,
        { propertyId, bookingDate, bookingType, userId: req.user.userId },
        req.requestId
      );
      
      StandardizedErrorService.logError(standardizedError);
      throw standardizedError;
    }
  })
);

// Update booking status (owners only)
router.patch("/:id/status", 
  ownerJWTValidation, 
  requireOwnerRole,
  bookingManagementLimiter,
  sanitizeIdParam,
  verifyBookingManagement,
  asyncHandler(async (req: any, res) => {
    const bookingId = parseInt(req.params.id);
    const { status } = req.body;
    const booking = req.booking; // Set by verifyBookingManagement middleware

    if (!status || !['pending_payment', 'confirmed', 'cancelled', 'completed', 'payment_failed'].includes(status)) {
      throw new ValidationError('Invalid booking status provided', {
        providedStatus: status,
        validStatuses: ['pending_payment', 'confirmed', 'cancelled', 'completed', 'payment_failed']
      });
    }

    // Booking is already fetched and verified by verifyBookingManagement middleware
    if (!booking) {
      throw new AppError({
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Booking not found for status update',
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.BUSINESS_LOGIC,
        details: { bookingId, userId: req.user.userId }
      });
    }

    // Check if user owns the property
    const property = await storage.getProperty(booking.propertyId);
    if (!property || property.ownerId !== req.user.userId) {
      throw new AppError({
        code: ErrorCode.FORBIDDEN,
        message: 'You can only update bookings for properties you own',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SECURITY_ERROR,
        details: { 
          bookingId, 
          propertyId: booking.propertyId,
          requestingUserId: req.user.userId,
          propertyOwnerId: property?.ownerId
        }
      });
    }

    const updatedBooking = await storage.updateBookingStatus(bookingId, status);
    
    if (!updatedBooking) {
      throw new Error("Failed to update booking status");
    }

    // Send delta updates to both property owner and booking guest
    sendDeltaUpdateToUser(
      req.user.userId, // Property owner
      'booking',
      bookingId,
      updatedBooking,
      {
        source: 'booking_status_update',
        userId: req.user.userId,
        priority: status === 'confirmed' || status === 'cancelled' ? 'high' : 'medium'
      }
    );

    // Also send delta update to the booking guest if different from owner
    if (booking.userId !== req.user.userId) {
      sendDeltaUpdateToUser(
        booking.userId, // Guest
        'booking',
        bookingId,
        updatedBooking,
        {
          source: 'booking_status_update',
          userId: req.user.userId,
          priority: status === 'confirmed' || status === 'cancelled' ? 'high' : 'medium'
        }
      );
    }

    // Log booking status change for audit
    AuditService.logBookingAction(
      req.user.userId,
      status === 'confirmed' ? 'approve' : status === 'cancelled' ? 'reject' : 'complete',
      bookingId,
      {
        oldStatus: booking.status,
        newStatus: status,
        propertyId: booking.propertyId,
        guestId: booking.userId
      },
      req
    );

    return sendSuccess(res, updatedBooking, "Booking status updated successfully");
  })
);

// Get booking availability for a property (public - for calendar display)
router.get("/property/:id/availability", 
  sanitizeIdParam, 
  asyncHandler(async (req, res) => {
    const propertyId = parseInt(req.params.id);
    
    // Check if property exists
    const property = await storage.getProperty(propertyId);
    if (!property) {
      throw new NotFoundError("Property");
    }

    const bookings = await storage.getBookingsByProperty(propertyId);
    
    // For privacy, only return dates, booking types, and status - not user information
    const bookingDates = bookings.map(booking => ({
      date: booking.bookingDate,
      type: booking.bookingType,
      status: booking.status
    }));

    return sendSuccess(res, bookingDates);
  })
);

// Get bookings for a specific property (owners only)
router.get("/property/:id", 
  authenticate, 
  authorize(["owner"]), 
  sanitizeIdParam, 
  asyncHandler(async (req: any, res) => {
    const propertyId = parseInt(req.params.id);
    
    // Check if user owns the property
    const property = await storage.getProperty(propertyId);
    if (!property) {
      throw new NotFoundError("Property");
    }

    if (property.ownerId !== req.user.userId) {
      throw new AuthorizationError("You can only view bookings for your own properties");
    }

    const bookings = await storage.getBookingsByProperty(propertyId);
    return sendSuccess(res, bookings);
  })
);

// Get all bookings for owner's properties
router.get("/owner/me", 
  ownerJWTValidation, 
  requireOwnerRole,
  validateOwnerOperation,
  asyncHandler(async (req: any, res) => {
    // Log booking access for audit
    AuditService.logBookingAction(
      req.user.userId,
      'view',
      'owner_bookings_list',
      { action: 'list_own_bookings' },
      req
    );

    const bookings = await storage.getBookingsByOwner(req.user.userId);
    return sendSuccess(res, bookings);
  })
);

// Get all bookings for owner's properties with review status
router.get("/owner/me/detailed", 
  ownerJWTValidation, 
  requireOwnerRole,
  validateOwnerOperation,
  asyncHandler(async (req: any, res) => {
    // Log detailed booking access for audit
    AuditService.logBookingAction(
      req.user.userId,
      'view',
      'owner_bookings_detailed',
      { action: 'list_detailed_bookings' },
      req
    );

    const { bookingService } = require('../services/BookingService');
    const bookings = await bookingService.getOwnerBookingsWithReviews(req.user.userId);
    return sendSuccess(res, bookings);
  })
);

// ✅ PERFORMANCE: Batch endpoint to get bookings for multiple properties at once
// This solves the N+1 query problem by fetching all bookings in a single database query
router.post("/by-property-ids",
  authenticate,
  authorize(["owner"]),
  asyncHandler(async (req: any, res) => {
    const { propertyIds } = req.body;
    
    // Validate input
    if (!Array.isArray(propertyIds) || propertyIds.length === 0) {
      throw new ValidationError("propertyIds must be a non-empty array");
    }
    
    if (propertyIds.length > 50) {
      throw new ValidationError("Cannot fetch bookings for more than 50 properties at once");
    }
    
    // Validate that all property IDs are numbers
    const validPropertyIds = propertyIds.filter(id => typeof id === 'number' && id > 0);
    if (validPropertyIds.length !== propertyIds.length) {
      throw new ValidationError("All property IDs must be positive numbers");
    }
    
    // Verify ownership of all properties in a single query
    const properties = await storage.getPropertiesByIds(validPropertyIds);
    const ownedPropertyIds = properties
      .filter(property => property.ownerId === req.user.userId)
      .map(property => property.id);
    
    if (ownedPropertyIds.length !== validPropertyIds.length) {
      throw new AuthorizationError("You can only view bookings for your own properties");
    }
    
    // Log batch booking access for audit
    AuditService.logBookingAction(
      req.user.userId,
      'view',
      'batch_property_bookings',
      { 
        action: 'batch_fetch_bookings',
        propertyCount: ownedPropertyIds.length,
        propertyIds: ownedPropertyIds 
      },
      req
    );
    
    // Fetch bookings for all properties in a single optimized query
    const bookingsByProperty = await storage.getBookingsByPropertyIds(ownedPropertyIds);
    
    return sendSuccess(res, {
      bookingsByProperty,
      propertyCount: ownedPropertyIds.length,
      totalBookings: Object.values(bookingsByProperty).flat().length
    });
  })
);

// Check availability for a property
router.get("/check-availability/:propertyId", 
  sanitizeIdParam, 
  asyncHandler(async (req, res) => {
    const { propertyId } = req.params;
    const { date, type } = req.query;

    if (!date || !type) {
      throw new ValidationError("Date and type parameters are required");
    }

    if (!['morning', 'full_day'].includes(type as string)) {
      throw new ValidationError("Type must be 'morning' or 'full_day'");
    }

    const property = await storage.getProperty(parseInt(propertyId));
    if (!property) {
      throw new NotFoundError("Property");
    }

    const isAvailable = await storage.checkAvailability(
      parseInt(propertyId),
      new Date(date as string),
      type as 'morning' | 'full_day'
    );

    return sendSuccess(res, { available: isAvailable });
  })
);

export default router;