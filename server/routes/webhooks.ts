import { Router } from "express";
import rateLimit from "express-rate-limit";
import { z } from "zod";
import { enhancedWebhookService } from "../services/EnhancedWebhookService";
import { enhancedPaymentService } from "../services/EnhancedPaymentService";
import { storage } from "../storage";
import { 
  asyncHandler, 
  sendSuccess, 
  sendError, 
  ValidationError,
  PaymentError
} from "../middlewares/errorHandler";

const router = Router();

// Rate limiting for webhook endpoints
const webhookLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // Allow up to 100 webhook requests per minute
  message: {
    error: "Too many webhook requests from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use a combination of IP and user agent for more granular rate limiting
    return `${req.ip}_${req.get('User-Agent') || 'unknown'}`;
  }
});

// Middleware to parse raw body for webhook signature verification
function parseRawBody(req: any, res: any, next: any): void {
  req.rawBody = '';
  req.setEncoding('utf8');
  
  req.on('data', (chunk: string) => {
    req.rawBody += chunk;
  });
  
  req.on('end', () => {
    try {
      req.body = JSON.parse(req.rawBody);
      next();
    } catch (error) {
      sendError(res, new ValidationError("Invalid JSON in webhook payload"), req);
      return;
    }
  });
}

// Middleware to extract webhook headers
function extractWebhookHeaders(req: any, res: any, next: any): void {
  const signature = req.get('X-Razorpay-Signature');
  const eventId = req.get('X-Razorpay-Event-Id');
  const timestamp = req.get('X-Razorpay-Timestamp');
  
  if (!signature || !eventId || !timestamp) {
    sendError(res, new ValidationError("Missing required webhook headers"), req);
    return;
  }
  
  req.webhookData = {
    signature,
    eventId,
    timestamp: parseInt(timestamp),
    sourceIp: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type')
  };
  
  next();
}

// Razorpay webhook endpoint
router.post("/razorpay",
  webhookLimiter,
  parseRawBody,
  extractWebhookHeaders,
  asyncHandler(async (req: any, res) => {
    const { signature, eventId, timestamp, sourceIp, userAgent, contentType } = req.webhookData;
    const rawPayload = req.rawBody;
    
    try {
      // Process webhook through enhanced webhook service
      const result = await enhancedWebhookService.processWebhook(
        rawPayload,
        signature,
        eventId,
        timestamp,
        sourceIp,
        userAgent,
        contentType
      );
      
      if (!result.success) {
        console.error("Webhook processing failed:", result.error);
        
        // Return appropriate status based on whether retry is recommended
        const statusCode = result.shouldRetry ? 500 : 400;
        
        return res.status(statusCode).json({
          success: false,
          message: result.message,
          eventId: result.eventId,
          processed: result.processed
        });
      }
      
      // Success response
      return sendSuccess(res, {
        eventId: result.eventId,
        processed: result.processed,
        message: result.message
      });
      
    } catch (error: any) {
      console.error("Webhook processing error:", error);
      
      // Log the error for monitoring
      const errorMessage = error.message || "Webhook processing failed";
      
      return res.status(500).json({
        success: false,
        message: errorMessage,
        eventId: eventId,
        processed: false,
        shouldRetry: true
      });
    }
  })
);

// Test webhook endpoint (for development/testing)
router.post("/test",
  asyncHandler(async (req: any, res) => {
    if (process.env.NODE_ENV === 'production') {
      return sendError(res, new ValidationError("Test webhook not available in production"), req);
    }
    
    const { event, payload } = req.body;
    
    if (!event || !payload) {
      throw new ValidationError("Missing event or payload in test webhook");
    }
    
    console.log("Test webhook received:", { event, payload });
    
    // Simulate webhook processing
    const mockWebhookData = {
      entity: payload.entity || "payment",
      event: event,
      account_id: "test_account",
      created_at: Math.floor(Date.now() / 1000),
      payload: payload
    };
    
    const result = await enhancedWebhookService.processWebhook(
      JSON.stringify(mockWebhookData),
      "test_signature",
      `test_event_${Date.now()}`,
      Math.floor(Date.now() / 1000),
      req.ip,
      req.get('User-Agent'),
      req.get('Content-Type')
    );
    
    return sendSuccess(res, {
      message: "Test webhook processed",
      result: result
    });
  })
);

// Webhook status endpoint (for monitoring)
router.get("/status",
  asyncHandler(async (req: any, res) => {
    // Basic health check for webhook service
    const status = {
      service: "webhook",
      status: "healthy",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "development"
    };
    
    return sendSuccess(res, status);
  })
);

export default router;