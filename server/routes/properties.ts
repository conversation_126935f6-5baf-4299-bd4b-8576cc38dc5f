import { Router } from "express";
import rateLimit from "express-rate-limit";
import { storage } from "../storage";
import { propertyFormSchema } from "../../shared/schema";
import { authenticate, authorize } from "./auth";
import { enhancedJWTValidation, ownerJWTValidation } from "../middlewares/enhancedAuth";
import { 
  verifyPropertyOwnership,
  requireOwnerRole,
  validateOwnerOperation
} from "../middlewares/ownership";
import AuditService from "../services/AuditService";
import { 
  sanitizeSearchQuery, 
  sanitizeIdParam,
} from "../middlewares/sanitization";
import { 
  asyncHandler, 
  ValidationError,
  NotFoundError,
  AuthorizationError 
} from "../middlewares/errorHandler";
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFoundError,
  sendAuthorizationError,
  sendPaginatedResponse,
  getRequestId,
  withErrorHandling
} from "../../shared/api-response-utils";
import { PropertyResponse, PaginatedResponse } from "../../shared/api-response-types";
import { cacheMiddleware, cacheConfigs, CacheInvalidator } from "../middlewares/cache";
import { 
  cachePropertySearch, 
  cachePropertyDetails, 
  cacheFeaturedProperties,
  createCacheInvalidationMiddleware,
  responseCaching,
  etagCaching,
  requestDeduplication
} from "../middlewares/caching";
import { propertyCacheService } from "../services/PropertyCacheService";
import { propertyService } from "../services/PropertyService";
import multer from "multer";
import { join, extname } from "path";
import fs from "fs";
import crypto from "crypto";
import { cloudinaryService } from "../cloudinaryService";
import { parseSecureInteger, createSecureParamsMiddleware } from "../utils/secure-input";
import { validateMediaURLsMiddleware, validateExistingMediaURLMiddleware } from "../utils/secure-url-validation";

const router = Router();

// Create secure parameter validation middleware for property routes
const securePropertyIdMiddleware = createSecureParamsMiddleware({
  id: (value: string) => parseSecureInteger(value, 'propertyId', { min: 1, max: 999999999 })
});

// Rate limiting for search endpoints
const searchLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute  
  max: 30, // Limit search requests to 30 per minute
  message: {
    error: "Too many search requests from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Enhanced rate limiting for owner operations
const ownerOperationsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit owner operations to 50 per 15 minutes
  message: {
    error: "Too many owner operations from this IP, please try again later.",
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: any) => `owner_ops:${req.user?.userId || req.ip}`, // Use userId for rate limiting
});

// Critical operations rate limiting (delete, major updates)
const criticalOperationsLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit critical operations to 10 per hour
  message: {
    error: "Too many critical operations. Please wait before performing more actions.",
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: any) => `critical_ops:${req.user?.userId || req.ip}`,
});

// Input validation middleware
function validate(schema: any) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      return next();
    } catch (error: any) {
      return sendValidationError(res, error.message, undefined, getRequestId(req));
    }
  };
}

// Get all properties with filtering
router.get("/", 
  searchLimiter, 
  sanitizeSearchQuery,
  responseCaching(600), // 10 minutes cache headers
  etagCaching({ weak: true }),
  requestDeduplication(),
  cacheMiddleware(cacheConfigs.propertySearch),
  asyncHandler(async (req, res) => {
  const { 
    featured, 
    location, 
    date, 
    minPrice, 
    maxPrice, 
    amenities 
  } = req.query;

  // Parse amenities if provided
  let amenitiesArray: string[] | undefined;
  if (amenities && typeof amenities === 'string') {
    amenitiesArray = amenities.split(',').map(a => a.trim());
  }

  // Parse date if provided
  let searchDate: Date | undefined;
  if (date && typeof date === 'string') {
    searchDate = new Date(date);
  }

  // Try advanced cache first
  const cacheParams = {
    location: location as string,
    date: date as string,
    minPrice: minPrice ? parseFloat(minPrice as string) : undefined,
    maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined,
    amenities: amenitiesArray,
    featured: featured === 'true',
    limit: undefined,
    offset: undefined
  };

  const cachedResult = await propertyCacheService.getCachedPropertySearch(cacheParams);
  if (cachedResult) {
    res.set('X-Advanced-Cache', 'HIT');
    return sendSuccess(res, cachedResult);
  }

  const properties = await storage.getProperties(
    featured === 'true',
    location as string,
    searchDate,
    minPrice ? parseFloat(minPrice as string) : undefined,
    maxPrice ? parseFloat(maxPrice as string) : undefined,
    amenitiesArray
  );

  // Cache the result
  await propertyCacheService.cachePropertySearch(cacheParams, properties);
  res.set('X-Advanced-Cache', 'MISS');

  return sendSuccess(res, properties);
}));

// Property reviews endpoint for backward compatibility (must come before /:id route)
router.get("/:id/reviews", 
  sanitizeIdParam,
  responseCaching(600), // 10 minutes cache headers
  asyncHandler(async (req, res) => {
    const propertyId = parseInt(req.params.id);
    
    const property = await storage.getProperty(propertyId);
    if (!property) {
      throw new NotFoundError("Property");
    }

    const reviews = await storage.getReviews(propertyId);
    const averageRating = await storage.getPropertyAverageRating(propertyId);
    
    return sendSuccess(res, { 
      reviews, 
      averageRating, 
      totalReviews: reviews.length 
    });
  })
);

// Get single property
router.get("/:id", 
  securePropertyIdMiddleware, // SECURITY: Use secure parameter validation
  sanitizeIdParam,
  responseCaching(1800), // 30 minutes cache headers
  etagCaching({ weak: false }),
  requestDeduplication(),
  cacheMiddleware(cacheConfigs.propertyDetails),
  asyncHandler(async (req, res) => {
  const propertyId = req.params.id as unknown as number; // Already validated and parsed by middleware

  // Try advanced cache first
  const cachedProperty = await propertyCacheService.getCachedPropertyDetails(propertyId);
  if (cachedProperty) {
    res.set('X-Advanced-Cache', 'HIT');
    return sendSuccess(res, cachedProperty);
  }

  const property = await storage.getProperty(propertyId);

  if (!property) {
    throw new NotFoundError("Property");
  }

  // Cache the result
  await propertyCacheService.cachePropertyDetails(propertyId, property);
  res.set('X-Advanced-Cache', 'MISS');

  return sendSuccess(res, property);
}));

// Create new property (owners only)
router.post("/", 
  ownerJWTValidation, 
  requireOwnerRole,
  ownerOperationsLimiter,
  validate(propertyFormSchema),
  createCacheInvalidationMiddleware('property'),
  asyncHandler(async (req: any, res) => {
    const propertyData = {
      ...req.body,
      ownerId: req.user.userId
    };

    const newProperty = await storage.createProperty(propertyData);

    // Log property creation for audit
    AuditService.logPropertyAction(
      req.user.userId,
      'create',
      newProperty.id,
      { propertyTitle: newProperty.title, location: newProperty.location },
      req
    );

    // Invalidate both old and new cache systems
    await Promise.all([
      CacheInvalidator.invalidateProperty(newProperty.id),
      propertyCacheService.invalidatePropertyCache(newProperty.id, 'update'),
      propertyCacheService.invalidateOwnerCache(req.user.userId)
    ]);

    return sendSuccess(res, newProperty, "Property created successfully", 201);
  })
);

// Update property (owners only)
router.put("/:id", 
  ownerJWTValidation, 
  requireOwnerRole,
  ownerOperationsLimiter,
  sanitizeIdParam,
  verifyPropertyOwnership,
  createCacheInvalidationMiddleware('property'),
  asyncHandler(async (req: any, res) => {
    const propertyId = parseInt(req.params.id);
    const existingProperty = req.property; // Set by verifyPropertyOwnership middleware

    // Debug: Log the comparison values
    console.log(`🔍 [DEBUG] Property ownership check:`, {
      propertyId,
      existingProperty_ownerId: existingProperty.ownerId,
      req_user_userId: req.user.userId,
      types: {
        ownerId: typeof existingProperty.ownerId,
        userId: typeof req.user.userId
      }
    });

    // Ensure both values are numbers for comparison
    const propertyOwnerId = Number(existingProperty.ownerId);
    const currentUserId = Number(req.user.userId);
    
    if (propertyOwnerId !== currentUserId) {
      throw new AuthorizationError("You can only update your own properties");
    }

    // Check if pricing fields were updated
    const hasPricingChanges = [
      'halfDayPrice', 'fullDayPrice', 
      'weekdayHalfDayPrice', 'weekdayFullDayPrice',
      'weekendHalfDayPrice', 'weekendFullDayPrice'
    ].some(field => req.body[field] !== undefined);

    const updatedProperty = await storage.updateProperty(propertyId, req.body);

    if (!updatedProperty) {
      throw new NotFoundError("Property");
    }

    // Enhanced cache invalidation for pricing updates
    await Promise.all([
      CacheInvalidator.invalidateProperty(propertyId),
      propertyCacheService.invalidatePropertyCache(propertyId, 'update'),
      propertyCacheService.invalidateOwnerCache(req.user.userId),
      // Invalidate search caches if pricing changed (affects price filtering)
      hasPricingChanges ? propertyCacheService.invalidatePropertyCache(propertyId, 'update') : Promise.resolve(),
      // Invalidate owner cache for this property
      hasPricingChanges ? propertyCacheService.invalidateOwnerCache(req.user.userId) : Promise.resolve()
    ]);

    // Add real-time pricing sync flag to response
    const response = {
      ...updatedProperty,
      pricingUpdated: hasPricingChanges,
      lastPriceUpdate: hasPricingChanges ? new Date().toISOString() : null
    };

    // Log property update for audit
    AuditService.logPropertyAction(
      req.user.userId,
      'update',
      propertyId,
      { 
        hasPricingChanges, 
        updatedFields: Object.keys(req.body),
        propertyTitle: existingProperty.title 
      },
      req
    );

    // Log pricing changes specifically if they occurred
    if (hasPricingChanges) {
      AuditService.logPricingAction(
        req.user.userId,
        propertyId,
        existingProperty,
        updatedProperty,
        req
      );
    }

    return sendSuccess(res, response, "Property updated successfully");
  })
);

// Delete property (owners only)
router.delete("/:id", 
  ownerJWTValidation, 
  requireOwnerRole,
  criticalOperationsLimiter,
  sanitizeIdParam,
  verifyPropertyOwnership,
  createCacheInvalidationMiddleware('property'),
  asyncHandler(async (req: any, res) => {
    const propertyId = parseInt(req.params.id);
    const existingProperty = req.property; // Set by verifyPropertyOwnership middleware
    
    // Additional validation: Ensure both values are numbers for comparison
    const propertyOwnerId = Number(existingProperty.ownerId);
    const currentUserId = Number(req.user.userId);
    
    if (propertyOwnerId !== currentUserId) {
      throw new AuthorizationError("You can only delete your own properties");
    }

    const deleted = await storage.deleteProperty(propertyId);

    if (!deleted) {
      throw new Error("Failed to delete property");
    }

    // Log property deletion for audit (critical action)
    AuditService.logPropertyAction(
      req.user.userId,
      'delete',
      propertyId,
      { 
        propertyTitle: existingProperty.title,
        location: existingProperty.location,
        deletionReason: req.body.reason || 'Not specified'
      },
      req
    );

    // Invalidate both cache systems
    await Promise.all([
      CacheInvalidator.invalidateProperty(propertyId),
      propertyCacheService.invalidatePropertyCache(propertyId, 'delete'),
      propertyCacheService.invalidateOwnerCache(req.user.userId)
    ]);

    return sendSuccess(res, null, "Property deleted successfully");
  })
);

const UPLOADS_DIR = join(process.cwd(), "uploads");

// Ensure uploads directory exists
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

// Configure multer for cloud uploads (memory storage for direct Cloudinary upload)
const upload = multer({
  storage: multer.memoryStorage(), // Use memory storage for direct cloud upload
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit (max of image/video limits)
    files: 10 // Maximum 10 files
  },
  fileFilter: function (req, file, cb) {
    // File size constants
    const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
    const MAX_VIDEO_SIZE = 10 * 1024 * 1024; // 10MB
    
    // Check file type for both images and videos
    const allowedImageTypes = /jpeg|jpg|png/;
    const allowedVideoTypes = /mp4|webm/;
    
    const isImageExtension = allowedImageTypes.test(file.originalname.toLowerCase());
    const isVideoExtension = allowedVideoTypes.test(file.originalname.toLowerCase());
    const isImageMimetype = file.mimetype.startsWith('image/');
    const isVideoMimetype = file.mimetype.startsWith('video/');

    // Validate image files
    if (isImageMimetype && isImageExtension) {
      if (file.size && file.size > MAX_IMAGE_SIZE) {
        return cb(new Error(`Image file size exceeds 5MB limit (${(file.size / 1024 / 1024).toFixed(2)}MB)`));
      }
      return cb(null, true);
    }
    
    // Validate video files
    if (isVideoMimetype && isVideoExtension) {
      if (file.size && file.size > MAX_VIDEO_SIZE) {
        return cb(new Error(`Video file size exceeds 10MB limit (${(file.size / 1024 / 1024).toFixed(2)}MB)`));
      }
      return cb(null, true);
    }
    
    // Invalid file type
    if (isImageMimetype) {
      return cb(new Error('Only JPEG and PNG image files are allowed'));
    } else if (isVideoMimetype) {
      return cb(new Error('Only MP4 and WebM video files are allowed'));
    } else {
      return cb(new Error('Only image files (JPEG, PNG) and video files (MP4, WebM) are allowed'));
    }
  }
});

// Configure multer for fallback local storage
const multerDiskStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOADS_DIR);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const hash = crypto.createHash('md5').update(file.originalname + uniqueSuffix).digest('hex');
    cb(null, file.fieldname + '-' + hash + extname(file.originalname));
  }
});

const uploadLocal = multer({
  storage: multerDiskStorage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Maximum 10 files
  }
});

// Get properties owned by current user
router.get("/owner/me", 
  ownerJWTValidation, 
  requireOwnerRole,
  validateOwnerOperation,
  responseCaching(900), // 15 minutes cache headers
  etagCaching({ weak: true }),
  asyncHandler(async (req: any, res) => {
    const ownerId = req.user.userId;

    // Log property access for audit
    AuditService.logPropertyAction(
      ownerId,
      'read',
      'owner_properties_list',
      { action: 'list_own_properties' },
      req
    );

    // Try advanced cache first
    const cachedProperties = await propertyCacheService.getCachedOwnerProperties(ownerId);
    if (cachedProperties) {
      res.set('X-Advanced-Cache', 'HIT');
      return sendSuccess(res, cachedProperties);
    }

    const properties = await storage.getPropertiesByOwner(ownerId);

    // Cache the result
    await propertyCacheService.cacheOwnerProperties(ownerId, properties);
    res.set('X-Advanced-Cache', 'MISS');

    return sendSuccess(res, properties);
  })
);

// Enhanced media upload with cloud storage and auto-compression
router.post("/media/upload",
  authenticate,
  authorize(["owner"]),
  upload.fields([
    { name: 'images', maxCount: 10 },
    { name: 'videos', maxCount: 5 }
  ]),
  asyncHandler(async (req: any, res) => {
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const { propertyId, youtubeVideos } = req.body;
    const ownerId = req.user.userId;

    // Parse YouTube videos if provided
    let parsedYoutubeVideos: string[] = [];
    if (youtubeVideos) {
      try {
        parsedYoutubeVideos = JSON.parse(youtubeVideos);
      } catch (error) {
        throw new ValidationError("Invalid YouTube videos format");
      }
    }

    // Validate inputs
    const hasFiles = files && ((files.images && files.images.length > 0) || (files.videos && files.videos.length > 0));
    const hasYoutubeVideos = parsedYoutubeVideos && parsedYoutubeVideos.length > 0;
    
    if (!hasFiles && !hasYoutubeVideos) {
      throw new ValidationError("No media files or YouTube videos provided");
    }

    if (!propertyId) {
      throw new ValidationError("Property ID is required");
    }

    // Verify property ownership
    const property = await storage.getProperty(parseInt(propertyId));
    if (!property || property.ownerId !== ownerId) {
      throw new AuthorizationError("Property not found or access denied");
    }

    try {
      let imageUrls: string[] = [];
      let videoUrls: string[] = [];
      let uploadResults: any[] = [];

      // Process images
      if (files.images && files.images.length > 0) {
        if (cloudinaryService.isInitialized()) {
          // Use Cloudinary for images with auto-compression (direct from memory)
          const imageUploadPromises = files.images.map(file => ({
            buffer: file.buffer, // Use memory buffer directly
            filename: file.originalname
          }));

          const imageResults = await cloudinaryService.uploadMultipleImages(imageUploadPromises, 'farmhouse-properties/images');

          imageUrls = imageResults.map(result => result.secure_url);
          uploadResults.push(...imageResults.map(result => ({
            type: 'image',
            url: result.secure_url,
            public_id: result.public_id,
            width: result.width,
            height: result.height,
            format: result.format,
            size: result.bytes,
            cloudStorage: true
          })));
        } else {
          // Fallback: save to local storage and return local URLs
          const savedFiles = await Promise.all(files.images.map(async (file) => {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const filename = `image-${uniqueSuffix}${extname(file.originalname)}`;
            const filepath = join(UPLOADS_DIR, filename);
            
            fs.writeFileSync(filepath, file.buffer);
            
            return {
              filename,
              url: `/uploads/${filename}`,
              size: file.size
            };
          }));

          imageUrls = savedFiles.map(file => file.url);
          uploadResults.push(...savedFiles.map(file => ({
            type: 'image',
            url: file.url,
            filename: file.filename,
            size: file.size,
            cloudStorage: false
          })));
        }
      }

      // Process videos with enhanced streaming support
      if (files.videos && files.videos.length > 0) {
        if (cloudinaryService.isInitialized()) {
          // Use Cloudinary for videos with streaming optimization (direct from memory)
          const videoUploadPromises = files.videos.map(async (file) => {
            return await cloudinaryService.uploadVideo(file.buffer, file.originalname, 'farmhouse-videos');
          });

          const videoResults = await Promise.all(videoUploadPromises);

          // Generate streaming URLs and thumbnails
          videoUrls = videoResults.map(result => result.secure_url);
          uploadResults.push(...videoResults.map(result => {
            const streamingUrlHD = cloudinaryService.generateStreamingUrl(result.public_id, 'hd');
            const streamingUrlSD = cloudinaryService.generateStreamingUrl(result.public_id, 'sd');
            const thumbnailUrl = cloudinaryService.generateVideoThumbnail(result.public_id, { 
              width: 400, 
              height: 300 
            });

            return {
              type: 'video',
              url: result.secure_url,
              streamingUrls: {
                hd: streamingUrlHD,
                sd: streamingUrlSD
              },
              thumbnail: thumbnailUrl,
              public_id: result.public_id,
              width: result.width,
              height: result.height,
              format: result.format,
              size: result.bytes,
              duration: result.duration,
              codec: result.video?.codec,
              streaming: true,
              cloudStorage: true
            };
          }));
        } else {
          // Fallback: save to local storage and return local URLs
          const savedFiles = await Promise.all(files.videos.map(async (file) => {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const filename = `video-${uniqueSuffix}${extname(file.originalname)}`;
            const filepath = join(UPLOADS_DIR, filename);
            
            fs.writeFileSync(filepath, file.buffer);
            
            return {
              filename,
              url: `/uploads/${filename}`,
              size: file.size
            };
          }));

          videoUrls = savedFiles.map(file => file.url);
          uploadResults.push(...savedFiles.map(file => ({
            type: 'video',
            url: file.url,
            filename: file.filename,
            size: file.size,
            streaming: false,
            cloudStorage: false
          })));
        }
      }

      // Process YouTube videos
      if (parsedYoutubeVideos.length > 0) {
        // Add YouTube videos to video URLs
        videoUrls.push(...parsedYoutubeVideos);
        uploadResults.push(...parsedYoutubeVideos.map(url => ({
          type: 'youtube_video',
          url: url,
          platform: 'YouTube',
          source: 'embed'
        })));
      }

      // Update property with new media URLs
      const currentImages = property.images || [];
      const currentVideos = property.videos || [];
      const updatedImages = [...currentImages, ...imageUrls];
      const updatedVideos = [...currentVideos, ...videoUrls];

      // Update property with both images and videos
      await storage.updateProperty(parseInt(propertyId), {
        images: updatedImages,
        videos: updatedVideos
      });

      // Invalidate cache for this property
      propertyCacheService.invalidatePropertyCache(parseInt(propertyId), 'update');
      
      // Return detailed upload results
      return sendSuccess(res, {
        message: "Media uploaded successfully",
        uploadedCount: uploadResults.length,
        results: uploadResults,
        summary: {
          totalImages: updatedImages.length,
          totalVideos: updatedVideos.length,
          newImages: imageUrls.length,
          newVideos: videoUrls.length
        },
        cloudStorage: cloudinaryService.isInitialized()
      });

    } catch (error) {
      console.error('Media upload error:', error);
      
      // Clean up any uploaded files on error
      if (files.images) {
        files.images.forEach(file => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });
      }
      
      if (files.videos) {
        files.videos.forEach(file => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });
      }
      
      throw error;
    }
  })
);

// Minimum photo requirement for published listings
const MIN_PHOTOS_REQUIRED = 6;

// Enhanced media delete for specific property (images and videos)
router.delete("/media/delete",
  authenticate,
  authorize(["owner"]),
  validateExistingMediaURLMiddleware(), // SECURITY: Validate existing media URLs
  asyncHandler(async (req: any, res) => {
    const { propertyId, mediaUrl, mediaType = 'image' } = req.body;
    const ownerId = req.user.userId;

    if (!propertyId || !mediaUrl) {
      throw new ValidationError("Property ID and media URL are required");
    }

    // Verify property ownership
    const property = await storage.getProperty(parseInt(propertyId));
    if (!property || property.ownerId !== ownerId) {
      throw new AuthorizationError("Property not found or access denied");
    }

    // Check minimum photo requirement before deletion
    const currentTotalPhotos = (property.images?.length || 0) + (property.videos?.length || 0);
    if (currentTotalPhotos - 1 < MIN_PHOTOS_REQUIRED) {
      throw new ValidationError(`You must have at least ${MIN_PHOTOS_REQUIRED} photos in your listing. Upload more photos before deleting this one.`);
    }

    try {
      let updateData: any = {};
      
      // Remove from property images or videos based on mediaType
      if (mediaType === 'video') {
        const currentVideos = property.videos || [];
        const updatedVideos = currentVideos.filter(video => video !== mediaUrl);
        updateData.videos = updatedVideos;
      } else {
        const currentImages = property.images || [];
        const updatedImages = currentImages.filter(img => img !== mediaUrl);
        updateData.images = updatedImages;
      }

      await storage.updateProperty(parseInt(propertyId), updateData);

      // Try to delete from Cloudinary if it's a Cloudinary URL
      if (cloudinaryService.isInitialized() && mediaUrl.includes('cloudinary.com')) {
        try {
          // Extract public ID from Cloudinary URL
          const urlParts = mediaUrl.split('/');
          let publicId = '';
          
          // Handle different Cloudinary URL formats
          if (mediaUrl.includes('/upload/')) {
            const uploadIndex = urlParts.findIndex((part: string) => part === 'upload');
            if (uploadIndex !== -1) {
              // Skip version parameter if present (e.g., v1234567890)
              let startIndex = uploadIndex + 1;
              if (urlParts[startIndex] && urlParts[startIndex].startsWith('v')) {
                startIndex++;
              }
              
              // Reconstruct public ID from folder structure
              const publicIdParts = urlParts.slice(startIndex);
              const lastPart = publicIdParts[publicIdParts.length - 1];
              publicIdParts[publicIdParts.length - 1] = lastPart.split('.')[0]; // Remove extension
              publicId = publicIdParts.join('/');
            }
          } else {
            // Fallback for simple URLs
            const publicIdWithExtension = urlParts[urlParts.length - 1];
            publicId = publicIdWithExtension.split('.')[0];
          }
          
          if (publicId) {
            await cloudinaryService.deleteImage(publicId);
            console.log(`Successfully deleted ${mediaType} from Cloudinary: ${publicId}`);
          }
        } catch (cloudinaryError) {
          console.warn(`Failed to delete ${mediaType} from Cloudinary:`, cloudinaryError);
          // Continue anyway as the URL was removed from the property
        }
      }

      // Invalidate cache for this property
      propertyCacheService.invalidatePropertyCache(parseInt(propertyId), 'update');

      return sendSuccess(res, {
        message: `${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} deleted successfully`,
        mediaType,
        deletedUrl: mediaUrl,
        cloudStorage: cloudinaryService.isInitialized()
      });
    } catch (error) {
      throw new Error("Failed to delete media");
    }
  })
);

// Get featured properties (optimized endpoint)
router.get("/featured", 
  responseCaching(3600), // 1 hour cache headers
  etagCaching({ weak: true }),
  requestDeduplication(),
  asyncHandler(async (req, res) => {
    // Try advanced cache first
    const cachedFeatured = await propertyCacheService.getCachedFeaturedProperties();
    if (cachedFeatured) {
      res.set('X-Advanced-Cache', 'HIT');
      return sendSuccess(res, cachedFeatured);
    }

    const featuredProperties = await storage.getProperties(true); // Get featured properties

    // Cache the result
    await propertyCacheService.cacheFeaturedProperties(featuredProperties);
    res.set('X-Advanced-Cache', 'MISS');

    return sendSuccess(res, featuredProperties);
  })
);

// Get location suggestions for autocomplete
router.get("/locations/suggestions", 
  responseCaching(7200), // 2 hours cache headers
  asyncHandler(async (req, res) => {
    // Try cache first
    const cachedLocations = await propertyCacheService.getCachedLocationSuggestions();
    if (cachedLocations) {
      res.set('X-Advanced-Cache', 'HIT');
      return sendSuccess(res, cachedLocations);
    }

    // Get unique locations from database
    const locations = await storage.getUniqueLocations();

    // Cache the result
    await propertyCacheService.cacheLocationSuggestions(locations);
    res.set('X-Advanced-Cache', 'MISS');

    return sendSuccess(res, locations);
  })
);

// Get price range statistics
router.get("/stats/price-ranges", 
  responseCaching(3600), // 1 hour cache headers
  asyncHandler(async (req, res) => {
    // Try cache first
    const cachedPriceRanges = await propertyCacheService.getCachedPriceRanges();
    if (cachedPriceRanges) {
      res.set('X-Advanced-Cache', 'HIT');
      return sendSuccess(res, cachedPriceRanges);
    }

    // Calculate price statistics
    const priceStats = await storage.getPriceStatistics();

    // Cache the result
    await propertyCacheService.cachePriceRanges(priceStats);
    res.set('X-Advanced-Cache', 'MISS');

    return sendSuccess(res, priceStats);
  })
);

// Get available amenities for filtering
router.get("/amenities/available", 
  responseCaching(7200), // 2 hours cache headers
  asyncHandler(async (req, res) => {
    // Try cache first
    const cachedAmenities = await propertyCacheService.getCachedAmenityFilters();
    if (cachedAmenities) {
      res.set('X-Advanced-Cache', 'HIT');
      return sendSuccess(res, cachedAmenities);
    }

    // Get unique amenities from database
    const amenities = await storage.getAvailableAmenities();

    // Cache the result
    await propertyCacheService.cacheAmenityFilters(amenities);
    res.set('X-Advanced-Cache', 'MISS');

    return sendSuccess(res, amenities);
  })
);

// Check availability for a property (cached)
router.get("/:id/availability/:date/:type", 
  sanitizeIdParam,
  responseCaching(300), // 5 minutes cache headers
  asyncHandler(async (req, res) => {
    const propertyId = parseInt(req.params.id);
    const date = req.params.date;
    const bookingType = req.params.type;

    // Try cache first
    const cachedAvailability = await propertyCacheService.getCachedAvailability(propertyId, date, bookingType);
    if (cachedAvailability !== null) {
      res.set('X-Advanced-Cache', 'HIT');
      return sendSuccess(res, { available: cachedAvailability });
    }

    // Check availability from database
    const isAvailable = await storage.checkAvailability(propertyId, new Date(date), bookingType as any);

    // Cache the result
    await propertyCacheService.cacheAvailability(propertyId, date, bookingType, isAvailable);
    res.set('X-Advanced-Cache', 'MISS');

    return sendSuccess(res, { available: isAvailable });
  })
);

// Get dynamic pricing for a property on a specific date
router.get("/:id/pricing/:date", 
  sanitizeIdParam,
  responseCaching(60), // 1 minute cache headers for pricing
  asyncHandler(async (req, res) => {
    const propertyId = parseInt(req.params.id);
    const date = req.params.date;

    // Get property from database (this will have latest pricing)
    const property = await storage.getProperty(propertyId);
    if (!property) {
      throw new NotFoundError("Property");
    }

    // Calculate effective pricing for the requested date
    const bookingDate = new Date(date);
    const pricing = {
      date: date,
      dayOfWeek: bookingDate.getDay(),
      isWeekend: [0, 5, 6].includes(bookingDate.getDay()), // Fri, Sat, Sun
      pricing: {
        halfDay: propertyService.getEffectivePrice(property, bookingDate, '12h'),
        fullDay: propertyService.getEffectivePrice(property, bookingDate, '24h')
      },
      basePricing: {
        halfDay: property.halfDayPrice,
        fullDay: property.fullDayPrice
      },
      weekdayPricing: {
        halfDay: property.weekdayHalfDayPrice || null,
        fullDay: property.weekdayFullDayPrice || null
      },
      weekendPricing: {
        halfDay: property.weekendHalfDayPrice || null,
        fullDay: property.weekendFullDayPrice || null
      }
    };

    return sendSuccess(res, pricing);
  })
);

// Admin endpoints for cache management
router.get("/_cache/health", 
  authenticate,
  authorize(["admin"]),
  asyncHandler(async (req, res) => {
    const health = await propertyCacheService.healthCheck();
    return sendSuccess(res, health);
  })
);

router.get("/_cache/metrics", 
  authenticate,
  authorize(["admin"]),
  asyncHandler(async (req, res) => {
    const metrics = propertyCacheService.getCacheMetrics();
    return sendSuccess(res, metrics);
  })
);

router.post("/_cache/invalidate", 
  authenticate,
  authorize(["admin"]),
  asyncHandler(async (req, res) => {
    const { type, propertyId, ownerId } = req.body;

    switch (type) {
      case 'property':
        if (propertyId) {
          await propertyCacheService.invalidatePropertyCache(propertyId);
        }
        break;
      case 'owner':
        if (ownerId) {
          await propertyCacheService.invalidateOwnerCache(ownerId);
        }
        break;
      case 'all':
        await propertyCacheService.invalidateAllPropertyCache();
        break;
      default:
        throw new ValidationError("Invalid cache invalidation type");
    }

    return sendSuccess(res, null, "Cache invalidated successfully");
  })
);

router.post("/_cache/warmup", 
  authenticate,
  authorize(["admin"]),
  asyncHandler(async (req, res) => {
    await propertyCacheService.warmupCache();
    return sendSuccess(res, null, "Cache warmup completed");
  })
);

// Media reorder endpoint for changing display order
router.put("/media/reorder",
  authenticate,
  authorize(["owner"]),
  validateExistingMediaURLMiddleware(), // SECURITY: Validate existing media URLs
  asyncHandler(async (req: any, res) => {
    const { propertyId, mediaUrl, newOrder, mediaType = 'image' } = req.body;
    const ownerId = req.user.userId;

    if (!propertyId || !mediaUrl || newOrder === undefined) {
      throw new ValidationError("Property ID, media URL, and new order are required");
    }

    // Verify property ownership
    const property = await storage.getProperty(parseInt(propertyId));
    if (!property || property.ownerId !== ownerId) {
      throw new AuthorizationError("Property not found or access denied");
    }

    try {
      let updateData: any = {};
      
      if (mediaType === 'video') {
        const currentVideos = property.videos || [];
        const currentIndex = currentVideos.indexOf(mediaUrl);
        
        if (currentIndex === -1) {
          throw new ValidationError("Video not found in property");
        }
        
        // Reorder videos array
        const reorderedVideos = [...currentVideos];
        const [movedVideo] = reorderedVideos.splice(currentIndex, 1);
        reorderedVideos.splice(newOrder, 0, movedVideo);
        
        updateData.videos = reorderedVideos;
      } else {
        const currentImages = property.images || [];
        const currentIndex = currentImages.indexOf(mediaUrl);
        
        if (currentIndex === -1) {
          throw new ValidationError("Image not found in property");
        }
        
        // Reorder images array
        const reorderedImages = [...currentImages];
        const [movedImage] = reorderedImages.splice(currentIndex, 1);
        reorderedImages.splice(newOrder, 0, movedImage);
        
        updateData.images = reorderedImages;
      }

      await storage.updateProperty(parseInt(propertyId), updateData);

      // Invalidate cache for this property
      propertyCacheService.invalidatePropertyCache(parseInt(propertyId), 'update');

      return sendSuccess(res, {
        message: `${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} order updated successfully`,
        mediaType,
        mediaUrl,
        newOrder,
        cloudStorage: cloudinaryService.isInitialized()
      });
    } catch (error) {
      throw new Error("Failed to reorder media");
    }
  })
);

// Media edit endpoint for updating title/caption
router.put("/media/edit",
  authenticate,
  authorize(["owner"]),
  validateExistingMediaURLMiddleware(), // SECURITY: Validate existing media URLs
  asyncHandler(async (req: any, res) => {
    const { propertyId, mediaUrl, title, caption } = req.body;
    const ownerId = req.user.userId;

    if (!propertyId || !mediaUrl) {
      throw new ValidationError("Property ID and media URL are required");
    }

    // Verify property ownership
    const property = await storage.getProperty(parseInt(propertyId));
    if (!property || property.ownerId !== ownerId) {
      throw new AuthorizationError("Property not found or access denied");
    }

    try {
      // For now, we'll store media metadata in a simple way
      // In a real application, you might want to create a separate media table
      // For this implementation, we'll just validate the media exists
      const allMedia = [...(property.images || []), ...(property.videos || [])];
      
      if (!allMedia.includes(mediaUrl)) {
        throw new ValidationError("Media not found in property");
      }

      // Since we're storing URLs directly in the property, we can't easily store metadata
      // For this demo, we'll just return success
      // In a production app, you'd want a proper media metadata table
      
      // Invalidate cache for this property
      propertyCacheService.invalidatePropertyCache(parseInt(propertyId), 'update');

      return sendSuccess(res, {
        message: "Media details updated successfully",
        mediaUrl,
        title,
        caption,
        note: "Media metadata is stored client-side for this demo"
      });
    } catch (error) {
      throw new Error("Failed to update media details");
    }
  })
);

// Add pre-uploaded media URLs to property (for Cloudinary direct uploads)
router.post("/media/add-urls",
  authenticate,
  authorize(["owner"]),
  validateMediaURLsMiddleware(), // SECURITY: Prevent SSRF attacks and validate URLs
  asyncHandler(async (req: any, res) => {
    const { propertyId, images = [], videos = [] } = req.body;
    const ownerId = req.user.userId;

    if (!propertyId) {
      throw new ValidationError("Property ID is required");
    }

    if (!images.length && !videos.length) {
      throw new ValidationError("At least one image or video URL is required");
    }

    // Verify property ownership
    const property = await storage.getProperty(parseInt(propertyId));
    if (!property || property.ownerId !== ownerId) {
      throw new AuthorizationError("Property not found or access denied");
    }

    try {
      // Get current media
      const currentImages = property.images || [];
      const currentVideos = property.videos || [];

      // Add new URLs to existing media
      const updatedImages = [...currentImages, ...images];
      const updatedVideos = [...currentVideos, ...videos];

      // Update property with new media URLs
      const updateData: any = {};
      if (images.length > 0) {
        updateData.images = updatedImages;
      }
      if (videos.length > 0) {
        updateData.videos = updatedVideos;
      }

      await storage.updateProperty(parseInt(propertyId), updateData);

      // Invalidate cache
      propertyCacheService.invalidatePropertyCache(parseInt(propertyId), 'update');

      return sendSuccess(res, {
        message: "Media URLs added successfully",
        addedImages: images.length,
        addedVideos: videos.length,
        totalImages: updatedImages.length,
        totalVideos: updatedVideos.length
      });
    } catch (error) {
      throw new Error("Failed to add media URLs");
    }
  })
);

// Bulk reorder media for drag & drop
router.put("/media/reorder-bulk",
  authenticate,
  authorize(["owner"]),
  validateExistingMediaURLMiddleware(), // SECURITY: Validate existing media URLs in array
  asyncHandler(async (req: any, res) => {
    const { propertyId, newOrder } = req.body;
    const ownerId = req.user.userId;

    if (!propertyId || !newOrder || !Array.isArray(newOrder)) {
      throw new ValidationError("Property ID and new order array are required");
    }

    // Verify property ownership
    const property = await storage.getProperty(parseInt(propertyId));
    if (!property || property.ownerId !== ownerId) {
      throw new AuthorizationError("Property not found or access denied");
    }

    try {
      // Separate images and videos from the new order
      const currentImages = property.images || [];
      const currentVideos = property.videos || [];
      
      const newImages: string[] = [];
      const newVideos: string[] = [];

      newOrder.forEach(mediaUrl => {
        if (currentImages.includes(mediaUrl)) {
          newImages.push(mediaUrl);
        } else if (currentVideos.includes(mediaUrl)) {
          newVideos.push(mediaUrl);
        }
      });

      // Update property with new order
      await storage.updateProperty(parseInt(propertyId), {
        images: newImages,
        videos: newVideos
      });

      // Invalidate cache
      propertyCacheService.invalidatePropertyCache(parseInt(propertyId), 'update');

      return sendSuccess(res, {
        message: "Media order updated successfully",
        imageCount: newImages.length,
        videoCount: newVideos.length
      });
    } catch (error) {
      throw new Error("Failed to reorder media");
    }
  })
);

export default router;