import { Router } from "express";
import { authenticate, authorize } from "./auth";
import { cloudinaryService } from "../cloudinaryService";
import { config } from "../config";
import { sendSuccess, sendError, getRequestId } from "../../shared/api-response-utils";
import { asyncHandler } from "../middlewares/errorHandler";
import crypto from "crypto";

const router = Router();

// Generate signed upload parameters for client-side uploads
router.post("/upload-signature", 
  authenticate, 
  authorize(["owner"]), 
  asyncHandler(async (req: any, res) => {
    if (!config.cloudinary.available) {
      return sendError(res, "SERVICE_UNAVAILABLE", "Cloudinary service not available", 503, undefined, getRequestId(req));
    }

    const { 
      folder = 'farmhouse-properties',
      resourceType = 'auto',
      transformation,
      eager,
      tags = [],
      context = {}
    } = req.body;

    const userId = req.user.userId;
    const timestamp = Math.round(new Date().getTime() / 1000);
    
    // Create upload parameters
    const uploadParams: any = {
      timestamp,
      folder: `${folder}/${userId}`,
      resource_type: resourceType,
      upload_preset: undefined, // We'll use signature instead
      tags: ['farmhouse', 'property', `user_${userId}`, ...tags],
      context: {
        user_id: userId,
        upload_source: 'web_app',
        ...context
      }
    };

    // Add transformations for different media types
    if (resourceType === 'video' || resourceType === 'auto') {
      uploadParams.video_codec = 'h264';
      uploadParams.audio_codec = 'aac';
      uploadParams.quality = 'auto:good';
      
      // Add eager transformations for videos
      uploadParams.eager = [
        {
          quality: 'auto:good',
          format: 'mp4',
          video_codec: 'h264',
          audio_codec: 'aac',
          width: 1280,
          height: 720,
          crop: 'limit'
        },
        {
          quality: 'auto:low',
          format: 'mp4', 
          video_codec: 'h264',
          audio_codec: 'aac',
          width: 640,
          height: 360,
          crop: 'limit'
        },
        // Generate video thumbnail
        {
          resource_type: 'image',
          format: 'jpg',
          quality: 'auto',
          width: 400,
          height: 300,
          crop: 'fill'
        }
      ];
      uploadParams.eager_async = true;
    } else {
      // Image optimizations
      uploadParams.quality = 'auto:good';
      uploadParams.fetch_format = 'auto';
      uploadParams.eager = [
        {
          width: 1200,
          height: 800,
          crop: 'limit',
          quality: 'auto:good',
          format: 'auto'
        },
        {
          width: 400,
          height: 300,
          crop: 'fill',
          quality: 'auto:good',
          format: 'auto'
        }
      ];
    }

    // Custom transformations if provided
    if (transformation) {
      uploadParams.transformation = transformation;
    }

    if (eager) {
      uploadParams.eager = eager;
      uploadParams.eager_async = true;
    }

    try {
      // Generate signature
      const stringToSign = Object.keys(uploadParams)
        .filter(key => uploadParams[key] !== undefined && uploadParams[key] !== null)
        .sort()
        .map(key => {
          const value = uploadParams[key];
          if (Array.isArray(value)) {
            return `${key}=${value.join(',')}`;
          } else if (typeof value === 'object') {
            return `${key}=${JSON.stringify(value)}`;
          }
          return `${key}=${value}`;
        })
        .join('&') + config.cloudinary.apiSecret!;

      const signature = crypto
        .createHash('sha1')
        .update(stringToSign)
        .digest('hex');

      const signedParams = {
        ...uploadParams,
        signature,
        api_key: config.cloudinary.apiKey!,
        cloud_name: config.cloudinary.cloudName!,
        upload_url: `https://api.cloudinary.com/v1_1/${config.cloudinary.cloudName}/auto/upload`
      };

      return sendSuccess(res, signedParams, "Upload signature generated successfully");
    } catch (error: any) {
      console.error('Error generating Cloudinary signature:', error);
      return sendError(res, "INTERNAL_ERROR", "Failed to generate upload signature", 500, undefined, getRequestId(req));
    }
  })
);

// Get Cloudinary configuration for client-side
router.get("/config", 
  authenticate, 
  asyncHandler(async (req: any, res) => {
    if (!config.cloudinary.available) {
      return sendError(res, "SERVICE_UNAVAILABLE", "Cloudinary service not available", 503, undefined, getRequestId(req));
    }

    const clientConfig = {
      cloudName: config.cloudinary.cloudName!,
      apiKey: config.cloudinary.apiKey!,
      uploadUrl: `https://api.cloudinary.com/v1_1/${config.cloudinary.cloudName}/auto/upload`,
      // Security settings
      maxFileSize: {
        image: 5 * 1024 * 1024, // 5MB
        video: 10 * 1024 * 1024 // 10MB
      },
      allowedFormats: {
        image: ['jpg', 'jpeg', 'png'],
        video: ['mp4', 'webm']
      }
    };

    return sendSuccess(res, clientConfig, "Cloudinary configuration retrieved");
  })
);

export default router;