/**
 * Architecture Usage Example
 * Demonstrates how to use the new DI container, repositories, and service factories
 */

import { 
  initializeApplication, 
  getApplication, 
  resolveService, 
  createRequestLogger 
} from '../core/ApplicationBootstrap';
import { TOKENS } from '../core/Container';
import { ILogger } from '../core/interfaces/ILogger';
import { ICache } from '../core/interfaces/ICache';
import { IUserRepository } from '../repositories/interfaces/IUserRepository';
import { IPropertyRepository } from '../repositories/interfaces/IPropertyRepository';

/**
 * Example: Application Initialization
 */
export async function initializeApp() {
  try {
    // Initialize the application with DI container
    const app = await initializeApplication();
    console.log('Application initialized successfully');
    
    // Get core services
    const logger = resolveService<ILogger>(TOKENS.LOGGER);
    const cache = resolveService<ICache>(TOKENS.CACHE);
    
    logger.info('Application services initialized', 'Example');
    
    return app;
  } catch (error) {
    console.error('Failed to initialize application:', error);
    throw error;
  }
}

/**
 * Example: Using Repositories with Dependency Injection
 */
export async function repositoryExample() {
  const app = getApplication();
  const container = app.getContainer();
  
  // Create request-scoped container
  const requestScope = app.createRequestScope();
  
  try {
    // Resolve repositories from request scope
    const userRepo = requestScope.resolve<IUserRepository>('IUserRepository');
    const propertyRepo = requestScope.resolve<IPropertyRepository>('IPropertyRepository');
    
    // Use repositories
    const users = await userRepo.findAll({ limit: 10 });
    const properties = await propertyRepo.findFeatured(5);
    
    console.log(`Found ${users.length} users and ${properties.length} properties`);
    
    return { users, properties };
  } finally {
    // Clean up request scope
    if (requestScope.dispose) {
      await requestScope.dispose();
    }
  }
}

/**
 * Example: Service Layer with DI
 */
export class UserService {
  constructor(
    private userRepository: IUserRepository,
    private logger: ILogger,
    private cache: ICache
  ) {}

  async getUserProfile(userId: number) {
    const cacheKey = `user_profile:${userId}`;
    
    try {
      // Try cache first
      const cached = await this.cache.get(cacheKey);
      if (cached) {
        this.logger.debug('User profile cache hit', 'UserService', { userId });
        return cached;
      }

      // Fetch from repository
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Cache the result
      await this.cache.set(cacheKey, user, 300); // 5 minutes
      
      this.logger.info('User profile retrieved', 'UserService', { userId });
      return user;
    } catch (error) {
      this.logger.error('Error getting user profile', error as Error, 'UserService', { userId });
      throw error;
    }
  }

  async createUser(userData: any) {
    try {
      // Validate email is not taken
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new Error('Email already taken');
      }

      // Create user with hashed password
      const user = await this.userRepository.createWithHashedPassword(userData);
      
      this.logger.audit('create', 'user', user.id, user.id, undefined, {
        email: user.email,
        role: user.role
      });

      return user;
    } catch (error) {
      this.logger.error('Error creating user', error as Error, 'UserService');
      throw error;
    }
  }
}

/**
 * Example: Express Route Handler with DI
 */
export function createUserRouteHandler() {
  return async (req: any, res: any, next: any) => {
    // Create request-scoped logger
    const requestId = req.headers['x-request-id'] || 'unknown';
    const logger = createRequestLogger(requestId, req.user?.id);
    
    try {
      // Get request-scoped container
      const container = req.container || getApplication().createRequestScope();
      
      // Resolve dependencies
      const userRepo = container.resolve('IUserRepository') as IUserRepository;
      const cache = container.resolve(TOKENS.CACHE) as ICache;
      
      // Create service with dependencies
      const userService = new UserService(userRepo, logger, cache);
      
      // Handle request
      const userData = req.body;
      const user = await userService.createUser(userData);
      
      res.json({ success: true, user: { id: user.id, email: user.email } });
    } catch (error) {
      logger.error('Route handler error', error as Error, 'UserRoutes');
      res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Example: Transaction Management with Unit of Work
 */
export async function transactionExample() {
  const app = getApplication();
  const requestScope = app.createRequestScope();
  
  try {
    const userRepo = requestScope.resolve('IUserRepository') as IUserRepository;
    const propertyRepo = requestScope.resolve('IPropertyRepository') as IPropertyRepository;
    const unitOfWork = requestScope.resolve('IUnitOfWork') as any;
    
    // Execute operations in transaction
    const result = await unitOfWork.executeInTransaction(async () => {
      // Create user
      const user = await userRepo.createWithHashedPassword({
        username: 'propertyowner',
        email: '<EMAIL>',
        password: 'password123',
        role: 'owner',
        fullName: 'Property Owner'
      });

      // Create property for user
      const property = await propertyRepo.create({
        ownerId: user.id,
        title: 'Beautiful Villa',
        description: 'A stunning villa with ocean views',
        location: 'Goa',
        halfDayPrice: 5000,
        fullDayPrice: 8000,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['wifi', 'pool', 'parking'],
        images: ['image1.jpg'],
        status: 'active',
        featured: false
      });

      return { user, property };
    });

    console.log('Transaction completed successfully:', result);
    return result;
  } finally {
    if (requestScope.dispose) {
      await requestScope.dispose();
    }
  }
}

/**
 * Example: Service Factory Usage
 */
export function serviceFactoryExample() {
  const app = getApplication();
  const container = app.getContainer();
  
  // Register custom service
  container.registerFactory(
    'CustomAnalyticsService',
    (container) => {
      const logger = container.resolve<ILogger>(TOKENS.LOGGER);
      const cache = container.resolve<ICache>(TOKENS.CACHE);
      
      return {
        trackEvent: async (eventName: string, data: any) => {
          logger.info(`Analytics event: ${eventName}`, 'Analytics', data);
          await cache.set(`event:${Date.now()}`, { eventName, data, timestamp: new Date() }, 3600);
        }
      };
    },
    'singleton'
  );

  // Use the custom service
  const analytics = container.resolve('CustomAnalyticsService');
  return analytics;
}

/**
 * Example: Performance Monitoring Integration
 */
export async function performanceMonitoringExample() {
  const app = getApplication();
  const container = app.getContainer();
  
  try {
    // Get performance monitor service if available
    const performanceMonitor = container.resolve(TOKENS.PERFORMANCE_MONITOR_SERVICE) as any;
    
    // Record a performance metric
    const startTime = Date.now();
    
    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Record the performance
    performanceMonitor.recordQuery('example_operation', Date.now() - startTime, true);
    
    // Get metrics
    const metrics = await performanceMonitor.getMetrics();
    console.log('Performance metrics:', metrics);
    
    return metrics;
  } catch (error) {
    console.warn('Performance monitoring not available:', error);
    return null;
  }
}

/**
 * Example: Graceful Shutdown
 */
export async function shutdownExample() {
  try {
    const app = getApplication();
    const logger = resolveService<ILogger>(TOKENS.LOGGER);
    
    logger.info('Starting graceful shutdown', 'Example');
    
    // Shutdown the application
    await app.shutdown();
    
    console.log('Application shutdown completed');
  } catch (error) {
    console.error('Error during shutdown:', error);
  }
}