// Example usage of CalendarService
// This file demonstrates how to use the CalendarService CRUD operations

import { calendarService } from '../services/CalendarService';
import { InsertCalendarBooking, InsertCalendarSyncStatus } from '../../shared/schema';

async function exampleCalendarOperations() {
  try {
    console.log('=== CalendarService Example Usage ===\n');

    // Example 1: Create a calendar booking
    console.log('1. Creating a calendar booking...');
    const newBooking: InsertCalendarBooking = {
      propertyId: 1,
      startDate: '2024-08-15',
      endDate: '2024-08-16',
      status: 'confirmed',
      bookingType: 'direct',
      guestName: '<PERSON>',
      guestPhone: '+1234567890',
      guestCount: 4,
      notes: 'Birthday celebration',
      source: 'website',
      createdBy: 1
    };

    const createdBooking = await calendarService.createCalendarBooking(newBooking);
    console.log('✓ Booking created:', {
      id: createdBooking.id,
      propertyId: createdBooking.propertyId,
      dates: `${createdBooking.startDate} to ${createdBooking.endDate}`,
      guest: createdBooking.guestName
    });

    // Example 2: Check availability
    console.log('\n2. Checking availability...');
    const availability = await calendarService.checkAvailability(
      1, // propertyId
      '2024-08-15',
      '2024-08-16'
    );
    console.log('✓ Availability check:', {
      isAvailable: availability.isAvailable,
      message: availability.message,
      conflicts: availability.conflictingBookings.length
    });

    // Example 3: Get calendar bookings with filters
    console.log('\n3. Fetching calendar bookings...');
    const bookings = await calendarService.getCalendarBookings({
      propertyId: 1,
      limit: 5,
      status: ['confirmed', 'tentative']
    });
    console.log('✓ Bookings fetched:', {
      count: bookings.length,
      examples: bookings.slice(0, 2).map(b => ({
        id: b.id,
        dates: `${b.startDate} to ${b.endDate}`,
        status: b.status,
        property: b.property?.title || 'Unknown'
      }))
    });

    // Example 4: Update a booking
    console.log('\n4. Updating booking status...');
    const updatedBooking = await calendarService.updateCalendarBooking(
      createdBooking.id,
      { 
        status: 'tentative',
        notes: 'Status changed to tentative'
      }
    );
    console.log('✓ Booking updated:', {
      id: updatedBooking?.id,
      newStatus: updatedBooking?.status,
      newNotes: updatedBooking?.notes
    });

    // Example 5: Get property calendar for date range
    console.log('\n5. Getting property calendar...');
    const propertyCalendar = await calendarService.getPropertyCalendar(
      1,
      '2024-08-01',
      '2024-08-31'
    );
    console.log('✓ Property calendar:', {
      propertyId: 1,
      bookingsInAugust: propertyCalendar.length,
      dateRange: '2024-08-01 to 2024-08-31'
    });

    // Example 6: Create sync status
    console.log('\n6. Creating sync status...');
    const syncData: InsertCalendarSyncStatus = {
      propertyId: 1,
      calendarType: 'google',
      isActive: true,
      syncSettings: {
        autoSync: true,
        syncInterval: '1h'
      }
    };

    const syncStatus = await calendarService.createSyncStatus(syncData);
    console.log('✓ Sync status created:', {
      id: syncStatus.id,
      propertyId: syncStatus.propertyId,
      calendarType: syncStatus.calendarType,
      isActive: syncStatus.isActive
    });

    // Example 7: Get calendar statistics
    console.log('\n7. Getting calendar statistics...');
    const stats = await calendarService.getCalendarStats(1, 2024);
    console.log('✓ Calendar statistics:', {
      propertyId: 1,
      year: 2024,
      totalBookings: stats.totalBookings,
      confirmedBookings: stats.confirmedBookings,
      tentativeBookings: stats.tentativeBookings,
      blockedDays: stats.blockedDays,
      topSources: Object.keys(stats.bySource).slice(0, 3)
    });

    // Example 8: Clean up - delete the test booking
    console.log('\n8. Cleaning up test data...');
    const deleted = await calendarService.deleteCalendarBooking(createdBooking.id);
    console.log('✓ Test booking deleted:', deleted);

    console.log('\n=== CalendarService Example Completed Successfully ===');

  } catch (error) {
    console.error('❌ Error in calendar service example:', error);
    throw error;
  }
}

// Export the example function
export { exampleCalendarOperations };

// Run example if this file is executed directly
if (require.main === module) {
  exampleCalendarOperations()
    .then(() => {
      console.log('\n🎉 All calendar service operations completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Calendar service example failed:', error);
      process.exit(1);
    });
}