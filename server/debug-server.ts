import express from "express";
import { createServer } from "http";
import path from "path";

const app = express();
const server = createServer(app);

// Simple test route
app.get("/test", (req, res) => {
  res.json({ message: "Server is working!", timestamp: new Date().toISOString() });
});

// Serve static files from the built frontend
const distPath = path.resolve(process.cwd(), "dist", "public");
console.log("Serving static files from:", distPath);

app.use(express.static(distPath));

// Fallback to index.html for client-side routing
app.get("*", (req, res) => {
  res.sendFile(path.resolve(distPath, "index.html"));
});

const port = 5000;
server.listen(port, "0.0.0.0", () => {
  console.log(`Debug server listening on port ${port}`);
  console.log(`Test endpoint: http://localhost:${port}/test`);
});

// Handle server errors
server.on('error', (err) => {
  console.error('Server error:', err);
});

// Handle process errors
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});