/**
 * ✅ ERROR HANDLING STANDARDIZATION
 * 
 * Centralized error classes and utilities for consistent error handling
 * across the entire backend codebase.
 * 
 * Benefits:
 * - 🚀 Consistent error responses across all endpoints
 * - 🚀 Better error tracking and debugging
 * - 🚀 Structured logging for monitoring
 * - 🚀 Type-safe error handling
 */

import { Request } from 'express';

// Error severity levels for logging and monitoring
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Error categories for better organization
export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  BUSINESS_LOGIC = 'business_logic',
  DATABASE = 'database',
  EXTERNAL_SERVICE = 'external_service',
  SYSTEM = 'system'
}

// Enhanced error context interface
export interface ErrorContext {
  userId?: number;
  requestId?: string;
  service?: string;
  operation?: string;
  metadata?: Record<string, any>;
  stack?: string;
}

// Base application error class with enhanced tracking
export abstract class BaseError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly isOperational: boolean;
  public readonly severity: ErrorSeverity;
  public readonly category: ErrorCategory;
  public readonly context: ErrorContext;
  public readonly timestamp: Date;

  constructor(
    message: string,
    statusCode: number,
    code: string,
    isOperational: boolean = true,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.SYSTEM,
    context: ErrorContext = {}
  ) {
    super(message);
    
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.severity = severity;
    this.category = category;
    this.context = context;
    this.timestamp = new Date();
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, this.constructor);
    
    // Set the name to the class name
    this.name = this.constructor.name;
  }

  // Convert error to structured log format
  toLog(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      severity: this.severity,
      category: this.category,
      isOperational: this.isOperational,
      timestamp: this.timestamp.toISOString(),
      context: this.context,
      stack: this.stack
    };
  }

  // Convert error to API response format
  toResponse(includeStack: boolean = false): Record<string, any> {
    return {
      error: {
        code: this.code,
        message: this.message,
        ...(this.context.requestId && { requestId: this.context.requestId }),
        ...(includeStack && { stack: this.stack })
      }
    };
  }
}

// Validation errors (400)
export class ValidationError extends BaseError {
  public readonly details?: any;

  constructor(message: string, details?: any, context?: ErrorContext) {
    super(
      message,
      400,
      'VALIDATION_ERROR',
      true,
      ErrorSeverity.LOW,
      ErrorCategory.VALIDATION,
      context
    );
    this.details = details;
  }

  override toResponse(includeStack: boolean = false): Record<string, any> {
    const response = super.toResponse(includeStack);
    if (this.details) {
      response.error.details = this.details;
    }
    return response;
  }
}

// Authentication errors (401)
export class AuthenticationError extends BaseError {
  constructor(message: string = 'Authentication required', context?: ErrorContext) {
    super(
      message,
      401,
      'AUTHENTICATION_ERROR',
      true,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHENTICATION,
      context
    );
  }
}

// Authorization errors (403)
export class AuthorizationError extends BaseError {
  constructor(message: string = 'Insufficient permissions', context?: ErrorContext) {
    super(
      message,
      403,
      'AUTHORIZATION_ERROR',
      true,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHORIZATION,
      context
    );
  }
}

// Not found errors (404)
export class NotFoundError extends BaseError {
  public readonly resource?: string;

  constructor(resource: string = 'Resource', context?: ErrorContext) {
    super(
      `${resource} not found`,
      404,
      'NOT_FOUND',
      true,
      ErrorSeverity.LOW,
      ErrorCategory.BUSINESS_LOGIC,
      context
    );
    this.resource = resource;
  }
}

// Conflict errors (409)
export class ConflictError extends BaseError {
  constructor(message: string, context?: ErrorContext) {
    super(
      message,
      409,
      'CONFLICT_ERROR',
      true,
      ErrorSeverity.MEDIUM,
      ErrorCategory.BUSINESS_LOGIC,
      context
    );
  }
}

// Rate limit errors (429)
export class RateLimitError extends BaseError {
  public readonly retryAfter?: number;

  constructor(message: string = 'Rate limit exceeded', retryAfter?: number, context?: ErrorContext) {
    super(
      message,
      429,
      'RATE_LIMIT_ERROR',
      true,
      ErrorSeverity.LOW,
      ErrorCategory.SYSTEM,
      context
    );
    if (retryAfter !== undefined) {
      this.retryAfter = retryAfter;
    }
  }

  override toResponse(includeStack: boolean = false): Record<string, any> {
    const response = super.toResponse(includeStack);
    if (this.retryAfter) {
      response.error.retryAfter = this.retryAfter;
    }
    return response;
  }
}

// Business logic errors (422)
export class BusinessLogicError extends BaseError {
  constructor(message: string, code: string = 'BUSINESS_LOGIC_ERROR', context?: ErrorContext) {
    super(
      message,
      422,
      code,
      true,
      ErrorSeverity.MEDIUM,
      ErrorCategory.BUSINESS_LOGIC,
      context
    );
  }
}

// External service errors (502)
export class ExternalServiceError extends BaseError {
  public readonly service: string;
  public readonly originalError?: any;

  constructor(service: string, message: string, originalError?: any, context?: ErrorContext) {
    super(
      `External service error (${service}): ${message}`,
      502,
      'EXTERNAL_SERVICE_ERROR',
      true,
      ErrorSeverity.HIGH,
      ErrorCategory.EXTERNAL_SERVICE,
      { ...context, service }
    );
    this.service = service;
    this.originalError = originalError;
  }

  override toLog(): Record<string, any> {
    const log = super.toLog();
    log.service = this.service;
    if (this.originalError) {
      log.originalError = this.originalError;
    }
    return log;
  }
}

// Database errors (500)
export class DatabaseError extends BaseError {
  public readonly query?: string;
  public readonly originalError?: any;

  constructor(message: string, query?: string, originalError?: any, context?: ErrorContext) {
    super(
      message,
      500,
      'DATABASE_ERROR',
      false, // Database errors are not operational
      ErrorSeverity.HIGH,
      ErrorCategory.DATABASE,
      context
    );
    if (query !== undefined) {
      this.query = query;
    }
    this.originalError = originalError;
  }

  override toLog(): Record<string, any> {
    const log = super.toLog();
    if (this.query) {
      log.query = this.query;
    }
    if (this.originalError) {
      log.originalError = this.originalError;
    }
    return log;
  }
}

// Internal server errors (500)
export class InternalError extends BaseError {
  constructor(message: string = 'Internal server error', context?: ErrorContext) {
    super(
      message,
      500,
      'INTERNAL_ERROR',
      false,
      ErrorSeverity.CRITICAL,
      ErrorCategory.SYSTEM,
      context
    );
  }
}

// Payment-specific errors
export class PaymentError extends BaseError {
  public readonly paymentProvider?: string;
  public readonly paymentId?: string;

  constructor(
    message: string,
    paymentProvider?: string,
    paymentId?: string,
    context?: ErrorContext
  ) {
    super(
      message,
      400,
      'PAYMENT_ERROR',
      true,
      ErrorSeverity.HIGH,
      ErrorCategory.EXTERNAL_SERVICE,
      { ...context, metadata: { paymentProvider, paymentId } }
    );
    if (paymentProvider !== undefined) {
      this.paymentProvider = paymentProvider;
    }
    if (paymentId !== undefined) {
      this.paymentId = paymentId;
    }
  }
}

// Error factory for creating errors from unknown sources
export class ErrorFactory {
  static fromUnknown(error: unknown, context?: ErrorContext): BaseError {
    // If it's already our error, return it
    if (error instanceof BaseError) {
      return error;
    }

    // Handle standard Error objects
    if (error instanceof Error) {
      // Check for specific error types
      if (error.name === 'ValidationError') {
        return new ValidationError(error.message, undefined, context);
      }
      
      if (error.name === 'CastError' || error.name === 'TypeError') {
        return new ValidationError(`Invalid data type: ${error.message}`, undefined, context);
      }

      // Database connection errors
      if (error.message.includes('ECONNREFUSED') || error.message.includes('ETIMEDOUT')) {
        return new DatabaseError('Database connection failed', undefined, error, context);
      }

      // Generic error
      const errorContext = { ...context };
      if (error.stack) {
        errorContext.stack = error.stack;
      }
      return new InternalError(error.message, errorContext);
    }

    // Handle string errors
    if (typeof error === 'string') {
      return new InternalError(error, context);
    }

    // Handle object errors (e.g., from external APIs)
    if (typeof error === 'object' && error !== null) {
      const err = error as any;
      const message = err.message || err.error || 'Unknown error occurred';
      const code = err.code || err.statusCode || 500;
      
      if (code >= 400 && code < 500) {
        return new BusinessLogicError(message, err.code, context);
      }
      
      return new InternalError(message, context);
    }

    // Fallback for unknown error types
    return new InternalError('An unknown error occurred', context);
  }

  // Create error with request context
  static withContext(error: unknown, req: Request, additionalContext?: Partial<ErrorContext>): BaseError {
    const context: ErrorContext = {
      requestId: req.headers['x-request-id'] as string,
      userId: (req as any).user?.userId,
      metadata: {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('user-agent'),
        ...additionalContext?.metadata
      },
      ...additionalContext
    };

    return this.fromUnknown(error, context);
  }
}

// Type guard to check if error is operational
export function isOperationalError(error: Error): boolean {
  if (error instanceof BaseError) {
    return error.isOperational;
  }
  return false;
}

// Type guard to check if error needs immediate attention
export function isCriticalError(error: Error): boolean {
  if (error instanceof BaseError) {
    return error.severity === ErrorSeverity.CRITICAL;
  }
  return false;
}

// Export all error classes for convenience
export * from '../middlewares/errorHandler';