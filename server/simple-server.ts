import express from "express";
import path from "path";
import { fileURLToPath } from "url";

const app = express();
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Serve static files from the built frontend
const distPath = path.resolve(__dirname, "..", "dist", "public");
console.log("Serving static files from:", distPath);

// Serve assets with /assets prefix
app.use("/assets", express.static(path.join(distPath, "assets")));

// Serve other static files from root
app.use(express.static(distPath));

// Simple health check
app.get("/api/health", (req, res) => {
  res.json({ status: "ok", timestamp: new Date().toISOString() });
});

// Serve uploaded files
app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

// Fallback to index.html for client-side routing
app.get("*", (req, res) => {
  res.sendFile(path.resolve(distPath, "index.html"));
});

const PORT = 5000;
app.listen(PORT, "0.0.0.0", () => {
  console.log(`Simple server running on http://localhost:${PORT}`);
});