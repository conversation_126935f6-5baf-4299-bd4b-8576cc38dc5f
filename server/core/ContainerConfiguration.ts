/**
 * Container Configuration
 * Centralized dependency injection setup
 */

import { Container, TOKENS } from './Container';
import { ServiceFactory, ServiceConfig } from './factories/ServiceFactory';
import { LoggerService } from '../services/LoggerService';
import { cacheService } from '../services/CacheService';

export class ContainerConfiguration {
  private container: Container;
  private serviceFactory: ServiceFactory;

  constructor(config: ServiceConfig) {
    this.container = new Container();
    this.serviceFactory = new ServiceFactory(this.container, config);
    this.configureServices(config);
  }

  getContainer(): Container {
    return this.container;
  }

  private configureServices(config: ServiceConfig): void {
    // Register configuration
    this.container.registerInstance(TOKENS.CONFIG, config);
    // Don't register the container as a service to avoid circular disposal

    // Register core services
    this.registerCoreServices();

    // Register repositories
    this.registerRepositories();

    // Register business services
    this.registerBusinessServices();

    // Apply service decorators
    this.serviceFactory.applyServiceDecorators();
  }

  private registerCoreServices(): void {
    // Logger service - singleton
    this.container.registerFactory(
      TOKENS.LOGGER,
      () => this.serviceFactory.createLogger(),
      'singleton'
    );

    // Cache service - singleton
    this.container.registerFactory(
      TOKENS.CACHE,
      () => this.serviceFactory.createCache(),
      'singleton'
    );

    // For backward compatibility, also register existing services
    this.container.registerInstance('LoggerService', LoggerService);
    this.container.registerInstance('CacheService', cacheService);
  }

  private registerRepositories(): void {
    this.serviceFactory.createRepositories();
    this.serviceFactory.createUnitOfWork();
  }

  private registerBusinessServices(): void {
    this.serviceFactory.createBusinessServices();
  }

  /**
   * Create request-scoped container for handling HTTP requests
   */
  createRequestScope(): Container {
    return this.container.createScope() as Container;
  }

  /**
   * Gracefully shutdown the container and dispose resources
   */
  async shutdown(): Promise<void> {
    await this.container.dispose();
  }
}

/**
 * Default configuration factory
 */
export function createDefaultConfig(): ServiceConfig {
  const environment = (process.env.NODE_ENV as any) || 'development';
  
  return {
    environment,
    cache: {
      enabled: environment !== 'test',
      provider: environment === 'production' ? 'redis' : 'memory',
      ttl: {
        default: 300,    // 5 minutes
        short: 60,       // 1 minute
        medium: 900,     // 15 minutes
        long: 3600       // 1 hour
      }
    },
    logging: {
      level: environment === 'production' ? 'info' : 'debug',
      format: environment === 'production' ? 'json' : 'console',
      destinations: environment === 'production' 
        ? ['console', 'file'] 
        : ['console']
    },
    database: {
      pool: {
        min: 2,
        max: environment === 'production' ? 20 : 10
      },
      timeout: 30000
    },
    performance: {
      monitoring: environment === 'production',
      metricsRetention: 1000,
      slowQueryThreshold: 500
    }
  };
}

/**
 * Global container instance
 */
let globalContainer: ContainerConfiguration | null = null;

export function initializeContainer(config?: ServiceConfig): ContainerConfiguration {
  if (globalContainer) {
    // Reset the global container for testing
    globalContainer = null;
  }

  const serviceConfig = config || createDefaultConfig();
  globalContainer = new ContainerConfiguration(serviceConfig);
  return globalContainer;
}

export function getContainer(): Container {
  if (!globalContainer) {
    throw new Error('Container not initialized. Call initializeContainer() first.');
  }
  return globalContainer.getContainer();
}

export async function shutdownContainer(): Promise<void> {
  if (globalContainer) {
    await globalContainer.shutdown();
    globalContainer = null;
  }
}