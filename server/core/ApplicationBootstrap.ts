/**
 * Application Bootstrap
 * Initializes the dependency injection container and configures services
 */

import { 
  ContainerConfiguration, 
  createDefaultConfig, 
  initializeContainer,
  getContainer 
} from './ContainerConfiguration';
import { ServiceMigrationHelper } from './adapters/LegacyServiceAdapter';
import { TOKENS } from './Container';
import { ILogger } from './interfaces/ILogger';

export class ApplicationBootstrap {
  private containerConfig: ContainerConfiguration;
  private migrationHelper: ServiceMigrationHelper;

  constructor() {
    // Initialize with default configuration
    const config = createDefaultConfig();
    this.containerConfig = initializeContainer(config);
    this.migrationHelper = new ServiceMigrationHelper(this.containerConfig.getContainer());
  }

  /**
   * Initialize the application with dependency injection
   */
  async initialize(): Promise<void> {
    const logger = this.containerConfig.getContainer().resolve<ILogger>(TOKENS.LOGGER);
    
    try {
      logger.info('Starting application bootstrap', 'ApplicationBootstrap');

      // Register legacy services for backward compatibility
      this.migrationHelper.registerLegacyServices();

      // Migrate existing service instances
      this.migrationHelper.migrateExistingServices();

      logger.info('Application bootstrap completed successfully', 'ApplicationBootstrap');
    } catch (error) {
      logger.error('Application bootstrap failed', error as Error, 'ApplicationBootstrap');
      throw error;
    }
  }

  /**
   * Get the configured container
   */
  getContainer() {
    return this.containerConfig.getContainer();
  }

  /**
   * Create request-scoped container
   */
  createRequestScope() {
    return this.containerConfig.createRequestScope();
  }

  /**
   * Get migration helper
   */
  getMigrationHelper() {
    return this.migrationHelper;
  }

  /**
   * Gracefully shutdown the application
   */
  async shutdown(): Promise<void> {
    const logger = this.containerConfig.getContainer().resolve<ILogger>(TOKENS.LOGGER);
    
    try {
      logger.info('Starting application shutdown', 'ApplicationBootstrap');
      await this.containerConfig.shutdown();
      logger.info('Application shutdown completed', 'ApplicationBootstrap');
    } catch (error) {
      logger.error('Error during application shutdown', error as Error, 'ApplicationBootstrap');
      throw error;
    }
  }
}

// Global application instance
let applicationInstance: ApplicationBootstrap | null = null;

/**
 * Initialize the global application instance
 */
export async function initializeApplication(): Promise<ApplicationBootstrap> {
  if (applicationInstance) {
    // Reset the global application instance for testing
    applicationInstance = null;
  }

  applicationInstance = new ApplicationBootstrap();
  await applicationInstance.initialize();
  
  return applicationInstance;
}

/**
 * Get the global application instance
 */
export function getApplication(): ApplicationBootstrap {
  if (!applicationInstance) {
    throw new Error('Application not initialized. Call initializeApplication() first.');
  }
  return applicationInstance;
}

/**
 * Shutdown the global application instance
 */
export async function shutdownApplication(): Promise<void> {
  if (applicationInstance) {
    await applicationInstance.shutdown();
    applicationInstance = null;
  }
}

/**
 * Express middleware factory for dependency injection
 */
export function createDIMiddleware() {
  const app = getApplication();
  return app.getMigrationHelper().createRouteHandlerWrapper;
}

/**
 * Utility function to resolve services
 */
export function resolveService<T>(token: string | symbol): T {
  const container = getContainer();
  return container.resolve<T>(token);
}

/**
 * Utility function to create request logger
 */
export function createRequestLogger(requestId: string, userId?: string | number): ILogger {
  const logger = resolveService<ILogger>(TOKENS.LOGGER);
  
  // Create a logger wrapper with request context
  return {
    info: (message: string, context?: string, metadata?: Record<string, any>) => 
      logger.info(message, context, { ...metadata, requestId, userId }),
    
    warn: (message: string, context?: string, metadata?: Record<string, any>) => 
      logger.warn(message, context, { ...metadata, requestId, userId }),
    
    error: (message: string, error?: Error, context?: string, metadata?: Record<string, any>) => 
      logger.error(message, error, context, { ...metadata, requestId, userId }),
    
    debug: (message: string, context?: string, metadata?: Record<string, any>) => 
      logger.debug(message, context, { ...metadata, requestId, userId }),
    
    httpRequest: logger.httpRequest.bind(logger),
    performance: logger.performance.bind(logger),
    audit: logger.audit.bind(logger)
  };
}