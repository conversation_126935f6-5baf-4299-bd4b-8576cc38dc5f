/**
 * Legacy Service Adapter
 * Provides backward compatibility with existing services while migrating to new architecture
 */

import { IContainer } from '../interfaces/IContainer';
import { ILogger } from '../interfaces/ILogger';
import { ICache } from '../interfaces/ICache';
import { TOKENS } from '../Container';

/**
 * Adapter for existing logger service
 */
export class LoggerAdapter implements ILogger {
  private legacyLogger: any;

  constructor(legacyLogger: any) {
    this.legacyLogger = legacyLogger;
  }

  info(message: string, context?: string, metadata?: Record<string, any>): void {
    this.legacyLogger.info(message, context, metadata);
  }

  warn(message: string, context?: string, metadata?: Record<string, any>): void {
    this.legacyLogger.warn(message, context, metadata);
  }

  error(message: string, error?: Error, context?: string, metadata?: Record<string, any>): void {
    this.legacyLogger.error(message, error, context, metadata);
  }

  debug(message: string, context?: string, metadata?: Record<string, any>): void {
    this.legacyLogger.debug(message, context, metadata);
  }

  httpRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    requestId?: string,
    userId?: string | number,
    userAgent?: string,
    ip?: string,
    metadata?: Record<string, any>
  ): void {
    this.legacyLogger.httpRequest(
      method, url, statusCode, responseTime, requestId, userId, userAgent, ip, metadata
    );
  }

  performance(
    operation: string,
    startTime: number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.legacyLogger.performance(operation, startTime, requestId, metadata);
  }

  audit(
    action: string,
    resource: string,
    resourceId: string | number,
    userId?: string | number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void {
    this.legacyLogger.audit(action, resource, resourceId, userId, requestId, metadata);
  }
}

/**
 * Adapter for existing cache service
 */
export class CacheAdapter implements ICache {
  private legacyCache: any;

  constructor(legacyCache: any) {
    this.legacyCache = legacyCache;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    return this.legacyCache.set(key, value, ttl);
  }

  async get<T>(key: string): Promise<T | null> {
    return this.legacyCache.get(key);
  }

  async delete(key: string): Promise<boolean> {
    return this.legacyCache.delete(key);
  }

  async keys(pattern: string): Promise<string[]> {
    return this.legacyCache.keys(pattern);
  }

  async clear(): Promise<void> {
    return this.legacyCache.clear();
  }

  async has(key: string): Promise<boolean> {
    return this.legacyCache.has(key);
  }

  getStats(): any {
    return this.legacyCache.getStats();
  }

  configure(options: Record<string, any>): void {
    if (this.legacyCache.configure) {
      this.legacyCache.configure(options);
    }
  }
}

/**
 * Service migration helper
 */
export class ServiceMigrationHelper {
  private container: IContainer;

  constructor(container: IContainer) {
    this.container = container;
  }

  /**
   * Register legacy services with adapters
   */
  registerLegacyServices(): void {
    // Register legacy logger
    try {
      const { logger: legacyLogger } = require('../../services/LoggerService');
      const loggerAdapter = new LoggerAdapter(legacyLogger);
      this.container.registerInstance(TOKENS.LOGGER, loggerAdapter);
    } catch (error) {
      console.warn('Could not register legacy logger service:', error);
    }

    // Register legacy cache
    try {
      const { cacheService: legacyCache } = require('../../services/CacheService');
      const cacheAdapter = new CacheAdapter(legacyCache);
      this.container.registerInstance(TOKENS.CACHE, cacheAdapter);
    } catch (error) {
      console.warn('Could not register legacy cache service:', error);
    }
  }

  /**
   * Create compatibility layer for existing route handlers
   */
  createRouteHandlerWrapper(handler: Function) {
    return async (req: any, res: any, next: any) => {
      // Create request-scoped container
      const scopedContainer = this.container.createScope();
      
      // Inject container into request for use in handlers
      req.container = scopedContainer;
      req.services = {
        logger: scopedContainer.resolve<ILogger>(TOKENS.LOGGER),
        cache: scopedContainer.resolve<ICache>(TOKENS.CACHE)
      };

      try {
        await handler(req, res, next);
      } finally {
        // Clean up scoped container
        if (scopedContainer.dispose) {
          await scopedContainer.dispose();
        }
      }
    };
  }

  /**
   * Migrate existing service instances to use DI
   */
  migrateExistingServices(): void {
    try {
      // Migrate PropertyCacheService
      const { propertyCacheService } = require('../../services/PropertyCacheService');
      if (propertyCacheService) {
        this.container.registerInstance(TOKENS.PROPERTY_CACHE_SERVICE, propertyCacheService);
      }

      // Migrate PerformanceMonitorService
      const { performanceMonitor } = require('../../services/PerformanceMonitorService');
      if (performanceMonitor) {
        this.container.registerInstance(TOKENS.PERFORMANCE_MONITOR_SERVICE, performanceMonitor);
      }
    } catch (error) {
      console.warn('Could not migrate existing services:', error);
    }
  }

  /**
   * Update existing storage to use repositories
   */
  createStorageAdapter(): any {
    const logger = this.container.resolve<ILogger>(TOKENS.LOGGER);
    
    // Import existing storage
    const { storage: legacyStorage } = require('../../storage');
    
    // Create adapter that delegates to repositories when available
    return new Proxy(legacyStorage, {
      get(target, prop, receiver) {
        // Try to resolve from container first
        try {
          switch (prop) {
            case 'getAllUsers':
            case 'getUser':
            case 'getUserByEmail':
            case 'createUser':
            case 'updateUser':
              const userRepo = receiver.container?.resolve('IUserRepository');
              if (userRepo && userRepo[prop]) {
                return userRepo[prop].bind(userRepo);
              }
              break;
              
            case 'getProperties':
            case 'getProperty':
            case 'createProperty':
            case 'updateProperty':
            case 'deleteProperty':
              const propertyRepo = receiver.container?.resolve('IPropertyRepository');
              if (propertyRepo && propertyRepo[prop]) {
                return propertyRepo[prop].bind(propertyRepo);
              }
              break;
          }
        } catch (error) {
          // Fall back to legacy implementation
          logger.debug('Falling back to legacy storage method', 'ServiceMigrationHelper', { 
            method: String(prop),
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        // Fall back to original implementation
        return Reflect.get(target, prop, receiver);
      }
    });
  }
}

/**
 * Express middleware to inject dependencies into requests
 */
export function createDependencyInjectionMiddleware(container: IContainer) {
  return (req: any, res: any, next: any) => {
    // Create request-scoped container
    const scopedContainer = container.createScope();
    
    // Attach to request
    req.container = scopedContainer;
    req.services = {
      logger: scopedContainer.resolve<ILogger>(TOKENS.LOGGER),
      cache: scopedContainer.resolve<ICache>(TOKENS.CACHE)
    };

    // Clean up on response end
    const cleanup = async () => {
      if (scopedContainer.dispose) {
        await scopedContainer.dispose();
      }
    };

    res.on('finish', cleanup);
    res.on('close', cleanup);
    res.on('error', cleanup);

    next();
  };
}