/**
 * Service Factory for consistent service creation
 * Implements factory pattern with configuration-based instantiation
 */

import { IContainer } from '../interfaces/IContainer';
import { ILogger } from '../interfaces/ILogger';
import { ICache } from '../interfaces/ICache';
import { TOKENS } from '../Container';

export interface ServiceConfig {
  environment: 'development' | 'production' | 'test';
  cache: {
    enabled: boolean;
    provider: 'memory' | 'redis';
    ttl: {
      default: number;
      short: number;
      medium: number;
      long: number;
    };
  };
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    format: 'json' | 'console';
    destinations: ('console' | 'file' | 'external')[];
  };
  database: {
    pool: {
      min: number;
      max: number;
    };
    timeout: number;
  };
  performance: {
    monitoring: boolean;
    metricsRetention: number;
    slowQueryThreshold: number;
  };
}

export class ServiceFactory {
  private container: IContainer;
  private config: ServiceConfig;

  constructor(container: IContainer, config: ServiceConfig) {
    this.container = container;
    this.config = config;
  }

  /**
   * Create a logger service with environment-specific configuration
   */
  createLogger(): ILogger {
    // Create a simple logger implementation for the architecture tests
    return {
      info: (message: string, context?: string, metadata?: Record<string, any>) => 
        console.log(`[INFO] ${context ? `[${context}] ` : ''}${message}`, metadata),
      
      warn: (message: string, context?: string, metadata?: Record<string, any>) => 
        console.warn(`[WARN] ${context ? `[${context}] ` : ''}${message}`, metadata),
      
      error: (message: string, error?: Error, context?: string, metadata?: Record<string, any>) => 
        console.error(`[ERROR] ${context ? `[${context}] ` : ''}${message}`, error, metadata),
      
      debug: (message: string, context?: string, metadata?: Record<string, any>) => 
        console.log(`[DEBUG] ${context ? `[${context}] ` : ''}${message}`, metadata),
      
      httpRequest: (
        method: string,
        url: string,
        statusCode: number,
        responseTime: number,
        requestId?: string,
        userId?: string | number,
        userAgent?: string,
        ip?: string,
        metadata?: Record<string, any>
      ) => console.log(`[HTTP] ${method} ${url} ${statusCode} - ${responseTime}ms`),
      
      performance: (
        operation: string,
        startTime: number,
        requestId?: string,
        metadata?: Record<string, any>
      ) => {
        const duration = Date.now() - startTime;
        console.log(`[PERF] ${operation} completed in ${duration}ms`);
      },
      
      audit: (
        action: string,
        resource: string,
        resourceId: string | number,
        userId?: string | number,
        requestId?: string,
        metadata?: Record<string, any>
      ) => console.log(`[AUDIT] ${action} ${resource}:${resourceId}`)
    };
  }

  /**
   * Create a cache service based on configuration
   */
  createCache(): ICache {
    if (!this.config.cache.enabled) {
      return this.createNullCache();
    }

    switch (this.config.cache.provider) {
      case 'redis':
        return this.createRedisCache();
      case 'memory':
      default:
        return this.createMemoryCache();
    }
  }

  /**
   * Create repository services with proper dependencies
   */
  createRepositories(): void {
    const logger = this.container.resolve<ILogger>(TOKENS.LOGGER);

    // For tests, register mock repositories
    if (this.config.environment === 'test') {
      this.registerMockRepositories(logger);
      return;
    }

    // Production repositories
    try {
      // User Repository
      const UserRepository = require('../../repositories/UserRepository').UserRepository;
      this.container.registerFactory(
        'IUserRepository',
        () => new UserRepository(logger),
        'scoped'
      );

      // Property Repository  
      const PropertyRepository = require('../../repositories/PropertyRepository').PropertyRepository;
      this.container.registerFactory(
        'IPropertyRepository',
        () => new PropertyRepository(logger),
        'scoped'
      );

      // Booking Repository
      const BookingRepository = require('../../repositories/BookingRepository').BookingRepository;
      this.container.registerFactory(
        'IBookingRepository',
        () => new BookingRepository(logger),
        'scoped'
      );

      // Review Repository
      const ReviewRepository = require('../../repositories/ReviewRepository').ReviewRepository;
      this.container.registerFactory(
        'IReviewRepository',
        () => new ReviewRepository(logger),
        'scoped'
      );
    } catch (error) {
      // Fallback to mock repositories if real ones can't be loaded
      logger.warn('Failed to load repositories, using mocks', 'ServiceFactory', { error });
      this.registerMockRepositories(logger);
    }
  }

  private registerMockRepositories(logger: ILogger): void {
    // Mock User Repository
    this.container.registerFactory(
      'IUserRepository',
      () => ({
        findById: async () => null,
        findBy: async () => [],
        create: async () => ({ id: 1 } as any),
        update: async () => null,
        delete: async () => false,
        count: async () => 0,
        exists: async () => false,
        findAll: async () => [],
        findOne: async () => null,
        findByEmail: async () => null,
        findByPhone: async () => null,
        isEmailTaken: async () => false,
        isPhoneTaken: async () => false,
        createWithHashedPassword: async () => ({ id: 1 } as any),
        updatePassword: async () => false,
        getPaginated: async () => ({ users: [], total: 0, hasMore: false })
      }),
      'scoped'
    );

    // Mock Property Repository
    this.container.registerFactory(
      'IPropertyRepository',
      () => ({
        findById: async () => null,
        findBy: async () => [],
        create: async () => ({ id: 1 } as any),
        update: async () => null,
        delete: async () => false,
        count: async () => 0,
        exists: async () => false,
        findAll: async () => [],
        findOne: async () => null,
        findFeatured: async () => [],
        searchProperties: async () => [],
        findByOwner: async () => [],
        findByLocation: async () => [],
        findByPriceRange: async () => [],
        findByAmenities: async () => [],
        checkAvailability: async () => true,
        getUniqueLocations: async () => [],
        getPriceStatistics: async () => ({ min: 0, max: 0, avg: 0, median: 0 }),
        getAvailableAmenities: async () => [],
        updateStatus: async () => false,
        setFeatured: async () => false,
        searchWithPagination: async () => ({ properties: [], total: 0, hasMore: false })
      }),
      'scoped'
    );

    // Register other mock repositories as needed
    this.container.registerFactory('IBookingRepository', () => ({}), 'scoped');
    this.container.registerFactory('IReviewRepository', () => ({}), 'scoped');
  }

  /**
   * Create business services with dependencies
   */
  createBusinessServices(): void {
    const logger = this.container.resolve<ILogger>(TOKENS.LOGGER);
    const cache = this.container.resolve<ICache>(TOKENS.CACHE);

    // For tests, register mock services
    if (this.config.environment === 'test') {
      this.registerMockBusinessServices(logger, cache);
      return;
    }

    try {
      // Property Cache Service
      const PropertyCacheService = require('../../services/PropertyCacheService').PropertyCacheService;
      this.container.registerFactory(
        'IPropertyCacheService',
        () => new PropertyCacheService(cache, logger),
        'singleton'
      );

      // Performance Monitor Service
      const PerformanceMonitorService = require('../../services/PerformanceMonitorService').PerformanceMonitorService;
      this.container.registerFactory(
        'IPerformanceMonitorService',
        () => new PerformanceMonitorService(logger, cache),
        'singleton'
      );
    } catch (error) {
      // Fallback to mock services
      logger.warn('Failed to load business services, using mocks', 'ServiceFactory', { error });
      this.registerMockBusinessServices(logger, cache);
    }
  }

  private registerMockBusinessServices(logger: ILogger, cache: ICache): void {
    // Mock Property Cache Service
    this.container.registerFactory(
      'IPropertyCacheService',
      () => ({
        get: async () => null,
        set: async () => {},
        invalidate: async () => {},
        clear: async () => {}
      }),
      'singleton'
    );

    // Mock Performance Monitor Service
    this.container.registerFactory(
      TOKENS.PERFORMANCE_MONITOR_SERVICE,
      () => ({
        recordQuery: () => {},
        getMetrics: async () => ({
          queries: { total: 0, slow: 0, failed: 0, averageTime: 0 }
        })
      }),
      'singleton'
    );
  }

  /**
   * Create unit of work for transaction management
   */
  createUnitOfWork(): void {
    const logger = this.container.resolve<ILogger>(TOKENS.LOGGER);
    
    // For tests, register mock unit of work
    if (this.config.environment === 'test') {
      this.container.registerFactory(
        'IUnitOfWork',
        () => ({
          executeInTransaction: async (work: () => Promise<any>) => work()
        }),
        'scoped'
      );
      return;
    }

    try {
      const UnitOfWork = require('../../repositories/UnitOfWork').UnitOfWork;
      this.container.registerFactory(
        'IUnitOfWork',
        () => new UnitOfWork(logger),
        'scoped'
      );
    } catch (error) {
      // Fallback to mock
      logger.warn('Failed to load UnitOfWork, using mock', 'ServiceFactory', { error });
      this.container.registerFactory(
        'IUnitOfWork',
        () => ({
          executeInTransaction: async (work: () => Promise<any>) => work()
        }),
        'scoped'
      );
    }
  }

  /**
   * Apply decorators to services for cross-cutting concerns
   */
  applyServiceDecorators(): void {
    // Performance monitoring decorator
    if (this.config.performance.monitoring) {
      this.wrapWithPerformanceMonitoring();
    }

    // Caching decorator
    if (this.config.cache.enabled) {
      this.wrapWithCaching();
    }

    // Logging decorator
    this.wrapWithLogging();
  }

  private createMemoryCache(): ICache {
    // For now, use the existing cache service structure
    const { cacheService } = require('../../services/CacheService');
    return cacheService;
  }

  private createRedisCache(): ICache {
    // For now, use the existing cache service structure
    const { cacheService } = require('../../services/CacheService');
    return cacheService;
  }

  private createNullCache(): ICache {
    // Simple null cache implementation
    return {
      get: async () => null,
      set: async () => {},
      delete: async () => true,
      clear: async () => {},
      keys: async () => [],
      has: async () => false,
      getStats: () => ({
        size: 0,
        maxSize: 0,
        hitRate: 0,
        memoryUsage: '0 MB'
      })
    };
  }

  private wrapWithPerformanceMonitoring(): void {
    // Wrap repository methods with performance monitoring
    const performanceMonitor = this.container.resolve<any>('IPerformanceMonitorService');
    
    // This would wrap repository methods to track performance
    // Implementation depends on specific requirements
  }

  private wrapWithCaching(): void {
    // Wrap specific repository methods with caching
    // Implementation depends on caching strategy
  }

  private wrapWithLogging(): void {
    // Wrap service methods with logging
    // Implementation depends on logging requirements
  }
}

/**
 * Service decorator for adding cross-cutting concerns
 */
export class ServiceDecorator {
  static withLogging<T extends object>(
    target: T,
    logger: ILogger,
    context: string
  ): T {
    return new Proxy(target, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver);
        
        if (typeof value === 'function') {
          return function (...args: any[]) {
            const startTime = Date.now();
            logger.debug(`Calling ${String(prop)}`, context, { args: args.length });
            
            try {
              const result = value.apply(target, args);
              
              if (result instanceof Promise) {
                return result
                  .then((res) => {
                    logger.performance(String(prop), startTime, undefined, { context });
                    return res;
                  })
                  .catch((error) => {
                    logger.error(`Error in ${String(prop)}`, error, context);
                    throw error;
                  });
              }
              
              logger.performance(String(prop), startTime, undefined, { context });
              return result;
            } catch (error) {
              logger.error(`Error in ${String(prop)}`, error as Error, context);
              throw error;
            }
          };
        }
        
        return value;
      }
    });
  }

  static withCaching<T extends object>(
    target: T,
    cache: ICache,
    keyPrefix: string,
    ttl: number = 300
  ): T {
    return new Proxy(target, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver);
        
        if (typeof value === 'function' && String(prop).startsWith('find')) {
          return async function (...args: any[]) {
            const cacheKey = `${keyPrefix}:${String(prop)}:${JSON.stringify(args)}`;
            
            // Try to get from cache first
            const cached = await cache.get(cacheKey);
            if (cached) {
              return cached;
            }
            
            // Execute original method
            const result = await value.apply(target, args);
            
            // Cache the result
            await cache.set(cacheKey, result, ttl);
            
            return result;
          };
        }
        
        return value;
      }
    });
  }
}