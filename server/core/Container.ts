/**
 * Lightweight Dependency Injection Container
 * Implements inversion of control with lifecycle management
 */

import { IContainer, IDisposable, ServiceDescriptor, ServiceLifetime } from './interfaces/IContainer';

export class Container implements IContainer, IDisposable {
  private services = new Map<string | symbol, ServiceDescriptor>();
  private singletonInstances = new Map<string | symbol, any>();
  private scopedInstances = new Map<string | symbol, any>();
  private isScoped = false;
  private parentContainer: Container | undefined;

  constructor(parent?: Container) {
    this.parentContainer = parent;
    this.isScoped = !!parent;
  }

  /**
   * Register a service with the container
   */
  register<T>(
    token: string | symbol,
    implementation: new (...args: any[]) => T,
    lifetime: ServiceLifetime = 'transient',
    dependencies: (string | symbol)[] = []
  ): IContainer {
    this.services.set(token, {
      token,
      implementation,
      lifetime,
      dependencies
    });
    return this;
  }

  /**
   * Register a service using a factory function
   */
  registerFactory<T>(
    token: string | symbol,
    factory: (container: IContainer) => T,
    lifetime: ServiceLifetime = 'transient'
  ): IContainer {
    this.services.set(token, {
      token,
      factory,
      lifetime,
      dependencies: []
    });
    return this;
  }

  /**
   * Register a singleton instance
   */
  registerInstance<T>(token: string | symbol, instance: T): IContainer {
    this.services.set(token, {
      token,
      instance,
      lifetime: 'singleton',
      dependencies: []
    });
    this.singletonInstances.set(token, instance);
    return this;
  }

  /**
   * Resolve a service from the container
   */
  resolve<T>(token: string | symbol): T {
    const descriptor = this.getDescriptor(token);
    
    if (!descriptor) {
      throw new Error(`Service '${String(token)}' is not registered`);
    }

    return this.createInstance<T>(descriptor);
  }

  /**
   * Check if a service is registered
   */
  isRegistered(token: string | symbol): boolean {
    return this.services.has(token) || (this.parentContainer?.isRegistered(token) ?? false);
  }

  /**
   * Create a scoped container for request-specific services
   */
  createScope(): IContainer {
    return new Container(this);
  }

  /**
   * Dispose of scoped resources
   */
  async dispose(): Promise<void> {
    // Dispose of scoped instances
    for (const instance of Array.from(this.scopedInstances.values())) {
      if (this.isDisposable(instance)) {
        try {
          await instance.dispose();
        } catch (error) {
          // Log disposal error but continue with other disposals
          console.error('Error disposing scoped service:', error);
        }
      }
    }
    this.scopedInstances.clear();

    // Only dispose singletons if this is the root container
    if (!this.isScoped) {
      for (const instance of Array.from(this.singletonInstances.values())) {
        if (this.isDisposable(instance)) {
          try {
            await instance.dispose();
          } catch (error) {
            // Log disposal error but continue with other disposals
            console.error('Error disposing singleton service:', error);
          }
        }
      }
      this.singletonInstances.clear();
    }
  }

  private getDescriptor(token: string | symbol): ServiceDescriptor | undefined {
    let descriptor = this.services.get(token);
    
    if (!descriptor && this.parentContainer) {
      descriptor = this.parentContainer.getDescriptor(token);
    }
    
    return descriptor;
  }

  private createInstance<T>(descriptor: ServiceDescriptor): T {
    switch (descriptor.lifetime) {
      case 'singleton':
        return this.resolveSingleton<T>(descriptor);
      case 'scoped':
        return this.resolveScoped<T>(descriptor);
      case 'transient':
      default:
        return this.resolveTransient<T>(descriptor);
    }
  }

  private resolveSingleton<T>(descriptor: ServiceDescriptor): T {
    const container = this.getRootContainer();
    
    if (descriptor.instance) {
      return descriptor.instance;
    }

    let instance = container.singletonInstances.get(descriptor.token);
    if (!instance) {
      instance = this.instantiate<T>(descriptor);
      container.singletonInstances.set(descriptor.token, instance);
    }
    
    return instance;
  }

  private resolveScoped<T>(descriptor: ServiceDescriptor): T {
    const container = this.getScopedContainer();
    
    let instance = container.scopedInstances.get(descriptor.token);
    if (!instance) {
      instance = this.instantiate<T>(descriptor);
      container.scopedInstances.set(descriptor.token, instance);
    }
    
    return instance;
  }

  private resolveTransient<T>(descriptor: ServiceDescriptor): T {
    return this.instantiate<T>(descriptor);
  }

  private instantiate<T>(descriptor: ServiceDescriptor): T {
    if (descriptor.factory) {
      return descriptor.factory(this);
    }

    if (descriptor.implementation) {
      const dependencies = descriptor.dependencies?.map(dep => this.resolve(dep)) || [];
      return new descriptor.implementation(...dependencies);
    }

    if (descriptor.instance) {
      return descriptor.instance;
    }

    throw new Error(`Cannot instantiate service '${String(descriptor.token)}'`);
  }

  private getRootContainer(): Container {
    let container: Container = this;
    while (container.parentContainer) {
      container = container.parentContainer;
    }
    return container;
  }

  private getScopedContainer(): Container {
    return this.isScoped ? this : this;
  }

  private isDisposable(obj: any): obj is IDisposable {
    return obj && typeof obj.dispose === 'function';
  }
}

// Service tokens for type-safe dependency injection
export const TOKENS = {
  // Core services
  LOGGER: Symbol('ILogger'),
  CACHE: Symbol('ICache'),
  CONTAINER: Symbol('IContainer'),
  
  // Repository services
  USER_REPOSITORY: Symbol('IUserRepository'),
  PROPERTY_REPOSITORY: Symbol('IPropertyRepository'),
  BOOKING_REPOSITORY: Symbol('IBookingRepository'),
  REVIEW_REPOSITORY: Symbol('IReviewRepository'),
  UNIT_OF_WORK: Symbol('IUnitOfWork'),
  
  // Business services
  PROPERTY_CACHE_SERVICE: Symbol('IPropertyCacheService'),
  PERFORMANCE_MONITOR_SERVICE: Symbol('IPerformanceMonitorService'),
  
  // Configuration
  CONFIG: Symbol('IConfig'),
} as const;