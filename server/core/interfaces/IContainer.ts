/**
 * Dependency Injection Container Interface
 * Provides a standardized way to register and resolve dependencies
 */

export type ServiceLifetime = 'singleton' | 'transient' | 'scoped';

export interface ServiceDescriptor<T = any> {
  token: string | symbol;
  implementation?: new (...args: any[]) => T;
  factory?: (container: IContainer) => T;
  instance?: T;
  lifetime: ServiceLifetime;
  dependencies?: (string | symbol)[];
}

export interface IContainer {
  /**
   * Register a service with the container
   */
  register<T>(
    token: string | symbol,
    implementation: new (...args: any[]) => T,
    lifetime?: ServiceLifetime,
    dependencies?: (string | symbol)[]
  ): IContainer;

  /**
   * Register a service using a factory function
   */
  registerFactory<T>(
    token: string | symbol,
    factory: (container: IContainer) => T,
    lifetime?: ServiceLifetime
  ): IContainer;

  /**
   * Register a singleton instance
   */
  registerInstance<T>(
    token: string | symbol,
    instance: T
  ): IContainer;

  /**
   * Resolve a service from the container
   */
  resolve<T>(token: string | symbol): T;

  /**
   * Check if a service is registered
   */
  isRegistered(token: string | symbol): boolean;

  /**
   * Create a scoped container for request-specific services
   */
  createScope(): IContainer;

  /**
   * Dispose of scoped resources
   */
  dispose?(): void;
}

export interface IDisposable {
  dispose(): void | Promise<void>;
}