/**
 * Cache Service Interface
 */

export interface ICacheStats {
  size: number;
  maxSize: number;
  hitRate: number;
  memoryUsage: string;
}

export interface ICache {
  /**
   * Set a value in the cache
   */
  set<T>(key: string, value: T, ttl?: number): Promise<void>;

  /**
   * Get a value from the cache
   */
  get<T>(key: string): Promise<T | null>;

  /**
   * Delete a value from the cache
   */
  delete(key: string): Promise<boolean>;

  /**
   * Get all keys matching a pattern
   */
  keys(pattern: string): Promise<string[]>;

  /**
   * Clear all cache entries
   */
  clear(): Promise<void>;

  /**
   * Check if a key exists in the cache
   */
  has(key: string): Promise<boolean>;

  /**
   * Get cache statistics
   */
  getStats(): ICacheStats;

  /**
   * Set cache configuration
   */
  configure?(options: Record<string, any>): void;
}