/**
 * Repository Pattern Interfaces
 */

export interface IQueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  include?: string[];
}

export interface IFilterCriteria {
  [key: string]: any;
}

export interface IRepository<TEntity, TKey = number> {
  /**
   * Find an entity by its primary key
   */
  findById(id: TKey): Promise<TEntity | null>;

  /**
   * Find entities matching the given criteria
   */
  findBy(criteria: IFilterCriteria, options?: IQueryOptions): Promise<TEntity[]>;

  /**
   * Find a single entity matching the criteria
   */
  findOne(criteria: IFilterCriteria): Promise<TEntity | null>;

  /**
   * Get all entities
   */
  findAll(options?: IQueryOptions): Promise<TEntity[]>;

  /**
   * Create a new entity
   */
  create(entity: Partial<TEntity>): Promise<TEntity>;

  /**
   * Update an existing entity
   */
  update(id: TKey, updates: Partial<TEntity>): Promise<TEntity | null>;

  /**
   * Delete an entity
   */
  delete(id: TKey): Promise<boolean>;

  /**
   * Count entities matching criteria
   */
  count(criteria?: IFilterCriteria): Promise<number>;

  /**
   * Check if an entity exists
   */
  exists(criteria: IFilterCriteria): Promise<boolean>;
}

export interface IUnitOfWork {
  /**
   * Begin a new transaction
   */
  beginTransaction(): Promise<void>;

  /**
   * Commit the current transaction
   */
  commit(): Promise<void>;

  /**
   * Rollback the current transaction
   */
  rollback(): Promise<void>;

  /**
   * Execute work within a transaction
   */
  executeInTransaction<T>(work: () => Promise<T>): Promise<T>;
}