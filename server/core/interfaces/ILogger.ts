/**
 * Logger Service Interface
 */

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface ILogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string | undefined;
  metadata?: Record<string, any> | undefined;
  error?: {
    name: string;
    message: string;
    stack?: string | undefined;
  } | undefined;
}

export interface ILogger {
  info(message: string, context?: string, metadata?: Record<string, any>): void;
  warn(message: string, context?: string, metadata?: Record<string, any>): void;
  error(message: string, error?: Error, context?: string, metadata?: Record<string, any>): void;
  debug(message: string, context?: string, metadata?: Record<string, any>): void;
  
  // HTTP request logging
  httpRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    requestId?: string,
    userId?: string | number,
    userAgent?: string,
    ip?: string,
    metadata?: Record<string, any>
  ): void;

  // Performance logging
  performance(
    operation: string,
    startTime: number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void;

  // Audit logging
  audit(
    action: string,
    resource: string,
    resourceId: string | number,
    userId?: string | number,
    requestId?: string,
    metadata?: Record<string, any>
  ): void;
}