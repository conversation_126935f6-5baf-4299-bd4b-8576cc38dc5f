import { Request, Response, NextFunction } from 'express';
import { performanceMonitor } from '../services/PerformanceMonitorService';
import { logger } from '../services/LoggerService';

/**
 * Performance monitoring middleware for tracking API response times and analyzing request patterns
 */

interface ExtendedRequest extends Request {
  startTime?: number;
  operation?: string;
  cacheHit?: boolean;
}

/**
 * Middleware to track request performance metrics
 */
export function trackPerformance(operation?: string) {
  return (req: ExtendedRequest, res: Response, next: NextFunction) => {
    req.startTime = Date.now();
    req.operation = operation || `${req.method} ${req.route?.path || req.path}`;

    // Override res.json to capture completion time
    const originalJson = res.json.bind(res);
    const originalSend = res.send.bind(res);

    const recordMetric = () => {
      if (req.startTime) {
        const duration = Date.now() - req.startTime;
        const success = res.statusCode >= 200 && res.statusCode < 400;
        const cacheHit = res.get('X-Cache') === 'HIT' || res.get('X-Advanced-Cache') === 'HIT';

        performanceMonitor.recordQuery(
          req.operation!,
          duration,
          success,
          cacheHit,
          !success ? `HTTP ${res.statusCode}` : undefined
        );

        // Log slow requests
        if (duration > 1000) {
          logger.warn('Slow request detected', 'performance', {
            operation: req.operation,
            duration,
            path: req.path,
            method: req.method,
            statusCode: res.statusCode,
            cacheHit
          });
        }
      }
    };

    res.json = function(data: any) {
      recordMetric();
      return originalJson(data);
    };

    res.send = function(data: any) {
      recordMetric();
      return originalSend(data);
    };

    next();
  };
}

/**
 * Middleware to track search analytics for business intelligence
 */
export function trackSearchAnalytics() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Only track search requests
    if (req.path.includes('/properties') && req.method === 'GET') {
      const { location, minPrice, maxPrice, amenities } = req.query;

      // Determine price range
      let priceRange = 'any';
      if (minPrice || maxPrice) {
        const min = minPrice ? Number(minPrice) : 0;
        const max = maxPrice ? Number(maxPrice) : Infinity;
        
        if (max <= 2000) priceRange = 'budget';
        else if (max <= 5000) priceRange = 'mid-range';
        else priceRange = 'premium';
      }

      // Record search analytics
      performanceMonitor.recordSearch({
        location: location as string,
        priceRange,
        searchTerm: [location, amenities].filter(Boolean).join(' ')
      });
    }

    next();
  };
}

/**
 * Middleware to add performance headers to responses
 */
export function addPerformanceHeaders() {
  return (req: ExtendedRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    req.startTime = startTime;

    // Override response methods to add timing headers
    const originalJson = res.json.bind(res);
    const originalSend = res.send.bind(res);

    const addHeaders = () => {
      const duration = Date.now() - startTime;
      res.set({
        'X-Response-Time': `${duration}ms`,
        'X-Request-ID': req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        'X-API-Version': '1.0',
        'X-Server-Time': new Date().toISOString()
      });
    };

    res.json = function(data: any) {
      addHeaders();
      return originalJson(data);
    };

    res.send = function(data: any) {
      addHeaders();
      return originalSend(data);
    };

    next();
  };
}

/**
 * Middleware for request rate monitoring and alerting
 */
export function monitorRequestRate() {
  const requestCounts = new Map<string, { count: number; window: number }>();
  const windowSize = 60000; // 1 minute
  const alertThreshold = 100; // requests per minute

  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    const currentWindow = Math.floor(now / windowSize);

    const existing = requestCounts.get(clientIP);
    if (!existing || existing.window !== currentWindow) {
      requestCounts.set(clientIP, { count: 1, window: currentWindow });
    } else {
      existing.count++;
      
      // Alert on high request rate
      if (existing.count > alertThreshold) {
        logger.warn('High request rate detected', 'performance', {
          clientIP,
          requestCount: existing.count,
          threshold: alertThreshold,
          userAgent: req.headers['user-agent']
        });
      }
    }

    // Clean up old entries every 5 minutes
    if (Math.random() < 0.001) { // 0.1% chance per request
      const cutoff = currentWindow - 5; // 5 windows ago
      const entries = Array.from(requestCounts.entries());
      for (const [ip, data] of entries) {
        if (data.window < cutoff) {
          requestCounts.delete(ip);
        }
      }
    }

    next();
  };
}

/**
 * Middleware to detect and log suspicious request patterns
 */
export function detectAnomalies() {
  const requestPatterns = new Map<string, {
    paths: Set<string>;
    userAgents: Set<string>;
    lastRequest: number;
    requestCount: number;
  }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    const userAgent = req.headers['user-agent'] || 'unknown';

    const existing = requestPatterns.get(clientIP);
    if (!existing) {
      requestPatterns.set(clientIP, {
        paths: new Set([req.path]),
        userAgents: new Set([userAgent]),
        lastRequest: now,
        requestCount: 1
      });
    } else {
      existing.paths.add(req.path);
      existing.userAgents.add(userAgent);
      existing.requestCount++;
      
      const timeSinceLastRequest = now - existing.lastRequest;
      existing.lastRequest = now;

      // Detect potential bot/scraper behavior
      if (existing.requestCount > 50 && existing.paths.size > 20 && timeSinceLastRequest < 1000) {
        logger.warn('Potential bot/scraper detected', 'security', {
          clientIP,
          requestCount: existing.requestCount,
          uniquePaths: existing.paths.size,
          uniqueUserAgents: existing.userAgents.size,
          avgRequestInterval: timeSinceLastRequest
        });
      }

      // Detect rapid requests
      if (timeSinceLastRequest < 100 && existing.requestCount > 10) {
        logger.warn('Rapid requests detected', 'security', {
          clientIP,
          timeSinceLastRequest,
          requestCount: existing.requestCount
        });
      }
    }

    next();
  };
}

/**
 * Middleware to add database query monitoring
 */
export function monitorDatabaseQueries() {
  return (req: Request, res: Response, next: NextFunction) => {
    // This would integrate with your database layer to monitor query times
    // For now, we'll add it as a hook in the response
    
    const originalJson = res.json.bind(res);
    res.json = function(data: any) {
      // If this was a database-heavy operation, record additional metrics
      if (req.path.includes('/properties') || req.path.includes('/bookings')) {
        const dbOperation = `${req.method} ${req.path}`;
        // You would replace this with actual database query time measurement
        const estimatedDbTime = Math.random() * 100; // Placeholder
        
        if (estimatedDbTime > 50) {
          logger.debug('Database query time recorded', 'performance', {
            operation: dbOperation,
            estimatedTime: estimatedDbTime
          });
        }
      }
      
      return originalJson(data);
    };

    next();
  };
}

/**
 * Middleware to track API endpoint usage statistics
 */
export function trackEndpointUsage() {
  const endpointStats = new Map<string, {
    count: number;
    totalTime: number;
    errors: number;
    lastUsed: number;
  }>();

  return (req: ExtendedRequest, res: Response, next: NextFunction) => {
    const endpoint = `${req.method} ${req.route?.path || req.path}`;
    const startTime = Date.now();

    const originalJson = res.json.bind(res);
    res.json = function(data: any) {
      const duration = Date.now() - startTime;
      const isError = res.statusCode >= 400;

      const existing = endpointStats.get(endpoint);
      if (!existing) {
        endpointStats.set(endpoint, {
          count: 1,
          totalTime: duration,
          errors: isError ? 1 : 0,
          lastUsed: Date.now()
        });
      } else {
        existing.count++;
        existing.totalTime += duration;
        if (isError) existing.errors++;
        existing.lastUsed = Date.now();
      }

      // Log endpoint stats periodically
      if (Math.random() < 0.01) { // 1% chance
        const stats = Array.from(endpointStats.entries())
          .map(([ep, data]) => ({
            endpoint: ep,
            count: data.count,
            avgTime: Math.round(data.totalTime / data.count),
            errorRate: Math.round((data.errors / data.count) * 100),
            lastUsed: new Date(data.lastUsed).toISOString()
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 10);

        logger.info('Endpoint usage statistics', 'analytics', { stats });
      }

      return originalJson(data);
    };

    next();
  };
}

/**
 * Health check endpoint middleware
 */
export async function performanceHealthCheck(req: Request, res: Response, next: NextFunction): Promise<void> {
  if (req.path === '/_health/performance' && req.method === 'GET') {
    try {
      const health = await performanceMonitor.performHealthCheck();
      const summary = await performanceMonitor.getPerformanceSummary();

      res.json({
        success: true,
        health,
        summary,
        timestamp: new Date().toISOString()
      });
      return;
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Health check failed',
        error: error instanceof Error ? error.message : String(error)
      });
      return;
    }
  }

  if (req.path === '/_metrics' && req.method === 'GET') {
    try {
      const metrics = await performanceMonitor.exportMetrics();
      res.set('Content-Type', 'text/plain');
      res.send(metrics);
      return;
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Metrics export failed',
        error: error instanceof Error ? error.message : String(error)
      });
      return;
    }
  }

  next();
}