import { Request, Response, NextFunction } from "express";
import crypto from "crypto";

// CSRF Protection middleware
export function csrfProtection(options?: { cookie?: boolean }) {
  const useSecureCookie = options?.cookie !== false;
  
  return (req: Request, res: Response, next: NextFunction) => {
    // Generate a token if one doesn't exist
    if (!req.cookies._csrf) {
      const csrfToken = crypto.randomBytes(32).toString('hex');
      res.cookie('_csrf', csrfToken, {
        httpOnly: true,
        secure: useSecureCookie && process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/'
      });
      req.csrfToken = () => csrfToken;
    } else {
      req.csrfToken = () => req.cookies._csrf;
    }
    
    // Skip CSRF check for GET, HEAD, OPTIONS requests as they should be idempotent
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next();
    }
    
    // For other methods, validate the token
    const csrfFromHeader = req.headers['x-csrf-token'] || req.headers['x-xsrf-token'];
    const csrfFromBody = req.body?._csrf;
    
    // Compare token from request with token in cookie
    const csrfFromRequest = csrfFromHeader || csrfFromBody;
    if (!csrfFromRequest || csrfFromRequest !== req.cookies._csrf) {
      return res.status(403).json({
        message: 'Invalid or missing CSRF token',
      });
    }
    
    next();
  };
}