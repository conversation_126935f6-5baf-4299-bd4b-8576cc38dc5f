import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { validationSchemas } from '../../shared/validations';
import { safeValidate, formatValidationError, ValidationPresets } from '../../shared/validation-utils';

// Custom validation error for better error handling
export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Generic validation middleware factory using consolidated utilities
export function validateRequest<T>(
  schema: z.ZodSchema<T>,
  property: 'body' | 'query' | 'params' = 'body'
) {
  return (req: Request, res: Response, next: NextFunction) => {
    const data = req[property];
    const result = safeValidate(schema, data);
    
    if (result.success) {
      // Replace the original data with validated data
      (req as any)[property] = result.data;
      return next();
    } else {
      const firstError = result.errors[0];
      const validationError = new ValidationError(
        firstError.message,
        firstError.field,
        'validation_failed'
      );
      
      return res.status(400).json({
        error: 'Validation failed',
        message: validationError.message,
        field: validationError.field,
        code: validationError.code,
        details: result.errors
      });
    }
  };
}

// Specific validation middlewares for each endpoint
export const validateUserRegistration = validateRequest(
  validationSchemas.userRegistration,
  'body'
);

export const validateUserLogin = validateRequest(
  validationSchemas.userLogin,
  'body'
);

export const validateOTPRequest = validateRequest(
  validationSchemas.otpRequest,
  'body'
);

export const validateOTPVerification = validateRequest(
  validationSchemas.otpVerification,
  'body'
);

export const validatePropertyForm = validateRequest(
  validationSchemas.propertyForm,
  'body'
);

export const validateBookingForm = validateRequest(
  validationSchemas.bookingForm,
  'body'
);

export const validateReviewForm = validateRequest(
  validationSchemas.reviewForm,
  'body'
);

export const validateReviewResponse = validateRequest(
  validationSchemas.reviewResponse,
  'body'
);

export const validatePropertySearch = validateRequest(
  validationSchemas.propertySearch,
  'query'
);

export const validateContactForm = validateRequest(
  validationSchemas.contactForm,
  'body'
);

export const validateProfileUpdate = validateRequest(
  validationSchemas.profileUpdate,
  'body'
);

export const validatePasswordChange = validateRequest(
  validationSchemas.passwordChange,
  'body'
);

// Validation middleware for route parameters
// Use consolidated ID validators for all parameter validation
const idParamSchema = z.object({ id: ValidationPresets.id });

export const validatePropertyId = validateRequest(idParamSchema, 'params');
export const validateUserId = validateRequest(idParamSchema, 'params');
export const validateBookingId = validateRequest(idParamSchema, 'params');
export const validateReviewId = validateRequest(idParamSchema, 'params');

// Validation for file uploads
export const validateImageUpload = (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.files || !Array.isArray(req.files)) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'No files provided'
      });
    }

    // Transform Express files to match our validation schema
    const files = (req.files as any[]).map(file => ({
      name: file.originalname,
      size: file.size,
      type: file.mimetype
    }));

    // Use consolidated file validation
    const result = safeValidate(ValidationPresets.files, files);
    
    if (result.success) {
      return next();
    } else {
      const firstError = result.errors[0];
      return res.status(400).json({
        error: 'Validation failed',
        message: firstError.message,
        field: firstError.field,
        details: result.errors
      });
    }
  } catch (error) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'File validation error'
    });
  }
};

// Validation for pagination parameters - use consolidated pagination validator
export const validatePagination = validateRequest(ValidationPresets.pagination, 'query');

// Validation for date range queries
export const validateDateRange = validateRequest(
  z.object({
    startDate: z.string().optional().refine(date => {
      if (!date) return true;
      return !isNaN(Date.parse(date));
    }, 'Invalid start date format'),
    endDate: z.string().optional().refine(date => {
      if (!date) return true;
      return !isNaN(Date.parse(date));
    }, 'Invalid end date format')
  }).refine(data => {
    if (data.startDate && data.endDate) {
      return new Date(data.startDate) <= new Date(data.endDate);
    }
    return true;
  }, {
    message: 'End date must be after start date'
  }),
  'query'
);

// Utility function for manual validation in services
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      throw new ValidationError(
        firstError.message,
        firstError.path.join('.'),
        firstError.code
      );
    }
    throw error;
  }
}

// Safe validation that doesn't throw
export function safeValidateData<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: Array<{ field: string; message: string; code: string }>;
} {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  const errors = result.error.errors.map(error => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code
  }));
  
  return { success: false, errors };
}

// Export validation schemas for use in services
export { validationSchemas };