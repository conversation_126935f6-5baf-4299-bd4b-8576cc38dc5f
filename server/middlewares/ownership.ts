import { Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import { 
  sendAuthorizationError, 
  sendNotFoundError, 
  sendValidationError 
} from "../../shared/api-response-utils";
import { logger } from "../services/LoggerService";
import { AuthenticatedRequest, OptionalAuthRequest } from '../types/auth';

/**
 * Middleware to verify property ownership
 * Ensures only the authenticated owner can access their properties
 */
const verifyPropertyOwnershipImpl = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const propertyId = parseInt(req.params.id || req.params.propertyId);
    
    if (!propertyId || isNaN(propertyId)) {
      logger.warn('Invalid property ID provided for ownership verification', 'security', {
        userId: req.user?.userId,
        providedId: req.params.id || req.params.propertyId,
        path: req.path
      });
      return sendValidationError(res, "Invalid property ID provided");
    }

    // Fetch property with owner information
    const property = await storage.getProperty(propertyId);
    
    if (!property) {
      logger.warn('Property not found during ownership verification', 'security', {
        userId: req.user?.userId,
        propertyId,
        path: req.path
      });
      return sendNotFoundError(res, "Property not found");
    }

    // Verify ownership
    if (property.ownerId !== req.user.userId) {
      logger.error('Unauthorized property access attempt', undefined, 'security', {
        userId: req.user.userId,
        propertyId,
        actualOwnerId: property.ownerId,
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
      return sendAuthorizationError(res, "Access denied. Property not found or you don't have permission to access it.");
    }

    // Log successful ownership verification for audit
    logger.info('Property ownership verified successfully', 'security', {
      userId: req.user.userId,
      propertyId,
      action: 'ownership_verified',
      path: req.path
    });

    // Attach property to request for downstream use
    req.property = property;
    return next();
  } catch (error) {
    logger.error('Error during property ownership verification', error instanceof Error ? error : undefined, 'security', {
      userId: req.user?.userId,
      propertyId: req.params.id || req.params.propertyId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return sendAuthorizationError(res, "Unable to verify property ownership. Please try again.");
  }
};

/**
 * Middleware to verify booking access rights
 * Ensures only the property owner or booking user can access booking data
 */
const verifyBookingAccessImpl = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const bookingId = parseInt(req.params.id || req.params.bookingId);
    
    if (!bookingId || isNaN(bookingId)) {
      logger.warn('Invalid booking ID provided for access verification', 'security', {
        userId: req.user?.userId,
        providedId: req.params.id || req.params.bookingId,
        path: req.path
      });
      return sendValidationError(res, "Invalid booking ID provided");
    }

    // Fetch booking with property information
    const booking = await storage.getBooking(bookingId);
    
    if (!booking) {
      logger.warn('Booking not found during access verification', 'security', {
        userId: req.user?.userId,
        bookingId,
        path: req.path
      });
      return sendNotFoundError(res, "Booking not found");
    }

    // Get property information to check ownership
    const property = await storage.getProperty(booking.propertyId);
    if (!property) {
      return sendNotFoundError(res, "Property not found");
    }

    // Verify access rights (booking owner OR property owner)
    const isBookingOwner = booking.userId === req.user.userId;
    const isPropertyOwner = property.ownerId === req.user.userId;
    
    if (!isBookingOwner && !isPropertyOwner) {
      logger.error('Unauthorized booking access attempt', undefined, 'security', {
        userId: req.user.userId,
        bookingId,
        bookingUserId: booking.userId,
        propertyOwnerId: property.ownerId,
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
      return sendAuthorizationError(res, "Access denied. Booking not found or you don't have permission to access it.");
    }

    // Log successful access verification for audit
    logger.info('Booking access verified successfully', 'security', {
      userId: req.user.userId,
      bookingId,
      accessType: isBookingOwner ? 'booking_owner' : 'property_owner',
      action: 'booking_access_verified',
      path: req.path
    });

    // Attach booking and property to request for downstream use
    req.booking = booking;
    req.property = property;
    return next();
  } catch (error) {
    logger.error('Error during booking access verification', error instanceof Error ? error : undefined, 'security', {
      userId: req.user?.userId,
      bookingId: req.params.id || req.params.bookingId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return sendAuthorizationError(res, "Unable to verify booking access. Please try again.");
  }
};

/**
 * Middleware to verify booking ownership for management operations
 * Only property owners can manage (approve/reject/complete) bookings
 */
const verifyBookingManagementImpl = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const bookingId = parseInt(req.params.id || req.params.bookingId);
    
    if (!bookingId || isNaN(bookingId)) {
      return sendValidationError(res, "Invalid booking ID provided");
    }

    // Fetch booking with property information
    const booking = await storage.getBooking(bookingId);
    
    if (!booking) {
      return sendNotFoundError(res, "Booking not found");
    }

    // Get property information to check ownership
    const property = await storage.getProperty(booking.propertyId);
    if (!property) {
      return sendNotFoundError(res, "Property not found");
    }

    // Only property owners can manage bookings
    if (property.ownerId !== req.user.userId) {
      logger.error('Unauthorized booking management attempt', undefined, 'security', {
        userId: req.user.userId,
        bookingId,
        propertyOwnerId: property.ownerId,
        action: 'booking_management_attempt',
        path: req.path,
        method: req.method,
        requestBody: req.body,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
      return sendAuthorizationError(res, "Access denied. Only property owners can manage bookings.");
    }

    // Log successful management verification for audit
    logger.info('Booking management access verified', 'security', {
      userId: req.user.userId,
      bookingId,
      propertyId: property.id,
      action: 'booking_management_verified',
      path: req.path
    });

    req.booking = booking;
    req.property = property;
    return next();
  } catch (error) {
    logger.error('Error during booking management verification', error instanceof Error ? error : undefined, 'security', {
      userId: req.user?.userId,
      bookingId: req.params.id || req.params.bookingId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return sendAuthorizationError(res, "Unable to verify booking management access. Please try again.");
  }
};

/**
 * Middleware to enforce strict owner role requirements
 * Enhanced version with additional security checks
 */
const requireOwnerRoleImpl = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      logger.error('No user context in owner role check', undefined, 'security', {
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
      return sendAuthorizationError(res, "Authentication required");
    }

    if (req.user.role !== 'owner') {
      logger.error('Non-owner attempting to access owner endpoint', undefined, 'security', {
        userId: req.user.userId,
        userRole: req.user.role,
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
      return sendAuthorizationError(res, "Owner role required. Access denied.");
    }

    // Log successful role verification
    logger.info('Owner role verified successfully', 'security', {
      userId: req.user.userId,
      action: 'owner_role_verified',
      path: req.path
    });

    return next();
  } catch (error) {
    logger.error('Error during owner role verification', error instanceof Error ? error : undefined, 'security', {
      userId: req.user?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return sendAuthorizationError(res, "Unable to verify owner role. Please try again.");
  }
};

/**
 * Middleware to validate owner operations with comprehensive checks
 */
const validateOwnerOperationImpl = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    // Ensure user is authenticated
    if (!req.user) {
      return sendAuthorizationError(res, "Authentication required");
    }

    // Ensure user has owner role
    if (req.user.role !== 'owner') {
      logger.error('Non-owner attempting owner operation', undefined, 'security', {
        userId: req.user.userId,
        userRole: req.user.role,
        operation: req.path,
        method: req.method
      });
      return sendAuthorizationError(res, "Owner role required");
    }

    // Additional security checks for sensitive operations
    const sensitiveOperations = ['/api/properties', '/api/bookings/owner'];
    const isSensitive = sensitiveOperations.some(op => req.path.includes(op));
    
    if (isSensitive) {
      // Validate JWT token freshness (optional enhanced security)
      const tokenAge = Date.now() - (req.user as any).iat * 1000;
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
      
      if (tokenAge > maxAge) {
        logger.warn('Expired token used for sensitive operation', 'security', {
          userId: req.user.userId,
          tokenAge: tokenAge / (60 * 60 * 1000), // hours
          operation: req.path
        });
        return sendAuthorizationError(res, "Token expired. Please login again.");
      }
    }

    return next();
  } catch (error) {
    logger.error('Error during owner operation validation', error instanceof Error ? error : undefined, 'security', {
      userId: req.user?.userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendAuthorizationError(res, "Unable to validate owner operation");
  }
};

// Export wrapper functions that work with Express standard Request type
export const verifyPropertyOwnership = (req: Request, res: Response, next: NextFunction) => {
  return verifyPropertyOwnershipImpl(req as AuthenticatedRequest, res, next);
};

export const verifyBookingAccess = (req: Request, res: Response, next: NextFunction) => {
  return verifyBookingAccessImpl(req as AuthenticatedRequest, res, next);
};

export const verifyBookingManagement = (req: Request, res: Response, next: NextFunction) => {
  return verifyBookingManagementImpl(req as AuthenticatedRequest, res, next);
};

export const requireOwnerRole = (req: Request, res: Response, next: NextFunction) => {
  return requireOwnerRoleImpl(req as AuthenticatedRequest, res, next);
};

export const validateOwnerOperation = (req: Request, res: Response, next: NextFunction) => {
  return validateOwnerOperationImpl(req as AuthenticatedRequest, res, next);
};