import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { config } from "../config";
import { 
  sendAuthenticationError,
  sendAuthorizationError, 
  sendValidationError,
  sendError
} from "../../shared/api-response-utils";
import { AuthenticationError } from "./errorHandler";
import { logger } from "../services/LoggerService";
import AuditService from "../services/AuditService";
import { TokenBlacklistService } from "../services/TokenBlacklistService";
import { TokenBlacklistCircuitBreaker } from "../services/CircuitBreaker";
import { AuthenticatedRequest, OptionalAuthRequest } from '../types/auth';

/**
 * Enhanced JWT validation middleware with comprehensive security checks
 */
const enhancedJWTValidationImpl = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    // Extract token from Authorization header or cookies
    let token: string | undefined;
    
    // Primary: Check Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    
    // Fallback: Check HTTP-only cookie (more secure)
    if (!token && req.cookies?.token) {
      token = req.cookies.token;
    }

    if (!token) {
      logger.warn('No JWT token provided', 'security', {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        path: req.path,
        method: req.method
      });
      return sendAuthenticationError(res, "Authentication required");
    }

    // Verify JWT signature and decode
    let decoded: any;
    try {
      decoded = jwt.verify(token, config.jwt.secret, {
        algorithms: ['HS256']
      });
    } catch (jwtError: any) {
      logger.error('JWT verification failed', jwtError, 'security', {
        error: jwtError.message,
        tokenPrefix: token.substring(0, 10) + '...',
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        path: req.path
      });

      // Log security event for invalid tokens
      AuditService.logSecurityEvent(
        'invalid_token',
        null,
        {
          error: jwtError.message,
          tokenType: 'JWT',
          path: req.path
        },
        req
      );

      return sendAuthenticationError(res, "Invalid or expired token");
    }

    // Check if token is blacklisted using circuit breaker with fail-closed approach
    try {
      const isBlacklisted = await TokenBlacklistCircuitBreaker.execute(
        () => TokenBlacklistService.isTokenBlacklisted(token),
        {
          // CRITICAL: Fail closed - treat as blacklisted if service fails
          fallback: () => {
            logger.error('Token blacklist service unavailable - failing closed for security', undefined, 'security', {
              userId: decoded.userId,
              tokenPrefix: token.substring(0, 20) + '...',
              ip: req.ip,
              securityDecision: 'FAIL_CLOSED'
            });

            // Log security event for service failure
            AuditService.logSecurityEvent(
              'unauthorized_access',
              decoded.userId,
              {
                service: 'token_blacklist',
                action: 'fail_closed',
                reason: 'Service unavailable during security check',
                path: req.path
              },
              req
            );

            return true; // Fail closed - treat as blacklisted
          },
          timeout: 2000 // 2 second timeout for security operations
        }
      );

      if (isBlacklisted) {
        logger.warn('Blacklisted token usage attempt', 'security', {
          userId: decoded.userId,
          tokenPrefix: token.substring(0, 20) + '...',
          ip: req.ip
        });

        AuditService.logSecurityEvent(
          'invalid_token',
          decoded.userId,
          {
            error: 'Token has been revoked',
            path: req.path
          },
          req
        );

        return sendAuthenticationError(res, "Token has been revoked");
      }
    } catch (error) {
      // This should rarely happen due to circuit breaker fallback
      // But if it does, we still fail closed for security
      logger.error('Critical failure in token blacklist check - failing closed', error instanceof Error ? error : undefined, 'security', {
        userId: decoded.userId,
        tokenPrefix: token.substring(0, 20) + '...',
        error: error instanceof Error ? error.message : 'Unknown error',
        securityDecision: 'FAIL_CLOSED_CRITICAL'
      });

      AuditService.logSecurityEvent(
        'unauthorized_access',
        decoded.userId,
        {
          error: 'Token blacklist check critical failure',
          action: 'fail_closed',
          details: error instanceof Error ? error.message : 'Unknown error',
          path: req.path
        },
        req
      );

      return sendAuthenticationError(res, "Authentication system temporarily unavailable");
    }

    // Validate token structure
    if (!decoded.userId || !decoded.role) {
      logger.error('JWT token missing required claims', undefined, 'security', {
        hasUserId: !!decoded.userId,
        hasRole: !!decoded.role,
        claims: Object.keys(decoded),
        ip: req.ip
      });

      AuditService.logSecurityEvent(
        'invalid_token',
        decoded.userId || null,
        {
          error: 'Missing required claims',
          missingClaims: [
            !decoded.userId && 'userId',
            !decoded.role && 'role'
          ].filter(Boolean),
          path: req.path
        },
        req
      );

      return sendAuthenticationError(res, "Invalid token format");
    }

    // Enhanced security checks
    const now = Math.floor(Date.now() / 1000);
    
    // Check token expiration with buffer
    if (decoded.exp && decoded.exp < now) {
      logger.warn('Expired JWT token used', 'security', {
        userId: decoded.userId,
        expiredAt: new Date(decoded.exp * 1000).toISOString(),
        timeDiff: now - decoded.exp,
        ip: req.ip
      });

      AuditService.logSecurityEvent(
        'invalid_token',
        decoded.userId,
        {
          error: 'Token expired',
          expiredAt: new Date(decoded.exp * 1000).toISOString(),
          path: req.path
        },
        req
      );

      return sendAuthenticationError(res, "Token expired. Please login again.");
    }

    // Check if token is too fresh (potential replay attack)
    if (decoded.iat && decoded.iat > now + 60) {
      logger.error('Future-dated JWT token detected', undefined, 'security', {
        userId: decoded.userId,
        issuedAt: new Date(decoded.iat * 1000).toISOString(),
        currentTime: new Date().toISOString(),
        timeDiff: decoded.iat - now,
        ip: req.ip
      });

      AuditService.logSecurityEvent(
        'invalid_token',
        decoded.userId,
        {
          error: 'Future-dated token',
          issuedAt: new Date(decoded.iat * 1000).toISOString(),
          suspectedAttack: 'token_manipulation',
          path: req.path
        },
        req
      );

      return sendAuthenticationError(res, "Invalid token timestamp");
    }

    // Validate role format
    const validRoles = ['user', 'owner', 'admin'];
    if (!validRoles.includes(decoded.role)) {
      logger.error('Invalid role in JWT token', undefined, 'security', {
        userId: decoded.userId,
        invalidRole: decoded.role,
        validRoles,
        ip: req.ip
      });

      AuditService.logSecurityEvent(
        'role_mismatch',
        decoded.userId,
        {
          providedRole: decoded.role,
          validRoles,
          suspectedAttack: 'privilege_escalation',
          path: req.path
        },
        req
      );

      return sendAuthorizationError(res, "Invalid user role");
    }

    // Rate limiting based on token age (older tokens have stricter limits)
    if (decoded.iat) {
      const tokenAge = now - decoded.iat;
      const maxAge = 7 * 24 * 60 * 60; // 7 days in seconds

      if (tokenAge > maxAge) {
        logger.warn('Very old JWT token used', 'security', {
          userId: decoded.userId,
          tokenAgeHours: Math.round(tokenAge / 3600),
          maxAgeHours: maxAge / 3600,
          ip: req.ip
        });

        AuditService.logSecurityEvent(
          'invalid_token',
          decoded.userId,
          {
            error: 'Token too old',
            tokenAgeHours: Math.round(tokenAge / 3600),
            recommendation: 'Force re-authentication',
            path: req.path
          },
          req
        );

        return sendAuthorizationError(res, "Token too old. Please login again for security.");
      }
    }

    // Enhanced validation for critical operations
    if (isCriticalOperation(req.path, req.method)) {
      // Require fresher tokens for critical operations
      if (decoded.iat) {
        const tokenAge = now - decoded.iat;
        const maxCriticalAge = 2 * 60 * 60; // 2 hours for critical operations

        if (tokenAge > maxCriticalAge) {
          logger.warn('Old token used for critical operation', 'security', {
            userId: decoded.userId,
            role: decoded.role,
            operation: `${req.method} ${req.path}`,
            tokenAgeHours: Math.round(tokenAge / 3600),
            ip: req.ip
          });

          return sendAuthorizationError(res, "Please re-authenticate for this sensitive operation");
        }
      }

      // Additional logging for critical operations
      logger.info('Critical operation authenticated', 'security', {
        userId: decoded.userId,
        role: decoded.role,
        operation: `${req.method} ${req.path}`,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    }

    // Attach user info to request
    req.user = {
      userId: decoded.userId,
      role: decoded.role,
      iat: decoded.iat,
      exp: decoded.exp
    };

    // Log successful authentication
    logger.debug('JWT validation successful', 'auth', {
      userId: decoded.userId,
      role: decoded.role,
      path: req.path,
      method: req.method
    });

    return next();
  } catch (error) {
    logger.error('Unexpected error during JWT validation', error instanceof Error ? error : undefined, 'security', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      ip: req.ip,
      path: req.path
    });

    AuditService.logSecurityEvent(
      'invalid_token',
      null,
      {
        error: 'JWT validation system error',
        details: error instanceof Error ? error.message : 'Unknown error',
        path: req.path
      },
      req
    );

    return sendAuthenticationError(res, "Authentication system error");
  }
};

/**
 * Check if the current operation is considered critical
 */
function isCriticalOperation(path: string, method: string): boolean {
  const criticalPatterns = [
    { path: /\/properties\/\d+$/, method: 'DELETE' }, // Delete property
    { path: /\/bookings\/\d+\/status$/, method: 'PATCH' }, // Update booking status
    { path: /\/properties\/\d+\/pricing$/, method: 'PUT' }, // Update pricing
    { path: /\/admin\//, method: 'ANY' }, // Any admin operation
    { path: /\/owner\/.*\/delete/, method: 'ANY' }, // Any delete operation
  ];

  return criticalPatterns.some(pattern => {
    const pathMatches = pattern.path.test(path);
    const methodMatches = pattern.method === 'ANY' || pattern.method === method;
    return pathMatches && methodMatches;
  });
}

// Export wrapper functions that work with Express standard Request type
export const enhancedJWTValidation = (req: Request, res: Response, next: NextFunction) => {
  return enhancedJWTValidationImpl(req as AuthenticatedRequest, res, next);
};

/**
 * Middleware specifically for owner operations with enhanced validation
 */
const ownerJWTValidationImpl = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  // First run standard JWT validation
  await enhancedJWTValidation(req, res, (error?: any) => {
    if (error) return;

    // Additional owner-specific validations
    if (!req.user || req.user.role !== 'owner') {
      logger.error('Non-owner attempting owner operation', undefined, 'security', {
        userId: req.user?.userId,
        userRole: req.user?.role,
        operation: `${req.method} ${req.path}`,
        ip: req.ip
      });

      AuditService.logSecurityEvent(
        'role_mismatch',
        req.user?.userId || null,
        {
          requiredRole: 'owner',
          providedRole: req.user?.role || 'none',
          operation: `${req.method} ${req.path}`,
          suspectedAttack: 'privilege_escalation'
        },
        req
      );

      return sendAuthorizationError(res, "Owner privileges required");
    }

    // Log owner operation access
    AuditService.logAuthAction(
      req.user.userId,
      'token_refresh',
      {
        userRole: req.user.role,
        operation: `${req.method} ${req.path}`,
        tokenAge: req.user.iat ? Math.floor(Date.now() / 1000) - req.user.iat : undefined
      },
      req,
      true
    );

    return next();
  });
};

export const ownerJWTValidation = (req: Request, res: Response, next: NextFunction) => {
  return ownerJWTValidationImpl(req as AuthenticatedRequest, res, next);
};

/**
 * Enhanced token blacklist checking (requires Redis or similar)
 */
export const checkTokenBlacklist = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    // Extract token from request
    let token: string | undefined;
    
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else if (req.cookies?.token) {
      token = req.cookies.token;
    }

    if (!token) {
      return next(); // Let other middleware handle missing token
    }

    // Check if token is blacklisted using circuit breaker with fail-closed approach
    try {
      const isBlacklisted = await TokenBlacklistCircuitBreaker.execute(
        () => TokenBlacklistService.isTokenBlacklisted(token),
        {
          // CRITICAL: Fail closed - treat as blacklisted if service fails
          fallback: () => {
            logger.error('Token blacklist service unavailable in secondary check - failing closed', undefined, 'security', {
              userId: req.user?.userId,
              tokenPrefix: token.substring(0, 10) + '...',
              ip: req.ip,
              path: req.path,
              securityDecision: 'FAIL_CLOSED'
            });

            return true; // Fail closed - treat as blacklisted
          },
          timeout: 2000
        }
      );

      if (isBlacklisted) {
        logger.warn('Blacklisted token usage attempted', 'security', {
          userId: req.user?.userId,
          tokenPrefix: token.substring(0, 10) + '...',
          ip: req.ip,
          path: req.path
        });

        AuditService.logSecurityEvent(
          'invalid_token',
          req.user?.userId || null,
          {
            error: 'Blacklisted token',
            reason: 'Token revoked or user logged out',
            path: req.path
          },
          req
        );

        return sendAuthenticationError(res, "Token has been revoked. Please login again.");
      }

      return next();
    } catch (blacklistError) {
      // Handle blacklist check errors - fail closed for security
      logger.error('Error checking token blacklist in secondary check - failing closed', blacklistError instanceof Error ? blacklistError : undefined, 'security', {
        userId: req.user?.userId,
        securityDecision: 'FAIL_CLOSED_ERROR'
      });

      return sendAuthenticationError(res, "Authentication verification failed");
    }
  } catch (error) {
      // Critical failure - fail closed for security
      logger.error('Critical error in secondary token blacklist check - failing closed', error instanceof Error ? error : undefined, 'security', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: req.user?.userId,
        securityDecision: 'FAIL_CLOSED_CRITICAL'
      });

      AuditService.logSecurityEvent(
        'unauthorized_access',
        req.user?.userId || null,
        {
          error: 'Secondary token blacklist check critical failure',
          action: 'fail_closed',
          details: error instanceof Error ? error.message : 'Unknown error',
          path: req.path
        },
        req
      );
      
      return sendAuthenticationError(res, "Authentication system temporarily unavailable");
    }
};

export default {
  enhancedJWTValidation,
  ownerJWTValidation,
  checkTokenBlacklist
};