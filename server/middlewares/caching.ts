import { Request, Response, NextFunction } from 'express';
import { propertyCacheService } from '../services/PropertyCacheService';
import { logger } from '../services/LoggerService';

/**
 * Advanced caching middleware for property APIs
 * Provides intelligent caching with automatic invalidation and performance monitoring
 */

interface CacheOptions {
  enabled?: boolean;
  keyGenerator?: (req: Request) => string;
  ttl?: number;
  skipCache?: (req: Request) => boolean;
  onHit?: (req: Request, data: any) => void;
  onMiss?: (req: Request) => void;
}

/**
 * Generic caching middleware factory
 */
export function createCacheMiddleware(options: CacheOptions = {}) {
  const {
    enabled = true,
    keyGenerator = (req: Request) => `${req.method}:${req.originalUrl}`,
    ttl,
    skipCache = () => false,
    onHit,
    onMiss
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching if disabled or conditions not met
    if (!enabled || skipCache(req)) {
      return next();
    }

    const cacheKey = keyGenerator(req);
    
    try {
      // Try to get from cache
      const cached = await propertyCacheService.getCachedPropertySearch(req.query as any);
      
      if (cached) {
        if (onHit) onHit(req, cached);
        
        // Set cache headers
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey.substring(0, 50),
          'Cache-Control': `public, max-age=${ttl || 300}`
        });
        
        return res.json(cached);
      }

      // Cache miss - continue with the request
      if (onMiss) onMiss(req);
      
      // Override res.json to cache the response
      const originalJson = res.json.bind(res);
      res.json = function(data: any) {
        // Cache the response asynchronously
        if (res.statusCode === 200 && data) {
          setImmediate(async () => {
            try {
              await propertyCacheService.cachePropertySearch(req.query as any, data);
            } catch (error) {
              logger.error('Failed to cache response', error instanceof Error ? error : new Error(String(error)), 'cache', { 
                key: cacheKey 
              });
            }
          });
        }
        
        // Set cache headers
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey.substring(0, 50),
          'Cache-Control': `public, max-age=${ttl || 300}`
        });
        
        return originalJson(data);
      };

    } catch (error) {
      logger.error('Cache middleware error', error instanceof Error ? error : new Error(String(error)), 'cache', { 
        key: cacheKey 
      });
    }

    next();
  };
}

/**
 * Property search caching middleware
 */
export const cachePropertySearch = createCacheMiddleware({
  enabled: true,
  keyGenerator: (req: Request) => {
    const params = {
      location: req.query.location as string,
      date: req.query.date as string,
      minPrice: req.query.minPrice ? Number(req.query.minPrice) : undefined,
      maxPrice: req.query.maxPrice ? Number(req.query.maxPrice) : undefined,
      amenities: req.query.amenities ? String(req.query.amenities).split(',') : undefined,
      featured: req.query.featured === 'true',
      limit: req.query.limit ? Number(req.query.limit) : undefined,
      offset: req.query.offset ? Number(req.query.offset) : undefined
    };
    return `property_search:${JSON.stringify(params)}`;
  },
  ttl: 600, // 10 minutes
  skipCache: (req: Request) => {
    // Skip cache for authenticated user-specific requests
    return req.headers.authorization !== undefined || req.cookies.auth_token !== undefined;
  }
});

/**
 * Property details caching middleware
 */
export const cachePropertyDetails = createCacheMiddleware({
  enabled: true,
  keyGenerator: (req: Request) => `property_details:${req.params.id}`,
  ttl: 1800, // 30 minutes
  skipCache: (req: Request) => {
    // Never skip property details caching
    return false;
  }
});

/**
 * Featured properties caching middleware
 */
export const cacheFeaturedProperties = createCacheMiddleware({
  enabled: true,
  keyGenerator: () => 'featured_properties:list',
  ttl: 3600, // 1 hour
  skipCache: () => false
});

/**
 * Cache invalidation middleware - to be called after property updates
 */
export function createCacheInvalidationMiddleware(
  invalidationType: 'property' | 'owner' | 'all' = 'property'
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original json method
    const originalJson = res.json.bind(res);
    
    res.json = function(data: any) {
      // Only invalidate on successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        setImmediate(async () => {
          try {
            switch (invalidationType) {
              case 'property':
                const propertyId = req.params.id ? Number(req.params.id) : undefined;
                if (propertyId) {
                  await propertyCacheService.invalidatePropertyCache(
                    propertyId, 
                    req.method === 'DELETE' ? 'delete' : 'update'
                  );
                }
                break;
              
              case 'owner':
                const ownerId = req.user?.userId;
                if (ownerId) {
                  await propertyCacheService.invalidateOwnerCache(ownerId);
                }
                break;
              
              case 'all':
                await propertyCacheService.invalidateAllPropertyCache();
                break;
            }
            
            logger.info('Cache invalidated after operation', 'cache', {
              type: invalidationType,
              method: req.method,
              path: req.path
            });
          } catch (error) {
            logger.error('Cache invalidation failed', error instanceof Error ? error : new Error(String(error)), 'cache', { 
              type: invalidationType 
            });
          }
        });
      }
      
      return originalJson(data);
    };
    
    next();
  };
}

/**
 * Cache warming middleware - preloads frequently accessed data
 */
export async function cacheWarmupMiddleware(
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> {
  // Only warm up cache on application start or specific triggers
  if (req.path === '/_cache/warmup' && req.method === 'POST') {
    try {
      await propertyCacheService.warmupCache();
      
      res.json({
        success: true,
        message: 'Cache warmup completed',
        timestamp: new Date().toISOString()
      });
      return;
    } catch (error) {
      logger.error('Cache warmup failed', error instanceof Error ? error : new Error(String(error)), 'cache');
      
      res.status(500).json({
        success: false,
        message: 'Cache warmup failed',
        error: error instanceof Error ? error.message : String(error)
      });
      return;
    }
  }
  
  next();
}

/**
 * Cache metrics and health check middleware
 */
export async function cacheHealthMiddleware(
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> {
  if (req.path === '/_cache/health' && req.method === 'GET') {
    try {
      const health = await propertyCacheService.healthCheck();
      
      res.json({
        success: true,
        cache: health,
        timestamp: new Date().toISOString()
      });
      return;
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Cache health check failed',
        error: error instanceof Error ? error.message : String(error)
      });
      return;
    }
  }
  
  if (req.path === '/_cache/metrics' && req.method === 'GET') {
    try {
      const metrics = propertyCacheService.getCacheMetrics();
      
      res.json({
        success: true,
        metrics,
        timestamp: new Date().toISOString()
      });
      return;
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Cache metrics retrieval failed',
        error: error instanceof Error ? error.message : String(error)
      });
      return;
    }
  }
  
  next();
}

/**
 * ETags and conditional caching middleware
 */
export function etagCaching(options: { weak?: boolean } = {}) {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalJson = res.json.bind(res);
    
    res.json = function(data: any) {
      if (res.statusCode === 200 && data) {
        // Generate ETag based on content
        const content = JSON.stringify(data);
        const etag = options.weak ? 
          `W/"${Buffer.from(content).toString('base64').substring(0, 16)}"` :
          `"${Buffer.from(content).toString('base64').substring(0, 16)}"`;
        
        res.set('ETag', etag);
        
        // Check if client has current version
        const clientETag = req.headers['if-none-match'];
        if (clientETag === etag) {
          return res.status(304).send();
        }
      }
      
      return originalJson(data);
    };
    
    next();
  };
}

/**
 * Response compression and caching headers
 */
export function responseCaching(maxAge: number = 300) {
  return (req: Request, res: Response, next: NextFunction) => {
    // Set appropriate cache headers
    res.set({
      'Cache-Control': `public, max-age=${maxAge}`,
      'Vary': 'Accept-Encoding, User-Agent',
      'X-Content-Type-Options': 'nosniff'
    });
    
    next();
  };
}

/**
 * Request deduplication middleware
 * Prevents multiple identical requests from hitting the database simultaneously
 */
const ongoingRequests = new Map<string, Promise<any>>();

export function requestDeduplication() {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const requestKey = `${req.method}:${req.originalUrl}:${JSON.stringify(req.query)}`;
    
    // Check if identical request is already in progress
    if (ongoingRequests.has(requestKey)) {
      try {
        const result = await ongoingRequests.get(requestKey);
        res.set('X-Request-Deduplicated', 'true');
        res.json(result);
        return;
      } catch (error) {
        // If the ongoing request failed, continue with this one
        ongoingRequests.delete(requestKey);
      }
    }
    
    // Override res.json to store the result for deduplication
    const originalJson = res.json.bind(res);
    let requestPromise: Promise<any>;
    
    res.json = function(data: any) {
      // Clean up the ongoing request
      ongoingRequests.delete(requestKey);
      
      return originalJson(data);
    };
    
    // Store this request as ongoing
    requestPromise = new Promise((resolve, reject) => {
      const originalEnd = res.end.bind(res);
      res.end = function(chunk?: any) {
        if (res.statusCode === 200 && chunk) {
          try {
            const data = JSON.parse(chunk);
            resolve(data);
          } catch (e) {
            resolve(chunk);
          }
        } else if (res.statusCode === 304) {
          // 304 Not Modified is not an error - resolve with null to indicate cache hit
          resolve(null);
        } else {
          reject(new Error(`Request failed with status ${res.statusCode}`));
        }
        return originalEnd(chunk);
      };
    });
    
    ongoingRequests.set(requestKey, requestPromise);
    
    // Clean up after 30 seconds to prevent memory leaks
    setTimeout(() => {
      ongoingRequests.delete(requestKey);
    }, 30000);
    
    next();
  };
}