import { Request, Response, NextFunction } from 'express';
import { resetDatabaseConnection } from '../db';
import { databaseManager } from '../utils/database';
import { logger } from '../services/LoggerService';

/**
 * Middleware to handle database connection errors gracefully
 * This middleware should be used for routes that perform database operations
 */
export function databaseErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Check if this is a database connection error
  const isDatabaseError = 
    error.message?.includes('terminating connection') ||
    error.message?.includes('connection terminated') ||
    error.message?.includes('ECONNRESET') ||
    error.message?.includes('ENOTFOUND') ||
    error.message?.includes('connection closed') ||
    error.message?.includes('Client has encountered a connection error') ||
    (error as any).code === 'ECONNRESET' ||
    (error as any).code === '57P01'; // PostgreSQL connection termination code

  if (isDatabaseError) {
    logger.error('Database connection error detected in request', error, 'database', {
      code: (error as any).code || 'unknown',
      requestId: req.headers['x-request-id'] || 'unknown',
      path: req.path,
      method: req.method
    });

    // Attempt to reset the database connection in the background
    resetDatabaseConnection()
      .then(() => {
        logger.info('Database connection reset successfully', 'database');
      })
      .catch(resetError => {
        logger.error('Failed to reset database connection', resetError instanceof Error ? resetError : new Error(String(resetError)), 'database');
      });

    // Return a user-friendly error response
    res.status(503).json({
      success: false,
      error: 'Database temporarily unavailable',
      message: 'The service is experiencing database connectivity issues. Please try again in a moment.',
      code: 'DATABASE_CONNECTION_ERROR',
      requestId: req.headers['x-request-id']
    });
    return;
  }

  // If it's not a database error, pass it to the next error handler
  next(error);
}

/**
 * Middleware to test database connection before handling requests
 * Use this for critical database operations that need immediate failure detection
 */
export function requireDatabaseConnection(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  databaseManager.testConnection()
    .then(success => {
      if (success) {
        next();
      } else {
        logger.warn('Database connection test failed for request', 'database', {
          requestId: req.headers['x-request-id'] || 'unknown',
          path: req.path,
          method: req.method
        });

        res.status(503).json({
          success: false,
          error: 'Database unavailable',
          message: 'The database is currently unavailable. Please try again later.',
          code: 'DATABASE_UNAVAILABLE',
          requestId: req.headers['x-request-id']
        });
      }
    })
    .catch(error => {
      logger.error('Database connection test error', error, 'database', {
        requestId: req.headers['x-request-id'] || 'unknown',
        path: req.path,
        method: req.method
      });

      res.status(503).json({
        success: false,
        error: 'Database connection test failed',
        message: 'Unable to verify database connectivity. Please try again later.',
        code: 'DATABASE_TEST_FAILED',
        requestId: req.headers['x-request-id']
      });
    });
}

/**
 * Wrapper function to automatically handle database errors in async route handlers
 */
export function withDatabaseErrorHandling<T extends any[]>(
  handler: (...args: T) => Promise<any>
) {
  return async (...args: T): Promise<any> => {
    try {
      return await handler(...args);
    } catch (error) {
      // If this is a database error, attempt connection reset
      if (error instanceof Error) {
        const isDatabaseError = 
          error.message?.includes('terminating connection') ||
          error.message?.includes('connection terminated') ||
          error.message?.includes('ECONNRESET') ||
          error.message?.includes('connection closed') ||
          (error as any).code === 'ECONNRESET' ||
          (error as any).code === '57P01';

        if (isDatabaseError) {
          logger.error('Database error in handler, attempting reset', error, 'database', {
            code: (error as any).code || 'unknown'
          });

          // Reset connection in background
          resetDatabaseConnection().catch(resetError => {
            logger.error('Failed to reset connection in error handler', resetError, 'database');
          });
        }
      }
      
      // Re-throw the error to be handled by the calling code
      throw error;
    }
  };
}