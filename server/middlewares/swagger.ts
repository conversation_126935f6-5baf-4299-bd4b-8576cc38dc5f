import { Router } from 'express';
import path from 'path';
import fs from 'fs';
import { sendSuccess } from './errorHandler';

const router = Router();

// Serve OpenAPI specification
router.get('/openapi.yaml', (req, res) => {
  const yamlPath = path.join(process.cwd(), 'docs', 'api', 'openapi.yaml');
  
  if (!fs.existsSync(yamlPath)) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: 'OpenAPI specification not found'
      }
    });
  }

  res.setHeader('Content-Type', 'application/x-yaml');
  return res.sendFile(yamlPath);
});

// Serve OpenAPI specification as JSON
router.get('/openapi.json', (req, res) => {
  const yamlPath = path.join(process.cwd(), 'docs', 'api', 'openapi.yaml');
  
  if (!fs.existsSync(yamlPath)) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: 'OpenAPI specification not found'
      }
    });
  }

  try {
    // For now, just point to the YAML file
    // In a production setup, you'd want to use js-yaml to convert
    return sendSuccess(res, {
      message: 'OpenAPI specification available',
      yamlUrl: '/api/docs/openapi.yaml',
      swaggerUI: '/api/docs'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to process OpenAPI specification'
      }
    });
  }
});

// Serve simple API documentation HTML
router.get('/', (req, res) => {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farmhouse Rental API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        body { margin: 0; padding: 0; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            SwaggerUIBundle({
                url: '/api/docs/openapi.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                layout: "StandaloneLayout",
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    // Add any custom headers here if needed
                    return request;
                },
                responseInterceptor: function(response) {
                    // Handle responses if needed
                    return response;
                }
            });
        };
    </script>
</body>
</html>`;

  res.setHeader('Content-Type', 'text/html');
  res.send(html);
});

export default router;