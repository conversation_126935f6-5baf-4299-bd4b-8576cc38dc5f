import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import { config } from '../config';

/**
 * Enhanced Content Security Policy configuration
 */
export const contentSecurityPolicy = (req: Request, res: Response, next: NextFunction) => {
  const csp = helmet.contentSecurityPolicy({
    directives: {
      defaultSrc: ["'self'"],
      
      // Scripts: Allow self, inline scripts for React, and specific CDNs
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Required for React development
        "'unsafe-eval'", // Required for React development (remove in production)
        "https://unpkg.com", // For any CDN libraries
        "https://cdn.jsdelivr.net",
        ...(config.isDevelopment() ? ["'unsafe-eval'"] : [])
      ],
    
    // Stylesheets: Allow self, inline styles, and Google Fonts
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Required for styled-components and CSS-in-JS
      "https://fonts.googleapis.com",
      "https://cdn.jsdelivr.net"
    ],
    
    // Images: Allow self, data URLs, and Cloudinary
    imgSrc: [
      "'self'",
      "data:",
      "blob:",
      "https://res.cloudinary.com", // Cloudinary images
      "https://images.unsplash.com", // If using Unsplash
      "https://source.unsplash.com"
    ],
    
    // Fonts: Allow self and Google Fonts
    fontSrc: [
      "'self'",
      "https://fonts.gstatic.com",
      "data:"
    ],
    
    // Connect: Allow self and API endpoints
    connectSrc: [
      "'self'",
      "https://api.cloudinary.com", // Cloudinary API
      "wss://localhost:*", // WebSocket for development
      ...(config.isDevelopment() ? ["ws://localhost:*", "http://localhost:*"] : [])
    ],
    
    // Media: Allow self and trusted sources
    mediaSrc: [
      "'self'",
      "https://res.cloudinary.com",
      "blob:",
      "data:"
    ],
    
    // Objects: Restrict to prevent XSS
    objectSrc: ["'none'"],
    
    // Base URI: Restrict to prevent injection
    baseUri: ["'self'"],
    
    // Forms: Allow self only
    formAction: ["'self'"],
    
    // Frames: Allow self (for any embedded content)
    frameSrc: ["'self'"],
    
    // Manifest: Allow self
    manifestSrc: ["'self'"],
    
    // Workers: Allow self
    workerSrc: ["'self'", "blob:"],
    
    // Upgrade insecure requests in production
    ...(config.app.nodeEnv === 'production' ? { upgradeInsecureRequests: [] } : {})
  },
  
  // Report violations in development
  reportOnly: config.isDevelopment(),
  
  // Custom report URI if needed
  ...(config.app.nodeEnv === 'production' ? {
    reportUri: '/api/security/csp-report'
  } : {})
  });

  return csp(req, res, next);
};

/**
 * Security headers configuration
 */
export const securityHeaders = helmet({
  // Content Security Policy (configured above)
  contentSecurityPolicy: false, // We'll use our custom CSP
  
  // Cross-Origin Embedder Policy
  crossOriginEmbedderPolicy: false, // May interfere with some third-party services
  
  // DNS Prefetch Control
  dnsPrefetchControl: {
    allow: false
  },
  
  // Frameguard (X-Frame-Options)
  frameguard: {
    action: 'deny' // Prevent clickjacking
  },
  
  // Hide Powered-By header
  hidePoweredBy: true,
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  
  // IE No Open
  ieNoOpen: true,
  
  // Don't sniff MIME types
  noSniff: true,
  
  // Origin Agent Cluster
  originAgentCluster: true,
  
  // Permissions Policy (commented out as it may not be supported in current helmet version)
  // permissionsPolicy: {
  //   camera: [],
  //   microphone: [],
  //   geolocation: ["'self'"], // Allow geolocation for property location features
  //   fullscreen: ["'self'"],
  //   payment: [], // Disable payment API
  //   usb: [],
  //   bluetooth: [],
  //   magnetometer: [],
  //   gyroscope: [],
  //   accelerometer: []
  // },
  
  // Referrer Policy
  referrerPolicy: {
    policy: ['no-referrer-when-downgrade']
  },
  
  // X-Content-Type-Options
  xssFilter: true
});

/**
 * CSP violation reporting endpoint
 */
export const cspReportHandler = (req: Request, res: Response, next: NextFunction) => {
  if (req.path === '/api/security/csp-report') {
    // Parse CSP violation report
    const report = req.body;
    
    // Log CSP violations (in production, you might want to send to monitoring service)
    console.warn('🛡️  CSP Violation Report:', {
      timestamp: new Date().toISOString(),
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      report: report
    });
    
    // Respond with 204 No Content
    res.status(204).end();
    return;
  }
  
  next();
};

/**
 * Rate limiting for security endpoints
 */
export const securityRateLimit = {
  // CSP report endpoint rate limit
  cspReports: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Allow up to 100 CSP reports per IP per window
    message: 'Too many CSP reports from this IP',
    standardHeaders: true,
    legacyHeaders: false
  }
};

/**
 * Additional security middleware for sensitive operations
 */
export const enhancedSecurity = (req: Request, res: Response, next: NextFunction) => {
  // Add additional security headers for sensitive operations
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('Surrogate-Control', 'no-store');
  
  // Add custom security headers
  res.setHeader('X-Security-Enhanced', 'true');
  res.setHeader('X-Request-ID', req.headers['x-request-id'] || 'unknown');
  
  next();
};

/**
 * Trusted domains configuration
 */
export const trustedDomains = {
  development: [
    'localhost',
    '127.0.0.1',
    '0.0.0.0'
  ],
  production: [
    // Add your production domain(s) here
    'farmhouse-rental.com',
    'www.farmhouse-rental.com'
  ]
};

/**
 * Get allowed origins for CORS
 */
export const getAllowedOrigins = (): string[] => {
  const domains = config.isDevelopment() ? trustedDomains.development : trustedDomains.production;
  const protocols = config.isDevelopment() ? ['http', 'https'] : ['https'];
  const ports = config.isDevelopment() ? ['3000', '3001', '5000', '5001'] : ['80', '443'];
  
  const origins: string[] = [];
  
  domains.forEach(domain => {
    protocols.forEach(protocol => {
      if (config.isDevelopment()) {
        ports.forEach(port => {
          origins.push(`${protocol}://${domain}:${port}`);
        });
      } else {
        origins.push(`${protocol}://${domain}`);
      }
    });
  });
  
  return origins;
};