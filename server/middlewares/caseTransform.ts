/**
 * Case Transform Middleware
 * 
 * Automatically transforms request/response data between camelCase (API) and snake_case (database)
 * to maintain consistent naming conventions across the application layers.
 */

import { Request, Response, NextFunction } from 'express';
import {
  transformApiRequest,
  transformDatabaseResult,
  toCamelCase,
  toSnakeCase,
  debugTransformation
} from '../../shared/case-transformer';

/**
 * Middleware to transform incoming request data from camelCase to snake_case
 * Apply this to routes that need to interact with the database
 */
export const transformIncomingData = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Transform request body (POST/PUT/PATCH data)
    if (req.body && typeof req.body === 'object' && Object.keys(req.body).length > 0) {
      const originalBody = { ...req.body };
      req.body = transformApiRequest(req.body);
      
      // Debug in development
      if (process.env.NODE_ENV === 'development') {
        debugTransformation(originalBody, req.body, 'Request Body Transform');
      }
    }
    
    // Transform query parameters (GET requests)
    if (req.query && typeof req.query === 'object' && Object.keys(req.query).length > 0) {
      const originalQuery = { ...req.query };
      req.query = transformApiRequest(req.query);
      
      // Debug in development
      if (process.env.NODE_ENV === 'development') {
        debugTransformation(originalQuery, req.query, 'Query Params Transform');
      }
    }
    
    next();
  } catch (error) {
    console.error('Error in transformIncomingData middleware:', error);
    next(); // Continue anyway to avoid breaking the request
  }
};

/**
 * Middleware to transform outgoing response data from snake_case to camelCase
 * Apply this to routes that return database data to the frontend
 */
export const transformOutgoingData = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Store the original json method
    const originalJson = res.json;
    
    // Override the json method to transform data before sending
    res.json = function(data: any) {
      if (data && typeof data === 'object') {
        const originalData = JSON.parse(JSON.stringify(data)); // Deep clone for debugging
        
        // Handle standard API response format
        if (data.success !== undefined && data.hasOwnProperty('data')) {
          // Transform the data portion while preserving response structure
          if (data.data !== null && data.data !== undefined) {
            data.data = transformDatabaseResult(data.data);
          }
        } else {
          // Handle direct data responses (less common)
          data = transformDatabaseResult(data);
        }
        
        // Debug in development
        if (process.env.NODE_ENV === 'development') {
          debugTransformation(originalData, data, 'Response Data Transform');
        }
      }
      
      // Call the original json method with transformed data
      return originalJson.call(this, data);
    };
    
    next();
  } catch (error) {
    console.error('Error in transformOutgoingData middleware:', error);
    next(); // Continue anyway to avoid breaking the request
  }
};

/**
 * Combined middleware that transforms both incoming and outgoing data
 * Convenient wrapper for routes that need both transformations
 */
export const transformData = (req: Request, res: Response, next: NextFunction) => {
  // First transform incoming data
  transformIncomingData(req, res, () => {
    // Then set up outgoing data transformation
    transformOutgoingData(req, res, next);
  });
};

/**
 * Selective transform middleware - only transforms specific fields
 * Useful for routes that have mixed data types or need custom handling
 */
export const transformSpecificFields = (fieldsToTransform: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Transform specific fields in request body
      if (req.body && typeof req.body === 'object') {
        const originalBody = { ...req.body };
        
        fieldsToTransform.forEach(field => {
          if (req.body[field] !== undefined) {
            const snakeField = field.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
            req.body[snakeField] = req.body[field];
            delete req.body[field];
          }
        });
        
        if (process.env.NODE_ENV === 'development') {
          debugTransformation(originalBody, req.body, `Selective Field Transform: ${fieldsToTransform.join(', ')}`);
        }
      }
      
      next();
    } catch (error) {
      console.error('Error in transformSpecificFields middleware:', error);
      next();
    }
  };
};

/**
 * Middleware to skip transformation for specific routes
 * Useful for routes that handle raw data or have custom formatting needs
 */
export const skipTransformation = (req: Request, res: Response, next: NextFunction) => {
  // Add a flag to skip transformation
  (req as any).skipTransformation = true;
  next();
};

/**
 * Conditional transform middleware - only transforms if certain conditions are met
 */
export const conditionalTransform = (
  condition: (req: Request) => boolean,
  transformType: 'incoming' | 'outgoing' | 'both' = 'both'
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!condition(req)) {
      return next();
    }
    
    switch (transformType) {
      case 'incoming':
        return transformIncomingData(req, res, next);
      case 'outgoing':
        return transformOutgoingData(req, res, next);
      case 'both':
        return transformData(req, res, next);
      default:
        return next();
    }
  };
};

/**
 * Error handling middleware for transformation errors
 * Should be placed after transform middlewares to catch any issues
 */
export const handleTransformErrors = (error: Error, req: Request, res: Response, next: NextFunction): void => {
  if (error.message.includes('transform')) {
    console.error('Data transformation error:', error);
    
    res.status(500).json({
      success: false,
      data: null,
      message: 'Internal server error during data processing',
      error: {
        code: 'TRANSFORMATION_ERROR',
        message: process.env.NODE_ENV === 'development' 
          ? error.message 
          : 'Data transformation failed',
        requestId: (req as any).requestId
      },
      timestamp: new Date().toISOString(),
      requestId: (req as any).requestId
    });
    return;
  }
  
  next(error);
};

/**
 * Validation middleware to ensure transformed data maintains integrity
 */
export const validateTransformation = (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json;
  
  res.json = function(data: any) {
    if (data && typeof data === 'object' && data.data) {
      // Basic validation - ensure we didn't lose any data during transformation
      const dataKeys = Object.keys(data.data);
      const hasValidStructure = dataKeys.length > 0;
      
      if (!hasValidStructure) {
        console.warn('⚠️ Potential data loss during transformation:', data);
      }
      
      // Log successful transformations in development
      if (process.env.NODE_ENV === 'development' && hasValidStructure) {
        console.log('✅ Data transformation validation passed');
      }
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Utility function to apply standard transformations to a router
 */
export const applyStandardTransforms = (router: any) => {
  // Apply transforms to all routes in this router
  router.use(transformData);
  router.use(validateTransformation);
  router.use(handleTransformErrors);
  
  return router;
};

/**
 * Route-specific transform configurations
 * Define which routes need specific transformation behavior
 */
export const ROUTE_TRANSFORM_CONFIG = {
  // Routes that need full transformation
  FULL_TRANSFORM: [
    '/api/v1/users',
    '/api/v1/properties',
    '/api/v1/bookings',
    '/api/v1/reviews'
  ],
  
  // Routes that only need outgoing transformation
  OUTGOING_ONLY: [
    '/api/v1/dashboard/summary',
    '/api/v1/properties/stats'
  ],
  
  // Routes that need no transformation (raw data)
  NO_TRANSFORM: [
    '/api/v1/uploads',
    '/api/v1/webhooks',
    '/api/v1/health'
  ],
  
  // Routes with custom field mappings
  CUSTOM_FIELDS: {
    '/api/v1/auth/register': ['fullName', 'phoneNumber'],
    '/api/v1/properties/create': ['halfDayPrice', 'fullDayPrice'],
    '/api/v1/bookings/create': ['bookingType', 'specialRequests']
  }
} as const;

/**
 * Smart middleware that applies appropriate transforms based on route
 */
export const smartTransform = (req: Request, res: Response, next: NextFunction) => {
  const route = req.route?.path || req.path;
  
  // Check if route needs no transformation
  if (ROUTE_TRANSFORM_CONFIG.NO_TRANSFORM.some(pattern => route.includes(pattern))) {
    return skipTransformation(req, res, next);
  }
  
  // Check if route needs outgoing transformation only
  if (ROUTE_TRANSFORM_CONFIG.OUTGOING_ONLY.some(pattern => route.includes(pattern))) {
    return transformOutgoingData(req, res, next);
  }
  
  // Check if route has custom field requirements
  const customFieldRoute = Object.keys(ROUTE_TRANSFORM_CONFIG.CUSTOM_FIELDS)
    .find(pattern => route.includes(pattern));
  
  if (customFieldRoute) {
    const fields = ROUTE_TRANSFORM_CONFIG.CUSTOM_FIELDS[customFieldRoute as keyof typeof ROUTE_TRANSFORM_CONFIG.CUSTOM_FIELDS];
    return transformSpecificFields([...fields])(req, res, next);
  }
  
  // Default to full transformation
  return transformData(req, res, next);
};

// Export middleware functions
export default {
  transformIncomingData,
  transformOutgoingData,
  transformData,
  transformSpecificFields,
  skipTransformation,
  conditionalTransform,
  handleTransformErrors,
  validateTransformation,
  applyStandardTransforms,
  smartTransform,
  ROUTE_TRANSFORM_CONFIG
};