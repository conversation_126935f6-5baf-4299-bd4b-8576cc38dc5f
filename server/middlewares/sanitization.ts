import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import {
  emailValidator,
  indianPhoneValidator,
  safeTextValidator,
  createPriceRangeValidator,
  ValidationPresets
} from '../../shared/validation-utils';

// Common validation schemas for input sanitization using consolidated utilities
export const sanitizationSchemas = {
  // Search query validation - use consolidated patterns
  searchQuery: z.object({
    location: safeTextValidator("Location", 100).optional(),
    minPrice: z.string().regex(/^\d*\.?\d*$/).transform(val => 
      val ? parseFloat(val) : undefined
    ).optional(),
    maxPrice: z.string().regex(/^\d*\.?\d*$/).transform(val => 
      val ? parseFloat(val) : undefined
    ).optional(),
    amenities: z.string().optional().transform(val => 
      val ? val.split(',').map(a => a.trim().replace(/[<>'"]/g, '')) : undefined
    ),
    featured: z.string().optional().transform(val => 
      val === 'true' ? true : val === 'false' ? false : undefined
    ),
    date: z.string().optional().refine(val => 
      !val || !isNaN(Date.parse(val)), 'Invalid date format'
    )
  }).refine((data) => {
    if (data.minPrice && data.maxPrice) {
      return data.maxPrice >= data.minPrice;
    }
    return true;
  }, {
    message: "Maximum price must be greater than or equal to minimum price",
    path: ["maxPrice"]
  }),

  // ID parameters validation - use consolidated validator
  idParam: z.object({
    id: ValidationPresets.id
  }),

  // Phone number validation - use consolidated validator
  phoneNumber: indianPhoneValidator,

  // Email validation - use consolidated validator
  email: emailValidator,

  // General text sanitization - use consolidated safe text validator
  sanitizeText: (maxLength: number = 500) => safeTextValidator("Text", maxLength),

  // Property title and description
  propertyData: z.object({
    title: z.string().min(5).max(100).transform(val => val.trim()),
    description: z.string().min(10).max(1000).transform(val => val.trim()),
    location: z.string().min(5).max(200).transform(val => val.trim())
  })
};

// Middleware factory for request validation and sanitization
export function validateAndSanitize(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Combine query, params, and body for validation
      const dataToValidate = {
        ...req.query,
        ...req.params,
        ...req.body
      };

      const result = schema.safeParse(dataToValidate);
      
      if (!result.success) {
        return res.status(400).json({
          error: 'Invalid input data',
          details: result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }

      // Replace original data with sanitized data
      const sanitizedData = result.data;
      
      // Apply sanitized data back to request
      // Filter out dangerous keys to prevent prototype pollution
      const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
      
      Object.keys(sanitizedData).forEach(key => {
        // Skip dangerous keys that could lead to prototype pollution
        if (dangerousKeys.includes(key) || key.includes('__proto__') || key.includes('constructor')) {
          return;
        }
        
        if (req.query[key] !== undefined) {
          req.query[key] = sanitizedData[key];
        }
        if (req.params[key] !== undefined) {
          req.params[key] = sanitizedData[key];
        }
        if (req.body[key] !== undefined) {
          req.body[key] = sanitizedData[key];
        }
      });

      return next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      return res.status(500).json({ error: 'Internal validation error' });
    }
  };
}

// Specific middleware for different endpoints
export const sanitizeSearchQuery = validateAndSanitize(sanitizationSchemas.searchQuery);
export const sanitizeIdParam = validateAndSanitize(sanitizationSchemas.idParam);
export const sanitizePhoneNumber = validateAndSanitize(
  z.object({ phone: sanitizationSchemas.phoneNumber })
);

// Rate limiting helper (simple in-memory store for development)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(maxRequests: number = 10, windowMs: number = 60000) {
  return (req: Request, res: Response, next: NextFunction) => {
    const identifier = req.ip || 'unknown';
    const now = Date.now();
    
    const userLimit = rateLimitStore.get(identifier);
    
    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize rate limit for this user
      rateLimitStore.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      });
      return next();
    }
    
    if (userLimit.count >= maxRequests) {
      return res.status(429).json({
        error: 'Too many requests',
        message: `Rate limit exceeded. Try again in ${Math.ceil((userLimit.resetTime - now) / 1000)} seconds.`
      });
    }
    
    userLimit.count++;
    next();
  };
}

// SQL injection prevention helper
export function preventSQLInjection(input: string): string {
  if (typeof input !== 'string') return '';
  
  // Remove potentially dangerous SQL keywords and characters
  return input
    .replace(/['";\\]/g, '') // Remove quotes and backslashes
    .replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi, '') // Remove SQL keywords
    .trim();
}

// XSS prevention helper
export function preventXSS(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}