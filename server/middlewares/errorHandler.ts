import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { config } from '../config';
import { logger } from '../services/LoggerService';
import { 
  sendError as sendStandardError, 
  sendInternalError, 
  getRequestId, 
  standardizeError 
} from '../../shared/api-response-utils';
import { ERROR_CODES } from '../../shared/api-response-types';
import { databaseErrorHandler } from './databaseErrorHandler';

// Standard API response format
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | undefined;
  message: string | undefined;
  error?: {
    code: string;
    message: string;
    details?: any;
    requestId?: string;
  };
  timestamp: string;
}

// Custom error classes for better error handling
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class PaymentError extends AppError {
  constructor(message: string = 'Payment processing failed') {
    super(message, 400, 'PAYMENT_ERROR');
    this.name = 'PaymentError';
  }
}

// Enhanced error logging with context
export function logError(error: Error, req: Request, additionalContext?: any) {
  const requestId = req.headers['x-request-id'] as string;
  const userId = (req as any).user?.id;
  
  logger.error(
    `API Error: ${error.message}`,
    error,
    'api',
    {
      requestId,
      userId,
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      ...additionalContext
    }
  );
}

// Generate unique request ID for tracking
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

// Middleware to add request ID to all requests
export function addRequestId(req: Request, res: Response, next: NextFunction) {
  const requestId = req.headers['x-request-id'] as string || generateRequestId();
  req.headers['x-request-id'] = requestId;
  res.setHeader('X-Request-ID', requestId);
  next();
}

// Standardized success response helper
export function sendSuccess<T>(
  res: Response, 
  data?: T, 
  message?: string, 
  statusCode: number = 200
): Response {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
}

// Standardized error response helper
export function sendError(
  res: Response,
  error: AppError | Error,
  req?: Request,
  statusCode?: number
): Response {
  const requestId = req?.headers['x-request-id'] as string;
  
  let code = 'INTERNAL_ERROR';
  let status = statusCode || 500;
  let message = 'Internal server error';

  if (error instanceof AppError) {
    code = error.code;
    status = error.statusCode;
    message = error.message;
  } else if (error instanceof ZodError) {
    code = 'VALIDATION_ERROR';
    status = 400;
    message = 'Validation failed';
  }

  const response: ApiResponse = {
    success: false,
    data: undefined,
    message: undefined,
    error: {
      code,
      message,
      requestId,
      details: config.isDevelopment() && error instanceof ZodError 
        ? error.errors 
        : undefined
    },
    timestamp: new Date().toISOString()
  };

  // Log error for monitoring
  if (req) {
    logError(error, req);
  }

  return res.status(status).json(response);
}

// Global error handler middleware
export function globalErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const requestId = getRequestId(req);
  
  // First, try to handle database-specific errors
  // If databaseErrorHandler handles it, it will send a response and return
  // If not, it will call next() and we continue with standard error handling
  databaseErrorHandler(error, req, res, (err?: any) => {
    // If databaseErrorHandler called next(), continue with standard handling
    if (err) {
      error = err instanceof Error ? err : error; // Use the potentially modified error
    }
    
    // Log error for monitoring
    logError(error, req, { 
      unexpected: !(error instanceof AppError),
      stack: error.stack 
    });

    // Standardize the error
    const standardError = standardizeError(error);
    
    // Don't leak sensitive error details in production
    const message = config.isDevelopment() 
      ? standardError.message 
      : standardError.code === ERROR_CODES.INTERNAL_ERROR 
        ? 'An unexpected error occurred'
        : standardError.message;

    // Use the new standardized error response
    sendStandardError(
      res,
      standardError.code,
      message,
      undefined,
      config.isDevelopment() ? standardError.details : undefined,
      requestId
    );
  });
}

// Async error handler wrapper
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// 404 handler for unmatched routes
export function notFoundHandler(req: Request, res: Response, next: NextFunction) {
  const error = new NotFoundError(`Route ${req.method} ${req.path}`);
  sendError(res, error, req);
}