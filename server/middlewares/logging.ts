import { Request, Response, NextFunction } from 'express';
import { logger, createRequestLogger } from '../services/LoggerService';

// Extend Request type to include start time for performance tracking
declare global {
  namespace Express {
    interface Request {
      startTime?: number;
    }
  }
}

export function requestLoggingMiddleware(req: Request, res: Response, next: NextFunction) {
  // Add start time for performance tracking
  req.startTime = Date.now();
  
  const requestId = req.headers['x-request-id'] as string;
  const userId = (req as any).user?.userId;
  
  // Create request-scoped logger
  const requestLogger = createRequestLogger(requestId, userId);

  // Log incoming request
  const shouldLogRequest = !req.path.includes('/health') && !req.path.includes('/metrics');
  
  if (shouldLogRequest) {
    requestLogger.info(
      `Incoming request: ${req.method} ${req.path}`,
      'http',
      {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        contentLength: req.get('Content-Length'),
        contentType: req.get('Content-Type')
      }
    );
  }

  // Track if we've already logged this response to avoid duplicates
  let responseLogged = false;
  
  // Function to log response once
  const logResponse = () => {
    if (responseLogged || !shouldLogRequest) return;
    responseLogged = true;
    
    const responseTime = Date.now() - (req.startTime || Date.now());
    logger.httpRequest(
      req.method,
      req.path,
      res.statusCode,
      responseTime,
      requestId,
      userId,
      req.get('User-Agent'),
      req.ip,
      {
        contentLength: res.get('Content-Length'),
        contentType: res.get('Content-Type'),
        cacheStatus: res.get('X-Cache')
      }
    );
  };

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data: any) {
    const result = originalJson.call(this, data);
    logResponse();
    return result;
  };

  // Override res.send to also log non-JSON responses
  const originalSend = res.send;
  res.send = function(data: any) {
    const result = originalSend.call(this, data);
    logResponse();
    return result;
  };

  // Also log on response finish event as a fallback
  res.on('finish', logResponse);

  next();
}

// Middleware to log specific events
export function logAuthEvent(
  event: 'login' | 'logout' | 'register' | 'failed_login' | 'token_refresh'
) {
  return (req: Request, res: Response, next: NextFunction) => {
    const requestId = req.headers['x-request-id'] as string;
    const userId = (req as any).user?.userId;
    const ip = req.ip;

    // Log the auth event
    logger.auth(event, userId, ip, requestId, {
      userAgent: req.get('User-Agent'),
      email: req.body?.email || req.body?.identifier
    });

    next();
  };
}

export function logBusinessEvent(event: string, getMetadata?: (req: Request) => Record<string, any>) {
  return (req: Request, res: Response, next: NextFunction) => {
    const requestId = req.headers['x-request-id'] as string;
    const userId = (req as any).user?.userId;
    const metadata = getMetadata ? getMetadata(req) : {};

    logger.business(event, userId, requestId, metadata);

    next();
  };
}

export function logSecurityEvent(event: string, getMetadata?: (req: Request) => Record<string, any>) {
  return (req: Request, res: Response, next: NextFunction) => {
    const requestId = req.headers['x-request-id'] as string;
    const userId = (req as any).user?.userId;
    const ip = req.ip;
    const metadata = getMetadata ? getMetadata(req) : {};

    logger.security(event, userId, ip, requestId, {
      userAgent: req.get('User-Agent'),
      ...metadata
    });

    next();
  };
}

// Audit logging middleware
export function auditLog(action: string, resource: string, getResourceId: (req: Request) => string | number) {
  return (req: Request, res: Response, next: NextFunction) => {
    const requestId = req.headers['x-request-id'] as string;
    const userId = (req as any).user?.userId;
    const resourceId = getResourceId(req);

    // Log after successful response
    const originalJson = res.json;
    res.json = function(data: any) {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        logger.audit(action, resource, resourceId, userId, requestId, {
          method: req.method,
          statusCode: res.statusCode
        });
      }
      return originalJson.call(this, data);
    };

    next();
  };
}

// Performance logging middleware
export function performanceLog(operation: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] as string;

    // Log performance after response
    const originalJson = res.json;
    res.json = function(data: any) {
      logger.performance(operation, startTime, requestId, {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode
      });
      return originalJson.call(this, data);
    };

    next();
  };
}