/**
 * ✅ ENHANCED ERROR HANDLING MIDDLEWARE
 * 
 * Provides comprehensive error handling with:
 * - Structured error logging
 * - Error categorization and severity tracking
 * - Request context preservation
 * - Performance monitoring
 * - Error rate limiting per user
 */

import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { config } from '../config';
import { logger } from '../services/LoggerService';
import { 
  BaseError, 
  ErrorFactory, 
  ValidationError,
  DatabaseError,
  InternalError,
  isOperationalError,
  isCriticalError,
  ErrorContext,
  ErrorSeverity
} from '../errors';
import { 
  sendError, 
  getRequestId 
} from '../../shared/api-response-utils';
import { ERROR_CODES } from '../../shared/api-response-types';

// Track error rates per user for abuse prevention
const errorRateTracker = new Map<string, { count: number; resetAt: number }>();
const ERROR_RATE_WINDOW = 60 * 1000; // 1 minute
const MAX_ERRORS_PER_USER = 50;

// Performance tracking
const errorProcessingTimes: number[] = [];
const MAX_TRACKED_TIMES = 100;

// Error notification queue for critical errors
interface ErrorNotification {
  error: BaseError;
  request: Request;
  timestamp: Date;
}
const criticalErrorQueue: ErrorNotification[] = [];

/**
 * Enhanced error logging with rich context
 */
export function logEnhancedError(
  error: BaseError | Error,
  req: Request,
  processingTime?: number
): void {
  const requestId = getRequestId(req);
  const userId = (req as any).user?.userId;
  
  // Build comprehensive log context
  const logContext = {
    requestId,
    userId,
    method: req.method,
    path: req.path,
    query: req.query,
    ip: req.ip,
    userAgent: req.get('user-agent'),
    referer: req.get('referer'),
    processingTime,
    timestamp: new Date().toISOString()
  };

  // Log based on error type and severity
  if (error instanceof BaseError) {
    const errorLog = error.toLog();
    
    // Choose log level based on severity
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error('Critical error occurred', error, 'error-handler', { ...errorLog, ...logContext });
        // Queue for notification
        criticalErrorQueue.push({ error, request: req, timestamp: new Date() });
        break;
      case ErrorSeverity.HIGH:
        logger.error('High severity error', error, 'error-handler', { ...errorLog, ...logContext });
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn('Medium severity error', 'error-handler', { ...errorLog, ...logContext });
        break;
      case ErrorSeverity.LOW:
        logger.info('Low severity error', 'error-handler', { ...errorLog, ...logContext });
        break;
    }
  } else {
    // Unknown errors are logged as high severity
    logger.error('Unknown error type', error, 'error-handler', {
      message: error.message,
      stack: error.stack,
      ...logContext
    });
  }
}

/**
 * Track error rates per user to prevent abuse
 */
function trackUserErrorRate(userId?: number): boolean {
  if (!userId) return true; // Don't track anonymous users
  
  const key = `user-${userId}`;
  const now = Date.now();
  
  const tracking = errorRateTracker.get(key);
  if (!tracking || tracking.resetAt < now) {
    errorRateTracker.set(key, { count: 1, resetAt: now + ERROR_RATE_WINDOW });
    return true;
  }
  
  tracking.count++;
  return tracking.count <= MAX_ERRORS_PER_USER;
}

/**
 * Convert Zod validation errors to our ValidationError format
 */
function handleZodError(error: ZodError, context?: ErrorContext): ValidationError {
  const details = error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code
  }));
  
  return new ValidationError(
    'Validation failed',
    details,
    context
  );
}

/**
 * Handle database-specific errors
 */
function handleDatabaseError(error: any, context?: ErrorContext): DatabaseError {
  // PostgreSQL error codes
  if (error.code === '23505') {
    return new DatabaseError('Duplicate key violation', error.detail, error, context);
  }
  if (error.code === '23503') {
    return new DatabaseError('Foreign key violation', error.detail, error, context);
  }
  if (error.code === '23502') {
    return new DatabaseError('Not null violation', error.detail, error, context);
  }
  
  // Connection errors
  if (error.code === 'ECONNREFUSED') {
    return new DatabaseError('Database connection refused', undefined, error, context);
  }
  
  return new DatabaseError(error.message || 'Database error occurred', undefined, error, context);
}

/**
 * Main error processing function
 */
export async function processError(
  error: unknown,
  req: Request,
  res: Response
): Promise<void> {
  const startTime = Date.now();
  const requestId = getRequestId(req);
  const userId = (req as any).user?.userId;
  
  // Check error rate limit
  if (!trackUserErrorRate(userId)) {
    res.status(429).json({
      success: false,
      error: {
        code: 'TOO_MANY_ERRORS',
        message: 'Too many errors from this user. Please try again later.',
        requestId
      }
    });
    return;
  }
  
  // Convert to BaseError
  let processedError: BaseError;
  
  if (error instanceof BaseError) {
    processedError = error;
  } else if (error instanceof ZodError) {
    processedError = handleZodError(error, { requestId: requestId || '', userId });
  } else if ((error as any)?.code && typeof (error as any).code === 'string') {
    // Likely a database error
    processedError = handleDatabaseError(error, { requestId: requestId || '', userId });
  } else {
    processedError = ErrorFactory.withContext(error, req);
  }
  
  // Calculate processing time
  const processingTime = Date.now() - startTime;
  errorProcessingTimes.push(processingTime);
  if (errorProcessingTimes.length > MAX_TRACKED_TIMES) {
    errorProcessingTimes.shift();
  }
  
  // Log the error
  logEnhancedError(processedError, req, processingTime);
  
  // Prepare response
  const isDev = config.isDevelopment();
  const statusCode = processedError.statusCode || 500;
  
  // Don't leak sensitive information in production
  let message = processedError.message;
  if (!isDev && !isOperationalError(processedError)) {
    message = 'An unexpected error occurred';
  }
  
  // Build error response
  const errorResponse = {
    code: processedError.code,
    message,
    ...(requestId && { requestId }),
    ...(isDev && processedError instanceof ValidationError && processedError.details && {
      details: processedError.details
    }),
    ...(isDev && { 
      stack: processedError.stack,
      category: processedError.category,
      severity: processedError.severity
    })
  };
  
  // Send response
  res.status(statusCode).json({
    success: false,
    error: errorResponse,
    timestamp: new Date().toISOString()
  });
}

/**
 * Global error handler middleware
 */
export function enhancedGlobalErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Prevent sending multiple responses
  if (res.headersSent) {
    return next(error);
  }
  
  processError(error, req, res).catch(err => {
    // Last resort error handling
    console.error('Error handler failed:', err);
    res.status(500).json({
      success: false,
      error: {
        code: 'HANDLER_ERROR',
        message: 'Error handler encountered an error'
      }
    });
  });
}

/**
 * Async handler wrapper with automatic error catching
 */
export function asyncWrapper<T = any>(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<T>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(error => {
      // Add request context to error if not already present
      if (error instanceof BaseError && !error.context.requestId) {
        const requestId = getRequestId(req);
        if (requestId) {
          error.context.requestId = requestId;
        }
        error.context.userId = (req as any).user?.userId;
      }
      next(error);
    });
  };
}

/**
 * Error boundary middleware to catch sync errors
 */
export function errorBoundary(
  fn: (req: Request, res: Response, next: NextFunction) => void
) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      fn(req, res, next);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Get error handling statistics
 */
export function getErrorStats() {
  const avgProcessingTime = errorProcessingTimes.length > 0
    ? errorProcessingTimes.reduce((a, b) => a + b, 0) / errorProcessingTimes.length
    : 0;
  
  return {
    errorRateTracking: {
      trackedUsers: errorRateTracker.size,
      window: ERROR_RATE_WINDOW,
      maxErrorsPerUser: MAX_ERRORS_PER_USER
    },
    performance: {
      averageProcessingTime: avgProcessingTime,
      sampledRequests: errorProcessingTimes.length
    },
    criticalErrors: {
      queued: criticalErrorQueue.length,
      recent: criticalErrorQueue.slice(-5).map(n => ({
        error: n.error.code,
        message: n.error.message,
        timestamp: n.timestamp
      }))
    }
  };
}

/**
 * Process critical error notifications (should be called by a background job)
 */
export async function processCriticalErrorNotifications() {
  const toProcess = [...criticalErrorQueue];
  criticalErrorQueue.length = 0;
  
  for (const notification of toProcess) {
    // Here you would send notifications to monitoring services,
    // email administrators, create tickets, etc.
    logger.error('Processing critical error notification', notification.error, 'error-notifier', {
      errorCode: notification.error.code,
      requestId: notification.error.context.requestId,
      userId: notification.error.context.userId
    });
  }
  
  return toProcess.length;
}

// Export middleware
export default enhancedGlobalErrorHandler;