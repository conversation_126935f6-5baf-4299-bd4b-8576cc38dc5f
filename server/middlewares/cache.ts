import { Request, Response, NextFunction } from 'express';
import { CacheService } from '../services/CacheService';

interface CacheMiddlewareOptions {
  ttl?: number; // Time to live in seconds
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request, res: Response) => boolean;
  vary?: string[]; // Headers to vary cache by
}

export function cacheMiddleware(options: CacheMiddlewareOptions = {}) {
  const {
    ttl = 300, // 5 minutes default
    keyGenerator = defaultKeyGenerator,
    condition = defaultCondition,
    vary = []
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching if condition is not met
    if (!condition(req, res)) {
      return next();
    }

    const cacheKey = keyGenerator(req);
    
    try {
      // Try to get cached response
      const cachedResponse = await CacheService.getInstance().get<{
        statusCode: number;
        data: any;
        headers: Record<string, string>;
        timestamp: number;
      }>(cacheKey);

      if (cachedResponse) {
        // Set cache headers
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);
        res.set('X-Cache-Timestamp', new Date(cachedResponse.timestamp).toISOString());
        
        // Set vary headers
        if (vary.length > 0) {
          res.set('Vary', vary.join(', '));
        }

        // Set cached headers
        Object.entries(cachedResponse.headers).forEach(([key, value]) => {
          res.set(key, value);
        });

        return res.status(cachedResponse.statusCode).json(cachedResponse.data);
      }

      // Cache miss - intercept response
      const originalJson = res.json;
      const originalSend = res.send;
      
      res.json = function(data: any) {
        // Cache successful responses only
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const responseToCache = {
            statusCode: res.statusCode,
            data,
            headers: {
              'Content-Type': 'application/json',
              ...(vary.length > 0 && { 'Vary': vary.join(', ') })
            },
            timestamp: Date.now()
          };

          // Store in cache asynchronously
          CacheService.getInstance().set(cacheKey, responseToCache, ttl)
            .catch(error => {
              console.error('Cache storage error:', error);
            });
        }

        // Set cache headers
        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);
        
        if (vary.length > 0) {
          res.set('Vary', vary.join(', '));
        }

        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      // Continue without caching on error
      next();
    }
  };
}

function defaultKeyGenerator(req: Request): string {
  const { method, path, query } = req;
  const sortedQuery = Object.keys(query)
    .sort()
    .map(key => `${key}=${query[key]}`)
    .join('&');
  
  return CacheService.generateKey(method, path, sortedQuery);
}

function defaultCondition(req: Request, res: Response): boolean {
  // Only cache GET requests
  return req.method === 'GET';
}

// Predefined cache configurations
export const cacheConfigs = {
  // Short cache for frequently changing data
  short: { ttl: 60 }, // 1 minute
  
  // Medium cache for semi-static data
  medium: { ttl: 300 }, // 5 minutes
  
  // Long cache for static data
  long: { ttl: 3600 }, // 1 hour
  
  // Property search cache (varies by user location)
  propertySearch: {
    ttl: 300,
    vary: ['User-Agent', 'Accept-Encoding'],
    keyGenerator: (req: Request) => {
      const { location, featured, minPrice, maxPrice, amenities, date } = req.query;
      const locationStr = Array.isArray(location) ? String(location[0]) : String(location || 'all');
      const featuredStr = Array.isArray(featured) ? String(featured[0]) : String(featured || 'false');
      const minPriceStr = Array.isArray(minPrice) ? String(minPrice[0]) : String(minPrice || '0');
      const maxPriceStr = Array.isArray(maxPrice) ? String(maxPrice[0]) : String(maxPrice || 'inf');
      const amenitiesStr = Array.isArray(amenities) ? amenities.map(String).join(',') : String(amenities || 'none');
      const dateStr = Array.isArray(date) ? String(date[0]) : String(date || 'any');
      
      return CacheService.generateKey(
        'property-search',
        locationStr,
        featuredStr,
        minPriceStr,
        maxPriceStr,
        amenitiesStr,
        dateStr
      );
    }
  },

  // Property details cache
  propertyDetails: {
    ttl: 600, // 10 minutes
    keyGenerator: (req: Request) => {
      return CacheService.generateKey('property', req.params.id);
    }
  },

  // Review cache
  reviews: {
    ttl: 300,
    keyGenerator: (req: Request) => {
      return CacheService.generateKey('reviews', req.params.id);
    }
  }
};

// Cache invalidation helpers
export class CacheInvalidator {
  static async invalidatePattern(pattern: string): Promise<void> {
    const cache = CacheService.getInstance();
    const keys = await cache.keys(pattern);
    
    await Promise.all(keys.map(key => cache.delete(key)));
    
    if (keys.length > 0) {
      console.log(`🗑️ Cache invalidated: ${keys.length} keys matching pattern "${pattern}"`);
    }
  }

  static async invalidateProperty(propertyId: string | number): Promise<void> {
    await Promise.all([
      this.invalidatePattern(`property:${propertyId}*`),
      this.invalidatePattern(`property-search:*`), // Property changes affect search results
      this.invalidatePattern(`reviews:${propertyId}*`)
    ]);
  }

  static async invalidateUser(userId: string | number): Promise<void> {
    await this.invalidatePattern(`user:${userId}*`);
  }

  static async invalidateBookings(propertyId?: string | number): Promise<void> {
    if (propertyId) {
      await this.invalidatePattern(`bookings:property:${propertyId}*`);
    }
    await this.invalidatePattern(`bookings:*`);
  }
}