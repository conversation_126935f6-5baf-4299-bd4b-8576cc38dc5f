/**
 * Legacy database module - DEPRECATED
 * 
 * This module is kept for backward compatibility.
 * New code should use the robust database manager from './utils/database.ts'
 */

import databaseManager, { 
  db, 
  withConnection, 
  withTransaction,
  databaseManager as dbManager 
} from './utils/database';
import { withDatabaseErrorBoundary } from './utils/error-boundary';

// Legacy exports for backward compatibility
export { db, withConnection, withTransaction };

// Legacy pool interface for existing code
export const pool = {
  connect: async () => {
    console.warn('⚠️ Using legacy pool.connect() - consider migrating to withConnection()');
    const pool = dbManager.getDrizzleInstance() as any;
    return pool.pool.connect();
  },
  query: async (text: string, params?: any[]) => {
    console.warn('⚠️ Using legacy pool.query() - consider migrating to withConnection()');
    return withConnection(async (client) => {
      return client.query(text, params);
    }, 'legacy-pool-query');
  },
  end: async () => {
    console.warn('⚠️ Using legacy pool.end() - using modern shutdown');
    return dbManager.shutdown();
  }
};

// Legacy functions - kept for backward compatibility
export async function testDatabaseConnection(): Promise<boolean> {
  console.warn('⚠️ Using legacy testDatabaseConnection() - consider using dbManager.testConnection()');
  return dbManager.testConnection();
}

export async function ensureHealthyConnection(maxRetries: number = 3): Promise<boolean> {
  console.warn('⚠️ Using legacy ensureHealthyConnection() - consider using withConnection()');
  try {
    return await withDatabaseErrorBoundary(
      () => dbManager.testConnection(),
      'legacy-health-check'
    );
  } catch {
    return false;
  }
}

export async function withHealthyConnection<T>(
  operation: () => Promise<T>,
  operationName: string = 'database operation'
): Promise<T> {
  console.warn('⚠️ Using legacy withHealthyConnection() - consider using withConnection()');
  return withDatabaseErrorBoundary(
    () => withConnection(async () => operation(), operationName),
    operationName
  );
}

export async function closeDatabase(): Promise<void> {
  console.warn('⚠️ Using legacy closeDatabase() - using modern shutdown');
  return dbManager.shutdown();
}

export async function resetDatabaseConnection(): Promise<void> {
  console.warn('⚠️ Using legacy resetDatabaseConnection() - using modern recovery');
  const recovered = await dbManager.recoverConnection();
  if (!recovered) {
    console.error('❌ Database connection recovery failed');
  }
}

// Export database manager for new code
export { databaseManager };
export default databaseManager;