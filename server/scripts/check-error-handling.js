#!/usr/bin/env node

/**
 * <PERSON>ript to check for non-standardized error handling patterns in route files
 * 
 * Usage: node server/scripts/check-error-handling.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Patterns that indicate non-standardized error handling
const problematicPatterns = [
  {
    name: 'Manual try-catch in routes',
    pattern: /router\.(get|post|put|patch|delete|all)\s*\([^)]*\)\s*{\s*try\s*{/gm,
    suggestion: 'Use asyncHandler wrapper instead of manual try-catch'
  },
  {
    name: 'Direct .catch() on promises',
    pattern: /\.(then|catch)\s*\(/gm,
    suggestion: 'Use async/await with as<PERSON><PERSON><PERSON><PERSON> instead of .then().catch()'
  },
  {
    name: 'Direct error responses',
    pattern: /res\.status\s*\(\s*[45]\d\d\s*\)\.json\s*\(/gm,
    suggestion: 'Throw specific error types and let global handler respond'
  },
  {
    name: 'Console.error in routes',
    pattern: /console\.(error|warn)\s*\(/gm,
    suggestion: 'Use logger service and throw errors for proper handling'
  },
  {
    name: '<PERSON><PERSON> throws',
    pattern: /throw\s+new\s+Error\s*\(/gm,
    suggestion: 'Use specific error types (ValidationError, NotFoundError, etc.)'
  },
  {
    name: 'Missing asyncHandler',
    pattern: /router\.(get|post|put|patch|delete)\s*\([^)]*async\s*\([^)]*\)\s*=>\s*{(?!.*asyncHandler)/gm,
    suggestion: 'Wrap async route handlers with asyncHandler'
  }
];

// Good patterns to check for
const goodPatterns = [
  {
    name: 'Uses asyncHandler',
    pattern: /asyncHandler\s*\(\s*async/gm
  },
  {
    name: 'Uses specific error types',
    pattern: /throw\s+new\s+(ValidationError|NotFoundError|AuthorizationError|ConflictError|BusinessLogicError)/gm
  },
  {
    name: 'Uses sendSuccess helper',
    pattern: /sendSuccess\s*\(/gm
  }
];

async function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(process.cwd(), filePath);
  
  const issues = [];
  const good = [];
  
  // Check for problematic patterns
  for (const { name, pattern, suggestion } of problematicPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        pattern: name,
        count: matches.length,
        suggestion
      });
    }
  }
  
  // Check for good patterns
  for (const { name, pattern } of goodPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      good.push({
        pattern: name,
        count: matches.length
      });
    }
  }
  
  return {
    file: relativePath,
    issues,
    good,
    score: good.length > 0 && issues.length === 0 ? 'GOOD' : issues.length > 0 ? 'NEEDS_UPDATE' : 'REVIEW'
  };
}

async function main() {
  console.log('🔍 Checking for non-standardized error handling patterns...\n');
  
  // Find all route files
  const routeFiles = glob.sync('server/routes/**/*.ts', {
    ignore: [
      '**/example-*.ts',
      '**/*.test.ts',
      '**/*.spec.ts'
    ]
  });
  
  console.log(`Found ${routeFiles.length} route files to check\n`);
  
  const results = await Promise.all(routeFiles.map(checkFile));
  
  // Group by score
  const needsUpdate = results.filter(r => r.score === 'NEEDS_UPDATE');
  const good = results.filter(r => r.score === 'GOOD');
  const review = results.filter(r => r.score === 'REVIEW');
  
  // Print results
  if (needsUpdate.length > 0) {
    console.log('❌ Files that need updating:\n');
    for (const result of needsUpdate) {
      console.log(`  ${result.file}`);
      for (const issue of result.issues) {
        console.log(`    - ${issue.pattern} (${issue.count} occurrences)`);
        console.log(`      💡 ${issue.suggestion}`);
      }
      console.log();
    }
  }
  
  if (review.length > 0) {
    console.log('⚠️  Files that need review:\n');
    for (const result of review) {
      console.log(`  ${result.file}`);
    }
    console.log();
  }
  
  if (good.length > 0) {
    console.log('✅ Files with good error handling:\n');
    for (const result of good) {
      console.log(`  ${result.file}`);
      for (const pattern of result.good) {
        console.log(`    + ${pattern.pattern} (${pattern.count} uses)`);
      }
    }
    console.log();
  }
  
  // Summary
  console.log('\n📊 Summary:');
  console.log(`  Total files: ${routeFiles.length}`);
  console.log(`  ✅ Good: ${good.length}`);
  console.log(`  ❌ Needs update: ${needsUpdate.length}`);
  console.log(`  ⚠️  Needs review: ${review.length}`);
  
  // Exit with error if files need updating
  if (needsUpdate.length > 0) {
    console.log('\n❗ Some files need to be updated to use standardized error handling.');
    console.log('   Please review the docs/ERROR_HANDLING_STANDARDIZATION.md guide.');
    process.exit(1);
  }
  
  console.log('\n✨ All files use standardized error handling!');
  process.exit(0);
}

// Run the script
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});