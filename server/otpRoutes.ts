import { Router } from 'express';
import { otpService, OTPRequest, OTPVerification } from './services/OTPService';
import { storage } from './storage';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { createPersistentRateLimit } from './persistentRateLimit';
import { config } from './config';
import { db } from './db';
import { otpTokens } from '../shared/schema';
import { eq } from 'drizzle-orm';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendInternalError,
  sendOtpError,
  getRequestId,
  withErrorHandling
} from '../shared/api-response-utils';
import { ERROR_CODES } from '../shared/api-response-types';
import {
  otpRateLimiter,
  authRateLimiter,
  sanitizeInput,
  validateInput,
  securityHeaders,
  otpRequestSchema,
  otpVerificationSchema,
  registrationSchema,
  phoneSchema,
  emailSchema
} from './utils/security';

const router = Router();

// Apply security middleware to all routes
router.use(securityHeaders);
router.use(sanitizeInput);

// Enhanced validation schemas for registration
const registrationOTPSchema = z.object({
  identifier: z.string().min(1, "Email or phone number is required"),
  code: z.string().length(6, "OTP must be 6 digits"),
  type: z.enum(['email', 'sms']),
  userData: registrationSchema.extend({
    username: z.string().optional(),
    termsAccepted: z.boolean().default(true),
    privacyPolicyAccepted: z.boolean().default(true),
    cookiePolicyAccepted: z.boolean().default(true),
    dataProcessingConsent: z.boolean().default(true),
    marketingConsent: z.boolean().default(false),
    emailCode: z.string().optional() // For dual verification
  }).optional()
});

const loginOTPSchema = z.object({
  identifier: z.string().min(1, "Email or phone number is required"),
  code: z.string().length(6, "OTP must be 6 digits"),
  type: z.enum(['email', 'sms'])
});

// Helper function to normalize phone numbers
function normalizePhone(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Add +91 for Indian numbers if not present
  if (digits.length === 10) {
    return `+91${digits}`;
  } else if (digits.length === 12 && digits.startsWith('91')) {
    return `+${digits}`;
  } else if (digits.length === 13 && digits.startsWith('91')) {
    return `+${digits}`;
  }
  
  return phone; // Return as-is if format is unclear
}

// Check if user exists endpoint
// Development endpoint to clear rate limits
if (process.env.NODE_ENV === 'development') {
  router.post('/clear-rate-limits', withErrorHandling(async (req, res) => {
    const requestId = getRequestId(req);
    
    try {
      // Clear all rate limit entries from the database
      await db.delete(otpTokens).where(eq(otpTokens.type, 'rate_limit'));
      
      return sendSuccess(res, null, "Rate limits cleared successfully", 200, requestId);
    } catch (error) {
      console.error('Error clearing rate limits:', error);
      return sendInternalError(res, 'Failed to clear rate limits', requestId);
    }
  }));
}

router.post('/check-user', withErrorHandling(async (req, res) => {
  const requestId = getRequestId(req);
  const { identifier, type } = req.body;
  
  if (!identifier || !type) {
    return sendValidationError(res, 'Identifier and type are required', undefined, requestId);
  }

  const normalizedIdentifier = type === 'sms' ? normalizePhone(identifier) : identifier.toLowerCase().trim();
  
  let existingUser;
  if (type === 'email') {
    existingUser = await storage.getUserByEmail(normalizedIdentifier);
  } else {
    existingUser = await storage.getUserByPhone(normalizedIdentifier);
  }

  const responseData = {
    exists: !!existingUser,
    identifier: normalizedIdentifier
  };
  
  return sendSuccess(res, responseData, null, 200, requestId);
}));

// Database-persistent rate limiters for security (survive server restarts)
const otpSendLimiter = createPersistentRateLimit({
  windowMs: process.env.NODE_ENV === 'development' ? 2 * 60 * 1000 : 10 * 60 * 1000, // 2 minutes in dev, 10 minutes in prod
  max: process.env.NODE_ENV === 'development' ? 20 : 3, // 20 in dev, 3 in prod
  message: process.env.NODE_ENV === 'development' 
    ? "Too many OTP requests. You can only request 20 OTPs per 2 minutes in development."
    : "Too many OTP requests. You can only request 3 OTPs per 10 minutes to prevent SMS abuse.",
  keyGenerator: (req) => {
    // Rate limit by IP + identifier to prevent abuse of different phone/email
    const identifier = req.body?.identifier || req.ip;
    return `otp_send:${req.ip}:${identifier}`;
  }
}).middleware();

// Even stricter rate limiter for same phone number (prevents SMS bombing)
const strictOtpLimiter = createPersistentRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Maximum 3 OTP requests per hour for the same phone number (1 initial + 2 resends)
  message: "Maximum OTP limit reached. You can only request 3 OTPs per hour for the same phone number.",
  keyGenerator: (req) => {
    // Rate limit strictly by phone number to prevent SMS bombing
    const identifier = req.body?.identifier || 'unknown';
    return `phone:${identifier}`;
  }
}).middleware();

const otpVerifyLimiter = createPersistentRateLimit({
  windowMs: process.env.NODE_ENV === 'development' ? 1 * 60 * 1000 : 5 * 60 * 1000, // 1 minute in dev, 5 minutes in prod
  max: process.env.NODE_ENV === 'development' ? 50 : 10, // 50 in dev, 10 in prod
  message: process.env.NODE_ENV === 'development'
    ? "Too many verification attempts from this IP in development."
    : "Too many verification attempts from this IP.",
  keyGenerator: (req) => {
    // Rate limit by IP + identifier for verification attempts
    const identifier = req.body?.identifier || req.ip;
    return `verify:${req.ip}:${identifier}`;
  }
}).middleware();

// Send OTP for registration or login
router.post('/send-otp', 
  otpRateLimiter,
  authRateLimiter, 
  validateInput(otpRequestSchema),
  withErrorHandling(async (req, res) => {
    const requestId = getRequestId(req);
    
    try {
      const { identifier, type } = req.body; // Already validated by middleware
    
    let normalizedIdentifier = identifier;
    if (type === 'sms') {
      normalizedIdentifier = normalizePhone(identifier);
    }

    const result = await otpService.sendOTP({
      identifier: normalizedIdentifier,
      type
    } as OTPRequest);

    if (result.success) {
      const responseData = {
        identifier: normalizedIdentifier
      };
      return sendSuccess(res, responseData, result.message, 200, requestId);
    } else {
      return sendError(res, ERROR_CODES.OTP_INVALID, result.message, undefined, undefined, requestId);
    }
  } catch (error) {
    console.error('Send OTP error:', error);
    if (error instanceof z.ZodError) {
      return sendValidationError(res, error.errors[0].message, undefined, requestId);
    } else {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send OTP';
      console.error('Detailed error:', errorMessage);
      return sendInternalError(res, errorMessage, requestId);
    }
  }
}));

// Verify OTP and register new user
router.post('/verify-otp-register', 
  authRateLimiter,
  validateInput(registrationOTPSchema),
  async (req, res) => {
    try {
      const { identifier, code, type, userData } = req.body; // Already validated
    
    let normalizedIdentifier = identifier;
    if (type === 'sms') {
      normalizedIdentifier = normalizePhone(identifier);
    }


    // Verify the primary OTP (SMS for phone-based registration)
    const primaryVerificationResult = await otpService.verifyOTP({
      identifier: normalizedIdentifier,
      code,
      type
    } as OTPVerification);

    if (!primaryVerificationResult.success) {
      return res.status(400).json({
        success: false,
        message: primaryVerificationResult.message
      });
    }

    if (!userData) {
      return res.status(400).json({
        success: false,
        message: 'User data is required for registration'
      });
    }

    // For dual verification (signup), also verify email OTP if provided
    const userDataWithEmailCode = userData as any; // Type assertion for emailCode
    if (userDataWithEmailCode.emailCode && userDataWithEmailCode.email) {
      
      // Check for email master code first (bypass database check)
      const isDevelopment = process.env.NODE_ENV === 'development';
      const isMasterCode = isDevelopment && (userDataWithEmailCode.emailCode === '999999' || userDataWithEmailCode.emailCode === '123456');
      
      if (isMasterCode) {
        // Master code used in development
      } else {
        // Normal verification for non-master codes
        const emailVerificationResult = await otpService.verifyOTP({
          identifier: userDataWithEmailCode.email,
          code: userDataWithEmailCode.emailCode,
          type: 'email'
        } as OTPVerification);

        if (!emailVerificationResult.success) {
          return res.status(400).json({
            success: false,
            message: 'Email verification failed: ' + emailVerificationResult.message
          });
        }
      }
    }

    // Check if user already exists by email or phone
    let existingUser;
    if (userData.email) {
      existingUser = await storage.getUserByEmail(userData.email);
    }
    if (!existingUser && userData.phone) {
      existingUser = await storage.getUserByPhone(userData.phone);
    }
    if (!existingUser && type === 'email') {
      existingUser = await storage.getUserByEmail(normalizedIdentifier);
    } else if (!existingUser && type === 'sms') {
      existingUser = await storage.getUserByPhone(normalizedIdentifier);
    }

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email/phone'
      });
    }

    // Generate unique username to avoid conflicts
    const baseUsername = userData.email || userData.phone || normalizedIdentifier;
    const timestamp = Date.now();
    const uniqueUsername = `${baseUsername}_${timestamp}`;

    // Create new user

    const newUser = await storage.createUser({
      email: userData.email || (type === 'email' ? normalizedIdentifier : ''),
      phone: userData.phone || (type === 'sms' ? normalizedIdentifier : ''),
      username: uniqueUsername,
      fullName: userData.fullName,
      password: '', // No password needed for OTP users
      role: userData.role || 'user',
      termsAccepted: true,
      privacyPolicyAccepted: true,
      cookiePolicyAccepted: true,
      dataProcessingConsent: true,
      marketingConsent: false
    });

    // Generate JWT token
    const token = jwt.sign(
      { userId: newUser.id, role: newUser.role },
      config.jwt.secret,
      { 
        expiresIn: '7d',
        audience: 'farmhouse-rental-app',
        issuer: 'farmhouse-rental-api'
      }
    );

    res.cookie('auth_token', token, {
      httpOnly: true,
      secure: config.app.useSecureCookies,
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
    });

    return res.json({
      success: true,
      message: 'Registration successful',
      user: {
        id: newUser.id,
        email: newUser.email,
        phone: newUser.phone,
        fullName: newUser.fullName,
        role: newUser.role
      },
      token: token
    });

  } catch (error) {
    console.error('Verify OTP register error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Registration failed';
    console.error('Detailed registration error:', errorMessage);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: error.errors[0].message
      });
    } else {
      return res.status(500).json({
        success: false,
        message: errorMessage
      });
    }
  }
});

// Verify OTP and login existing user
router.post('/verify-otp-login', 
  authRateLimiter,
  validateInput(loginOTPSchema),
  async (req, res) => {
    try {
      const { identifier, code, type } = req.body; // Already validated
    
    let normalizedIdentifier = identifier;
    if (type === 'sms') {
      normalizedIdentifier = normalizePhone(identifier);
    }

    // Verify OTP
    const verificationResult = await otpService.verifyOTP({
      identifier: normalizedIdentifier,
      code,
      type
    } as OTPVerification);

    if (!verificationResult.success) {
      return res.status(400).json({
        success: false,
        message: verificationResult.message
      });
    }

    // Find user with proper error handling
    let user;
    try {
      if (type === 'email') {
        user = await storage.getUserByEmail(normalizedIdentifier);
      } else {
        user = await storage.getUserByPhone(normalizedIdentifier);
      }
    } catch (dbError) {
      console.error('Database error during user lookup:', dbError);
      return res.status(500).json({
        success: false,
        message: 'Database connection error. Please try again in a moment.'
      });
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'No account found with this email/phone. Please register first.'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, role: user.role },
      config.jwt.secret,
      { 
        expiresIn: '7d',
        audience: 'farmhouse-rental-app',
        issuer: 'farmhouse-rental-api'
      }
    );

    res.cookie('auth_token', token, {
      httpOnly: true,
      secure: config.app.useSecureCookies,
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
    });

    return res.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        phone: user.phone,
        fullName: user.fullName,
        role: user.role
      },
      token: token
    });

  } catch (error) {
    console.error('Verify OTP login error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Login failed';
    console.error('Detailed login error:', errorMessage);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: error.errors[0].message
      });
    } else {
      return res.status(500).json({
        success: false,
        message: errorMessage
      });
    }
  }
});

export default router;