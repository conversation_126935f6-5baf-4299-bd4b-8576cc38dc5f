import { db } from './db';
import { otpTokens } from '../shared/schema';
import { and, eq, lt, gte, count } from 'drizzle-orm';
import { Request, Response, NextFunction } from 'express';

interface RateLimitOptions {
  windowMs: number; // Window duration in milliseconds
  max: number; // Maximum number of requests
  message: string; // Error message when limit is exceeded
  keyGenerator: (req: Request) => string; // Function to generate unique key
}

/**
 * Database-persistent rate limiter that survives server restarts
 * Uses the otp_tokens table to track rate limit attempts
 */
export class PersistentRateLimit {
  private options: RateLimitOptions;

  constructor(options: RateLimitOptions) {
    this.options = options;
  }

  /**
   * Middleware function for Express
   */
  middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const key = this.options.keyGenerator(req);
        const now = new Date();
        const windowStart = new Date(now.getTime() - this.options.windowMs);

        // Clean up expired entries first
        await this.cleanup(windowStart);

        // Count attempts within the window for this key
        const [result] = await db
          .select({ count: count() })
          .from(otpTokens)
          .where(
            and(
              eq(otpTokens.identifier, key),
              gte(otpTokens.createdAt, windowStart),
              eq(otpTokens.type, 'rate_limit') // Use special type for rate limiting
            )
          );

        const currentCount = result?.count || 0;

        if (currentCount >= this.options.max) {
          // Calculate time until window resets
          const oldestAttempt = await db
            .select({ createdAt: otpTokens.createdAt })
            .from(otpTokens)
            .where(
              and(
                eq(otpTokens.identifier, key),
                eq(otpTokens.type, 'rate_limit')
              )
            )
            .orderBy(otpTokens.createdAt)
            .limit(1);

          let retryAfter = Math.ceil(this.options.windowMs / 1000); // Default fallback
          if (oldestAttempt.length > 0) {
            const resetTime = new Date(oldestAttempt[0].createdAt.getTime() + this.options.windowMs);
            retryAfter = Math.ceil((resetTime.getTime() - now.getTime()) / 1000);
          }

          // Format human-readable time
          const timeMessage = this.formatTime(retryAfter);
          
          return res.status(429).json({
            error: this.options.message.replace(/Please try again in \d+ \w+/, `Please try again in ${timeMessage}`)
          });
        }

        // Record this attempt
        await db.insert(otpTokens).values({
          identifier: key,
          code: 'rate_limit_attempt', // Special marker for rate limiting
          type: 'rate_limit',
          expiresAt: new Date(now.getTime() + this.options.windowMs)
        });

        return next();
      } catch (error) {
        console.error('Persistent rate limit error:', error);
        // On error, allow the request to proceed (fail open)
        return next();
      }
    };
  }

  /**
   * Clean up expired rate limit entries
   */
  private async cleanup(windowStart: Date) {
    try {
      await db
        .delete(otpTokens)
        .where(
          and(
            eq(otpTokens.type, 'rate_limit'),
            lt(otpTokens.expiresAt, new Date())
          )
        );
    } catch (error) {
      console.error('Rate limit cleanup error:', error);
    }
  }

  /**
   * Format seconds into human-readable time
   */
  private formatTime(seconds: number): string {
    if (seconds >= 3600) {
      const hours = Math.ceil(seconds / 3600);
      return hours === 1 ? '1 hour' : `${hours} hours`;
    } else if (seconds >= 60) {
      const minutes = Math.ceil(seconds / 60);
      return minutes === 1 ? '1 minute' : `${minutes} minutes`;
    } else {
      return seconds === 1 ? '1 second' : `${seconds} seconds`;
    }
  }
}

/**
 * Factory function to create persistent rate limiters
 */
export function createPersistentRateLimit(options: RateLimitOptions) {
  return new PersistentRateLimit(options);
}