import { eq, and, desc, gte, lte, like, ilike, or, inArray, sql, lt } from "drizzle-orm";
import { db, withHealthyConnection } from "./db";
import { 
  users, properties, bookings, reviews, ownerInterestRequests, gstRecords, 
  paymentOrders, paymentTransactions, paymentAuditLogs,
  type User, type InsertUser, 
  type Property, type InsertProperty,
  type Booking, type InsertBooking,
  type Review, type InsertReview,
  type OwnerInterestRequest, type InsertOwnerInterest
} from "../shared/schema.ts";
import bcrypt from "bcrypt";

export interface IStorage {
  // User operations
  getAllUsers(): Promise<User[]>;
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByPhone(phone: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;

  // Property operations
  getProperties(
    featured?: boolean, 
    location?: string, 
    date?: Date,
    minPrice?: number,
    maxPrice?: number,
    amenities?: string[]
  ): Promise<Property[]>;
  getProperty(id: number): Promise<Property | undefined>;
  getPropertiesByOwner(ownerId: number): Promise<Property[]>;
  getPropertiesByIds(propertyIds: number[]): Promise<Property[]>;
  createProperty(property: InsertProperty): Promise<Property>;
  updateProperty(id: number, property: Partial<InsertProperty>): Promise<Property | undefined>;
  deleteProperty(id: number): Promise<boolean>;

  // Booking operations
  getBookings(userId: number): Promise<Booking[]>;
  getBookingsWithProperty(userId: number): Promise<(Booking & { property: Property })[]>;
  getBookingsByProperty(propertyId: number): Promise<Booking[]>;
  getBookingsByPropertyIds(propertyIds: number[]): Promise<Record<number, (Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } })[]>>;
  getBookingsByOwner(ownerId: number): Promise<(Booking & { property: Property })[]>;
  getBooking(id: number): Promise<Booking | undefined>;
  checkAvailability(propertyId: number, date: Date, type: 'morning' | 'full_day'): Promise<boolean>;
  createBooking(booking: InsertBooking): Promise<Booking>;
  updateBookingStatus(id: number, status: string): Promise<Booking | undefined>;

  // Review operations
  getReviews(propertyId: number): Promise<(Review & { user: User })[]>;
  getUserReviews(userId: number): Promise<(Review & { property: Property })[]>;
  getReview(id: number): Promise<Review | undefined>;
  createReview(review: InsertReview): Promise<Review>;
  updateReview(id: number, review: Partial<InsertReview>): Promise<Review | undefined>;
  addOwnerResponse(id: number, response: string): Promise<Review | undefined>;
  deleteReview(id: number): Promise<boolean>;
  getPropertyAverageRating(propertyId: number): Promise<number>;

  // Owner Interest operations
  createOwnerInterest(ownerInterest: InsertOwnerInterest): Promise<OwnerInterestRequest>;
  getOwnerInterests(): Promise<OwnerInterestRequest[]>;
  getOwnerInterest(id: number): Promise<OwnerInterestRequest | undefined>;
  updateOwnerInterestStatus(id: number, status: string): Promise<OwnerInterestRequest | undefined>;

  // Performance optimization methods
  getUniqueLocations(): Promise<string[]>;
  getPriceStatistics(): Promise<{ min: number; max: number; avg: number; median: number }>;
  getAvailableAmenities(): Promise<string[]>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getAllUsers(): Promise<User[]> {
    try {
      const allUsers = await db.select().from(users);
      return allUsers;
    } catch (error) {
      console.error("Error fetching all users:", error);
      return [];
    }
  }

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async getUserByPhone(phone: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.phone, phone));
    return user;
  }

  async createUser(userData: any): Promise<User> {
    return withHealthyConnection(async () => {
      // Hash the password before storing
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Ensure phone field is included for consistency
      const userDataWithPhone = {
        ...userData,
        password: hashedPassword,
        phone: userData.phone || null
      };

      const [user] = await db
        .insert(users)
        .values(userDataWithPhone)
        .returning();

      if (!user) {
        throw new Error('Failed to create user');
      }

      return user;
    }, 'user creation');
  }

  async updateUser(id: number, userData: Partial<InsertUser>): Promise<User | undefined> {
    const [user] = await db
      .update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning();

    return user;
  }

  // Property operations
  async getProperties(
    featured?: boolean, 
    location?: string, 
    date?: Date,
    minPrice?: number,
    maxPrice?: number,
    amenities?: string[]
  ): Promise<Property[]> {
    console.log(`🔍 [DEBUG] getProperties called with:`, { featured, location, date, minPrice, maxPrice, amenities });
    try {
      // Build WHERE conditions array for efficient SQL filtering
      const conditions = [];

      // Featured filter
      if (featured) {
        conditions.push(eq(properties.featured, true));
      }

      // Location search (title, description, location)
      if (location && location.trim() !== '') {
        const searchTerm = `%${location.trim().toLowerCase()}%`;

        conditions.push(
          or(
            // Search in location field (flexible matching)
            sql`LOWER(${properties.location}) LIKE ${searchTerm}`,
            // Search in title
            sql`LOWER(${properties.title}) LIKE ${searchTerm}`,
            // Search in description
            sql`LOWER(${properties.description}) LIKE ${searchTerm}`
          )
        );
      }

      // Price range filters - more efficient SQL approach
      if (minPrice !== undefined || maxPrice !== undefined) {
        const priceConditions = [];

        if (minPrice !== undefined && maxPrice !== undefined) {
          // Both min and max price specified
          priceConditions.push(
            or(
              and(gte(properties.halfDayPrice, minPrice), lte(properties.halfDayPrice, maxPrice)),
              and(gte(properties.fullDayPrice, minPrice), lte(properties.fullDayPrice, maxPrice))
            )
          );
        } else if (minPrice !== undefined) {
          // Only minimum price specified
          priceConditions.push(
            or(
              gte(properties.halfDayPrice, minPrice),
              gte(properties.fullDayPrice, minPrice)
            )
          );
        } else if (maxPrice !== undefined) {
          // Only maximum price specified
          priceConditions.push(
            or(
              lte(properties.halfDayPrice, maxPrice),
              lte(properties.fullDayPrice, maxPrice)
            )
          );
        }

        if (priceConditions.length > 0) {
          conditions.push(...priceConditions);
        }
      }

      // Execute the main query with SQL-based filtering
      const baseQuery = db
        .select()
        .from(properties)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(properties.createdAt));



      let filteredProperties = await baseQuery;

      // Amenities filtering (requires JSON operations, done post-query for compatibility)
      if (amenities && amenities.length > 0) {
        filteredProperties = filteredProperties.filter(prop => {
          const propAmenities = prop.amenities as string[];
          return amenities.some(amenity => 
            propAmenities.some(propAmenity => 
              propAmenity.toLowerCase().includes(amenity.toLowerCase())
            )
          );
        });
      }

      // Date availability filtering (optimized with single query)
      if (date) {
        const searchDate = new Date(date);
        searchDate.setHours(0, 0, 0, 0);
        const formattedDate = searchDate.toISOString().split('T')[0];

        // Get all property IDs for batch query
        const propertyIds = filteredProperties.map(p => p.id);

        if (propertyIds.length > 0) {
          // Fetch all bookings for the search date in a single query
          const existingBookings = await db
            .select()
            .from(bookings)
            .where(
              and(
                inArray(bookings.propertyId, propertyIds),
                eq(bookings.bookingDate, formattedDate)
              )
            );

          // Create a Set of property IDs that are unavailable
          const unavailablePropertyIds = new Set<number>();
          existingBookings.forEach(booking => {
            // Any existing booking makes the property unavailable for full_day
            // Morning bookings or full_day bookings make it unavailable for morning
            unavailablePropertyIds.add(booking.propertyId);
          });

          // Filter out unavailable properties
          filteredProperties = filteredProperties.filter(property => 
            !unavailablePropertyIds.has(property.id)
          );
        }
      }

      console.log(`🔍 [DEBUG] Found ${filteredProperties.length} properties after applying all filters`);
      console.log(`🔍 [DEBUG] Query conditions applied:`, conditions.length);
      console.log(`🔍 [DEBUG] Featured filter:`, featured);
      console.log(`🔍 [DEBUG] Location filter:`, location);
      console.log(`🔍 [DEBUG] Date filter:`, date);
      console.log(`🔍 [DEBUG] Price filters:`, { minPrice, maxPrice });
      console.log(`🔍 [DEBUG] Amenities filter:`, amenities);
      
      return filteredProperties;
    } catch (error) {
      console.error("❌ [ERROR] Error fetching properties:", error);
      console.error(error);
      return [];
    }
  }

  async getProperty(id: number): Promise<Property | undefined> {
    const [property] = await db
      .select()
      .from(properties)
      .where(eq(properties.id, id));
    return property;
  }

  async getPropertiesByOwner(ownerId: number): Promise<Property[]> {
    return db
      .select()
      .from(properties)
      .where(eq(properties.ownerId, ownerId))
      .orderBy(desc(properties.createdAt));
  }

  // ✅ PERFORMANCE: Batch method to get multiple properties by IDs in a single query
  async getPropertiesByIds(propertyIds: number[]): Promise<Property[]> {
    if (propertyIds.length === 0) return [];
    
    return db
      .select()
      .from(properties)
      .where(inArray(properties.id, propertyIds))
      .orderBy(desc(properties.createdAt));
  }

  async createProperty(property: any): Promise<Property> {
    try {
      // Convert property data to a format Drizzle can work with
      const propertyData = {
        ownerId: property.ownerId,
        title: property.title,
        description: property.description,
        location: property.location,
        halfDayPrice: property.halfDayPrice,
        fullDayPrice: property.fullDayPrice,
        weekdayHalfDayPrice: property.weekdayHalfDayPrice,
        weekdayFullDayPrice: property.weekdayFullDayPrice,
        weekendHalfDayPrice: property.weekendHalfDayPrice,
        weekendFullDayPrice: property.weekendFullDayPrice,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        amenities: property.amenities as any, // Handle JSON types
        images: property.images as any, // Handle JSON types
        videos: property.videos as any || [], // Handle JSON types
        status: property.status || 'active',
        featured: property.featured === undefined ? false : property.featured
      };

      const [newProperty] = await db
        .insert(properties)
        .values(propertyData)
        .returning();

      if (!newProperty) {
        throw new Error('Failed to create property');
      }

      return newProperty;
    } catch (error) {
      console.error("Error creating property:", error);
      throw error;
    }
  }

  async updateProperty(id: number, property: any): Promise<Property | undefined> {
    try {
      // First verify property exists
      const existingProperty = await this.getProperty(id);
      if (!existingProperty) {
        console.error(`Cannot update: Property with ID ${id} not found`);
        return undefined;
      }

      // Build update object
      const updateData: Record<string, any> = {};

      // Only include fields that need updating
      if (property.title !== undefined) updateData.title = property.title;
      if (property.description !== undefined) updateData.description = property.description;
      if (property.location !== undefined) updateData.location = property.location;
      if (property.halfDayPrice !== undefined) updateData.halfDayPrice = property.halfDayPrice;
      if (property.fullDayPrice !== undefined) updateData.fullDayPrice = property.fullDayPrice;
      if (property.weekdayHalfDayPrice !== undefined) updateData.weekdayHalfDayPrice = property.weekdayHalfDayPrice;
      if (property.weekdayFullDayPrice !== undefined) updateData.weekdayFullDayPrice = property.weekdayFullDayPrice;
      if (property.weekendHalfDayPrice !== undefined) updateData.weekendHalfDayPrice = property.weekendHalfDayPrice;
      if (property.weekendFullDayPrice !== undefined) updateData.weekendFullDayPrice = property.weekendFullDayPrice;
      if (property.bedrooms !== undefined) updateData.bedrooms = property.bedrooms;
      if (property.bathrooms !== undefined) updateData.bathrooms = property.bathrooms;
      if (property.status !== undefined) updateData.status = property.status;
      if (property.featured !== undefined) updateData.featured = property.featured;

      // Handle JSON fields properly
      if (property.amenities !== undefined) {
        updateData.amenities = property.amenities;
      }

      if (property.images !== undefined) {
        updateData.images = property.images;
      }

      if (property.videos !== undefined) {
        updateData.videos = property.videos;
      }

      // Only perform update if there are changes
      if (Object.keys(updateData).length === 0) {
        return existingProperty;
      }

      const [updatedProperty] = await db
        .update(properties)
        .set(updateData)
        .where(eq(properties.id, id))
        .returning();

      return updatedProperty;
    } catch (error) {
      console.error("Error updating property:", error);
      return undefined;
    }
  }

  async deleteProperty(id: number): Promise<boolean> {
    try {
      // First check if the property exists
      const property = await this.getProperty(id);
      if (!property) {
        console.error(`Property with ID ${id} not found for deletion`);
        return false;
      }

      await db
        .delete(properties)
        .where(eq(properties.id, id));

      // If no error was thrown, consider it successful
      return true;
    } catch (error) {
      console.error("Error deleting property:", error);
      return false;
    }
  }

  // Booking operations
  async getBookings(userId: number): Promise<Booking[]> {
    return db
      .select()
      .from(bookings)
      .where(eq(bookings.userId, userId))
      .orderBy(desc(bookings.createdAt));
  }

  async getBookingsWithProperty(userId: number): Promise<(Booking & { property: Property })[]> {
    const result = await db
      .select()
      .from(bookings)
      .innerJoin(properties, eq(bookings.propertyId, properties.id))
      .where(eq(bookings.userId, userId))
      .orderBy(desc(bookings.createdAt));

    return result.map(row => ({
      ...row.bookings,
      property: row.properties
    }));
  }

  async getBookingsByProperty(propertyId: number): Promise<Booking[]> {
    return db
      .select()
      .from(bookings)
      .where(eq(bookings.propertyId, propertyId))
      .orderBy(desc(bookings.createdAt));
  }

  // ✅ PERFORMANCE: Batch method to get bookings for multiple properties in a single query
  // This solves the N+1 query problem by fetching all bookings with property and guest data at once
  async getBookingsByPropertyIds(propertyIds: number[]): Promise<Record<number, (Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } })[]>> {
    if (propertyIds.length === 0) return {};

    // Single optimized query to fetch all bookings for all properties
    const result = await db
      .select({
        // Booking fields
        bookingId: bookings.id,
        propertyId: bookings.propertyId,
        userId: bookings.userId,
        bookingDate: bookings.bookingDate,
        bookingType: bookings.bookingType,
        guests: bookings.guests,
        totalPrice: bookings.totalPrice,
        status: bookings.status,
        specialRequests: bookings.specialRequests,
        createdAt: bookings.createdAt,
        
        // Property fields
        propertyTitle: properties.title,
        propertyDescription: properties.description,
        propertyLocation: properties.location,
        propertyHalfDayPrice: properties.halfDayPrice,
        propertyFullDayPrice: properties.fullDayPrice,
        propertyBedrooms: properties.bedrooms,
        propertyBathrooms: properties.bathrooms,
        propertyAmenities: properties.amenities,
        propertyImages: properties.images,
        propertyStatus: properties.status,
        propertyFeatured: properties.featured,
        
        // Guest fields
        guestId: users.id,
        guestFullName: users.fullName,
        guestUsername: users.username,
        guestEmail: users.email,
        guestPhone: users.phone
      })
      .from(bookings)
      .innerJoin(properties, eq(bookings.propertyId, properties.id))
      .innerJoin(users, eq(bookings.userId, users.id))
      .where(inArray(bookings.propertyId, propertyIds))
      .orderBy(desc(bookings.createdAt));

    // Group bookings by property ID
    const bookingsByProperty: Record<number, (Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } })[]> = {};

    // Initialize empty arrays for all requested property IDs
    propertyIds.forEach(id => {
      bookingsByProperty[id] = [];
    });

    // Group the results
    result.forEach(row => {
      const booking = {
        id: row.bookingId,
        propertyId: row.propertyId,
        userId: row.userId,
        bookingDate: row.bookingDate,
        bookingType: row.bookingType,
        guests: row.guests,
        totalPrice: row.totalPrice,
        status: row.status,
        specialRequests: row.specialRequests,
        createdAt: row.createdAt,
        property: {
          id: row.propertyId,
          title: row.propertyTitle,
          description: row.propertyDescription,
          location: row.propertyLocation,
          halfDayPrice: row.propertyHalfDayPrice,
          fullDayPrice: row.propertyFullDayPrice,
          bedrooms: row.propertyBedrooms,
          bathrooms: row.propertyBathrooms,
          amenities: row.propertyAmenities,
          images: row.propertyImages,
          status: row.propertyStatus,
          featured: row.propertyFeatured,
          ownerId: 0, // Will be filled by the caller
          createdAt: new Date(), // Will be filled by the caller
        },
        guest: {
          id: row.guestId,
          fullName: row.guestFullName,
          username: row.guestUsername,
          email: row.guestEmail,
          phone: row.guestPhone
        }
      } as Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } };

      bookingsByProperty[row.propertyId].push(booking);
    });

    return bookingsByProperty;
  }

  async getBookingsByOwner(ownerId: number): Promise<(Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } })[]> {
    // Use JOIN to fetch bookings with property and guest data in a single query
    const result = await db
      .select({
        // Booking fields
        bookingId: bookings.id,
        propertyId: bookings.propertyId,
        userId: bookings.userId,
        bookingDate: bookings.bookingDate,
        bookingType: bookings.bookingType,
        guests: bookings.guests,
        totalPrice: bookings.totalPrice,
        status: bookings.status,
        specialRequests: bookings.specialRequests,
        createdAt: bookings.createdAt,
        // Payment fields
        paymentStatus: bookings.paymentStatus,
        advanceAmount: bookings.advanceAmount,
        remainingAmount: bookings.remainingAmount,
        gstAmount: bookings.gstAmount,
        paymentDueDate: bookings.paymentDueDate,
        paymentExpiry: bookings.paymentExpiry,
        // Property fields
        propertyTitle: properties.title,
        propertyDescription: properties.description,
        propertyLocation: properties.location,
        propertyHalfDayPrice: properties.halfDayPrice,
        propertyFullDayPrice: properties.fullDayPrice,
        propertyBedrooms: properties.bedrooms,
        propertyBathrooms: properties.bathrooms,
        propertyAmenities: properties.amenities,
        propertyImages: properties.images,
        propertyStatus: properties.status,
        propertyFeatured: properties.featured,
        propertyLatitude: properties.latitude,
        propertyLongitude: properties.longitude,
        propertyOwnerId: properties.ownerId,
        propertyCreatedAt: properties.createdAt,
        // Guest fields
        guestId: users.id,
        guestFullName: users.fullName,
        guestUsername: users.username,
        guestEmail: users.email,
        guestPhone: users.phone,
      })
      .from(bookings)
      .innerJoin(properties, eq(bookings.propertyId, properties.id))
      .innerJoin(users, eq(bookings.userId, users.id))
      .where(eq(properties.ownerId, ownerId))
      .orderBy(desc(bookings.createdAt));

    return result.map(row => ({
      id: row.bookingId,
      propertyId: row.propertyId,
      userId: row.userId,
      bookingDate: row.bookingDate,
      bookingType: row.bookingType,
      guests: row.guests,
      totalPrice: row.totalPrice,
      status: row.status,
      specialRequests: row.specialRequests,
      createdAt: row.createdAt,
      // Payment fields
      paymentStatus: row.paymentStatus,
      advanceAmount: row.advanceAmount,
      remainingAmount: row.remainingAmount,
      gstAmount: row.gstAmount,
      paymentDueDate: row.paymentDueDate,
      paymentExpiry: row.paymentExpiry,
      property: {
        id: row.propertyId,
        title: row.propertyTitle,
        description: row.propertyDescription,
        location: row.propertyLocation,
        halfDayPrice: row.propertyHalfDayPrice,
        fullDayPrice: row.propertyFullDayPrice,
        weekdayHalfDayPrice: row.propertyHalfDayPrice,
        weekdayFullDayPrice: row.propertyFullDayPrice,
        weekendHalfDayPrice: row.propertyHalfDayPrice,
        weekendFullDayPrice: row.propertyFullDayPrice,
        bedrooms: row.propertyBedrooms,
        bathrooms: row.propertyBathrooms,
        amenities: row.propertyAmenities,
        images: row.propertyImages,
        videos: [], // Default empty array for videos
        status: row.propertyStatus,
        featured: row.propertyFeatured,
        latitude: row.propertyLatitude,
        longitude: row.propertyLongitude,
        ownerId: row.propertyOwnerId,
        createdAt: row.propertyCreatedAt,
      },
      guest: {
        id: row.guestId,
        fullName: row.guestFullName,
        username: row.guestUsername,
        email: row.guestEmail,
        phone: row.guestPhone,
      }
    }));
  }

  async getBooking(id: number): Promise<Booking | undefined> {
    const [booking] = await db
      .select()
      .from(bookings)
      .where(eq(bookings.id, id));
    return booking;
  }

  async checkAvailability(propertyId: number, date: Date, type: 'morning' | 'full_day'): Promise<boolean> {
    try {
      // Format the date to match the stored format (ISO string without time)
      const formattedDate = date.toISOString().split('T')[0];
      
      // Query existing ACTIVE bookings for this property on this date
      // Only check confirmed and pending_payment bookings (ignore cancelled, failed, expired)
      const existingBookings = await db
        .select()
        .from(bookings)
        .where(
          and(
            eq(bookings.propertyId, propertyId),
            eq(bookings.bookingDate, formattedDate),
            or(
              eq(bookings.status, 'confirmed'),
              eq(bookings.status, 'pending_payment')
            )
          )
        );

      // Check if the requested time slot is available
      if (existingBookings.length === 0) {
        // No active bookings found, the property is available
        return true;
      }
      
      // For full_day bookings, any existing active booking makes the property unavailable
      if (type === 'full_day') {
        return false;
      }

      // For morning bookings, only check if there are existing morning or full_day bookings
      return !existingBookings.some(booking => 
        booking.bookingType === 'morning' || booking.bookingType === 'full_day'
      );
    } catch (error) {
      console.error("Error checking availability:", error);
      return false; // Default to unavailable on error
    }
  }

  async createBooking(booking: any): Promise<Booking> {
    try {
      // Create a safely formatted booking object
      // Note: We're accepting either string or Date for bookingDate, which provides flexibility
      const bookingData = {
        propertyId: booking.propertyId,
        userId: booking.userId,
        bookingDate: booking.bookingDate,
        bookingType: booking.bookingType,
        guests: booking.guests,
        totalPrice: booking.totalPrice,
        status: booking.status || 'confirmed'
      };

      const [newBooking] = await db
        .insert(bookings)
        .values(bookingData)
        .returning();
      return newBooking;
    } catch (error) {
      console.error("Error creating booking:", error);
      
      // Check if this is a unique constraint violation (duplicate booking)
      if (error instanceof Error && (
        error.message.includes('bookings_no_duplicates_idx') ||
        error.message.includes('bookings_fullday_exclusive_idx') ||
        error.message.includes('duplicate key value')
      )) {
        throw new Error('This time slot is no longer available. Please select a different date or time.');
      }
      
      throw error;
    }
  }

  // NEW: Atomic booking creation with availability check
  async createBookingAtomic(booking: any): Promise<Booking> {
    try {
      // Use a database transaction with SERIALIZABLE isolation to ensure atomicity and prevent race conditions
      const result = await db.transaction(async (tx) => {
        // Set transaction isolation level to SERIALIZABLE for maximum consistency
        await tx.execute(sql`SET TRANSACTION ISOLATION LEVEL SERIALIZABLE`);
        
        // Re-check availability within the transaction
        const formattedDate = booking.bookingDate;
        
        // Query existing ACTIVE bookings for this property on this date
        const existingBookings = await tx
          .select()
          .from(bookings)
          .where(
            and(
              eq(bookings.propertyId, booking.propertyId),
              eq(bookings.bookingDate, formattedDate),
              or(
                eq(bookings.status, 'confirmed'),
                eq(bookings.status, 'pending_payment')
              )
            )
          );
        
        // Check availability logic (same as checkAvailability but in transaction)
        if (existingBookings.length > 0) {
          // For full_day bookings, any existing active booking makes the property unavailable
          if (booking.bookingType === 'full_day') {
            throw new Error('This date is no longer available for full day booking.');
          }
          
          // For morning bookings, check if there are existing morning or full_day bookings
          const hasConflict = existingBookings.some(existingBooking => 
            existingBooking.bookingType === 'morning' || existingBooking.bookingType === 'full_day'
          );
          
          if (hasConflict) {
            throw new Error('This time slot is no longer available.');
          }
        }

        // If we reach here, the slot is available - create the booking
        const bookingData = {
          propertyId: booking.propertyId,
          userId: booking.userId,
          bookingDate: booking.bookingDate,
          bookingType: booking.bookingType,
          guests: booking.guests,
          totalPrice: booking.totalPrice,
          status: booking.status || 'confirmed'
        };

        const [newBooking] = await tx
          .insert(bookings)
          .values(bookingData)
          .returning();
        
        return newBooking;
      });

      return result;
    } catch (error) {
      console.error("Error creating atomic booking:", error);
      throw error;
    }
  }

  async updateBookingStatus(id: number, status: string): Promise<Booking | undefined> {
    try {
      console.log(`🔄 [BOOKING_STATUS] Attempting to update booking ${id} from status to: ${status}`);
      
      // First verify that the booking exists
      const existingBooking = await this.getBooking(id);
      if (!existingBooking) {
        console.error(`❌ [BOOKING_STATUS] Cannot update status: Booking with ID ${id} not found`);
        return undefined;
      }
      
      console.log(`📋 [BOOKING_STATUS] Current booking status: ${existingBooking.status} -> Updating to: ${status}`);
      
      const [updatedBooking] = await db
        .update(bookings)
        .set({ status } as any)
        .where(eq(bookings.id, id))
        .returning();
      
      if (updatedBooking) {
        console.log(`✅ [BOOKING_STATUS] Successfully updated booking ${id} to status: ${updatedBooking.status}`);
      } else {
        console.error(`❌ [BOOKING_STATUS] Failed to update booking ${id} - no booking returned from update query`);
      }
      
      return updatedBooking;
    } catch (error) {
      console.error(`❌ [BOOKING_STATUS] Error updating booking status for ID ${id}:`, error);
      return undefined;
    }
  }

  // Review operations
  async getReviews(propertyId: number): Promise<(Review & { user: User })[]> {
    try {
      // Use JOIN to fetch reviews with user data in a single query
      const reviewResults = await db
        .select()
        .from(reviews)
        .innerJoin(users, eq(reviews.userId, users.id))
        .where(eq(reviews.propertyId, propertyId))
        .orderBy(desc(reviews.createdAt));

      return reviewResults.map(row => ({
        ...row.reviews,
        user: row.users
      }));
    } catch (error) {
      console.error("Error fetching property reviews:", error);
      return [];
    }
  }

  async getUserReviews(userId: number): Promise<(Review & { property: Property })[]> {
    try {
      // Use JOIN to fetch reviews with property data in a single query
      const reviewResults = await db
        .select()
        .from(reviews)
        .innerJoin(properties, eq(reviews.propertyId, properties.id))
        .where(eq(reviews.userId, userId))
        .orderBy(desc(reviews.createdAt));

      return reviewResults.map(row => ({
        ...row.reviews,
        property: row.properties
      }));
    } catch (error) {
      console.error("Error fetching user reviews:", error);
      return [];
    }
  }

  async getReview(id: number): Promise<Review | undefined> {
    try {
      const [review] = await db
        .select()
        .from(reviews)
        .where(eq(reviews.id, id));
      return review;
    } catch (error) {
      console.error("Error fetching review:", error);
      return undefined;
    }
  }

  async createReview(review: any): Promise<Review> {
    try {
      const reviewData = {
        propertyId: review.propertyId,
        userId: review.userId,
        rating: review.rating as "1" | "2" | "3" | "4" | "5",
        comment: review.comment
      };

      const [newReview] = await db
        .insert(reviews)
        .values(reviewData)
        .returning();
      return newReview;
    } catch (error) {
      console.error("Error creating review:", error);
      throw error;
    }
  }

  async updateReview(id: number, reviewUpdate: Partial<InsertReview>): Promise<Review | undefined> {
    try {
      const [updatedReview] = await db
        .update(reviews)
        .set(reviewUpdate)
        .where(eq(reviews.id, id))
        .returning();
      return updatedReview;
    } catch (error) {
      console.error("Error updating review:", error);
      return undefined;
    }
  }

  async addOwnerResponse(id: number, response: string): Promise<Review | undefined> {
    try {
      const [updatedReview] = await db
        .update(reviews)
        .set({
          response: response
        } as any)
        .where(eq(reviews.id, id))
        .returning();
      return updatedReview;
    } catch (error) {
      console.error("Error adding owner response:", error);
      return undefined;
    }
  }

  async deleteReview(id: number): Promise<boolean> {
    try {
      await db
        .delete(reviews)
        .where(eq(reviews.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting review:", error);
      return false;
    }
  }

  async getPropertyAverageRating(propertyId: number): Promise<number> {
    try {
      // Get all reviews for this property
      const propertyReviews = await db
        .select()
        .from(reviews)
        .where(eq(reviews.propertyId, propertyId));

      // Calculate average rating
      if (propertyReviews.length === 0) {
        return 0;
      }

      const sum = propertyReviews.reduce((total, review) => {
        return total + parseInt(review.rating);
      }, 0);

      return Number((sum / propertyReviews.length).toFixed(1));
    } catch (error) {
      console.error("Error calculating average rating:", error);
      return 0;
    }
  }

  // Owner Interest operations
  async createOwnerInterest(ownerInterest: InsertOwnerInterest): Promise<OwnerInterestRequest> {
    try {
      const [newOwnerInterest] = await db
        .insert(ownerInterestRequests)
        .values(ownerInterest as any)
        .returning();
      return newOwnerInterest;
    } catch (error) {
      console.error("Error creating owner interest request:", error);
      throw error;
    }
  }

  async getOwnerInterests(): Promise<OwnerInterestRequest[]> {
    try {
      return await db
        .select()
        .from(ownerInterestRequests)
        .orderBy(desc(ownerInterestRequests.createdAt));
    } catch (error) {
      console.error("Error fetching owner interest requests:", error);
      return [];
    }
  }

  async getOwnerInterest(id: number): Promise<OwnerInterestRequest | undefined> {
    try {
      const [ownerInterest] = await db
        .select()
        .from(ownerInterestRequests)
        .where(eq(ownerInterestRequests.id, id));
      return ownerInterest;
    } catch (error) {
      console.error("Error fetching owner interest request:", error);
      return undefined;
    }
  }

  async updateOwnerInterestStatus(id: number, status: string): Promise<OwnerInterestRequest | undefined> {
    try {
      const [updatedOwnerInterest] = await db
        .update(ownerInterestRequests)
        .set({ status } as any)
        .where(eq(ownerInterestRequests.id, id))
        .returning();
      return updatedOwnerInterest;
    } catch (error) {
      console.error("Error updating owner interest status:", error);
      return undefined;
    }
  }

  // Performance optimization methods
  async getUniqueLocations(): Promise<string[]> {
    try {
      const results = await db
        .selectDistinct({ location: properties.location })
        .from(properties)
        .where(eq(properties.status, 'active'));

      return results
        .map(r => r.location)
        .filter(Boolean)
        .sort();
    } catch (error) {
      console.error("Error fetching unique locations:", error);
      return [];
    }
  }

  async getPriceStatistics(): Promise<{ min: number; max: number; avg: number; median: number }> {
    try {
      // Get price statistics for both half-day and full-day prices
      const halfDayStats = await db
        .select({
          min: sql<number>`MIN(${properties.halfDayPrice})`,
          max: sql<number>`MAX(${properties.halfDayPrice})`,
          avg: sql<number>`AVG(${properties.halfDayPrice})`,
        })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const fullDayStats = await db
        .select({
          min: sql<number>`MIN(${properties.fullDayPrice})`,
          max: sql<number>`MAX(${properties.fullDayPrice})`,
          avg: sql<number>`AVG(${properties.fullDayPrice})`,
        })
        .from(properties)
        .where(eq(properties.status, 'active'));

      // Get median (using both price types)
      const allPrices = await db
        .select({
          halfDay: properties.halfDayPrice,
          fullDay: properties.fullDayPrice
        })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const flatPrices = allPrices.flatMap(p => [p.halfDay, p.fullDay]).sort((a, b) => a - b);
      const median = flatPrices.length > 0 
        ? flatPrices[Math.floor(flatPrices.length / 2)]
        : 0;

      return {
        min: Math.min(halfDayStats[0]?.min || 0, fullDayStats[0]?.min || 0),
        max: Math.max(halfDayStats[0]?.max || 0, fullDayStats[0]?.max || 0),
        avg: Math.round(((halfDayStats[0]?.avg || 0) + (fullDayStats[0]?.avg || 0)) / 2),
        median
      };
    } catch (error) {
      console.error("Error fetching price statistics:", error);
      return { min: 0, max: 0, avg: 0, median: 0 };
    }
  }

  async getAvailableAmenities(): Promise<string[]> {
    try {
      const results = await db
        .select({ amenities: properties.amenities })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const allAmenities = new Set<string>();

      results.forEach(result => {
        if (Array.isArray(result.amenities)) {
          result.amenities.forEach((amenity: string) => {
            if (amenity && typeof amenity === 'string') {
              allAmenities.add(amenity.trim().toLowerCase());
            }
          });
        }
      });

      return Array.from(allAmenities).sort();
    } catch (error) {
      console.error("Error fetching available amenities:", error);
      return [];
    }
  }

  async getReviewsByProperty(propertyId: number): Promise<any[]> {
    return await db
      .select({
        id: reviews.id,
        rating: reviews.rating,
        comment: reviews.comment,
        response: reviews.response,
        createdAt: reviews.createdAt,
        updatedAt: reviews.updatedAt,
        user: {
          id: users.id,
          fullName: users.fullName,
          username: users.username
        }
      })
      .from(reviews)
      .leftJoin(users, eq(reviews.userId, users.id))
      .where(eq(reviews.propertyId, propertyId))
      .orderBy(desc(reviews.createdAt));
  }

  async getReviewByBookingId(bookingId: number): Promise<any> {
    const result = await db
      .select({
        id: reviews.id,
        rating: reviews.rating,
        comment: reviews.comment,
        response: reviews.response,
        createdAt: reviews.createdAt,
        updatedAt: reviews.updatedAt
      })
      .from(reviews)
      .where(eq(reviews.bookingId, bookingId))
      .limit(1);

    return result[0] || null;
  }
  async cleanupStaleBookings(userId: number, cutoffTime: Date): Promise<void> {
    try {
      await db.transaction(async (tx) => {
        // First, find all stale bookings for this user
        const staleBookings = await tx
          .select({ id: bookings.id })
          .from(bookings)
          .where(
            and(
              eq(bookings.userId, userId),
              eq(bookings.status, 'pending_payment'),
              lt(bookings.createdAt, cutoffTime)
            )
          );
        
        if (staleBookings.length === 0) {
          return;
        }

        const bookingIds = staleBookings.map(b => b.id);
        console.log(`[CLEANUP] User ${userId}: Found ${bookingIds.length} stale bookings to clean up: ${bookingIds.join(', ')}`);
        
        // Delete all related records in correct order (foreign key dependencies)
        
        // 1. Delete payment audit logs (references payment_orders)
        const auditResult = await tx
          .delete(paymentAuditLogs)
          .where(inArray(paymentAuditLogs.paymentOrderId, 
            tx.select({ id: paymentOrders.id })
              .from(paymentOrders)
              .where(inArray(paymentOrders.bookingId, bookingIds))
          ));
        console.log(`[CLEANUP] User ${userId}: Deleted ${auditResult.rowCount || 0} related payment audit logs`);
        
        // 2. Delete payment transactions (references payment_orders)
        const transactionResult = await tx
          .delete(paymentTransactions)
          .where(inArray(paymentTransactions.paymentOrderId,
            tx.select({ id: paymentOrders.id })
              .from(paymentOrders) 
              .where(inArray(paymentOrders.bookingId, bookingIds))
          ));
        console.log(`[CLEANUP] User ${userId}: Deleted ${transactionResult.rowCount || 0} related payment transactions`);
        
        // 3. Delete payment orders (references bookings)
        const paymentOrderResult = await tx
          .delete(paymentOrders)
          .where(inArray(paymentOrders.bookingId, bookingIds));
        console.log(`[CLEANUP] User ${userId}: Deleted ${paymentOrderResult.rowCount || 0} related payment orders`);
        
        // 4. Delete reviews (references bookings)
        const reviewResult = await tx
          .delete(reviews)
          .where(inArray(reviews.bookingId, bookingIds));
        console.log(`[CLEANUP] User ${userId}: Deleted ${reviewResult.rowCount || 0} related reviews`);
        
        // 5. Delete GST records (references bookings)
        const gstResult = await tx
          .delete(gstRecords)
          .where(inArray(gstRecords.bookingId, bookingIds));
        console.log(`[CLEANUP] User ${userId}: Deleted ${gstResult.rowCount || 0} related GST records`);
        
        // 6. Finally delete the bookings
        await tx
          .delete(bookings)
          .where(
            and(
              eq(bookings.userId, userId),
              eq(bookings.status, 'pending_payment'),
              lt(bookings.createdAt, cutoffTime)
            )
          );
        
        console.log(`Cleaned up ${bookingIds.length} stale pending_payment bookings for user ${userId}`);
      });
    } catch (error) {
      console.error("Error cleaning up stale bookings:", error);
      throw error;
    }
  }

  async cleanupAllStaleBookings(cutoffTime: Date): Promise<number> {
    try {
      // Use a transaction to ensure atomicity
      return await db.transaction(async (tx) => {
        // First, find all stale bookings
        const staleBookings = await tx
          .select({ id: bookings.id })
          .from(bookings)
          .where(
            and(
              eq(bookings.status, 'pending_payment'),
              lt(bookings.createdAt, cutoffTime)
            )
          );
        
        if (staleBookings.length === 0) {
          console.log(`[CLEANUP] No stale pending_payment bookings found older than ${cutoffTime.toISOString()}`);
          return 0;
        }

        const bookingIds = staleBookings.map(b => b.id);
        console.log(`[CLEANUP] Found ${bookingIds.length} stale bookings to clean up: ${bookingIds.join(', ')}`);

        // Delete all related records in correct order (foreign key dependencies)
        
        // 1. Delete payment audit logs (references payment_orders)
        const auditResult = await tx
          .delete(paymentAuditLogs)
          .where(inArray(paymentAuditLogs.paymentOrderId, 
            tx.select({ id: paymentOrders.id })
              .from(paymentOrders)
              .where(inArray(paymentOrders.bookingId, bookingIds))
          ));
        const deletedAuditCount = auditResult.rowCount || 0;
        console.log(`[CLEANUP] Deleted ${deletedAuditCount} related payment audit logs`);
        
        // 2. Delete payment transactions (references payment_orders)
        const transactionResult = await tx
          .delete(paymentTransactions)
          .where(inArray(paymentTransactions.paymentOrderId,
            tx.select({ id: paymentOrders.id })
              .from(paymentOrders) 
              .where(inArray(paymentOrders.bookingId, bookingIds))
          ));
        const deletedTransactionCount = transactionResult.rowCount || 0;
        console.log(`[CLEANUP] Deleted ${deletedTransactionCount} related payment transactions`);
        
        // 3. Delete payment orders (references bookings)
        const paymentOrderResult = await tx
          .delete(paymentOrders)
          .where(inArray(paymentOrders.bookingId, bookingIds));
        const deletedPaymentOrderCount = paymentOrderResult.rowCount || 0;
        console.log(`[CLEANUP] Deleted ${deletedPaymentOrderCount} related payment orders`);
        
        // 4. Delete reviews (references bookings)
        const reviewResult = await tx
          .delete(reviews)
          .where(inArray(reviews.bookingId, bookingIds));
        const deletedReviewCount = reviewResult.rowCount || 0;
        console.log(`[CLEANUP] Deleted ${deletedReviewCount} related reviews`);
        
        // 5. Delete GST records (references bookings)
        const gstResult = await tx
          .delete(gstRecords)
          .where(inArray(gstRecords.bookingId, bookingIds));
        const deletedGstCount = gstResult.rowCount || 0;
        console.log(`[CLEANUP] Deleted ${deletedGstCount} related GST records`);

        // 6. Finally delete the bookings
        const bookingResult = await tx
          .delete(bookings)
          .where(
            and(
              eq(bookings.status, 'pending_payment'),
              lt(bookings.createdAt, cutoffTime)
            )
          );
        
        const deletedBookingCount = bookingResult.rowCount || 0;
        console.log(`[CLEANUP] Removed ${deletedBookingCount} stale pending_payment bookings older than ${cutoffTime.toISOString()}`);
        
        return deletedBookingCount;
      });
    } catch (error) {
      console.error("Error cleaning up all stale bookings:", error);
      throw error;
    }
  }
}

export const storage = new DatabaseStorage();