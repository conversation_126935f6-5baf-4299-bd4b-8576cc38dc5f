/**
 * Review Repository Interface
 */

import { IRepository } from '../../core/interfaces/IRepository';
import { Review, InsertReview, User, Property } from '../../../shared/schema';

export interface ReviewWithUser extends Review {
  user: User;
}

export interface ReviewWithProperty extends Review {
  property: Property;
}

export interface ReviewStatistics {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: Record<string, number>;
}

export interface IReviewRepository extends IRepository<Review, number> {
  /**
   * Find reviews for a property with user details
   */
  findByPropertyWithUser(propertyId: number): Promise<ReviewWithUser[]>;

  /**
   * Find reviews by user with property details
   */
  findByUserWithProperty(userId: number): Promise<ReviewWithProperty[]>;

  /**
   * Find reviews by property
   */
  findByProperty(propertyId: number): Promise<Review[]>;

  /**
   * Find reviews by user
   */
  findByUser(userId: number): Promise<Review[]>;

  /**
   * Get average rating for property
   */
  getPropertyAverageRating(propertyId: number): Promise<number>;

  /**
   * Get review statistics for property
   */
  getPropertyReviewStats(propertyId: number): Promise<ReviewStatistics>;

  /**
   * Add owner response to review
   */
  addOwnerResponse(reviewId: number, response: string): Promise<Review | null>;

  /**
   * Check if user has already reviewed property
   */
  hasUserReviewedProperty(userId: number, propertyId: number): Promise<boolean>;

  /**
   * Get recent reviews across all properties
   */
  getRecentReviews(limit?: number): Promise<ReviewWithUser[]>;

  /**
   * Get reviews by rating
   */
  findByRating(rating: number, propertyId?: number): Promise<Review[]>;

  /**
   * Get reviews that need owner response
   */
  getPendingOwnerResponses(ownerId: number): Promise<ReviewWithProperty[]>;

  /**
   * Update review rating and comment
   */
  updateReview(reviewId: number, rating: number, comment: string): Promise<Review | null>;

  /**
   * Get review summary for multiple properties
   */
  getPropertiesReviewSummary(propertyIds: number[]): Promise<Record<number, ReviewStatistics>>;

  /**
   * Delete review (soft delete)
   */
  softDelete(reviewId: number): Promise<boolean>;

  /**
   * Get reviews with pagination
   */
  getPaginated(
    propertyId?: number,
    page?: number,
    limit?: number
  ): Promise<{
    reviews: ReviewWithUser[];
    total: number;
    hasMore: boolean;
  }>;
}