/**
 * Property Repository Interface
 */

import { IRepository } from '../../core/interfaces/IRepository';
import { Property, InsertProperty } from '../../../shared/schema';

export interface PropertySearchParams {
  featured?: boolean;
  location?: string;
  date?: Date;
  minPrice?: number;
  maxPrice?: number;
  amenities?: string[];
  limit?: number;
  offset?: number;
}

export interface PriceStatistics {
  min: number;
  max: number;
  avg: number;
  median: number;
}

export interface IPropertyRepository extends IRepository<Property, number> {
  /**
   * Search properties with advanced filtering
   */
  searchProperties(params: PropertySearchParams): Promise<Property[]>;

  /**
   * Find properties by owner
   */
  findByOwner(ownerId: number): Promise<Property[]>;

  /**
   * Find featured properties
   */
  findFeatured(limit?: number): Promise<Property[]>;

  /**
   * Get properties by location
   */
  findByLocation(location: string): Promise<Property[]>;

  /**
   * Get properties within price range
   */
  findByPriceRange(minPrice: number, maxPrice: number): Promise<Property[]>;

  /**
   * Get properties with specific amenities
   */
  findByAmenities(amenities: string[]): Promise<Property[]>;

  /**
   * Check property availability for a date
   */
  checkAvailability(propertyId: number, date: Date, bookingType: 'morning' | 'full_day'): Promise<boolean>;

  /**
   * Get unique locations
   */
  getUniqueLocations(): Promise<string[]>;

  /**
   * Get price statistics
   */
  getPriceStatistics(): Promise<PriceStatistics>;

  /**
   * Get available amenities
   */
  getAvailableAmenities(): Promise<string[]>;

  /**
   * Update property status
   */
  updateStatus(propertyId: number, status: string): Promise<boolean>;

  /**
   * Set property as featured
   */
  setFeatured(propertyId: number, featured: boolean): Promise<boolean>;

  /**
   * Get properties with pagination and search
   */
  searchWithPagination(
    params: PropertySearchParams & { page: number }
  ): Promise<{
    properties: Property[];
    total: number;
    hasMore: boolean;
  }>;
}