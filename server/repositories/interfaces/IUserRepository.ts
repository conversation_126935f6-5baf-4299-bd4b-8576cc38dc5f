/**
 * User Repository Interface
 */

import { IRepository } from '../../core/interfaces/IRepository';
import { User, InsertUser } from '../../../shared/schema';

export interface IUserRepository extends IRepository<User, number> {
  /**
   * Find user by email address
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Find user by phone number
   */
  findByPhone(phone: string): Promise<User | null>;

  /**
   * Check if email is already taken
   */
  isEmailTaken(email: string, excludeUserId?: number): Promise<boolean>;

  /**
   * Check if phone is already taken
   */
  isPhoneTaken(phone: string, excludeUserId?: number): Promise<boolean>;

  /**
   * Create user with hashed password
   */
  createWithHashedPassword(userData: InsertUser): Promise<User>;

  /**
   * Update user password
   */
  updatePassword(userId: number, hashedPassword: string): Promise<boolean>;

  /**
   * Get users with pagination
   */
  getPaginated(page: number, limit: number): Promise<{
    users: User[];
    total: number;
    hasMore: boolean;
  }>;
}