/**
 * Booking Repository Interface
 */

import { IRepository } from '../../core/interfaces/IRepository';
import { Booking, InsertBooking, Property } from '../../../shared/schema';

export interface BookingWithProperty extends Booking {
  property: Property;
}

export interface IBookingRepository extends IRepository<Booking, number> {
  /**
   * Find bookings by user
   */
  findByUser(userId: number): Promise<Booking[]>;

  /**
   * Find bookings by user with property details
   */
  findByUserWithProperty(userId: number): Promise<BookingWithProperty[]>;

  /**
   * Find bookings by property
   */
  findByProperty(propertyId: number): Promise<Booking[]>;

  /**
   * Find bookings by owner (property owner)
   */
  findByOwner(ownerId: number): Promise<BookingWithProperty[]>;

  /**
   * Find bookings for a specific date
   */
  findByDate(date: Date): Promise<Booking[]>;

  /**
   * Find bookings within date range
   */
  findByDateRange(startDate: Date, endDate: Date): Promise<Booking[]>;

  /**
   * Check if property is available for booking
   */
  checkPropertyAvailability(
    propertyId: number, 
    date: Date, 
    bookingType: 'morning' | 'full_day'
  ): Promise<boolean>;

  /**
   * Update booking status
   */
  updateStatus(bookingId: number, status: string): Promise<Booking | null>;

  /**
   * Get upcoming bookings for user
   */
  getUpcomingBookings(userId: number): Promise<BookingWithProperty[]>;

  /**
   * Get past bookings for user
   */
  getPastBookings(userId: number): Promise<BookingWithProperty[]>;

  /**
   * Get bookings by status
   */
  findByStatus(status: string): Promise<Booking[]>;

  /**
   * Cancel booking
   */
  cancelBooking(bookingId: number, reason?: string): Promise<boolean>;

  /**
   * Get booking statistics for property
   */
  getPropertyBookingStats(propertyId: number): Promise<{
    totalBookings: number;
    confirmedBookings: number;
    cancelledBookings: number;
    revenue: number;
  }>;

  /**
   * Get booking statistics for owner
   */
  getOwnerBookingStats(ownerId: number): Promise<{
    totalBookings: number;
    totalRevenue: number;
    averageBookingValue: number;
    bookingsByMonth: Array<{ month: string; count: number; revenue: number }>;
  }>;
}