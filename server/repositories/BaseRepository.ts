/**
 * Base Repository Implementation
 * Provides common repository functionality
 */

import { eq, and, desc, count, sql } from 'drizzle-orm';
import { db } from '../db';
import { IRepository, IQueryOptions, IFilterCriteria } from '../core/interfaces/IRepository';
import { ILogger } from '../core/interfaces/ILogger';

export abstract class BaseRepository<TEntity, TKey = number> implements IRepository<TEntity, TKey> {
  protected abstract tableName: string;
  protected abstract table: any;
  protected logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  async findById(id: TKey): Promise<TEntity | null> {
    try {
      const result = await db
        .select()
        .from(this.table)
        .where(eq(this.table.id, id))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error(`Error finding ${this.tableName} by ID`, error as Error, 'repository');
      throw error;
    }
  }

  async findBy(criteria: IFilterCriteria, options?: IQueryOptions): Promise<TEntity[]> {
    try {
      let query = db.select().from(this.table);

      // Apply where conditions
      if (Object.keys(criteria).length > 0) {
        const conditions = this.buildWhereConditions(criteria);
        if (conditions.length > 0) {
          query = query.where(and(...conditions)) as any;
        }
      }

      // Apply ordering
      if (options?.orderBy) {
        const orderField = this.table[options.orderBy];
        if (orderField) {
          query = options.orderDirection === 'asc' 
            ? query.orderBy(orderField) as any
            : query.orderBy(desc(orderField)) as any;
        }
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit) as any;
      }
      if (options?.offset) {
        query = query.offset(options.offset) as any;
      }

      return await query;
    } catch (error) {
      this.logger.error(`Error finding ${this.tableName} by criteria`, error as Error, 'repository');
      throw error;
    }
  }

  async findOne(criteria: IFilterCriteria): Promise<TEntity | null> {
    const results = await this.findBy(criteria, { limit: 1 });
    return results[0] || null;
  }

  async findAll(options?: IQueryOptions): Promise<TEntity[]> {
    return this.findBy({}, options);
  }

  async create(entity: Partial<TEntity>): Promise<TEntity> {
    try {
      const result = await db
        .insert(this.table)
        .values(entity)
        .returning();

      const created = (result as any)[0];
      if (!created) {
        throw new Error(`Failed to create ${this.tableName}`);
      }

      this.logger.debug(`Created ${this.tableName}`, 'repository', { id: (created as any).id });
      return created;
    } catch (error) {
      this.logger.error(`Error creating ${this.tableName}`, error as Error, 'repository');
      throw error;
    }
  }

  async update(id: TKey, updates: Partial<TEntity>): Promise<TEntity | null> {
    try {
      const result = await db
        .update(this.table)
        .set(updates)
        .where(eq(this.table.id, id))
        .returning();

      const updated = result[0] || null;
      if (updated) {
        this.logger.debug(`Updated ${this.tableName}`, 'repository', { id });
      }
      
      return updated;
    } catch (error) {
      this.logger.error(`Error updating ${this.tableName}`, error as Error, 'repository', { id });
      throw error;
    }
  }

  async delete(id: TKey): Promise<boolean> {
    try {
      const result = await db
        .delete(this.table)
        .where(eq(this.table.id, id));

      const deleted = (result.rowCount || 0) > 0;
      if (deleted) {
        this.logger.debug(`Deleted ${this.tableName}`, 'repository', { id });
      }
      
      return deleted;
    } catch (error) {
      this.logger.error(`Error deleting ${this.tableName}`, error as Error, 'repository', { id });
      throw error;
    }
  }

  async count(criteria?: IFilterCriteria): Promise<number> {
    try {
      let query = db.select({ count: count() }).from(this.table);

      if (criteria && Object.keys(criteria).length > 0) {
        const conditions = this.buildWhereConditions(criteria);
        if (conditions.length > 0) {
          query = query.where(and(...conditions)) as any;
        }
      }

      const result = await query;
      return (result as any)[0]?.count || 0;
    } catch (error) {
      this.logger.error(`Error counting ${this.tableName}`, error as Error, 'repository');
      throw error;
    }
  }

  async exists(criteria: IFilterCriteria): Promise<boolean> {
    const count = await this.count(criteria);
    return count > 0;
  }

  protected buildWhereConditions(criteria: IFilterCriteria): any[] {
    const conditions: any[] = [];

    for (const [key, value] of Object.entries(criteria)) {
      if (value !== undefined && value !== null) {
        const field = this.table[key];
        if (field) {
          if (Array.isArray(value)) {
            // Handle array values (IN clause)
            conditions.push(sql`${field} = ANY(${value})`);
          } else {
            conditions.push(eq(field, value));
          }
        }
      }
    }

    return conditions;
  }

  protected async executeInTransaction<T>(work: () => Promise<T>): Promise<T> {
    return db.transaction(async (tx) => {
      return work();
    });
  }
}