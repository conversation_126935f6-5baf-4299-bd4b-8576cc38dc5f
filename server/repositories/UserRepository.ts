/**
 * User Repository Implementation
 */

import { eq, and, ne } from 'drizzle-orm';
import bcrypt from 'bcrypt';
import { db } from '../db';
import { users, User, InsertUser } from '../../shared/schema';
import { BaseRepository } from './BaseRepository';
import { IUserRepository } from './interfaces/IUserRepository';
import { ILogger } from '../core/interfaces/ILogger';

export class UserRepository extends BaseRepository<User, number> implements IUserRepository {
  protected tableName = 'users';
  protected table = users;

  constructor(logger: ILogger) {
    super(logger);
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error('Error finding user by email', error as Error, 'UserRepository');
      throw error;
    }
  }

  async findByPhone(phone: string): Promise<User | null> {
    try {
      const result = await db
        .select()
        .from(users)
        .where(eq(users.phone, phone))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error('Error finding user by phone', error as Error, 'UserRepository');
      throw error;
    }
  }

  async isEmailTaken(email: string, excludeUserId?: number): Promise<boolean> {
    try {
      let query = db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.email, email));

      if (excludeUserId) {
        query = db.select({ id: users.id })
          .from(users)
          .where(and(eq(users.email, email), ne(users.id, excludeUserId)));
      }

      const result = await query.limit(1);
      return result.length > 0;
    } catch (error) {
      this.logger.error('Error checking if email is taken', error as Error, 'UserRepository');
      throw error;
    }
  }

  async isPhoneTaken(phone: string, excludeUserId?: number): Promise<boolean> {
    try {
      let query = db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.phone, phone));

      if (excludeUserId) {
        query = db.select({ id: users.id })
          .from(users)
          .where(and(eq(users.phone, phone), ne(users.id, excludeUserId)));
      }

      const result = await query.limit(1);
      return result.length > 0;
    } catch (error) {
      this.logger.error('Error checking if phone is taken', error as Error, 'UserRepository');
      throw error;
    }
  }

  async createWithHashedPassword(userData: InsertUser): Promise<User> {
    try {
      // Hash the password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      const userDataWithHashedPassword = {
        ...userData,
        password: hashedPassword,
        phone: userData.phone || null
      };

      const result = await db
        .insert(users)
        .values(userDataWithHashedPassword)
        .returning();

      const user = result[0];
      if (!user) {
        throw new Error('Failed to create user');
      }

      this.logger.audit('create', 'user', user.id, user.id, undefined, {
        email: user.email,
        role: user.role
      });

      return user;
    } catch (error) {
      this.logger.error('Error creating user with hashed password', error as Error, 'UserRepository');
      throw error;
    }
  }

  async updatePassword(userId: number, hashedPassword: string): Promise<boolean> {
    try {
      const result = await db
        .update(users)
        .set({ password: hashedPassword })
        .where(eq(users.id, userId));

      const updated = (result.rowCount || 0) > 0;
      
      if (updated) {
        this.logger.audit('update_password', 'user', userId, userId);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error updating user password', error as Error, 'UserRepository', { userId });
      throw error;
    }
  }

  async getPaginated(page: number, limit: number): Promise<{
    users: User[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      const offset = (page - 1) * limit;

      // Get total count
      const totalResult = await db
        .select({ count: users.id })
        .from(users);
      const total = totalResult.length;

      // Get paginated users
      const usersList = await db
        .select()
        .from(users)
        .limit(limit + 1) // Get one extra to check if there are more
        .offset(offset);

      const hasMore = usersList.length > limit;
      const actualUsers = hasMore ? usersList.slice(0, limit) : usersList;

      return {
        users: actualUsers,
        total,
        hasMore
      };
    } catch (error) {
      this.logger.error('Error getting paginated users', error as Error, 'UserRepository');
      throw error;
    }
  }
}