/**
 * Property Repository Implementation
 */

import { eq, and, desc, gte, lte, like, or, inArray, sql } from 'drizzle-orm';
import { db } from '../db';
import { properties, bookings, Property, InsertProperty } from '../../shared/schema';
import { BaseRepository } from './BaseRepository';
import { IPropertyRepository, PropertySearchParams, PriceStatistics } from './interfaces/IPropertyRepository';
import { ILogger } from '../core/interfaces/ILogger';

export class PropertyRepository extends BaseRepository<Property, number> implements IPropertyRepository {
  protected tableName = 'properties';
  protected table = properties;

  constructor(logger: ILogger) {
    super(logger);
  }

  async searchProperties(params: PropertySearchParams): Promise<Property[]> {
    try {
      const conditions = [];

      // Featured filter
      if (params.featured) {
        conditions.push(eq(properties.featured, true));
      }

      // Location search
      if (params.location && params.location.trim() !== '') {
        const searchTerm = `%${params.location.trim().toLowerCase()}%`;
        conditions.push(
          or(
            sql`LOWER(${properties.location}) LIKE ${searchTerm}`,
            sql`LOWER(${properties.title}) LIKE ${searchTerm}`,
            sql`LOWER(${properties.description}) LIKE ${searchTerm}`
          )
        );
      }

      // Price range filters
      if (params.minPrice !== undefined || params.maxPrice !== undefined) {
        const priceConditions = [];
        
        if (params.minPrice !== undefined && params.maxPrice !== undefined) {
          priceConditions.push(
            or(
              and(gte(properties.halfDayPrice, params.minPrice), lte(properties.halfDayPrice, params.maxPrice)),
              and(gte(properties.fullDayPrice, params.minPrice), lte(properties.fullDayPrice, params.maxPrice))
            )
          );
        } else if (params.minPrice !== undefined) {
          priceConditions.push(
            or(
              gte(properties.halfDayPrice, params.minPrice),
              gte(properties.fullDayPrice, params.minPrice)
            )
          );
        } else if (params.maxPrice !== undefined) {
          priceConditions.push(
            or(
              lte(properties.halfDayPrice, params.maxPrice),
              lte(properties.fullDayPrice, params.maxPrice)
            )
          );
        }
        
        if (priceConditions.length > 0) {
          conditions.push(...priceConditions);
        }
      }

      // Base query
      let query = db
        .select()
        .from(properties)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(properties.createdAt));

      // Apply pagination
      if (params.limit) {
        query = query.limit(params.limit) as any;
      }
      if (params.offset) {
        query = query.offset(params.offset) as any;
      }

      let filteredProperties = await query;

      // Amenities filtering (post-query for compatibility)
      if (params.amenities && params.amenities.length > 0) {
        filteredProperties = filteredProperties.filter(prop => {
          const propAmenities = prop.amenities as string[];
          return params.amenities!.some(amenity => 
            propAmenities.some(propAmenity => 
              propAmenity.toLowerCase().includes(amenity.toLowerCase())
            )
          );
        });
      }

      // Date availability filtering
      if (params.date) {
        const searchDate = new Date(params.date);
        searchDate.setHours(0, 0, 0, 0);
        const formattedDate = searchDate.toISOString().split('T')[0];
        
        const propertyIds = filteredProperties.map(p => p.id);
        
        if (propertyIds.length > 0) {
          const existingBookings = await db
            .select()
            .from(bookings)
            .where(
              and(
                inArray(bookings.propertyId, propertyIds),
                eq(bookings.bookingDate, formattedDate)
              )
            );
          
          const unavailablePropertyIds = new Set<number>();
          existingBookings.forEach(booking => {
            unavailablePropertyIds.add(booking.propertyId);
          });
          
          filteredProperties = filteredProperties.filter(property => 
            !unavailablePropertyIds.has(property.id)
          );
        }
      }

      return filteredProperties;
    } catch (error) {
      this.logger.error('Error searching properties', error as Error, 'PropertyRepository');
      throw error;
    }
  }

  async findByOwner(ownerId: number): Promise<Property[]> {
    return this.findBy({ ownerId }, { orderBy: 'createdAt', orderDirection: 'desc' });
  }

  async findFeatured(limit: number = 10): Promise<Property[]> {
    return this.findBy({ featured: true }, { limit, orderBy: 'createdAt', orderDirection: 'desc' });
  }

  async findByLocation(location: string): Promise<Property[]> {
    try {
      const searchTerm = `%${location.toLowerCase()}%`;
      
      const result = await db
        .select()
        .from(properties)
        .where(sql`LOWER(${properties.location}) LIKE ${searchTerm}`)
        .orderBy(desc(properties.createdAt));

      return result;
    } catch (error) {
      this.logger.error('Error finding properties by location', error as Error, 'PropertyRepository');
      throw error;
    }
  }

  async findByPriceRange(minPrice: number, maxPrice: number): Promise<Property[]> {
    try {
      const result = await db
        .select()
        .from(properties)
        .where(
          or(
            and(gte(properties.halfDayPrice, minPrice), lte(properties.halfDayPrice, maxPrice)),
            and(gte(properties.fullDayPrice, minPrice), lte(properties.fullDayPrice, maxPrice))
          )
        )
        .orderBy(desc(properties.createdAt));

      return result;
    } catch (error) {
      this.logger.error('Error finding properties by price range', error as Error, 'PropertyRepository');
      throw error;
    }
  }

  async findByAmenities(amenities: string[]): Promise<Property[]> {
    try {
      const allProperties = await this.findAll();
      
      return allProperties.filter(property => {
        const propAmenities = property.amenities as string[];
        return amenities.some(amenity => 
          propAmenities.some(propAmenity => 
            propAmenity.toLowerCase().includes(amenity.toLowerCase())
          )
        );
      });
    } catch (error) {
      this.logger.error('Error finding properties by amenities', error as Error, 'PropertyRepository');
      throw error;
    }
  }

  async checkAvailability(propertyId: number, date: Date, bookingType: 'morning' | 'full_day'): Promise<boolean> {
    try {
      const formattedDate = date.toISOString().split('T')[0];
      
      const existingBookings = await db
        .select()
        .from(bookings)
        .where(
          and(
            eq(bookings.propertyId, propertyId),
            eq(bookings.bookingDate, formattedDate)
          )
        );

      if (existingBookings.length === 0) {
        return true;
      }

      if (bookingType === 'full_day') {
        return false;
      }

      return !existingBookings.some(booking => 
        booking.bookingType === 'morning' || booking.bookingType === 'full_day'
      );
    } catch (error) {
      this.logger.error('Error checking property availability', error as Error, 'PropertyRepository');
      return false;
    }
  }

  async getUniqueLocations(): Promise<string[]> {
    try {
      const result = await db
        .selectDistinct({ location: properties.location })
        .from(properties)
        .where(eq(properties.status, 'active'));
      
      return result
        .map(r => r.location)
        .filter(Boolean)
        .sort();
    } catch (error) {
      this.logger.error('Error fetching unique locations', error as Error, 'PropertyRepository');
      return [];
    }
  }

  async getPriceStatistics(): Promise<PriceStatistics> {
    try {
      const halfDayStats = await db
        .select({
          min: sql<number>`MIN(${properties.halfDayPrice})`,
          max: sql<number>`MAX(${properties.halfDayPrice})`,
          avg: sql<number>`AVG(${properties.halfDayPrice})`,
        })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const fullDayStats = await db
        .select({
          min: sql<number>`MIN(${properties.fullDayPrice})`,
          max: sql<number>`MAX(${properties.fullDayPrice})`,
          avg: sql<number>`AVG(${properties.fullDayPrice})`,
        })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const allPrices = await db
        .select({
          halfDay: properties.halfDayPrice,
          fullDay: properties.fullDayPrice
        })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const flatPrices = allPrices.flatMap(p => [p.halfDay, p.fullDay]).sort((a, b) => a - b);
      const median = flatPrices.length > 0 
        ? flatPrices[Math.floor(flatPrices.length / 2)]
        : 0;

      return {
        min: Math.min(halfDayStats[0]?.min || 0, fullDayStats[0]?.min || 0),
        max: Math.max(halfDayStats[0]?.max || 0, fullDayStats[0]?.max || 0),
        avg: Math.round(((halfDayStats[0]?.avg || 0) + (fullDayStats[0]?.avg || 0)) / 2),
        median
      };
    } catch (error) {
      this.logger.error('Error fetching price statistics', error as Error, 'PropertyRepository');
      return { min: 0, max: 0, avg: 0, median: 0 };
    }
  }

  async getAvailableAmenities(): Promise<string[]> {
    try {
      const result = await db
        .select({ amenities: properties.amenities })
        .from(properties)
        .where(eq(properties.status, 'active'));

      const allAmenities = new Set<string>();
      
      result.forEach(result => {
        if (Array.isArray(result.amenities)) {
          result.amenities.forEach((amenity: string) => {
            if (amenity && typeof amenity === 'string') {
              allAmenities.add(amenity.trim().toLowerCase());
            }
          });
        }
      });

      return Array.from(allAmenities).sort();
    } catch (error) {
      this.logger.error('Error fetching available amenities', error as Error, 'PropertyRepository');
      return [];
    }
  }

  async updateStatus(propertyId: number, status: string): Promise<boolean> {
    try {
      const result = await this.update(propertyId, { status } as any);
      return result !== null;
    } catch (error) {
      this.logger.error('Error updating property status', error as Error, 'PropertyRepository');
      return false;
    }
  }

  async setFeatured(propertyId: number, featured: boolean): Promise<boolean> {
    try {
      const result = await this.update(propertyId, { featured } as any);
      return result !== null;
    } catch (error) {
      this.logger.error('Error setting property featured status', error as Error, 'PropertyRepository');
      return false;
    }
  }

  async searchWithPagination(params: PropertySearchParams & { page: number }): Promise<{
    properties: Property[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      const limit = params.limit || 10;
      const offset = ((params.page || 1) - 1) * limit;

      // Get total count with same filters (excluding pagination)
      const countParams = { ...params };
      delete countParams.limit;
      delete countParams.offset;
      
      const allProperties = await this.searchProperties(countParams);
      const total = allProperties.length;

      // Get paginated results
      const searchParams = { ...params, limit: limit + 1, offset };
      const properties = await this.searchProperties(searchParams);

      const hasMore = properties.length > limit;
      const actualProperties = hasMore ? properties.slice(0, limit) : properties;

      return {
        properties: actualProperties,
        total,
        hasMore
      };
    } catch (error) {
      this.logger.error('Error searching properties with pagination', error as Error, 'PropertyRepository');
      throw error;
    }
  }
}