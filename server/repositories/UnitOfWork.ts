/**
 * Unit of Work Implementation
 * Manages database transactions across multiple repositories
 */

import { db } from '../db';
import { IUnitOfWork } from '../core/interfaces/IRepository';
import { ILogger } from '../core/interfaces/ILogger';

export class UnitOfWork implements IUnitOfWork {
  private logger: ILogger;
  private isInTransaction = false;
  private currentTransaction: any = null;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  async beginTransaction(): Promise<void> {
    if (this.isInTransaction) {
      throw new Error('Transaction already in progress');
    }

    try {
      this.currentTransaction = await db.transaction(async (tx) => {
        this.isInTransaction = true;
        return tx;
      });
      
      this.logger.debug('Transaction started', 'UnitOfWork');
    } catch (error) {
      this.logger.error('Error starting transaction', error as Error, 'UnitOfWork');
      throw error;
    }
  }

  async commit(): Promise<void> {
    if (!this.isInTransaction || !this.currentTransaction) {
      throw new Error('No transaction in progress');
    }

    try {
      await this.currentTransaction.commit();
      this.isInTransaction = false;
      this.currentTransaction = null;
      
      this.logger.debug('Transaction committed', 'UnitOfWork');
    } catch (error) {
      this.logger.error('Error committing transaction', error as Error, 'UnitOfWork');
      await this.rollback();
      throw error;
    }
  }

  async rollback(): Promise<void> {
    if (!this.isInTransaction || !this.currentTransaction) {
      throw new Error('No transaction in progress');
    }

    try {
      await this.currentTransaction.rollback();
      this.isInTransaction = false;
      this.currentTransaction = null;
      
      this.logger.debug('Transaction rolled back', 'UnitOfWork');
    } catch (error) {
      this.logger.error('Error rolling back transaction', error as Error, 'UnitOfWork');
      throw error;
    }
  }

  async executeInTransaction<T>(work: () => Promise<T>): Promise<T> {
    return db.transaction(async (tx) => {
      const startTime = Date.now();
      this.logger.debug('Executing work in transaction', 'UnitOfWork');
      
      try {
        const result = await work();
        
        this.logger.performance('transaction_execution', startTime, undefined, {
          context: 'UnitOfWork'
        });
        
        return result;
      } catch (error) {
        this.logger.error('Error executing work in transaction', error as Error, 'UnitOfWork');
        throw error;
      }
    });
  }

  /**
   * Get current transaction status
   */
  isTransactionActive(): boolean {
    return this.isInTransaction;
  }

  /**
   * Execute multiple operations in a single transaction
   */
  async executeBatch<T>(operations: Array<() => Promise<any>>): Promise<T[]> {
    return this.executeInTransaction(async () => {
      const results: T[] = [];
      
      for (const operation of operations) {
        const result = await operation();
        results.push(result);
      }
      
      return results;
    });
  }

  /**
   * Save changes with automatic transaction management
   */
  async saveChanges<T>(work: () => Promise<T>): Promise<T> {
    if (this.isInTransaction) {
      // Already in transaction, just execute the work
      return work();
    }

    // Start new transaction
    return this.executeInTransaction(work);
  }
}