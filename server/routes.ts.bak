import express from "express";
import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import multer from "multer";
import { fileURLToPath } from "url";
import { dirname, join, extname } from "path";
import fs from "fs";
import crypto from "crypto";
import { emailService } from "./emailService";
import { cloudinaryService } from "./cloudinaryService";
import rateLimit from "express-rate-limit";
import cors from "cors";
import { config } from "./config";
import { 
  sanitizeSearchQuery, 
  sanitizeIdParam, 
  rateLimit as customRateLimit,
  preventSQLInjection 
} from "./middlewares/sanitization";
import {
  userRegisterSchema,
  userLoginSchema,
  propertyFormSchema,
  bookingFormSchema,
  reviewFormSchema,
  reviewResponseSchema,
} from "../shared/schema.ts";
import { ZodError } from "zod";
import { fromZodError } from "zod-validation-error";
const UPLOADS_DIR = join(process.cwd(), "uploads");

// Rate limiting configuration for security
const MAX_LOGIN_ATTEMPTS = 5;
const loginAttemptsMap = new Map<string, number>();
const blacklistedTokens = new Set<string>(); // For storing revoked tokens

// Rate limiting middleware configurations
const generalApiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: "Too many requests from this IP, please try again later.",
    retryAfter: 15 * 60
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Skip successful requests to /api/health
  skip: (req) => req.path === '/api/health'
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit auth requests to 20 per windowMs
  message: {
    error: "Too many authentication attempts from this IP, please try again later.",
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const bookingLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // Limit booking requests to 5 per minute
  message: {
    error: "Too many booking attempts from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// OTP-specific rate limiters for security
const otpSendLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 5, // Limit OTP send requests to 5 per 10 minutes
  message: {
    error: "Too many OTP requests from this IP. Please wait 10 minutes before trying again.",
    retryAfter: 10 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Rate limit by IP + identifier to prevent abuse of different phone/email
    const identifier = req.body?.identifier || req.ip;
    return `${req.ip}:${identifier}`;
  }
});

const otpVerifyLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // Limit OTP verification attempts to 10 per 5 minutes
  message: {
    error: "Too many verification attempts from this IP. Please wait 5 minutes before trying again.",
    retryAfter: 5 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Rate limit by IP + identifier for verification attempts
    const identifier = req.body?.identifier || req.ip;
    return `${req.ip}:${identifier}`;
  }
});

const searchLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute  
  max: 30, // Limit search requests to 30 per minute
  message: {
    error: "Too many search requests from this IP, please try again later.",
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Ensure uploads directory exists
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

// Configure multer for memory storage (for Cloudinary upload)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max file size
    files: 10 // Maximum 10 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Check MIME type
    const allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Only JPEG, PNG, GIF, and WebP images are allowed"));
    }
  },
});

// Authentication middleware with enhanced security
const authenticate = (req: Request, res: Response, next: NextFunction) => {
  // Try to get token from Authorization header first, then from cookie
  const authHeader = req.headers.authorization;
  const cookieToken = req.cookies?.auth_token;

  // No token found
  if (!authHeader && !cookieToken) {
    return return res.status(401).json({ message: "Authentication required" });
  }

  // Get token from appropriate source
  const token = authHeader ? authHeader.split(" ")[1] : cookieToken;

  try {
    // Check if token is blacklisted
    if (blacklistedTokens.has(token)) {
      return return res.status(401).json({ message: "Token has been revoked" });
    }

    // Verify token with full options
    const decoded = jwt.verify(token, config.jwt.secret, {
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithms: ['HS256'] // Explicitly limit algorithms
    }) as { userId: number; role: string; jti?: string };

    // Assign user data to request
    req.user = {
      userId: decoded.userId,
      role: decoded.role
    };

    return next();
  } catch (error) {
    // Provide limited information about the error
    if (error instanceof jwt.TokenExpiredError) {
      return return res.status(401).json({ message: "Session expired, please log in again" });
    } else if (error instanceof jwt.JsonWebTokenError) {
      return return res.status(401).json({ message: "Invalid token" });
    }

    return return res.status(401).json({ message: "Authentication failed" });
  }
};

// Role-based access control middleware
const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return return res.status(401).json({ message: "Authentication required" });
    }

    if (roles.includes(req.user.role)) {
      return next();
    } else {
      return return res.status(403).json({ message: "Insufficient permissions" });
    }
  };
};

// Validation middleware
const validate = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = schema.parse(req.body);
      req.body = result; // Update req.body with parsed/transformed data
      return next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationError = fromZodError(error);
        return return res.status(400).json({ 
          message: "Validation error", 
          errors: validationError.details 
        });
      }
      next(error);
    }
  };
};

export async function registerRoutes(app: Express): Promise<Server> {

  // Configure CORS with environment-specific origins
  const corsOptions = {
    origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
      // Allow requests with no origin only in development (mobile apps, curl, etc.)
      if (!origin) {
        if (config.isDevelopment()) {
          return callback(null, true);
        } else {
          // In production, require a valid origin header
          return callback(new Error('CORS policy violation: Origin header required'), false);
        }
      }

      const allowedOrigins = config.isDevelopment() ? [
        // Development origins - more permissive
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:5000',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:5000',
        // Replit development domains
        /.*\.replit\.dev$/,
        /.*\.repl\.co$/,
        /.*\.replit\.app$/
      ] : [
        // Production origins - strict whitelist
        'https://farm-house-hub-chaubey-fazhall.replit.app',
        // Add your production domain here when deployed
        // 'https://yourdomain.com',
        // Only allow specific Replit app domain in production
        /^https:\/\/[a-zA-Z0-9-]+\.replit\.app$/
      ];

      const isAllowed = allowedOrigins.some(allowedOrigin => {
        if (typeof allowedOrigin === 'string') {
          return origin === allowedOrigin;
        } else {
          // RegExp
          return allowedOrigin.test(origin);
        }
      });

      if (isAllowed) {
        callback(null, true);
      } else {
        console.warn(`CORS blocked origin: ${origin} (Environment: ${config.app.nodeEnv})`);
        callback(new Error(`CORS policy violation: Origin ${origin} is not allowed`), false);
      }
    },
    credentials: true, // Allow credentials (cookies, authorization headers)
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // Restrict methods
    allowedHeaders: [
      'Origin', 
      'X-Requested-With', 
      'Content-Type', 
      'Accept', 
      'Authorization',
      'X-CSRF-Token' // Add CSRF token support
    ],
    exposedHeaders: ['RateLimit-Limit', 'RateLimit-Remaining', 'RateLimit-Reset'],
    maxAge: 86400 // Cache preflight response for 24 hours
  };

  // Apply CORS only to API routes (not to static assets)
  app.use("/api", cors(corsOptions));

  // Apply general rate limiting to all API routes
  app.use("/api", generalApiLimiter);

  // Security headers middleware for API routes
  app.use("/api", (req, res, next) => {
    // Ensure JSON content type for API responses
    res.header("Content-Type", "application/json");
    
    // Additional security headers
    res.header("X-Content-Type-Options", "nosniff");
    res.header("X-Frame-Options", "DENY");
    res.header("X-XSS-Protection", "1; mode=block");
    res.header("Referrer-Policy", "strict-origin-when-cross-origin");
    
    // Prevent caching of sensitive API responses
    if (req.path.includes('/auth') || req.path.includes('/profile')) {
      res.header("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");
      res.header("Pragma", "no-cache");
      res.header("Expires", "0");
      res.header("Surrogate-Control", "no-store");
    }
    
    next();
  });

  // Serve uploaded files
  app.use("/uploads", express.static(UPLOADS_DIR));

  // Profile Routes
  app.get("/api/profile", authenticate, async (req, res) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return return res.status(401).json({ error: "Unauthorized" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return return res.status(404).json({ error: "User not found" });
      }

      // Return user profile without password
      const { password, ...profile } = user;
      return res.json(profile);
    } catch (error) {
      console.error("Error fetching profile:", error);
      return return res.status(500).json({ error: "Internal server error" });
    }
  });

  app.patch("/api/profile", authenticate, async (req, res) => {
    // Explicitly set response headers to ensure JSON response
    res.setHeader('Content-Type', 'application/json; charset=utf-8');

    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401);
        return res.end(JSON.stringify({ error: "Unauthorized" }));
      }

      console.log("Updating profile for user:", userId, "with data:", req.body);
      const { phone, address, bio } = req.body;

      // Only include editable fields in the update
      const updateData: any = {};
      if (phone !== undefined) updateData.phone = phone || null;
      if (address !== undefined) updateData.address = address || null;
      if (bio !== undefined) updateData.bio = bio || null;

      console.log("Filtered update data:", updateData);

      const updatedUser = await storage.updateUser(userId, updateData);

      if (!updatedUser) {
        res.status(404);
        return res.end(JSON.stringify({ error: "User not found" }));
      }

      // Return updated profile without password
      const { password, ...profile } = updatedUser;
      console.log("Profile updated successfully:", profile);

      res.status(200);
      return res.end(JSON.stringify(profile));
    } catch (error) {
      console.error("Error updating profile:", error);
      res.status(500);
      return res.end(JSON.stringify({ error: "Internal server error", details: (error as Error).message }));
    }
  });

  // Authentication Routes
  app.post("/api/auth/register", authLimiter, validate(userRegisterSchema), async (req, res) => {
    try {
      const { email, consentData } = req.body;

      // Check if user already exists
      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return return res.status(400).json({ message: "User with this email already exists" });
      }

      // Prepare user data with consent information
      const userData = {
        ...req.body,
        termsAccepted: consentData?.termsAccepted || false,
        privacyPolicyAccepted: consentData?.termsAccepted || false, // Same checkbox covers all legal docs
        cookiePolicyAccepted: consentData?.termsAccepted || false, // Same checkbox covers all legal docs
        dataProcessingConsent: consentData?.dataProcessingConsent || false,
        marketingConsent: consentData?.marketingConsent || false,
        consentTimestamp: new Date()
      };

      // Remove the consent data object to avoid database schema conflicts
      delete userData.consentData;

      // Create new user with consent tracking
      const user = await storage.createUser(userData);

      // Generate token
      const token = jwt.sign(
        { userId: user.id, role: user.role },
        config.jwt.secret,
        { expiresIn: "7d" }
      );

      // Set the token as an HTTP-only cookie for better security
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      return res.status(201).json({ 
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          role: user.role
        },
        token 
      });
    } catch (error) {
      console.error("Registration error:", error);
      return return res.status(500).json({ message: "Server error during registration" });
    }
  });

  app.post("/api/auth/login", authLimiter, validate(userLoginSchema), async (req, res) => {
    try {
      // Implement rate limiting for login attempts
      // This is a simple implementation. In production, use a more robust solution like rate-limit-redis
      const clientIP = typeof req.ip === 'string' ? req.ip : 'unknown';
      const loginAttempts = loginAttemptsMap.get(clientIP) || 0;

      if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
        return return res.status(429).json({ 
          message: "Too many login attempts. Please try again later.",
          retryAfter: 15 * 60 // 15 minutes in seconds
        });
      }

      const { email, password } = req.body;

      // Find user by email
      const user = await storage.getUserByEmail(email);
      if (!user) {
        // Increment failed attempts
        loginAttemptsMap.set(clientIP, loginAttempts + 1);

        // Schedule cleanup of attempts after the cooldown period
        setTimeout(() => {
          loginAttemptsMap.delete(clientIP);
        }, 15 * 60 * 1000); // 15 minutes

        return return res.status(401).json({ message: "Invalid email or password" });
      }

      // Verify password with constant time comparison to prevent timing attacks
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        // Increment failed attempts
        loginAttemptsMap.set(clientIP, loginAttempts + 1);

        // Schedule cleanup of attempts after the cooldown period
        setTimeout(() => {
          loginAttemptsMap.delete(clientIP);
        }, 15 * 60 * 1000); // 15 minutes

        return return res.status(401).json({ message: "Invalid email or password" });
      }

      // Reset login attempts on successful login
      loginAttemptsMap.delete(clientIP);

      // Generate token with shorter expiration time
      const token = jwt.sign(
        { 
          userId: user.id, 
          role: user.role,
          // Add random jitter to prevent token reuse
          jti: crypto.randomBytes(16).toString('hex')
        },
        config.jwt.secret,
        { 
          expiresIn: "1d", // Shorter expiration time for better security
          audience: 'farmhouse-rental-app',
          issuer: 'farmhouse-rental-api'
        }
      );

      // Set token in HTTP-only cookie for better security
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 1 day
      });

      // Also return token in response for API compatibility
      res.json({ 
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          role: user.role
        },
        token 
      });
    } catch (error) {
      console.error("Login error:", error);
      return return res.status(500).json({ message: "Server error during login" });
    }
  });

  app.get("/api/auth/me", authenticate, async (req, res) => {
    try {
      const user = await storage.getUser(req.user!.userId);
      if (!user) {
        return return res.status(404).json({ message: "User not found" });
      }

      res.json({
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role
      });
    } catch (error) {
      console.error("Get user error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Logout endpoint to invalidate tokens
  app.post("/api/auth/logout", authenticate, (req, res) => {
    try {
      // Get the token
      const authHeader = req.headers.authorization;
      const cookieToken = req.cookies?.auth_token;
      const token = authHeader ? authHeader.split(" ")[1] : cookieToken;

      if (token) {
        // Add token to blacklist
        blacklistedTokens.add(token);

        // Set a timeout to remove the token from blacklist after it expires
        // This prevents the blacklist from growing indefinitely
        try {
          const decoded = jwt.decode(token) as { exp?: number };
          if (decoded && decoded.exp) {
            const expiryTime = decoded.exp * 1000 - Date.now() + 10000; // Add 10s buffer
            setTimeout(() => {
              blacklistedTokens.delete(token);
            }, expiryTime > 0 ? expiryTime : 0);
          }
        } catch (e) {
          // If decoding fails, set a default expiry of 24 hours
          setTimeout(() => {
            blacklistedTokens.delete(token);
          }, 24 * 60 * 60 * 1000);
        }
      }

      // Clear the cookie
      res.clearCookie('auth_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/'
      });

      res.json({ message: "Successfully logged out" });
    } catch (error) {
      console.error("Logout error:", error);
      return res.status(500).json({ message: "Server error during logout" });
    }
  });

  // Property Routes
  app.get("/api/properties", searchLimiter, sanitizeSearchQuery, async (req, res) => {
    try {
      // Only filter by featured if explicitly requested
      const featured = req.query.featured === "true" ? true : undefined;
      const location = req.query.location as string | undefined;
      const date = req.query.date ? new Date(req.query.date as string) : undefined;

      // Handle new advanced search parameters
      const minPrice = req.query.minPrice ? parseFloat(req.query.minPrice as string) : undefined;
      const maxPrice = req.query.maxPrice ? parseFloat(req.query.maxPrice as string) : undefined;
      const amenities = req.query.amenities ? (req.query.amenities as string).split(',') : undefined;

      console.log("Search parameters:", { 
        featured, 
        location, 
        date, 
        minPrice, 
        maxPrice, 
        amenities 
      });

      const properties = await storage.getProperties(
        featured, 
        location, 
        date, 
        minPrice, 
        maxPrice, 
        amenities
      );

      console.log(`Found ${properties.length} properties after applying all filters`);

      res.json(properties);
    } catch (error) {
      console.error("Get properties error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/properties/:id", sanitizeIdParam, async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id || '0');
      if (isNaN(propertyId) || propertyId <= 0) {
        return return res.status(400).json({ message: "Invalid property ID" });
      }
      const property = await storage.getProperty(propertyId);

      if (!property) {
        return return res.status(404).json({ message: "Property not found" });
      }

      res.json(property);
    } catch (error) {
      console.error("Get property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/properties", authenticate, authorize(["owner"]), validate(propertyFormSchema), async (req, res) => {
    try {
      const property = {
        ...req.body,
        ownerId: req.user!.userId
      };

      const newProperty = await storage.createProperty(property);
      return res.status(201).json(newProperty);
    } catch (error) {
      console.error("Create property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.put("/api/properties/:id", authenticate, authorize(["owner"]), sanitizeIdParam, async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id);

      // Verify ownership
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return return res.status(404).json({ message: "Property not found" });
      }

      if (property.ownerId !== req.user!.userId) {
        return return res.status(403).json({ message: "You don't have permission to update this property" });
      }

      const updatedProperty = await storage.updateProperty(propertyId, req.body);
      res.json(updatedProperty);
    } catch (error) {
      console.error("Update property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/properties/:id", authenticate, authorize(["owner"]), async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id);

      // Verify ownership
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return return res.status(404).json({ message: "Property not found" });
      }

      if (property.ownerId !== req.user!.userId) {
        return return res.status(403).json({ message: "You don't have permission to delete this property" });
      }

      await storage.deleteProperty(propertyId);
      res.status(204).end();
    } catch (error) {
      console.error("Delete property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/properties/owner/me", authenticate, authorize(["owner"]), async (req, res) => {
    try {
      const properties = await storage.getPropertiesByOwner(req.user!.userId);
      res.json(properties);
    } catch (error) {
      console.error("Get owner properties error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Cloudinary Image Upload Routes
  app.post("/api/upload/images", authenticate, upload.array("images", 10), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        return return res.status(400).json({ message: "No files uploaded" });
      }

      if (!cloudinaryService.isInitialized()) {
        return return res.status(503).json({ 
          message: "Image upload service unavailable. Please check configuration." 
        });
      }

      const uploadFiles = files.map(file => ({
        buffer: file.buffer,
        filename: file.originalname
      }));

      const results = await cloudinaryService.uploadMultipleImages(uploadFiles, 'farmhouse-properties');
      
      const imageUrls = results.map(result => ({
        url: result.secure_url,
        publicId: result.public_id,
        width: result.width,
        height: result.height,
        format: result.format,
        bytes: result.bytes
      }));

      res.json({ 
        success: true,
        images: imageUrls,
        count: imageUrls.length
      });
    } catch (error) {
      console.error("Cloudinary upload error:", error);
      return res.status(500).json({ 
        message: "Failed to upload images",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Delete images from Cloudinary
  app.delete("/api/upload/images", authenticate, async (req, res) => {
    try {
      const { publicIds } = req.body;

      if (!publicIds || !Array.isArray(publicIds) || publicIds.length === 0) {
        return return res.status(400).json({ message: "No image IDs provided" });
      }

      if (!cloudinaryService.isInitialized()) {
        return return res.status(503).json({ 
          message: "Image service unavailable. Please check configuration." 
        });
      }

      const results = await cloudinaryService.deleteMultipleImages(publicIds);
      
      res.json({
        success: true,
        deleted: results.success,
        failed: results.failed,
        deletedCount: results.success.length,
        failedCount: results.failed.length
      });
    } catch (error) {
      console.error("Cloudinary delete error:", error);
      return res.status(500).json({ 
        message: "Failed to delete images",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Booking Routes
  app.get("/api/bookings", authenticate, async (req, res) => {
    try {
      const bookings = await storage.getBookings(req.user!.userId);

      // Get property details for each booking
      const bookingsWithProperties = await Promise.all(
        bookings.map(async (booking) => {
          const property = await storage.getProperty(booking.propertyId);
          return {
            ...booking,
            property: property ? {
              id: property.id,
              title: property.title,
              location: property.location,
              images: property.images
            } : null
          };
        })
      );

      res.json(bookingsWithProperties);
    } catch (error) {
      console.error("Get user bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/bookings", authenticate, async (req, res) => {
    try {
      const { propertyId, bookingDate, bookingType, guests } = req.body;

      // Validate the booking data
      if (!propertyId || !bookingDate || !bookingType || !guests) {
        return return res.status(400).json({ message: "Missing required booking information" });
      }

      // Get property to calculate price
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return return res.status(404).json({ message: "Property not found" });
      }

      // Calculate total price
      const basePrice = bookingType === "morning" ? property.halfDayPrice : property.fullDayPrice;
      const cleaningFee = 15;
      const totalPrice = basePrice + cleaningFee;

      // Check availability
      const isAvailable = await storage.checkAvailability(propertyId, new Date(bookingDate), bookingType);
      if (!isAvailable) {
        return return res.status(400).json({ message: "This date and time slot is not available" });
      }

      // Create booking
      const newBooking = await storage.createBooking({
        propertyId,
        userId: req.user!.userId,
        bookingDate,
        bookingType,
        guests: parseInt(guests),
        totalPrice,
        status: 'confirmed'
      });

      return res.status(201).json(newBooking);
    } catch (error) {
      console.error("Create booking error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/user", authenticate, async (req, res) => {
    try {
      const bookings = await storage.getBookings(req.user!.userId);
      res.json(bookings);
    } catch (error) {
      console.error("Get user bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/user/:userId", authenticate, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      // Check if user is requesting their own bookings or is an admin
      if (req.user!.userId !== userId && req.user!.role !== 'admin') {
        return return res.status(403).json({ message: "Access denied" });
      }

      const bookings = await storage.getBookingsWithProperty(userId);
      res.json(bookings);
    } catch (error) {
      console.error("Get user bookings with property error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/owner", authenticate, authorize(["owner"]), async (req, res) => {
    try {
      const bookings = await storage.getBookingsByOwner(req.user!.userId);
      res.json(bookings);
    } catch (error) {
      console.error("Get owner bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/bookings/property/:id", async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id);
      const bookings = await storage.getBookingsByProperty(propertyId);

      // For privacy, only return the dates and booking types, not user information
      const bookingDates = bookings.map(booking => ({
        date: booking.bookingDate,
        type: booking.bookingType
      }));

      res.json(bookingDates);
    } catch (error) {
      console.error("Get property bookings error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/bookings", bookingLimiter, authenticate, validate(bookingFormSchema), async (req, res) => {
    try {
      const { propertyId, bookingDate, bookingType, guests } = req.body;

      // Check property exists
      const property = await storage.getProperty(propertyId);
      if (!property) {
        return return res.status(404).json({ message: "Property not found" });
      }

      // Convert date string to Date object
      const dateObj = new Date(bookingDate);

      // Validate the date
      if (isNaN(dateObj.getTime())) {
        return return res.status(400).json({ message: "Invalid date format" });
      }

      // Check if the date is in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (dateObj < today) {
        return return res.status(400).json({ message: "Cannot book dates in the past" });
      }

      // Format date for availability check
      const formattedDate = dateObj.toISOString().split('T')[0];

      // Check availability
      const isAvailable = await storage.checkAvailability(propertyId, dateObj, bookingType);
      if (!isAvailable) {
        return return res.status(400).json({ message: "Property is not available for the selected date and time" });
      }

      // Calculate price based on booking type
      const price = bookingType === 'morning' ? property.halfDayPrice : property.fullDayPrice;

      // Create booking with properly formatted data
      const bookingData = {
        propertyId,
        userId: req.user!.userId,
        bookingDate: formattedDate,
        bookingType,
        guests,
        totalPrice: price,
        status: 'confirmed'
      };

      const booking = await storage.createBooking(bookingData);

      // Get user information for emails
      const guest = await storage.getUser(req.user!.userId);
      const owner = await storage.getUser(property.ownerId);

      if (guest && owner) {
        // Send email notifications
        const emailData = {
          bookingId: booking.id,
          propertyTitle: property.title,
          guestName: guest.fullName || guest.username,
          guestEmail: guest.email,
          ownerEmail: owner.email,
          bookingDate: bookingDate,
          bookingType: bookingType,
          guests: req.body.guests,
          totalPrice: price + 15, // Including cleaning fee
          propertyLocation: property.location
        };

        // Send emails asynchronously (don't wait for email sending to complete)
        emailService.sendBookingEmails(emailData).then(result => {
          console.log('Email notifications sent:', result);
        }).catch(error => {
          console.error('Failed to send email notifications:', error);
        });
      }

      return res.status(201).json(booking);
    } catch (error) {
      console.error("Create booking error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/check-availability/:propertyId", async (req, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      const date = new Date(req.query.date as string);
      const type = req.query.type as 'morning' | 'full_day';

      if (!date || !type) {
        return return res.status(400).json({ message: "Date and booking type are required" });
      }

      const isAvailable = await storage.checkAvailability(propertyId, date, type);
      res.json({ available: isAvailable });
    } catch (error) {
      console.error("Check availability error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Reviews Routes
  app.get("/api/properties/:propertyId/reviews", async (req, res) => {
    try {
      const propertyId = parseInt(req.params.propertyId);
      const reviews = await storage.getReviews(propertyId);

      // Get property average rating
      const averageRating = await storage.getPropertyAverageRating(propertyId);

      res.json({ 
        reviews, 
        averageRating, 
        totalReviews: reviews.length 
      });
    } catch (error) {
      console.error("Get property reviews error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/users/:userId/reviews", authenticate, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      // Make sure users can only access their own reviews unless they are an owner
      if (req.user?.userId !== userId && req.user?.role !== 'owner') {
        return return res.status(403).json({ message: "Not authorized to access these reviews" });
      }

      const reviews = await storage.getUserReviews(userId);
      res.json(reviews);
    } catch (error) {
      console.error("Get user reviews error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/reviews", authenticate, validate(reviewFormSchema), async (req, res) => {
    try {
      const { propertyId, rating, comment, bookingId } = req.body;
      const userId = req.user!.userId;

      // Check if the user has already reviewed this property
      const propertyReviews = await storage.getReviews(propertyId);
      const existingReview = propertyReviews.find(r => r.userId === userId);

      if (existingReview) {
        return return res.status(400).json({ 
          message: "You have already reviewed this property. You can edit your existing review." 
        });
      }

      // Create the review
      const review = await storage.createReview({
        propertyId,
        userId,
        rating,
        comment,
        bookingId
      });

      return res.status(201).json(review);
    } catch (error) {
      console.error("Create review error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.put("/api/reviews/:id", authenticate, validate(reviewFormSchema), async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const { rating, comment } = req.body;
      const userId = req.user!.userId;

      // Get existing review to check ownership
      const existingReview = await storage.getReview(reviewId);

      if (!existingReview) {
        return return res.status(404).json({ message: "Review not found" });
      }

      // Check if user is allowed to edit this review
      if (existingReview.userId !== userId) {
        return return res.status(403).json({ message: "Not authorized to edit this review" });
      }

      // Update the review
      const updatedReview = await storage.updateReview(reviewId, { rating, comment });
      res.json(updatedReview);
    } catch (error) {
      console.error("Update review error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Owner only middleware for owner-specific operations
  const ownerOnly = (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== 'owner') {
      return return res.status(403).json({ message: "Owner access required" });
    }

    next();
  };

  // Admin routes for user consent management
  app.get("/api/admin/users/consent", authenticate, ownerOnly, async (req, res) => {
    try {
      // Fetch all users with their consent information
      // Use the database storage layer
      const allUsers = await storage.getAllUsers();

      // Map user data to the expected format
      const rows = allUsers.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        termsAccepted: user.termsAccepted || false,
        privacyPolicyAccepted: user.privacyPolicyAccepted || false,
        cookiePolicyAccepted: user.cookiePolicyAccepted || false,
        dataProcessingConsent: user.dataProcessingConsent || false,
        marketingConsent: user.marketingConsent || false,
        consentTimestamp: user.consentTimestamp
      }));

      res.json(rows);
    } catch (error) {
      console.error("Error fetching user consent data:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.post("/api/reviews/:id/response", authenticate, ownerOnly, validate(reviewResponseSchema), async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const { response } = req.body;

      // Get the review
      const review = await storage.getReview(reviewId);

      if (!review) {
        return return res.status(404).json({ message: "Review not found" });
      }

      // Get the property to check ownership
      const property = await storage.getProperty(review.propertyId);

      if (!property) {
        return return res.status(404).json({ message: "Property not found" });
      }

      // Check if the owner is the one responding
      if (property.ownerId !== req.user!.userId) {
        return return res.status(403).json({ message: "Only the property owner can respond to reviews" });
      }

      // Add owner response
      const updatedReview = await storage.addOwnerResponse(reviewId, response);
      res.json(updatedReview);
    } catch (error) {
      console.error("Add review response error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/reviews/:id", authenticate, async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const userId = req.user!.userId;

      // Get existing review to check ownership
      const existingReview = await storage.getReview(reviewId);

      if (!existingReview) {
        return return res.status(404).json({ message: "Review not found" });
      }

      // Check if user is allowed to delete this review (owner of review or owner of property)
      const property = await storage.getProperty(existingReview.propertyId);

      if (existingReview.userId !== userId && property?.ownerId !== userId) {
        return return res.status(403).json({ message: "Not authorized to delete this review" });
      }

      // Delete the review
      await storage.deleteReview(reviewId);
      res.status(204).send();
    } catch (error) {
      console.error("Delete review error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Import and add OTP authentication routes
  const otpRoutes = await import("./otpRoutes");
  app.use("/api/auth/otp", otpRoutes.default);

  const httpServer = createServer(app);

  return httpServer;
}