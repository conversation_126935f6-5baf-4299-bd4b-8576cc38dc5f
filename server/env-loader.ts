import { config as loadEnv } from 'dotenv';
import { resolve } from 'path';
import { existsSync } from 'fs';

/**
 * Load environment variables based on NODE_ENV
 * NODE_ENV can be 'development', 'production', or 'test'
 */
export function loadEnvironmentConfig(): void {
  // Get the environment from NODE_ENV, default to development
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  console.log(`>>> Starting ${nodeEnv} server`);
  console.log(`🔧 Loading environment configuration for: ${nodeEnv}`);
  
  // Define possible environment files in order of priority
  const envFiles = [
    `.env.${nodeEnv}`,     // .env.production, .env.development, or .env.test
    '.env.local',          // Local overrides (not committed)
    '.env'                 // Default fallback
  ];
  
  // Load each existing environment file
  for (const envFile of envFiles) {
    const envPath = resolve(process.cwd(), envFile);
    
    if (existsSync(envPath)) {
      console.log(`  📄 Loading: ${envFile}`);
      
      // Load environment file
      const result = loadEnv({ path: envPath });
      
      if (result.error) {
        console.warn(`  ⚠️ Warning: Failed to load ${envFile}:`, result.error.message);
      } else {
        console.log(`  ✅ Loaded: ${envFile}`);
      }
    } else {
      console.log(`  ⏭️ Skipped: ${envFile} (file not found)`);
    }
  }
  
  // Ensure NODE_ENV is set to the detected environment
  if (!process.env.NODE_ENV) {
    process.env.NODE_ENV = nodeEnv;
    console.log(`  🔄 Set NODE_ENV to: ${process.env.NODE_ENV}`);
  }
  
  console.log(`  🎯 Final configuration: NODE_ENV=${process.env.NODE_ENV}`);
  console.log(`  📊 DATABASE_URL: ${process.env.DATABASE_URL ? 'SET' : 'NOT SET'}`);
}

// Execute immediately when this module is imported
loadEnvironmentConfig();