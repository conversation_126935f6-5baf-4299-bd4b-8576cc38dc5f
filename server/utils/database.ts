import pg from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from '../../shared/schema';
import { logger } from './structured-logger';
import { TIME_UNITS } from '../../shared/constants';

const { Pool } = pg;

interface ConnectionMetrics {
  totalConnections: number;
  idleConnections: number;
  activeConnections: number;
  lastHealthCheck: Date | null;
  connectionErrors: number;
  lastError: Error | null;
}

interface DatabaseConfig {
  maxConnections: number;
  minConnections: number;
  connectionTimeoutMs: number;
  idleTimeoutMs: number;
  statementTimeoutMs: number;
  healthCheckIntervalMs: number;
  maxRetries: number;
  retryDelayMs: number;
}

class DatabaseManager {
  private pool: pg.Pool | null = null;
  private db: ReturnType<typeof drizzle> | null = null;
  private metrics: ConnectionMetrics;
  private config: DatabaseConfig;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isShuttingDown = false;

  constructor() {
    this.metrics = {
      totalConnections: 0,
      idleConnections: 0,
      activeConnections: 0,
      lastHealthCheck: null,
      connectionErrors: 0,
      lastError: null,
    };

    this.config = {
      maxConnections: this.getMaxConnections(),
      minConnections: 1,
      connectionTimeoutMs: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'), // Reduced from 45s
      idleTimeoutMs: parseInt(process.env.DB_IDLE_TIMEOUT || '60000'), // Reduced to 1 minute (from 4 minutes)
      statementTimeoutMs: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'), // Reduced from 90s
      healthCheckIntervalMs: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '300000'), // 5 minutes (reduced from 1 minute for cost optimization)
      maxRetries: 5, // Increased from 3
      retryDelayMs: 1000, // Reduced from 2000ms with exponential backoff
    };
  }

  private getMaxConnections(): number {
    // Neon free tier: 20 connections max, but keep pool small for efficiency
    const isProduction = process.env.NODE_ENV === 'production';
    const defaultMax = isProduction ? 3 : 2; // Very conservative limits for cost optimization
    
    const envMax = process.env.DB_MAX_CONNECTIONS;
    return envMax ? parseInt(envMax, 10) : defaultMax;
  }

  private getDatabaseUrl(): string {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL must be set');
    }
    return process.env.DATABASE_URL;
  }

  private createPool(): pg.Pool {
    if (this.pool) {
      return this.pool;
    }

    const connectionString = this.getDatabaseUrl();
    
    this.pool = new Pool({
      connectionString,
      max: this.config.maxConnections,
      min: this.config.minConnections,
      idleTimeoutMillis: this.config.idleTimeoutMs,
      connectionTimeoutMillis: this.config.connectionTimeoutMs,
      statement_timeout: this.config.statementTimeoutMs,
      query_timeout: this.config.statementTimeoutMs,
      keepAlive: false, // Disable keep-alive to reduce persistent connections
      keepAliveInitialDelayMillis: 10000, // Reduced from 15000
      allowExitOnIdle: false, // Changed from true for better connection stability
      // Neon-specific SSL configuration
      ssl: { rejectUnauthorized: false },
      application_name: 'farmhouse-app',
      // Additional settings for better connection handling
      idle_in_transaction_session_timeout: 30000,
      log: (msg: string) => logger.debug(msg, { component: 'pg-pool' }),
    });

    this.setupPoolEventHandlers();
    this.startHealthChecks();

    // Prewarm connections after pool creation
    this.prewarmConnections().catch(err => {
      logger.warn('Failed to prewarm connections on startup', { component: 'database' });
    });

    return this.pool;
  }

  private setupPoolEventHandlers(): void {
    if (!this.pool) return;

    this.pool.on('connect', (client: pg.PoolClient) => {
      this.metrics.totalConnections++;
      // Reduced logging to minimize overhead
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Database client connected', { component: 'database' }, { 
          totalConnections: this.metrics.totalConnections 
        });
      }
      
      // Set statement timeout for this client
      client.query(`SET statement_timeout = ${this.config.statementTimeoutMs}`).catch(err => {
        logger.warn('Failed to set statement timeout', { component: 'database' }, { error: err.message });
      });
    });

    this.pool.on('acquire', (client: pg.PoolClient) => {
      this.metrics.activeConnections++;
      this.metrics.idleConnections = Math.max(0, this.metrics.idleConnections - 1);
      // Only log in development mode to reduce overhead
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Database client acquired', { component: 'database' }, { 
          activeConnections: this.metrics.activeConnections,
          idleConnections: this.metrics.idleConnections
        });
      }
    });

    this.pool.on('release', (err: Error | undefined, client: pg.PoolClient) => {
      this.metrics.activeConnections = Math.max(0, this.metrics.activeConnections - 1);
      this.metrics.idleConnections++;
      
      if (err) {
        logger.error('Error releasing database client', { component: 'database' }, { 
          activeConnections: this.metrics.activeConnections,
          idleConnections: this.metrics.idleConnections
        }, err);
        this.metrics.connectionErrors++;
        this.metrics.lastError = err;
      } else if (process.env.NODE_ENV === 'development') {
        logger.debug('Database client released', { component: 'database' }, { 
          activeConnections: this.metrics.activeConnections,
          idleConnections: this.metrics.idleConnections
        });
      }
    });

    this.pool.on('remove', () => {
      this.metrics.totalConnections = Math.max(0, this.metrics.totalConnections - 1);
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Database client removed', { component: 'database' }, { 
          totalConnections: this.metrics.totalConnections 
        });
      }
    });

    this.pool.on('error', (err: Error, client?: pg.PoolClient) => {
      this.metrics.connectionErrors++;
      this.metrics.lastError = err;
      
      logger.error('Database pool error', { component: 'database' }, { 
        connectionErrors: this.metrics.connectionErrors,
        errorType: err.name,
        errorCode: (err as any).code
      }, err);
      
      // Check if this is a recoverable connection error (including Neon-specific)
      const isRecoverableError = 
        err.message.includes('terminating connection') ||
        err.message.includes('Connection terminated') ||
        err.message.includes('ECONNRESET') ||
        err.message.includes('ENOTFOUND') ||
        err.message.includes('ETIMEDOUT') ||
        err.message.includes('connection closed') ||
        err.message.includes('database is starting up') ||
        err.message.includes('could not connect') ||
        (err as any).code === 'ECONNRESET' ||
        (err as any).code === 'ETIMEDOUT';
      
      if (isRecoverableError) {
        logger.warn('Recoverable database connection error detected, scheduling recovery', { component: 'database' });
        
        // Attempt recovery after a short delay (non-blocking)
        setTimeout(async () => {
          try {
            logger.info('Attempting automatic database connection recovery', { component: 'database' });
            const recovered = await this.recoverConnection();
            if (recovered) {
              logger.info('Database connection recovery successful', { component: 'database' });
            } else {
              logger.error('Database connection recovery failed', { component: 'database' });
            }
          } catch (recoveryError) {
            logger.error('Database recovery attempt failed', { component: 'database' }, {}, recoveryError as Error);
          }
        }, 2000);
      } else {
        logger.error('Non-recoverable database pool error - manual intervention may be required', { component: 'database' });
      }
    });
  }

  private startHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      if (!this.isShuttingDown) {
        await this.performHealthCheck();
        
        // Refresh connections periodically
        if (this.metrics.connectionErrors > 0 || Math.random() < 0.1) { // 10% chance or when errors
          await this.refreshIdleConnections();
        }
      }
    }, this.config.healthCheckIntervalMs);
  }

  async performHealthCheck(): Promise<boolean> {
    try {
      const start = Date.now();
      const client = await this.pool!.connect();
      
      try {
        await client.query('SELECT 1 as health');
        const duration = Date.now() - start;
        
        this.metrics.lastHealthCheck = new Date();
        
        // Only log health checks in development or if they're slow
        if (process.env.NODE_ENV === 'development' || duration > 1000) {
          logger.info('Database health check passed', { component: 'database' }, { 
            duration,
            connectionErrors: this.metrics.connectionErrors 
          });
        }
        
        return true;
      } finally {
        client.release();
      }
    } catch (error) {
      this.metrics.connectionErrors++;
      this.metrics.lastError = error as Error;
      logger.error('Database health check failed', { component: 'database' }, {
        connectionErrors: this.metrics.connectionErrors
      }, error as Error);
      
      // Check if this is a connection error that requires recovery (including Neon-specific)
      const errorMessage = (error as Error).message;
      const errorCode = (error as any).code;
      const isConnectionError = 
        errorMessage.includes('terminating connection') ||
        errorMessage.includes('Connection terminated') ||
        errorMessage.includes('ECONNRESET') ||
        errorMessage.includes('ENOTFOUND') ||
        errorMessage.includes('ETIMEDOUT') ||
        errorMessage.includes('connection closed') ||
        errorMessage.includes('database is starting up') ||
        errorMessage.includes('Query read timeout') ||
        errorCode === 'ECONNRESET' ||
        errorCode === 'ETIMEDOUT';
      
      if (isConnectionError) {
        logger.warn('Health check detected connection issue, triggering recovery', { component: 'database' });
        
        // Trigger recovery in the background (non-blocking)
        setTimeout(async () => {
          try {
            const recovered = await this.recoverConnection();
            if (recovered) {
              logger.info('Health check triggered recovery successful', { component: 'database' });
            } else {
              logger.error('Health check triggered recovery failed', { component: 'database' });
            }
          } catch (recoveryError) {
            logger.error('Recovery from health check failed', { component: 'database' }, {}, recoveryError as Error);
          }
        }, 1000);
      }
      
      return false;
    }
  }

  trackConnectionMetric(event: string, error?: Error): void {
    switch (event) {
      case 'connect':
        this.metrics.totalConnections++;
        break;
      case 'error':
        this.metrics.connectionErrors++;
        if (error) {
          this.metrics.lastError = error;
        }
        break;
      case 'acquire':
        this.metrics.activeConnections++;
        this.metrics.idleConnections = Math.max(0, this.metrics.idleConnections - 1);
        break;
      case 'release':
        this.metrics.activeConnections = Math.max(0, this.metrics.activeConnections - 1);
        this.metrics.idleConnections++;
        break;
      case 'remove':
        this.metrics.totalConnections = Math.max(0, this.metrics.totalConnections - 1);
        break;
    }
  }

  private calculateRetryDelay(attempt: number): number {
    const baseDelay = this.config.retryDelayMs;
    const maxDelay = 30000; // 30 seconds max
    const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
    const jitter = Math.random() * 0.3 * delay; // 30% jitter
    return Math.floor(delay + jitter);
  }

  private async prewarmConnections(): Promise<void> {
    const warmupCount = Math.min(2, this.config.maxConnections);
    const promises = [];
    
    for (let i = 0; i < warmupCount; i++) {
      promises.push(
        this.withConnection(
          async (client) => {
            await client.query('SELECT 1');
          },
          'connection-prewarm'
        ).catch(err => {
          logger.warn('Failed to prewarm connection', { component: 'database' }, { index: i });
        })
      );
    }
    
    await Promise.allSettled(promises);
    logger.info('Connection prewarming completed', { component: 'database' }, { count: warmupCount });
  }

  private async refreshIdleConnections(): Promise<void> {
    const idleThreshold = 30000; // 30 seconds
    
    try {
      // Test connection health with a simple query
      await this.withConnection(
        async (client) => {
          await client.query('SELECT 1');
        },
        'connection-refresh'
      );
      
      logger.debug('Connection refresh completed', { component: 'database' });
    } catch (error) {
      logger.error('Error refreshing connections', { component: 'database' }, {}, error as Error);
    }
  }

  async withConnection<T>(
    operation: (client: pg.PoolClient) => Promise<T>,
    operationName: string = 'database operation'
  ): Promise<T> {
    if (this.isShuttingDown) {
      throw new Error('Database is shutting down');
    }

    const pool = this.createPool();
    let client: pg.PoolClient | null = null;
    let retryCount = 0;

    while (retryCount <= this.config.maxRetries) {
      try {
        client = await pool.connect();
        const result = await operation(client);
        return result;
      } catch (error) {
        const err = error as Error;
        this.metrics.connectionErrors++;
        this.metrics.lastError = err;

        logger.error(`Database operation failed (${operationName})`, { component: 'database' }, {
          attempt: retryCount + 1,
          maxRetries: this.config.maxRetries,
          operationName
        }, err);

        // Check if it's a connection-related error
        const isConnectionError = 
          err.message.includes('terminating connection') ||
          err.message.includes('Connection terminated') ||
          err.message.includes('connect') ||
          (err as any).code === '57P01';

        if (isConnectionError && retryCount < this.config.maxRetries) {
          retryCount++;
          const delay = this.calculateRetryDelay(retryCount);
          logger.warn(`Retrying database operation`, { component: 'database' }, {
            operationName,
            attempt: retryCount,
            delayMs: delay
          });
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        throw err;
      } finally {
        if (client) {
          try {
            client.release();
          } catch (releaseError) {
            logger.error('Error releasing database client', { component: 'database' }, {}, releaseError as Error);
          }
          client = null;
        }
      }
    }

    throw new Error(`Database operation failed after ${this.config.maxRetries + 1} attempts`);
  }

  async withTransaction<T>(
    operation: (client: pg.PoolClient) => Promise<T>,
    operationName: string = 'transaction'
  ): Promise<T> {
    return this.withConnection(async (client) => {
      await client.query('BEGIN');
      try {
        const result = await operation(client);
        await client.query('COMMIT');
        logger.debug(`Database transaction committed`, { component: 'database' }, { operationName });
        return result;
      } catch (error) {
        try {
          await client.query('ROLLBACK');
          logger.warn(`Database transaction rolled back`, { component: 'database' }, { operationName, error: (error as Error).message });
        } catch (rollbackError) {
          logger.error(`Failed to rollback database transaction`, { component: 'database' }, { operationName }, rollbackError as Error);
        }
        throw error;
      }
    }, `transaction: ${operationName}`);
  }

  getDrizzleInstance(): ReturnType<typeof drizzle> {
    if (!this.db) {
      const pool = this.createPool();
      this.db = drizzle(pool, { schema });
    }
    return this.db;
  }

  getMetrics(): ConnectionMetrics {
    return { ...this.metrics };
  }

  getPoolStats(): { total: number; idle: number; waiting: number } {
    if (!this.pool) {
      return { total: 0, idle: 0, waiting: 0 };
    }

    return {
      total: this.pool.totalCount,
      idle: this.pool.idleCount,
      waiting: this.pool.waitingCount,
    };
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down database connections', { component: 'database' });
    this.isShuttingDown = true;

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.pool) {
      try {
        await this.pool.end();
        logger.info('Database pool closed successfully', { component: 'database' });
      } catch (error) {
        logger.error('Error closing database pool', { component: 'database' }, {}, error as Error);
      }
      this.pool = null;
    }

    this.db = null;
    logger.info('Database shutdown complete', { component: 'database' });
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.withConnection(
        async (client) => {
          await client.query('SELECT NOW() as current_time');
        },
        'connection test'
      );
      return true;
    } catch (error) {
      logger.error('Database connection test failed', { component: 'database' }, {}, error as Error);
      return false;
    }
  }

  updateHealthCheckInterval(intervalMs: number): void {
    logger.info('Updating health check interval', { component: 'database' }, { intervalMs });
    
    // Clear existing interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    // Update configuration
    this.config.healthCheckIntervalMs = intervalMs;
    
    // Restart health checks with new interval
    this.startHealthChecks();
  }

  async recoverConnection(): Promise<boolean> {
    logger.info('Starting database connection recovery', { component: 'database' });
    
    try {
      // Close existing connections cleanly
      if (this.pool) {
        logger.debug('Closing existing pool for recovery', { component: 'database' });
        try {
          await this.pool.end();
        } catch (error) {
          logger.warn('Error closing pool during recovery', { component: 'database' }, { error: (error as Error).message });
        }
        this.pool = null;
      }

      // Clear health check interval
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // Reset database instance
      this.db = null;

      // Reset error metrics  
      this.metrics.connectionErrors = 0;
      this.metrics.lastError = null;

      // Wait a moment before recreating
      await new Promise(resolve => setTimeout(resolve, TIME_UNITS.SECOND));

      // Recreate pool and test connection
      logger.debug('Recreating database pool', { component: 'database' });
      const newPool = this.createPool();
      
      // Test the new connection
      const isHealthy = await this.testConnection();
      
      if (isHealthy) {
        logger.info('Database connection recovery successful', { component: 'database' });
        
        // Prewarm connections after recovery
        await this.prewarmConnections().catch(err => {
          logger.warn('Failed to prewarm connections after recovery', { component: 'database' });
        });
        
        return true;
      } else {
        logger.error('Database connection recovery failed - test connection unsuccessful', { component: 'database' });
        return false;
      }
    } catch (error) {
      logger.error('Database connection recovery failed', { component: 'database' }, {}, error as Error);
      this.metrics.connectionErrors++;
      this.metrics.lastError = error as Error;
      return false;
    }
  }
}

// Singleton instance
export const databaseManager = new DatabaseManager();

// Export convenience functions
export const db = new Proxy({} as ReturnType<typeof drizzle>, {
  get(_, prop) {
    return databaseManager.getDrizzleInstance()[prop as keyof ReturnType<typeof drizzle>];
  }
});

export const withConnection = databaseManager.withConnection.bind(databaseManager);
export const withTransaction = databaseManager.withTransaction.bind(databaseManager);

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down database', { component: 'database' });
  await databaseManager.shutdown();
});

process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down database', { component: 'database' });
  await databaseManager.shutdown();
});

export default databaseManager;