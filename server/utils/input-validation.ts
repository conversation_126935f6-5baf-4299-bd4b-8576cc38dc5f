import { z } from 'zod';
import { log } from './logger';

// Phone number validation schema
export const phoneNumberSchema = z.string()
  .transform((val) => {
    // Remove any non-digit characters except +
    return val.replace(/[^\d+]/g, '');
  })
  .refine((cleaned) => {
    // Validate cleaned number
    return /^[\+]?[1-9][\d]{0,15}$/.test(cleaned) && cleaned.length >= 10 && cleaned.length <= 16;
  }, 'Invalid phone number format')
  .transform((cleaned) => {
    // If already has +, return as is
    if (cleaned.startsWith('+')) {
      return cleaned;
    }
    
    // Handle numbers with Indian country code but no +
    if (cleaned.length === 12 && cleaned.startsWith('91')) {
      return `+${cleaned}`;
    }
    
    // Handle Indian phone numbers (10 digits without country code)
    if (cleaned.length === 10 && /^[6-9]/.test(cleaned)) {
      return `+91${cleaned}`;
    }
    
    // For other 10-digit numbers (international), just add +
    if (cleaned.length === 10) {
      return `+${cleaned}`;
    }
    
    // For other numbers, just add +
    return `+${cleaned}`;
  });

// Email validation schema
export const emailSchema = z.string()
  .transform((val) => val.toLowerCase().trim())
  .refine((email) => email.length >= 5 && email.length <= 254, 'Email must be 5-254 characters')
  .refine((email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && !email.includes('..');
  }, 'Invalid email format');

// OTP code validation schema
export const otpCodeSchema = z.string()
  .transform((val) => val.trim())
  .refine((val) => val.length >= 4 && val.length <= 8, 'OTP must be 4-8 digits')
  .refine((val) => /^\d+$/.test(val), 'OTP must contain only digits');

// User input validation schema
export const userInputSchema = z.object({
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name cannot exceed 100 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Full name can only contain letters, spaces, hyphens, apostrophes, and periods')
    .transform((val) => val.trim().replace(/\s+/g, ' ')),
  
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username cannot exceed 30 characters')
    .regex(/^[a-zA-Z0-9_\-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
    .transform((val) => val.toLowerCase().trim()),
  
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password cannot exceed 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
});

// SMS template validation schema
export const smsTemplateSchema = z.object({
  key: z.string()
    .transform((val) => val.toLowerCase().trim())
    .refine((val) => val.length >= 3 && val.length <= 50, 'Template key must be 3-50 characters')
    .refine((val) => /^[a-z0-9_]+$/.test(val), 'Template key can only contain lowercase letters, numbers, and underscores'),
  
  name: z.string()
    .min(3, 'Template name must be at least 3 characters')
    .max(100, 'Template name cannot exceed 100 characters')
    .transform((val) => val.trim()),
  
  content: z.string()
    .min(10, 'Template content must be at least 10 characters')
    .max(1600, 'Template content cannot exceed 1600 characters (SMS limit)')
    .transform((val) => val.trim()),
  
  variables: z.array(z.string().regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, 'Variable names must be valid identifiers'))
    .max(10, 'Cannot have more than 10 variables per template'),
  
  dltTemplateId: z.string()
    .min(19, 'DLT Template ID must be at least 19 characters')
    .max(19, 'DLT Template ID must be exactly 19 characters')
    .regex(/^\d{19}$/, 'DLT Template ID must be 19 digits')
});

// Property validation schema
export const propertyInputSchema = z.object({
  title: z.string()
    .min(10, 'Property title must be at least 10 characters')
    .max(200, 'Property title cannot exceed 200 characters')
    .transform((val) => val.trim()),
  
  description: z.string()
    .min(50, 'Property description must be at least 50 characters')
    .max(2000, 'Property description cannot exceed 2000 characters')
    .transform((val) => val.trim()),
  
  location: z.string()
    .min(5, 'Location must be at least 5 characters')
    .max(200, 'Location cannot exceed 200 characters')
    .transform((val) => val.trim()),
  
  pricePerNight: z.number()
    .min(100, 'Price per night must be at least ₹100')
    .max(100000, 'Price per night cannot exceed ₹100,000')
    .int('Price must be a whole number'),
  
  coordinates: z.object({
    latitude: z.number()
      .min(-90, 'Latitude must be between -90 and 90')
      .max(90, 'Latitude must be between -90 and 90'),
    longitude: z.number()
      .min(-180, 'Longitude must be between -180 and 180')
      .max(180, 'Longitude must be between -180 and 180')
  }).optional(),
  
  amenities: z.array(z.string().max(50, 'Amenity name cannot exceed 50 characters'))
    .max(20, 'Cannot have more than 20 amenities'),
  
  maxGuests: z.number()
    .min(1, 'Must accommodate at least 1 guest')
    .max(50, 'Cannot accommodate more than 50 guests')
    .int('Guest count must be a whole number')
});

// Booking validation schema
export const bookingInputSchema = z.object({
  propertyId: z.number().int().positive('Property ID must be a positive integer'),
  
  checkIn: z.string()
    .refine((date) => {
      const checkInDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return checkInDate >= today;
    }, 'Check-in date must be today or in the future'),
  
  checkOut: z.string()
    .refine((date) => {
      const checkOutDate = new Date(date);
      return !isNaN(checkOutDate.getTime());
    }, 'Check-out date must be valid'),
  
  guests: z.number()
    .min(1, 'Must have at least 1 guest')
    .max(50, 'Cannot have more than 50 guests')
    .int('Guest count must be a whole number'),
  
  totalAmount: z.number()
    .min(100, 'Total amount must be at least ₹100')
    .max(1000000, 'Total amount cannot exceed ₹10,00,000')
    .multipleOf(0.01, 'Total amount must be a valid currency amount')
}).refine((data) => {
  const checkIn = new Date(data.checkIn);
  const checkOut = new Date(data.checkOut);
  return checkOut > checkIn;
}, {
  message: 'Check-out date must be after check-in date',
  path: ['checkOut']
});

// File upload validation
export const fileUploadSchema = z.object({
  fieldname: z.string().max(50),
  originalname: z.string().max(255),
  encoding: z.string(),
  mimetype: z.string()
    .refine((type) => {
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
        'application/pdf',
        'text/plain',
        'application/json'
      ];
      return allowedTypes.includes(type);
    }, 'File type not allowed'),
  size: z.number()
    .max(10 * 1024 * 1024, 'File size cannot exceed 10MB') // 10MB limit
});

// Input sanitization utilities
export class InputSanitizer {
  // Remove potentially dangerous characters from strings
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .replace(/\0/g, ''); // Remove null bytes
  }

  // Sanitize HTML content
  static sanitizeHtml(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  // Sanitize SQL to prevent injection
  static sanitizeSql(input: string): string {
    if (typeof input !== 'string') return '';
    
    const result = input
      .replace(/[';]/g, '') // Remove SQL injection characters
      .replace(/--/g, '') // Remove SQL comment syntax
      .replace(/\b(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|EXEC|UNION|SCRIPT)\b/gi, '') // Remove dangerous SQL keywords
      .replace(/\s+/g, ' '); // Normalize whitespace
    
    // Return with leading space if original had leading whitespace after keyword removal
    return result.startsWith(' ') ? result : result.trim();
  }

  // Sanitize phone numbers
  static sanitizePhoneNumber(input: string): string {
    if (typeof input !== 'string') return '';
    
    // Remove all non-digit characters except +
    const cleaned = input.replace(/[^\d+]/g, '');
    
    // Validate format and length
    if (!/^[\+]?[1-9][\d]{0,15}$/.test(cleaned) || cleaned.length < 10) {
      throw new Error('Invalid phone number format');
    }
    
    return cleaned;
  }

  // Sanitize email addresses
  static sanitizeEmail(input: string): string {
    if (typeof input !== 'string') return '';
    
    const cleaned = input.toLowerCase().trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(cleaned)) {
      throw new Error('Invalid email format');
    }
    
    return cleaned;
  }

  // Sanitize file paths
  static sanitizeFilePath(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/[^a-zA-Z0-9\-_\.]/g, '') // Only allow alphanumeric, hyphens, underscores, and dots
      .replace(/\.{2,}/g, '.') // Replace multiple dots with single dot
      .replace(/^\.+|\.+$/g, '') // Remove leading/trailing dots
      .substring(0, 255); // Limit length
  }

  // Sanitize URLs
  static sanitizeUrl(input: string): string {
    if (typeof input !== 'string') return '';
    
    try {
      const url = new URL(input);
      
      // Only allow specific protocols
      if (!['http:', 'https:', 'ftp:', 'ftps:'].includes(url.protocol)) {
        throw new Error('Invalid URL protocol');
      }
      
      return url.toString();
    } catch (error) {
      throw new Error('Invalid URL format');
    }
  }
}

// Request validation middleware factory
export function validateInput<T extends z.ZodSchema>(schema: T) {
  return (req: any, res: any, next: any) => {
    try {
      // Validate request body
      const validatedData = schema.parse(req.body);
      
      // Replace req.body with validated data
      req.body = validatedData;
      
      log(`✅ Input validation passed for ${req.method} ${req.path}`, 'input-validation');
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }));
        
        log(`❌ Input validation failed for ${req.method} ${req.path}: ${JSON.stringify(errorMessages)}`, 'input-validation');
        
        return res.status(400).json({
          error: 'Validation failed',
          details: errorMessages
        });
      }
      
      log(`❌ Input validation error for ${req.method} ${req.path}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'input-validation');
      
      return res.status(500).json({
        error: 'Validation error',
        message: 'An error occurred during input validation'
      });
    }
  };
}

// Validation helper functions
export const validatePhoneNumber = (phone: string): boolean => {
  try {
    phoneNumberSchema.parse(phone);
    return true;
  } catch {
    return false;
  }
};

export const validateEmail = (email: string): boolean => {
  try {
    emailSchema.parse(email);
    return true;
  } catch {
    return false;
  }
};

export const validateOtpCode = (code: string): boolean => {
  try {
    otpCodeSchema.parse(code);
    return true;
  } catch {
    return false;
  }
};

// Schemas are already exported individually above