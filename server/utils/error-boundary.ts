import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

interface ErrorContext {
  requestId?: string;
  userId?: string;
  operation: string;
  metadata?: Record<string, any>;
}

interface ErrorBoundaryOptions {
  context: ErrorContext;
  fallbackResponse?: any;
  shouldReportError?: (error: Error) => boolean;
  onError?: (error: Error, context: ErrorContext) => void;
}

class ErrorBoundary {
  private static errorCounts = new Map<string, number>();
  private static lastErrors = new Map<string, Date>();

  static async withErrorBoundary<T>(
    operation: () => Promise<T>,
    options: ErrorBoundaryOptions
  ): Promise<T> {
    const { context, fallbackResponse, shouldReportError, onError } = options;
    
    try {
      return await operation();
    } catch (error) {
      const err = error as Error;
      
      // Track error frequency
      this.trackError(context.operation);
      
      // Log error with context
      this.logError(err, context);
      
      // Report error if configured
      if (shouldReportError?.(err) !== false) {
        this.reportError(err, context);
      }
      
      // Custom error handler
      if (onError) {
        try {
          onError(err, context);
        } catch (handlerError) {
          console.error('Error in custom error handler:', handlerError);
        }
      }
      
      // Return fallback or rethrow
      if (fallbackResponse !== undefined) {
        console.warn(`Returning fallback response for ${context.operation}`);
        return fallbackResponse;
      }
      
      throw err;
    }
  }

  private static trackError(operation: string): void {
    const count = this.errorCounts.get(operation) || 0;
    this.errorCounts.set(operation, count + 1);
    this.lastErrors.set(operation, new Date());
  }

  private static logError(error: Error, context: ErrorContext): void {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      operation: context.operation,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
      },
      context: {
        requestId: context.requestId,
        userId: context.userId,
        metadata: context.metadata,
      },
    };

    // Different log levels based on error type
    if (this.isCriticalError(error)) {
      console.error('🔥 CRITICAL ERROR:', JSON.stringify(errorInfo, null, 2));
    } else if (this.isWarningError(error)) {
      console.warn('⚠️ WARNING ERROR:', JSON.stringify(errorInfo, null, 2));
    } else {
      console.error('❌ ERROR:', JSON.stringify(errorInfo, null, 2));
    }
  }

  private static reportError(error: Error, context: ErrorContext): void {
    // In a real application, this would send to error tracking service
    // like Sentry, Rollbar, or CloudWatch
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      operation: context.operation,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context,
      frequency: this.errorCounts.get(context.operation) || 1,
    };

    // For now, just log the report
    // TODO: Integrate with actual error reporting service
    if (process.env.NODE_ENV === 'production') {
      console.log('📊 ERROR REPORT:', JSON.stringify(report, null, 2));
    }
  }

  private static isCriticalError(error: Error): boolean {
    const criticalErrors = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'DATABASE_CONNECTION_FAILED',
      'AUTHENTICATION_FAILED',
    ];

    return criticalErrors.some(critical => 
      error.message.includes(critical) || 
      (error as any).code === critical
    );
  }

  private static isWarningError(error: Error): boolean {
    const warningErrors = [
      'VALIDATION_ERROR',
      'NOT_FOUND',
      'UNAUTHORIZED',
      'RATE_LIMITED',
    ];

    return warningErrors.some(warning => 
      error.message.includes(warning) || 
      (error as any).code === warning
    );
  }

  static getErrorStats(): Record<string, { count: number; lastOccurrence: Date }> {
    const stats: Record<string, { count: number; lastOccurrence: Date }> = {};
    
    for (const [operation, count] of Array.from(this.errorCounts.entries())) {
      const lastOccurrence = this.lastErrors.get(operation);
      if (lastOccurrence) {
        stats[operation] = { count, lastOccurrence };
      }
    }
    
    return stats;
  }

  static clearStats(): void {
    this.errorCounts.clear();
    this.lastErrors.clear();
  }
}

// Database operation error boundary
export function withDatabaseErrorBoundary<T>(
  operation: () => Promise<T>,
  operationName: string,
  requestId?: string
): Promise<T> {
  return ErrorBoundary.withErrorBoundary(operation, {
    context: {
      operation: `database:${operationName}`,
      ...(requestId && { requestId }),
    },
    shouldReportError: (error) => {
      // Don't report validation errors or expected database errors
      return !error.message.includes('duplicate key') &&
             !error.message.includes('not found') &&
             !(error as any).code?.startsWith('23'); // PostgreSQL constraint violations
    },
    onError: (error, context) => {
      // Custom database error handling
      if (error.message.includes('terminating connection')) {
        console.warn('🔄 Database connection terminated, will retry automatically');
      } else if (error.message.includes('connection timeout')) {
        console.warn('⏱️ Database operation timed out');
      }
    },
  });
}

// API operation error boundary
export function withAPIErrorBoundary<T>(
  operation: () => Promise<T>,
  operationName: string,
  req?: Request,
  fallbackResponse?: T
): Promise<T> {
  return ErrorBoundary.withErrorBoundary(operation, {
    context: {
      operation: `api:${operationName}`,
      requestId: (req as any)?.id,
      userId: (req as any)?.user?.id,
      metadata: {
        method: req?.method,
        url: req?.originalUrl,
        userAgent: req?.get('User-Agent'),
        ip: req?.ip,
      },
    },
    fallbackResponse,
    onError: (error, context) => {
      // Custom API error handling
      if (error.name === 'ValidationError') {
        console.warn(`⚠️ Validation error in ${context.operation}`);
      } else if (error.message.includes('rate limit')) {
        console.warn(`🚦 Rate limit exceeded for ${context.operation}`);
      }
    },
  });
}

// External service error boundary
export function withExternalServiceErrorBoundary<T>(
  operation: () => Promise<T>,
  serviceName: string,
  fallbackValue?: T
): Promise<T> {
  return ErrorBoundary.withErrorBoundary(operation, {
    context: {
      operation: `external:${serviceName}`,
    },
    fallbackResponse: fallbackValue,
    shouldReportError: (error) => {
      // Always report external service errors
      return true;
    },
    onError: (error, context) => {
      console.error(`🌐 External service ${serviceName} error:`, error.message);
      
      // Could implement circuit breaker logic here
      if (error.message.includes('timeout')) {
        console.warn(`⏱️ ${serviceName} timeout - consider implementing circuit breaker`);
      }
    },
  });
}

// Express middleware for global error boundary
export function errorBoundaryMiddleware(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  ErrorBoundary.withErrorBoundary(
    async () => {
      throw error; // Re-throw to log properly
    },
    {
      context: {
        operation: 'middleware:global-error-handler',
        requestId: (req as any)?.id,
        userId: (req as any)?.user?.id,
        metadata: {
          method: req.method,
          url: req.originalUrl,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        },
      },
    }
  ).catch(() => {
    // Error was logged, now send appropriate response
    
    if (res.headersSent) {
      return next(error);
    }

    // Determine error type and response
    let statusCode = 500;
    let message = 'Internal Server Error';

    if (error instanceof z.ZodError) {
      statusCode = 400;
      message = 'Validation Error';
    } else if (error.name === 'ValidationError') {
      statusCode = 400;
      message = error.message;
    } else if (error.message.includes('not found')) {
      statusCode = 404;
      message = 'Not Found';
    } else if (error.message.includes('unauthorized')) {
      statusCode = 401;
      message = 'Unauthorized';
    } else if (error.message.includes('forbidden')) {
      statusCode = 403;
      message = 'Forbidden';
    } else if (error.message.includes('rate limit')) {
      statusCode = 429;
      message = 'Too Many Requests';
    }

    // Don't expose internal errors in production
    if (process.env.NODE_ENV === 'production' && statusCode === 500) {
      message = 'Internal Server Error';
    }

    res.status(statusCode).json({
      success: false,
      error: message,
      requestId: (req as any)?.id,
      timestamp: new Date().toISOString(),
    });
  });
}

// Health check for error boundary system
export function getErrorBoundaryHealth(): {
  status: 'healthy' | 'warning' | 'critical';
  stats: Record<string, { count: number; lastOccurrence: Date }>;
  recommendations: string[];
} {
  const stats = ErrorBoundary.getErrorStats();
  const recommendations: string[] = [];
  let status: 'healthy' | 'warning' | 'critical' = 'healthy';

  // Analyze error patterns
  for (const [operation, { count, lastOccurrence }] of Object.entries(stats)) {
    const hoursAgo = (Date.now() - lastOccurrence.getTime()) / (1000 * 60 * 60);
    
    if (count > 100 && hoursAgo < 1) {
      status = 'critical';
      recommendations.push(`High error rate in ${operation}: ${count} errors in last hour`);
    } else if (count > 50 && hoursAgo < 1) {
      status = status === 'critical' ? 'critical' : 'warning';
      recommendations.push(`Elevated error rate in ${operation}: ${count} errors in last hour`);
    }
  }

  if (recommendations.length === 0) {
    recommendations.push('Error rates are within normal limits');
  }

  return { status, stats, recommendations };
}

export { ErrorBoundary };