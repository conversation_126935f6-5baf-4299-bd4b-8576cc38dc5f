import { ValidationError } from '../middlewares/errorHandler';
import { logger } from '../services/LoggerService';

/**
 * Secure input validation utilities to prevent injection attacks and type confusion
 */

/**
 * Safely parse an integer from request parameters with validation
 */
export function parseSecureInteger(
  value: string | undefined, 
  fieldName: string,
  options: {
    min?: number;
    max?: number;
    required?: boolean;
  } = {}
): number {
  const { min = 1, max = Number.MAX_SAFE_INTEGER, required = true } = options;

  // Check if value exists
  if (!value || value.trim() === '') {
    if (required) {
      logger.warn(`Missing required integer parameter: ${fieldName}`, 'security');
      throw new ValidationError(`Missing required parameter: ${fieldName}`);
    }
    return 0;
  }

  // Basic format validation
  if (!/^\d+$/.test(value.trim())) {
    logger.warn(`Invalid integer format for parameter: ${fieldName}`, 'security', {
      value: value.substring(0, 50) + (value.length > 50 ? '...' : ''),
      fieldName
    });
    throw new ValidationError(`Invalid ${fieldName} format. Must be a positive integer.`);
  }

  // Parse to number
  const parsed = parseInt(value.trim(), 10);

  // Check for parsing errors
  if (isNaN(parsed) || !isFinite(parsed)) {
    logger.warn(`Failed to parse integer parameter: ${fieldName}`, 'security', {
      value: value.substring(0, 50) + (value.length > 50 ? '...' : ''),
      fieldName
    });
    throw new ValidationError(`Invalid ${fieldName}. Must be a valid integer.`);
  }

  // Range validation
  if (parsed < min || parsed > max) {
    logger.warn(`Integer parameter out of range: ${fieldName}`, 'security', {
      value: parsed,
      min,
      max,
      fieldName
    });
    throw new ValidationError(`${fieldName} must be between ${min} and ${max}`);
  }

  return parsed;
}

/**
 * Safely parse a string with length and pattern validation
 */
export function parseSecureString(
  value: string | undefined,
  fieldName: string,
  options: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    allowEmpty?: boolean;
    required?: boolean;
    sanitize?: boolean;
  } = {}
): string {
  const { 
    minLength = 0, 
    maxLength = 1000, 
    pattern, 
    allowEmpty = false, 
    required = true,
    sanitize = true 
  } = options;

  // Check if value exists
  if (!value) {
    if (required) {
      logger.warn(`Missing required string parameter: ${fieldName}`, 'security');
      throw new ValidationError(`Missing required parameter: ${fieldName}`);
    }
    return '';
  }

  let sanitized = sanitize ? sanitizeString(value) : value;

  // Check empty string
  if (!allowEmpty && sanitized.trim() === '') {
    logger.warn(`Empty string parameter: ${fieldName}`, 'security');
    throw new ValidationError(`${fieldName} cannot be empty`);
  }

  // Length validation
  if (sanitized.length < minLength || sanitized.length > maxLength) {
    logger.warn(`String parameter length out of range: ${fieldName}`, 'security', {
      length: sanitized.length,
      minLength,
      maxLength,
      fieldName
    });
    throw new ValidationError(`${fieldName} must be between ${minLength} and ${maxLength} characters`);
  }

  // Pattern validation
  if (pattern && !pattern.test(sanitized)) {
    logger.warn(`String parameter pattern validation failed: ${fieldName}`, 'security', {
      pattern: pattern.toString(),
      fieldName
    });
    throw new ValidationError(`${fieldName} format is invalid`);
  }

  return sanitized;
}

/**
 * Sanitize string input to prevent XSS and injection attacks
 */
function sanitizeString(input: string): string {
  return input
    // Remove null bytes
    .replace(/\0/g, '')
    // Limit control characters
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Validate email format securely
 */
export function parseSecureEmail(
  value: string | undefined,
  fieldName: string = 'email',
  required: boolean = true
): string {
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
  return parseSecureString(value, fieldName, {
    minLength: 5,
    maxLength: 254, // RFC 5321 limit
    pattern: emailPattern,
    required,
    sanitize: true
  });
}

/**
 * Validate phone number format securely
 */
export function parseSecurePhone(
  value: string | undefined,
  fieldName: string = 'phone',
  required: boolean = true
): string {
  // Allow international format with optional + and spaces/hyphens
  const phonePattern = /^\+?[\d\s\-\(\)]{10,15}$/;
  
  const cleaned = value ? value.replace(/[\s\-\(\)]/g, '') : '';
  
  return parseSecureString(cleaned, fieldName, {
    minLength: 10,
    maxLength: 15,
    pattern: phonePattern,
    required,
    sanitize: true
  });
}

/**
 * Parse and validate array input securely
 */
export function parseSecureArray<T>(
  value: any,
  fieldName: string,
  parser: (item: any, index: number) => T,
  options: {
    maxLength?: number;
    minLength?: number;
    required?: boolean;
  } = {}
): T[] {
  const { maxLength = 100, minLength = 0, required = true } = options;

  if (!Array.isArray(value)) {
    if (required) {
      logger.warn(`Expected array for parameter: ${fieldName}`, 'security');
      throw new ValidationError(`${fieldName} must be an array`);
    }
    return [];
  }

  if (value.length < minLength || value.length > maxLength) {
    logger.warn(`Array parameter length out of range: ${fieldName}`, 'security', {
      length: value.length,
      minLength,
      maxLength,
      fieldName
    });
    throw new ValidationError(`${fieldName} array must have between ${minLength} and ${maxLength} items`);
  }

  try {
    return value.map((item, index) => parser(item, index));
  } catch (error) {
    logger.warn(`Array parsing failed for parameter: ${fieldName}`, 'security', {
      error: error instanceof Error ? error.message : 'Unknown error',
      fieldName
    });
    throw new ValidationError(`Invalid data in ${fieldName} array`);
  }
}

/**
 * Rate limiting for parameter validation to prevent abuse
 */
const validationAttempts = new Map<string, { count: number; lastAttempt: number }>();

export function checkValidationRateLimit(identifier: string): boolean {
  const now = Date.now();
  const key = `validation:${identifier}`;
  const current = validationAttempts.get(key);
  
  // Clean up old entries
  if (current && now - current.lastAttempt > 60000) { // 1 minute
    validationAttempts.delete(key);
  }
  
  const updated = current && now - current.lastAttempt < 60000
    ? { count: current.count + 1, lastAttempt: now }
    : { count: 1, lastAttempt: now };
  
  validationAttempts.set(key, updated);
  
  // Allow up to 100 validation attempts per minute per IP
  if (updated.count > 100) {
    logger.warn('Validation rate limit exceeded', 'security', {
      identifier,
      attempts: updated.count
    });
    return false;
  }
  
  return true;
}

/**
 * Middleware factory for secure parameter parsing
 */
export function createSecureParamsMiddleware(paramValidators: Record<string, (value: string) => any>) {
  return (req: any, res: any, next: any) => {
    try {
      // Rate limit validation attempts
      const identifier = req.ip || 'unknown';
      if (!checkValidationRateLimit(identifier)) {
        return res.status(429).json({
          error: 'Too many validation attempts',
          code: 'RATE_LIMIT_EXCEEDED'
        });
      }

      // Validate and parse parameters
      for (const [paramName, validator] of Object.entries(paramValidators)) {
        if (req.params[paramName] !== undefined) {
          try {
            req.params[paramName] = validator(req.params[paramName]);
          } catch (error) {
            logger.warn(`Parameter validation failed: ${paramName}`, 'security', {
              value: req.params[paramName],
              error: error instanceof Error ? error.message : 'Unknown error',
              ip: req.ip
            });
            return res.status(400).json({
              error: error instanceof Error ? error.message : 'Invalid parameter',
              field: paramName,
              code: 'VALIDATION_ERROR'
            });
          }
        }
      }

      next();
    } catch (error) {
      logger.error('Secure params middleware error', error instanceof Error ? error : undefined, 'security');
      return res.status(500).json({
        error: 'Parameter validation system error',
        code: 'VALIDATION_SYSTEM_ERROR'
      });
    }
  };
}