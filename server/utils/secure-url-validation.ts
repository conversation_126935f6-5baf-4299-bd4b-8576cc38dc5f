import { ValidationError } from '../middlewares/errorHandler';
import { logger } from '../services/LoggerService';
import { DELAYS } from '../../shared/constants';

/**
 * Secure URL validation utilities to prevent SSRF attacks and malicious content
 */

interface URLValidationOptions {
  allowedProtocols?: string[];
  allowedDomains?: string[];
  blockPrivateIPs?: boolean;
  maxUrlLength?: number;
  contentTypes?: string[];
  requireContentTypeCheck?: boolean;
}

/**
 * Default security configuration for media URLs
 */
const DEFAULT_MEDIA_CONFIG: URLValidationOptions = {
  allowedProtocols: ['https'], // Only HTTPS for security
  allowedDomains: [
    // Cloudinary domains
    'res.cloudinary.com',
    'cloudinary.com',
    // Trusted CDN domains
    'images.unsplash.com',
    'source.unsplash.com',
    // Allow other major CDNs if needed
    'cdn.jsdelivr.net',
    'cdnjs.cloudflare.com',
    // Add your own domains here
  ],
  blockPrivateIPs: true,
  maxUrlLength: 2048,
  contentTypes: [
    // Image types
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // Video types  
    'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'
  ],
  requireContentTypeCheck: false // Set to true in production for stricter validation
};

/**
 * Private IP ranges to block (RFC 1918, RFC 3927, etc.)
 */
const PRIVATE_IP_RANGES = [
  /^10\./,                    // 10.0.0.0/8
  /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
  /^192\.168\./,              // ***********/16
  /^169\.254\./,              // ***********/16 (link-local)
  /^127\./,                   // *********/8 (loopback)
  /^0\./,                     // 0.0.0.0/8
  /^224\./,                   // *********/4 (multicast)
  /^240\./,                   // 240.0.0.0/4 (reserved)
];

/**
 * Validate a single URL for security compliance
 */
export async function validateSecureURL(
  url: string, 
  options: URLValidationOptions = DEFAULT_MEDIA_CONFIG
): Promise<{ valid: boolean; error?: string; sanitizedUrl?: string }> {
  
  const config = { ...DEFAULT_MEDIA_CONFIG, ...options };
  
  try {
    // Basic length check
    if (url.length > config.maxUrlLength!) {
      logger.warn('URL too long', 'security', { urlLength: url.length, maxLength: config.maxUrlLength });
      return { valid: false, error: 'URL too long' };
    }

    // Parse URL
    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch (error) {
      logger.warn('Invalid URL format', 'security', { url: url.substring(0, 100) });
      return { valid: false, error: 'Invalid URL format' };
    }

    // Protocol validation
    if (!config.allowedProtocols!.includes(parsedUrl.protocol.slice(0, -1))) {
      logger.warn('Disallowed protocol', 'security', { 
        protocol: parsedUrl.protocol,
        allowedProtocols: config.allowedProtocols,
        hostname: parsedUrl.hostname
      });
      return { valid: false, error: `Protocol ${parsedUrl.protocol} not allowed` };
    }

    // Domain validation
    if (!config.allowedDomains!.includes(parsedUrl.hostname)) {
      logger.warn('Disallowed domain', 'security', { 
        hostname: parsedUrl.hostname,
        allowedDomains: config.allowedDomains
      });
      return { valid: false, error: `Domain ${parsedUrl.hostname} not allowed` };
    }

    // Private IP blocking
    if (config.blockPrivateIPs) {
      const isPrivateIP = PRIVATE_IP_RANGES.some(range => range.test(parsedUrl.hostname));
      if (isPrivateIP) {
        logger.error('SSRF attempt detected - private IP', undefined, 'security', { 
          hostname: parsedUrl.hostname,
          url: url.substring(0, 100)
        });
        return { valid: false, error: 'Private IP addresses not allowed' };
      }
    }

    // Additional hostname checks for SSRF prevention
    if (parsedUrl.hostname === 'localhost' || 
        parsedUrl.hostname.endsWith('.internal') ||
        parsedUrl.hostname.endsWith('.local')) {
      logger.error('SSRF attempt detected - suspicious hostname', undefined, 'security', { 
        hostname: parsedUrl.hostname 
      });
      return { valid: false, error: 'Suspicious hostname not allowed' };
    }

    // Content type validation (optional)
    if (config.requireContentTypeCheck && config.contentTypes) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        try {
          const response = await fetch(parsedUrl.toString(), { 
            method: 'HEAD',
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          
          const contentType = response.headers.get('content-type');
          if (!contentType || !config.contentTypes.some(type => contentType.startsWith(type))) {
            logger.warn('Invalid content type', 'security', { 
              contentType,
              allowedTypes: config.contentTypes,
              url: url.substring(0, 100)
            });
            return { valid: false, error: 'Invalid content type' };
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          throw fetchError;
        }
      } catch (error) {
        logger.warn('Content type check failed', 'security', { 
          error: error instanceof Error ? error.message : 'Unknown error',
          url: url.substring(0, 100)
        });
        return { valid: false, error: 'Unable to verify content type' };
      }
    }

    // Sanitize URL (remove fragments, normalize)
    const sanitizedUrl = `${parsedUrl.protocol}//${parsedUrl.host}${parsedUrl.pathname}${parsedUrl.search}`;

    logger.debug('URL validation successful', 'security', { 
      originalUrl: url.substring(0, 100),
      sanitizedUrl: sanitizedUrl.substring(0, 100),
      domain: parsedUrl.hostname
    });

    return { valid: true, sanitizedUrl };

  } catch (error) {
    logger.error('URL validation failed', error instanceof Error ? error : undefined, 'security', {
      url: url.substring(0, 100),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return { valid: false, error: 'URL validation failed' };
  }
}

/**
 * Validate an array of URLs for media content
 */
export async function validateMediaURLs(
  urls: string[],
  mediaType: 'image' | 'video' = 'image',
  maxCount: number = 20
): Promise<{ validUrls: string[]; errors: string[] }> {
  
  if (urls.length > maxCount) {
    logger.warn('Too many URLs provided', 'security', { 
      count: urls.length, 
      maxCount,
      mediaType 
    });
    throw new ValidationError(`Too many ${mediaType} URLs. Maximum ${maxCount} allowed.`);
  }

  const contentTypes = mediaType === 'image' 
    ? DEFAULT_MEDIA_CONFIG.contentTypes!.filter(type => type.startsWith('image/'))
    : DEFAULT_MEDIA_CONFIG.contentTypes!.filter(type => type.startsWith('video/'));

  const validUrls: string[] = [];
  const errors: string[] = [];

  // Process URLs sequentially to avoid overwhelming external services
  for (let i = 0; i < urls.length; i++) {
    const url = urls[i];
    
    if (typeof url !== 'string' || !url.trim()) {
      errors.push(`${mediaType} ${i + 1}: Invalid URL format`);
      continue;
    }

    const validation = await validateSecureURL(url.trim(), {
      ...DEFAULT_MEDIA_CONFIG,
      contentTypes,
      requireContentTypeCheck: false // Set to true for stricter validation
    });

    if (validation.valid && validation.sanitizedUrl) {
      validUrls.push(validation.sanitizedUrl);
    } else {
      errors.push(`${mediaType} ${i + 1}: ${validation.error || 'Validation failed'}`);
    }

    // Small delay to prevent overwhelming external services
    if (i < urls.length - 1) {
      await new Promise(resolve => setTimeout(resolve, DELAYS.SOUND_SEQUENCE_SHORT));
    }
  }

  logger.info('Media URL validation completed', 'security', {
    mediaType,
    totalProvided: urls.length,
    validCount: validUrls.length,
    errorCount: errors.length
  });

  return { validUrls, errors };
}

/**
 * Express middleware for validating media URLs in request body
 */
export function validateMediaURLsMiddleware() {
  return async (req: any, res: any, next: any) => {
    try {
      const { images = [], videos = [] } = req.body;

      // Validate images
      if (images.length > 0) {
        const imageValidation = await validateMediaURLs(images, 'image', 10);
        if (imageValidation.errors.length > 0) {
          logger.warn('Image URL validation failed', 'security', {
            errors: imageValidation.errors,
            ip: req.ip
          });
          return res.status(400).json({
            error: 'Invalid image URLs',
            details: imageValidation.errors,
            code: 'INVALID_IMAGE_URLS'
          });
        }
        req.body.images = imageValidation.validUrls;
      }

      // Validate videos
      if (videos.length > 0) {
        const videoValidation = await validateMediaURLs(videos, 'video', 5);
        if (videoValidation.errors.length > 0) {
          logger.warn('Video URL validation failed', 'security', {
            errors: videoValidation.errors,
            ip: req.ip
          });
          return res.status(400).json({
            error: 'Invalid video URLs',
            details: videoValidation.errors,
            code: 'INVALID_VIDEO_URLS'
          });
        }
        req.body.videos = videoValidation.validUrls;
      }

      next();
    } catch (error) {
      logger.error('Media URL validation middleware error', error instanceof Error ? error : undefined, 'security');
      return res.status(400).json({
        error: error instanceof Error ? error.message : 'URL validation failed',
        code: 'URL_VALIDATION_ERROR'
      });
    }
  };
}

/**
 * Check if a domain is in the allowed list
 */
export function isDomainAllowed(hostname: string): boolean {
  return DEFAULT_MEDIA_CONFIG.allowedDomains!.includes(hostname);
}

/**
 * Add a domain to the allowed list (admin function)
 */
export function addAllowedDomain(domain: string): void {
  if (!DEFAULT_MEDIA_CONFIG.allowedDomains!.includes(domain)) {
    DEFAULT_MEDIA_CONFIG.allowedDomains!.push(domain);
    logger.info('Added allowed domain', 'security', { domain });
  }
}

/**
 * Remove a domain from the allowed list (admin function)
 */
export function removeAllowedDomain(domain: string): void {
  const index = DEFAULT_MEDIA_CONFIG.allowedDomains!.indexOf(domain);
  if (index > -1) {
    DEFAULT_MEDIA_CONFIG.allowedDomains!.splice(index, 1);
    logger.info('Removed allowed domain', 'security', { domain });
  }
}

/**
 * Validate existing media URL (stricter validation for URLs that should already be in the system)
 */
export async function validateExistingMediaURL(url: string): Promise<boolean> {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    const parsedUrl = new URL(url);
    
    // Must be HTTPS
    if (parsedUrl.protocol !== 'https:') {
      logger.warn('Non-HTTPS URL in existing media', 'security', { url: url.substring(0, 100) });
      return false;
    }
    
    // Must be from allowed domains
    if (!DEFAULT_MEDIA_CONFIG.allowedDomains!.includes(parsedUrl.hostname)) {
      logger.warn('Disallowed domain in existing media', 'security', { 
        hostname: parsedUrl.hostname,
        url: url.substring(0, 100)
      });
      return false;
    }
    
    return true;
  } catch (error) {
    logger.warn('Invalid existing media URL', 'security', { 
      url: url.substring(0, 100),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

/**
 * Middleware for validating existing media URLs in request body
 */
export function validateExistingMediaURLMiddleware() {
  return async (req: any, res: any, next: any) => {
    try {
      const { mediaUrl, newOrder } = req.body;

      // Validate single media URL
      if (mediaUrl && typeof mediaUrl === 'string') {
        const isValid = await validateExistingMediaURL(mediaUrl);
        if (!isValid) {
          logger.warn('Invalid existing media URL provided', 'security', {
            mediaUrl: mediaUrl.substring(0, 100),
            ip: req.ip,
            userAgent: req.headers['user-agent']
          });
          return res.status(400).json({
            error: 'Invalid media URL',
            code: 'INVALID_MEDIA_URL'
          });
        }
      }

      // Validate array of media URLs (for bulk operations)
      if (newOrder && Array.isArray(newOrder)) {
        if (newOrder.length > 50) { // Reasonable limit
          return res.status(400).json({
            error: 'Too many media URLs in request',
            code: 'TOO_MANY_MEDIA_URLS'
          });
        }

        for (let i = 0; i < newOrder.length; i++) {
          const url = newOrder[i];
          if (typeof url === 'string' && url.trim()) {
            const isValid = await validateExistingMediaURL(url);
            if (!isValid) {
              logger.warn('Invalid existing media URL in array', 'security', {
                mediaUrl: url.substring(0, 100),
                index: i,
                ip: req.ip,
                userAgent: req.headers['user-agent']
              });
              return res.status(400).json({
                error: `Invalid media URL at position ${i + 1}`,
                code: 'INVALID_MEDIA_URL_IN_ARRAY'
              });
            }
          }
        }
      }

      next();
    } catch (error) {
      logger.error('Existing media URL validation middleware error', error instanceof Error ? error : undefined, 'security');
      return res.status(400).json({
        error: 'Media URL validation failed',
        code: 'MEDIA_URL_VALIDATION_ERROR'
      });
    }
  };
}