import { z } from 'zod';
import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';

// Phone number validation schema
export const phoneSchema = z.string()
  .min(10, 'Phone number must be at least 10 digits')
  .max(15, 'Phone number cannot exceed 15 digits')
  .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
  .transform((phone) => {
    // Normalize phone number
    const cleaned = phone.replace(/\D/g, '');
    
    // Handle Indian numbers
    if (cleaned.length === 10 && cleaned.startsWith('6789'.charAt(0))) {
      return `+91${cleaned}`;
    }
    
    // Handle international numbers
    if (cleaned.length > 10) {
      return `+${cleaned}`;
    }
    
    throw new Error('Invalid phone number');
  });

// Email validation schema
export const emailSchema = z.string()
  .email('Invalid email format')
  .min(5, 'Email must be at least 5 characters')
  .max(100, 'Email cannot exceed 100 characters')
  .toLowerCase()
  .transform((email) => email.trim());

// OTP validation schema
export const otpSchema = z.string()
  .length(6, 'OTP must be exactly 6 digits')
  .regex(/^\d{6}$/, 'OTP must contain only digits');

// Identifier validation (phone or email)
export const identifierSchema = z.string()
  .min(5, 'Identifier must be at least 5 characters')
  .max(100, 'Identifier cannot exceed 100 characters')
  .refine((identifier) => {
    // Check if it's a valid phone number or email
    try {
      phoneSchema.parse(identifier);
      return true;
    } catch {
      try {
        emailSchema.parse(identifier);
        return true;
      } catch {
        return false;
      }
    }
  }, 'Identifier must be a valid phone number or email');

// Rate limiting configurations
export const createRateLimiter = (windowMs: number, max: number, message: string) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: message,
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        success: false,
        error: message,
        retryAfter: Math.ceil(windowMs / 1000),
        timestamp: new Date().toISOString()
      });
    }
  });
};

// OTP specific rate limiter
export const otpRateLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts per 15 minutes
  'Too many OTP requests. Please try again later.'
);

// General API rate limiter
export const apiRateLimiter = createRateLimiter(
  60 * 1000, // 1 minute
  30, // 30 requests per minute
  'Too many API requests. Please try again later.'
);

// Aggressive rate limiter for auth endpoints
export const authRateLimiter = createRateLimiter(
  5 * 60 * 1000, // 5 minutes
  10, // 10 attempts per 5 minutes
  'Too many authentication attempts. Please try again later.'
);

// Input sanitization middleware
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitize = (obj: any): any => {
    if (typeof obj === 'string') {
      return obj.trim().slice(0, 1000); // Limit string length
    }
    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof key === 'string' && key.length <= 100) {
          sanitized[key] = sanitize(value);
        }
      }
      return sanitized;
    }
    return obj;
  };

  req.body = sanitize(req.body);
  req.query = sanitize(req.query);
  req.params = sanitize(req.params);
  
  next();
};

// Validation middleware factory
export const validateInput = (schema: z.ZodSchema): (req: Request, res: Response, next: NextFunction) => void => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          })),
          timestamp: new Date().toISOString()
        });
        return;
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  };
};

// Security headers middleware
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), camera=(), microphone=()');
  
  // Only add HSTS in production
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=********; includeSubDomains');
  }
  
  next();
};

// Environment validation
export const validateEnvironment = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Always required variables
  const alwaysRequired = ['DATABASE_URL'];
  
  // Only required in production
  const productionRequired = [
    'JWT_SECRET',
    'COOKIE_SECRET',
    'SESSION_SECRET'
  ];

  const productionOptional = [
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN', 
    'SENDGRID_API_KEY'
  ];

  // Check always required variables
  const missingAlways = alwaysRequired.filter(envVar => !process.env[envVar]);
  if (missingAlways.length > 0) {
    throw new Error(`Missing required environment variables: ${missingAlways.join(', ')}`);
  }

  // Check production requirements
  if (isProduction) {
    const missingProd = productionRequired.filter(envVar => !process.env[envVar]);
    if (missingProd.length > 0) {
      throw new Error(`Missing production environment variables: ${missingProd.join(', ')}`);
    }
    
    const missingOptional = productionOptional.filter(envVar => !process.env[envVar]);
    if (missingOptional.length > 0) {
      console.warn(`Optional production environment variables missing: ${missingOptional.join(', ')}`);
      console.warn('Some features may not work properly without these variables');
    }
  }

  // Validate secret lengths only if they exist
  const secrets = ['JWT_SECRET', 'COOKIE_SECRET', 'SESSION_SECRET'];
  for (const secret of secrets) {
    if (process.env[secret] && process.env[secret]!.length < 32) {
      if (isProduction) {
        throw new Error(`${secret} must be at least 32 characters long in production`);
      } else {
        console.warn(`${secret} should be at least 32 characters long for better security`);
      }
    }
  }
};

// OTP request validation schema
export const otpRequestSchema = z.object({
  identifier: identifierSchema,
  type: z.enum(['sms', 'email'], {
    errorMap: () => ({ message: 'Type must be either "sms" or "email"' })
  })
});

// OTP verification schema
export const otpVerificationSchema = z.object({
  identifier: identifierSchema,
  code: otpSchema,
  type: z.enum(['sms', 'email'])
});

// Registration schema
export const registrationSchema = z.object({
  phone: phoneSchema,
  email: emailSchema,
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(50, 'Full name cannot exceed 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Full name can only contain letters and spaces'),
  role: z.enum(['user', 'owner'])
});

export default {
  phoneSchema,
  emailSchema,
  otpSchema,
  identifierSchema,
  createRateLimiter,
  otpRateLimiter,
  apiRateLimiter,
  authRateLimiter,
  sanitizeInput,
  validateInput,
  securityHeaders,
  validateEnvironment,
  otpRequestSchema,
  otpVerificationSchema,
  registrationSchema
};