/**
 * ✅ STORAGE LAYER ERROR HANDLING UTILITIES
 * 
 * Provides consistent error handling for database operations
 * with automatic retry logic and detailed error context.
 */

import { DatabaseError, NotFoundError, ConflictError, ErrorContext } from '../errors';
import { logger } from '../services/LoggerService';

// Retry configuration
interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  delayMs: 100,
  backoffMultiplier: 2,
  retryableErrors: ['ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND', 'EAI_AGAIN']
};

/**
 * Wraps storage methods with consistent error handling
 */
export function wrapStorageMethod<T extends (...args: any[]) => Promise<any>>(
  methodName: string,
  method: T,
  options?: { 
    throwNotFound?: boolean;
    retryConfig?: Partial<RetryConfig>;
  }
): T {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...options?.retryConfig };
  
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    let lastError: any;
    let attempt = 0;
    
    while (attempt < retryConfig.maxAttempts) {
      attempt++;
      
      try {
        const result = await method(...args);
        
        // Handle methods that should throw on not found
        if (options?.throwNotFound && (result === undefined || result === null)) {
          throw new NotFoundError(methodName.replace(/^get/, ''));
        }
        
        return result;
      } catch (error: any) {
        lastError = error;
        
        // Don't retry if it's already our error type
        if (error instanceof DatabaseError || 
            error instanceof NotFoundError || 
            error instanceof ConflictError) {
          throw error;
        }
        
        // Check if error is retryable
        const isRetryable = retryConfig.retryableErrors.some(code => 
          error.code === code || error.message?.includes(code)
        );
        
        if (!isRetryable || attempt >= retryConfig.maxAttempts) {
          // Convert to DatabaseError with context
          const context: ErrorContext = {
            service: 'storage',
            operation: methodName,
            metadata: {
              attempt,
              args: args.length > 0 ? args[0] : undefined, // Log first arg (usually ID)
              errorCode: error.code,
              errorDetail: error.detail
            }
          };
          
          throw convertToDatabaseError(error, methodName, context);
        }
        
        // Calculate delay with exponential backoff
        const delay = retryConfig.delayMs * Math.pow(retryConfig.backoffMultiplier, attempt - 1);
        
        logger.warn(`Storage operation ${methodName} failed, retrying...`, 'storage-wrapper', {
          attempt,
          maxAttempts: retryConfig.maxAttempts,
          delay,
          error: error.message
        });
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // Should never reach here, but just in case
    throw lastError;
  }) as T;
}

/**
 * Convert database-specific errors to our DatabaseError type
 */
function convertToDatabaseError(error: any, operation: string, context: ErrorContext): DatabaseError {
  // PostgreSQL unique constraint violation
  if (error.code === '23505') {
    const match = error.detail?.match(/Key \((\w+)\)=\(([^)]+)\) already exists/);
    if (match) {
      return new ConflictError(`${match[1]} '${match[2]}' already exists`, context);
    }
    return new ConflictError('Duplicate key violation', context);
  }
  
  // PostgreSQL foreign key violation
  if (error.code === '23503') {
    const match = error.detail?.match(/Key \((\w+)\)=\(([^)]+)\) is not present/);
    if (match) {
      return new NotFoundError(`Referenced ${match[1]} '${match[2]}'`, context);
    }
    return new DatabaseError('Foreign key constraint violation', error.detail, error, context);
  }
  
  // PostgreSQL not null violation
  if (error.code === '23502') {
    const match = error.detail?.match(/column "(\w+)"/);
    if (match) {
      return new DatabaseError(`Required field '${match[1]}' is missing`, error.detail, error, context);
    }
    return new DatabaseError('Required field is missing', error.detail, error, context);
  }
  
  // PostgreSQL check constraint violation
  if (error.code === '23514') {
    return new DatabaseError('Check constraint violation', error.detail, error, context);
  }
  
  // Connection errors
  if (error.code === 'ECONNREFUSED') {
    return new DatabaseError('Database connection refused. Please check if the database is running.', undefined, error, context);
  }
  
  if (error.code === 'ETIMEDOUT') {
    return new DatabaseError('Database connection timed out', undefined, error, context);
  }
  
  // Default database error
  return new DatabaseError(
    error.message || `Database operation '${operation}' failed`,
    error.query,
    error,
    context
  );
}

/**
 * Batch operation wrapper with transaction support
 */
export async function wrapBatchOperation<T>(
  operationName: string,
  operations: Array<() => Promise<any>>,
  options?: {
    stopOnError?: boolean;
    transaction?: any; // Your transaction object
  }
): Promise<T[]> {
  const results: T[] = [];
  const errors: Array<{ index: number; error: any }> = [];
  
  for (let i = 0; i < operations.length; i++) {
    try {
      const result = await operations[i]();
      results.push(result);
    } catch (error) {
      errors.push({ index: i, error });
      
      if (options?.stopOnError) {
        // Rollback transaction if provided
        if (options.transaction?.rollback) {
          await options.transaction.rollback();
        }
        
        throw new DatabaseError(
          `Batch operation '${operationName}' failed at index ${i}`,
          undefined,
          error,
          {
            service: 'storage',
            operation: operationName,
            metadata: {
              failedIndex: i,
              totalOperations: operations.length,
              errors
            }
          }
        );
      }
    }
  }
  
  // If there were errors but stopOnError was false, log them
  if (errors.length > 0) {
    logger.warn(`Batch operation '${operationName}' completed with errors`, 'storage-wrapper', {
      successCount: results.length,
      errorCount: errors.length,
      errors: errors.map(e => ({ index: e.index, error: e.error.message }))
    });
  }
  
  return results;
}

/**
 * Create a storage method wrapper with automatic logging
 */
export function createStorageMethod<T extends (...args: any[]) => Promise<any>>(
  methodName: string,
  implementation: T,
  options?: {
    logArgs?: boolean;
    logResult?: boolean;
    sensitiveFields?: string[];
  }
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    const startTime = Date.now();
    
    try {
      // Log method call
      if (options?.logArgs) {
        logger.debug(`Storage method ${methodName} called`, 'storage', {
          args: sanitizeArgs(args, options.sensitiveFields)
        });
      }
      
      const result = await implementation(...args);
      
      // Log successful result
      const duration = Date.now() - startTime;
      logger.debug(`Storage method ${methodName} completed`, 'storage', {
        duration,
        ...(options?.logResult && { result: sanitizeResult(result, options.sensitiveFields) })
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log error
      logger.error(`Storage method ${methodName} failed`, error instanceof Error ? error : undefined, 'storage', {
        duration,
        args: options?.logArgs ? sanitizeArgs(args, options.sensitiveFields) : undefined
      });
      
      throw error;
    }
  }) as T;
}

/**
 * Sanitize sensitive data from logs
 */
function sanitizeArgs(args: any[], sensitiveFields?: string[]): any[] {
  if (!sensitiveFields || sensitiveFields.length === 0) {
    return args;
  }
  
  return args.map(arg => {
    if (typeof arg === 'object' && arg !== null) {
      const sanitized = { ...arg };
      for (const field of sensitiveFields) {
        if (field in sanitized) {
          sanitized[field] = '[REDACTED]';
        }
      }
      return sanitized;
    }
    return arg;
  });
}

function sanitizeResult(result: any, sensitiveFields?: string[]): any {
  if (!sensitiveFields || sensitiveFields.length === 0 || !result) {
    return result;
  }
  
  if (Array.isArray(result)) {
    return result.map(item => sanitizeResult(item, sensitiveFields));
  }
  
  if (typeof result === 'object') {
    const sanitized = { ...result };
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }
    return sanitized;
  }
  
  return result;
}

// Export utility functions
export default {
  wrapStorageMethod,
  wrapBatchOperation,
  createStorageMethod,
  convertToDatabaseError
};