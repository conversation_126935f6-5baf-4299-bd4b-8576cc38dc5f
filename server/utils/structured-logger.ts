import { randomUUID } from 'crypto';
import { Request, Response, NextFunction } from 'express';

export interface LogContext {
  correlationId?: string;
  userId?: string;
  operation?: string;
  component?: string;
  requestId?: string;
  sessionId?: string;
  ipAddress?: string | undefined;
  userAgent?: string | undefined;
  [key: string]: any;
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace'
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  metadata?: Record<string, any>;
  duration?: number;
  error?: {
    name: string;
    message: string;
    stack?: string | undefined;
    code?: string | undefined;
  } | undefined;
}

export class StructuredLogger {
  private static instance: StructuredLogger;
  private static currentContext: LogContext = {};
  
  static getInstance(): StructuredLogger {
    if (!StructuredLogger.instance) {
      StructuredLogger.instance = new StructuredLogger();
    }
    return StructuredLogger.instance;
  }
  
  /**
   * Set current logging context (used by middleware)
   */
  static setContext(context: LogContext): void {
    StructuredLogger.currentContext = { ...StructuredLogger.currentContext, ...context };
  }
  
  /**
   * Get current logging context
   */
  static getContext(): LogContext {
    return { ...StructuredLogger.currentContext };
  }
  
  /**
   * Clear current context
   */
  static clearContext(): void {
    StructuredLogger.currentContext = {};
  }
  
  /**
   * Generate a new correlation ID
   */
  static generateCorrelationId(): string {
    return randomUUID();
  }
  
  /**
   * Create a child logger with additional context
   */
  static child(additionalContext: LogContext): ChildLogger {
    return new ChildLogger({ ...StructuredLogger.currentContext, ...additionalContext });
  }
  
  /**
   * Log with specific level
   */
  private logWithLevel(
    level: LogLevel,
    message: string,
    context: LogContext = {},
    metadata?: Record<string, any>,
    error?: Error
  ): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: { ...StructuredLogger.currentContext, ...context },
      ...(metadata ? { metadata } : {}),
      ...(error ? {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
          code: (error as any).code
        }
      } : {})
    };
    
    this.output(entry);
  }
  
  /**
   * Get minimum log level based on environment
   */
  private getMinLogLevel(): LogLevel {
    const env = process.env.NODE_ENV || 'development';
    const logLevelOverride = process.env.LOG_LEVEL?.toLowerCase();
    
    // Allow override via environment variable
    if (logLevelOverride) {
      switch (logLevelOverride) {
        case 'error': return LogLevel.ERROR;
        case 'warn': return LogLevel.WARN;
        case 'info': return LogLevel.INFO;
        case 'debug': return LogLevel.DEBUG;
        case 'trace': return LogLevel.TRACE;
      }
    }
    
    // Default levels by environment
    switch (env) {
      case 'production':
        return LogLevel.WARN; // Only warnings and errors in production
      case 'test':
        return LogLevel.ERROR; // Only errors in tests
      default:
        return LogLevel.INFO; // INFO level for development (excludes DEBUG/TRACE)
    }
  }

  /**
   * Check if log level should be output
   */
  private shouldLog(level: LogLevel): boolean {
    const minLevel = this.getMinLogLevel();
    const levels = [LogLevel.ERROR, LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG, LogLevel.TRACE];
    
    return levels.indexOf(level) <= levels.indexOf(minLevel);
  }

  /**
   * Output log entry (can be overridden for different outputs)
   */
  private output(entry: LogEntry): void {
    // Respect log level filtering
    if (!this.shouldLog(entry.level)) {
      return;
    }

    const isDevelopment = process.env.NODE_ENV === 'development';
    
    if (isDevelopment) {
      // Pretty console output for development
      this.prettyConsoleOutput(entry);
    } else {
      // JSON output for production (for log aggregation)
      console.log(JSON.stringify(entry));
    }
  }
  
  /**
   * Pretty console output for development
   */
  private prettyConsoleOutput(entry: LogEntry): void {
    const { timestamp, level, message, context, metadata, error, duration } = entry;
    
    // Color coding for different log levels
    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[35m', // Magenta
      trace: '\x1b[37m'  // White
    };
    const reset = '\x1b[0m';
    
    const color = colors[level] || reset;
    const levelUpper = level.toUpperCase().padEnd(5);
    
    let logLine = `${color}[${timestamp}] ${levelUpper}${reset} ${message}`;
    
    // Add correlation ID if present
    if (context.correlationId) {
      logLine += ` [${context.correlationId.substring(0, 8)}...]`;
    }
    
    // Add duration if present
    if (duration !== undefined) {
      logLine += ` (${duration}ms)`;
    }
    
    console.log(logLine);
    
    // Add context if present
    if (Object.keys(context).length > 0) {
      console.log(`  Context: ${JSON.stringify(context, null, 2)}`);
    }
    
    // Add metadata if present
    if (metadata && Object.keys(metadata).length > 0) {
      console.log(`  Metadata: ${JSON.stringify(metadata, null, 2)}`);
    }
    
    // Add error details if present
    if (error) {
      console.log(`  Error: ${error.name}: ${error.message}`);
      if (error.stack) {
        console.log(`  Stack: ${error.stack}`);
      }
    }
  }
  
  /**
   * Public logging methods
   */
  error(message: string, context?: LogContext, metadata?: Record<string, any>, error?: Error): void {
    this.logWithLevel(LogLevel.ERROR, message, context, metadata, error);
  }
  
  warn(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.logWithLevel(LogLevel.WARN, message, context, metadata);
  }
  
  info(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.logWithLevel(LogLevel.INFO, message, context, metadata);
  }
  
  debug(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.logWithLevel(LogLevel.DEBUG, message, context, metadata);
  }
  
  trace(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.logWithLevel(LogLevel.TRACE, message, context, metadata);
  }
  
  /**
   * Log an operation with timing
   */
  async logOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    context?: LogContext
  ): Promise<T> {
    const startTime = Date.now();
    const operationContext = { ...context, operation };
    
    this.info(`Starting operation: ${operation}`, operationContext);
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      this.info(`Operation completed: ${operation}`, operationContext, { duration });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.error(
        `Operation failed: ${operation}`,
        operationContext,
        { duration },
        error as Error
      );
      
      throw error;
    }
  }
}

/**
 * Child logger with fixed context
 */
export class ChildLogger {
  constructor(private context: LogContext) {}
  
  private getLogger(): StructuredLogger {
    return StructuredLogger.getInstance();
  }
  
  error(message: string, additionalContext?: LogContext, metadata?: Record<string, any>, error?: Error): void {
    this.getLogger().error(message, { ...this.context, ...additionalContext }, metadata, error);
  }
  
  warn(message: string, additionalContext?: LogContext, metadata?: Record<string, any>): void {
    this.getLogger().warn(message, { ...this.context, ...additionalContext }, metadata);
  }
  
  info(message: string, additionalContext?: LogContext, metadata?: Record<string, any>): void {
    this.getLogger().info(message, { ...this.context, ...additionalContext }, metadata);
  }
  
  debug(message: string, additionalContext?: LogContext, metadata?: Record<string, any>): void {
    this.getLogger().debug(message, { ...this.context, ...additionalContext }, metadata);
  }
  
  trace(message: string, additionalContext?: LogContext, metadata?: Record<string, any>): void {
    this.getLogger().trace(message, { ...this.context, ...additionalContext }, metadata);
  }
  
  async logOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    additionalContext?: LogContext
  ): Promise<T> {
    return this.getLogger().logOperation(operation, fn, { ...this.context, ...additionalContext });
  }
}

/**
 * Express middleware to add correlation ID and request context
 */
export function correlationMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Generate or extract correlation ID
  const correlationId = req.headers['x-correlation-id'] as string || StructuredLogger.generateCorrelationId();
  const requestId = randomUUID();
  
  // Set correlation ID in response header
  res.setHeader('x-correlation-id', correlationId);
  
  // Build request context
  const ipAddress = req.ip || req.connection.remoteAddress;
  const userAgent = req.headers['user-agent'];
  
  const requestContext: LogContext = {
    correlationId,
    requestId,
    method: req.method,
    url: req.originalUrl || req.url,
    component: 'http-request',
    ipAddress: ipAddress || undefined,
    userAgent: userAgent || undefined
  };
  
  // Add user context if available
  if ((req as any).user?.id) {
    requestContext.userId = (req as any).user.id;
  }
  
  // Set context for this request
  StructuredLogger.setContext(requestContext);
  
  // Create child logger for this request
  const logger = StructuredLogger.child(requestContext);
  
  // Attach logger to request object
  (req as any).logger = logger;
  
  // Log incoming request
  logger.info(`Incoming ${req.method} request`, undefined, {
    headers: req.headers,
    query: req.query,
    body: req.method !== 'GET' ? req.body : undefined
  });
  
  // Log response when finished
  res.on('finish', () => {
    logger.info(`Request completed`, undefined, {
      statusCode: res.statusCode,
      contentLength: res.getHeader('content-length')
    });
  });
  
  // Clear context after request
  res.on('close', () => {
    StructuredLogger.clearContext();
  });
  
  next();
}

/**
 * Performance timing decorator
 */
export function logTiming(operation: string) {
  return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function(...args: any[]) {
      const logger = StructuredLogger.getInstance();
      return logger.logOperation(operation, () => method.apply(this, args));
    };
    
    return descriptor;
  };
}

/**
 * Singleton logger instance
 */
export const logger = StructuredLogger.getInstance();

/**
 * Express request logger type extension
 */
declare global {
  namespace Express {
    interface Request {
      logger?: ChildLogger;
    }
  }
}

export default StructuredLogger;