import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { cacheService } from '../services/CacheService';
import { log } from './logger';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
  onLimitReached?: (req: Request, res: Response) => void;
}

interface RateLimitRecord {
  count: number;
  resetTime: number;
  blocked: boolean;
  blockUntil?: number;
}

export class AdvancedRateLimiter {
  private static instance: AdvancedRateLimiter;
  private rateLimiters = new Map<string, any>();

  static getInstance(): AdvancedRateLimiter {
    if (!AdvancedRateLimiter.instance) {
      AdvancedRateLimiter.instance = new AdvancedRateLimiter();
    }
    return AdvancedRateLimiter.instance;
  }

  // Create or get rate limiter for a specific endpoint
  createRateLimiter(name: string, config: RateLimitConfig) {
    if (this.rateLimiters.has(name)) {
      return this.rateLimiters.get(name);
    }

    const limiter = rateLimit({
      windowMs: config.windowMs,
      max: config.maxRequests,
      message: {
        error: config.message,
        retryAfter: Math.ceil(config.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: config.keyGenerator || ((req: Request) => {
        return req.ip || req.connection.remoteAddress || 'unknown';
      }),
      skip: (req: Request) => {
        // Skip rate limiting for development
        if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
          return true;
        }
        return false;
      },
      handler: (req: Request, res: Response) => {
        const key = (config.keyGenerator || ((r: Request) => r.ip || 'unknown'))(req);
        
        log(`🚫 Rate limit exceeded for ${name}: ${key}`, 'rate-limiting');
        
        if (config.onLimitReached) {
          config.onLimitReached(req, res);
        }

        res.status(429).json({
          error: config.message,
          retryAfter: Math.ceil(config.windowMs / 1000),
          timestamp: new Date().toISOString()
        });
      }
    });

    this.rateLimiters.set(name, limiter);
    return limiter;
  }

  // Advanced rate limiting with cache-based tracking
  async checkRateLimit(
    identifier: string,
    operation: string,
    config: {
      maxRequests: number;
      windowMs: number;
      blockDurationMs?: number;
      progressiveDelay?: boolean;
    }
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  }> {
    const key = `rate_limit:${operation}:${identifier}`;
    const now = Date.now();
    
    try {
      const record = await cacheService.get<RateLimitRecord>(key);
      
      if (!record) {
        // First request
        await cacheService.set(key, {
          count: 1,
          resetTime: now + config.windowMs,
          blocked: false
        }, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: now + config.windowMs
        };
      }

      // Check if currently blocked
      if (record.blocked && record.blockUntil && now < record.blockUntil) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: record.resetTime,
          retryAfter: Math.ceil((record.blockUntil - now) / 1000)
        };
      }

      // Check if window has expired
      if (now >= record.resetTime) {
        // Reset counter
        await cacheService.set(key, {
          count: 1,
          resetTime: now + config.windowMs,
          blocked: false
        }, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: now + config.windowMs
        };
      }

      // Increment counter
      const newCount = record.count + 1;
      
      if (newCount > config.maxRequests) {
        // Rate limit exceeded
        const blockDuration = config.blockDurationMs || config.windowMs;
        const blockUntil = now + blockDuration;
        
        await cacheService.set(key, {
          ...record,
          count: newCount,
          blocked: true,
          blockUntil
        }, Math.ceil(blockDuration / 1000));
        
        log(`🚫 Rate limit exceeded for ${operation}:${identifier} (${newCount}/${config.maxRequests})`, 'rate-limiting');
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: record.resetTime,
          retryAfter: Math.ceil(blockDuration / 1000)
        };
      }

      // Update counter
      await cacheService.set(key, {
        ...record,
        count: newCount
      }, Math.ceil((record.resetTime - now) / 1000));
      
      return {
        allowed: true,
        remaining: config.maxRequests - newCount,
        resetTime: record.resetTime
      };

    } catch (error) {
      log(`❌ Rate limiting error for ${operation}:${identifier}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'rate-limiting');
      
      // Fail open - allow request if rate limiting fails
      return {
        allowed: true,
        remaining: config.maxRequests,
        resetTime: now + config.windowMs
      };
    }
  }

  // Progressive rate limiting (increases delay with each violation)
  async checkProgressiveRateLimit(
    identifier: string,
    operation: string,
    config: {
      maxRequests: number;
      windowMs: number;
      baseDelayMs: number;
      maxDelayMs: number;
      multiplier: number;
    }
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  }> {
    const violationKey = `violations:${operation}:${identifier}`;
    const violations = await cacheService.get<number>(violationKey) || 0;
    
    const dynamicDelay = Math.min(
      config.baseDelayMs * Math.pow(config.multiplier, violations),
      config.maxDelayMs
    );
    
    const result = await this.checkRateLimit(identifier, operation, {
      ...config,
      blockDurationMs: dynamicDelay
    });
    
    if (!result.allowed) {
      // Increment violation count
      await cacheService.set(violationKey, violations + 1, 3600); // 1 hour
      
      log(`📈 Progressive rate limit violation ${violations + 1} for ${operation}:${identifier}`, 'rate-limiting');
    }
    
    return result;
  }

  // Clear rate limit for specific identifier
  async clearRateLimit(identifier: string, operation: string): Promise<void> {
    const key = `rate_limit:${operation}:${identifier}`;
    const violationKey = `violations:${operation}:${identifier}`;
    
    await Promise.all([
      cacheService.delete(key),
      cacheService.delete(violationKey)
    ]);
    
    log(`🧹 Rate limit cleared for ${operation}:${identifier}`, 'rate-limiting');
  }

  // Get rate limit status
  async getRateLimitStatus(identifier: string, operation: string): Promise<{
    current: number;
    limit: number;
    remaining: number;
    resetTime: number;
    blocked: boolean;
    violations: number;
  }> {
    const key = `rate_limit:${operation}:${identifier}`;
    const violationKey = `violations:${operation}:${identifier}`;
    
    const [record, violations] = await Promise.all([
      cacheService.get<RateLimitRecord>(key),
      cacheService.get<number>(violationKey)
    ]);
    
    if (!record) {
      return {
        current: 0,
        limit: 0,
        remaining: 0,
        resetTime: 0,
        blocked: false,
        violations: violations || 0
      };
    }
    
    return {
      current: record.count,
      limit: 0, // Would need to store this in config
      remaining: Math.max(0, (0) - record.count), // Would need actual limit
      resetTime: record.resetTime,
      blocked: record.blocked || false,
      violations: violations || 0
    };
  }
}

// Pre-configured rate limiters
export const rateLimiters = {
  // API rate limiting
  api: (maxRequests: number = 100, windowMs: number = 60000) => {
    return AdvancedRateLimiter.getInstance().createRateLimiter('api', {
      windowMs,
      maxRequests,
      message: 'Too many API requests from this IP, please try again later.',
      keyGenerator: (req: Request) => req.ip || 'unknown'
    });
  },

  // Authentication rate limiting
  auth: (maxRequests: number = 5, windowMs: number = 900000) => { // 15 minutes
    return AdvancedRateLimiter.getInstance().createRateLimiter('auth', {
      windowMs,
      maxRequests,
      message: 'Too many authentication attempts, please try again later.',
      keyGenerator: (req: Request) => req.ip || 'unknown',
      onLimitReached: (req: Request, res: Response) => {
        log(`🚨 Authentication rate limit exceeded for IP: ${req.ip}`, 'rate-limiting');
      }
    });
  },

  // OTP rate limiting
  otp: (maxRequests: number = 3, windowMs: number = 300000) => { // 5 minutes
    return AdvancedRateLimiter.getInstance().createRateLimiter('otp', {
      windowMs,
      maxRequests,
      message: 'Too many OTP requests, please try again later.',
      keyGenerator: (req: Request) => {
        const identifier = req.body?.identifier || req.ip;
        return `otp:${identifier}`;
      }
    });
  },

  // Registration rate limiting
  registration: (maxRequests: number = 3, windowMs: number = 3600000) => { // 1 hour
    return AdvancedRateLimiter.getInstance().createRateLimiter('registration', {
      windowMs,
      maxRequests,
      message: 'Too many registration attempts, please try again later.',
      keyGenerator: (req: Request) => req.ip || 'unknown'
    });
  },

  // Password reset rate limiting
  passwordReset: (maxRequests: number = 3, windowMs: number = 3600000) => { // 1 hour
    return AdvancedRateLimiter.getInstance().createRateLimiter('passwordReset', {
      windowMs,
      maxRequests,
      message: 'Too many password reset attempts, please try again later.',
      keyGenerator: (req: Request) => {
        const email = req.body?.email || req.ip;
        return `reset:${email}`;
      }
    });
  },

  // File upload rate limiting
  fileUpload: (maxRequests: number = 10, windowMs: number = 60000) => { // 1 minute
    return AdvancedRateLimiter.getInstance().createRateLimiter('fileUpload', {
      windowMs,
      maxRequests,
      message: 'Too many file uploads, please try again later.',
      keyGenerator: (req: Request) => {
        const userId = (req as any).user?.id || req.ip;
        return `upload:${userId}`;
      }
    });
  },

  // Search rate limiting
  search: (maxRequests: number = 30, windowMs: number = 60000) => { // 1 minute
    return AdvancedRateLimiter.getInstance().createRateLimiter('search', {
      windowMs,
      maxRequests,
      message: 'Too many search requests, please try again later.',
      keyGenerator: (req: Request) => req.ip || 'unknown'
    });
  },

  // Booking rate limiting
  booking: (maxRequests: number = 5, windowMs: number = 300000) => { // 5 minutes
    return AdvancedRateLimiter.getInstance().createRateLimiter('booking', {
      windowMs,
      maxRequests,
      message: 'Too many booking attempts, please try again later.',
      keyGenerator: (req: Request) => {
        const userId = (req as any).user?.id || req.ip;
        return `booking:${userId}`;
      }
    });
  }
};

// Rate limiting middleware factory
export function createRateLimitMiddleware(
  operation: string,
  config: {
    maxRequests: number;
    windowMs: number;
    keyGenerator?: (req: Request) => string;
    progressive?: boolean;
  }
): (req: Request, res: Response, next: any) => Promise<void> {
  return async (req: Request, res: Response, next: any): Promise<void> => {
    const rateLimiter = AdvancedRateLimiter.getInstance();
    const identifier = config.keyGenerator ? config.keyGenerator(req) : req.ip || 'unknown';
    
    try {
      const result = config.progressive
        ? await rateLimiter.checkProgressiveRateLimit(identifier, operation, {
            ...config,
            baseDelayMs: config.windowMs,
            maxDelayMs: config.windowMs * 10,
            multiplier: 2
          })
        : await rateLimiter.checkRateLimit(identifier, operation, config);
      
      if (!result.allowed) {
        res.status(429).json({
          error: 'Rate limit exceeded',
          retryAfter: result.retryAfter || Math.ceil(config.windowMs / 1000),
          resetTime: result.resetTime,
          timestamp: new Date().toISOString()
        });
        return;
      }
      
      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': config.maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      });
      
      next();
    } catch (error) {
      log(`❌ Rate limit middleware error for ${operation}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'rate-limiting');
      next(); // Fail open
    }
  };
}

// IP-based rate limiting
export function ipRateLimit(maxRequests: number = 100, windowMs: number = 60000) {
  return createRateLimitMiddleware('ip', {
    maxRequests,
    windowMs,
    keyGenerator: (req: Request) => req.ip || 'unknown'
  });
}

// User-based rate limiting
export function userRateLimit(maxRequests: number = 1000, windowMs: number = 60000) {
  return createRateLimitMiddleware('user', {
    maxRequests,
    windowMs,
    keyGenerator: (req: Request) => {
      const userId = (req as any).user?.id;
      return userId ? `user:${userId}` : req.ip || 'unknown';
    }
  });
}

// Endpoint-specific rate limiting
export function endpointRateLimit(endpoint: string, maxRequests: number = 50, windowMs: number = 60000) {
  return createRateLimitMiddleware(endpoint, {
    maxRequests,
    windowMs,
    keyGenerator: (req: Request) => `${endpoint}:${req.ip || 'unknown'}`
  });
}

// Export the rate limiter instance
export const rateLimiter = AdvancedRateLimiter.getInstance();