import { z } from 'zod';
import { log } from './logger';

// Environment validation schemas
const requiredStringSchema = z.string().min(1, 'Cannot be empty');
const optionalStringSchema = z.string().optional();
const portSchema = z.string().regex(/^\d+$/, 'Must be a valid port number').transform(Number);
const booleanSchema = z.string().transform(val => val.toLowerCase() === 'true');

// Database URL validation
const databaseUrlSchema = z.string()
  .startsWith('postgresql://', 'Must be a valid PostgreSQL connection string')
  .min(20, 'Database URL seems too short');

// JWT Secret validation (minimum 32 characters for security)
const jwtSecretSchema = z.string()
  .min(32, 'JWT secret must be at least 32 characters for security')
  .max(512, 'JWT secret too long');

// API Key validation (basic format checking)
const twilioAuthTokenSchema = z.string()
  .min(32, 'Twilio auth token seems too short')
  .max(64, 'Twilio auth token seems too long');

const sendgridApiKeySchema = z.string()
  .startsWith('SG.', 'SendGrid API key must start with SG.')
  .min(20, 'SendGrid API key seems too short');

const cloudinaryApiKeySchema = z.string()
  .regex(/^\d+$/, 'Cloudinary API key must be numeric')
  .min(10, 'Cloudinary API key seems too short');

// Complete environment schema
const environmentSchema = z.object({
  // Core application
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  PORT: portSchema.default('5000'),
  
  // Database (required)
  DATABASE_URL: databaseUrlSchema,
  
  // Security secrets (required in production)
  JWT_SECRET: jwtSecretSchema,
  COOKIE_SECRET: jwtSecretSchema,
  SESSION_SECRET: jwtSecretSchema,
  
  // Twilio SMS (required for SMS features)
  TWILIO_ACCOUNT_SID: requiredStringSchema,
  TWILIO_AUTH_TOKEN: twilioAuthTokenSchema,
  TWILIO_MESSAGING_SID: requiredStringSchema,
  DLT_ENTITY_ID: requiredStringSchema,
  
  // Email service (required for email features)
  SENDGRID_API_KEY: sendgridApiKeySchema,
  
  // File storage (required for image uploads)
  CLOUDINARY_CLOUD_NAME: requiredStringSchema,
  CLOUDINARY_API_KEY: cloudinaryApiKeySchema,
  CLOUDINARY_API_SECRET: requiredStringSchema,
  
  // Optional configuration
  USE_SECURE_COOKIES: booleanSchema.default('false'),
  ENABLE_SECURITY_HEADERS: booleanSchema.default('true'),
  ENABLE_RATE_LIMITING: booleanSchema.default('true'),
  DB_MAX_CONNECTIONS: optionalStringSchema,
  
  // Optional database connection details (alternative format)
  PGDATABASE: optionalStringSchema,
  PGHOST: optionalStringSchema,
  PGPORT: optionalStringSchema,
  PGUSER: optionalStringSchema,
  PGPASSWORD: optionalStringSchema,
});

export type ValidatedEnvironment = z.infer<typeof environmentSchema>;

interface ValidationResult {
  success: boolean;
  data?: ValidatedEnvironment;
  errors?: string[];
  warnings?: string[];
}

export class EnvironmentValidator {
  private static validatedEnv: ValidatedEnvironment | null = null;
  
  /**
   * Validate environment variables with detailed error reporting
   */
  static validate(): ValidationResult {
    const warnings: string[] = [];
    
    try {
      // Parse and validate environment
      const parsed = environmentSchema.parse(process.env);
      
      // Additional production checks
      if (parsed.NODE_ENV === 'production') {
        const productionWarnings = this.validateProductionEnvironment(parsed);
        warnings.push(...productionWarnings);
      }
      
      // Store validated environment
      this.validatedEnv = parsed;
      
      log('✅ Environment validation passed', 'environment');
      
      if (warnings.length > 0) {
        log(`⚠️ Environment warnings: ${warnings.join(', ')}`, 'environment');
      }
      
      return {
        success: true,
        data: parsed,
        warnings
      };
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        );
        
        log(`❌ Environment validation failed: ${errors.join(', ')}`, 'environment');
        
        return {
          success: false,
          errors
        };
      }
      
      log(`❌ Environment validation error: ${error}`, 'environment');
      
      return {
        success: false,
        errors: ['Unknown validation error']
      };
    }
  }
  
  /**
   * Get validated environment (throws if not validated)
   */
  static getValidatedEnv(): ValidatedEnvironment {
    if (!this.validatedEnv) {
      throw new Error('Environment not validated. Call EnvironmentValidator.validate() first.');
    }
    return this.validatedEnv;
  }
  
  /**
   * Validate production-specific requirements
   */
  private static validateProductionEnvironment(env: ValidatedEnvironment): string[] {
    const warnings: string[] = [];
    
    // Check for secure cookies in production
    if (!env.USE_SECURE_COOKIES) {
      warnings.push('USE_SECURE_COOKIES should be true in production');
    }
    
    // Check secret strength
    if (env.JWT_SECRET.length < 64) {
      warnings.push('JWT_SECRET should be at least 64 characters in production');
    }
    
    // Check for development patterns in secrets
    const devPatterns = ['test', 'dev', 'local', '123', 'abc'];
    for (const pattern of devPatterns) {
      if (env.JWT_SECRET.toLowerCase().includes(pattern)) {
        warnings.push('JWT_SECRET appears to contain development patterns');
        break;
      }
    }
    
    // Check database connection
    if (env.DATABASE_URL.includes('localhost') || env.DATABASE_URL.includes('127.0.0.1')) {
      warnings.push('DATABASE_URL points to localhost in production');
    }
    
    return warnings;
  }
  
  /**
   * Mask sensitive values for logging
   */
  static getMaskedEnvForLogging(): Record<string, string> {
    const env = this.getValidatedEnv();
    
    return {
      NODE_ENV: env.NODE_ENV,
      PORT: env.PORT.toString(),
      DATABASE_URL: this.maskDatabaseUrl(env.DATABASE_URL),
      JWT_SECRET: this.maskSecret(env.JWT_SECRET),
      COOKIE_SECRET: this.maskSecret(env.COOKIE_SECRET),
      SESSION_SECRET: this.maskSecret(env.SESSION_SECRET),
      TWILIO_ACCOUNT_SID: this.maskSecret(env.TWILIO_ACCOUNT_SID),
      TWILIO_AUTH_TOKEN: this.maskSecret(env.TWILIO_AUTH_TOKEN),
      TWILIO_MESSAGING_SID: this.maskSecret(env.TWILIO_MESSAGING_SID),
      DLT_ENTITY_ID: env.DLT_ENTITY_ID,
      SENDGRID_API_KEY: this.maskSecret(env.SENDGRID_API_KEY),
      CLOUDINARY_CLOUD_NAME: env.CLOUDINARY_CLOUD_NAME,
      CLOUDINARY_API_KEY: this.maskSecret(env.CLOUDINARY_API_KEY),
      CLOUDINARY_API_SECRET: this.maskSecret(env.CLOUDINARY_API_SECRET),
      USE_SECURE_COOKIES: env.USE_SECURE_COOKIES.toString(),
      ENABLE_SECURITY_HEADERS: env.ENABLE_SECURITY_HEADERS.toString(),
      ENABLE_RATE_LIMITING: env.ENABLE_RATE_LIMITING.toString(),
    };
  }
  
  /**
   * Check if environment is development
   */
  static isDevelopment(): boolean {
    return this.getValidatedEnv().NODE_ENV === 'development';
  }
  
  /**
   * Check if environment is production
   */
  static isProduction(): boolean {
    return this.getValidatedEnv().NODE_ENV === 'production';
  }
  
  /**
   * Check if environment is test
   */
  static isTest(): boolean {
    return this.getValidatedEnv().NODE_ENV === 'test';
  }
  
  /**
   * Mask database URL for logging
   */
  private static maskDatabaseUrl(url: string): string {
    try {
      const parsed = new URL(url);
      if (parsed.password) {
        parsed.password = '***';
      }
      return parsed.toString();
    } catch {
      return url.substring(0, 20) + '***';
    }
  }
  
  /**
   * Mask secret for logging
   */
  private static maskSecret(secret: string): string {
    if (secret.length <= 8) {
      return '***';
    }
    return secret.substring(0, 4) + '***' + secret.substring(secret.length - 4);
  }
}

/**
 * Validate environment on module load in non-test environments
 */
if (process.env.NODE_ENV !== 'test') {
  const result = EnvironmentValidator.validate();
  
  if (!result.success) {
    console.error('❌ Environment validation failed:');
    result.errors?.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }
  
  if (result.warnings && result.warnings.length > 0) {
    console.warn('⚠️ Environment warnings:');
    result.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
}

export default EnvironmentValidator;