// Standardized error types for consistent error handling across the application

export interface ApiError {
  message: string;
  code: string | undefined;
  status: number | undefined;
  details: unknown | undefined;
}

export class AppError extends Error {
  public readonly status: number;
  public readonly code: string | undefined;
  public readonly details: unknown | undefined;

  constructor(message: string, status: number = 500, code?: string, details?: unknown) {
    super(message);
    this.name = 'AppError';
    this.status = status;
    this.code = code;
    this.details = details;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: unknown) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Access forbidden') {
    super(message, 403, 'FORBIDDEN');
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Database operation failed', details?: unknown) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message?: string) {
    super(message || `${service} service unavailable`, 503, 'EXTERNAL_SERVICE_ERROR');
  }
}

// Type guard to check if error is our custom AppError
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}

// Helper to format error responses consistently
export function formatErrorResponse(error: unknown): ApiError {
  if (isAppError(error)) {
    return {
      message: error.message,
      code: error.code || undefined,
      status: error.status,
      details: error.details || undefined
    };
  }

  if (error instanceof Error) {
    return {
      message: error.message,
      code: undefined,
      status: 500,
      details: undefined
    };
  }

  return {
    message: 'An unexpected error occurred',
    code: undefined,
    status: 500,
    details: undefined
  };
}