// Load environment configuration FIRST before any other imports
import "./env-loader";

import express, { type Request, Response, NextFunction } from "express";
import compression from "compression";
import { registerRoutes } from "./routes/index";
import { serveStatic, log } from "./static-server";
import { config } from "./config";
import cookieParser from "cookie-parser";
import crypto from "crypto";
import helmet from "helmet";
import { join } from "path";
import { 
  globalErrorHandler, 
  notFoundHandler, 
  addRequestId 
} from "./middlewares/errorHandler";
import { errorBoundaryMiddleware } from "./utils/error-boundary";
import { logger } from "./services/LoggerService";
import { 
  contentSecurityPolicy, 
  securityHeaders, 
  cspReportHandler, 
  enhancedSecurity 
} from "./middlewares/security";
import { setupDeveloperExperience } from "./config/DeveloperExperience";
import { resetDatabaseConnection, closeDatabase } from "./db";
import { databaseManager } from "./utils/database";
import { bookingCleanupService } from "./services/BookingCleanupService";
import { webSocketService } from "./services/WebSocketService";

// Define Express request type with user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: number;
        role: string;
      };
      csrfToken?: () => string;
    }
  }
}

const app = express();

// Set Express environment to match NODE_ENV
app.set('env', process.env.NODE_ENV || 'development');

// Add request ID to all requests for tracking
app.use(addRequestId);

// Enable compression for better performance
app.use(compression({
  filter: (req: Request, res: Response) => {
    // Don't compress responses if the client doesn't support it
    if (req.headers['x-no-compression']) {
      return false;
    }
    // Fall back to standard filter function
    return compression.filter(req, res);
  },
  level: config.isProduction() ? 6 : 1, // Higher compression in production
  threshold: 1024, // Only compress if over 1KB
  memLevel: 8,
}));

// Trust proxy for deployment environments (more secure configuration)
if (config.isProduction()) {
  app.set('trust proxy', 1); // Trust first proxy only
} else {
  // In Replit development environment, we still need to trust proxy
  // because Replit uses proxies even in development
  app.set('trust proxy', 1);
}

// Enhanced security headers
app.use(securityHeaders);
app.use(contentSecurityPolicy);

// CSP violation reporting
app.use(cspReportHandler);

// Ensure required environment variables are set for production deployment
if (config.isProduction()) {
  // Configuration validation is already handled by the config service
  // It will throw an error if required variables are missing

  // Warn about optional but recommended variables
  const recommendedVars = ['SESSION_SECRET', 'COOKIE_SECRET'];
  const missingRecommended = recommendedVars.filter(varName => !process.env[varName]);
  if (missingRecommended.length) {
    console.warn(`Recommended environment variables missing: ${missingRecommended.join(', ')}`);
    console.warn('Using auto-generated secrets - set these for consistent sessions across deployments');
  }
}

// Set up cookie parser with a secure secret
app.use(cookieParser(config.cookie.secret));

// Serve static files from public directory
app.use('/uploads', express.static(join(process.cwd(), 'public/uploads')));

// JSON and form parsing (must come before CSRF middleware)
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Enhanced security measures for sensitive endpoints
app.use('/api/auth', enhancedSecurity);
app.use('/api/admin', enhancedSecurity);
app.use('/api/users', enhancedSecurity);

// Set up caching policies
app.use((req, res, next) => {
  // Only apply no-cache headers to API routes and HTML, not static assets
  if (req.path.startsWith('/api') || req.path === '/' || req.path.endsWith('.html')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  } else if (req.path.startsWith('/assets') || req.path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg)$/)) {
    // Allow caching for static assets with proper cache control
    if (config.isProduction()) {
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
    } else {
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    }
  }

  next();
});

// HTTP request logging is now handled by the structured logging middleware
// This duplicate logger has been removed to reduce log noise
// If you need additional debugging, use LOG_LEVEL=debug environment variable

(async () => {
  const server = await registerRoutes(app);

  // Initialize WebSocket server
  webSocketService.initialize(server);

  // Setup developer experience features in development
  if (app.get("env") === "development") {
    await setupDeveloperExperience(app, {
      hotReload: { 
        enabled: true,
        watchEnvironmentFiles: true,
        watchConfigFiles: true,
        debounceMs: 500
      },
      seeding: { 
        autoSeedOnStartup: false, // Set to true if you want auto-seeding
        defaultScenario: 'development',
        outputCredentials: true
      },
      documentation: { 
        enabled: true, 
        autoGenerate: true,
        serveOnRoute: '/docs',
        includeExamples: true
      },
      middleware: {
        enableReloadEndpoints: true,
        enableSeedingEndpoints: true,
        enableDocumentationEndpoints: true
      }
    });
  }

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    try {
      const { setupCustomVite } = await import("./custom-vite");
      await setupCustomVite(app, server);
      logger.debug("Vite middleware enabled successfully", 'vite-dev');
    } catch (error) {
      logger.warn("Vite middleware disabled due to rollup issue", 'vite-dev', { 
        error: error instanceof Error ? error.message : String(error),
        suggestion: "Try: rm -rf node_modules package-lock.json && npm install"
      });
      serveStatic(app);
    }
  } else {
    serveStatic(app);
  }

  // Global error handlers must be AFTER all routes and static file serving
  app.use(errorBoundaryMiddleware);
  app.use(notFoundHandler);
  app.use(globalErrorHandler);

  // Add global error handlers to prevent crashes
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    logger.error('Uncaught Exception occurred', error, 'process');
    
    // Check if this is a recoverable database connection error
    const isDatabaseConnectionError = error.message?.includes('terminating connection') || 
        error.message?.includes('connection terminated') ||
        error.message?.includes('Connection terminated') ||
        error.message?.includes('ECONNRESET') ||
        error.message?.includes('ENOTFOUND') ||
        error.message?.includes('connection closed');
    
    if (isDatabaseConnectionError) {
      console.log('🔄 Database connection error detected, attempting recovery...');
      resetDatabaseConnection().catch(err => {
        console.error('❌ Failed to reset database connection:', err);
      });
      
      // For database errors, log but don't exit - allow recovery
      console.log('✅ Server continuing to run, database will reconnect automatically');
      return;
    }
    
    // For non-database critical errors, still exit after delay
    setTimeout(() => {
      console.error('💥 Process will exit due to uncaught exception');
      process.exit(1);
    }, 1000);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    logger.error('Unhandled Promise Rejection', reason instanceof Error ? reason : new Error(String(reason)), 'process', { 
      promise: String(promise) 
    });
    
    // Check if this is a recoverable database connection rejection
    const isDatabaseConnectionError = String(reason).includes('terminating connection') || 
        String(reason).includes('connection terminated') ||
        String(reason).includes('Connection terminated') ||
        String(reason).includes('ECONNRESET') ||
        String(reason).includes('ENOTFOUND') ||
        String(reason).includes('connection closed');
    
    if (isDatabaseConnectionError) {
      console.log('🔄 Database connection rejection detected, attempting recovery...');
      resetDatabaseConnection().catch(err => {
        console.error('❌ Failed to reset database connection:', err);
      });
      
      // For database errors, log but don't exit - allow recovery
      console.log('✅ Server continuing to run, database will reconnect automatically');
      return;
    }
    
    // For non-database critical rejections, consider exiting after delay
    console.warn('⚠️ Non-database unhandled rejection - monitoring for stability');
  });

  // Add graceful shutdown handlers
  const gracefulShutdown = async (signal: string) => {
    console.log(`\nReceived ${signal}, starting graceful shutdown...`);
    logger.info(`Graceful shutdown initiated`, 'shutdown', { signal });

    try {
      // Stop booking cleanup service
      bookingCleanupService.stop();
      
      // Close HTTP server
      await new Promise<void>((resolve, reject) => {
        server.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('HTTP server closed');
            resolve();
          }
        });
      });

      // Close database connections
      await closeDatabase();

      console.log('Graceful shutdown completed');
      logger.info('Graceful shutdown completed', 'shutdown');
      process.exit(0);
    } catch (error) {
      console.error('Error during graceful shutdown:', error);
      logger.error('Error during graceful shutdown', error instanceof Error ? error : new Error(String(error)), 'shutdown');
      process.exit(1);
    }
  };

  // Listen for shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // Test database connection on startup
  databaseManager.testConnection().then(success => {
    if (!success) {
      console.warn('Database connection test failed during startup');
    }
  }).catch(err => {
    console.error('Error testing database connection during startup:', err);
  });

  // Initialize booking cleanup service
  try {
    bookingCleanupService.initialize();
  } catch (error) {
    console.error('Failed to initialize booking cleanup service:', error);
    logger.error('Failed to initialize booking cleanup service', error instanceof Error ? error : new Error(String(error)), 'startup');
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen(port, "0.0.0.0", () => {
    log(`serving on port ${port}`);
    logger.info(`Server started successfully on port ${port}`, 'startup', {
      port,
      environment: config.app.nodeEnv,
      nodeVersion: process.version,
      uptime: process.uptime()
    });
  });
})();