/**
 * Naming Convention Transformation Demo
 * 
 * This file demonstrates the complete naming convention transformation
 * from database (snake_case) to API/Frontend (camelCase) and shows
 * how the transformation layer works in practice.
 */

import {
  toCamelCase,
  toSnakeCase,
  transformDatabaseResult,
  transformApiRequest,
  transformWithMapping,
  COMMON_FIELD_MAPPINGS
} from '../shared/case-transformer';

// =============================================
// BEFORE: Inconsistent Naming (Problems)
// =============================================

// ❌ PROBLEM: Mixed naming conventions
const problematicApiResponse = {
  success: true,
  data: {
    id: 1,
    full_name: "<PERSON>",          // snake_case in API response
    createdAt: "2024-01-01",        // camelCase in API response  
    booking_type: "full_day",       // snake_case in API response
    halfDayPrice: 1500,             // camelCase in API response
    owner_id: 123                   // snake_case in API response
  }
};

// ❌ PROBLEM: Frontend code handling mixed formats
const problematicFrontendCode = {
  // Confusing property access
  displayName: problematicApiResponse.data.full_name,    // snake_case
  bookingType: problematicApiResponse.data.booking_type, // snake_case
  price: problematicApiResponse.data.halfDayPrice,       // camelCase
  created: problematicApiResponse.data.createdAt         // camelCase
};

console.log('❌ PROBLEMATIC (Mixed Conventions):');
console.log('API Response:', JSON.stringify(problematicApiResponse, null, 2));
console.log('Frontend Usage:', problematicFrontendCode);
console.log('\n' + '='.repeat(60) + '\n');

// =============================================
// AFTER: Consistent Naming (Solution)
// =============================================

// ✅ SOLUTION: Database query result (snake_case)
const databaseQueryResult = {
  id: 1,
  full_name: "John Doe",
  email: "<EMAIL>",
  created_at: "2024-01-01T10:00:00Z",
  updated_at: "2024-01-01T10:00:00Z",
  booking_type: "full_day",
  half_day_price: 1500,
  full_day_price: 2500,
  weekday_half_day_price: 1200,
  weekday_full_day_price: 2000,
  weekend_half_day_price: 1800,
  weekend_full_day_price: 3000,
  owner_id: 123,
  property_id: 456,
  user_id: 789,
  special_requests: "Late checkout please",
  total_price: 2500,
  booking_date: "2024-02-15",
  is_verified: true,
  is_active: true
};

console.log('🗄️  DATABASE RESULT (snake_case):');
console.log(JSON.stringify(databaseQueryResult, null, 2));
console.log('\n' + '-'.repeat(40) + '\n');

// ✅ SOLUTION: Transform to API response (camelCase)
const transformedApiResponse = {
  success: true,
  data: transformDatabaseResult(databaseQueryResult),
  message: "Data retrieved successfully",
  meta: {
    timestamp: new Date().toISOString(),
    requestId: "req_123456789"
  }
};

console.log('🌐 API RESPONSE (camelCase):');
console.log(JSON.stringify(transformedApiResponse, null, 2));
console.log('\n' + '-'.repeat(40) + '\n');

// ✅ SOLUTION: Clean frontend usage (consistent camelCase)
const cleanFrontendCode = {
  displayName: transformedApiResponse.data.fullName,        // ✅ camelCase
  email: transformedApiResponse.data.email,                 // ✅ camelCase
  bookingType: transformedApiResponse.data.bookingType,     // ✅ camelCase
  prices: {
    halfDay: transformedApiResponse.data.halfDayPrice,      // ✅ camelCase
    fullDay: transformedApiResponse.data.fullDayPrice,      // ✅ camelCase
    weekdayHalf: transformedApiResponse.data.weekdayHalfDayPrice,  // ✅ camelCase
    weekendFull: transformedApiResponse.data.weekendFullDayPrice   // ✅ camelCase
  },
  metadata: {
    created: transformedApiResponse.data.createdAt,         // ✅ camelCase
    updated: transformedApiResponse.data.updatedAt,         // ✅ camelCase
    ownerId: transformedApiResponse.data.ownerId,           // ✅ camelCase
    propertyId: transformedApiResponse.data.propertyId      // ✅ camelCase
  },
  booking: {
    specialRequests: transformedApiResponse.data.specialRequests,  // ✅ camelCase
    totalPrice: transformedApiResponse.data.totalPrice,           // ✅ camelCase
    bookingDate: transformedApiResponse.data.bookingDate          // ✅ camelCase
  },
  status: {
    isVerified: transformedApiResponse.data.isVerified,     // ✅ camelCase
    isActive: transformedApiResponse.data.isActive          // ✅ camelCase
  }
};

console.log('💻 FRONTEND USAGE (Consistent camelCase):');
console.log(JSON.stringify(cleanFrontendCode, null, 2));
console.log('\n' + '='.repeat(60) + '\n');

// =============================================
// REVERSE TRANSFORMATION EXAMPLE
// =============================================

// ✅ Frontend form data (camelCase)
const frontendFormData = {
  fullName: "Jane Smith",
  email: "<EMAIL>",
  bookingType: "morning",
  specialRequests: "Early check-in required",
  halfDayPrice: 1800,
  fullDayPrice: 2800,
  propertyId: 456,
  bookingDate: "2024-03-01",
  isVerified: false
};

console.log('📝 FRONTEND FORM DATA (camelCase):');
console.log(JSON.stringify(frontendFormData, null, 2));
console.log('\n' + '-'.repeat(40) + '\n');

// ✅ Transform to database format (snake_case)
const databaseInsertData = transformApiRequest(frontendFormData);

console.log('🗄️  DATABASE INSERT DATA (snake_case):');
console.log(JSON.stringify(databaseInsertData, null, 2));
console.log('\n' + '='.repeat(60) + '\n');

// =============================================
// COMPONENT INTEGRATION EXAMPLE
// =============================================

// ✅ React component using consistent naming
interface BookingCardProps {
  booking: {
    id: number;
    fullName: string;           // ✅ camelCase
    bookingType: string;        // ✅ camelCase
    totalPrice: number;         // ✅ camelCase
    specialRequests?: string;   // ✅ camelCase
    createdAt: string;          // ✅ camelCase
  };
}

const BookingCard: React.FC<BookingCardProps> = ({ booking }) => {
  // ✅ All property access uses consistent camelCase
  const displayPrice = booking.totalPrice.toLocaleString('en-IN');
  const bookingDate = new Date(booking.createdAt).toLocaleDateString();
  const hasSpecialRequests = Boolean(booking.specialRequests);
  
  return {
    guestName: booking.fullName,
    type: booking.bookingType,
    price: displayPrice,
    date: bookingDate,
    hasSpecialRequests
  };
};

console.log('⚛️  REACT COMPONENT EXAMPLE:');
console.log('Props interface uses consistent camelCase naming');
console.log('All property access is predictable and IDE-friendly');
console.log('\n' + '='.repeat(60) + '\n');

// =============================================
// VALIDATION & TYPE SAFETY
// =============================================

// ✅ TypeScript interfaces ensure consistency
interface UserProfile {
  id: number;
  fullName: string;        // ✅ camelCase
  email: string;
  createdAt: string;       // ✅ camelCase
  isVerified: boolean;     // ✅ camelCase
  phoneNumber?: string;    // ✅ camelCase
}

interface PropertyListing {
  id: number;
  ownerId: number;              // ✅ camelCase
  title: string;
  halfDayPrice: number;         // ✅ camelCase
  fullDayPrice: number;         // ✅ camelCase
  weekdayHalfDayPrice?: number; // ✅ camelCase
  bookingType: 'morning' | 'full_day';  // ✅ camelCase
  specialRequests?: string;     // ✅ camelCase
  createdAt: string;            // ✅ camelCase
}

// ✅ API client with type safety
class ApiClient {
  async getUser(id: number): Promise<UserProfile> {
    const response = await fetch(`/api/v1/users/${id}`);
    const data = await response.json();
    
    // Transformation happens automatically via middleware
    return data.data as UserProfile;  // ✅ Type-safe camelCase
  }
  
  async createProperty(data: Omit<PropertyListing, 'id' | 'createdAt'>): Promise<PropertyListing> {
    const response = await fetch('/api/v1/properties', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)  // ✅ Sent as camelCase, transformed to snake_case by middleware
    });
    
    const result = await response.json();
    return result.data as PropertyListing;  // ✅ Received as camelCase from transformation
  }
}

console.log('🔒 TYPE SAFETY EXAMPLE:');
console.log('TypeScript interfaces enforce consistent camelCase');
console.log('API client methods use predictable naming');
console.log('Transformation happens transparently');
console.log('\n' + '='.repeat(60) + '\n');

// =============================================
// TESTING THE TRANSFORMATION
// =============================================

// Test case: Complex nested object
const complexTestData = {
  user_profile: {
    full_name: "Test User",
    created_at: "2024-01-01",
    phone_number: "+1234567890"
  },
  property_details: {
    half_day_price: 1500,
    full_day_price: 2500,
    weekday_half_day_price: 1200,
    owner_id: 123
  },
  booking_history: [
    {
      booking_id: 1,
      booking_type: "full_day",
      total_price: 2500,
      special_requests: "Pool access",
      created_at: "2024-01-15"
    },
    {
      booking_id: 2,
      booking_type: "morning",
      total_price: 1500,
      special_requests: null,
      created_at: "2024-01-20"
    }
  ]
};

const transformedTestData = toCamelCase(complexTestData);

console.log('🧪 TRANSFORMATION TEST:');
console.log('Original (snake_case):');
console.log(JSON.stringify(complexTestData, null, 2));
console.log('\nTransformed (camelCase):');
console.log(JSON.stringify(transformedTestData, null, 2));

// Verify transformation integrity
const reverseTransformed = toSnakeCase(transformedTestData);
console.log('\nReverse Transformed (back to snake_case):');
console.log(JSON.stringify(reverseTransformed, null, 2));

const isTransformationValid = JSON.stringify(complexTestData) === JSON.stringify(reverseTransformed);
console.log(`\n✅ Transformation integrity: ${isTransformationValid ? 'PASSED' : 'FAILED'}`);

console.log('\n' + '='.repeat(60));
console.log('🎯 NAMING CONVENTION TRANSFORMATION COMPLETE!');
console.log('✅ Database uses snake_case');
console.log('✅ API responses use camelCase');
console.log('✅ Frontend uses camelCase');
console.log('✅ TypeScript ensures type safety');
console.log('✅ Transformations are automatic and transparent');
console.log('='.repeat(60));

// Export for use in tests or other demonstrations
export {
  databaseQueryResult,
  transformedApiResponse,
  cleanFrontendCode,
  frontendFormData,
  databaseInsertData,
  BookingCard,
  ApiClient,
  complexTestData,
  transformedTestData
};