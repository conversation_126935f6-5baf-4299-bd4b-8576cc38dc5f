// Simple test server to verify basic functionality
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = 3000;

// Serve static files from dist/public
const distPath = path.resolve(__dirname, 'dist', 'public');
console.log('Serving static files from:', distPath);

app.use(express.static(distPath));

// Fallback to index.html for SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.resolve(distPath, 'index.html'));
});

app.listen(port, '0.0.0.0', () => {
  console.log(`✅ Test server running at http://localhost:${port}`);
  console.log(`📂 Static files served from: ${distPath}`);
});