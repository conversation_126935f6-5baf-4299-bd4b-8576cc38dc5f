import { defineConfig } from 'vitest/config'
import { resolve } from 'path'
import { config } from 'dotenv'

// Load environment variables from .env.production
config({ path: resolve(__dirname, '.env.production') })

// WARNING: Integration tests have been disabled to prevent data loss
// The setup file was causing database deletion which could wipe customer data
// Use unit tests or separate test database for integration testing
export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: process.env.DATABASE_URL || 'postgresql://farmhouse_user:farmhouse_password@localhost:5433/farmhouse_rental',
      JWT_SECRET: process.env.JWT_SECRET || 'test-jwt-secret-key-12345',
      SENDGRID_API_KEY: process.env.SENDGRID_API_KEY || 'SG.test_key_for_integration_tests',
      TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID || 'ACtest_account_sid_for_integration_tests',
      TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN || 'test_token',
      TWILIO_MESSAGING_SID: process.env.TWILIO_MESSAGING_SID || 'test_messaging_sid',
      DLT_ENTITY_ID: process.env.DLT_ENTITY_ID || 'test_dlt_entity_id',
      COOKIE_SECRET: process.env.COOKIE_SECRET || 'test-cookie-secret',
      SESSION_SECRET: process.env.SESSION_SECRET || 'test-session-secret',
      CLOUDINARY_CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME || 'test_cloudinary',
      CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY || 'test_cloudinary_key',
      CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET || 'test_cloudinary_secret',
      BKAFARM: process.env.BKAFARM || 'BKAFARM',
    },
    setupFiles: ['./tests/integration/setup.ts'],
    include: [
      'tests/integration/**/*.test.{ts,tsx}'
    ],
    exclude: [
      'tests/unit/**/*',
      'tests/e2e/**/*',
      'node_modules/**/*'
    ],
    testTimeout: 30000, // Longer timeout for integration tests
    hookTimeout: 30000,
    reporters: ['verbose'],
    // Run integration tests sequentially to avoid database conflicts
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './client/src'),
      '@server': resolve(__dirname, './server'),
      '@shared': resolve(__dirname, './shared')
    }
  }
})