@echo off
echo ========================================
echo FARMHOUSE WEBSITE ACCESS HELPER
echo ========================================
echo.
echo Your server is running on WSL!
echo WSL IP Address: **************
echo.
echo Try opening these URLs in your Windows browser:
echo.
echo 1. http://localhost:5000
echo 2. http://127.0.0.1:5000  
echo 3. http://**************:5000
echo.
echo ========================================
echo YouTube Video Features are READY!
echo ========================================
echo.
echo Main Website: http://localhost:5000
echo Owner Dashboard: http://localhost:5000/owner/dashboard
echo Media Management: http://localhost:5000/owner/dashboard
echo API Test: http://localhost:5000/api/test
echo Health Check: http://localhost:5000/health
echo.
echo ========================================
echo Opening browser automatically...
echo ========================================

REM Open the website in default browser
start http://localhost:5000

echo.
echo If the website doesn't load, try:
echo 1. Disable Windows Firewall temporarily
echo 2. Run 'wsl --shutdown' and restart WSL
echo 3. Try the other URLs listed above
echo.
pause