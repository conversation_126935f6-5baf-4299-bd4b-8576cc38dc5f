import React, { createContext, useContext, useEffect, useRef, useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useDashboard } from './DashboardContext';
import { WEBSOCKET, INTERVALS, TIMEOUTS } from '../../../shared/constants';

// WebSocket message types
interface WebSocketMessage {
  type: 'booking_update' | 'property_update' | 'notification' | 'heartbeat';
  data: any;
  timestamp: number;
}

// WebSocket context type
interface WebSocketContextType {
  isConnected: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'reconnecting';
  reconnectAttempts: number;
  sendMessage: (message: WebSocketMessage) => void;
  connect: () => void;
  disconnect: () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

// Custom hook to use WebSocket context
export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

// WebSocket Provider Component
interface WebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { user, setConnectionState, playNotificationSound } = useDashboard();
  const queryClient = useQueryClient();
  
  // WebSocket state
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected' | 'reconnecting'>('disconnected');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [isConnected, setIsConnected] = useState(false);

  // Update dashboard context when connection state changes
  useEffect(() => {
    setConnectionState({
      status: connectionStatus,
      reconnectAttempts,
      isConnected,
    });
  }, [connectionStatus, reconnectAttempts, isConnected, setConnectionState]);

  // WebSocket message handler
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'booking_update':
          // Invalidate booking queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
          playNotificationSound('booking');
          break;
          
        case 'property_update':
          // Invalidate property queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['/api/properties'] });
          playNotificationSound('info');
          break;
          
        case 'notification':
          // Handle general notifications
          playNotificationSound('info');
          break;
          
        case 'heartbeat':
          // WebSocket heartbeat - no action needed
          break;
          
        default:
          console.log('Unknown WebSocket message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }, [queryClient, playNotificationSound]);

  // WebSocket connection logic
  const connect = useCallback(() => {
    if (!user?.id) {
      console.log('Cannot connect WebSocket: No user ID');
      return;
    }

    // Close existing connection if present
    if (wsRef.current && wsRef.current.readyState !== WEBSOCKET.CLOSED) {
      wsRef.current.close(WEBSOCKET.NORMAL_CLOSURE, 'Reconnecting');
    }

    try {
      setConnectionStatus('connecting');
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/ws/owner/${user.id}`;
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        setConnectionStatus('connected');
        setIsConnected(true);
        setReconnectAttempts(0);
        
        // Clear any existing polling since WebSocket is working
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        
        // Start heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
        }
        
        heartbeatIntervalRef.current = setInterval(() => {
          if (wsRef.current?.readyState === WEBSOCKET.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'heartbeat',
              timestamp: Date.now()
            }));
          }
        }, TIMEOUTS.WEBSOCKET_HEARTBEAT);
      };
      
      wsRef.current.onmessage = handleMessage;
      
      wsRef.current.onclose = (event) => {
        console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        
        // Clear the reference to prevent memory leaks
        wsRef.current = null;
        setIsConnected(false);
        
        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }
        
        // Only attempt reconnection if not manually closed
        if (event.code !== WEBSOCKET.NORMAL_CLOSURE && reconnectAttempts < WEBSOCKET.MAX_RECONNECT_ATTEMPTS) {
          setConnectionStatus('reconnecting');
          setReconnectAttempts(prev => prev + 1);
          
          const delay = Math.min(
            WEBSOCKET.INITIAL_RECONNECT_DELAY * Math.pow(2, reconnectAttempts),
            WEBSOCKET.MAX_RECONNECT_DELAY
          );
          
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`🔄 Attempting WebSocket reconnection (${reconnectAttempts + 1}/${WEBSOCKET.MAX_RECONNECT_ATTEMPTS})`);
            connect();
          }, delay);
        } else {
          console.log('🚫 WebSocket connection failed permanently, falling back to polling');
          setConnectionStatus('disconnected');
          startPolling();
        }
      };
      
      wsRef.current.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionStatus('disconnected');
      };
      
    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      setConnectionStatus('disconnected');
      startPolling();
    }
  }, [user?.id, handleMessage, reconnectAttempts]);

  // Polling fallback when WebSocket fails
  const startPolling = useCallback(() => {
    console.log('📡 Starting polling fallback...');
    
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
    
    pollingIntervalRef.current = setInterval(() => {
      // Invalidate queries to trigger refetch
      queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
      queryClient.invalidateQueries({ queryKey: ['/api/properties'] });
    }, INTERVALS.POLLING_DEFAULT);
  }, [queryClient]);

  // Send message through WebSocket
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WEBSOCKET.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message:', message);
    }
  }, []);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close(WEBSOCKET.NORMAL_CLOSURE, 'Manual disconnect');
      wsRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    
    setConnectionStatus('disconnected');
    setIsConnected(false);
    setReconnectAttempts(0);
  }, []);

  // Auto-connect when user is available
  useEffect(() => {
    if (user?.id) {
      connect();
    } else {
      disconnect();
    }
    
    return () => {
      disconnect();
    };
  }, [user?.id, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Cleaning up WebSocket connections...');
      disconnect();
    };
  }, [disconnect]);

  const contextValue: WebSocketContextType = {
    isConnected,
    connectionStatus,
    reconnectAttempts,
    sendMessage,
    connect,
    disconnect,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

// Export types
export type { WebSocketMessage, WebSocketContextType };