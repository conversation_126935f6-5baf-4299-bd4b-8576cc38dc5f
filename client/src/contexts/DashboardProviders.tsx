import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DashboardProvider } from './DashboardContext';
import { WebSocketProvider } from './WebSocketContext';
import { PropertyProvider } from './PropertyContext';

// Create a query client instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 401/403 errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false, // Don't retry mutations by default
    },
  },
});

/**
 * Comprehensive Dashboard Context Providers
 * 
 * This component provides a clean way to wrap the entire dashboard
 * with all necessary contexts in the correct order.
 * 
 * Context Hierarchy:
 * 1. QueryClientProvider - React Query for data fetching
 * 2. DashboardProvider - Core dashboard state and utilities
 * 3. WebSocketProvider - Real-time updates
 * 4. PropertyProvider - Property-specific state
 * 
 * Benefits:
 * ✅ Single provider component to wrap dashboard
 * ✅ Correct context hierarchy order
 * ✅ Centralized query client configuration
 * ✅ Easy to add/remove contexts
 */

interface DashboardProvidersProps {
  children: React.ReactNode;
}

export const DashboardProviders: React.FC<DashboardProvidersProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <DashboardProvider>
        <WebSocketProvider>
          <PropertyProvider>
            {children}
          </PropertyProvider>
        </WebSocketProvider>
      </DashboardProvider>
    </QueryClientProvider>
  );
};

// Export query client for use in tests or other contexts
export { queryClient };

/**
 * 🎯 Usage Example:
 * 
 * ```typescript
 * // In your main dashboard component or app root:
 * import { DashboardProviders } from '@/contexts/DashboardProviders';
 * import { OwnerDashboard } from '@/pages/OwnerDashboard';
 * 
 * function App() {
 *   return (
 *     <DashboardProviders>
 *       <OwnerDashboard />
 *     </DashboardProviders>
 *   );
 * }
 * ```
 * 
 * 🔧 Provider Order Explained:
 * 
 * 1. **QueryClientProvider** - Must be outermost as all other contexts use React Query
 * 2. **DashboardProvider** - Core dashboard state, used by WebSocket and Property providers
 * 3. **WebSocketProvider** - Depends on DashboardProvider for user info and notifications
 * 4. **PropertyProvider** - Depends on DashboardProvider for auth and toast functions
 * 
 * This order ensures each context has access to the dependencies it needs.
 */