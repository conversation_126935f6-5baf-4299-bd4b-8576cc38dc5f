import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { apiRequest } from "@/lib/queryClient";

interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role: string;
  phone?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: boolean;
  login: (user: User, token: string) => void;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  token: null,
  loading: true,
  login: () => {},
  logout: () => {},
  isAuthenticated: false,
});

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state with fallback support
  useEffect(() => {
    let isMounted = true;
    
    const initAuth = async () => {
      try {
        // First try HTTP-only cookie authentication
        const response = await Promise.race([
          fetch("/api/auth/me", {
            credentials: 'include' // Ensure cookies are sent
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Request timeout')), 5000)
          )
        ]) as Response;
        
        if (!isMounted) return;
        
        if (response.ok) {
          const result = await response.json();
          const userData = result.data || result;
          if (!isMounted) return;
          
          setUser(userData);
          
          // Check if we have a stored token to maintain compatibility
          const storedToken = localStorage.getItem('token');
          setToken(storedToken || 'authenticated');
        } else {
          // Cookie auth failed, try localStorage token as fallback
          const storedToken = localStorage.getItem('token');
          if (storedToken) {
            try {
              const tokenResponse = await fetch("/api/auth/me", {
                headers: { Authorization: `Bearer ${storedToken}` },
                credentials: 'include'
              });
              
              if (!isMounted) return;
              
              if (tokenResponse.ok) {
                const result = await tokenResponse.json();
                const userData = result.data || result;
                if (!isMounted) return;
                
                setUser(userData);
                setToken(storedToken);
              } else {
                // Token is invalid, clear it
                localStorage.removeItem('token');
                if (!isMounted) return;
                
                setUser(null);
                setToken(null);
              }
            } catch (tokenError) {
              console.error("Token auth error:", tokenError);
              localStorage.removeItem('token');
              if (!isMounted) return;
              
              setUser(null);
              setToken(null);
            }
          } else {
            // No stored token, user is not authenticated
            if (!isMounted) return;
            
            setUser(null);
            setToken(null);
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        // On network error, check if we have a stored token
        const storedToken = localStorage.getItem('token');
        if (storedToken) {
          localStorage.removeItem('token');
        }
        if (!isMounted) return;
        
        setUser(null);
        setToken(null);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    
    initAuth();
    
    return () => {
      isMounted = false;
    };
  }, []);

  const login = (userData: User, authToken: string) => {
    setUser(userData);
    setToken(authToken);
    // Store token in localStorage for persistence and test compatibility
    localStorage.setItem('token', authToken);
    setLoading(false);
  };

  const logout = async () => {
    try {
      // Call the server-side logout endpoint to invalidate the token and clear cookies
      await apiRequest("POST", "/api/auth/logout");
    } catch (error) {
      console.error("Logout API error:", error);
      // Continue with client-side logout even if the API call fails
    }
    
    // Clear local state and localStorage
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
  };

  const value = {
    user,
    token,
    loading,
    login,
    logout,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => useContext(AuthContext);
