import React, { createContext, useContext, useCallback, useState, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import {
  AUDIO_FREQUENCIES,
  AUDIO_DURATIONS,
  AUDIO_VOLUMES,
  NOTIFICATION_SOUND_DELAYS,
} from '../../../shared/constants';

// Types for dashboard state
export interface DashboardUser {
  id: number;
  fullName: string;
  email: string;
  role: string;
}

export interface ConnectionState {
  status: 'connected' | 'connecting' | 'disconnected' | 'reconnecting';
  reconnectAttempts: number;
  isConnected: boolean;
}

export interface DashboardState {
  selectedTab: string;
  connectionState: ConnectionState;
  soundEnabled: boolean;
}

// Context types
interface DashboardContextType {
  // User & Auth
  user: DashboardUser | null;
  getAuthHeaders: () => HeadersInit;
  handleAuthError: (error: any, action: string) => void;
  
  // Dashboard State
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  
  // Connection State
  connectionState: ConnectionState;
  setConnectionState: React.Dispatch<React.SetStateAction<ConnectionState>>;
  
  // Utility Functions
  formatPrice: (price: number) => string;
  getStatusColor: (status: string) => string;
  playNotificationSound: (type: string) => void;
  
  // Booking Management
  updateBookingOptimistically: (bookingId: number, status: string, callback: () => void) => void;
  
  // Toast Management
  showSuccessToast: (title: string, description: string) => void;
  showErrorToast: (title: string, description: string) => void;
  showInfoToast: (title: string, description: string) => void;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

// Custom hook to use dashboard context
export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};

// Dashboard Provider Component
interface DashboardProviderProps {
  children: React.ReactNode;
}

export const DashboardProvider: React.FC<DashboardProviderProps> = ({ children }) => {
  const { user: authUser, token } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Dashboard state
  const [selectedTab, setSelectedTab] = useState('properties');
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isConnected: false,
  });
  
  // Audio context and sound management
  const audioContextRef = useRef<AudioContext | null>(null);
  const soundTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Transform auth user to dashboard user
  const user: DashboardUser | null = authUser ? {
    id: authUser.id,
    fullName: authUser.fullName || authUser.username || 'User',
    email: authUser.email,
    role: authUser.role,
  } : null;

  // Authentication headers
  const getAuthHeaders = useCallback((): HeadersInit => {
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }, [token]);

  // Handle authentication errors
  const handleAuthError = useCallback((error: any, action: string) => {
    console.error('Authentication error:', error);
    
    if (error.message?.includes('unauthorized') || error.message?.includes('not authorized')) {
      toast({
        title: '🔒 Authorization Required',
        description: `You are not authorized to ${action}. Please check your permissions.`,
        variant: 'destructive',
      });
    } else if (error.message?.includes('Authentication required')) {
      toast({
        title: '🔐 Login Required',
        description: 'Please log in to continue.',
        variant: 'destructive',
      });
    } else {
      toast({
        title: '⚠️ Error',
        description: `Failed to ${action}. Please try again.`,
        variant: 'destructive',
      });
    }
  }, [toast]);

  // Utility function for price formatting
  const formatPrice = useCallback((price: number): string => {
    return `₹${price.toLocaleString('en-IN')}`;
  }, []);

  // Utility function for status colors
  const getStatusColor = useCallback((status: string): string => {
    switch (status) {
      case 'confirmed': 
        return 'bg-green-100 text-green-800 border-green-300';
      case 'pending': 
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'rejected': 
        return 'bg-red-100 text-red-800 border-red-300';
      case 'cancelled': 
        return 'bg-gray-100 text-gray-800 border-gray-300';
      default: 
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  }, []);

  // Sound notification system
  const initializeAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      try {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    }
    
    if (audioContextRef.current?.state === 'suspended') {
      audioContextRef.current?.resume();
    }
  }, []);

  const playTone = useCallback((frequency: number, duration: number, volume: number = AUDIO_VOLUMES.MEDIUM) => {
    initializeAudioContext();
    const audioContext = audioContextRef.current;
    
    if (!audioContext) return;

    try {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration);
    } catch (error) {
      console.error('Error playing tone:', error);
    }
  }, [initializeAudioContext]);

  const playNotificationSound = useCallback((type: string) => {
    initializeAudioContext();
    
    const scheduleSound = (frequency: number, duration: number, delay: number) => {
      const timeout = setTimeout(() => {
        playTone(frequency, duration);
      }, delay);
      
      soundTimeoutsRef.current?.add(timeout);
      
      // Auto-cleanup after completion
      setTimeout(() => {
        soundTimeoutsRef.current?.delete(timeout);
      }, delay + duration * 1000);
      
      return timeout;
    };

    switch (type) {
      case 'success':
        scheduleSound(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.SHORT, 0);
        scheduleSound(AUDIO_FREQUENCIES.G5, AUDIO_DURATIONS.SHORT, NOTIFICATION_SOUND_DELAYS.SUCCESS_E5);
        setTimeout(() => {
          // Cleanup timeouts after sequence
        }, NOTIFICATION_SOUND_DELAYS.SUCCESS_CLEANUP);
        break;
        
      case 'error':
        scheduleSound(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.MEDIUM, 0);
        setTimeout(() => {
          // Cleanup timeouts after sequence
        }, NOTIFICATION_SOUND_DELAYS.ERROR_CLEANUP);
        break;
        
      case 'warning':
        scheduleSound(AUDIO_FREQUENCIES.C5, AUDIO_DURATIONS.MEDIUM, 0);
        setTimeout(() => {
          // Cleanup timeouts after sequence
        }, NOTIFICATION_SOUND_DELAYS.WARNING_CLEANUP);
        break;
        
      case 'info':
        scheduleSound(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.SHORT, 0);
        setTimeout(() => {
          // Cleanup timeouts after sequence
        }, NOTIFICATION_SOUND_DELAYS.INFO_CLEANUP);
        break;
        
      case 'booking':
        scheduleSound(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.SHORT, 0);
        scheduleSound(AUDIO_FREQUENCIES.G5, AUDIO_DURATIONS.SHORT, NOTIFICATION_SOUND_DELAYS.BOOKING_E5);
        scheduleSound(AUDIO_FREQUENCIES.C6, AUDIO_DURATIONS.SHORT, NOTIFICATION_SOUND_DELAYS.BOOKING_G5);
        setTimeout(() => {
          // Cleanup timeouts after sequence
        }, NOTIFICATION_SOUND_DELAYS.BOOKING_CLEANUP);
        break;
    }
  }, [initializeAudioContext, playTone]);

  // Optimistic booking updates
  const updateBookingOptimistically = useCallback((
    bookingId: number, 
    status: string, 
    callback: () => void
  ) => {
    // Update the query cache optimistically
    queryClient.setQueryData(['/api/bookings/owner'], (oldData: any) => {
      if (!oldData?.bookings) return oldData;
      
      return {
        ...oldData,
        bookings: oldData.bookings.map((booking: any) =>
          booking.id === bookingId ? { ...booking, status } : booking
        ),
      };
    });

    // Play success sound
    playNotificationSound('booking');
    
    // Execute callback
    callback();
  }, [queryClient, playNotificationSound]);

  // Toast utilities
  const showSuccessToast = useCallback((title: string, description: string) => {
    toast({
      title,
      description,
    });
    playNotificationSound('success');
  }, [toast, playNotificationSound]);

  const showErrorToast = useCallback((title: string, description: string) => {
    toast({
      title,
      description,
      variant: 'destructive',
    });
    playNotificationSound('error');
  }, [toast, playNotificationSound]);

  const showInfoToast = useCallback((title: string, description: string) => {
    toast({
      title,
      description,
    });
    playNotificationSound('info');
  }, [toast, playNotificationSound]);

  // Cleanup sound timeouts on unmount
  React.useEffect(() => {
    return () => {
      soundTimeoutsRef.current?.forEach(timeout => clearTimeout(timeout));
      soundTimeoutsRef.current?.clear();
      
      if (audioContextRef.current) {
        audioContextRef.current?.close();
      }
    };
  }, []);

  const contextValue: DashboardContextType = {
    // User & Auth
    user,
    getAuthHeaders,
    handleAuthError,
    
    // Dashboard State
    selectedTab,
    setSelectedTab,
    
    // Connection State
    connectionState,
    setConnectionState,
    
    // Utility Functions
    formatPrice,
    getStatusColor,
    playNotificationSound,
    
    // Booking Management
    updateBookingOptimistically,
    
    // Toast Management
    showSuccessToast,
    showErrorToast,
    showInfoToast,
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
};

// Types are exported above with their definitions