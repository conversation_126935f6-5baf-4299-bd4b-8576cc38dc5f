import React, { createContext, useContext, useState, ReactNode } from 'react';

interface FavoritesContextType {
  favorites: number[];
  addToFavorites: (propertyId: number) => void;
  removeFromFavorites: (propertyId: number) => void;
  isFavorite: (propertyId: number) => boolean;
  toggleFavorite: (propertyId: number) => void;
}

const FavoritesContext = createContext<FavoritesContextType>({
  favorites: [],
  addToFavorites: () => {},
  removeFromFavorites: () => {},
  isFavorite: () => false,
  toggleFavorite: () => {},
});

interface FavoritesProviderProps {
  children: ReactNode;
}

export function FavoritesProvider({ children }: FavoritesProviderProps) {
  const [favorites, setFavorites] = useState<number[]>(() => {
    // Load favorites from localStorage with error handling
    try {
      const saved = localStorage.getItem('favorites');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.warn('Failed to load favorites from localStorage:', error);
      return [];
    }
  });

  const updateLocalStorage = (newFavorites: number[]) => {
    try {
      localStorage.setItem('favorites', JSON.stringify(newFavorites));
    } catch (error) {
      console.warn('Failed to save favorites to localStorage:', error);
    }
  };

  const addToFavorites = (propertyId: number) => {
    const newFavorites = [...favorites, propertyId];
    setFavorites(newFavorites);
    updateLocalStorage(newFavorites);
  };

  const removeFromFavorites = (propertyId: number) => {
    // Remove only the first occurrence of the property ID
    const index = favorites.indexOf(propertyId);
    if (index > -1) {
      const newFavorites = [...favorites];
      newFavorites.splice(index, 1);
      setFavorites(newFavorites);
      updateLocalStorage(newFavorites);
    }
  };

  const isFavorite = (propertyId: number) => {
    return favorites.includes(propertyId);
  };

  const toggleFavorite = (propertyId: number) => {
    if (isFavorite(propertyId)) {
      removeFromFavorites(propertyId);
    } else {
      addToFavorites(propertyId);
    }
  };

  return (
    <FavoritesContext.Provider value={{
      favorites,
      addToFavorites,
      removeFromFavorites,
      isFavorite,
      toggleFavorite,
    }}>
      {children}
    </FavoritesContext.Provider>
  );
}

export const useFavorites = () => useContext(FavoritesContext);