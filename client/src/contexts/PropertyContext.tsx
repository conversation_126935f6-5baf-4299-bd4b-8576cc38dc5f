import React, { createContext, useContext, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useDashboard } from './DashboardContext';
import { Property } from '../../../shared/schema-camelcase';

// Property-related types
export interface PropertyStats {
  totalProperties: number;
  activeProperties: number;
  totalBookings: number;
  totalRevenue: number;
  averageRating: number;
}

export interface PropertyFilters {
  status?: 'active' | 'inactive' | 'all';
  location?: string;
  priceRange?: {
    min: number;
    max: number;
  };
}

// Property context type
interface PropertyContextType {
  // Property Data
  properties: Property[];
  propertiesLoading: boolean;
  propertiesError: Error | null;
  
  // Property Stats
  stats: PropertyStats | null;
  statsLoading: boolean;
  
  // Property Actions
  createProperty: (propertyData: Partial<Property>) => Promise<Property>;
  updateProperty: (propertyId: number, updates: Partial<Property>) => Promise<Property>;
  deleteProperty: (propertyId: number) => Promise<void>;
  
  // Property Utilities
  getProperty: (propertyId: number) => Property | undefined;
  getPropertiesByStatus: (status: string) => Property[];
  
  // Filters
  filters: PropertyFilters;
  setFilters: React.Dispatch<React.SetStateAction<PropertyFilters>>;
  
  // Pagination
  currentPage: number;
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
  totalPages: number;
  
  // Refresh
  refetchProperties: () => void;
}

const PropertyContext = createContext<PropertyContextType | undefined>(undefined);

// Custom hook to use property context
export const useProperty = () => {
  const context = useContext(PropertyContext);
  if (context === undefined) {
    throw new Error('useProperty must be used within a PropertyProvider');
  }
  return context;
};

// Property Provider Component
interface PropertyProviderProps {
  children: React.ReactNode;
}

export const PropertyProvider: React.FC<PropertyProviderProps> = ({ children }) => {
  const { 
    user, 
    getAuthHeaders, 
    handleAuthError, 
    showSuccessToast, 
    showErrorToast 
  } = useDashboard();
  const queryClient = useQueryClient();
  
  // Local state
  const [filters, setFilters] = React.useState<PropertyFilters>({
    status: 'all',
  });
  const [currentPage, setCurrentPage] = React.useState(1);
  
  const propertiesPerPage = 10;

  // Properties query
  const { 
    data: propertiesData, 
    isLoading: propertiesLoading, 
    error: propertiesError,
    refetch: refetchProperties 
  } = useQuery({
    queryKey: ['/api/properties', currentPage, filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: propertiesPerPage.toString(),
        ...(filters.status !== 'all' && { status: filters.status }),
        ...(filters.location && { location: filters.location }),
        ...(filters.priceRange && {
          minPrice: filters.priceRange.min.toString(),
          maxPrice: filters.priceRange.max.toString(),
        }),
      });
      
      const response = await fetch(`/api/properties?${params}`, {
        credentials: 'include',
        headers: getAuthHeaders(),
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to view properties');
        }
        throw new Error('Failed to fetch properties');
      }
      
      return response.json();
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Property stats query
  const { 
    data: stats, 
    isLoading: statsLoading 
  } = useQuery({
    queryKey: ['/api/properties/stats'],
    queryFn: async () => {
      const response = await fetch('/api/properties/stats', {
        credentials: 'include',
        headers: getAuthHeaders(),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch property stats');
      }
      
      return response.json();
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });

  // Create property mutation
  const createPropertyMutation = useMutation({
    mutationFn: async (propertyData: Partial<Property>) => {
      const response = await fetch('/api/properties', {
        method: 'POST',
        headers: getAuthHeaders(),
        credentials: 'include',
        body: JSON.stringify(propertyData),
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to create properties');
        }
        throw new Error('Failed to create property');
      }
      
      return response.json();
    },
    onSuccess: (newProperty) => {
      // Update the cache
      queryClient.setQueryData(['/api/properties', currentPage, filters], (oldData: any) => {
        if (!oldData) return { properties: [newProperty], total: 1 };
        return {
          ...oldData,
          properties: [newProperty, ...oldData.properties],
          total: oldData.total + 1,
        };
      });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: ['/api/properties/stats'] });
      
      showSuccessToast(
        '✅ Property Created', 
        `Property "${newProperty.title}" has been created successfully.`
      );
    },
    onError: (error: any) => {
      if (error.message?.includes('authorized')) {
        handleAuthError(error, 'create properties');
      } else {
        showErrorToast('❌ Creation Failed', 'Failed to create property. Please try again.');
      }
    },
  });

  // Update property mutation
  const updatePropertyMutation = useMutation({
    mutationFn: async ({ propertyId, updates }: { propertyId: number; updates: Partial<Property> }) => {
      const response = await fetch(`/api/properties/${propertyId}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        credentials: 'include',
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to update this property');
        }
        throw new Error('Failed to update property');
      }
      
      return response.json();
    },
    onSuccess: (updatedProperty) => {
      // Update the cache
      queryClient.setQueryData(['/api/properties', currentPage, filters], (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          properties: oldData.properties.map((property: Property) =>
            property.id === updatedProperty.id ? updatedProperty : property
          ),
        };
      });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: ['/api/properties/stats'] });
      
      showSuccessToast(
        '✅ Property Updated', 
        `Property "${updatedProperty.title}" has been updated successfully.`
      );
    },
    onError: (error: any) => {
      if (error.message?.includes('authorized')) {
        handleAuthError(error, 'update this property');
      } else {
        showErrorToast('❌ Update Failed', 'Failed to update property. Please try again.');
      }
    },
  });

  // Delete property mutation
  const deletePropertyMutation = useMutation({
    mutationFn: async (propertyId: number) => {
      const response = await fetch(`/api/properties/${propertyId}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
        credentials: 'include',
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to delete this property');
        }
        throw new Error('Failed to delete property');
      }
      
      return response.json();
    },
    onSuccess: (_, propertyId) => {
      // Update the cache
      queryClient.setQueryData(['/api/properties', currentPage, filters], (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          properties: oldData.properties.filter((property: Property) => property.id !== propertyId),
          total: oldData.total - 1,
        };
      });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: ['/api/properties/stats'] });
      
      showSuccessToast('✅ Property Deleted', 'Property has been deleted successfully.');
    },
    onError: (error: any) => {
      if (error.message?.includes('authorized')) {
        handleAuthError(error, 'delete this property');
      } else {
        showErrorToast('❌ Deletion Failed', 'Failed to delete property. Please try again.');
      }
    },
  });

  // Property utility functions
  const getProperty = useCallback((propertyId: number): Property | undefined => {
    return propertiesData?.properties?.find((property: Property) => property.id === propertyId);
  }, [propertiesData?.properties]);

  const getPropertiesByStatus = useCallback((status: string): Property[] => {
    if (!propertiesData?.properties) return [];
    if (status === 'all') return propertiesData.properties;
    return propertiesData.properties.filter((property: Property) => property.status === status);
  }, [propertiesData?.properties]);

  // Wrapper functions for mutations
  const createProperty = useCallback(async (propertyData: Partial<Property>): Promise<Property> => {
    return createPropertyMutation.mutateAsync(propertyData);
  }, [createPropertyMutation]);

  const updateProperty = useCallback(async (propertyId: number, updates: Partial<Property>): Promise<Property> => {
    return updatePropertyMutation.mutateAsync({ propertyId, updates });
  }, [updatePropertyMutation]);

  const deleteProperty = useCallback(async (propertyId: number): Promise<void> => {
    return deletePropertyMutation.mutateAsync(propertyId);
  }, [deletePropertyMutation]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    if (!propertiesData?.total) return 1;
    return Math.ceil(propertiesData.total / propertiesPerPage);
  }, [propertiesData?.total]);

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const contextValue: PropertyContextType = {
    // Property Data
    properties: propertiesData?.properties || [],
    propertiesLoading,
    propertiesError: propertiesError as Error | null,
    
    // Property Stats
    stats: stats?.data || null,
    statsLoading,
    
    // Property Actions
    createProperty,
    updateProperty,
    deleteProperty,
    
    // Property Utilities
    getProperty,
    getPropertiesByStatus,
    
    // Filters
    filters,
    setFilters,
    
    // Pagination
    currentPage,
    setCurrentPage,
    totalPages,
    
    // Refresh
    refetchProperties,
  };

  return (
    <PropertyContext.Provider value={contextValue}>
      {children}
    </PropertyContext.Provider>
  );
};

// Types are already exported with their definitions