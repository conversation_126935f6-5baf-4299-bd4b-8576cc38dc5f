
import { useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Property } from "@shared/schema";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import PropertyForm from "@/components/PropertyForm";
import { ImageUpload } from "@/components/ImageUpload";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X, Image as ImageIcon, Video } from "lucide-react";

export default function EditProperty() {
  const params = useParams<{ id: string }>();
  const id = params.id;
  const { user } = useAuth();
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("details");

  // Fetch property details
  const { data: property, isLoading, error } = useQuery<Property>({
    queryKey: [`/api/properties/${id}`],
    queryFn: async () => {
      const response = await fetch(`/api/properties/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Property not found");
        }
        throw new Error("Failed to fetch property");
      }
      return response.json();
    },
    enabled: !!user && user.role === 'owner',
  });

  // Media upload mutation
  const uploadMediaMutation = useMutation({
    mutationFn: async ({ files, mediaType }: { files: File[], mediaType: 'photos' | 'videos' }) => {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('media', file);
      });
      formData.append('propertyId', id);
      formData.append('mediaType', mediaType);

      const response = await fetch('/api/properties/media/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload media');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/properties/${id}`] });
      toast({
        title: "Success",
        description: "Media uploaded successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Media delete mutation
  const deleteMediaMutation = useMutation({
    mutationFn: async ({ imageUrl }: { imageUrl: string }) => {
      const response = await fetch('/api/properties/media/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({ propertyId: parseInt(id), imageUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete media');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/properties/${id}`] });
      toast({
        title: "Success",
        description: "Media deleted successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Redirect if not logged in or not an owner
  useEffect(() => {
    if (!user) {
      navigate(`/login?redirect=/owner/property/edit/${id}`);
    } else if (user.role !== 'owner') {
      toast({
        title: "Access Denied",
        description: "Only property owners can edit listings",
        variant: "destructive",
      });
      navigate('/');
    }
  }, [user, navigate, toast, id]);

  // Check if user is the owner of this property
  useEffect(() => {
    if (property && user && Number(property.ownerId) !== Number(user.id)) {
      toast({
        title: "Access Denied",
        description: "You can only edit your own properties",
        variant: "destructive",
      });
      navigate('/owner/dashboard');
    }
  }, [property, user, navigate, toast]);

  const handleMediaUpload = async (files: File[], mediaType: 'photos' | 'videos') => {
    if (files.length === 0) return;
    
    uploadMediaMutation.mutate({ files, mediaType });
  };

  const handleMediaDelete = async (imageUrl: string) => {
    if (!window.confirm("Are you sure you want to delete this media?")) {
      return;
    }
    
    deleteMediaMutation.mutate({ imageUrl });
  };

  const getPhotos = () => {
    if (!property?.images) return [];
    return property.images.filter(url => 
      /\.(jpg|jpeg|png|gif|webp)$/i.test(url)
    );
  };

  const getVideos = () => {
    if (!property?.images) return [];
    return property.images.filter(url => 
      /\.(mp4|webm|ogg|mov|avi)$/i.test(url)
    );
  };

  if (!user || user.role !== 'owner') {
    return null;
  }

  if (isLoading) {
    return (
      <div className="py-12 bg-[#F7F4F1]">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Skeleton className="h-12 w-1/2 mb-8" />
            <div className="space-y-6">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-10 w-full" />
              <div className="grid grid-cols-2 gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-40 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="py-12 bg-[#F7F4F1]">
        <div className="container mx-auto px-4 text-center">
          <h1 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-4">Error</h1>
          <p className="text-[#605045]">Could not load property data. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="py-12 bg-[#F7F4F1]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-8">Edit Property</h1>
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">Property Details</TabsTrigger>
              <TabsTrigger value="photos">Photos</TabsTrigger>
              <TabsTrigger value="videos">Videos</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              <PropertyForm mode="edit" initialData={property} />
            </TabsContent>

            <TabsContent value="photos" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="h-5 w-5" />
                    Property Photos
                  </CardTitle>
                  <CardDescription>
                    Manage photos for your property. Upload high-quality images to showcase your property.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Photo Upload Section */}
                  <div className="border-2 border-dashed border-[#7FA650] rounded-lg p-6">
                    <ImageUpload
                      images={getPhotos()}
                      onImagesChange={(urls) => {
                        // This will be handled by the upload mutation
                        // The ImageUpload component will handle the upload via its own API calls
                      }}
                      maxImages={10}
                      className="w-full"
                    />
                  </div>

                  {/* Current Photos Grid */}
                  {getPhotos().length > 0 && (
                    <div>
                      <h3 className="font-medium text-[#2D3C2D] mb-4">Current Photos ({getPhotos().length})</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {getPhotos().map((photo, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                              <img
                                src={photo}
                                alt={`Property photo ${index + 1}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDNIMTNMMTEgMUg1QzMuODkgMSAzIDEuODkgMyAzVjE5QzMgMjAuMTEgMy44OSAyMSA1IDIxSDE5QzIwLjExIDIxIDIxIDIwLjExIDIxIDE5VjNaTTEyIDdMMTMuNSA5SDEwLjVMMTIgN1oiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                                }}
                              />
                            </div>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => handleMediaDelete(photo)}
                              disabled={deleteMediaMutation.isPending}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="videos" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Video className="h-5 w-5" />
                    Property Videos
                  </CardTitle>
                  <CardDescription>
                    Manage videos for your property. Upload engaging videos to give potential guests a virtual tour.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Video Upload Section */}
                  <div className="border-2 border-dashed border-[#7FA650] rounded-lg p-6">
                    <ImageUpload
                      images={getVideos()}
                      onImagesChange={(urls) => {
                        // This will be handled by the upload mutation
                        // The ImageUpload component will handle the upload via its own API calls
                      }}
                      maxImages={5}
                      className="w-full"
                    />
                  </div>

                  {/* Current Videos Grid */}
                  {getVideos().length > 0 && (
                    <div>
                      <h3 className="font-medium text-[#2D3C2D] mb-4">Current Videos ({getVideos().length})</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {getVideos().map((video, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                              <video
                                src={video}
                                className="w-full h-full object-cover"
                                controls
                                preload="metadata"
                              >
                                Your browser does not support the video tag.
                              </video>
                            </div>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => handleMediaDelete(video)}
                              disabled={deleteMediaMutation.isPending}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {getVideos().length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Video className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No videos uploaded yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
