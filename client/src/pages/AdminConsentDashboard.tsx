import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { apiRequest } from "@/lib/queryClient";
import PageTransition from "@/components/PageTransition";

interface UserConsent {
  id: number;
  username: string;
  email: string;
  termsAccepted: boolean;
  privacyPolicyAccepted: boolean;
  cookiePolicyAccepted: boolean;
  dataProcessingConsent: boolean;
  marketingConsent: boolean;
  consentTimestamp: string | null;
}

export default function AdminConsentDashboard() {
  const { user } = useAuth();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredUsers, setFilteredUsers] = useState<UserConsent[]>([]);

  // Redirect if not admin
  useEffect(() => {
    if (!user || user.role !== "owner") {
      toast({
        title: "Access Denied",
        description: "You need administrator privileges to access this page.",
        variant: "destructive",
      });
      navigate("/");
    }
  }, [user, navigate, toast]);

  // Fetch all users with their consent information
  const { data: users, isLoading, isError, refetch } = useQuery({
    queryKey: ["/api/admin/users/consent"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/admin/users/consent");
      const data = await response.json();
      return data as UserConsent[];
    },
    enabled: !!user && user.role === "owner",
  });

  // Filter users based on search term
  useEffect(() => {
    if (!users) return;
    
    if (!searchTerm) {
      setFilteredUsers(users);
      return;
    }

    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const filtered = users.filter(
      (user) =>
        user.username.toLowerCase().includes(lowerCaseSearchTerm) ||
        user.email.toLowerCase().includes(lowerCaseSearchTerm)
    );
    setFilteredUsers(filtered);
  }, [users, searchTerm]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not recorded";
    return new Date(dateString).toLocaleString();
  };

  if (!user || user.role !== "owner") {
    return null;
  }

  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-10">
        <Card className="border border-[#D5CCC4] shadow-sm">
          <CardHeader className="bg-[#4A6741] text-white">
            <CardTitle className="text-xl font-semibold">User Consent Dashboard</CardTitle>
            <CardDescription className="text-[#E5DDD3]">
              Monitor and manage user consent for legal compliance
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <div className="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder="Search by username or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border-[#D5CCC4]"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    refetch();
                  }}
                  className="border-[#4A6741] text-[#4A6741]"
                >
                  Refresh Data
                </Button>
                <Button
                  onClick={() => {
                    // Export CSV functionality
                    const csvContent = "data:text/csv;charset=utf-8," +
                      "ID,Username,Email,Terms Accepted,Privacy Policy Accepted,Cookie Policy Accepted,Data Processing Consent,Marketing Consent,Consent Timestamp\n" +
                      filteredUsers.map(user => 
                        `${user.id},"${user.username}","${user.email}",${user.termsAccepted},${user.privacyPolicyAccepted},${user.cookiePolicyAccepted},${user.dataProcessingConsent},${user.marketingConsent},"${user.consentTimestamp || ''}"`
                      ).join("\n");
                    
                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute("download", "user_consent_data.csv");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    toast({
                      title: "Export Successful",
                      description: "User consent data has been exported to CSV.",
                    });
                  }}
                  className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
                >
                  Export to CSV
                </Button>
              </div>
            </div>

            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="all">All Users</TabsTrigger>
                <TabsTrigger value="complete">Complete Consent</TabsTrigger>
                <TabsTrigger value="partial">Partial Consent</TabsTrigger>
                <TabsTrigger value="none">No Consent</TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-0">
                <UserConsentTable
                  users={filteredUsers}
                  isLoading={isLoading}
                  isError={isError}
                />
              </TabsContent>

              <TabsContent value="complete" className="mt-0">
                <UserConsentTable
                  users={filteredUsers.filter(
                    (user) =>
                      user.termsAccepted &&
                      user.privacyPolicyAccepted &&
                      user.cookiePolicyAccepted
                  )}
                  isLoading={isLoading}
                  isError={isError}
                />
              </TabsContent>

              <TabsContent value="partial" className="mt-0">
                <UserConsentTable
                  users={filteredUsers.filter(
                    (user) =>
                      (user.termsAccepted ||
                        user.privacyPolicyAccepted ||
                        user.cookiePolicyAccepted ||
                        user.dataProcessingConsent ||
                        user.marketingConsent) &&
                      !(
                        user.termsAccepted &&
                        user.privacyPolicyAccepted &&
                        user.cookiePolicyAccepted
                      )
                  )}
                  isLoading={isLoading}
                  isError={isError}
                />
              </TabsContent>

              <TabsContent value="none" className="mt-0">
                <UserConsentTable
                  users={filteredUsers.filter(
                    (user) =>
                      !user.termsAccepted &&
                      !user.privacyPolicyAccepted &&
                      !user.cookiePolicyAccepted &&
                      !user.dataProcessingConsent &&
                      !user.marketingConsent
                  )}
                  isLoading={isLoading}
                  isError={isError}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </PageTransition>
  );
}

function UserConsentTable({
  users,
  isLoading,
  isError,
}: {
  users: UserConsent[] | undefined;
  isLoading: boolean;
  isError: boolean;
}) {
  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not recorded";
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return <div className="text-center py-8">Loading user consent data...</div>;
  }

  if (isError) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading user consent data. Please try again.
      </div>
    );
  }

  if (!users || users.length === 0) {
    return <div className="text-center py-8">No users found.</div>;
  }

  return (
    <div className="rounded-md border border-[#D5CCC4]">
      <Table>
        <TableCaption>List of users and their consent status.</TableCaption>
        <TableHeader>
          <TableRow className="bg-[#F7F5F2]">
            <TableHead className="w-[80px]">ID</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Email</TableHead>
            <TableHead className="text-center">Terms</TableHead>
            <TableHead className="text-center">Privacy</TableHead>
            <TableHead className="text-center">Cookies</TableHead>
            <TableHead className="text-center">Data Processing</TableHead>
            <TableHead className="text-center">Marketing</TableHead>
            <TableHead className="text-right">Consent Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id} className="hover:bg-[#F7F5F2]">
              <TableCell className="font-medium">{user.id}</TableCell>
              <TableCell>{user.username}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell className="text-center">
                {user.termsAccepted ? (
                  <Badge className="bg-green-600">Accepted</Badge>
                ) : (
                  <Badge variant="outline" className="text-red-600 border-red-600">
                    Missing
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-center">
                {user.privacyPolicyAccepted ? (
                  <Badge className="bg-green-600">Accepted</Badge>
                ) : (
                  <Badge variant="outline" className="text-red-600 border-red-600">
                    Missing
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-center">
                {user.cookiePolicyAccepted ? (
                  <Badge className="bg-green-600">Accepted</Badge>
                ) : (
                  <Badge variant="outline" className="text-red-600 border-red-600">
                    Missing
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-center">
                {user.dataProcessingConsent ? (
                  <Badge className="bg-green-600">Accepted</Badge>
                ) : (
                  <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                    Not Given
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-center">
                {user.marketingConsent ? (
                  <Badge className="bg-green-600">Opted In</Badge>
                ) : (
                  <Badge variant="outline" className="text-gray-600 border-gray-600">
                    Opted Out
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-right">
                {formatDate(user.consentTimestamp)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}