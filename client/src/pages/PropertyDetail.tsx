import { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { Property } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import BookingForm from "@/components/BookingForm";
import ReviewSection from "@/components/ReviewSection";
import LocationMap from "@/components/LocationMap";
import SocialShare from "@/components/SocialShare";
import { useAuth } from "@/contexts/AuthContext";

export default function PropertyDetail() {
  const params = useParams<{ id: string }>();
  const id = params.id;
  const [_, navigate] = useLocation();
  const { isAuthenticated } = useAuth();
  
  // Clear any pending booking data when authenticated (but don't auto-submit)
  useEffect(() => {
    if (isAuthenticated && id) {
      const pendingBookingStr = sessionStorage.getItem('pendingBooking');
      if (pendingBookingStr) {
        // Just clear the pending booking - don't auto-submit
        sessionStorage.removeItem('pendingBooking');
      }
    }
  }, [isAuthenticated, id]);

  // Fetch property details
  const { data: property, isLoading: propertyLoading } = useQuery<Property>({
    queryKey: [`/api/properties/${id}`],
    queryFn: async () => {
      const response = await fetch(`/api/properties/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          navigate("/not-found");
        }
        throw new Error("Failed to fetch property");
      }
      const result = await response.json();
      return result.data;
    },
  });


  if (propertyLoading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <Skeleton className="h-14 w-3/4 mb-6" />
            <Skeleton className="h-6 w-1/2 mb-4" />
            <Skeleton className="h-6 w-1/3" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-10">
            <Skeleton className="h-96 lg:col-span-2 lg:row-span-2" />
            <Skeleton className="h-44" />
            <Skeleton className="h-44" />
          </div>
        </div>
      </section>
    );
  }

  if (!property) {
    return null;
  }

  const { 
    title, 
    description = "", 
    location, 
    latitude,
    longitude,
    images = [], 
    bedrooms, 
    bathrooms, 
    amenities = [], 
    halfDayPrice, 
    fullDayPrice 
  } = property;

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Property Header */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
            <h1 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-2 md:mb-0">{title}</h1>
            <div className="flex items-center space-x-4">
              <SocialShare 
                url={window.location.href}
                title={`${title} - Beautiful Farmhouse in ${location}`}
                description={description.slice(0, 150) + "..."}
                image={images[0]}
              />
              <button className="flex items-center text-[#605045] hover:text-[#4A6741]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                </svg>
                Save
              </button>
            </div>
          </div>
          
          <div className="flex flex-wrap items-center text-[#605045] mb-4">
            <div className="mb-2 md:mb-0">
              <span className="mr-1">•</span>
              <span>{location}</span>
            </div>
          </div>
        </div>
        
        {/* Property Gallery */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-10">
          {/* Main image */}
          <div className="lg:col-span-2 lg:row-span-2">
            <img 
              src={images[0]} 
              alt={title} 
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
          
          {/* Additional images */}
          {images.slice(1, 4).map((image, index) => (
            <div key={index} className={index === 2 ? "hidden lg:block" : ""}>
              <img 
                src={image} 
                alt={`${title} - image ${index + 2}`} 
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Property Details */}
          <div className="lg:col-span-2">
            <div className="border-b border-[#EBE6E1] pb-8 mb-8">
              <h2 className="font-heading text-2xl font-bold text-[#2D3C2D] mb-4">About This Farmhouse</h2>
              <div className="text-[#605045] space-y-4">
                {description.split('\n').map((paragraph, index) => (
                  <p key={index}>{paragraph}</p>
                ))}
              </div>
            </div>
            
            <div className="border-b border-[#EBE6E1] pb-8 mb-8">
              <h2 className="font-heading text-2xl font-bold text-[#2D3C2D] mb-4">Amenities</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#4A6741] mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>{bedrooms} Bedrooms</span>
                </div>
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#4A6741] mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>{bathrooms} Bathrooms</span>
                </div>
                
                {amenities.map((amenity, index) => (
                  <div key={index} className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#4A6741] mr-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>{amenity}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="border-b border-[#EBE6E1] pb-8 mb-8">
              <h2 className="font-heading text-2xl font-bold text-[#2D3C2D] mb-4">Location</h2>
              <div className="rounded-lg overflow-hidden h-64 bg-[#EBE6E1]">
                <LocationMap 
                  location={location} 
                  {...(latitude !== null && longitude !== null ? { latitude, longitude } : {})}
                  className="w-full h-full" 
                />
              </div>
              <p className="mt-2 text-[#605045]">{location}</p>
            </div>
            
            {/* Review Section */}
            <ReviewSection propertyId={Number(id)} />
          </div>
          
          {/* Booking Panel */}
          <div className="lg:col-span-1">
            <BookingForm 
              propertyId={Number(id)}
              halfDayPrice={halfDayPrice}
              fullDayPrice={fullDayPrice}
            />
          </div>
        </div>
      </div>
    </section>
  );
}
