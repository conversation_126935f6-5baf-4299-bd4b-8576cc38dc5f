import { useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import PropertyForm from "@/components/PropertyForm";

export default function CreateProperty() {
  const { user } = useAuth();
  const [_, navigate] = useLocation();
  const { toast } = useToast();

  // Redirect if not logged in or not an owner
  useEffect(() => {
    if (!user) {
      navigate('/login?redirect=/owner/property/create');
    } else if (user.role !== 'owner') {
      toast({
        title: "Access Denied",
        description: "Only property owners can create listings",
        variant: "destructive",
      });
      navigate('/');
    }
  }, [user, navigate, toast]);

  if (!user || user.role !== 'owner') {
    return null;
  }

  return (
    <div className="py-12 bg-[#F7F4F1]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <h1 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-8">Create New Property</h1>
          <PropertyForm mode="create" />
        </div>
      </div>
    </div>
  );
}
