import { useState } from "react";
import { useLocation, use<PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/contexts/AuthContext";
import LegalDocumentModal from "@/components/LegalDocumentModal";
import { Mail, Phone, Loader2 } from "lucide-react";

const registerSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^[6-9]\d{9}$/, "Please enter a valid Indian mobile number"),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "You must accept the Terms of Service, Privacy Policy, and Cookie Policy",
  }),
  role: z.enum(["user", "owner"]),
  consentToDataProcessing: z.boolean().default(false),
  consentToMarketing: z.boolean().default(false),
});

type RegisterFormValues = z.infer<typeof registerSchema>;

export default function Register() {
  const { login } = useAuth();
  const [_, navigate] = useLocation();
  const search = useSearch();
  const { toast } = useToast();
  const redirectPath = new URLSearchParams(search).get("redirect") || "/";
  const [modalOpen, setModalOpen] = useState(false);
  const [documentType, setDocumentType] = useState<"terms" | "privacy" | "cookie">("terms");
  
  

  const openLegalDocument = (type: "terms" | "privacy" | "cookie") => {
    setDocumentType(type);
    setModalOpen(true);
  };

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      acceptTerms: false,
      consentToDataProcessing: false,
      consentToMarketing: false,
      role: "user",
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (data: RegisterFormValues) => {
      // Send dual OTP for registration
      const fullPhone = `+91${data.phone}`;
      
      // Send SMS OTP
      const smsResponse = await apiRequest("POST", "/api/auth/otp/send-otp", {
        identifier: fullPhone,
        type: 'sms'
      });
      
      // Send Email OTP
      const emailResponse = await apiRequest("POST", "/api/auth/otp/send-otp", {
        identifier: data.email,
        type: 'email'
      });

      const [smsResult, emailResult] = await Promise.all([
        smsResponse.json(),
        emailResponse.json()
      ]);

      return { 
        sms: smsResult, 
        email: emailResult,
        userData: {
          fullName: data.fullName,
          email: data.email,
          phone: fullPhone,
          role: data.role,
          consentData: {
            termsAccepted: data.acceptTerms,
            dataProcessingConsent: data.consentToDataProcessing,
            marketingConsent: data.consentToMarketing,
            consentTimestamp: new Date().toISOString(),
          }
        }
      };
    },
    onSuccess: (data) => {
      // Store user data temporarily and navigate to OTP verification page
      sessionStorage.setItem('pendingRegistration', JSON.stringify(data.userData));
      toast({
        title: "OTPs Sent",
        description: "Verification codes sent to your phone and email",
        variant: "default",
      });
      // Navigate to dedicated OTP verification page
      navigate(`/verify-registration?phone=${encodeURIComponent(data.userData.phone)}&email=${encodeURIComponent(data.userData.email)}`);
    },
    onError: (error: Error) => {
      toast({
        title: "Registration failed",
        description: error.message || "An error occurred during registration.",
        variant: "destructive",
      });
    },
  });

  

  const onSubmit = (data: RegisterFormValues) => {
    registerMutation.mutate(data);
  };

  return (
    <div className="min-h-screen bg-[#F7F5F2] py-12">
      <div className="container px-4 mx-auto">
        <Card className="max-w-lg mx-auto shadow-md">
          <CardHeader className="bg-[#4A6741] text-white text-center">
            <CardTitle className="text-2xl font-bold">Create an account</CardTitle>
            <CardDescription className="text-[#E5DDD3]">Enter your details to register</CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D]">Full Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your full name"
                          {...field}
                          className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D]">Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter your email"
                          {...field}
                          className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D]">Phone Number</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#605045] font-medium">
                            +91
                          </div>
                          <Input
                            placeholder="Phone number"
                            className="pl-12 border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                            maxLength={10}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="acceptTerms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          aria-required="true"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sm font-normal text-[#605045]">
                          I agree to the <button type="button" onClick={(e) => { e.preventDefault(); openLegalDocument("terms"); }} className="text-[#4A6741] hover:text-[#3A5131] underline">Terms of Service</button>, <button type="button" onClick={(e) => { e.preventDefault(); openLegalDocument("privacy"); }} className="text-[#4A6741] hover:text-[#3A5131] underline">Privacy Policy</button>, and <button type="button" onClick={(e) => { e.preventDefault(); openLegalDocument("cookie"); }} className="text-[#4A6741] hover:text-[#3A5131] underline">Cookie Policy</button> <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="consentToDataProcessing"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sm font-normal text-[#605045]">
                          I consent to BookAFarm processing my personal data to provide the booking service as described in the <button type="button" onClick={(e) => { e.preventDefault(); openLegalDocument("privacy"); }} className="text-[#4A6741] hover:text-[#3A5131] underline">Privacy Policy</button>
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="consentToMarketing"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sm font-normal text-[#605045]">
                          I would like to receive marketing communications about similar properties and special offers (optional)
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D]">Account Type</FormLabel>
                      <FormControl>
                        <RadioGroup
                          value={field.value}
                          onValueChange={field.onChange}
                          className="flex flex-col space-y-2"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="user" />
                            </FormControl>
                            <FormLabel className="font-normal">Guest (Book properties)</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="owner" />
                            </FormControl>
                            <FormLabel className="font-normal">Owner (List properties)</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white"
                  disabled={registerMutation.isPending}
                >
                  {registerMutation.isPending ? "Creating account..." : "Create Account"}
                </Button>
                
                
                
                <div className="text-center text-sm text-[#605045]">
                  <span>Already have an account? </span>
                  <a
                    href={`/login${redirectPath !== "/" ? `?redirect=${encodeURIComponent(redirectPath)}` : ""}`}
                    className="text-[#4A6741] hover:text-[#3A5131] font-semibold"
                  >
                    Sign In
                  </a>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        
      </div>
      
      {/* Legal Document Modal */}
      <LegalDocumentModal 
        open={modalOpen}
        documentType={documentType}
        onClose={() => setModalOpen(false)}
      />
    </div>
  );
}