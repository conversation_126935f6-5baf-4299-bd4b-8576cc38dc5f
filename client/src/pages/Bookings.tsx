import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Calendar, MapPin, Users, Clock, CreditCard, CalendarDays, IndianRupee } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Link } from "wouter";
import LoadingSpinner from "@/components/LoadingSpinner";

interface BookingWithProperty {
  id: number;
  propertyId: number;
  userId: number;
  bookingDate: string;
  bookingType: "morning" | "full_day";
  guests: number;
  totalPrice: number;
  status: string;
  createdAt: string;
  property: {
    id: number;
    title: string;
    location: string;
    images: string[];
  };
}

export default function Bookings() {
  const { user, loading: authLoading } = useAuth();

  const { data: bookings, isLoading, error } = useQuery<BookingWithProperty[]>({
    queryKey: ['/api/bookings'],
    enabled: !!user && !authLoading,
    retry: 1,
  });

  if (authLoading) {
    return (
      <section className="py-16 bg-white min-h-screen">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="grid gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="w-full">
                  <CardContent className="p-6">
                    <div className="flex space-x-6">
                      <Skeleton className="w-32 h-32 rounded-lg" />
                      <div className="flex-1">
                        <Skeleton className="h-6 w-48 mb-2" />
                        <Skeleton className="h-4 w-32 mb-4" />
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                        <Skeleton className="h-8 w-32" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-[#4A6741] mb-4">Please Sign In</h1>
          <p className="text-[#605045] mb-4">You need to be logged in to view your bookings.</p>
          <Link href="/login">
            <Button className="bg-[#4A6741] hover:bg-[#3A5131]">Sign In</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <section className="py-16 bg-white min-h-screen">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-24 h-24 mx-auto mb-6 bg-[#F7F4F1] rounded-full flex items-center justify-center">
              <CalendarDays className="w-12 h-12 text-[#4A6741]" />
            </div>
            <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Unable to load bookings</h3>
            <p className="text-[#605045] mb-6">There was an error loading your bookings. Please try again later.</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Fix timezone issues by parsing dates consistently as UTC
  const parseDateAsUTC = (dateString: string) => {
    // Extract just the date part to avoid timezone conversion issues
    const datePart = dateString.split('T')[0]; // '2025-07-28'
    return new Date(datePart + 'T00:00:00.000Z');
  };

  // Fix timezone handling and categorization logic using UTC dates
  const today = new Date();
  today.setUTCHours(0, 0, 0, 0); // Start of today in UTC for accurate comparison

  const upcomingBookings = bookings?.filter((booking) => {
    const bookingDate = parseDateAsUTC(booking.bookingDate);
    
    // Future bookings with any active status (not cancelled/failed)
    return bookingDate >= today && 
           !['cancelled', 'payment_failed', 'expired'].includes(booking.status.toLowerCase());
  }) || [];

  const pastBookings = bookings?.filter((booking) => {
    const bookingDate = parseDateAsUTC(booking.bookingDate);
    
    // Past bookings OR cancelled/failed bookings regardless of date
    return bookingDate < today || 
           ['cancelled', 'payment_failed', 'expired'].includes(booking.status.toLowerCase());
  }) || [];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
      case 'pending_payment':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'payment_failed':
      case 'expired':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getBookingTypeLabel = (type: string) => {
    return type === 'morning' ? '12-Hour Access' : '24-Hour Access';
  };

  const getBookingTime = (type: string) => {
    return type === 'morning' ? '8am - 8pm' : '8am - 8am (next day)';
  };

  const BookingCard = ({ booking }: { booking: BookingWithProperty }) => {
    console.log('BookingCard - booking.property:', booking.property);
    console.log('BookingCard - images:', booking.property?.images);
    
    const imageUrl = booking.property?.images?.[0] || "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=400&q=80";
    console.log('BookingCard - imageUrl:', imageUrl);
    
    return (
      <Card className="w-full hover:shadow-lg transition-shadow duration-300 mb-4">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6">
            {/* Property Image */}
            <div className="w-full md:w-32 h-32 rounded-lg overflow-hidden bg-[#F7F4F1] flex-shrink-0">
              <img 
                src={imageUrl} 
                alt={booking.property?.title || "Property"}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error('Image failed to load:', imageUrl);
                  e.currentTarget.src = "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=400&q=80";
                }}
              />
            </div>

            {/* Booking Details */}
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4">
                <div>
                  <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-1">
                    {booking.property?.title}
                  </h3>
                  <div className="flex items-center text-[#605045] mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span className="text-sm">{booking.property?.location}</span>
                  </div>
                </div>
                <Badge className={`${getStatusColor(booking.status)} font-medium`}>
                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                </Badge>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center text-[#605045]">
                  <Calendar className="w-4 h-4 mr-2 text-[#4A6741]" />
                  <div>
                    <div className="text-sm font-medium text-[#2D3C2D]">
                      {format(parseDateAsUTC(booking.bookingDate), "MMM dd, yyyy")}
                    </div>
                    <div className="text-xs">{format(parseDateAsUTC(booking.bookingDate), "EEEE")}</div>
                  </div>
                </div>

                <div className="flex items-center text-[#605045]">
                  <Clock className="w-4 h-4 mr-2 text-[#4A6741]" />
                  <div>
                    <div className="text-sm font-medium text-[#2D3C2D]">
                      {getBookingTypeLabel(booking.bookingType)}
                    </div>
                    <div className="text-xs">{getBookingTime(booking.bookingType)}</div>
                  </div>
                </div>

                <div className="flex items-center text-[#605045]">
                  <Users className="w-4 h-4 mr-2 text-[#4A6741]" />
                  <div>
                    <div className="text-sm font-medium text-[#2D3C2D]">
                      {booking.guests} {booking.guests === 1 ? 'Guest' : 'Guests'}
                    </div>
                    <div className="text-xs">Total</div>
                  </div>
                </div>

                <div className="flex items-center text-[#605045]">
                  <IndianRupee className="w-4 h-4 mr-2 text-[#4A6741]" />
                  <div>
                    <div className="text-sm font-medium text-[#2D3C2D]">
                      ₹{booking.totalPrice.toLocaleString()}
                    </div>
                    <div className="text-xs">Total Amount</div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="text-xs text-[#766C63] mb-2 sm:mb-0">
                  Booking ID: #{booking.id} • Booked on {format(new Date(booking.createdAt), "MMM dd, yyyy")}
                </div>
                <div className="flex space-x-2">
                  <Link href={`/property/${booking.property?.id}`}>
                    <Button variant="outline" size="sm" className="border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white">
                      View Property
                    </Button>
                  </Link>
                  {booking.status === 'confirmed' && new Date(booking.bookingDate) >= new Date() && (
                    <Button size="sm" className="bg-[#4A6741] hover:bg-[#3A5131] text-white">
                      Contact Host
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <section className="py-16 bg-white min-h-screen">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-2">Your Bookings</h1>
            <p className="text-[#605045]">Manage and view all your farmhouse reservations</p>
          </div>

          {!bookings || bookings.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-6 bg-[#F7F4F1] rounded-full flex items-center justify-center">
                <CalendarDays className="w-12 h-12 text-[#4A6741]" />
              </div>
              <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">No bookings yet</h3>
              <p className="text-[#605045] mb-6">Start your farmhouse adventure by making your first booking!</p>
              <Link href="/">
                <Button className="bg-[#4A6741] hover:bg-[#3A5131] text-white">
                  Explore Farmhouses
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-8">
              {upcomingBookings.length > 0 && (
                <section>
                  <h2 className="text-2xl font-semibold text-[#4A6741] mb-4">
                    Upcoming Bookings ({upcomingBookings.length})
                  </h2>
                  <div className="grid gap-6">
                    {upcomingBookings.map((booking) => (
                      <BookingCard key={booking.id} booking={booking} />
                    ))}
                  </div>
                </section>
              )}

              {pastBookings.length > 0 && (
                <section>
                  <h2 className="text-2xl font-semibold text-[#4A6741] mb-4">
                    Past Bookings ({pastBookings.length})
                  </h2>
                  <div className="grid gap-6">
                    {pastBookings.map((booking) => (
                      <BookingCard key={booking.id} booking={booking} />
                    ))}
                  </div>
                </section>
              )}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}