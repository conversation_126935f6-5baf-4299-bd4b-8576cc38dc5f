import { useState, useEffect, useRef, use<PERSON>allback, useMemo } from "react";
import {
  AUDIO_FREQUENCIES,
  AUDIO_DURATIONS,
  AUDIO_VOLUMES,
  NOTIFICATION_SOUND_DELAYS,
  DELAYS,
  INTERVALS,
  TIMEOUTS,
  WEBSOCKET,
  FEATURE_FLAGS,
  CACHE_DURATIONS,
  PAGINATION,
  UI_DIMENSIONS,
} from "../../../shared/constants";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { Property } from "@shared/schema";
import { useAuth } from "@/contexts/AuthContext";
import { useAuthorization, usePropertyAuthorization, useBookingAuthorization, useAPIHeaders, useAuthorizationErrorHandler } from "@/hooks/useAuthorization";
import { Guards } from "@/components/AuthGuard";
import { Skeleton } from "@/components/ui/skeleton";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { 
  Calendar, Clock, Users, MapPin, DollarSign, Phone, Mail, 
  CheckCircle, XCircle, AlertCircle, Eye, Star, ThumbsUp, 
  Building2, PlusCircle, Camera, BarChart3, Trash2, Edit3, 
  ShieldCheck, ShieldX, MessageSquare, Bell, BellRing, 
  Wifi, WifiOff, RotateCcw 
} from "lucide-react";
import { IndianRupee } from "lucide-react";
import { format } from "date-fns";
import { MediaManagementSection } from "@/components/MediaManagementSection";
import { PricingManagementTab } from "@/components/PricingManagementTab";
import { PropertyFullCalendar } from "@/components/PropertyFullCalendar";

// MERN Stack Technical Foundation - Modular Architecture
// ====================================================

// Feature Module Registry - Plugin System Foundation
interface FeatureModule {
  id: string;
  name: string;
  description?: string;
  version: string;
  component: React.ComponentType<any>;
  apiEndpoints?: string[];
  dependencies?: string[];
  enabled: boolean;
}

// Modular API Client - Reusable HTTP Layer
class ModularApiClient {
  private baseUrl: string = '/api';
  private authHeaders: () => HeadersInit;

  constructor(authHeadersFn: () => HeadersInit) {
    this.authHeaders = authHeadersFn;
  }

  // Generic CRUD operations for any resource
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) url.searchParams.append(key, value.toString());
      });
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.authHeaders(),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.authHeaders(),
      },
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...this.authHeaders(),
      },
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.authHeaders(),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Batch operations for efficiency
  async batch<T>(operations: Array<{
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    endpoint: string;
    data?: any;
  }>): Promise<T[]> {
    const response = await fetch(`${this.baseUrl}/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.authHeaders(),
      },
      credentials: 'include',
      body: JSON.stringify({ operations }),
    });

    if (!response.ok) {
      throw new Error(`Batch API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

// Reusable Data Hooks - Modular State Management
const useModularApi = () => {
  const { getAuthHeaders } = useAPIHeaders();
  const apiClient = useMemo(() => new ModularApiClient(getAuthHeaders), [getAuthHeaders]);
  return apiClient;
};

// Generic Resource Hook - Works with any CRUD resource
function useResource<T>(
  resourceName: string,
  options: {
    params?: Record<string, any>;
    enabled?: boolean;
    refetchInterval?: number;
  } = {}
) {
  const api = useModularApi();
  const { params, enabled = true, refetchInterval } = options;

  return useQuery<T>({
    queryKey: [resourceName, params],
    queryFn: () => api.get<T>(`/${resourceName}`, params),
    enabled,
    refetchInterval: refetchInterval || false,
    staleTime: CACHE_DURATIONS.SHORT, // 5 minutes default cache
  });
}

// Generic Mutation Hook - Works with any resource mutations
function useResourceMutation<T>(
  resourceName: string,
  method: 'POST' | 'PUT' | 'DELETE' = 'POST'
) {
  const api = useModularApi();
  const queryClient = useQueryClient();

  return useMutation<T, Error, { id?: string; data?: any }>({
    mutationFn: ({ id, data }) => {
      const endpoint = id ? `/${resourceName}/${id}` : `/${resourceName}`;
      switch (method) {
        case 'POST':
          return api.post<T>(endpoint, data);
        case 'PUT':
          return api.put<T>(endpoint, data);
        case 'DELETE':
          return api.delete<T>(endpoint);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [resourceName] });
    },
  });
}

// Feature Module Manager - Plugin System
class FeatureModuleManager {
  private static instance: FeatureModuleManager;
  private modules: Map<string, FeatureModule> = new Map();

  static getInstance(): FeatureModuleManager {
    if (!FeatureModuleManager.instance) {
      FeatureModuleManager.instance = new FeatureModuleManager();
    }
    return FeatureModuleManager.instance;
  }

  registerModule(module: FeatureModule): void {
    this.modules.set(module.id, module);
  }

  getModule(id: string): FeatureModule | undefined {
    return this.modules.get(id);
  }

  getEnabledModules(): FeatureModule[] {
    return Array.from(this.modules.values()).filter(module => module.enabled);
  }

  enableModule(id: string): void {
    const module = this.modules.get(id);
    if (module) {
      module.enabled = true;
    }
  }

  disableModule(id: string): void {
    const module = this.modules.get(id);
    if (module) {
      module.enabled = false;
    }
  }
}

// Plugin System Components
// ========================

interface FeatureModuleRegistryProps {
  modules?: FeatureModule[];
  onModuleToggle?: (moduleId: string, enabled: boolean) => void;
  className?: string;
}

const FeatureModuleRegistry: React.FC<FeatureModuleRegistryProps> = ({
  modules = [],
  onModuleToggle,
  className = ""
}) => {
  const manager = FeatureModuleManager.getInstance();
  const [registeredModules, setRegisteredModules] = useState<FeatureModule[]>([]);

  useEffect(() => {
    // Register provided modules
    modules.forEach(module => manager.registerModule(module));
    
    // Get all registered modules
    const allModules = manager.getEnabledModules();
    setRegisteredModules(allModules);
  }, [modules]);

  const handleToggle = (moduleId: string, enabled: boolean) => {
    if (enabled) {
      manager.enableModule(moduleId);
    } else {
      manager.disableModule(moduleId);
    }
    
    setRegisteredModules(manager.getEnabledModules());
    onModuleToggle?.(moduleId, enabled);
  };

  return (
    <div className={`feature-registry ${className}`}>
      <div className="space-y-4">
        {registeredModules.map((module) => (
          <div key={module.id} className="border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{module.name}</h3>
                <p className="text-sm text-gray-600">{module.description}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    v{module.version}
                  </Badge>
                  {module.dependencies && module.dependencies.length > 0 && (
                    <span className="text-xs text-gray-500">
                      Requires: {module.dependencies.join(', ')}
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={module.enabled}
                  onCheckedChange={(checked) => handleToggle(module.id, checked)}
                />
              </div>
            </div>
            
            {module.enabled && module.component && (
              <div className="mt-4 border-t pt-4">
                <module.component />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Plugin Hook for Dynamic Feature Loading
const usePluginSystem = () => {
  const manager = FeatureModuleManager.getInstance();
  
  const loadPlugin = useCallback(async (pluginId: string, config?: any) => {
    try {
      // TODO: Plugin system not implemented - plugins directory doesn't exist
      // For now, return false to indicate plugin loading failed
      console.warn(`Plugin system not implemented. Cannot load plugin: ${pluginId}`);
      return false;
    } catch (error) {
      console.error(`Failed to load plugin ${pluginId}:`, error);
      return false;
    }
  }, []);
  
  const getAvailablePlugins = useCallback(async () => {
    // In a real implementation, this would fetch from an API
    return [
      { id: 'advanced-analytics', name: 'Advanced Analytics', version: '1.0.0' },
      { id: 'guest-messaging', name: 'Guest Messaging', version: '2.1.0' },
      { id: 'automated-pricing', name: 'Dynamic Pricing', version: '1.5.0' }
    ];
  }, []);
  
  return {
    loadPlugin,
    getAvailablePlugins,
    enabledModules: manager.getEnabledModules(),
    enableModule: manager.enableModule.bind(manager),
    disableModule: manager.disableModule.bind(manager)
  };
};

// Reusable UI Components Library
// ==============================

// Modular Card Component - Foundation for all feature cards
interface ModularCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  loading?: boolean;
  error?: string;
  className?: string;
  onRefresh?: () => void;
}

const ModularCard: React.FC<ModularCardProps> = ({
  title,
  subtitle,
  children,
  actions,
  loading = false,
  error,
  className = "",
  onRefresh
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
          </div>
          <div className="flex items-center gap-2">
            {onRefresh && (
              <Button variant="outline" size="sm" onClick={onRefresh} disabled={loading}>
                <RotateCcw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {actions}
          </div>
        </div>
      </div>
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-red-600 text-sm">{error}</p>
            {onRefresh && (
              <Button variant="outline" size="sm" onClick={onRefresh} className="mt-2">
                Try Again
              </Button>
            )}
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

// Modular Data Table - Reusable for any resource
interface Column<T> {
  key: keyof T | 'actions';
  header: string;
  render?: (value: any, record: T) => React.ReactNode;
  width?: string;
  sortable?: boolean;
}

interface ModularDataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  error?: string;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number) => void;
  };
  selection?: {
    selectedKeys: string[];
    onChange: (keys: string[]) => void;
  };
  actions?: {
    bulk?: Array<{
      label: string;
      onClick: (selectedItems: T[]) => void;
      icon?: React.ComponentType<any>;
    }>;
  };
}

function ModularDataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  error,
  pagination,
  selection,
  actions
}: ModularDataTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof T;
    direction: 'asc' | 'desc';
  } | null>(null);

  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aVal = a[sortConfig.key];
      const bVal = b[sortConfig.key];
      
      if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [data, sortConfig]);

  const handleSort = (key: keyof T) => {
    setSortConfig(current => ({
      key,
      direction: current?.key === key && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selection && actions?.bulk && selection.selectedKeys.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
          <span className="text-sm text-blue-700">
            {selection.selectedKeys.length} items selected
          </span>
          {actions.bulk.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => {
                const selectedItems = data.filter(item => 
                  selection.selectedKeys.includes(item.id?.toString() || '')
                );
                action.onClick(selectedItems);
              }}
            >
              {action.icon && <action.icon className="h-4 w-4 mr-1" />}
              {action.label}
            </Button>
          ))}
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              {selection && (
                <th className="text-left p-3">
                  <input
                    type="checkbox"
                    checked={selection.selectedKeys.length === data.length}
                    onChange={(e) => {
                      selection.onChange(
                        e.target.checked 
                          ? data.map(item => item.id?.toString() || '') 
                          : []
                      );
                    }}
                  />
                </th>
              )}
              {columns.map(column => (
                <th
                  key={column.key.toString()}
                  className={`text-left p-3 font-medium text-gray-700 ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-50' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key as keyof T)}
                >
                  <div className="flex items-center gap-1">
                    {column.header}
                    {column.sortable && sortConfig?.key === column.key && (
                      <span className="text-gray-400">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {sortedData.map((record: any, index: number) => (
              <tr key={record.id || index} className="border-b border-gray-100 hover:bg-gray-50">
                {selection && (
                  <td className="p-3">
                    <input
                      type="checkbox"
                      checked={selection.selectedKeys.includes(record.id?.toString() || '')}
                      onChange={(e) => {
                        const key = record.id?.toString() || '';
                        if (e.target.checked) {
                          selection.onChange([...selection.selectedKeys, key]);
                        } else {
                          selection.onChange(
                            selection.selectedKeys.filter(k => k !== key)
                          );
                        }
                      }}
                    />
                  </td>
                )}
                {columns.map(column => (
                  <td key={column.key.toString()} className="p-3">
                    {column.render 
                      ? column.render(record[column.key as keyof T], record)
                      : record[column.key as keyof T]?.toString()
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">
            Showing {Math.min((pagination.current - 1) * pagination.pageSize + 1, pagination.total)} to{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} of {pagination.total} results
          </span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onChange(pagination.current - 1)}
              disabled={pagination.current <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onChange(pagination.current + 1)}
              disabled={pagination.current * pagination.pageSize >= pagination.total}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

// Modular Form Builder - Dynamic forms for any resource
interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'select' | 'textarea' | 'checkbox' | 'date';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  validation?: (value: any) => string | undefined;
}

interface ModularFormProps {
  fields: FormField[];
  onSubmit: (data: Record<string, any>) => void;
  loading?: boolean;
  initialData?: Record<string, any>;
  submitLabel?: string;
}

const ModularForm: React.FC<ModularFormProps> = ({
  fields,
  onSubmit,
  loading = false,
  initialData = {},
  submitLabel = "Submit"
}) => {
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    fields.forEach(field => {
      const value = formData[field.name];
      
      if (field.required && (!value || value === '')) {
        newErrors[field.name] = `${field.label} is required`;
        return;
      }

      if (field.validation && value) {
        const error = field.validation(value);
        if (error) {
          newErrors[field.name] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {fields.map(field => (
        <div key={field.name} className="space-y-1">
          <label className="text-sm font-medium text-gray-700">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </label>
          
          {field.type === 'select' ? (
            <Select
              value={formData[field.name] || ''}
              onValueChange={(value) => handleChange(field.name, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : field.type === 'textarea' ? (
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder={field.placeholder}
              value={formData[field.name] || ''}
              onChange={(e) => handleChange(field.name, e.target.value)}
            />
          ) : field.type === 'checkbox' ? (
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={formData[field.name] || false}
                onChange={(e) => handleChange(field.name, e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">{field.placeholder}</span>
            </div>
          ) : (
            <Input
              type={field.type}
              placeholder={field.placeholder}
              value={formData[field.name] || ''}
              onChange={(e) => handleChange(field.name, e.target.value)}
            />
          )}
          
          {errors[field.name] && (
            <p className="text-sm text-red-600">{errors[field.name]}</p>
          )}
        </div>
      ))}
      
      <Button type="submit" disabled={loading} className="w-full">
        {loading ? 'Submitting...' : submitLabel}
      </Button>
    </form>
  );
};

interface BookingWithPropertyAndGuest {
  id: number;
  propertyId: number;
  userId: number;
  bookingDate: string;
  bookingType: "morning" | "full_day";
  guests: number;
  totalPrice: number;
  status: string;
  specialRequests?: string;
  createdAt: string;
  property: {
    id: number;
    title: string;
    location: string;
    images: string[];
  };
  guest: {
    id: number;
    fullName: string;
    username: string;
    email: string;
    phone: string | null;
  };
}

// CDN Image URL Optimization Helper
const getOptimizedImageUrl = (originalUrl: string, width?: number, quality = 80) => {
  if (!originalUrl) return originalUrl;
  
  // If it's already a CDN URL (like Cloudinary, ImageKit, etc.), add transformations
  if (originalUrl.includes('cloudinary.com')) {
    // Cloudinary transformations
    const baseUrl = originalUrl.split('/upload/')[0] + '/upload/';
    const imagePath = originalUrl.split('/upload/')[1];
    const transformations = `w_${width || 800},q_${quality},f_auto,c_fill`;
    return `${baseUrl}${transformations}/${imagePath}`;
  } else if (originalUrl.includes('imagekit.io')) {
    // ImageKit transformations
    return `${originalUrl}?tr=w-${width || 800},q-${quality},f-auto`;
  } else if (originalUrl.includes('res.cloudinary.com') || originalUrl.includes('cdn.')) {
    // Generic CDN with query parameters
    const separator = originalUrl.includes('?') ? '&' : '?';
    return `${originalUrl}${separator}w=${width || 800}&q=${quality}&format=auto`;
  }
  
  // For non-CDN URLs, return as-is (could be enhanced with a proxy service)
  return originalUrl;
};

// Get thumbnail URL (low resolution for quick loading)
const getThumbnailUrl = (originalUrl: string) => getOptimizedImageUrl(originalUrl, 300, 60);

// Get full resolution URL
const getFullResUrl = (originalUrl: string) => getOptimizedImageUrl(originalUrl, 1200, 85);

// Image Preloader Hook for better UX
const useImagePreloader = (urls: string[]) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  
  useEffect(() => {
    if (urls.length === 0) return;
    
    const preloadImage = (url: string) => {
      return new Promise<void>((resolve) => {
        const img = new Image();
        img.onload = () => {
          setLoadedImages(prev => new Set(prev).add(url));
          resolve();
        };
        img.onerror = () => resolve(); // Still resolve on error to continue
        img.src = url;
      });
    };

    // Preload thumbnail URLs first
    const thumbnailUrls = urls.map(getThumbnailUrl);
    Promise.all(thumbnailUrls.map(preloadImage));
  }, [urls]);

  return loadedImages;
};

// Notification Badge Component
const NotificationBadge = ({ 
  count, 
  type = 'default',
  pulse = false 
}: { 
  count: number; 
  type?: 'default' | 'warning' | 'error' | 'success';
  pulse?: boolean;
}) => {
  if (count <= 0) return null;

  const getBadgeStyles = () => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-500 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'success':
        return 'bg-green-500 text-white';
      default:
        return 'bg-blue-500 text-white';
    }
  };

  return (
    <span className={`
      inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full
      ${getBadgeStyles()}
      ${pulse ? 'animate-pulse' : ''}
      ${count > 99 ? 'px-1' : ''}
    `}>
      {count > 99 ? '99+' : count}
    </span>
  );
};

// Enhanced Toast Notification System
const useEnhancedToast = () => {
  const { toast } = useToast();

  const showSuccessToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-green-50 border-green-200 text-green-800",
      duration: 4000,
    });
  };

  const showErrorToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: "destructive",
      duration: 6000,
    });
  };

  const showWarningToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-yellow-50 border-yellow-200 text-yellow-800",
      duration: 5000,
    });
  };

  const showInfoToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-blue-50 border-blue-200 text-blue-800",
      duration: 4000,
    });
  };

  return { showSuccessToast, showErrorToast, showWarningToast, showInfoToast };
};

// Sound notification hook
// Sound notifications hook with proper cleanup to prevent memory leaks
const useSoundNotifications = () => {
  const soundTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Clean up all sound timeouts
  const cleanupSoundTimeouts = useCallback(() => {
    soundTimeoutsRef.current?.forEach(timeout => clearTimeout(timeout));
    soundTimeoutsRef.current?.clear();
  }, []);

  // Enhanced cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupSoundTimeouts();
    };
  }, [cleanupSoundTimeouts]);

  const playNotificationSound = useCallback((type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
    // Check if audio is supported
    if (!('AudioContext' in window) && !('webkitAudioContext' in window)) return;
    
    try {
      // Create AudioContext for better browser support
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      const playTone = (frequency: number, duration: number, volume = AUDIO_VOLUMES.LOW) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
      };

      // Different sound patterns for different notification types
      switch (type) {
        case 'success':
          // Happy ascending tones
          playTone(AUDIO_FREQUENCIES.C5, AUDIO_DURATIONS.SHORT);
          const successTimeout1 = setTimeout(() => playTone(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.SHORT), NOTIFICATION_SOUND_DELAYS.SUCCESS_E5);
          const successTimeout2 = setTimeout(() => playTone(AUDIO_FREQUENCIES.G5, AUDIO_DURATIONS.MEDIUM), NOTIFICATION_SOUND_DELAYS.SUCCESS_G5);
          soundTimeoutsRef.current?.add(successTimeout1);
          soundTimeoutsRef.current?.add(successTimeout2);
          // Auto-cleanup after completion
          setTimeout(() => {
            soundTimeoutsRef.current?.delete(successTimeout1);
            soundTimeoutsRef.current?.delete(successTimeout2);
          }, NOTIFICATION_SOUND_DELAYS.SUCCESS_CLEANUP);
          break;
        case 'error':
          // Alert descending tones
          playTone(AUDIO_FREQUENCIES.A5, AUDIO_DURATIONS.MEDIUM);
          const errorTimeout = setTimeout(() => playTone(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.VERY_LONG), NOTIFICATION_SOUND_DELAYS.ERROR_E5);
          soundTimeoutsRef.current?.add(errorTimeout);
          setTimeout(() => soundTimeoutsRef.current?.delete(errorTimeout), NOTIFICATION_SOUND_DELAYS.ERROR_CLEANUP);
          break;
        case 'warning':
          // Two-tone warning
          playTone(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.MEDIUM);
          const warningTimeout = setTimeout(() => playTone(AUDIO_FREQUENCIES.C5, AUDIO_DURATIONS.MEDIUM), NOTIFICATION_SOUND_DELAYS.WARNING_C5);
          soundTimeoutsRef.current?.add(warningTimeout);
          setTimeout(() => soundTimeoutsRef.current?.delete(warningTimeout), NOTIFICATION_SOUND_DELAYS.WARNING_CLEANUP);
          break;
        case 'info':
        default:
          // Gentle notification tone
          playTone(AUDIO_FREQUENCIES.C5, AUDIO_DURATIONS.MEDIUM);
          const infoTimeout = setTimeout(() => playTone(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.MEDIUM), NOTIFICATION_SOUND_DELAYS.INFO_E5);
          soundTimeoutsRef.current?.add(infoTimeout);
          setTimeout(() => soundTimeoutsRef.current?.delete(infoTimeout), NOTIFICATION_SOUND_DELAYS.INFO_CLEANUP);
          break;
      }
    } catch (error) {
      console.warn('Failed to play notification sound:', error);
    }
  }, []);

  const playBookingNotificationSound = useCallback(() => {
    // Check if audio is supported
    if (!('AudioContext' in window) && !('webkitAudioContext' in window)) return;
    
    try {
      // Special sound for new booking notifications
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      const playTone = (frequency: number, duration: number, volume = AUDIO_VOLUMES.MEDIUM) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
      };

      // Distinctive booking notification melody
      playTone(AUDIO_FREQUENCIES.C5, AUDIO_DURATIONS.SHORT);
      const bookingTimeout1 = setTimeout(() => playTone(AUDIO_FREQUENCIES.E5, AUDIO_DURATIONS.SHORT), NOTIFICATION_SOUND_DELAYS.BOOKING_E5);
      const bookingTimeout2 = setTimeout(() => playTone(AUDIO_FREQUENCIES.G5, AUDIO_DURATIONS.SHORT), NOTIFICATION_SOUND_DELAYS.BOOKING_G5);
      const bookingTimeout3 = setTimeout(() => playTone(AUDIO_FREQUENCIES.C6, AUDIO_DURATIONS.LONG), NOTIFICATION_SOUND_DELAYS.BOOKING_C6);
      
      // Track timeouts for cleanup
      soundTimeoutsRef.current?.add(bookingTimeout1);
      soundTimeoutsRef.current?.add(bookingTimeout2);
      soundTimeoutsRef.current?.add(bookingTimeout3);
      
      // Auto-cleanup after completion
      setTimeout(() => {
        soundTimeoutsRef.current?.delete(bookingTimeout1);
        soundTimeoutsRef.current?.delete(bookingTimeout2);
        soundTimeoutsRef.current?.delete(bookingTimeout3);
      }, NOTIFICATION_SOUND_DELAYS.BOOKING_CLEANUP);
    } catch (error) {
      console.warn('Failed to play booking notification sound:', error);
    }
  }, []);

  return { playNotificationSound, playBookingNotificationSound, cleanupSoundTimeouts };
};

// Feature Flags Configuration - Scalable for Future Enhancements
// Feature Flags Configuration - Using centralized constants with local overrides
const LOCAL_FEATURE_FLAGS = {
  ...FEATURE_FLAGS,
  // Override the realtime values with constants
  REALTIME: {
    ...FEATURE_FLAGS.REALTIME,
    POLLING_INTERVAL: INTERVALS.POLLING_DEFAULT,
    RECONNECT_ATTEMPTS: WEBSOCKET.MAX_RECONNECT_ATTEMPTS,
    RECONNECT_INTERVAL: WEBSOCKET.RECONNECT_INTERVAL,
  }
};

// Connection status type
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

// Real-time updates hook with WebSocket + Polling fallback
const useRealTimeUpdates = (userId: number | null) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const queryClient = useQueryClient();
  const { showInfoToast, showSuccessToast, showErrorToast } = useEnhancedToast();
  const { playBookingNotificationSound, playNotificationSound } = useSoundNotifications();
  
  const wsRef = useRef<WebSocket | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket connection handler with enhanced error handling
  const connectWebSocket = useCallback(() => {
    if (!userId || !LOCAL_FEATURE_FLAGS.REALTIME.ENABLED || !LOCAL_FEATURE_FLAGS.REALTIME.WEBSOCKET_ENABLED) return;

    // Close existing connection if present
    if (wsRef.current && wsRef.current?.readyState !== WebSocket.CLOSED) {
      wsRef.current?.close(1000, 'Reconnecting');
    }

    try {
      setConnectionStatus('connecting');
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/ws/owner/${userId}`;
      
      wsRef.current = new WebSocket(wsUrl);

      if (wsRef.current) {
        wsRef.current.onopen = () => {
          console.log('WebSocket connected');
          setConnectionStatus('connected');
          setReconnectAttempts(0);
          
          // Clear polling since WebSocket is active
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
        };

        wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleRealTimeUpdate(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
        };

        wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setConnectionStatus('disconnected');
        
        // Clear the reference to prevent memory leaks
        wsRef.current = null;
        
        // Attempt reconnection if not manual close
        if (event.code !== WEBSOCKET.NORMAL_CLOSURE && reconnectAttempts < LOCAL_FEATURE_FLAGS.REALTIME.RECONNECT_ATTEMPTS) {
          scheduleReconnect();
        } else {
          // Fall back to polling if WebSocket fails
          startPolling();
        }
      };

        wsRef.current.onerror = (error) => {
          console.error('WebSocket error:', error);
          setConnectionStatus('error');
        };
      }

    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      setConnectionStatus('error');
      startPolling();
    }
  }, [userId, reconnectAttempts]);

  // Schedule WebSocket reconnection
  const scheduleReconnect = useCallback(() => {
    reconnectTimeoutRef.current = setTimeout(() => {
      setReconnectAttempts(prev => prev + 1);
      connectWebSocket();
    }, LOCAL_FEATURE_FLAGS.REALTIME.RECONNECT_INTERVAL);
  }, [connectWebSocket]);

  // Polling fallback
  const startPolling = useCallback(() => {
    if (!LOCAL_FEATURE_FLAGS.REALTIME.ENABLED || !LOCAL_FEATURE_FLAGS.REALTIME.POLLING_ENABLED || pollingIntervalRef.current) return;

    console.log('Starting polling fallback');
    pollingIntervalRef.current = setInterval(async () => {
      try {
        // Poll for dashboard summary updates
        await queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
        
        // Poll for booking updates if on bookings tab
        if (document.querySelector('[data-tab="bookings"][data-active="true"]')) {
          await queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        }
      } catch (error) {
        console.error('Polling update failed:', error);
      }
    }, LOCAL_FEATURE_FLAGS.REALTIME.POLLING_INTERVAL);
  }, [queryClient]);

  // Handle real-time updates from WebSocket
  const handleRealTimeUpdate = useCallback((data: any) => {
    const { type, payload } = data;

    switch (type) {
      case 'booking_created':
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
        
        showInfoToast(
          '🔔 New Booking Request!',
          `${payload.guestName} wants to book "${payload.propertyName}" for ${payload.date}.`
        );
        playBookingNotificationSound();

        // Browser notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('New Booking Request', {
            body: `${payload.guestName} wants to book ${payload.propertyName}`,
            icon: '/favicon.ico',
            tag: `booking-${payload.bookingId}`,
          });
        }
        break;

      case 'booking_status_changed':
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
        
        const statusMessages = {
          confirmed: `✅ Booking confirmed for "${payload.propertyName}"`,
          cancelled: `❌ Booking cancelled for "${payload.propertyName}"`,
          completed: `🏁 Booking completed for "${payload.propertyName}"`,
        };

        const message = statusMessages[payload.newStatus as keyof typeof statusMessages];
        if (message) {
          showInfoToast('📋 Status Update', message);
          playNotificationSound('info');
        }
        break;

      case 'booking_modified':
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        
        showInfoToast(
          '✏️ Booking Modified',
          `${payload.guestName} updated their booking for "${payload.propertyName}".`
        );
        playNotificationSound('info');
        break;

      case 'property_inquiry':
        showInfoToast(
          '💬 New Inquiry',
          `Someone is interested in "${payload.propertyName}". Check your messages.`
        );
        playNotificationSound('info');
        break;

      default:
        console.log('Unknown real-time update type:', type);
    }
  }, [queryClient, showInfoToast, playBookingNotificationSound, playNotificationSound]);

  // Initialize connection with comprehensive cleanup
  useEffect(() => {
    if (!userId || !LOCAL_FEATURE_FLAGS.REALTIME.ENABLED) return;

    connectWebSocket();

    return () => {
      // Comprehensive cleanup on unmount or dependency change
      console.log('Cleaning up real-time connections...');
      
      // Close WebSocket connection
      if (wsRef.current) {
        wsRef.current?.close(1000, 'Component unmounting');
        wsRef.current = null;
      }
      
      // Clear polling interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      
      // Clear reconnection timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      
      // Reset connection status
      setConnectionStatus('disconnected');
      setReconnectAttempts(0);
    };
  }, [userId, connectWebSocket]);

  return {
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    reconnectAttempts,
  };
};

// Connection Status Indicator Component
const ConnectionStatusIndicator = ({ 
  status, 
  isConnected, 
  reconnectAttempts 
}: { 
  status: ConnectionStatus; 
  isConnected: boolean; 
  reconnectAttempts: number; 
}) => {
  if (!FEATURE_FLAGS.REALTIME.ENABLED) return null;

  const getStatusInfo = () => {
    switch (status) {
      case 'connected':
        return {
          icon: <Wifi className="h-3 w-3 text-green-600" />,
          text: 'Live updates active',
          className: 'bg-green-50 text-green-700 border-green-200'
        };
      case 'connecting':
        return {
          icon: <RotateCcw className="h-3 w-3 text-blue-600 animate-spin" />,
          text: 'Connecting...',
          className: 'bg-blue-50 text-blue-700 border-blue-200'
        };
      case 'error':
        return {
          icon: <WifiOff className="h-3 w-3 text-red-600" />,
          text: `Connection error${reconnectAttempts > 0 ? ` (${reconnectAttempts}/5)` : ''}`,
          className: 'bg-red-50 text-red-700 border-red-200'
        };
      case 'disconnected':
        return {
          icon: <WifiOff className="h-3 w-3 text-yellow-600" />,
          text: 'Using polling updates',
          className: 'bg-yellow-50 text-yellow-700 border-yellow-200'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium border ${statusInfo.className}`}>
      {statusInfo.icon}
      <span className="hidden sm:inline">{statusInfo.text}</span>
    </div>
  );
};

// Optimistic Updates Hook
const useOptimisticUpdates = () => {
  const queryClient = useQueryClient();
  const { showErrorToast } = useEnhancedToast();

  const updateBookingOptimistically = useCallback(async (
    bookingId: number,
    newStatus: string,
    rollbackFn: () => void
  ) => {
    // Get current bookings data
    const currentBookings = queryClient.getQueryData<{ bookings: BookingWithPropertyAndGuest[] }>(['/api/bookings/owner']);
    
    if (currentBookings) {
      // Create optimistic update
      const optimisticBookings = {
        ...currentBookings,
        bookings: currentBookings.bookings.map(booking =>
          booking.id === bookingId 
            ? { ...booking, status: newStatus }
            : booking
        )
      };

      // Apply optimistic update
      queryClient.setQueryData(['/api/bookings/owner'], optimisticBookings);

      // Update dashboard summary optimistically
      const currentSummary = queryClient.getQueryData(['/api/owner/dashboard/summary']);
      if (currentSummary) {
        const updatedSummary = {
          ...currentSummary,
          pendingBookings: newStatus === 'confirmed' || newStatus === 'cancelled' 
            ? Math.max(0, (currentSummary as any).pendingBookings - 1)
            : (currentSummary as any).pendingBookings,
          confirmedBookings: newStatus === 'confirmed'
            ? ((currentSummary as any).confirmedBookings || 0) + 1
            : (currentSummary as any).confirmedBookings,
        };
        queryClient.setQueryData(['/api/owner/dashboard/summary'], updatedSummary);
      }

      // Set up rollback timeout (in case the server update fails)
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
      }, 5000);
    }
  }, [queryClient]);

  return { updateBookingOptimistically };
};

// Future Enhancement: Advanced Listing Management Component (Foundation)
const ListingManagementSection = ({ property }: { property: any }) => {
  if (!FEATURE_FLAGS.ENHANCEMENTS.LISTING_MANAGEMENT) return null;

  return (
    <div className="mt-4 p-4 border-t">
      <h4 className="font-medium text-gray-900 mb-3">Advanced Listing Management</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Check-in Time</label>
          <Input placeholder="3:00 PM" disabled />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Check-out Time</label>
          <Input placeholder="11:00 AM" disabled />
        </div>
      </div>
      <div className="mt-4">
        <label className="text-sm font-medium text-gray-700">Amenities</label>
        <div className="mt-2 flex flex-wrap gap-2">
          <Badge variant="secondary">WiFi</Badge>
          <Badge variant="secondary">Pool</Badge>
          <Badge variant="secondary">Kitchen</Badge>
          <Button variant="outline" size="sm" disabled>
            <PlusCircle className="h-3 w-3 mr-1" />
            Add Amenity
          </Button>
        </div>
      </div>
    </div>
  );
};

// Future Enhancement: Advanced Analytics Component (Foundation)
const AdvancedAnalyticsSection = () => {
  if (!FEATURE_FLAGS.ENHANCEMENTS.ADVANCED_ANALYTICS) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
        <p className="text-gray-600 mb-4">Get detailed insights into your property performance.</p>
        <div className="bg-gray-50 p-4 rounded-lg max-w-md mx-auto">
          <p className="text-sm text-gray-600 mb-2">Coming soon:</p>
          <ul className="text-sm text-gray-600 space-y-1 text-left">
            <li>• Property views and engagement metrics</li>
            <li>• Revenue trends and forecasting</li>
            <li>• Guest demographics and preferences</li>
            <li>• Competitive analysis</li>
            <li>• Custom reporting</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Property Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Views</p>
              <p className="text-2xl font-bold">1,234</p>
            </div>
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
          <p className="text-sm text-green-600 mt-2">↗ +12% from last month</p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-bold">8.5%</p>
            </div>
            <BarChart3 className="h-8 w-8 text-green-600" />
          </div>
          <p className="text-sm text-green-600 mt-2">↗ +2.1% from last month</p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg. Stay Duration</p>
              <p className="text-2xl font-bold">3.2 days</p>
            </div>
            <Calendar className="h-8 w-8 text-purple-600" />
          </div>
          <p className="text-sm text-red-600 mt-2">↘ -0.3 days from last month</p>
        </div>
      </div>

      {/* Revenue Chart Placeholder */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-medium mb-4">Revenue Trends</h3>
        <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
          <p className="text-gray-500">Interactive revenue chart would go here</p>
        </div>
      </div>
    </div>
  );
};

// Future Enhancement: Guest Messaging System (Foundation)
const GuestMessagingSection = () => {
  if (!FEATURE_FLAGS.ENHANCEMENTS.GUEST_MESSAGING) {
    return (
      <div className="text-center py-12">
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Guest Messaging</h3>
        <p className="text-gray-600 mb-4">Communicate directly with your guests through our platform.</p>
        <div className="bg-gray-50 p-4 rounded-lg max-w-md mx-auto">
          <p className="text-sm text-gray-600 mb-2">Features include:</p>
          <ul className="text-sm text-gray-600 space-y-1 text-left">
            <li>• Real-time chat with guests</li>
            <li>• Automated check-in instructions</li>
            <li>• Quick response templates</li>
            <li>• File and photo sharing</li>
            <li>• Message history and search</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border">
      <div className="p-4 border-b">
        <h3 className="font-medium">Recent Messages</h3>
      </div>
      <div className="divide-y">
        {/* Message previews would go here */}
        <div className="p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 w-8 h-8 rounded-full flex items-center justify-center">
              <Users className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <p className="font-medium text-sm">John Smith</p>
                <p className="text-xs text-gray-500">2 hours ago</p>
              </div>
              <p className="text-sm text-gray-600">Hi! What time is check-in tomorrow?</p>
              <p className="text-xs text-gray-500">Sunshine Villa - Confirmed</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Progressive Disclosure Hook for Features
const useFeatureDisclosure = () => {
  const [disclosedFeatures, setDisclosedFeatures] = useState<Set<string>>(new Set());

  const discloseFeature = (feature: string) => {
    setDisclosedFeatures(prev => new Set(Array.from(prev).concat(feature)));
  };

  const isFeatureDisclosed = (feature: string) => {
    return disclosedFeatures.has(feature);
  };

  return { discloseFeature, isFeatureDisclosed };
};

// Feature Preview Card Component
const FeaturePreviewCard = ({ 
  title, 
  description, 
  icon: Icon, 
  comingSoon = true,
  onClick 
}: {
  title: string;
  description: string;
  icon: any;
  comingSoon?: boolean;
  onClick?: () => void;
}) => {
  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow cursor-pointer" onClick={onClick}>
      <div className="flex items-center justify-between mb-3">
        <Icon className="h-8 w-8 text-gray-600" />
        {comingSoon && (
          <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">
            Coming Soon
          </Badge>
        )}
      </div>
      <h3 className="font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
      <div className="mt-4">
        <Button variant="outline" size="sm" disabled={comingSoon}>
          {comingSoon ? 'Notify Me' : 'Explore'}
        </Button>
      </div>
    </div>
  );
};

// Email Notification Preferences Component
const EmailNotificationSettings = ({ 
  isOpen, 
  onClose,
  currentSettings = {}
}: {
  isOpen: boolean;
  onClose: () => void;
  currentSettings?: any;
}) => {
  const [settings, setSettings] = useState({
    newBookings: true,
    bookingConfirmations: true,
    bookingCancellations: true,
    bookingModifications: true,
    paymentNotifications: true,
    weeklyReports: false,
    monthlyReports: true,
    ...currentSettings
  });

  const { showSuccessToast } = useEnhancedToast();
  
  const handleSaveSettings = async () => {
    try {
      const response = await fetch('/api/owner/notification-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        showSuccessToast(
          '✅ Settings Saved',
          'Your email notification preferences have been updated successfully.'
        );
        onClose();
      }
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Email Notification Settings
          </DialogTitle>
          <DialogDescription>
            Choose which email notifications you'd like to receive
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-gray-900">Booking Notifications</h4>
            
            {[
              { key: 'newBookings', label: 'New booking requests', icon: '📝' },
              { key: 'bookingConfirmations', label: 'Booking confirmations', icon: '✅' },
              { key: 'bookingCancellations', label: 'Booking cancellations', icon: '❌' },
              { key: 'bookingModifications', label: 'Booking modifications', icon: '✏️' },
              { key: 'paymentNotifications', label: 'Payment confirmations', icon: '💳' },
            ].map(({ key, label, icon }) => (
              <div key={key} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm">{icon}</span>
                  <label className="text-sm text-gray-700">{label}</label>
                </div>
                <input
                  type="checkbox"
                  checked={settings[key as keyof typeof settings]}
                  onChange={(e) => setSettings((prev: any) => ({
                    ...prev,
                    [key]: e.target.checked
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
          
          <div className="border-t pt-3">
            <h4 className="font-medium text-sm text-gray-900 mb-3">Reports</h4>
            
            {[
              { key: 'weeklyReports', label: 'Weekly performance reports', icon: '📊' },
              { key: 'monthlyReports', label: 'Monthly revenue summaries', icon: '📈' },
            ].map(({ key, label, icon }) => (
              <div key={key} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm">{icon}</span>
                  <label className="text-sm text-gray-700">{label}</label>
                </div>
                <input
                  type="checkbox"
                  checked={settings[key as keyof typeof settings]}
                  onChange={(e) => setSettings((prev: any) => ({
                    ...prev,
                    [key]: e.target.checked
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex gap-3 mt-6">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          <Button onClick={handleSaveSettings} className="flex-1">
            Save Settings
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Progressive Lazy Image Component with CDN optimization
const LazyImage = ({ 
  src, 
  alt, 
  className,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  priority = false
}: { 
  src: string; 
  alt: string; 
  className: string;
  sizes?: string;
  priority?: boolean; // For above-the-fold images
}) => {
  const [thumbnailLoaded, setThumbnailLoaded] = useState(false);
  const [fullResLoaded, setFullResLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority); // If priority, start in view
  const [loadFullRes, setLoadFullRes] = useState(priority);
  const [connectionSpeed, setConnectionSpeed] = useState<'slow' | 'fast'>('fast');
  const imgRef = useRef<HTMLDivElement>(null);

  const thumbnailUrl = getThumbnailUrl(src);
  const fullResUrl = getFullResUrl(src);

  // Detect connection speed
  useEffect(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        const isSlowConnection = 
          connection.effectiveType === 'slow-2g' || 
          connection.effectiveType === '2g' ||
          connection.saveData === true;
        setConnectionSpeed(isSlowConnection ? 'slow' : 'fast');
      }
    }
  }, []);

  useEffect(() => {
    if (priority) return; // Skip intersection observer for priority images

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          // Delay full-res loading based on connection speed
          const delay = connectionSpeed === 'slow' ? 500 : 100;
          setTimeout(() => setLoadFullRes(true), delay);
          observer.disconnect();
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, connectionSpeed]);

  return (
    <div ref={imgRef} className={`${className} relative overflow-hidden`}>
      {!isInView ? (
        // Placeholder while not in view
        <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
          <Camera className="h-8 w-8 text-gray-400" />
        </div>
      ) : (
        <>
          {/* Thumbnail (loads first) */}
          <img
            src={thumbnailUrl}
            alt={alt}
            className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
              thumbnailLoaded ? 'opacity-100' : 'opacity-0'
            } ${fullResLoaded && connectionSpeed === 'fast' ? 'opacity-0' : 'opacity-100'}`}
            onLoad={() => setThumbnailLoaded(true)}
            loading={priority ? "eager" : "lazy"}
          />
          
          {/* Full resolution image (only load on fast connections or user preference) */}
          {loadFullRes && connectionSpeed === 'fast' && (
            <img
              src={fullResUrl}
              alt={alt}
              className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-700 ${
                fullResLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => setFullResLoaded(true)}
              loading={priority ? "eager" : "lazy"}
              sizes={sizes}
            />
          )}
          
          {/* Loading overlay */}
          {!thumbnailLoaded && (
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
              <div className="flex flex-col items-center gap-2">
                <div className="w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-xs text-gray-500">
                  {connectionSpeed === 'slow' ? 'Optimizing...' : 'Loading...'}
                </span>
              </div>
            </div>
          )}
          
          {/* Progressive loading indicator */}
          {thumbnailLoaded && !fullResLoaded && loadFullRes && connectionSpeed === 'fast' && (
            <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
              HD Loading...
            </div>
          )}
          
          {/* Connection speed indicator */}
          {thumbnailLoaded && connectionSpeed === 'slow' && (
            <div className="absolute bottom-2 right-2 bg-blue-500/80 text-white text-xs px-2 py-1 rounded">
              Data Saver
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default function OwnerDashboard() {
  const { user, loading } = useAuth();
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const { showSuccessToast, showErrorToast, showWarningToast, showInfoToast } = useEnhancedToast();
  const { playNotificationSound, cleanupSoundTimeouts } = useSoundNotifications();
  const queryClient = useQueryClient();
  const auth = useAuthorization();
  const { getAuthHeaders } = useAPIHeaders();
  const { handleAuthError } = useAuthorizationErrorHandler();

  // State management for dashboard functionality - MUST BE BEFORE EARLY RETURNS
  const [selectedTab, setSelectedTab] = useState<'properties' | 'pricing' | 'bookings' | 'calendar' | 'analytics' | 'communications' | 'media' | 'settings'>('properties');
  const [bookingStatusFilter, setBookingStatusFilter] = useState<string>('all');
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);
  const [propertySearch, setPropertySearch] = useState('');
  const [showBookingDetails, setShowBookingDetails] = useState<BookingWithPropertyAndGuest | null>(null);
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    action: () => void;
    actionLabel: string;
    variant: 'default' | 'destructive';
  }>({ isOpen: false, title: '', description: '', action: () => {}, actionLabel: '', variant: 'default' });
  const [actionInProgress, setActionInProgress] = useState<number | null>(null);
  const [recentlyUpdated, setRecentlyUpdated] = useState<Set<number>>(new Set());
  const [propertyActionInProgress, setPropertyActionInProgress] = useState<number | null>(null);
  
  // Initialize ALL hooks BEFORE any early returns - CRITICAL FOR HOOKS RULES
  const { connectionStatus, isConnected, reconnectAttempts } = useRealTimeUpdates(user?.id || null);
  const { updateBookingOptimistically } = useOptimisticUpdates();
  const { discloseFeature, isFeatureDisclosed } = useFeatureDisclosure();
  
  // Pagination states
  const [propertyPage, setPropertyPage] = useState(1);
  const [bookingPage, setBookingPage] = useState(1);
  const PROPERTIES_PER_PAGE = 9;
  const BOOKINGS_PER_PAGE = 10;

  // All mutation hooks must be here
  const deleteProperty = useMutation({
    mutationFn: async (propertyId: number) => {
      if (!auth.canDelete) {
        throw new Error('You are not authorized to delete properties');
      }
      
      const response = await fetch(`/api/properties/${propertyId}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
        credentials: 'include',
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to delete this property');
        }
        throw new Error('Failed to delete property');
      }
      return response.json();
    },
    onSuccess: (_, propertyId) => {
      setPropertyActionInProgress(null);
      queryClient.invalidateQueries({ queryKey: ['/api/properties/owner'] });
      queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
      showWarningToast(
        "🗑️ Property Deleted",
        "Property has been permanently removed from your listings. All associated bookings have been cancelled."
      );
      playNotificationSound('warning');
    },
    onError: (error: any) => {
      setPropertyActionInProgress(null);
      if (error.message?.includes('authorized')) {
        handleAuthError(error, 'delete this property');
      } else {
        showErrorToast(
          "❌ Deletion Failed",
          "Unable to delete property. Please check if it has active bookings and try again."
        );
        playNotificationSound('error');
      }
    }
  });

  // Dashboard summary query
  const { data: dashboardSummary, isLoading: summaryLoading } = useQuery({
    queryKey: ['/api/owner/dashboard/summary'],
    queryFn: async () => {
      const response = await fetch('/api/owner/dashboard/summary', {
        credentials: 'include',
        headers: getAuthHeaders()
      });
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard summary');
      }
      return response.json();
    },
    enabled: !!user && user.role === 'owner' && auth.canAccess,
    staleTime: CACHE_DURATIONS.SHORT, // 5 minutes
  });

  // Properties query
  const { data: propertiesData, isLoading: propertiesLoading, error: propertiesError } = useQuery<{
    properties: Property[],
    total: number,
    hasMore: boolean
  }>({
    queryKey: ['/api/properties/owner', propertyPage, propertySearch],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: propertyPage.toString(),
        limit: PROPERTIES_PER_PAGE.toString(),
        fields: 'id,title,location,halfDayPrice,fullDayPrice,images',
        imageLimit: '1',
        ...(propertySearch && { search: propertySearch })
      });
      
      const response = await fetch(`/api/properties/owner/me?${params}`, {
        credentials: 'include',
        headers: getAuthHeaders()
      });
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        if (response.status === 403) {
          throw new Error('You are not authorized to view properties');
        }
        throw new Error('Failed to fetch properties');
      }
      const result = await response.json();
      return {
        properties: result.data || result,
        total: result.total || (result.data || result).length,
        hasMore: (result.data || result).length === PROPERTIES_PER_PAGE
      };
    },
    enabled: !!user && user.role === 'owner' && auth.canAccess && selectedTab === 'properties',
    placeholderData: (previousData: any) => previousData,
    staleTime: CACHE_DURATIONS.VERY_SHORT
  });

  // Bookings query
  const { data: bookingsData, isLoading: bookingsLoading, error: bookingsError } = useQuery<{
    bookings: BookingWithPropertyAndGuest[],
    total: number,
    hasMore: boolean
  }>({
    queryKey: ['/api/bookings/owner', bookingPage, bookingStatusFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: bookingPage.toString(),
        limit: BOOKINGS_PER_PAGE.toString(),
        ...(bookingStatusFilter !== 'all' && { status: bookingStatusFilter })
      });
      
      const response = await fetch(`/api/bookings/owner/me?${params}`, {
        credentials: 'include',
        headers: getAuthHeaders()
      });
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        if (response.status === 403) {
          throw new Error('You are not authorized to view bookings');
        }
        throw new Error('Failed to fetch bookings');
      }
      const result = await response.json();
      return {
        bookings: result.data || result,
        total: result.total || (result.data || result).length,
        hasMore: (result.data || result).length === BOOKINGS_PER_PAGE
      };
    },
    enabled: !!user && user.role === 'owner' && auth.canAccess && selectedTab === 'bookings',
    placeholderData: (previousData: any) => previousData
  });

  // Update booking status mutation
  const updateBookingStatus = useMutation({
    mutationFn: async ({ bookingId, status }: { bookingId: number; status: string }) => {
      if (!auth.canManage) {
        throw new Error('You are not authorized to manage bookings');
      }
      
      updateBookingOptimistically(bookingId, status, () => {
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
      });
      
      const response = await fetch(`/api/bookings/${bookingId}/status`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        credentials: 'include',
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to update this booking');
        }
        throw new Error('Failed to update booking status');
      }
      return response.json();
    },
    onSuccess: (_, { bookingId, status }) => {
      setActionInProgress(null);
      setRecentlyUpdated(prev => new Set(Array.from(prev).concat(bookingId)));
      
      setTimeout(() => {
        setRecentlyUpdated(prev => {
          const newSet = new Set(prev);
          newSet.delete(bookingId);
          return newSet;
        });
      }, 3000);
      
      queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
      queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
    },
    onError: (error: any) => {
      setActionInProgress(null);
      if (error.message?.includes('authorized')) {
        handleAuthError(error, 'update this booking');
      } else {
        showErrorToast('❌ Update Failed', 'Unable to update booking status. Please try again.');
        playNotificationSound('error');
      }
    }
  });

  // All useEffect hooks
  useEffect(() => {
    setBookingPage(1);
  }, [bookingStatusFilter]);

  useEffect(() => {
    setPropertyPage(1);
  }, [propertySearch]);

  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          toast({
            title: "Notifications Enabled",
            description: "You'll receive alerts for new booking requests.",
            duration: 3000,
          });
        }
      });
    }
  }, [toast]);

  // Comprehensive cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clean up all sound timeouts to prevent memory leaks
      cleanupSoundTimeouts();
      console.log('OwnerDashboard: Component cleanup completed');
    };
  }, [cleanupSoundTimeouts]);

  // Redirect if not logged in or not an owner
  useEffect(() => {
    try {
      if (!user) {
        navigate('/login?redirect=/owner/dashboard');
        return;
      }
      if (user.role !== 'owner') {
        navigate('/');
        return;
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to home page if navigation fails
      window.location.href = '/';
    }
  }, [user, navigate]);

  // Show loading state while authenticating
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Don't render dashboard if user is not authenticated or not an owner
  if (!user || user.role !== 'owner') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking access...</p>
        </div>
      </div>
    );
  }

  // Extract data for UI rendering
  const properties = (propertiesData as any)?.properties || [];
  const bookings = (bookingsData as any)?.bookings || [];

  // Helper function for property actions
  const handleDeleteProperty = (propertyId: number, propertyTitle: string) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Property',
      description: `Are you sure you want to permanently delete "${propertyTitle}"? This action cannot be undone and will remove the property from all bookings and search results.`,
      actionLabel: 'Delete Property',
      variant: 'destructive',
      action: () => {
        setPropertyActionInProgress(propertyId);
        deleteProperty.mutate(propertyId);
        setConfirmationDialog(prev => ({ ...prev, isOpen: false }));
      }
    });
  };

  // Extract summary data from dashboard API or fallback to computed values
  const totalProperties = dashboardSummary?.totalProperties ?? properties?.length ?? 0;
  const totalBookings = dashboardSummary?.totalBookings ?? bookings?.length ?? 0;
  const confirmedBookings = dashboardSummary?.confirmedBookings ?? bookings?.filter((b: any) => b.status === 'confirmed').length ?? 0;
  const pendingBookings = dashboardSummary?.pendingBookings ?? bookings?.filter((b: any) => b.status === 'pending').length ?? 0;
  const totalRevenue = dashboardSummary?.totalRevenue ?? bookings?.filter((b: any) => b.status === 'confirmed').reduce((sum: number, b: any) => sum + b.totalPrice, 0) ?? 0;

  // Helper functions
  const formatPrice = (price: number) => `₹${price.toLocaleString('en-IN')}`;
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-300';
      case 'cancelled': return 'bg-gray-100 text-gray-800 border-gray-300';
      default: return 'bg-blue-100 text-blue-800 border-blue-300';
    }
  };

  return (
    <Guards.Owner>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-4">
          {/* Header with Add Property button */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Owner Dashboard</h1>
              <p className="text-gray-600 mt-2">Welcome back, {(user as any)?.profile?.firstName || user?.email || 'User'}!</p>
            </div>
            <Button
              onClick={() => navigate('/properties/create')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <PlusCircle className="h-5 w-5 mr-2" />
              Add New Property
            </Button>
          </div>
          
          {/* Dashboard Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Properties</p>
                  <p className="text-2xl font-bold text-gray-900">{totalProperties}</p>
                </div>
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Bookings</p>
                  <p className="text-2xl font-bold text-gray-900">{totalBookings}</p>
                </div>
                <Calendar className="h-8 w-8 text-green-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{pendingBookings}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatPrice(totalRevenue)}</p>
                </div>
                <IndianRupee className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <nav className="flex space-x-8 border-b border-gray-200 mb-6">
            <button
              onClick={() => setSelectedTab('properties')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                selectedTab === 'properties'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Properties
            </button>
            
            <button
              onClick={() => setSelectedTab('pricing')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                selectedTab === 'pricing'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Pricing
            </button>
            
            <button
              onClick={() => setSelectedTab('bookings')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                selectedTab === 'bookings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Calendar className="h-4 w-4" />
              Bookings
              {pendingBookings > 0 && (
                <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-0.5 rounded-full">
                  {pendingBookings}
                </span>
              )}
            </button>
            
            <button
              onClick={() => setSelectedTab('calendar')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                selectedTab === 'calendar'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Calendar className="h-4 w-4" />
              Calendar
            </button>
            
            <button
              onClick={() => setSelectedTab('analytics')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                selectedTab === 'analytics'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Analytics
            </button>
            
            <button
              onClick={() => setSelectedTab('media')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                selectedTab === 'media'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Camera className="h-4 w-4" />
              Media Management
            </button>
            
            <button
              onClick={() => setSelectedTab('communications')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                selectedTab === 'communications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <MessageSquare className="h-4 w-4" />
              Communications
              {FEATURE_FLAGS.ENHANCEMENTS.GUEST_MESSAGING && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                  3
                </span>
              )}
            </button>
            
            <button
              onClick={() => setSelectedTab('settings')}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                selectedTab === 'settings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <ShieldCheck className="h-4 w-4" />
              Settings
            </button>
          </nav>

          {/* Properties Tab */}
          {selectedTab === 'properties' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">My Properties</h2>
                    <div className="flex items-center space-x-4">
                      <Input
                        type="text"
                        placeholder="Search properties..."
                        value={propertySearch}
                        onChange={(e) => setPropertySearch(e.target.value)}
                        className="w-64"
                      />
                      <Button
                        variant="outline"
                        onClick={() => navigate('/properties/create')}
                      >
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Add Property
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  {propertiesLoading ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {[...Array(3)].map((_, i) => (
                        <Skeleton key={i} className="h-64" />
                      ))}
                    </div>
                  ) : properties.length === 0 ? (
                    <div className="text-center py-12">
                      <Building2 className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No properties</h3>
                      <p className="mt-1 text-sm text-gray-500">Get started by adding a new property.</p>
                      <div className="mt-6">
                        <Button onClick={() => navigate('/properties/create')}>
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Add Your First Property
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {properties.map((property: any) => (
                          <div key={property.id} className="bg-white rounded-lg border overflow-hidden hover:shadow-lg transition-shadow">
                            <div className="aspect-w-16 aspect-h-12 bg-gray-200">
                              {property.images && property.images[0] ? (
                                <LazyImage
                                  src={getThumbnailUrl(property.images[0])}
                                  alt={property.title}
                                  className="w-full h-48 object-cover"
                                />
                              ) : (
                                <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                                  <Camera className="h-12 w-12 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div className="p-4">
                              <h3 className="text-lg font-semibold text-gray-900">{property.title}</h3>
                              <p className="text-sm text-gray-600 flex items-center mt-1">
                                <MapPin className="h-4 w-4 mr-1" />
                                {property.location}
                              </p>
                              <div className="mt-4 flex items-center justify-between">
                                <div>
                                  <p className="text-sm text-gray-500">Starting from</p>
                                  <p className="text-lg font-bold text-gray-900">
                                    {formatPrice(property.halfDayPrice || property.fullDayPrice)}
                                  </p>
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => navigate(`/properties/${property.id}/edit`)}
                                  >
                                    <Edit3 className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeleteProperty(property.id, property.title)}
                                    disabled={propertyActionInProgress === property.id}
                                  >
                                    {propertyActionInProgress === property.id ? (
                                      <Skeleton className="h-4 w-4" />
                                    ) : (
                                      <Trash2 className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      {/* Pagination */}
                      {((propertiesData as any)?.hasMore || propertyPage > 1) && (
                        <div className="flex items-center justify-between mt-6 pt-4 border-t">
                          <div className="text-sm text-gray-600">
                            Showing page {propertyPage} {(propertiesData as any)?.total && `of ${Math.ceil((propertiesData as any).total / PROPERTIES_PER_PAGE)} pages`}
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setPropertyPage(prev => Math.max(1, prev - 1))}
                              disabled={propertyPage <= 1 || propertiesLoading}
                            >
                              Previous
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setPropertyPage(prev => prev + 1)}
                              disabled={!(propertiesData as any)?.hasMore || propertiesLoading}
                            >
                              Next
                            </Button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Pricing Tab */}
          {selectedTab === 'pricing' && (
            <PricingManagementTab
              properties={properties || []}
              isLoading={propertiesLoading}
              onPricingUpdate={() => {
                queryClient.invalidateQueries({ queryKey: ['/api/properties/owner'] });
              }}
            />
          )}

          {/* Bookings Tab */}
          {selectedTab === 'bookings' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">Booking Requests</h2>
                    <Select value={bookingStatusFilter} onValueChange={setBookingStatusFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectItem value="all">All Bookings</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="confirmed">Confirmed</SelectItem>
                          <SelectItem value="rejected">Rejected</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="p-6">
                  {bookingsLoading ? (
                    <div className="space-y-4">
                      {[...Array(3)].map((_, i) => (
                        <Skeleton key={i} className="h-32" />
                      ))}
                    </div>
                  ) : bookings.length === 0 ? (
                    <div className="text-center py-12">
                      <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
                      <p className="mt-1 text-sm text-gray-500">Your booking requests will appear here.</p>
                    </div>
                  ) : (
                    <>
                      <div className="space-y-4">
                        {bookings.map((booking: any) => (
                          <div
                            key={booking.id}
                            className={`border rounded-lg p-6 ${
                              recentlyUpdated.has(booking.id) ? 'border-green-300 bg-green-50' : 'border-gray-200'
                            } transition-all duration-300`}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <h3 className="text-lg font-semibold text-gray-900">
                                      {booking.property.title}
                                    </h3>
                                    <p className="text-sm text-gray-600 flex items-center mt-1">
                                      <MapPin className="h-4 w-4 mr-1" />
                                      {booking.property.location}
                                    </p>
                                  </div>
                                  <Badge className={getStatusColor(booking.status)}>
                                    {booking.status}
                                  </Badge>
                                </div>
                                
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                                  <div>
                                    <p className="text-sm text-gray-500">Guest</p>
                                    <p className="font-medium text-gray-900">{booking.guest.fullName}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Date</p>
                                    <p className="font-medium text-gray-900">
                                      {format(new Date(booking.bookingDate), 'MMM dd, yyyy')}
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Type</p>
                                    <p className="font-medium text-gray-900">
                                      {booking.bookingType === 'full_day' ? 'Full Day' : 'Morning Only'}
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Amount</p>
                                    <p className="font-medium text-gray-900">{formatPrice(booking.totalPrice)}</p>
                                  </div>
                                </div>
                                
                                {booking.specialRequests && (
                                  <div className="mt-4">
                                    <p className="text-sm text-gray-500">Special Requests</p>
                                    <p className="text-sm text-gray-700 mt-1">{booking.specialRequests}</p>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-between mt-6 pt-4 border-t">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowBookingDetails(booking)}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                              
                              {booking.status === 'pending' && (
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => updateBookingStatus.mutate({ bookingId: booking.id, status: 'rejected' })}
                                    disabled={actionInProgress === booking.id}
                                  >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Reject
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() => updateBookingStatus.mutate({ bookingId: booking.id, status: 'confirmed' })}
                                    disabled={actionInProgress === booking.id}
                                  >
                                    {actionInProgress === booking.id ? (
                                      <>
                                        <Skeleton className="h-4 w-4 mr-2" />
                                        Processing...
                                      </>
                                    ) : (
                                      <>
                                        <CheckCircle className="h-4 w-4 mr-2" />
                                        Confirm
                                      </>
                                    )}
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      {/* Pagination */}
                      {((bookingsData as any)?.hasMore || bookingPage > 1) && (
                        <div className="flex items-center justify-between mt-6 pt-4 border-t">
                          <div className="text-sm text-gray-600">
                            Showing page {bookingPage} {(bookingsData as any)?.total && `of ${Math.ceil((bookingsData as any).total / BOOKINGS_PER_PAGE)} pages`}
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setBookingPage(prev => Math.max(1, prev - 1))}
                              disabled={bookingPage <= 1 || bookingsLoading}
                            >
                              Previous
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setBookingPage(prev => prev + 1)}
                              disabled={!(bookingsData as any)?.hasMore || bookingsLoading}
                            >
                              Next
                            </Button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Analytics Tab */}
          {selectedTab === 'analytics' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Analytics & Insights</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="text-center p-6 bg-blue-50 rounded-lg">
                      <Star className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-gray-900">{confirmedBookings}</p>
                      <p className="text-sm text-gray-600">Confirmed Bookings</p>
                    </div>
                    <div className="text-center p-6 bg-green-50 rounded-lg">
                      <ThumbsUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-gray-900">{totalRevenue > 0 ? '95%' : '0%'}</p>
                      <p className="text-sm text-gray-600">Guest Satisfaction</p>
                    </div>
                    <div className="text-center p-6 bg-purple-50 rounded-lg">
                      <IndianRupee className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-gray-900">{totalRevenue > 0 ? formatPrice(totalRevenue / totalProperties) : '₹0'}</p>
                      <p className="text-sm text-gray-600">Avg. Revenue per Property</p>
                    </div>
                  </div>
                  
                  {/* Future Analytics Preview */}
                  {!FEATURE_FLAGS.ENHANCEMENTS.ADVANCED_ANALYTICS && (
                    <div className="mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Advanced Analytics Coming Soon</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-start space-x-3">
                          <BarChart3 className="h-5 w-5 text-gray-400 mt-1" />
                          <div>
                            <p className="font-medium text-gray-700">Property Performance Insights</p>
                            <p className="text-sm text-gray-500">Track views, engagement, and conversion rates</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <Eye className="h-5 w-5 text-gray-400 mt-1" />
                          <div>
                            <p className="font-medium text-gray-700">Revenue Forecasting</p>
                            <p className="text-sm text-gray-500">AI-powered predictions for future bookings</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Media Management Tab */}
          {selectedTab === 'media' && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Media Management</h2>
                <p className="text-sm text-gray-600 mt-1">Upload and manage property images and videos</p>
              </div>
              <div className="p-6">
                <MediaManagementSection
                  properties={properties || []}
                  onMediaUpdate={() => {
                    queryClient.invalidateQueries({ queryKey: ['/api/properties/owner'] });
                  }}
                />
              </div>
            </div>
          )}

          {/* Communications Tab */}
          {selectedTab === 'communications' && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Guest Communications</h2>
                    <p className="text-sm text-gray-600 mt-1">Manage conversations with your guests</p>
                  </div>
                  {FEATURE_FLAGS.ENHANCEMENTS.GUEST_MESSAGING && (
                    <Button size="sm" disabled>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      New Message
                    </Button>
                  )}
                </div>
              </div>
              <div className="p-6">
                {!FEATURE_FLAGS.ENHANCEMENTS.GUEST_MESSAGING ? (
                  <div className="text-center py-12">
                    <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Communications Coming Soon</h3>
                    <p className="mt-1 text-sm text-gray-500">Guest messaging will be available in a future update.</p>
                    
                    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                      <div className="border rounded-lg p-4 text-left">
                        <RotateCcw className="h-5 w-5 text-gray-400 mb-2" />
                        <h4 className="font-medium text-gray-900">Automated Responses</h4>
                        <p className="text-sm text-gray-500 mt-1">Set up automated messages for check-in instructions and common questions.</p>
                      </div>
                      <div className="border rounded-lg p-4 text-left">
                        <MessageSquare className="h-5 w-5 text-gray-400 mb-2" />
                        <h4 className="font-medium text-gray-900">Message Templates</h4>
                        <p className="text-sm text-gray-500 mt-1">Save and reuse common responses to streamline communications.</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Guest messaging interface would go here */}
                    <p className="text-gray-600">Guest messaging interface is being developed.</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Calendar Tab */}
          {selectedTab === 'calendar' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Property Calendar Management</h2>
                  <p className="text-sm text-gray-600 mt-1">Manage bookings across all your properties with an interactive calendar</p>
                </div>
                <div className="p-6">
                  {properties && properties.length > 0 ? (
                    <div className="space-y-6">
                      {/* Property Selector */}
                      <div className="flex items-center gap-4">
                        <label htmlFor="calendar-property" className="text-sm font-medium text-gray-700">
                          Select Property:
                        </label>
                        <select 
                          id="calendar-property"
                          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue={properties[0]?.id}
                        >
                          {properties.map((property: any) => (
                            <option key={property.id} value={property.id}>
                              {property.title} - {property.location}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      {/* FullCalendar Component */}
                      <PropertyFullCalendar 
                        propertyId={properties[0]?.id?.toString() || '1'} 
                        className="mt-6"
                      />
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No Properties Found</h3>
                      <p className="mt-1 text-sm text-gray-500">Add a property to start managing your calendar.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {selectedTab === 'settings' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Dashboard Settings</h2>
                  <p className="text-sm text-gray-600 mt-1">Manage your dashboard preferences and features</p>
                </div>
                <div className="p-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-md font-medium text-gray-900 mb-4">Notification Preferences</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-700">New Booking Notifications</p>
                            <p className="text-sm text-gray-500">Get notified when you receive new booking requests</p>
                          </div>
                          <Switch checked={true} />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-700">Email Notifications</p>
                            <p className="text-sm text-gray-500">Receive booking updates via email</p>
                          </div>
                          <Switch checked={true} />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-700">Real-time Updates</p>
                            <p className="text-sm text-gray-500">Enable live dashboard updates</p>
                          </div>
                          <Switch checked={FEATURE_FLAGS.REALTIME.ENABLED} />
                        </div>
                      </div>
                    </div>
                    
                    <div className="pt-6 border-t">
                      <h3 className="text-md font-medium text-gray-900 mb-4">Feature Flags</h3>
                      <div className="space-y-4">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium text-gray-700">Advanced Analytics</p>
                            <Badge variant={FEATURE_FLAGS.ENHANCEMENTS.ADVANCED_ANALYTICS ? "default" : "secondary"}>
                              {FEATURE_FLAGS.ENHANCEMENTS.ADVANCED_ANALYTICS ? "Enabled" : "Coming Soon"}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-500">Detailed property performance insights and revenue forecasting</p>
                        </div>
                        
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium text-gray-700">Guest Messaging</p>
                            <Badge variant={FEATURE_FLAGS.ENHANCEMENTS.GUEST_MESSAGING ? "default" : "secondary"}>
                              {FEATURE_FLAGS.ENHANCEMENTS.GUEST_MESSAGING ? "Enabled" : "Coming Soon"}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-500">Direct communication with guests through the dashboard</p>
                        </div>
                        
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium text-gray-700">WebSocket Connection</p>
                            <Badge variant={isConnected ? "default" : "secondary"}>
                              {isConnected ? "Connected" : "Disconnected"}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-500">Real-time updates for bookings and notifications</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Booking Details Modal */}
          <Dialog open={!!showBookingDetails} onOpenChange={() => setShowBookingDetails(null)}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Booking Details</DialogTitle>
                <DialogDescription>
                  Complete information about this booking request
                </DialogDescription>
              </DialogHeader>
              {showBookingDetails && (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900">Property</h4>
                    <p className="text-sm text-gray-600">{showBookingDetails.property.title}</p>
                    <p className="text-sm text-gray-600 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {showBookingDetails.property.location}
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900">Guest Information</h4>
                    <p className="text-sm text-gray-600">{showBookingDetails.guest.fullName}</p>
                    <p className="text-sm text-gray-600 flex items-center">
                      <Mail className="h-4 w-4 mr-1" />
                      {showBookingDetails.guest.email}
                    </p>
                    {showBookingDetails.guest.phone && (
                      <p className="text-sm text-gray-600 flex items-center">
                        <Phone className="h-4 w-4 mr-1" />
                        {showBookingDetails.guest.phone}
                      </p>
                    )}
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900">Booking Details</h4>
                    <p className="text-sm text-gray-600 flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {format(new Date(showBookingDetails.bookingDate), 'MMMM dd, yyyy')}
                    </p>
                    <p className="text-sm text-gray-600 flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {showBookingDetails.bookingType === 'full_day' ? 'Full Day' : 'Morning Only'}
                    </p>
                    <p className="text-sm text-gray-600 flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {showBookingDetails.guests} guests
                    </p>
                    <p className="text-sm text-gray-600 flex items-center">
                      <IndianRupee className="h-4 w-4 mr-1" />
                      {formatPrice(showBookingDetails.totalPrice)}
                    </p>
                  </div>
                  
                  {showBookingDetails.specialRequests && (
                    <div>
                      <h4 className="font-semibold text-gray-900">Special Requests</h4>
                      <p className="text-sm text-gray-600">{showBookingDetails.specialRequests}</p>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between pt-4">
                    <Badge className={getStatusColor(showBookingDetails.status)}>
                      {showBookingDetails.status}
                    </Badge>
                    <p className="text-xs text-gray-500">
                      Booked {format(new Date(showBookingDetails.createdAt), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Action Confirmation Dialog */}
          <Dialog open={confirmationDialog.isOpen} onOpenChange={(open) => 
            setConfirmationDialog(prev => ({ ...prev, isOpen: open }))
          }>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {confirmationDialog.variant === 'destructive' ? (
                    <ShieldX className="h-5 w-5 text-red-600" />
                  ) : (
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                  )}
                  {confirmationDialog.title}
                </DialogTitle>
                <DialogDescription className="text-left">
                  {confirmationDialog.description}
                </DialogDescription>
              </DialogHeader>
              
              <div className="flex gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setConfirmationDialog(prev => ({ ...prev, isOpen: false }))}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant={confirmationDialog.variant}
                  onClick={confirmationDialog.action}
                  className="flex-1"
                >
                  {confirmationDialog.actionLabel}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </Guards.Owner>
  );
}

