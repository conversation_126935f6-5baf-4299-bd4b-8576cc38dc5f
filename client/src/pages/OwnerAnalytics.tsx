import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Property, Booking } from "@shared/schema";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from "recharts";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isWithinInterval } from "date-fns";

// Components
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronDown, Download, Calendar, BarChart2, <PERSON><PERSON><PERSON> as Pie<PERSON>hart<PERSON><PERSON>, TrendingUp } from "lucide-react";

const COLORS = ['#4A6741', '#605045', '#EBE6E1', '#F7F4F1', '#D1BEA8'];

export default function OwnerAnalytics() {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [dateRange, setDateRange] = useState<string>("last30days");
  const [activeTab, setActiveTab] = useState<string>("overview");

  // Fetch owner properties
  const { data: properties, isLoading: propertiesLoading } = useQuery<Property[]>({
    queryKey: ["/api/properties/owner/me"],
    queryFn: async () => {
      const response = await fetch("/api/properties/owner/me", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      if (!response.ok) throw new Error("Failed to fetch properties");
      return response.json();
    },
    enabled: isAuthenticated && user?.role === "owner",
  });

  // Fetch bookings for owner's properties
  const { data: bookings, isLoading: bookingsLoading } = useQuery<(Booking & { property: Property })[]>({
    queryKey: ["/api/bookings/owner/me"],
    queryFn: async () => {
      const response = await fetch("/api/bookings/owner/me", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      if (!response.ok) throw new Error("Failed to fetch bookings");
      return response.json();
    },
    enabled: isAuthenticated && user?.role === "owner",
  });

  // Filter bookings based on selected property and date range
  const filteredBookings = bookings ? bookings.filter(booking => {
    // Filter by property
    if (selectedProperty !== "all" && booking.propertyId !== parseInt(selectedProperty)) {
      return false;
    }

    // Filter by date range
    const bookingDate = new Date(booking.bookingDate);
    const today = new Date();
    
    if (dateRange === "last30days") {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(today.getDate() - 30);
      return bookingDate >= thirtyDaysAgo && bookingDate <= today;
    } else if (dateRange === "last90days") {
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(today.getDate() - 90);
      return bookingDate >= ninetyDaysAgo && bookingDate <= today;
    } else if (dateRange === "thisYear") {
      return bookingDate.getFullYear() === today.getFullYear();
    } else if (dateRange === "lastYear") {
      return bookingDate.getFullYear() === today.getFullYear() - 1;
    }
    
    return true;
  }) : [];

  // Calculate revenue and other metrics
  const totalRevenue = filteredBookings?.reduce((sum, booking) => sum + booking.totalPrice, 0) || 0;
  const totalBookings = filteredBookings?.length || 0;
  const averageRevenue = totalBookings > 0 ? totalRevenue / totalBookings : 0;
  
  // Prepare booking data for monthly chart
  const prepareMonthlyData = () => {
    if (!filteredBookings?.length) return [];
    
    // Group bookings by month
    const monthlyData: Record<string, { month: string, revenue: number, bookings: number }> = {};
    
    filteredBookings.forEach(booking => {
      const date = new Date(booking.bookingDate);
      const monthKey = format(date, 'yyyy-MM');
      const monthLabel = format(date, 'MMM yyyy');
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { month: monthLabel, revenue: 0, bookings: 0 };
      }
      
      monthlyData[monthKey].revenue += booking.totalPrice;
      monthlyData[monthKey].bookings += 1;
    });
    
    // Convert to array and sort by month
    return Object.values(monthlyData).sort((a, b) => {
      return a.month.localeCompare(b.month);
    });
  };

  // Prepare booking type distribution data for pie chart
  const prepareBookingTypeData = () => {
    if (!filteredBookings?.length) return [];
    
    const typeCounts: Record<string, number> = {
      morning: 0,
      full_day: 0,
    };
    
    filteredBookings.forEach(booking => {
      typeCounts[booking.bookingType] = (typeCounts[booking.bookingType] || 0) + 1;
    });
    
    return [
      { name: 'Morning', value: typeCounts.morning },
      { name: 'Full Day', value: typeCounts.full_day },
    ].filter(item => item.value > 0);
  };

  // Prepare property distribution data for pie chart
  const preparePropertyData = () => {
    if (!filteredBookings?.length || !properties?.length) return [];
    
    const propertyCounts: Record<number, { name: string, value: number }> = {};
    
    filteredBookings.forEach(booking => {
      if (!propertyCounts[booking.propertyId]) {
        const property = properties.find(p => p.id === booking.propertyId);
        propertyCounts[booking.propertyId] = { 
          name: property ? property.title : `Property ${booking.propertyId}`,
          value: 0 
        };
      }
      propertyCounts[booking.propertyId].value += 1;
    });
    
    return Object.values(propertyCounts);
  };

  // Prepare occupancy rate data
  const prepareOccupancyRateData = () => {
    if (!filteredBookings?.length || !properties?.length) return [];
    
    const result = [];
    
    for (const property of properties) {
      if (selectedProperty !== "all" && property.id !== parseInt(selectedProperty)) {
        continue;
      }
      
      // Get all days in the date range
      const today = new Date();
      let startDate, endDate;
      
      if (dateRange === "last30days") {
        startDate = new Date();
        startDate.setDate(today.getDate() - 30);
        endDate = today;
      } else if (dateRange === "last90days") {
        startDate = new Date();
        startDate.setDate(today.getDate() - 90);
        endDate = today;
      } else if (dateRange === "thisYear") {
        startDate = new Date(today.getFullYear(), 0, 1);
        endDate = today;
      } else if (dateRange === "lastYear") {
        startDate = new Date(today.getFullYear() - 1, 0, 1);
        endDate = new Date(today.getFullYear() - 1, 11, 31);
      } else {
        startDate = startOfMonth(today);
        endDate = endOfMonth(today);
      }
      
      const allDays = eachDayOfInterval({ start: startDate, end: endDate });
      
      // Count bookings for this property in the date range
      const propertyBookings = filteredBookings.filter(
        booking => booking.propertyId === property.id
      );
      
      // Count booked days
      let bookedDays = 0;
      for (const day of allDays) {
        const isBooked = propertyBookings.some(booking => {
          const bookingDate = new Date(booking.bookingDate);
          return format(day, 'yyyy-MM-dd') === format(bookingDate, 'yyyy-MM-dd');
        });
        if (isBooked) bookedDays++;
      }
      
      const occupancyRate = allDays.length > 0 ? (bookedDays / allDays.length) * 100 : 0;
      
      result.push({
        name: property.title,
        occupancyRate: Math.round(occupancyRate * 100) / 100, // Round to 2 decimal places
      });
    }
    
    return result;
  };

  // Handle export to CSV
  const exportToCSV = () => {
    if (!filteredBookings?.length) {
      toast({
        title: "No data to export",
        description: "There are no bookings matching your current filters to export.",
        variant: "destructive",
      });
      return;
    }
    
    // Prepare CSV content
    const headers = ["Date", "Property", "Type", "Customer", "Price", "Status"];
    const rows = filteredBookings.map(booking => [
      booking.bookingDate,
      booking.property.title,
      booking.bookingType === "morning" ? "Morning" : "Full Day",
      `User ${booking.userId}`,
      `₹${booking.totalPrice.toFixed(2)}`,
      booking.status,
    ]);
    
    const csvContent = [
      headers.join(","),
      ...rows.map(row => row.join(","))
    ].join("\n");
    
    // Create and download the CSV file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `bookings-export-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Export successful",
      description: "Your booking data has been exported to CSV.",
    });
  };

  // Loading state
  if (propertiesLoading || bookingsLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <Skeleton className="h-10 w-3/4 mb-4" />
          <Skeleton className="h-6 w-1/2" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[1, 2, 3, 4].map((_, i) => (
            <Skeleton key={i} className="h-40" />
          ))}
        </div>
        <Skeleton className="h-80 w-full" />
      </div>
    );
  }

  if (!isAuthenticated || user?.role !== "owner") {
    return (
      <div className="container mx-auto py-16 px-4 text-center">
        <h1 className="text-3xl font-bold text-[#2D3C2D] mb-4">Owner Analytics</h1>
        <p className="text-[#605045] mb-8">You must be logged in as an owner to access analytics.</p>
        <Button className="bg-[#4A6741] hover:bg-[#3A5131]" onClick={() => window.location.href = "/login"}>
          Login as Owner
        </Button>
      </div>
    );
  }

  const monthlyData = prepareMonthlyData();
  const bookingTypeData = prepareBookingTypeData();
  const propertyData = preparePropertyData();
  const occupancyData = prepareOccupancyRateData();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-[#2D3C2D]">Property Analytics</h1>
          <p className="text-[#605045]">Monitor your property performance and bookings</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Select Property" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              {properties?.map((property) => (
                <SelectItem key={property.id} value={property.id.toString()}>
                  {property.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Date Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last30days">Last 30 Days</SelectItem>
              <SelectItem value="last90days">Last 90 Days</SelectItem>
              <SelectItem value="thisYear">This Year</SelectItem>
              <SelectItem value="lastYear">Last Year</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="icon" onClick={exportToCSV}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl text-[#2D3C2D]">Total Revenue</CardTitle>
            <CardDescription>All properties</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#4A6741]">₹{totalRevenue.toFixed(2)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl text-[#2D3C2D]">Total Bookings</CardTitle>
            <CardDescription>All properties</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#4A6741]">{totalBookings}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl text-[#2D3C2D]">Average Revenue</CardTitle>
            <CardDescription>Per booking</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#4A6741]">₹{averageRevenue.toFixed(2)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl text-[#2D3C2D]">Properties</CardTitle>
            <CardDescription>Total listings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-[#4A6741]">{properties?.length || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart2 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="occupancy" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>Occupancy</span>
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <PieChartIcon className="h-4 w-4" />
            <span>Distribution</span>
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            <span>Trends</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Revenue and Bookings</CardTitle>
              <CardDescription>
                Overview of your revenue and booking trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              {monthlyData.length > 0 ? (
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={monthlyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 50 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="month" 
                        angle={-45} 
                        textAnchor="end" 
                        height={70}
                      />
                      <YAxis yAxisId="left" orientation="left" stroke="#4A6741" />
                      <YAxis yAxisId="right" orientation="right" stroke="#605045" />
                      <Tooltip cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }} />
                      <Legend />
                      <Bar 
                        yAxisId="left"
                        dataKey="revenue" 
                        name="Revenue (₹)" 
                        fill="#4A6741" 
                        radius={[4, 4, 0, 0]}
                      />
                      <Bar 
                        yAxisId="right"
                        dataKey="bookings" 
                        name="Bookings" 
                        fill="#605045" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-12 text-[#605045]">
                  <p>No booking data available for the selected filters.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Occupancy Tab */}
        <TabsContent value="occupancy" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Property Occupancy Rates</CardTitle>
              <CardDescription>
                Occupancy percentage for each property
              </CardDescription>
            </CardHeader>
            <CardContent>
              {occupancyData.length > 0 ? (
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={occupancyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 50 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" domain={[0, 100]} />
                      <YAxis 
                        dataKey="name" 
                        type="category" 
                        width={150}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }} />
                      <Bar 
                        dataKey="occupancyRate" 
                        name="Occupancy Rate (%)" 
                        fill="#4A6741" 
                        radius={[0, 4, 4, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-12 text-[#605045]">
                  <p>No occupancy data available for the selected filters.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Distribution Tab */}
        <TabsContent value="distribution" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Booking Type Distribution</CardTitle>
                <CardDescription>
                  Morning vs Full-day booking distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                {bookingTypeData.length > 0 ? (
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={bookingTypeData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {bookingTypeData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} bookings`, 'Count']} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-center py-12 text-[#605045]">
                    <p>No booking type data available.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Property Bookings Distribution</CardTitle>
                <CardDescription>
                  Bookings per property
                </CardDescription>
              </CardHeader>
              <CardContent>
                {propertyData.length > 0 ? (
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={propertyData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => {
                            const shortName = name.length > 15 ? `${name.substring(0, 15)}...` : name;
                            return `${shortName}: ${(percent * 100).toFixed(0)}%`;
                          }}
                        >
                          {propertyData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value, name) => [`${value} bookings`, name]} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-center py-12 text-[#605045]">
                    <p>No property distribution data available.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Booking Trends</CardTitle>
              <CardDescription>
                Visualize changes in booking patterns over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              {monthlyData.length > 0 ? (
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={monthlyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 50 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="month" 
                        angle={-45} 
                        textAnchor="end" 
                        height={70}
                      />
                      <YAxis />
                      <Tooltip cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }} />
                      <Legend />
                      <Bar 
                        dataKey="bookings" 
                        name="Bookings" 
                        fill="#605045" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-12 text-[#605045]">
                  <p>No trend data available for the selected filters.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}