import { useState } from "react";
import { useLocation, useSearch } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/contexts/AuthContext";
import { ArrowLeft, Smartphone } from "lucide-react";

const phoneSchema = z.object({
  phone: z.string().length(10, "Phone number must be exactly 10 digits").regex(/^[6-9]\d{9}$/, "Invalid phone number format"),
});

type PhoneFormValues = z.infer<typeof phoneSchema>;

export default function Login() {
  const { login } = useAuth();
  const [_, navigate] = useLocation();
  const search = useSearch();
  const { toast } = useToast();
  const redirectPath = new URLSearchParams(search).get("redirect") || "/";
  
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpValue, setOtpValue] = useState('');

  const phoneForm = useForm<PhoneFormValues>({
    resolver: zodResolver(phoneSchema),
    defaultValues: {
      phone: "",
    },
  });

  // Send OTP mutation
  const sendOTPMutation = useMutation({
    mutationFn: async (data: { identifier: string; type: 'sms' }) => {
      const response = await apiRequest("POST", "/api/auth/otp/send-otp", data);
      return response.json();
    },
    onSuccess: (data) => {
      setStep('otp');
      // Try multiple possible response structures
      const identifier = data.identifier || data.data?.identifier;
      setPhoneNumber(identifier);
      setOtpValue(''); // Clear OTP state
      toast({
        title: "OTP Sent",
        description: "Verification code sent to your phone"
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Verify OTP mutation for login
  const verifyOTPMutation = useMutation({
    mutationFn: async (data: { identifier: string; code: string; type: 'sms' }) => {
      if (!data.identifier) {
        const errorMsg = 'No identifier in data!';
        throw new Error(errorMsg);
      }
      
      const response = await apiRequest("POST", "/api/auth/otp/verify-otp-login", data);
      return response.json();
    },
    onSuccess: (data) => {
      login(data.user, data.token);
      toast({
        title: "Login successful",
        description: "Welcome back!"
      });
      navigate(redirectPath);
    },
    onError: (error: Error) => {
      toast({
        title: "Login failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const onPhoneSubmit = (data: PhoneFormValues) => {
    const formattedPhone = '+91' + data.phone.trim();
    
    // Store the phone number immediately as backup
    setPhoneNumber(formattedPhone);
    
    sendOTPMutation.mutate({
      identifier: formattedPhone,
      type: 'sms'
    });
  };

  const onOTPSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (otpValue.length === 6) {
      // Additional safety check
      if (!phoneNumber) {
        toast({
          title: "Error",
          description: "Phone number missing. Please go back and try again.",
          variant: "destructive"
        });
        return;
      }
      
      const payload = {
        identifier: phoneNumber,
        code: otpValue,
        type: 'sms' as const
      };
      
      verifyOTPMutation.mutate(payload);
    }
  };

  const goBackToPhone = () => {
    setStep('phone');
    setPhoneNumber('');
    setOtpValue('');
  };

  if (step === 'phone') {
    return (
      <div className="min-h-[80vh] flex items-center justify-center bg-[#F7F4F1] py-12">
        <div className="w-full max-w-md px-4">
          <Card className="shadow-lg">
            <CardHeader className="space-y-1">
              <CardTitle className="font-heading text-2xl font-bold text-[#2D3C2D] text-center">Welcome back</CardTitle>
              <CardDescription className="text-center text-[#605045]">
                Enter your phone number to sign in
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...phoneForm}>
                <form onSubmit={phoneForm.handleSubmit(onPhoneSubmit)} className="space-y-4">
                  <FormField
                    control={phoneForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#2D3C2D] flex items-center gap-2">
                          <Smartphone className="h-4 w-4" />
                          Phone Number
                        </FormLabel>
                        <FormControl>
                          <div className="flex">
                            <div className="flex items-center px-3 bg-gray-50 border border-r-0 border-[#D5CCC4] rounded-l-md">
                              <span className="text-[#2D3C2D] font-medium">+91</span>
                            </div>
                            <Input
                              type="tel"
                              inputMode="numeric"
                              pattern="[0-9]*"
                              maxLength={10}
                              placeholder="9876543210"
                              {...field}
                              className="rounded-l-none border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white"
                    disabled={sendOTPMutation.isPending}
                  >
                    {sendOTPMutation.isPending ? "Sending OTP..." : "Send OTP"}
                  </Button>
                </form>
              </Form>
              <div className="mt-6 text-center">
                <p className="text-sm text-[#605045]">
                  Don't have an account?{" "}
                  <Button
                    variant="link"
                    onClick={() => navigate("/register")}
                    className="p-0 h-auto text-[#4A6741] hover:text-[#3A5131] underline"
                  >
                    Register
                  </Button>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[80vh] flex items-center justify-center bg-[#F7F4F1] py-12">
      <div className="w-full max-w-md px-4">
        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <Button
              variant="ghost"
              onClick={goBackToPhone}
              className="mb-4 p-2 h-auto w-auto self-start"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <CardTitle className="font-heading text-2xl font-bold text-[#2D3C2D] text-center">Enter OTP</CardTitle>
            <CardDescription className="text-center text-[#605045]">
              We've sent a 6-digit code to {phoneNumber}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={onOTPSubmit} className="space-y-6">
              <div className="space-y-4">
                <label className="text-[#2D3C2D] text-center block font-medium">Enter 6-digit OTP</label>
                <div className="flex justify-center">
                  <Input
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={6}
                    placeholder="000000"
                    value={otpValue}
                    onChange={(e) => setOtpValue(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    autoFocus
                    className="w-32 h-12 text-center text-lg font-mono tracking-widest border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                    autoComplete="one-time-code"
                  />
                </div>
              </div>
              <Button
                type="submit"
                className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white"
                disabled={verifyOTPMutation.isPending || otpValue.length !== 6}
              >
                {verifyOTPMutation.isPending ? "Verifying..." : "Verify & Login"}
              </Button>
            </form>
            <div className="mt-6 text-center">
              <Button
                variant="link"
                onClick={() => sendOTPMutation.mutate({ identifier: phoneNumber, type: 'sms' })}
                className="text-[#4A6741] hover:text-[#3A5131] underline"
                disabled={sendOTPMutation.isPending}
              >
                Resend OTP
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}