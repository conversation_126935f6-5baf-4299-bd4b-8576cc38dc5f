
import { useState, useEffect } from "react";
import { useLocation, useSearch } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/contexts/AuthContext";
import { ArrowLeft, Loader2 } from "lucide-react";

const otpVerifySchema = z.object({
  smsCode: z.string().length(6, "SMS code must be 6 digits"),
  emailCode: z.string().length(6, "Email code must be 6 digits"),
});

type OTPVerifyValues = z.infer<typeof otpVerifySchema>;

export default function VerifyRegistration() {
  const { login } = useAuth();
  const [_, navigate] = useLocation();
  const search = useSearch();
  const { toast } = useToast();
  
  const params = new URLSearchParams(search);
  const phone = params.get("phone") || "";
  const email = params.get("email") || "";

  useEffect(() => {
    // Check if we have pending registration data
    const pendingData = sessionStorage.getItem('pendingRegistration');
    if (!pendingData) {
      toast({
        title: "Session expired",
        description: "Please start registration again",
        variant: "destructive"
      });
      navigate('/register');
    }
  }, [navigate, toast]);

  const form = useForm<OTPVerifyValues>({
    resolver: zodResolver(otpVerifySchema),
    defaultValues: {
      smsCode: "",
      emailCode: "",
    },
  });

  const verifyOTPMutation = useMutation({
    mutationFn: async (data: OTPVerifyValues) => {
      const pendingData = sessionStorage.getItem('pendingRegistration');
      if (!pendingData) {
        throw new Error('Registration data not found. Please start registration again.');
      }
      
      const userData = JSON.parse(pendingData);
      
      // Verify with SMS OTP first, which will create the account
      const response = await apiRequest("POST", "/api/auth/otp/verify-otp-register", {
        identifier: userData.phone,
        code: data.smsCode,
        type: 'sms',
        userData: {
          ...userData,
          emailCode: data.emailCode,
          email: userData.email
        }
      });
      
      return response.json();
    },
    onSuccess: (data) => {
      sessionStorage.removeItem('pendingRegistration');
      login(data.user, data.token);
      toast({
        title: "Registration successful",
        description: "Welcome to BookAFarm!"
      });
      navigate('/');
    },
    onError: (error: Error) => {
      toast({
        title: "Verification failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const resendOTPMutation = useMutation({
    mutationFn: async () => {
      const pendingData = sessionStorage.getItem('pendingRegistration');
      if (!pendingData) {
        throw new Error('Registration data not found. Please start registration again.');
      }
      
      const userData = JSON.parse(pendingData);
      
      // Send SMS OTP
      const smsResponse = await apiRequest("POST", "/api/auth/otp/send-otp", {
        identifier: userData.phone,
        type: 'sms'
      });
      
      // Send Email OTP
      const emailResponse = await apiRequest("POST", "/api/auth/otp/send-otp", {
        identifier: userData.email,
        type: 'email'
      });

      await Promise.all([smsResponse.json(), emailResponse.json()]);
    },
    onSuccess: () => {
      toast({
        title: "OTPs Resent",
        description: "New verification codes sent to your phone and email"
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const onSubmit = (data: OTPVerifyValues) => {
    verifyOTPMutation.mutate(data);
  };

  const goBack = () => {
    sessionStorage.removeItem('pendingRegistration');
    navigate('/register');
  };

  return (
    <div className="min-h-screen bg-[#F7F5F2] py-12">
      <div className="container px-4 mx-auto">
        <Card className="max-w-lg mx-auto shadow-md">
          <CardHeader className="bg-[#4A6741] text-white text-center">
            <Button
              variant="ghost"
              onClick={goBack}
              className="mb-4 p-2 h-auto w-auto self-start text-white hover:bg-[#3A5131]"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <CardTitle className="text-2xl font-bold">Enter Verification Codes</CardTitle>
            <CardDescription className="text-[#E5DDD3]">
              We sent 6-digit codes to your phone and email
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center space-y-2 mb-6">
              <p className="text-sm text-[#605045]">
                Verification codes sent to:
              </p>
              <p className="font-semibold text-[#2D3C2D]">{phone}</p>
              <p className="font-semibold text-[#2D3C2D]">{email}</p>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="smsCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D]">SMS Verification Code</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="000000"
                          maxLength={6}
                          className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741] text-center text-lg tracking-widest"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emailCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D]">Email Verification Code</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="000000"
                          maxLength={6}
                          className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741] text-center text-lg tracking-widest"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white"
                  disabled={verifyOTPMutation.isPending}
                >
                  {verifyOTPMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    "Verify & Complete Registration"
                  )}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={() => resendOTPMutation.mutate()}
                  className="w-full"
                  disabled={resendOTPMutation.isPending}
                >
                  {resendOTPMutation.isPending ? 'Resending...' : 'Resend Codes'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
