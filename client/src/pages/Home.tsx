import { useState, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { Property } from "@shared/schema";
import { api } from "@/lib/api";
import PropertyList from "@/components/PropertyList";
import SearchForm from "@/components/SearchForm";
import FeaturedCarousel from "@/components/FeaturedCarousel";
import LocationRecommendations from "@/components/LocationRecommendations";
import LocationFilter from "@/components/LocationFilter";
import InteractivePropertyMap from "@/components/InteractivePropertyMap";
import OwnerInterestForm from "@/components/OwnerInterestForm";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { X, Building2, PlusCircle } from "lucide-react";

interface AdvancedSearchParams {
  location: string;
  date: string;
  minPrice: number | undefined;
  maxPrice: number | undefined;
  amenities: string[] | undefined;
  featured: boolean | undefined;
}

export default function Home() {
  const [searchParams, setSearchParams] = useState<AdvancedSearchParams>({
    location: "",
    date: "",
    minPrice: undefined,
    maxPrice: undefined,
    amenities: undefined,
    featured: undefined
  });
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  const resultsRef = useRef<HTMLElement>(null);

  // Fetch featured properties
  const { data: featuredProperties, isLoading: isFeaturedLoading } = useQuery<Property[]>({
    queryKey: ["/api/properties", { featured: true }],
    queryFn: async () => {
      console.log('🔍 [DEBUG] Fetching featured properties from /api/properties?featured=true');
      const response = await fetch(`/api/properties?featured=true&_t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      console.log('🔍 [DEBUG] Featured properties response:', response.status, response.statusText);
      if (!response.ok) throw new Error("Failed to fetch featured properties");
      const result = await response.json();
      console.log('🔍 [DEBUG] Featured properties result:', result);
      console.log('🔍 [DEBUG] Featured properties data:', result.data || result);
      return result.data || result; // Handle both structured and direct responses
    }
  });

  // Fetch all properties for main listings
  const { data: allProperties, isLoading: isAllPropertiesLoading } = useQuery<Property[]>({
    queryKey: ["/api/properties", "all"],
    queryFn: async () => {
      console.log('🔍 [DEBUG] Fetching all properties from /api/properties');
      const response = await fetch(`/api/properties?_t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      console.log('🔍 [DEBUG] All properties response:', response.status, response.statusText);
      if (!response.ok) throw new Error("Failed to fetch all properties");
      const result = await response.json();
      console.log('🔍 [DEBUG] All properties result:', result);
      console.log('🔍 [DEBUG] All properties data:', result.data || result);
      return result.data || result; // Handle both structured and direct responses
    }
  });

  // Fetch properties based on search params
  const { data: searchResults, isLoading: isSearchLoading } = useQuery<Property[]>({
    queryKey: ["/api/properties", searchParams],
    queryFn: async () => {
      const params = new URLSearchParams();

      // Add basic search params
      if (searchParams.location) params.append("location", searchParams.location);
      if (searchParams.date) params.append("date", searchParams.date);

      // Add advanced search params
      if (searchParams.minPrice !== undefined) params.append("minPrice", searchParams.minPrice.toString());
      if (searchParams.maxPrice !== undefined) params.append("maxPrice", searchParams.maxPrice.toString());
      if (searchParams.amenities && searchParams.amenities.length > 0) {
        params.append("amenities", searchParams.amenities.join(','));
      }
      if (searchParams.featured) params.append("featured", "true");

      const url = `/api/properties?${params.toString()}`;
      console.log("Searching with URL:", url);

      const response = await fetch(url);
      if (!response.ok) throw new Error("Failed to fetch properties");
      const result = await response.json();
      return result.data || result; // Handle both structured and direct responses
    },
    enabled: !!(searchParams.location || searchParams.date || 
                searchParams.minPrice || searchParams.maxPrice || 
                (searchParams.amenities && searchParams.amenities.length > 0) ||
                searchParams.featured)
  });

  const handleSearch = (values: AdvancedSearchParams) => {
    console.log("Search values:", values);

    // If no specific filters are set (just showing all), clear the search params
    // to show featured properties instead of applying default price filters
    // Check if location is "all" or empty, and other defaults
    if ((!values.location || values.location === "all") && !values.date && 
        (values.minPrice === undefined || values.minPrice === 2000) && 
        (values.maxPrice === undefined || values.maxPrice === 25000) && 
        (!values.amenities || values.amenities.length === 0) && 
        !values.featured) {
      setSearchParams({
        location: "",
        date: "",
        minPrice: undefined,
        maxPrice: undefined,
        amenities: undefined,
        featured: undefined
      });
    } else {
      // Convert "all" location to empty string for API
      const processedValues = {
        ...values,
        location: values.location === "all" ? "" : values.location
      };
      setSearchParams(processedValues);
    }

    // Smooth scroll to results section after search
    setTimeout(() => {
      resultsRef.current?.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      });
    }, 100);
  };

  const clearFilter = (filterType: keyof AdvancedSearchParams) => {
    setSearchParams(prev => ({
      ...prev,
      [filterType]: filterType === 'amenities' ? [] : undefined
    }));
  };

  const clearAllFilters = () => {
    setSearchParams({
      location: "",
      date: "",
      minPrice: undefined,
      maxPrice: undefined,
      amenities: undefined,
      featured: undefined
    });
  };

  const handleLocationSelect = (location: string) => {
    setSearchParams(prev => ({
      ...prev,
      location: location
    }));

    // Scroll to results
    if (resultsRef.current) {
      resultsRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleClearLocation = () => {
    setSearchParams(prev => ({
      ...prev,
      location: ""
    }));
  };

  const findNearbyFarmhouses = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsGettingLocation(true);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;

        // Determine the nearest region based on coordinates
        // Hyderabad is approximately at 17.3850°N, 78.4867°E
        // Telangana covers roughly 15°N to 19.5°N, 77°E to 81°E
        let nearestLocation = "";

        if (latitude >= 17.2 && latitude <= 17.6 && longitude >= 78.2 && longitude <= 78.8) {
          nearestLocation = "Hyderabad";
        } else if (latitude >= 16.8 && latitude <= 17.2 && longitude >= 78.0 && longitude <= 79.0) {
          nearestLocation = "Ranga Reddy";
        } else if (latitude >= 17.6 && latitude <= 18.2 && longitude >= 78.0 && longitude <= 79.5) {
          nearestLocation = "Medchal-Malkajgiri";
        } else if (latitude >= 15.0 && latitude <= 19.5 && longitude >= 77.0 && longitude <= 81.0) {
          nearestLocation = "Telangana";
        } else {
          nearestLocation = "Hyderabad"; // Default fallback
        }

        setSearchParams(prev => ({
          ...prev,
          location: nearestLocation,
          featured: false
        }));

        setIsGettingLocation(false);

        // Scroll to results
        if (resultsRef.current) {
          resultsRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      },
      (error) => {
        setIsGettingLocation(false);
        let errorMessage = "Unable to retrieve your location. ";

        switch(error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += "Please allow location access and try again.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += "Location information is unavailable.";
            break;
          case error.TIMEOUT:
            errorMessage += "Location request timed out.";
            break;
          default:
            errorMessage += "An unknown error occurred.";
            break;
        }

        alert(errorMessage + " Showing properties in Hyderabad instead.");

        // Fallback to Hyderabad properties
        setSearchParams(prev => ({
          ...prev,
          location: "Hyderabad",
          featured: false
        }));

        if (resultsRef.current) {
          resultsRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  const hasActiveSearch = (searchParams.location && searchParams.location !== "all") || searchParams.date || 
                         (searchParams.minPrice && searchParams.minPrice !== 2000) || 
                         (searchParams.maxPrice && searchParams.maxPrice !== 25000) || 
                         (searchParams.amenities && searchParams.amenities.length > 0) ||
                         searchParams.featured;

  const isLoading = hasActiveSearch ? isSearchLoading : (isFeaturedLoading || isAllPropertiesLoading);

  // Determine which properties to show
  const properties = hasActiveSearch ? searchResults : allProperties;
  
  // If no active search and allProperties failed, fallback to featuredProperties
  const displayProperties = (!hasActiveSearch && (!allProperties || allProperties.length === 0)) 
    ? featuredProperties 
    : properties;

  // Debug logging
  console.log('🔍 [DEBUG] Home component state:');
  console.log('  hasActiveSearch:', hasActiveSearch);
  console.log('  allProperties:', allProperties ? `${allProperties.length} items` : 'undefined/empty');
  console.log('  featuredProperties:', featuredProperties ? `${featuredProperties.length} items` : 'undefined/empty');
  console.log('  searchResults:', searchResults ? `${searchResults.length} items` : 'undefined/empty');
  console.log('  displayProperties:', displayProperties ? `${displayProperties.length} items` : 'undefined/empty');
  console.log('  isAllPropertiesLoading:', isAllPropertiesLoading);
  console.log('  isFeaturedLoading:', isFeaturedLoading);
  console.log('  isSearchLoading:', isSearchLoading);
  if (allProperties) console.log('  allProperties sample:', allProperties.slice(0, 1));
  if (featuredProperties) console.log('  featuredProperties sample:', featuredProperties.slice(0, 1));

  return (
    <>
      {/* Hero Section */}
      <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(74, 103, 65, 0.7), rgba(45, 60, 45, 0.6)), url('https://images.unsplash.com/photo-1464822759844-d150baec0494?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
          }}
        />

        {/* Floating Elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-24 h-24 bg-[#D9B382] rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white rounded-full blur-xl animate-pulse delay-500"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-4xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full text-white mb-6 animate-fade-in">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              <span className="text-sm font-medium">Premium Farmhouse Experiences</span>
            </div>

            {/* Main Heading */}
            <h1 className="font-heading text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Escape to
              <span className="block text-[#D9B382] animate-pulse"> Rural Paradise</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto font-light">
              Discover authentic farmhouse experiences in Hyderabad and Telangana. 
              Book your perfect morning retreat or full-day farm escape today.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <button 
                onClick={() => {
                  // Scroll to the properties section below
                  if (resultsRef.current) {
                    resultsRef.current.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="px-8 py-4 bg-[#D9B382] text-[#2D3C2D] rounded-lg font-semibold text-lg hover:bg-[#C9A372] transform hover:scale-105 transition-all duration-300 shadow-lg"
              >
                Explore Properties
              </button>
              <button 
                onClick={findNearbyFarmhouses}
                disabled={isGettingLocation}
                className="px-8 py-4 bg-transparent border-2 border-white text-white rounded-lg font-semibold text-lg hover:bg-white hover:text-[#2D3C2D] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 justify-center"
              >
                {isGettingLocation ? (
                  <>
                    <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Getting Location...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Find Nearby Farmhouses
                  </>
                )}
              </button>
            </div>

            {/* Enhanced Search Form */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl border border-white/20">
              <SearchForm onSearch={(values) => handleSearch({ 
                location: values.location,
                date: values.date,
                minPrice: values.minPrice || undefined,
                maxPrice: values.maxPrice || undefined,
                amenities: values.amenities || undefined,
                featured: undefined 
              })} />
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mt-12 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-1">50+</div>
                <div className="text-white/80 text-sm">Premium Properties</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-1">1000+</div>
                <div className="text-white/80 text-sm">Happy Guests</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-1">4.9</div>
                <div className="text-white/80 text-sm">Average Rating</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </section>

      {/* One-Click Location Filter */}
      <section className="bg-[#F7F4F1] py-12">
        <div className="container mx-auto px-4">
          <LocationFilter
            selectedLocation={searchParams.location || ""}
            onLocationSelect={handleLocationSelect}
            onClearLocation={handleClearLocation}
          />
        </div>
      </section>

      {/* Featured Listings */}
      <section ref={resultsRef} className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="font-heading text-3xl font-bold text-[#2D3C2D]">
                {hasActiveSearch ? "Search Results" : "Featured Farmhouses"}
              </h2>
              {hasActiveSearch && displayProperties && (
                <p className="text-[#605045] mt-2">
                  {isLoading ? "Searching..." : `Showing ${displayProperties.length} ${displayProperties.length === 1 ? 'property' : 'properties'}`}
                  {searchParams.location && ` in ${searchParams.location}`}
                  {searchParams.date && ` for ${new Date(searchParams.date).toLocaleDateString()}`}
                </p>
              )}
              {!hasActiveSearch && (
                <p className="text-[#605045] mt-2">
                  {isLoading ? "Loading..." : `Showing ${Math.min(featuredProperties?.length || 0, 5)} featured properties`}
                </p>
              )}
            </div>
            {!hasActiveSearch && (
              <button 
                onClick={() => handleSearch({ location: "", date: "", minPrice: undefined, maxPrice: undefined, amenities: undefined, featured: true })}
                className="text-[#4A6741] hover:text-[#3A5131] font-semibold flex items-center transition-colors duration-200"
              >
                View all
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>

          {/* Active Filter Badges */}
          {hasActiveSearch && (
            <div className="mb-8">
              <div className="flex flex-wrap items-center gap-3">
                <span className="text-sm font-medium text-[#605045]">Active filters:</span>

                {searchParams.location && (
                  <div className="flex items-center gap-1 bg-[#4A6741] text-white px-3 py-1 rounded-full text-sm">
                    <span>📍 {searchParams.location}</span>
                    <button
                      onClick={() => clearFilter('location')}
                      className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {searchParams.date && (
                  <div className="flex items-center gap-1 bg-[#4A6741] text-white px-3 py-1 rounded-full text-sm">
                    <span>📅 {new Date(searchParams.date).toLocaleDateString()}</span>
                    <button
                      onClick={() => clearFilter('date')}
                      className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {(searchParams.minPrice || searchParams.maxPrice) && (
                  <div className="flex items-center gap-1 bg-[#4A6741] text-white px-3 py-1 rounded-full text-sm">
                    <span>💰 ₹{searchParams.minPrice || 0} - ₹{searchParams.maxPrice || 25000}</span>
                    <button
                      onClick={() => {
                        clearFilter('minPrice');
                        clearFilter('maxPrice');
                      }}
                      className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {searchParams.amenities && searchParams.amenities.length > 0 && (
                  <div className="flex items-center gap-1 bg-[#4A6741] text-white px-3 py-1 rounded-full text-sm">
                    <span>🏠 {searchParams.amenities.length} amenity{searchParams.amenities.length > 1 ? 'ies' : ''}</span>
                    <button
                      onClick={() => clearFilter('amenities')}
                      className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}

                {hasActiveSearch && (
                  <Button
                    onClick={clearAllFilters}
                    variant="outline"
                    size="sm"
                    className="border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white"
                  >
                    Clear all
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* No Results Found State */}
          {hasActiveSearch && !isLoading && displayProperties && displayProperties.length === 0 && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="mb-6">
                  <svg className="mx-auto h-16 w-16 text-[#605045]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-[#2D3C2D] mb-3">No properties found</h3>
                <p className="text-[#605045] mb-6">
                  We couldn't find any farmhouses matching your search criteria. Try adjusting your filters or search in a different location.
                </p>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-[#605045] mb-2">Suggestions:</p>
                    <ul className="text-sm text-[#605045] space-y-1">
                      <li>• Try searching in popular locations like Lonavala, Alibaug, or Goa</li>
                      <li>• Expand your price range</li>
                      <li>• Remove some amenity filters</li>
                      <li>• Try a different date</li>
                    </ul>
                  </div>

                  <div className="pt-4">
                    <Button
                      onClick={clearAllFilters}
                      className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
                    >
                      Clear all filters
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Show properties when we have results */}
          {(!hasActiveSearch || (displayProperties && displayProperties.length > 0)) && (
            <>
              {/* Featured Carousel for non-search results */}
              {!hasActiveSearch && featuredProperties && featuredProperties.length > 0 && (
                <div className="mb-12">
                  <FeaturedCarousel properties={featuredProperties.slice(0, 5)} />
                </div>
              )}

              {/* Regular property list */}
              <PropertyList 
                properties={displayProperties || []} 
                isLoading={isLoading} 
              />
            </>
          )}
        </div>
      </section>

      {/* Location Recommendations - only show when not searching */}
      {!hasActiveSearch && featuredProperties && featuredProperties.length > 0 && (
        <LocationRecommendations 
          properties={featuredProperties} 
          currentLocation={searchParams.location || undefined} 
        />
      )}

      {/* Interactive Property Map */}
      {displayProperties && displayProperties.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-4">Explore Properties on Map</h2>
              <p className="text-[#605045] text-lg max-w-2xl mx-auto">
                Discover farmhouses across Telangana and Hyderabad. Click on any pin to view property details and book your perfect rural getaway.
              </p>
            </div>
            <div className="max-w-6xl mx-auto">
              <InteractivePropertyMap 
                properties={displayProperties} 
                className="h-[500px] w-full rounded-2xl shadow-lg"
              />
            </div>
          </div>
        </section>
      )}

      {/* Why Choose Us */}
      <section className="py-16 bg-gradient-to-br from-white to-[#F7F4F1]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-4">Why Choose Our Farmhouses</h2>
            <p className="text-[#605045] text-lg max-w-2xl mx-auto">Experience the perfect blend of rustic charm and modern comfort</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Verified Properties</h3>
              <p className="text-[#605045]">Every farmhouse is personally inspected and verified for quality</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Instant Booking</h3>
              <p className="text-[#605045]">Book instantly with immediate confirmation and secure payments</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Prime Locations</h3>
              <p className="text-[#605045]">Handpicked locations near lakes, mountains, and scenic countryside</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 11-9.75 9.75A9.75 9.75 0 0112 2.25z" />
                </svg>
              </div>
              <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">24/7 Support</h3>
              <p className="text-[#605045]">Round-the-clock customer support for a worry-free experience</p>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-4">What Our Guests Say</h2>
            <p className="text-[#605045] text-lg">Real experiences from happy families and travelers</p>
          </div>

          <div className="grid grid-cols-1```text
 md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-white to-[#F7F4F1] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  ))}
                </div>
              </div>
              <p className="text-[#605045] mb-4 italic group-hover:text-[#4A6741] transition-colors">"The perfect escape from city life! The farmhouse was spotless, beautifully decorated, and the surrounding nature was breathtaking. Our kids loved the farm animals and pool."</p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-[#4A6741] rounded-full flex items-center justify-center text-white font-bold mr-3">
                  S
                </div>
                <div>
                  <h4 className="font-semibold text-[#2D3C2D]">Sneha Patel</h4>
                  <p className="text-sm text-[#605045]">Family Vacation, Mumbai</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white to-[#F7F4F1] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  ))}
                </div>
              </div>
              <p className="text-[#605045] mb-4 italic group-hover:text-[#4A6741] transition-colors">"Absolutely amazing experience! The booking process was smooth, and the property exceeded our expectations. Perfect for our anniversary celebration."</p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-[#4A6741] rounded-full flex items-center justify-center text-white font-bold mr-3">
                  R
                </div>
                <div>
                  <h4 className="font-semibold text-[#2D3C2D]">Rahul & Priya</h4>
                  <p className="text-sm text-[#605045]">Couple Retreat, Bangalore</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white to-[#F7F4F1] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  ))}
                </div>
              </div>
              <p className="text-[#605045] mb-4 italic group-hover:text-[#4A6741] transition-colors">"Great value for money! Clean facilities, friendly hosts, and beautiful surroundings. Our corporate team had an excellent offsite here."</p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-[#4A6741] rounded-full flex items-center justify-center text-white font-bold mr-3">
                  A
                </div>
                <div>
                  <h4 className="font-semibold text-[#2D3C2D]">Amit Kumar</h4>
                  <p className="text-sm text-[#605045]">Corporate Event, Delhi</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-[#F7F4F1]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-3">How It Works</h2>
            <p className="text-lg text-[#605045] max-w-2xl mx-auto">Book your perfect farmhouse getaway in three simple steps.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Step 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-[#4A6741] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">1</div>
              <h3 className="font-heading text-xl font-bold text-[#2D3C2D] mb-2">Search</h3>
              <p className="text-[#605045]">Find your ideal farmhouse by location, dates, and amenities.</p>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-[#4A6741] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">2</div>
              <h3 className="font-heading text-xl font-bold text-[#2D3C2D] mb-2">Book</h3>
              <p className="text-[#605045]">Choose morning-only or full-day bookings at your selected farmhouse.</p>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-[#4A6741] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">3</div>
              <h3 className="font-heading text-xl font-bold text-[#2D3C2D] mb-2">Enjoy</h3>
              <p className="text-[#605045]">Experience rural tranquility and make memories that last a lifetime.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Property Owner Interest Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <div className="inline-flex items-center px-4 py-2 bg-[#4A6741]/10 rounded-full text-[#4A6741] mb-4">
                <Building2 className="w-4 h-4 mr-2" />
                <span className="text-sm font-medium">For Property Owners</span>
              </div>
              <h2 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-4">
                List Your Property with Us
              </h2>
              <p className="text-lg text-[#605045] max-w-2xl mx-auto mb-8">
                Transform your farmhouse into a profitable rental property. Join our network of successful property owners and start earning from your unused spaces.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4">
                  <PlusCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Easy Listing</h3>
                <p className="text-[#605045]">Quick and simple property registration process with professional photography support</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Guaranteed Income</h3>
                <p className="text-[#605045]">Earn steady rental income with our marketing and booking management support</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-[#4A6741] to-[#3A5131] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 11-9.75 9.75A9.75 9.75 0 0112 2.25z" />
                  </svg>
                </div>
                <h3 className="font-heading text-xl font-semibold text-[#2D3C2D] mb-2">Full Support</h3>
                <p className="text-[#605045]">24/7 customer support, maintenance coordination, and guest management</p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#F7F4F1] to-[#EDE8E1] rounded-2xl p-8 text-center">
              <h3 className="font-heading text-2xl font-bold text-[#2D3C2D] mb-4">
                Ready to Start Earning?
              </h3>
              <p className="text-[#605045] mb-6 max-w-2xl mx-auto">
                Fill out our property interest form and our team will contact you within 24 hours to discuss listing opportunities, pricing, and next steps.
              </p>

              <Dialog>
                <DialogTrigger asChild>
                  <Button className="bg-[#4A6741] hover:bg-[#3A5131] text-white px-8 py-3 text-lg font-semibold">
                    List Your Property
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0 border-0">
                  <VisuallyHidden>
                    <DialogTitle>List Your Property</DialogTitle>
                    <DialogDescription>
                      Join our network of property owners and start earning from your farmhouse.
                    </DialogDescription>
                  </VisuallyHidden>
                  <OwnerInterestForm />
                </DialogContent>
              </Dialog>

              <div className="mt-6 grid grid-cols-3 gap-8 max-w-2xl mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-[#4A6741] mb-1">500+</div>
                  <div className="text-sm text-[#605045]">Listed Properties</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-[#4A6741] mb-1">₹85k</div>
                  <div className="text-sm text-[#605045]">Avg. Monthly Earnings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-[#4A6741] mb-1">4.8/5</div>
                  <div className="text-sm text-[#605045]">Owner Satisfaction</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </>
  );
}

