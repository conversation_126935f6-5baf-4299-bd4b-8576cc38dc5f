import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { StarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Review, ReviewForm } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, getQueryFn } from "@/lib/queryClient";

interface ReviewSectionProps {
  propertyId: number;
}

interface PropertyReviews {
  reviews: (Review & { user: { username: string; fullName?: string } })[];
  averageRating: number;
  totalReviews: number;
}

export default function ReviewSection({ propertyId }: ReviewSectionProps) {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [rating, setRating] = useState<"1" | "2" | "3" | "4" | "5">("5");
  const [comment, setComment] = useState("");

  // Fetch property reviews
  const { data, isLoading } = useQuery<PropertyReviews>({
    queryKey: [`/api/properties/${propertyId}/reviews`],
    queryFn: getQueryFn({ on401: "throw" }),
  });

  // Submit review mutation
  const mutation = useMutation({
    mutationFn: async (reviewData: ReviewForm) => {
      // apiRequest already handles authentication via cookies/token
      const response = await apiRequest("POST", "/api/reviews", reviewData);
      return response.json();
    },
    onSuccess: () => {
      // Reset form
      setRating("5");
      setComment("");
      setShowReviewForm(false);
      
      // Invalidate reviews cache to refresh the list
      queryClient.invalidateQueries({ queryKey: [`/api/properties/${propertyId}/reviews`] });
      
      toast({
        title: "Review submitted!",
        description: "Thank you for sharing your experience.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to submit review",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmitReview = () => {
    if (!comment.trim()) {
      toast({
        title: "Review comment required",
        description: "Please share your experience before submitting.",
        variant: "destructive",
      });
      return;
    }
    
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to submit a review.",
        variant: "destructive",
      });
      return;
    }
    
    mutation.mutate({
      propertyId,
      userId: user.id,
      rating,
      comment,
    });
  };

  // Check if user has already reviewed this property (one review per user per property)
  const hasReviewed = data?.reviews.some(review => user && review.userId === user.id);
  
  if (isLoading) {
    return (
      <div className="border-t border-[#EBE6E1] pt-8">
        <h2 className="font-heading text-2xl font-bold text-[#2D3C2D] mb-4">Reviews</h2>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-20 bg-gray-200 rounded mb-4"></div>
          <div className="h-20 bg-gray-200 rounded mb-4"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="border-t border-[#EBE6E1] pt-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h2 className="font-heading text-2xl font-bold text-[#2D3C2D] mb-2 md:mb-0">
          Reviews {data?.totalReviews ? `(${data.totalReviews})` : ""}
        </h2>
        
        <div className="flex items-center space-x-4">
          {data && data.totalReviews > 0 && (
            <div className="flex items-center">
              <div className="flex items-center mr-2">
                {[1, 2, 3, 4, 5].map(star => (
                  <StarIcon 
                    key={star}
                    className={`h-5 w-5 ${star <= Math.round(data.averageRating) ? "text-yellow-500 fill-yellow-500" : "text-gray-300"}`} 
                  />
                ))}
              </div>
              <span className="font-semibold">{data.averageRating.toFixed(1)}</span>
            </div>
          )}
          
          {isAuthenticated && !hasReviewed && !showReviewForm && (
            <Button 
              onClick={() => setShowReviewForm(true)}
              className="bg-[#4A6741] hover:bg-[#3A5131]"
            >
              Write a Review
            </Button>
          )}
          
          {isAuthenticated && hasReviewed && (
            <span className="text-sm text-[#766C63] italic">
              You have already reviewed this property
            </span>
          )}
        </div>
      </div>
      
      {/* Review Form */}
      {showReviewForm && (
        <div className="bg-[#F7F4F1] p-6 rounded-lg mb-8">
          <h3 className="font-heading text-xl font-bold text-[#2D3C2D] mb-4">Share Your Experience</h3>
          
          <div className="mb-4">
            <label className="block text-[#605045] mb-2">Rating</label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map(star => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star.toString() as "1" | "2" | "3" | "4" | "5")}
                  className="focus:outline-none"
                >
                  <StarIcon 
                    className={`h-8 w-8 ${parseInt(rating) >= star ? "text-yellow-500 fill-yellow-500" : "text-gray-300"}`} 
                  />
                </button>
              ))}
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-[#605045] mb-2">Your Review</label>
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Share your experience with this farmhouse..."
              className="min-h-[120px]"
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button 
              variant="outline" 
              onClick={() => setShowReviewForm(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmitReview}
              className="bg-[#4A6741] hover:bg-[#3A5131]"
              disabled={mutation.isPending}
            >
              {mutation.isPending ? "Submitting..." : "Submit Review"}
            </Button>
          </div>
        </div>
      )}
      
      {/* Review List */}
      <div className="space-y-6">
        {data && data.reviews.length > 0 ? (
          data.reviews.map((review) => (
            <div key={review.id} className="border-b border-[#EBE6E1] pb-6 mb-6 last:border-0">
              <div className="flex justify-between mb-2">
                <div>
                  <h4 className="font-semibold text-[#2D3C2D]">
                    {review.user.fullName || review.user.username}
                  </h4>
                  <div className="text-sm text-[#766C63]">
                    {new Date(review.createdAt).toLocaleDateString(undefined, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
                <div className="flex">
                  {[1, 2, 3, 4, 5].map(star => (
                    <StarIcon 
                      key={star}
                      className={`h-4 w-4 ${parseInt(review.rating) >= star ? "text-yellow-500 fill-yellow-500" : "text-gray-300"}`} 
                    />
                  ))}
                </div>
              </div>
              
              <p className="text-[#605045] mb-3">{review.comment}</p>
              
              {review.response && (
                <div className="bg-[#F7F4F1] p-4 rounded">
                  <h5 className="font-semibold text-[#2D3C2D] mb-1">Response from Owner</h5>
                  <p className="text-[#605045]">{review.response}</p>
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-[#766C63]">
            <p>No reviews yet. Be the first to share your experience!</p>
          </div>
        )}
      </div>
    </div>
  );
}