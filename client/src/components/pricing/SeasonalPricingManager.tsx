import React, { useState } from 'react';
import { Calendar as CalendarIcon, Plus, X, Percent, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { CurrencyInput } from '@/components/ui/currency-input';
import { useToast } from '@/hooks/use-toast';

// Types for seasonal pricing (future implementation)
export interface SeasonalPeriod {
  id: string;
  name: string;
  startDate: string; // YYYY-MM-DD
  endDate: string;   // YYYY-MM-DD
  type: 'peak' | 'off' | 'special' | 'holiday';
  enabled: boolean;
  pricing: {
    halfDayPrice?: number;
    fullDayPrice?: number;
    weekdayHalfDayPrice?: number;
    weekdayFullDayPrice?: number;
    weekendHalfDayPrice?: number;
    weekendFullDayPrice?: number;
  };
  multiplier?: number; // Alternative to fixed pricing - percentage increase/decrease
  description?: string;
  color: string;
  priority: number; // Higher priority overrides lower priority when periods overlap
}

export interface DiscountRule {
  id: string;
  name: string;
  type: 'early_bird' | 'last_minute' | 'bulk' | 'returning_customer' | 'seasonal';
  enabled: boolean;
  conditions: {
    daysInAdvance?: number;
    minimumStay?: number;
    minimumGuests?: number;
    applicableSeasons?: string[];
  };
  discount: {
    type: 'percentage' | 'fixed_amount';
    value: number;
    maxAmount?: number; // Cap for percentage discounts
  };
  validFrom: string;
  validUntil: string;
  description: string;
}

interface SeasonalPricingManagerProps {
  propertyId?: number;
  className?: string;
  onPricingChange?: (periods: SeasonalPeriod[], discounts: DiscountRule[]) => void;
}

// Predefined seasonal periods (templates)
const SEASONAL_TEMPLATES: Omit<SeasonalPeriod, 'id' | 'pricing'>[] = [
  {
    name: 'Summer Peak Season',
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    type: 'peak',
    enabled: false,
    multiplier: 1.3,
    description: 'High demand summer period',
    color: '#F59E0B',
    priority: 3
  },
  {
    name: 'Monsoon Off Season',
    startDate: '2024-07-01',
    endDate: '2024-09-30',
    type: 'off',
    enabled: false,
    multiplier: 0.8,
    description: 'Rainy season with lower demand',
    color: '#6B7280',
    priority: 2
  },
  {
    name: 'Winter Holiday Season',
    startDate: '2024-12-20',
    endDate: '2025-01-15',
    type: 'holiday',
    enabled: false,
    multiplier: 1.5,
    description: 'Christmas and New Year premium period',
    color: '#EF4444',
    priority: 4
  },
  {
    name: 'Spring Festival Season',
    startDate: '2024-03-01',
    endDate: '2024-03-31',
    type: 'special',
    enabled: false,
    multiplier: 1.2,
    description: 'Holi and spring celebrations',
    color: '#10B981',
    priority: 3
  }
];

// Predefined discount templates
const DISCOUNT_TEMPLATES: Omit<DiscountRule, 'id'>[] = [
  {
    name: 'Early Bird Discount',
    type: 'early_bird',
    enabled: false,
    conditions: { daysInAdvance: 30 },
    discount: { type: 'percentage', value: 10, maxAmount: 5000 },
    validFrom: '2024-01-01',
    validUntil: '2024-12-31',
    description: '10% off for bookings made 30 days in advance'
  },
  {
    name: 'Last Minute Deal',
    type: 'last_minute',
    enabled: false,
    conditions: { daysInAdvance: 2 },
    discount: { type: 'percentage', value: 15, maxAmount: 3000 },
    validFrom: '2024-01-01',
    validUntil: '2024-12-31',
    description: '15% off for last-minute bookings (within 2 days)'
  },
  {
    name: 'Group Booking Discount',
    type: 'bulk',
    enabled: false,
    conditions: { minimumGuests: 8 },
    discount: { type: 'percentage', value: 12, maxAmount: 8000 },
    validFrom: '2024-01-01',
    validUntil: '2024-12-31',
    description: '12% off for groups of 8 or more'
  }
];

export function SeasonalPricingManager({ 
  propertyId, 
  className = '',
  onPricingChange 
}: SeasonalPricingManagerProps) {
  const { toast } = useToast();
  const [seasonalPeriods, setSeasonalPeriods] = useState<SeasonalPeriod[]>([]);
  const [discountRules, setDiscountRules] = useState<DiscountRule[]>([]);
  const [isEnabled, setIsEnabled] = useState(false);
  const [activeTab, setActiveTab] = useState<'seasonal' | 'discounts'>('seasonal');

  const addSeasonalPeriod = (template: typeof SEASONAL_TEMPLATES[0]) => {
    const newPeriod: SeasonalPeriod = {
      ...template,
      id: `period_${Date.now()}`,
      pricing: {}
    };
    
    setSeasonalPeriods(prev => [...prev, newPeriod]);
    toast({
      title: 'Seasonal Period Added',
      description: `${template.name} has been added to your pricing calendar.`
    });
  };

  const removeSeasonalPeriod = (id: string) => {
    setSeasonalPeriods(prev => prev.filter(p => p.id !== id));
    toast({
      title: 'Period Removed',
      description: 'Seasonal pricing period has been removed.'
    });
  };

  const toggleSeasonalPeriod = (id: string) => {
    setSeasonalPeriods(prev => prev.map(p => 
      p.id === id ? { ...p, enabled: !p.enabled } : p
    ));
  };

  const addDiscountRule = (template: typeof DISCOUNT_TEMPLATES[0]) => {
    const newRule: DiscountRule = {
      ...template,
      id: `discount_${Date.now()}`
    };
    
    setDiscountRules(prev => [...prev, newRule]);
    toast({
      title: 'Discount Rule Added',
      description: `${template.name} has been configured.`
    });
  };

  const removeDiscountRule = (id: string) => {
    setDiscountRules(prev => prev.filter(r => r.id !== id));
  };

  const toggleDiscountRule = (id: string) => {
    setDiscountRules(prev => prev.map(r => 
      r.id === id ? { ...r, enabled: !r.enabled } : r
    ));
  };

  // Notify parent component of changes
  React.useEffect(() => {
    if (onPricingChange) {
      onPricingChange(seasonalPeriods, discountRules);
    }
  }, [seasonalPeriods, discountRules, onPricingChange]);

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg font-semibold">
              Advanced Pricing Management
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              Coming Soon
            </Badge>
          </div>
          <Switch
            checked={isEnabled}
            onCheckedChange={setIsEnabled}
            disabled={true} // Disabled for now - future feature
          />
        </div>
        <p className="text-sm text-gray-600">
          Configure seasonal pricing and automatic discounts to maximize revenue throughout the year.
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Feature Preview Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <CalendarIcon className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Future Enhancement Preview</h4>
              <p className="text-sm text-blue-800 mb-3">
                This advanced pricing system will allow you to:
              </p>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Set seasonal rates for peak/off-peak periods</li>
                <li>• Configure automatic discounts based on booking conditions</li>
                <li>• Manage holiday and special event pricing</li>
                <li>• Create early bird and last-minute deals</li>
                <li>• Implement dynamic pricing based on demand</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <Button
            variant={activeTab === 'seasonal' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('seasonal')}
            className="flex-1"
            disabled
          >
            <CalendarIcon className="h-4 w-4 mr-2" />
            Seasonal Pricing
          </Button>
          <Button
            variant={activeTab === 'discounts' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('discounts')}
            className="flex-1"
            disabled
          >
            <Percent className="h-4 w-4 mr-2" />
            Discount Rules
          </Button>
        </div>

        {/* Seasonal Pricing Tab */}
        {activeTab === 'seasonal' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">Seasonal Periods</h3>
              <Badge variant="secondary" className="text-xs">
                {seasonalPeriods.length} configured
              </Badge>
            </div>

            {/* Template Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {SEASONAL_TEMPLATES.map((template, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-3 hover:border-gray-300 transition-colors opacity-60"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: template.color }}
                      />
                      <h4 className="font-medium text-sm">{template.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {template.type}
                      </Badge>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => addSeasonalPeriod(template)}
                      disabled
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{template.description}</p>
                  <div className="flex items-center justify-between text-xs">
                    <span>{template.startDate} to {template.endDate}</span>
                    <span className="font-medium">
                      {template.multiplier && `${(template.multiplier * 100)}% of base`}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Discount Rules Tab */}
        {activeTab === 'discounts' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">Automatic Discounts</h3>
              <Badge variant="secondary" className="text-xs">
                {discountRules.length} configured
              </Badge>
            </div>

            {/* Discount Templates */}
            <div className="space-y-3">
              {DISCOUNT_TEMPLATES.map((template, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-3 hover:border-gray-300 transition-colors opacity-60"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Percent className="h-4 w-4 text-green-600" />
                      <h4 className="font-medium text-sm">{template.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {template.discount.value}{template.discount.type === 'percentage' ? '%' : '₹'} off
                      </Badge>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => addDiscountRule(template)}
                      disabled
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-600">{template.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Implementation Status */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <span className="text-gray-500">Implementation Status:</span>
              <Badge variant="outline" className="text-xs">
                Architecture Ready
              </Badge>
            </div>
            <span className="text-xs text-gray-400">
              Framework prepared for future development
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default SeasonalPricingManager;