import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CURRENCIES, CurrencyConfig } from '@/components/ui/currency-input';

interface CurrencySelectorProps {
  value: keyof typeof CURRENCIES;
  onChange: (currency: keyof typeof CURRENCIES) => void;
  disabled?: boolean;
  className?: string;
  showFlag?: boolean;
  showFullName?: boolean;
}

// Currency metadata for display purposes
const CURRENCY_METADATA: Record<keyof typeof CURRENCIES, {
  name: string;
  flag: string;
  region: string;
  description: string;
}> = {
  INR: {
    name: 'Indian Rupee',
    flag: '🇮🇳',
    region: 'India',
    description: 'Default currency for Indian market'
  },
  USD: {
    name: 'US Dollar',
    flag: '🇺🇸',
    region: 'United States',
    description: 'International bookings'
  },
  EUR: {
    name: 'Euro',
    flag: '🇪🇺',
    region: 'European Union',
    description: 'European market'
  }
};

export function CurrencySelector({
  value,
  onChange,
  disabled = false,
  className = '',
  showFlag = true,
  showFullName = false
}: CurrencySelectorProps) {
  const selectedCurrency = CURRENCIES[value];
  const selectedMetadata = CURRENCY_METADATA[value];

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <label className="text-sm font-medium text-gray-700">Currency</label>
        <Badge variant="outline" className="text-xs">
          Future Ready
        </Badge>
      </div>
      
      <Select 
        value={value} 
        onValueChange={onChange} 
        disabled={disabled}
      >
        <SelectTrigger className="w-full">
          <SelectValue>
            <div className="flex items-center gap-2">
              {showFlag && <span>{selectedMetadata.flag}</span>}
              <span className="font-medium">{selectedCurrency.symbol}</span>
              <span className="text-sm text-gray-600">
                {selectedCurrency.code}
                {showFullName && ` - ${selectedMetadata.name}`}
              </span>
            </div>
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          {Object.entries(CURRENCIES).map(([code, config]) => {
            const metadata = CURRENCY_METADATA[code as keyof typeof CURRENCIES];
            return (
              <SelectItem key={code} value={code}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-3">
                    {showFlag && <span>{metadata.flag}</span>}
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{config.symbol}</span>
                        <span className="text-sm font-medium">{config.code}</span>
                        {showFullName && (
                          <span className="text-sm text-gray-600">
                            {metadata.name}
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {metadata.description}
                      </span>
                    </div>
                  </div>
                  {code === value && (
                    <Badge variant="default" className="text-xs">
                      Selected
                    </Badge>
                  )}
                </div>
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
      
      <div className="text-xs text-gray-500">
        Selected: {selectedMetadata.region} ({selectedCurrency.locale})
      </div>
    </div>
  );
}

// Hook for currency conversion (future implementation)
export function useCurrencyConversion() {
  // TODO: Implement real-time currency conversion API
  // This would fetch exchange rates and convert prices between currencies
  
  const convertPrice = (amount: number, fromCurrency: keyof typeof CURRENCIES, toCurrency: keyof typeof CURRENCIES): number => {
    // Placeholder implementation - in production, this would use live exchange rates
    const exchangeRates = {
      'INR': { 'USD': 0.012, 'EUR': 0.011 },
      'USD': { 'INR': 83.0, 'EUR': 0.92 },
      'EUR': { 'INR': 90.0, 'USD': 1.09 }
    };
    
    if (fromCurrency === toCurrency) return amount;
    
    const rate = exchangeRates[fromCurrency as keyof typeof exchangeRates]?.[toCurrency as keyof typeof exchangeRates[keyof typeof exchangeRates]];
    return rate ? Math.round(amount * rate) : amount;
  };

  const formatPriceInCurrency = (amount: number, currency: keyof typeof CURRENCIES): string => {
    const config = CURRENCIES[currency];
    const formatted = new Intl.NumberFormat(config.locale, {
      style: 'currency',
      currency: config.code,
      minimumFractionDigits: 0,
      maximumFractionDigits: config.decimalPlaces
    }).format(amount);
    
    return formatted;
  };

  return {
    convertPrice,
    formatPriceInCurrency
  };
}

export default CurrencySelector;