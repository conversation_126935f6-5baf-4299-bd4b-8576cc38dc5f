import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { format, differenceInDays, isFuture, parseISO } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  FileText, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { cn } from '../lib/utils';

// Form schema with validation
const bookingFormSchema = z.object({
  guestName: z.string()
    .min(2, 'Guest name must be at least 2 characters')
    .max(100, 'Guest name must not exceed 100 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Guest name can only contain letters and spaces'),
  
  guestPhone: z.string()
    .regex(/^[\+]?[\d\s\-\(\)]{10,}$/, 'Please enter a valid phone number')
    .min(10, 'Phone number must be at least 10 digits'),
  
  guestCount: z.number()
    .int('Guest count must be a whole number')
    .min(1, 'At least 1 guest is required')
    .max(50, 'Maximum 50 guests allowed'),
  
  notes: z.string()
    .max(500, 'Notes must not exceed 500 characters')
    .optional(),
  
  status: z.enum(['confirmed', 'tentative', 'blocked'], {
    errorMap: () => ({ message: 'Please select a valid booking status' })
  }),
  
  bookingType: z.enum(['direct', 'online', 'whatsapp'], {
    errorMap: () => ({ message: 'Please select a valid booking type' })
  })
});

type BookingFormData = z.infer<typeof bookingFormSchema>;

interface BookingDetails {
  startDate: string;
  endDate: string;
  nights: number;
  totalCost?: number;
  pricePerNight?: number;
}

interface EnhancedBookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: BookingFormData & { startDate: string; endDate: string }) => void;
  bookingDetails?: BookingDetails;
  existingBooking?: any;
  mode: 'create' | 'edit';
  isLoading?: boolean;
  propertyTitle?: string;
}

export function EnhancedBookingModal({
  isOpen,
  onClose,
  onSubmit,
  bookingDetails,
  existingBooking,
  mode,
  isLoading = false,
  propertyTitle
}: EnhancedBookingModalProps) {
  const [step, setStep] = useState(1);
  const [conflicts, setConflicts] = useState<string[]>([]);
  const [availability, setAvailability] = useState<{
    isAvailable: boolean;
    message?: string;
  } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
    setValue,
    watch,
    reset,
    clearErrors
  } = useForm<BookingFormData>({
    resolver: zodResolver(bookingFormSchema),
    mode: 'onChange',
    defaultValues: {
      guestName: existingBooking?.guestName || '',
      guestPhone: existingBooking?.guestPhone || '',
      guestCount: existingBooking?.guestCount || 1,
      notes: existingBooking?.notes || '',
      status: existingBooking?.status || 'confirmed',
      bookingType: existingBooking?.bookingType || 'direct'
    }
  });

  const watchedStatus = watch('status');
  const watchedGuestCount = watch('guestCount');

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep(1);
      setConflicts([]);
      setAvailability(null);
      
      if (existingBooking) {
        reset({
          guestName: existingBooking.guestName || existingBooking.guest_name || '',
          guestPhone: existingBooking.guestPhone || existingBooking.guest_phone || '',
          guestCount: existingBooking.guestCount || existingBooking.guest_count || 1,
          notes: existingBooking.notes || '',
          status: existingBooking.status || 'confirmed',
          bookingType: existingBooking.bookingType || existingBooking.booking_type || 'direct'
        });
      } else {
        reset({
          guestName: '',
          guestPhone: '',
          guestCount: 1,
          notes: '',
          status: 'confirmed',
          bookingType: 'direct'
        });
      }
    }
  }, [isOpen, existingBooking, reset]);

  // Check availability when creating new booking
  useEffect(() => {
    if (mode === 'create' && bookingDetails && step === 1) {
      checkAvailability();
    }
  }, [mode, bookingDetails, step]);

  const checkAvailability = async () => {
    if (!bookingDetails) return;

    try {
      const response = await fetch('/api/calendar/availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startDate: bookingDetails.startDate,
          endDate: bookingDetails.endDate,
          excludeBookingId: existingBooking?.id
        }),
      });

      const data = await response.json();
      setAvailability(data);
      
      if (!data.isAvailable) {
        setConflicts(data.conflicts || [data.message]);
      } else {
        setConflicts([]);
      }
    } catch (error) {
      console.error('Error checking availability:', error);
      setAvailability({ isAvailable: false, message: 'Failed to check availability' });
    }
  };

  const handleFormSubmit = async (data: BookingFormData) => {
    if (!bookingDetails) return;

    if (mode === 'create' && !availability?.isAvailable) {
      setStep(1); // Go back to show conflicts
      return;
    }

    const submissionData = {
      ...data,
      startDate: bookingDetails.startDate,
      endDate: bookingDetails.endDate
    };

    onSubmit(submissionData);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'tentative':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getBookingTypeColor = (type: string) => {
    switch (type) {
      case 'direct':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'online':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'whatsapp':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar size={20} />
            {mode === 'create' ? 'Create New Booking' : 'Edit Booking'}
          </DialogTitle>
          {propertyTitle && (
            <DialogDescription>
              {propertyTitle}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="space-y-6">
          {/* Step Indicator */}
          <div className="flex items-center justify-center gap-2">
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
              step >= 1 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
            )}>
              1
            </div>
            <div className={cn(
              "w-16 h-0.5",
              step >= 2 ? "bg-blue-600" : "bg-gray-200"
            )} />
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
              step >= 2 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
            )}>
              2
            </div>
          </div>

          {/* Step 1: Booking Details & Conflicts */}
          {step === 1 && (
            <div className="space-y-4">
              {/* Booking Summary */}
              {bookingDetails && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                    <Clock size={16} />
                    Booking Details
                  </h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-blue-700 font-medium">Check-in</p>
                      <p className="text-blue-900">
                        {format(parseISO(bookingDetails.startDate), 'MMM dd, yyyy')}
                      </p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Check-out</p>
                      <p className="text-blue-900">
                        {format(parseISO(bookingDetails.endDate), 'MMM dd, yyyy')}
                      </p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Duration</p>
                      <p className="text-blue-900">
                        {bookingDetails.nights} night{bookingDetails.nights !== 1 ? 's' : ''}
                      </p>
                    </div>
                    {bookingDetails.totalCost && (
                      <div>
                        <p className="text-blue-700 font-medium">Total Cost</p>
                        <p className="text-blue-900">₹{bookingDetails.totalCost}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Availability Status */}
              {availability !== null && (
                <Alert className={availability.isAvailable ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                  <div className="flex items-center gap-2">
                    {availability.isAvailable ? (
                      <CheckCircle size={16} className="text-green-600" />
                    ) : (
                      <XCircle size={16} className="text-red-600" />
                    )}
                    <AlertDescription className={availability.isAvailable ? 'text-green-800' : 'text-red-800'}>
                      {availability.isAvailable 
                        ? 'Dates are available for booking'
                        : availability.message || 'Dates are not available'
                      }
                    </AlertDescription>
                  </div>
                </Alert>
              )}

              {/* Conflicts */}
              {conflicts.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-red-900 flex items-center gap-2">
                    <AlertTriangle size={16} />
                    Booking Conflicts
                  </h4>
                  {conflicts.map((conflict, index) => (
                    <Alert key={index} className="border-red-200 bg-red-50">
                      <AlertDescription className="text-red-800">
                        {conflict}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}

              {/* Navigation */}
              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  onClick={() => setStep(2)}
                  disabled={mode === 'create' && !availability?.isAvailable}
                >
                  Continue
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Guest Information Form */}
          {step === 2 && (
            <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
              {/* Guest Name */}
              <div className="space-y-2">
                <Label htmlFor="guestName" className="flex items-center gap-2">
                  <User size={16} />
                  Guest Name *
                </Label>
                <Input
                  id="guestName"
                  {...register('guestName')}
                  placeholder="Enter guest full name"
                  className={errors.guestName ? 'border-red-300 focus:border-red-500' : ''}
                />
                {errors.guestName && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <XCircle size={14} />
                    {errors.guestName.message}
                  </p>
                )}
              </div>

              {/* Guest Phone */}
              <div className="space-y-2">
                <Label htmlFor="guestPhone" className="flex items-center gap-2">
                  <Phone size={16} />
                  Phone Number *
                </Label>
                <Input
                  id="guestPhone"
                  {...register('guestPhone')}
                  placeholder="Enter phone number (e.g., +91 98765 43210)"
                  className={errors.guestPhone ? 'border-red-300 focus:border-red-500' : ''}
                />
                {errors.guestPhone && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <XCircle size={14} />
                    {errors.guestPhone.message}
                  </p>
                )}
              </div>

              {/* Guest Count */}
              <div className="space-y-2">
                <Label htmlFor="guestCount" className="flex items-center gap-2">
                  <User size={16} />
                  Guest Count *
                </Label>
                <Input
                  id="guestCount"
                  type="number"
                  min="1"
                  max="50"
                  {...register('guestCount', { valueAsNumber: true })}
                  className={errors.guestCount ? 'border-red-300 focus:border-red-500' : ''}
                />
                {errors.guestCount && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <XCircle size={14} />
                    {errors.guestCount.message}
                  </p>
                )}
                {watchedGuestCount > 20 && (
                  <Alert className="border-amber-200 bg-amber-50">
                    <Info size={16} className="text-amber-600" />
                    <AlertDescription className="text-amber-800">
                      Large groups may require additional arrangements. Please add relevant details in notes.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              {/* Status & Type */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">
                    Status *
                  </Label>
                  <Select 
                    value={watchedStatus} 
                    onValueChange={(value: any) => setValue('status', value)}
                  >
                    <SelectTrigger className={errors.status ? 'border-red-300 focus:border-red-500' : ''}>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="confirmed">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          Confirmed
                        </div>
                      </SelectItem>
                      <SelectItem value="tentative">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                          Tentative
                        </div>
                      </SelectItem>
                      <SelectItem value="blocked">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          Blocked
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.status && (
                    <p className="text-sm text-red-600">{errors.status.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bookingType">
                    Type *
                  </Label>
                  <Select 
                    value={watch('bookingType')} 
                    onValueChange={(value: any) => setValue('bookingType', value)}
                  >
                    <SelectTrigger className={errors.bookingType ? 'border-red-300 focus:border-red-500' : ''}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="direct">Direct</SelectItem>
                      <SelectItem value="online">Online</SelectItem>
                      <SelectItem value="whatsapp">WhatsApp</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.bookingType && (
                    <p className="text-sm text-red-600">{errors.bookingType.message}</p>
                  )}
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes" className="flex items-center gap-2">
                  <FileText size={16} />
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  {...register('notes')}
                  placeholder="Add any special requests, requirements, or notes..."
                  rows={3}
                  maxLength={500}
                  className={errors.notes ? 'border-red-300 focus:border-red-500' : ''}
                />
                <div className="flex justify-between items-center">
                  {errors.notes && (
                    <p className="text-sm text-red-600">{errors.notes.message}</p>
                  )}
                  <p className="text-sm text-gray-500 ml-auto">
                    {watch('notes')?.length || 0}/500
                  </p>
                </div>
              </div>

              {/* Status Preview */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Preview:</span>
                <Badge className={getStatusColor(watchedStatus)}>
                  {watchedStatus}
                </Badge>
                <Badge className={getBookingTypeColor(watch('bookingType'))}>
                  {watch('bookingType')}
                </Badge>
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  disabled={!isValid || isLoading}
                  className="min-w-32"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      {mode === 'create' ? 'Creating...' : 'Updating...'}
                    </div>
                  ) : (
                    mode === 'create' ? 'Create Booking' : 'Update Booking'
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}