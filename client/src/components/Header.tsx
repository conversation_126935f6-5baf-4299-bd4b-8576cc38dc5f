import { useEffect, useState } from "react";
import { Link, useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import SwiggyStyleAuthModal from "./SwiggyStyleAuthModal";

export default function Header() {
  const { user, logout } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [, navigate] = useLocation();

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setIsMenuOpen(false);
      setIsProfileMenuOpen(false);
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(!isMenuOpen);
    setIsProfileMenuOpen(false);
  };

  const toggleProfileMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsProfileMenuOpen(!isProfileMenuOpen);
    setIsMenuOpen(false);
  };

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  return (
    <header className="bg-white shadow-sm relative z-10">
      <div className="container mx-auto px-4 py-4 flex flex-col sm:flex-row items-center justify-between">
        <div className="flex items-center mb-4 sm:mb-0">
          <Link href="/" className="flex items-center">
            <div className="text-[#4A6741] text-3xl font-heading font-bold">BookAFarm</div>
            <span className="font-['Caveat'] text-[#A67F5D] ml-2 text-lg">Hyderabad Escapes</span>
          </Link>
        </div>

        <div className="flex items-center space-x-1 sm:space-x-4">
          {/* Navigation Links */}
          <Link href="/" className="px-3 py-2 text-[#605045] hover:text-[#4A6741] hidden md:block">
            Home
          </Link>
          <Link href="/" className="px-3 py-2 text-[#605045] hover:text-[#4A6741] hidden md:block">
            Explore
          </Link>
          <Link href="/" className="px-3 py-2 text-[#605045] hover:text-[#4A6741] hidden md:block">
            About
          </Link>

          {/* User Menu (Logged Out) */}
          {!user ? (
            <div className="flex items-center space-x-2">
              <Button 
                onClick={() => {
                  setShowAuthModal(true);
                }}
                variant="outline"
                className="px-3 py-2 text-sm font-semibold border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white"
              >
                Login
              </Button>
              <Button 
                onClick={() => navigate('/register')}
                className="px-3 py-2 bg-[#4A6741] text-white text-sm font-semibold hover:bg-[#3A5131]"
              >
                Sign Up
              </Button>
            </div>
          ) : (
            <div className="relative" onClick={toggleProfileMenu}>
              <button className="flex items-center space-x-1 px-3 py-2 rounded-md border border-[#D5CCC4] hover:border-[#B4A89E]">
                <span className="text-sm font-semibold">
                  {(() => {
                    if (user.fullName) {
                      return user.fullName.split(' ')[0];
                    }
                    if (user.username && user.username.includes('@')) {
                      return user.username.split('@')[0];
                    }
                    if (user.username) {
                      return user.username;
                    }
                    if (user.email) {
                      return user.email.split('@')[0];
                    }
                    return 'User';
                  })()}
                </span>
                <span>▼</span>
              </button>

              {isProfileMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                  {user.role === 'owner' && (
                    <Link href="/owner/dashboard" className="block px-4 py-2 text-sm text-[#605045] hover:bg-[#F7F4F1]">
                      Dashboard
                    </Link>
                  )}
                  <Link href="/bookings" className="block px-4 py-2 text-sm text-[#605045] hover:bg-[#F7F4F1]">
                    Your Bookings
                  </Link>
                  <Link href="/profile" className="block px-4 py-2 text-sm text-[#605045] hover:bg-[#F7F4F1]">
                    Profile
                  </Link>
                  <a href="#" className="block px-4 py-2 text-sm text-[#605045] hover:bg-[#F7F4F1]">Settings</a>
                  <button 
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-[#605045] hover:bg-[#F7F4F1]"
                  >
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Mobile Menu Button */}
          <button className="md:hidden ml-2" onClick={toggleMenu}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#605045]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-[#EBE6E1] py-2 px-4">
          <Link href="/"><a className="block py-2 text-[#605045]">Home</a></Link>
          <Link href="/"><a className="block py-2 text-[#605045]">Explore</a></Link>
          <Link href="/"><a className="block py-2 text-[#605045]">About</a></Link>
          {!user && (
            <>
              <button 
                onClick={() => {
                  setShowAuthModal(true);
                  setIsMenuOpen(false);
                }}
                className="block py-2 text-[#605045] text-left w-full"
              >
                Login
              </button>
              <Link href="/register"><a className="block py-2 text-[#605045]">Sign Up</a></Link>
            </>
          )}
          {user && user.role === 'owner' && (
            <Link href="/owner/dashboard"><a className="block py-2 text-[#605045]">Dashboard</a></Link>
          )}
        </div>
      )}
      
      <SwiggyStyleAuthModal 
        isOpen={showAuthModal} 
        onClose={() => setShowAuthModal(false)}
        initialMode="login"
      />
    </header>
  );
}