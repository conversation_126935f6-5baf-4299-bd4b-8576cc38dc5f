import React, { Component, ReactNode, ErrorInfo } from 'react';
import { <PERSON>ert<PERSON>ircle, RefreshCw, Home, Bug } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorId?: string;
}

export class CalendarErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorId: `cal-error-${Date.now()}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Calendar Error Boundary caught an error:', error, errorInfo);
    
    // Log error to monitoring service
    this.logError(error, errorInfo);
    
    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to a logging service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('userId') || 'anonymous'
    };

    // Send to logging service (e.g., Sentry, LogRocket, etc.)
    console.error('Error Report:', errorReport);
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorId: undefined });
  };

  private handleReportBug = () => {
    const errorDetails = {
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    // Create mailto link with error details
    const subject = encodeURIComponent('Calendar Component Bug Report');
    const body = encodeURIComponent(
      `Error Details:\n\n${JSON.stringify(errorDetails, null, 2)}\n\nPlease describe what you were doing when this error occurred:`
    );
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle size={24} />
              Calendar Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle size={16} className="text-red-600" />
              <AlertDescription className="text-red-800">
                Something went wrong with the calendar component. This error has been automatically reported.
              </AlertDescription>
            </Alert>

            {process.env.NODE_ENV === 'development' && (
              <div className="bg-gray-100 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Error Details (Development)</h4>
                <pre className="text-sm text-gray-700 overflow-x-auto whitespace-pre-wrap">
                  {this.state.error?.message}
                  {'\n\n'}
                  {this.state.error?.stack}
                </pre>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={this.handleRetry} className="flex-1">
                <RefreshCw size={16} className="mr-2" />
                Try Again
              </Button>
              <Button variant="outline" onClick={this.handleReload} className="flex-1">
                <Home size={16} className="mr-2" />
                Reload Page
              </Button>
              <Button variant="outline" onClick={this.handleReportBug}>
                <Bug size={16} className="mr-2" />
                Report Bug
              </Button>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600">
                Error ID: <code className="bg-gray-100 px-1 rounded">{this.state.errorId}</code>
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Include this ID when reporting the issue for faster resolution.
              </p>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}