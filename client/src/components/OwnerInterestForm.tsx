import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { DialogClose } from "@/components/ui/dialog";
import { X, Check, Building, MapPin, Users, DollarSign, Calendar, Info } from "lucide-react";
import { ImageUpload } from "./ImageUpload";

// Form validation schema
const ownerInterestSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  propertyLocation: z.string().min(2, "Property location is required"),
  propertyType: z.string().min(1, "Property type is required"),
  propertySize: z.string().optional(),
  expectedRevenue: z.string().optional(),
  currentOccupancy: z.string().optional(),
  amenities: z.array(z.string()).default([]),
  propertyDescription: z.string().optional(),
  experience: z.string().optional(),
  availability: z.string().optional(),
  additionalInfo: z.string().optional(),
  images: z.array(z.string()).optional(),
});

type OwnerInterestFormData = z.infer<typeof ownerInterestSchema>;

// Available amenities for selection
const availableAmenities = [
  "Swimming Pool",
  "Garden/Lawn",
  "Parking",
  "Kitchen",
  "Air Conditioning",
  "Wi-Fi",
  "BBQ Area",
  "Fire Pit",
  "Outdoor Seating",
  "Security",
  "Caretaker",
  "Farm Animals",
  "Organic Farming",
  "Lake/Pond",
  "Mountain View",
  "Forest Area",
  "Indoor Games",
  "Outdoor Games",
  "Event Space",
  "Photography Setup"
];

interface OwnerInterestFormProps {
  onClose?: () => void;
}

export default function OwnerInterestForm({ onClose }: OwnerInterestFormProps) {
  const { toast } = useToast();
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<OwnerInterestFormData>({
    resolver: zodResolver(ownerInterestSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      propertyLocation: "",
      propertyType: "",
      propertySize: "",
      expectedRevenue: "",
      currentOccupancy: "",
      amenities: [],
      propertyDescription: "",
      experience: "",
      availability: "",
      additionalInfo: "",
      images: [],
    }
  });

  const submitOwnerInterest = useMutation({
    mutationFn: async (data: OwnerInterestFormData) => {
      const response = await fetch("/api/owner-interest", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit form");
      }

      return response.json();
    },
    onSuccess: (data) => {
      setIsSubmitted(true);
      toast({
        title: "Success!",
        description: "Thank you for your interest! We'll contact you within 24 hours to discuss listing your property.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Something went wrong. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: OwnerInterestFormData) => {
    submitOwnerInterest.mutate(data);
  };

  const handleAmenityToggle = (amenity: string, checked: boolean) => {
    const currentAmenities = form.getValues("amenities");
    if (checked) {
      form.setValue("amenities", [...currentAmenities, amenity]);
    } else {
      form.setValue("amenities", currentAmenities.filter(a => a !== amenity));
    }
  };

  if (isSubmitted) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-[#2D3C2D] mb-2">Thank You!</h3>
            <p className="text-[#605045] mb-6">
              We've received your property listing request. Our team will review your information and contact you within 24 hours to discuss the next steps.
            </p>
            <div className="bg-[#F7F4F1] rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-[#2D3C2D] mb-2">What happens next?</h4>
              <ul className="text-sm text-[#605045] space-y-1 text-left">
                <li>• Property evaluation and verification</li>
                <li>• Discussion of listing terms and pricing</li>
                <li>• Professional photography arrangement</li>
                <li>• Listing setup and go-live</li>
              </ul>
            </div>
            <DialogClose asChild>
              <Button className="bg-[#4A6741] hover:bg-[#3A5131] text-white">
                Close
              </Button>
            </DialogClose>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto relative">
      <CardHeader>
        <div>
          <CardTitle className="text-2xl font-bold text-[#2D3C2D] flex items-center gap-2">
            <Building className="w-6 h-6" />
            List Your Property
          </CardTitle>
          <CardDescription className="text-[#605045] mt-2">
            Join our network of property owners and start earning from your farmhouse. Fill out this form and we'll contact you within 24 hours.
          </CardDescription>
        </div>

      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#2D3C2D] flex items-center gap-2">
                <Users className="w-5 h-5" />
                Personal Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address *</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="+91 9876543210" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Property Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#2D3C2D] flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Property Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="propertyLocation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Location *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Hyderabad, Telangana" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="propertyType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select property type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="farmhouse">Farmhouse</SelectItem>
                          <SelectItem value="villa">Villa</SelectItem>
                          <SelectItem value="cottage">Cottage</SelectItem>
                          <SelectItem value="resort">Resort</SelectItem>
                          <SelectItem value="heritage_property">Heritage Property</SelectItem>
                          <SelectItem value="eco_lodge">Eco Lodge</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="propertySize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Size</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 2 acres, 5000 sq ft" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="currentOccupancy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Guest Capacity</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 10-15 guests" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Business Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#2D3C2D] flex items-center gap-2">
                <span className="w-5 h-5 flex items-center justify-center text-[#4A6741] font-bold">₹</span>
                Business Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="expectedRevenue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expected Monthly Revenue</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select expected revenue range" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="₹0 - ₹5,000">₹0 - ₹5,000</SelectItem>
                          <SelectItem value="₹5,000 - ₹20,000">₹5,000 - ₹20,000</SelectItem>
                          <SelectItem value="₹20,000 - ₹35,000">₹20,000 - ₹35,000</SelectItem>
                          <SelectItem value="₹35,000 - ₹50,000">₹35,000 - ₹50,000</SelectItem>
                          <SelectItem value="₹50,000 - ₹70,000">₹50,000 - ₹70,000</SelectItem>
                          <SelectItem value="₹70,000 - ₹1,00,000">₹70,000 - ₹1,00,000</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="experience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hospitality Experience</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select experience level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No prior experience</SelectItem>
                          <SelectItem value="some">Some experience (1-2 years)</SelectItem>
                          <SelectItem value="experienced">Experienced (3-5 years)</SelectItem>
                          <SelectItem value="expert">Expert (5+ years)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Amenities */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#2D3C2D]">Property Amenities</h3>
              <p className="text-sm text-[#605045]">Select all amenities available at your property</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {availableAmenities.map((amenity) => (
                  <div key={amenity} className="flex items-center space-x-2">
                    <Checkbox
                      id={amenity}
                      checked={form.watch("amenities").includes(amenity)}
                      onCheckedChange={(checked) => handleAmenityToggle(amenity, checked as boolean)}
                    />
                    <Label htmlFor={amenity} className="text-sm cursor-pointer">
                      {amenity}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#2D3C2D] flex items-center gap-2">
                <Info className="w-5 h-5" />
                Additional Information
              </h3>
              <FormField
                control={form.control}
                name="propertyDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Property Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your property, its unique features, surroundings, and what makes it special..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="availability"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Availability</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Year round, Weekends only" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="additionalInfo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Information</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Any other information you'd like to share..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
             <div className="space-y-4">

              <FormField
                control={form.control}
                name="images"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#2D3C2D] font-semibold">Property Photos</FormLabel>
                    <FormDescription>
                      Upload photos and videos of your property (max 10 files, 5MB each, 10MB total)
                    </FormDescription>
                    <FormControl>
                      <ImageUpload
                        images={field.value || []}
                        onImagesChange={field.onChange}
                        maxImages={10}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <Button
                type="submit"
                disabled={submitOwnerInterest.isPending}
                className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white py-3"
              >
                {submitOwnerInterest.isPending ? (
                  <>
                    <svg className="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                  </>
                ) : (
                  "Submit Interest"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}