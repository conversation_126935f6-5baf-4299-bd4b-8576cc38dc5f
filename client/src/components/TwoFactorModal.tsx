import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Shield, Clock, RefreshCw, Smartphone, Mail } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { SecurityEventType } from "@/config/security";
import { auditLogger, logSuspiciousActivity } from "@/lib/audit";
import { useSessionSecurity } from "@/lib/session";

export type TwoFactorMethod = 'sms' | 'email' | 'totp';

export interface TwoFactorData {
  sessionId: string;
  amount: number;
  bookingId: number;
  propertyTitle: string;
}

interface TwoFactorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (verificationToken: string) => void;
  onFailure: (error: Error) => void;
  data: TwoFactorData;
  preferredMethod?: TwoFactorMethod;
}

interface VerificationState {
  step: 'select_method' | 'enter_code' | 'verifying';
  method: TwoFactorMethod | null;
  code: string;
  timeLeft: number;
  attempts: number;
  isBlocked: boolean;
}

// Content component without Dialog wrapper for use inside other dialogs
export function TwoFactorContent({
  onClose,
  onSuccess,
  onFailure,
  data,
  preferredMethod = 'sms'
}: Omit<TwoFactorModalProps, 'isOpen'>) {
  const [state, setState] = useState<VerificationState>({
    step: 'select_method',
    method: null,
    code: '',
    timeLeft: 300, // 5 minutes
    attempts: 0,
    isBlocked: false,
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [maskedContact, setMaskedContact] = useState('');
  const { toast } = useToast();
  const { getSecurityContext, recordPaymentAttempt } = useSessionSecurity();
  
  const codeInputRef = useRef<HTMLInputElement>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  // Auto-focus code input when step changes
  useEffect(() => {
    if (state.step === 'enter_code' && codeInputRef.current) {
      codeInputRef.current.focus();
    }
  }, [state.step]);

  // Start countdown timer
  useEffect(() => {
    if (state.step === 'enter_code' && state.timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setState(prev => {
          if (prev.timeLeft <= 1) {
            clearInterval(timerRef.current);
            return { ...prev, timeLeft: 0, step: 'select_method' };
          }
          return { ...prev, timeLeft: prev.timeLeft - 1 };
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [state.step, state.timeLeft]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  const sendVerificationCode = async (method: TwoFactorMethod) => {
    setIsLoading(true);
    
    try {
      const response = await apiRequest("POST", "/api/auth/2fa/send", {
        method,
        sessionId: data.sessionId,
        bookingId: data.bookingId,
        amount: data.amount,
      });

      const result = await response.json();
      
      if (result.success) {
        setState(prev => ({
          ...prev,
          step: 'enter_code',
          method,
          timeLeft: 300, // Reset timer
          attempts: 0,
        }));
        
        setMaskedContact(result.data.maskedContact);
        
        auditLogger.logSecurityEvent(SecurityEventType.TWO_FA_REQUIRED, {
          method,
          bookingId: data.bookingId,
          amount: data.amount,
          maskedContact: result.data.maskedContact,
        }, getSecurityContext() || undefined);

        toast({
          title: "Verification Code Sent",
          description: `Code sent to ${result.data.maskedContact}`,
        });
      } else {
        throw new Error(result.message || "Failed to send verification code");
      }
    } catch (error: any) {
      console.error("2FA send error:", error);
      
      auditLogger.logSecurityEvent(SecurityEventType.TWO_FA_FAILURE, {
        method,
        error: error.message,
        bookingId: data.bookingId,
      }, getSecurityContext() || undefined);

      toast({
        title: "Verification Failed",
        description: "Unable to send verification code. Please try again.",
        variant: "destructive",
      });
      
      onFailure(error);
    } finally {
      setIsLoading(false);
    }
  };

  const verifyCode = async () => {
    if (!state.method || state.code.length !== 6) {
      toast({
        title: "Invalid Code",
        description: "Please enter a 6-digit verification code.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setState(prev => ({ ...prev, step: 'verifying' }));

    try {
      const response = await apiRequest("POST", "/api/auth/2fa/verify", {
        code: state.code,
        method: state.method,
        sessionId: data.sessionId,
        bookingId: data.bookingId,
      });

      const result = await response.json();
      
      if (result.success) {
        auditLogger.logSecurityEvent(SecurityEventType.TWO_FA_SUCCESS, {
          method: state.method,
          bookingId: data.bookingId,
          attempts: state.attempts + 1,
        }, getSecurityContext() || undefined);

        recordPaymentAttempt(true);
        
        toast({
          title: "Verification Successful",
          description: "You can now proceed with payment.",
        });
        
        onSuccess(result.data.verificationToken);
        
      } else {
        throw new Error(result.message || "Verification failed");
      }
    } catch (error: any) {
      console.error("2FA verify error:", error);
      
      const newAttempts = state.attempts + 1;
      const isBlocked = newAttempts >= 3;
      
      setState(prev => ({
        ...prev,
        attempts: newAttempts,
        isBlocked,
        step: isBlocked ? 'select_method' : 'enter_code',
        code: '',
      }));

      auditLogger.logSecurityEvent(SecurityEventType.TWO_FA_FAILURE, {
        method: state.method,
        error: error.message,
        attempts: newAttempts,
        isBlocked,
        bookingId: data.bookingId,
      }, getSecurityContext() || undefined);

      recordPaymentAttempt(false, error.code);

      // Log suspicious activity for multiple failures
      if (newAttempts >= 2) {
        logSuspiciousActivity({
          activity: 'multiple_2fa_failures',
          attempts: newAttempts,
          bookingId: data.bookingId,
        }, getSecurityContext() || undefined);
      }

      if (isBlocked) {
        toast({
          title: "Verification Blocked",
          description: "Too many failed attempts. Please try again later.",
          variant: "destructive",
        });
        onFailure(new Error("2FA verification blocked"));
      } else {
        toast({
          title: "Verification Failed",
          description: `Invalid code. ${3 - newAttempts} attempts remaining.`,
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCodeChange = (value: string) => {
    // Only allow digits and limit to 6 characters
    const cleanValue = value.replace(/\D/g, '').slice(0, 6);
    setState(prev => ({ ...prev, code: cleanValue }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && state.code.length === 6 && !isLoading) {
      verifyCode();
    }
  };

  const renderMethodSelection = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Shield className="h-12 w-12 text-[#4A6741] mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Additional Security Required</h3>
        <p className="text-gray-600 text-sm">
          This transaction requires two-factor authentication for security.
        </p>
      </div>

      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>High-value transaction detected</strong><br />
          Amount: ₹{data.amount.toLocaleString()}<br />
          Property: {data.propertyTitle}
        </AlertDescription>
      </Alert>

      <div className="space-y-3">
        <Button
          onClick={() => sendVerificationCode('sms')}
          disabled={isLoading || state.isBlocked}
          className="w-full flex items-center justify-center space-x-2"
          variant="outline"
        >
          <Smartphone className="h-4 w-4" />
          <span>Send SMS Code</span>
        </Button>

        <Button
          onClick={() => sendVerificationCode('email')}
          disabled={isLoading || state.isBlocked}
          className="w-full flex items-center justify-center space-x-2"
          variant="outline"
        >
          <Mail className="h-4 w-4" />
          <span>Send Email Code</span>
        </Button>
      </div>

      {state.isBlocked && (
        <Alert variant="destructive">
          <AlertDescription>
            Too many failed attempts. Please wait before trying again.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );

  const renderCodeEntry = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Shield className="h-12 w-12 text-[#4A6741] mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Enter Verification Code</h3>
        <p className="text-gray-600 text-sm">
          We sent a 6-digit code to {maskedContact}
        </p>
      </div>

      <div className="space-y-3">
        <Input
          ref={codeInputRef}
          type="text"
          placeholder="000000"
          value={state.code}
          onChange={(e) => handleCodeChange(e.target.value)}
          onKeyPress={handleKeyPress}
          className="text-center text-2xl tracking-widest font-mono"
          maxLength={6}
          disabled={isLoading || state.step === 'verifying'}
        />

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">
            {state.attempts > 0 && `${3 - state.attempts} attempts remaining`}
          </span>
          <span className="flex items-center space-x-1 text-gray-500">
            <Clock className="h-4 w-4" />
            <span>{formatTime(state.timeLeft)}</span>
          </span>
        </div>

        <Button
          onClick={verifyCode}
          disabled={state.code.length !== 6 || isLoading}
          className="w-full bg-[#4A6741] hover:bg-[#3A5235]"
        >
          {state.step === 'verifying' ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Verifying...
            </>
          ) : (
            'Verify Code'
          )}
        </Button>

        <Button
          onClick={() => setState(prev => ({ ...prev, step: 'select_method', code: '' }))}
          variant="outline"
          className="w-full"
          disabled={isLoading}
        >
          Use Different Method
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Shield className="h-5 w-5" />
        <h3 className="font-semibold text-lg">Two-Factor Authentication</h3>
      </div>
      
      <div className="py-4">
        {state.step === 'select_method' && renderMethodSelection()}
        {(state.step === 'enter_code' || state.step === 'verifying') && renderCodeEntry()}
      </div>
    </div>
  );
}

// Original modal component for backward compatibility
export default function TwoFactorModal({
  isOpen,
  onClose,
  onSuccess,
  onFailure,
  data,
  preferredMethod = 'sms'
}: TwoFactorModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Two-Factor Authentication
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <TwoFactorContent
            onClose={onClose}
            onSuccess={onSuccess}
            onFailure={onFailure}
            data={data}
            preferredMethod={preferredMethod}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}