import { Property } from "@shared/schema";
import PropertyCard from "./PropertyCard";
import LoadingSpinner from "./LoadingSpinner";
import { Skeleton } from "@/components/ui/skeleton";

interface PropertyListProps {
  properties: Property[] | undefined;
  isLoading: boolean;
}

export default function PropertyList({ properties, isLoading }: PropertyListProps) {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-16 space-y-8">
        {/* Main farmhouse loader */}
        <LoadingSpinner 
          size="xl" 
          theme="tractor" 
          text="Searching for perfect farmhouses..."
          className="mb-8"
        />

        {/* Multiple themed loaders for variety */}
        <div className="flex justify-center space-x-12">
          <LoadingSpinner size="lg" theme="windmill" />
          <LoadingSpinner size="lg" theme="barn" />
          <LoadingSpinner size="lg" theme="harvest" />
        </div>

        {/* Subtle skeleton cards for context */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 opacity-30 max-w-4xl">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white rounded-xl overflow-hidden shadow-lg animate-pulse">
              <Skeleton className="w-full h-32 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200" />
              <div className="p-3 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!properties || properties.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="max-w-md mx-auto">
          {/* No Results Icon */}
          <div className="w-24 h-24 mx-auto mb-6 bg-[#F7F4F1] rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-[#766C63]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>

          <h3 className="text-2xl font-bold text-[#2D3C2D] mb-3">No properties found</h3>
          <p className="text-[#605045] mb-6">We couldn't find any farmhouses matching your criteria. Try adjusting your search filters.</p>

          {/* Suggestions */}
          <div className="text-left bg-[#F7F4F1] rounded-lg p-4">
            <h4 className="font-semibold text-[#2D3C2D] mb-2">Try:</h4>
            <ul className="text-sm text-[#605045] space-y-1">
              <li>• Expanding your date range</li>
              <li>• Selecting a different location</li>
              <li>• Removing some filters</li>
              <li>• Browsing all available properties</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  const displayProperties = properties || [];

  return (
    <div className="space-y-6">
      {/* Results Count */}
      <div className="flex justify-between items-center">
        <p className="text-[#605045]">
          Showing <span className="font-semibold text-[#2D3C2D]">{displayProperties.length}</span> 
          {displayProperties.length === 1 ? ' property' : ' properties'}
        </p>
      </div>

      {/* Property Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {displayProperties.map((property, index) => (
          <div 
            key={property.id} 
            className="animate-fade-in-up"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <PropertyCard property={property} />
          </div>
        ))}
      </div>
    </div>
  );
}