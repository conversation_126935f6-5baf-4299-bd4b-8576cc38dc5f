import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  TrendingUp, 
  Activity, 
  Clock, 
  Database,
  CheckCircle,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import { usePerformanceMonitoring, useN1Detection } from '../hooks/usePerformanceMonitoring';

/**
 * ✅ PERFORMANCE DASHBOARD: Monitor N+1 Query Optimization Results
 * 
 * This component displays real-time performance metrics showing the impact
 * of solving the N+1 query problem in the Owner Dashboard.
 */
export const PerformanceDashboard: React.FC = () => {
  const { summary } = usePerformanceMonitoring();
  const n1Patterns = useN1Detection();

  if (!summary) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 animate-pulse" />
            <span>Loading performance metrics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatPercent = (value: number) => `${Math.round(value)}%`;

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 90) return 'text-green-600';
    if (efficiency >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getEfficiencyBadgeColor = (efficiency: number) => {
    if (efficiency >= 90) return 'bg-green-100 text-green-800';
    if (efficiency >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Zap className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold">Performance Optimization Dashboard</h2>
      </div>

      {/* N+1 Query Status */}
      <Card className={n1Patterns.count > 0 ? 'border-orange-200 bg-orange-50' : 'border-green-200 bg-green-50'}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {n1Patterns.count > 0 ? (
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              ) : (
                <CheckCircle className="h-8 w-8 text-green-600" />
              )}
              <div>
                <h3 className="text-lg font-semibold">
                  {n1Patterns.count > 0 ? 'N+1 Patterns Detected' : 'N+1 Optimization Active'}
                </h3>
                <p className="text-sm text-gray-600">
                  {n1Patterns.count > 0 
                    ? `${n1Patterns.count} potential N+1 query patterns detected`
                    : 'All database queries are optimized with batch operations'
                  }
                </p>
              </div>
            </div>
            <Badge className={n1Patterns.count > 0 ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}>
              {n1Patterns.count > 0 ? 'Needs Attention' : 'Optimized'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Load Time</p>
                <p className="text-2xl font-bold">
                  {formatTime(summary.avgDashboardLoadTime)}
                </p>
                <p className="text-xs text-gray-500">Dashboard initialization</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-green-100 rounded-lg">
                <Database className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">API Calls (5 min)</p>
                <p className="text-2xl font-bold">{summary.totalAPICallsLast5Min}</p>
                <p className="text-xs text-gray-500">Network requests</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Cache Hit Rate</p>
                <p className="text-2xl font-bold">{formatPercent(summary.cacheHitRate)}</p>
                <p className="text-xs text-gray-500">Cached responses</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-orange-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">N+1 Patterns</p>
                <p className="text-2xl font-bold">{summary.n1PatternsDetected}</p>
                <p className="text-xs text-gray-500">Last 5 minutes</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Optimization Results */}
      {summary.optimizationStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Optimization Impact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">API Call Reduction</span>
                  <span className="text-lg font-bold text-green-600">
                    -{formatPercent(summary.optimizationStats.improvements.apiCallReduction)}
                  </span>
                </div>
                <Progress value={summary.optimizationStats.improvements.apiCallReduction} className="h-2" />
                <div className="text-xs text-gray-500">
                  From {summary.optimizationStats.beforeOptimization.apiCalls} to{' '}
                  {summary.optimizationStats.afterOptimization.apiCalls} calls
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Load Time Improvement</span>
                  <span className="text-lg font-bold text-green-600">
                    -{formatPercent(summary.optimizationStats.improvements.loadTimeImprovement)}
                  </span>
                </div>
                <Progress value={summary.optimizationStats.improvements.loadTimeImprovement} className="h-2" />
                <div className="text-xs text-gray-500">
                  From {formatTime(summary.optimizationStats.beforeOptimization.totalLoadTime)} to{' '}
                  {formatTime(summary.optimizationStats.afterOptimization.totalLoadTime)}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Payload Reduction</span>
                  <span className="text-lg font-bold text-green-600">
                    -{formatPercent(summary.optimizationStats.improvements.payloadReduction)}
                  </span>
                </div>
                <Progress value={summary.optimizationStats.improvements.payloadReduction} className="h-2" />
                <div className="text-xs text-gray-500">
                  Network data transfer optimized
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Dashboard Loads */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Dashboard Loads
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {summary.recentDashboardLoads.slice(0, 5).map((load, index) => {
              const efficiency = load.efficiency || 0;
              return (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="text-sm">
                      <div className="font-medium">
                        {load.propertyCount} properties loaded
                      </div>
                      <div className="text-gray-500">
                        {new Date(load.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {formatTime(load.totalLoadTime)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {load.apiCallsCount} API calls
                      </div>
                    </div>
                    
                    <Badge className={getEfficiencyBadgeColor(efficiency)}>
                      <span className={getEfficiencyColor(efficiency)}>
                        {formatPercent(efficiency)} efficient
                      </span>
                    </Badge>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* API Endpoint Usage */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            API Endpoint Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(summary.apiCallsByEndpoint)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 8)
              .map(([endpoint, count]) => (
                <div key={endpoint} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="font-mono text-sm truncate flex-1 mr-4">
                    {endpoint}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{count} calls</span>
                    {endpoint.includes('/by-property-ids') && (
                      <Badge className="bg-green-100 text-green-800">
                        Optimized
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};