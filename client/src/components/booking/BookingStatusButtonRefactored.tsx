import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { CheckCircle, XCircle } from "lucide-react";

interface BookingStatusButtonRefactoredProps {
  bookingId: number;
  status: string;
  onStatusUpdate: (bookingId: number, status: string) => void;
  isActionInProgress: boolean;
}

/**
 * Refactored BookingStatusButton Component
 * 
 * ✅ This component had minimal prop drilling originally
 * ✅ Kept the same interface for consistency
 * ✅ Could be further enhanced with context if needed
 * 
 * Benefits:
 * - Clean, focused component
 * - Single responsibility
 * - Easy to test and maintain
 */
export const BookingStatusButtonRefactored: React.FC<BookingStatusButtonRefactoredProps> = ({
  bookingId,
  status,
  onStatusUpdate,
  isActionInProgress
}) => {
  if (status !== 'pending') {
    return null;
  }

  return (
    <div className="flex gap-2">
      <Button
        size="sm"
        variant="outline"
        onClick={() => onStatusUpdate(bookingId, 'rejected')}
        disabled={isActionInProgress}
      >
        <XCircle className="h-4 w-4 mr-2" />
        Reject
      </Button>
      <Button
        size="sm"
        onClick={() => onStatusUpdate(bookingId, 'confirmed')}
        disabled={isActionInProgress}
      >
        {isActionInProgress ? (
          <>
            <Skeleton className="h-4 w-4 mr-2" />
            Processing...
          </>
        ) : (
          <>
            <CheckCircle className="h-4 w-4 mr-2" />
            Confirm
          </>
        )}
      </Button>
    </div>
  );
};

/**
 * 🎯 Component Analysis:
 * 
 * This component was already well-designed with minimal prop drilling.
 * The props it receives are all essential for its functionality:
 * 
 * ✅ bookingId - Required to identify which booking to update
 * ✅ status - Required to determine if buttons should be shown  
 * ✅ onStatusUpdate - Required callback for button actions
 * ✅ isActionInProgress - Required for loading state
 * 
 * No further optimization needed - this demonstrates good component design!
 */