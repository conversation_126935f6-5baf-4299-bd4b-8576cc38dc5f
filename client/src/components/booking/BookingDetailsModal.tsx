import React from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { MapPin, Mail, Phone, Calendar, Clock, Users, IndianRupee } from "lucide-react";
import { format } from "date-fns";
import { BookingWithPropertyAndGuest } from "@/types/booking";

interface BookingDetailsModalProps {
  booking: BookingWithPropertyAndGuest | null;
  isOpen: boolean;
  onClose: () => void;
  formatPrice: (price: number) => string;
  getStatusColor: (status: string) => string;
}

export const BookingDetailsModal: React.FC<BookingDetailsModalProps> = ({
  booking,
  isOpen,
  onClose,
  formatPrice,
  getStatusColor
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Booking Details</DialogTitle>
          <DialogDescription>
            Complete information about this booking request
          </DialogDescription>
        </DialogHeader>
        {booking && (
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900">Property</h4>
              <p className="text-sm text-gray-600">{booking.property.title}</p>
              <p className="text-sm text-gray-600 flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {booking.property.location}
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900">Guest Information</h4>
              <p className="text-sm text-gray-600">{booking.guest.fullName}</p>
              <p className="text-sm text-gray-600 flex items-center">
                <Mail className="h-4 w-4 mr-1" />
                {booking.guest.email}
              </p>
              {booking.guest.phone && (
                <p className="text-sm text-gray-600 flex items-center">
                  <Phone className="h-4 w-4 mr-1" />
                  {booking.guest.phone}
                </p>
              )}
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900">Booking Details</h4>
              <p className="text-sm text-gray-600 flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                {format(new Date(booking.bookingDate), 'MMMM dd, yyyy')}
              </p>
              <p className="text-sm text-gray-600 flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {booking.bookingType === 'full_day' ? 'Full Day' : 'Morning Only'}
              </p>
              <p className="text-sm text-gray-600 flex items-center">
                <Users className="h-4 w-4 mr-1" />
                {booking.guests} guests
              </p>
              <p className="text-sm text-gray-600 flex items-center">
                <IndianRupee className="h-4 w-4 mr-1" />
                {formatPrice(booking.totalPrice)}
              </p>
            </div>
            
            {booking.specialRequests && (
              <div>
                <h4 className="font-semibold text-gray-900">Special Requests</h4>
                <p className="text-sm text-gray-600">{booking.specialRequests}</p>
              </div>
            )}
            
            <div className="flex items-center justify-between pt-4">
              <Badge className={getStatusColor(booking.status)}>
                {booking.status}
              </Badge>
              <p className="text-xs text-gray-500">
                Booked {format(new Date(booking.createdAt), 'MMM dd, yyyy')}
              </p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};