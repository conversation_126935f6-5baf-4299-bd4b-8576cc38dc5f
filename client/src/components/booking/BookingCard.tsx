import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Eye } from "lucide-react";
import { format } from "date-fns";
import { BookingStatusButton } from "./BookingStatusButton";
import { BookingWithPropertyAndGuest } from "@/types/booking";

interface BookingCardProps {
  booking: BookingWithPropertyAndGuest;
  isRecentlyUpdated: boolean;
  onViewDetails: (booking: BookingWithPropertyAndGuest) => void;
  onStatusUpdate: (bookingId: number, status: string) => void;
  isActionInProgress: boolean;
  formatPrice: (price: number) => string;
  getStatusColor: (status: string) => string;
}

export const BookingCard: React.FC<BookingCardProps> = ({
  booking,
  isRecentlyUpdated,
  onViewDetails,
  onStatusUpdate,
  isActionInProgress,
  formatPrice,
  getStatusColor
}) => {
  return (
    <div
      className={`border rounded-lg p-6 ${
        isRecentlyUpdated ? 'border-green-300 bg-green-50' : 'border-gray-200'
      } transition-all duration-300`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {booking.property.title}
              </h3>
              <p className="text-sm text-gray-600 flex items-center mt-1">
                <MapPin className="h-4 w-4 mr-1" />
                {booking.property.location}
              </p>
            </div>
            <Badge className={getStatusColor(booking.status)}>
              {booking.status}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div>
              <p className="text-sm text-gray-500">Guest</p>
              <p className="font-medium text-gray-900">{booking.guest.fullName}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Date</p>
              <p className="font-medium text-gray-900">
                {format(new Date(booking.bookingDate), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <p className="font-medium text-gray-900">
                {booking.bookingType === 'full_day' ? 'Full Day' : 'Morning Only'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Amount</p>
              <p className="font-medium text-gray-900">{formatPrice(booking.totalPrice)}</p>
            </div>
          </div>
          
          {booking.specialRequests && (
            <div className="mt-4">
              <p className="text-sm text-gray-500">Special Requests</p>
              <p className="text-sm text-gray-700 mt-1">{booking.specialRequests}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center justify-between mt-6 pt-4 border-t">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewDetails(booking)}
        >
          <Eye className="h-4 w-4 mr-2" />
          View Details
        </Button>
        
        <BookingStatusButton
          bookingId={booking.id}
          status={booking.status}
          onStatusUpdate={onStatusUpdate}
          isActionInProgress={isActionInProgress}
        />
      </div>
    </div>
  );
};