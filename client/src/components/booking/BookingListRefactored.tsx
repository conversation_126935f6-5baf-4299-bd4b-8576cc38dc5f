import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar } from "lucide-react";
import { BookingFilters } from "./BookingFilters";
import { BookingCardRefactored } from "./BookingCardRefactored";
import { BookingDetailsModal } from "./BookingDetailsModal";
import { useDashboard } from "@/contexts/DashboardContext";
import { BookingWithPropertyAndGuest, BookingListData } from "@/types/booking";

const BOOKINGS_PER_PAGE = 10;

/**
 * Refactored BookingList Component - No More Prop Drilling!
 * 
 * ✅ BEFORE: Required 4+ props drilled from parent
 * ✅ AFTER: Uses context hooks - zero prop drilling
 * 
 * Benefits:
 * - Clean component interface
 * - Better testability
 * - Improved maintainability
 * - Reduced coupling
 */
export const BookingListRefactored: React.FC = () => {
  // ✅ No props needed - everything comes from context!
  const { 
    getAuthHeaders, 
    updateBookingOptimistically, 
    handleAuthError,
    showErrorToast,
    formatPrice,
    getStatusColor
  } = useDashboard();
  
  const queryClient = useQueryClient();
  
  // State management
  const [bookingStatusFilter, setBookingStatusFilter] = useState<string>('all');
  const [bookingPage, setBookingPage] = useState(1);
  const [showBookingDetails, setShowBookingDetails] = useState<BookingWithPropertyAndGuest | null>(null);
  const [actionInProgress, setActionInProgress] = useState<number | null>(null);
  const [recentlyUpdated, setRecentlyUpdated] = useState<Set<number>>(new Set());

  // Bookings query
  const { data: bookingsData, isLoading: bookingsLoading, error: bookingsError } = useQuery<BookingListData>({
    queryKey: ['/api/bookings/owner', bookingPage, bookingStatusFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: bookingPage.toString(),
        limit: BOOKINGS_PER_PAGE.toString(),
        ...(bookingStatusFilter !== 'all' && { status: bookingStatusFilter })
      });
      
      const response = await fetch(`/api/bookings/owner/me?${params}`, {
        credentials: 'include',
        headers: getAuthHeaders()
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        if (response.status === 403) {
          throw new Error('You are not authorized to view bookings');
        }
        throw new Error('Failed to fetch bookings');
      }
      
      const result = await response.json();
      return {
        bookings: result.data || result,
        total: result.total || (result.data || result).length,
        hasMore: (result.data || result).length === BOOKINGS_PER_PAGE
      };
    },
    placeholderData: (previousData: any) => previousData
  });

  // Update booking status mutation
  const updateBookingStatus = useMutation({
    mutationFn: async ({ bookingId, status }: { bookingId: number; status: string }) => {
      setActionInProgress(bookingId);
      
      updateBookingOptimistically(bookingId, status, () => {
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
      });
      
      const response = await fetch(`/api/bookings/${bookingId}/status`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        credentials: 'include',
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          throw new Error('You are not authorized to update this booking');
        }
        throw new Error('Failed to update booking status');
      }
      return response.json();
    },
    onSuccess: (_, { bookingId, status }) => {
      setActionInProgress(null);
      setRecentlyUpdated(prev => new Set(Array.from(prev).concat(bookingId)));
      
      setTimeout(() => {
        setRecentlyUpdated(prev => {
          const newSet = new Set(prev);
          newSet.delete(bookingId);
          return newSet;
        });
      }, 3000);
      
      queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
    },
    onError: (error: any) => {
      setActionInProgress(null);
      if (error.message?.includes('authorized')) {
        handleAuthError(error, 'update this booking');
      } else {
        showErrorToast('❌ Update Failed', 'Unable to update booking status. Please try again.');
      }
    }
  });

  // Reset page when filter changes
  React.useEffect(() => {
    setBookingPage(1);
  }, [bookingStatusFilter]);

  const bookings = bookingsData?.bookings || [];

  const handleStatusUpdate = (bookingId: number, status: string) => {
    updateBookingStatus.mutate({ bookingId, status });
  };

  const handleViewDetails = (booking: BookingWithPropertyAndGuest) => {
    setShowBookingDetails(booking);
  };

  const handleCloseDetails = () => {
    setShowBookingDetails(null);
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <BookingFilters
            statusFilter={bookingStatusFilter}
            onStatusFilterChange={setBookingStatusFilter}
          />
        </div>
        
        <div className="p-6">
          {bookingsLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-32" />
              ))}
            </div>
          ) : bookings.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
              <p className="mt-1 text-sm text-gray-500">Your booking requests will appear here.</p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {bookings.map((booking) => (
                  <BookingCardRefactored
                    key={booking.id}
                    booking={booking}
                    isRecentlyUpdated={recentlyUpdated.has(booking.id)}
                    onViewDetails={handleViewDetails}
                    onStatusUpdate={handleStatusUpdate}
                    isActionInProgress={actionInProgress === booking.id}
                  />
                ))}
              </div>
              
              {/* Pagination */}
              {(bookingsData?.hasMore || bookingPage > 1) && (
                <div className="flex items-center justify-between mt-6 pt-4 border-t">
                  <div className="text-sm text-gray-600">
                    Showing page {bookingPage} {bookingsData?.total && `of ${Math.ceil(bookingsData.total / BOOKINGS_PER_PAGE)} pages`}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setBookingPage(prev => Math.max(1, prev - 1))}
                      disabled={bookingPage <= 1 || bookingsLoading}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setBookingPage(prev => prev + 1)}
                      disabled={!bookingsData?.hasMore || bookingsLoading}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={showBookingDetails}
        isOpen={!!showBookingDetails}
        onClose={handleCloseDetails}
        formatPrice={formatPrice}
        getStatusColor={getStatusColor}
      />
    </div>
  );
};

/**
 * 🎯 Component Comparison:
 * 
 * BEFORE (BookingList):
 * ```typescript
 * interface BookingListProps {
 *   getAuthHeaders: () => HeadersInit;           // ❌ Prop drilling
 *   updateBookingOptimistically: Function;      // ❌ Prop drilling
 *   playNotificationSound: Function;            // ❌ Prop drilling
 *   handleAuthError: Function;                  // ❌ Prop drilling
 * }
 * ```
 * 
 * AFTER (BookingListRefactored):
 * ```typescript
 * // ✅ No props interface needed!
 * export const BookingListRefactored: React.FC = () => {
 *   const { getAuthHeaders, updateBookingOptimistically, ... } = useDashboard();
 * }
 * ```
 * 
 * Benefits:
 * ✅ Zero prop drilling
 * ✅ Cleaner component interface
 * ✅ Better testability (can mock context)
 * ✅ Improved reusability
 * ✅ Reduced coupling between parent and child
 */