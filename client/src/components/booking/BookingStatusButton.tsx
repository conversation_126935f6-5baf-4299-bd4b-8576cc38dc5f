import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { CheckCircle, XCircle } from "lucide-react";

interface BookingStatusButtonProps {
  bookingId: number;
  status: string;
  onStatusUpdate: (bookingId: number, status: string) => void;
  isActionInProgress: boolean;
}

export const BookingStatusButton: React.FC<BookingStatusButtonProps> = ({
  bookingId,
  status,
  onStatusUpdate,
  isActionInProgress
}) => {
  if (status !== 'pending') {
    return null;
  }

  return (
    <div className="flex gap-2">
      <Button
        size="sm"
        variant="outline"
        onClick={() => onStatusUpdate(bookingId, 'rejected')}
        disabled={isActionInProgress}
      >
        <XCircle className="h-4 w-4 mr-2" />
        Reject
      </Button>
      <Button
        size="sm"
        onClick={() => onStatusUpdate(bookingId, 'confirmed')}
        disabled={isActionInProgress}
      >
        {isActionInProgress ? (
          <>
            <Skeleton className="h-4 w-4 mr-2" />
            Processing...
          </>
        ) : (
          <>
            <CheckCircle className="h-4 w-4 mr-2" />
            Confirm
          </>
        )}
      </Button>
    </div>
  );
};