import { useState } from "react";
import { Button } from "@/components/ui/button";
import { MapPin, X } from "lucide-react";

interface LocationFilterProps {
  selectedLocation: string;
  onLocationSelect: (location: string) => void;
  onClearLocation: () => void;
}

export default function LocationFilter({ 
  selectedLocation, 
  onLocationSelect, 
  onClearLocation 
}: LocationFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const telanganaLocations = [
    {
      name: "Shankarpal<PERSON>",
      description: "Hyderabad-Bangalore Highway",
      count: 3,
      popular: true
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>", 
      description: "Medchal-Malkajgiri District",
      count: 2,
      popular: true
    },
    {
      name: "Gandipet",
      description: "Ranga Reddy District", 
      count: 2,
      popular: false
    },
    {
      name: "Patancheru",
      description: "Shankarpally Road",
      count: 1,
      popular: false
    },
    {
      name: "Chevella",
      description: "Ranga Reddy District",
      count: 1,
      popular: false
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      description: "Shameerpet Village",
      count: 1,
      popular: false
    }
  ];

  const popularLocations = telanganaLocations.filter(loc => loc.popular);
  const otherLocations = telanganaLocations.filter(loc => !loc.popular);

  const handleLocationClick = (locationName: string) => {
    onLocationSelect(locationName);
    setIsExpanded(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-[#EBE6E1] p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <MapPin className="h-5 w-5 text-[#4A6741] mr-2" />
          <h3 className="text-lg font-semibold text-[#2D3C2D]">
            Popular Locations in Telangana
          </h3>
        </div>
        
        {selectedLocation && (
          <Button
            onClick={onClearLocation}
            variant="outline"
            size="sm"
            className="text-[#605045] border-[#D5CCC4] hover:bg-[#F7F4F1]"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      {/* Popular Locations - Always Visible */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
        {popularLocations.map((location) => (
          <button
            key={location.name}
            onClick={() => handleLocationClick(location.name)}
            className={`p-3 rounded-lg border text-left transition-all duration-200 hover:shadow-md ${
              selectedLocation.includes(location.name)
                ? 'bg-[#4A6741] text-white border-[#4A6741]'
                : 'bg-white border-[#D5CCC4] hover:border-[#4A6741] hover:bg-[#F7F4F1]'
            }`}
          >
            <div className="flex items-center justify-between mb-1">
              <span className="font-semibold">{location.name}</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                selectedLocation.includes(location.name)
                  ? 'bg-white/20 text-white'
                  : 'bg-[#4A6741] text-white'
              }`}>
                {location.count} farms
              </span>
            </div>
            <p className={`text-sm ${
              selectedLocation.includes(location.name)
                ? 'text-white/80'
                : 'text-[#605045]'
            }`}>
              {location.description}
            </p>
          </button>
        ))}
      </div>

      {/* Expand/Collapse for More Locations */}
      <div className="border-t border-[#EBE6E1] pt-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-[#4A6741] hover:text-[#3A5131] font-medium text-sm flex items-center"
        >
          {isExpanded ? 'Show Less' : 'Show More Locations'}
          <svg 
            className={`ml-1 h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {/* Additional Locations - Expandable */}
        {isExpanded && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-4">
            {otherLocations.map((location) => (
              <button
                key={location.name}
                onClick={() => handleLocationClick(location.name)}
                className={`p-3 rounded-lg border text-left transition-all duration-200 hover:shadow-md ${
                  selectedLocation.includes(location.name)
                    ? 'bg-[#4A6741] text-white border-[#4A6741]'
                    : 'bg-white border-[#D5CCC4] hover:border-[#4A6741] hover:bg-[#F7F4F1]'
                }`}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="font-semibold">{location.name}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    selectedLocation.includes(location.name)
                      ? 'bg-white/20 text-white'
                      : 'bg-[#4A6741] text-white'
                  }`}>
                    {location.count} farm
                  </span>
                </div>
                <p className={`text-sm ${
                  selectedLocation.includes(location.name)
                    ? 'text-white/80'
                    : 'text-[#605045]'
                }`}>
                  {location.description}
                </p>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Selected Location Indicator */}
      {selectedLocation && (
        <div className="mt-4 p-3 bg-[#F7F4F1] rounded-lg border border-[#4A6741]/20">
          <div className="flex items-center">
            <MapPin className="h-4 w-4 text-[#4A6741] mr-2" />
            <span className="text-sm text-[#2D3C2D]">
              Showing farmhouses in: <strong>{selectedLocation}</strong>
            </span>
          </div>
        </div>
      )}
    </div>
  );
}