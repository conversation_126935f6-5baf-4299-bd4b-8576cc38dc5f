import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ValidationService, validationSchemas } from '@/lib/validation';
import type { PropertyFormData, BookingFormData, ReviewFormData } from '@/lib/validation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Eye, EyeOff, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

// Real-time validation indicator component
interface ValidationIndicatorProps {
  isValid: boolean;
  isValidating?: boolean;
  message?: string;
}

const ValidationIndicator: React.FC<ValidationIndicatorProps> = ({ isValid, isValidating, message }) => {
  if (isValidating) {
    return <AlertCircle className="h-4 w-4 text-yellow-500 animate-spin" />;
  }
  
  if (isValid) {
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  }
  
  return <XCircle className="h-4 w-4 text-red-500" />;
};

// Password strength indicator
interface PasswordStrengthProps {
  password: string;
}

const PasswordStrength: React.FC<PasswordStrengthProps> = ({ password }) => {
  const strength = ValidationService.getPasswordStrength(password);
  
  const getStrengthColor = (score: number) => {
    if (score <= 1) return 'bg-red-500';
    if (score <= 2) return 'bg-orange-500';
    if (score <= 3) return 'bg-yellow-500';
    if (score <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };
  
  const getStrengthText = (score: number) => {
    if (score <= 1) return 'Very Weak';
    if (score <= 2) return 'Weak';
    if (score <= 3) return 'Fair';
    if (score <= 4) return 'Good';
    return 'Strong';
  };
  
  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor(strength.score)}`}
            style={{ width: `${(strength.score / 5) * 100}%` }}
          />
        </div>
        <Badge variant={strength.isValid ? 'default' : 'destructive'}>
          {getStrengthText(strength.score)}
        </Badge>
      </div>
      {strength.feedback.length > 0 && (
        <ul className="text-sm text-gray-600 space-y-1">
          {strength.feedback.map((item, index) => (
            <li key={index} className="flex items-center space-x-1">
              <XCircle className="h-3 w-3 text-red-500" />
              <span>{item}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

// Property form with shared validation
export const ValidatedPropertyForm: React.FC<{
  onSubmit: (data: PropertyFormData) => Promise<void>;
  defaultValues?: Partial<PropertyFormData>;
}> = ({ onSubmit, defaultValues }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<PropertyFormData>({
    resolver: zodResolver(validationSchemas.propertyForm),
    defaultValues: {
      title: '',
      description: '',
      location: '',
      halfDayPrice: 0,
      fullDayPrice: 0,
      bedrooms: 1,
      bathrooms: 1,
      amenities: [],
      images: [],
      featured: false,
      ...defaultValues
    }
  });

  const handleSubmit = async (data: PropertyFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      
      // Client-side validation (redundant but shows consistency)
      const validatedData = ValidationService.validatePropertyForm(data);
      
      await onSubmit(validatedData);
      form.reset();
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Submission failed');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {submitError && (
          <Alert variant="destructive">
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Property Title</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input {...field} placeholder="Enter property title" />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <ValidationIndicator 
                      isValid={!form.formState.errors.title && field.value.length > 0}
                    />
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input {...field} placeholder="Property location" />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <ValidationIndicator 
                      isValid={!form.formState.errors.location && field.value.length > 0}
                    />
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="halfDayPrice"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Half Day Price (₹)</FormLabel>
                <FormControl>
                  <Input 
                    {...field}
                    type="number"
                    min="1"
                    onChange={e => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="fullDayPrice"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Day Price (₹)</FormLabel>
                <FormControl>
                  <Input 
                    {...field}
                    type="number"
                    min="1"
                    onChange={e => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  {...field}
                  placeholder="Describe your property..."
                  rows={4}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting ? 'Submitting...' : 'Create Property'}
        </Button>
      </form>
    </Form>
  );
};

// Booking form with shared validation
export const ValidatedBookingForm: React.FC<{
  propertyId: number;
  onSubmit: (data: BookingFormData) => Promise<void>;
}> = ({ propertyId, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<BookingFormData>({
    resolver: zodResolver(validationSchemas.bookingForm),
    defaultValues: {
      propertyId,
      bookingDate: '',
      bookingType: 'full_day',
      guests: 1,
      specialRequests: ''
    }
  });

  const handleSubmit = async (data: BookingFormData) => {
    try {
      setIsSubmitting(true);
      
      // Validate date is in the future
      if (!ValidationService.isValidFutureDate(data.bookingDate)) {
        form.setError('bookingDate', { message: 'Date cannot be in the past' });
        return;
      }
      
      await onSubmit(data);
      form.reset({ propertyId });
    } catch (error) {
      console.error('Booking submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="bookingDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Booking Date</FormLabel>
              <FormControl>
                <Input {...field} type="date" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="bookingType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Booking Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="morning">Morning (Half Day)</SelectItem>
                  <SelectItem value="full_day">Full Day</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="guests"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Guests</FormLabel>
              <FormControl>
                <Input 
                  {...field}
                  type="number"
                  min="1"
                  max="50"
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting ? 'Booking...' : 'Book Now'}
        </Button>
      </form>
    </Form>
  );
};

// Review form with shared validation
export const ValidatedReviewForm: React.FC<{
  propertyId: number;
  onSubmit: (data: ReviewFormData) => Promise<void>;
}> = ({ propertyId, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(validationSchemas.reviewForm),
    defaultValues: {
      propertyId,
      rating: '5',
      comment: ''
    }
  });

  const handleSubmit = async (data: ReviewFormData) => {
    try {
      setIsSubmitting(true);
      await onSubmit(data);
      form.reset({ propertyId });
    } catch (error) {
      console.error('Review submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="rating"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rating</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="1">1 Star</SelectItem>
                  <SelectItem value="2">2 Stars</SelectItem>
                  <SelectItem value="3">3 Stars</SelectItem>
                  <SelectItem value="4">4 Stars</SelectItem>
                  <SelectItem value="5">5 Stars</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="comment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Review Comment</FormLabel>
              <FormControl>
                <Textarea 
                  {...field}
                  placeholder="Share your experience..."
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting ? 'Submitting...' : 'Submit Review'}
        </Button>
      </form>
    </Form>
  );
};