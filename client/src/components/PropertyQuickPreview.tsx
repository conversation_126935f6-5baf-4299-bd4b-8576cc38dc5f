import React, { useState } from "react";
import { Property } from "@shared/schema";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";
import { X, MapPin, Users, Bath, Bed, Star, Heart } from "lucide-react";
import { useFavorites } from "@/contexts/FavoritesContext";

interface PropertyQuickPreviewProps {
  property: Property | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function PropertyQuickPreview({ property, isOpen, onClose }: PropertyQuickPreviewProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { isFavorite, toggleFavorite } = useFavorites();

  if (!property) return null;

  const {
    id,
    title,
    location,
    images,
    halfDayPrice,
    fullDayPrice,
    bedrooms,
    bathrooms,
    amenities,
    description,
    featured
  } = property;

  const handlePrevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleFavoriteClick = () => {
    toggleFavorite(id);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0" aria-describedby="property-preview-description">
        <div className="relative">
          {/* Image Gallery */}
          <div className="relative h-64 md:h-80 overflow-hidden">
            <div
              className="w-full h-full bg-cover bg-center"
              style={{ 
                backgroundImage: `url(${images[currentImageIndex] || "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80"})` 
              }}
              aria-label={title}
            ></div>
            
            {/* Image Navigation */}
            {images.length > 1 && (
              <>
                <button
                  onClick={handlePrevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-all"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={handleNextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-all"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                {currentImageIndex + 1} / {images.length}
              </div>
            )}

            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-all"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Favorite Button */}
            <button
              onClick={handleFavoriteClick}
              className={`absolute top-4 left-4 p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
                isFavorite(id) 
                  ? 'bg-red-500 text-white shadow-lg' 
                  : 'bg-black/50 text-white hover:bg-black/70'
              }`}
            >
              <Heart 
                className={`w-5 h-5 ${isFavorite(id) ? 'fill-current' : ''}`}
              />
            </button>

            {/* Featured Badge */}
            {featured && (
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-[#4A6741] text-white">Featured</Badge>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6">
            <DialogHeader className="mb-4">
              <DialogTitle className="text-2xl font-bold text-[#2D3C2D] mb-2">{title}</DialogTitle>
              <DialogDescription id="property-preview-description" className="text-[#605045]">
                Quick preview of {title} farmhouse property in {location}
              </DialogDescription>
              <div className="flex items-center text-[#605045] mb-4">
                <MapPin className="w-4 h-4 mr-2" />
                <span>{location}</span>
              </div>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                {/* Property Details */}
                <div className="flex items-center gap-4 text-sm text-[#605045]">
                  <div className="flex items-center gap-1">
                    <Bed className="w-4 h-4" />
                    <span>{bedrooms} Bedrooms</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Bath className="w-4 h-4" />
                    <span>{bathrooms} Bathrooms</span>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h4 className="font-semibold text-[#2D3C2D] mb-2">About this property</h4>
                  <p className="text-[#605045] text-sm leading-relaxed">
                    {description || "Experience the perfect blend of rural charm and modern comfort at this beautiful farmhouse retreat."}
                  </p>
                </div>

                {/* Amenities */}
                <div>
                  <h4 className="font-semibold text-[#2D3C2D] mb-3">Amenities</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {amenities.slice(0, 6).map((amenity, index) => (
                      <div key={index} className="flex items-center text-sm text-[#605045]">
                        <div className="w-1.5 h-1.5 bg-[#4A6741] rounded-full mr-2"></div>
                        {amenity}
                      </div>
                    ))}
                    {amenities.length > 6 && (
                      <div className="text-sm text-[#4A6741] font-medium">
                        +{amenities.length - 6} more
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                {/* Pricing */}
                <div className="bg-[#F7F4F1] p-4 rounded-lg">
                  <h4 className="font-semibold text-[#2D3C2D] mb-3">Pricing</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-[#605045]">Morning Access (12h)</span>
                      <span className="font-bold text-[#4A6741]">₹{halfDayPrice.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-[#605045]">Full Day Access (24h)</span>
                      <span className="font-bold text-[#4A6741]">₹{fullDayPrice.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <Link href={`/property/${id}`}>
                    <Button className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white h-12 text-lg font-semibold">
                      View Full Details & Book
                    </Button>
                  </Link>
                  
                  <Button 
                    variant="outline" 
                    className="w-full border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white h-10"
                    onClick={onClose}
                  >
                    Close Preview
                  </Button>
                </div>

                {/* Quick Stats */}
                <div className="border-t pt-4 space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-[#605045]">Property ID</span>
                    <span className="font-medium">#{id}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-[#605045]">Rating</span>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">4.8</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}