import React, { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Shield, AlertTriangle, Lock, Eye, Phone, Mail, CreditCard } from "lucide-react";
import { auditLogger, SecurityEventType } from "@/lib/audit";
import { useSessionSecurity } from "@/lib/session";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

// Fraud Detection Types
export interface FraudDetectionResult {
  riskScore: number;
  factors: Array<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  blockedReasons?: string[];
  allowedActions: string[];
  recommendedActions: string[];
}

// Velocity Check Alert
interface VelocityCheckAlertProps {
  isTriggered: boolean;
  transactionCount: number;
  timeWindow: string;
  threshold: number;
  onProceed: () => void;
  onCancel: () => void;
}

export const VelocityCheckAlert: React.FC<VelocityCheckAlertProps> = ({
  isTriggered,
  transactionCount,
  timeWindow,
  threshold,
  onProceed,
  onCancel
}) => {
  if (!isTriggered) return null;

  return (
    <Alert variant="destructive" className="border-orange-200 bg-orange-50">
      <AlertTriangle className="h-4 w-4 text-orange-600" />
      <AlertTitle className="text-orange-800">High Transaction Velocity Detected</AlertTitle>
      <AlertDescription className="text-orange-700 space-y-2">
        <p>
          You've made <strong>{transactionCount}</strong> transactions in the last <strong>{timeWindow}</strong>.
          This exceeds our safety threshold of {threshold} transactions.
        </p>
        <div className="flex gap-2 pt-2">
          <Button 
            onClick={onProceed} 
            variant="outline" 
            size="sm"
            className="border-orange-300 text-orange-700 hover:bg-orange-100"
          >
            Continue Anyway
          </Button>
          <Button 
            onClick={onCancel} 
            size="sm"
            className="bg-orange-600 hover:bg-orange-700"
          >
            Cancel Transaction
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
};

// Device Authentication Modal
interface DeviceAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (deviceToken: string) => void;
  deviceFingerprint?: string;
}

export const DeviceAuthModal: React.FC<DeviceAuthModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  deviceFingerprint
}) => {
  const [step, setStep] = useState<'verify' | 'authenticate'>('verify');
  const [isLoading, setIsLoading] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const { toast } = useToast();
  const { getSecurityContext } = useSessionSecurity();

  const sendDeviceVerification = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest('POST', '/api/security/device/verify', {
        deviceFingerprint,
        requestVerification: true,
      });

      const result = await response.json();
      
      if (result.success) {
        setStep('authenticate');
        
        auditLogger.logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, {
          activity: 'device_verification_requested',
          deviceFingerprint,
        }, getSecurityContext() || undefined);

        toast({
          title: "Verification Code Sent",
          description: "Please check your phone or email for the verification code.",
        });
      }
    } catch (error) {
      toast({
        title: "Verification Failed",
        description: "Unable to send device verification code.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const verifyDevice = async () => {
    if (verificationCode.length !== 6) return;

    setIsLoading(true);
    try {
      const response = await apiRequest('POST', '/api/security/device/authenticate', {
        deviceFingerprint,
        verificationCode,
      });

      const result = await response.json();
      
      if (result.success) {
        auditLogger.logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, {
          activity: 'device_authentication_success',
          deviceFingerprint,
        }, getSecurityContext() || undefined);

        onSuccess(result.data.deviceToken);
      } else {
        throw new Error('Device verification failed');
      }
    } catch (error) {
      toast({
        title: "Verification Failed",
        description: "Invalid verification code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            Device Verification Required
          </DialogTitle>
        </DialogHeader>

        {step === 'verify' && (
          <div className="space-y-4">
            <Alert>
              <Eye className="h-4 w-4" />
              <AlertTitle>New Device Detected</AlertTitle>
              <AlertDescription>
                We don't recognize this device. For security, we need to verify it's really you.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                We'll send a verification code to your registered phone number or email.
              </p>
            </div>

            <Button 
              onClick={sendDeviceVerification}
              disabled={isLoading}
              className="w-full bg-[#4A6741] hover:bg-[#3A5235]"
            >
              {isLoading ? "Sending..." : "Send Verification Code"}
            </Button>
          </div>
        )}

        {step === 'authenticate' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="verification-code">Verification Code</Label>
              <Input
                id="verification-code"
                type="text"
                placeholder="000000"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="text-center text-xl tracking-wider"
                maxLength={6}
              />
            </div>

            <Button 
              onClick={verifyDevice}
              disabled={isLoading || verificationCode.length !== 6}
              className="w-full bg-[#4A6741] hover:bg-[#3A5235]"
            >
              {isLoading ? "Verifying..." : "Verify Device"}
            </Button>

            <Button 
              onClick={() => setStep('verify')}
              variant="outline"
              className="w-full"
              disabled={isLoading}
            >
              Resend Code
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

// Fraud Detection Risk Assessment Display
interface RiskAssessmentProps {
  result: FraudDetectionResult;
  className?: string;
}

export const RiskAssessmentDisplay: React.FC<RiskAssessmentProps> = ({
  result,
  className = ""
}) => {
  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-600 bg-red-50 border-red-200';
    if (score >= 60) return 'text-orange-600 bg-orange-50 border-orange-200';
    if (score >= 40) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-green-600 bg-green-50 border-green-200';
  };

  const getRiskLevel = (score: number) => {
    if (score >= 80) return 'High Risk';
    if (score >= 60) return 'Medium Risk';
    if (score >= 40) return 'Low Risk';
    return 'Normal';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className={`p-4 rounded-lg border ${getRiskColor(result.riskScore)}`}>
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold">Risk Assessment</h3>
          <div className="text-right">
            <div className="text-2xl font-bold">{result.riskScore}%</div>
            <div className="text-xs">{getRiskLevel(result.riskScore)}</div>
          </div>
        </div>
        
        {result.factors.length > 0 && (
          <div className="mt-3">
            <h4 className="text-sm font-medium mb-2">Risk Factors:</h4>
            <div className="space-y-1">
              {result.factors.map((factor, index) => (
                <div key={index} className="flex items-start gap-2 text-xs">
                  <div className={`w-2 h-2 rounded-full mt-1 ${
                    factor.severity === 'high' ? 'bg-red-400' :
                    factor.severity === 'medium' ? 'bg-yellow-400' : 'bg-blue-400'
                  }`} />
                  <span>{factor.description}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {result.blockedReasons && result.blockedReasons.length > 0 && (
        <Alert variant="destructive">
          <Lock className="h-4 w-4" />
          <AlertTitle>Transaction Blocked</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1 mt-2">
              {result.blockedReasons.map((reason, index) => (
                <li key={index}>{reason}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {result.recommendedActions.length > 0 && (
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertTitle>Recommended Actions</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1 mt-2">
              {result.recommendedActions.map((action, index) => (
                <li key={index}>{action}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

// Fraud Prevention Education Component
export const FraudPreventionTips: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const tips = [
    {
      icon: <Lock className="h-5 w-5" />,
      title: "Secure Payment",
      description: "Always verify the website URL before entering payment details."
    },
    {
      icon: <Phone className="h-5 w-5" />,
      title: "Verify Communications",
      description: "We'll never ask for sensitive information via phone or email."
    },
    {
      icon: <Eye className="h-5 w-5" />,
      title: "Monitor Activity",
      description: "Regularly check your account for unauthorized transactions."
    },
    {
      icon: <CreditCard className="h-5 w-5" />,
      title: "Payment Security",
      description: "Use secure payment methods and avoid public WiFi for transactions."
    }
  ];

  return (
    <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600" />
          <span className="font-medium text-blue-800">Security Tips</span>
        </div>
        <Button variant="ghost" size="sm" className="text-blue-600">
          {isExpanded ? 'Hide' : 'Show'} Tips
        </Button>
      </div>

      {isExpanded && (
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          {tips.map((tip, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="text-blue-600 mt-1">
                {tip.icon}
              </div>
              <div>
                <h4 className="font-medium text-blue-900">{tip.title}</h4>
                <p className="text-sm text-blue-700">{tip.description}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};