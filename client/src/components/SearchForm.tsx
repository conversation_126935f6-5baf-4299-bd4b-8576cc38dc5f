import React, { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronUp, Filter, MapPin } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface SearchFormProps {
  onSearch: (values: SearchFormValues) => void;
}

const searchFormSchema = z.object({
  location: z.string().default(""),
  date: z.string().default(""),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  amenities: z.array(z.string()).optional(),
});

type SearchFormValues = z.infer<typeof searchFormSchema>;

// Available locations for farmhouse properties with search terms for API
const availableLocationsData = [
  { searchTerm: "Shamirpet", displayName: "Shamirpet, Hyderabad" },
  { searchTerm: "Shankarpalli", displayName: "Shankarpalli, Hyderabad" },
  { searchTerm: "Gandipet", displayName: "Gandipet, Hyderabad" },
  { searchTerm: "Patancheru", displayName: "Patancheru, Hyderabad" },
  { searchTerm: "Ellensburg", displayName: "Ellensburg, WA" },
  { searchTerm: "Leavenworth", displayName: "Leavenworth, WA" },
  { searchTerm: "Maple Valley", displayName: "Maple Valley, WA" },
  { searchTerm: "Sequim", displayName: "Sequim, WA" },
  { searchTerm: "Wenatchee", displayName: "Wenatchee, WA" }
];

// Common amenities for farm properties
const commonAmenities = [
  "Swimming Pool",
  "Cricket ground",  
  "WiFi",
  "Parking",
  "Kitchen",
  "Air conditioning",
  "BBQ",
  "Farm animals",
  "Garden",
  "Orchard"
];

export default function SearchForm({ onSearch }: SearchFormProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState([2000, 25000]);
  const [locationQuery, setLocationQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredLocations, setFilteredLocations] = useState(availableLocationsData);
  const locationInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<SearchFormValues>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      location: "all",
      date: "",
      minPrice: 2000,
      maxPrice: 25000,
      amenities: [],
    },
  });

  const handleSubmit = (values: SearchFormValues) => {
    // Update price range from slider
    values.minPrice = priceRange[0];
    values.maxPrice = priceRange[1];
    
    // Convert "all" location to empty string for API
    if (values.location === "all") {
      values.location = "";
    }
    
    onSearch(values);
  };

  // Handle price slider change
  const handlePriceChange = (values: number[]) => {
    setPriceRange(values);
    form.setValue("minPrice", values[0]);
    form.setValue("maxPrice", values[1]);
  };

  return (
    <div className="bg-white/95 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-white/20 max-w-5xl mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <div className="flex flex-col gap-6">
            {/* Header */}
            <div className="text-center mb-2">
              <h3 className="text-2xl font-bold text-[#2D3C2D] mb-2">Find Your Perfect Farmhouse</h3>
              <p className="text-[#605045]">Search from premium rural properties across India</p>
            </div>

            {/* Basic search row */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="relative group">
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-sm font-semibold text-[#605045] mb-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Where to?
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-12 px-4 border-2 border-[#E5E7EB] rounded-xl focus:border-[#4A6741] focus:ring-0 bg-white/80 backdrop-blur-sm hover:border-[#4A6741] transition-all duration-300">
                            <SelectValue placeholder="Choose destination" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="all">All Locations</SelectItem>
                          {availableLocationsData.map((locationData) => (
                            <SelectItem key={locationData.searchTerm} value={locationData.searchTerm}>
                              {locationData.displayName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="relative group">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-sm font-semibold text-[#605045] mb-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        When?
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          placeholder="Select date"
                          className="h-12 px-4 border-2 border-[#E5E7EB] rounded-xl focus:border-[#4A6741] focus:ring-0 bg-white/80 backdrop-blur-sm hover:border-[#4A6741] transition-all duration-300"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex flex-col gap-3 lg:col-span-1">
                <div className="flex items-center gap-2 mb-2">
                  <Button 
                    type="button" 
                    variant="outline"
                    className="flex items-center gap-2 h-12 px-4 border-2 border-[#E5E7EB] rounded-xl hover:border-[#4A6741] hover:bg-[#4A6741] hover:text-white transition-all duration-300"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    <Filter size={16} />
                    <span>More Filters</span>
                    {showFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                  </Button>
                </div>
                <Button 
                  type="submit" 
                  className="h-12 bg-gradient-to-r from-[#4A6741] to-[#3A5131] hover:from-[#3A5131] hover:to-[#2A4121] text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  🔍 Search Properties
                </Button>
              </div>
            </div>
            
            {/* Advanced filters section */}
            {showFilters && (
              <div className="border-t border-[#EBE6E1] pt-4 mt-2 space-y-6">
                {/* Price Range */}
                <div className="space-y-4">
                  <h3 className="font-medium text-[#2D3C2D]">Price Range</h3>
                  <div className="px-2">
                    <Slider
                      defaultValue={[2000, 10000]}
                      min={2000}
                      max={25000}
                      step={500}
                      value={priceRange}
                      onValueChange={handlePriceChange}
                      className="mb-6"
                    />
                    <div className="flex justify-between text-sm text-[#605045]">
                      <span>₹{priceRange[0]}</span>
                      <span>₹{priceRange[1]}{priceRange[1] === 25000 ? '+' : ''}</span>
                    </div>
                  </div>
                </div>

                {/* Amenities */}
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="amenities">
                    <AccordionTrigger className="text-[#2D3C2D] hover:text-[#4A6741]">
                      Property Amenities
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mt-2">
                        {commonAmenities.map((amenity) => (
                          <FormField
                            key={amenity}
                            control={form.control}
                            name="amenities"
                            render={({ field }) => {
                              return (
                                <FormItem
                                  key={amenity}
                                  className="flex flex-row items-start space-x-3 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(amenity) || false}
                                      onCheckedChange={(checked) => {
                                        const currentValues = field.value || [];
                                        return checked
                                          ? field.onChange([...currentValues, amenity])
                                          : field.onChange(
                                              currentValues?.filter(
                                                (value) => value !== amenity
                                              )
                                            );
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal cursor-pointer">
                                    {amenity}
                                  </FormLabel>
                                </FormItem>
                              );
                            }}
                          />
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}
