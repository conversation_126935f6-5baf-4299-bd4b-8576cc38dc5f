import { useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Property } from '@shared/schema';

interface InteractivePropertyMapProps {
  properties: Property[];
  className?: string;
  selectedPropertyId?: number;
}

export default function InteractivePropertyMap({ 
  properties, 
  className = "h-96 w-full",
  selectedPropertyId 
}: InteractivePropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const [, setLocation] = useLocation();
  
  useEffect(() => {
    if (!mapRef.current || !properties.length) return;
    
    // Clean up existing map instance if it exists
    if (mapInstanceRef.current) {
      mapInstanceRef.current.remove();
      mapInstanceRef.current = null;
    }
    
    // Add a small delay to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      if (!mapRef.current || !properties.length) return;
    
      // Get coordinates for a property - use actual coordinates if available, otherwise fall back to location mapping
      const getCoordinatesForProperty = (property: Property): [number, number] => {
        // Use actual property coordinates if available
        if (property.latitude !== null && property.longitude !== null && 
            property.latitude !== undefined && property.longitude !== undefined) {
          return [property.latitude, property.longitude];
        }
        
        // Fallback to hardcoded coordinates based on location names
        const locationMap: Record<string, [number, number]> = {
          'Hyderabad': [17.3850, 78.4867],
          'Gandipet': [17.3801, 78.3513],
          'Kompally': [17.5054, 78.4800],
          'Shamirpet': [17.5789, 78.5686],
          'Moinabad': [17.2988, 78.2464],
          'Medchal': [17.6297, 78.4845],
          'Shamshabad': [17.2431, 78.4169],
          'Secunderabad': [17.4399, 78.4983],
          'Gachibowli': [17.4406, 78.3752],
          'HITEC City': [17.4503, 78.3809],
          'Miyapur': [17.4939, 78.3511],
          'Kukatpally': [17.4950, 78.4110],
          'Madhapur': [17.4479, 78.3916],
          'Kondapur': [17.4605, 78.3781],
          'Uppal': [17.4015, 78.5583],
          'Pocharam': [17.2536, 78.5689],
          'Tellapur': [17.4711, 78.3025],
          'Ranga Reddy': [17.2536, 78.4689],
          'Medchal-Malkajgiri': [17.6297, 78.4845],
          'Telangana': [17.3850, 78.4867],
        };
        
        for (const [key, coords] of Object.entries(locationMap)) {
          if (property.location.toLowerCase().includes(key.toLowerCase())) {
            return coords;
          }
        }
        
        return locationMap['Hyderabad']; // Default fallback
      };
      
      // Calculate center point of all properties
      const bounds = L.latLngBounds([]);
      properties.forEach(property => {
        const coords = getCoordinatesForProperty(property);
        bounds.extend(coords);
      });
      
      try {
        // Initialize the map
        const center = bounds.isValid() ? bounds.getCenter() : L.latLng(17.3850, 78.4867);
        const map = L.map(mapRef.current).setView(center, 10);
        mapInstanceRef.current = map;
        
        // Add the OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
    
        // Create custom icon for properties
        const createPropertyIcon = (isSelected: boolean = false) => {
          return L.divIcon({
            html: `
              <div class="relative">
                <div class="w-8 h-8 ${isSelected ? 'bg-red-500' : 'bg-[#4A6741]'} rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                </div>
                ${isSelected ? '<div class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>' : ''}
              </div>
            `,
            className: 'property-marker',
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
          });
        };
        
        // Add markers for each property
        properties.forEach(property => {
          const coords = getCoordinatesForProperty(property);
          const isSelected = selectedPropertyId === property.id;
          
          const marker = L.marker(coords, {
            icon: createPropertyIcon(isSelected)
          }).addTo(map);
          
          // Create popup content
          const popupContent = `
            <div class="p-2 min-w-64">
              <div class="mb-2">
                <img 
                  src="${property.images[0] || '/placeholder-farmhouse.jpg'}" 
                  alt="${property.title}"
                  class="w-full h-32 object-cover rounded-lg"
                />
              </div>
              <h3 class="font-bold text-lg text-[#2D3C2D] mb-1">${property.title}</h3>
              <p class="text-sm text-[#605045] mb-2">${property.location}</p>
              <div class="flex justify-between items-center mb-2">
                <span class="text-lg font-bold text-[#4A6741]">₹${property.halfDayPrice}/12h</span>
                <span class="text-sm text-[#605045]">₹${property.fullDayPrice}/full day</span>
              </div>
              <p class="text-sm text-[#605045] mb-3 line-clamp-2">${property.description}</p>
              <button 
                onclick="window.location.href='/property/${property.id}'"
                class="w-full bg-[#4A6741] text-white py-2 px-4 rounded-lg hover:bg-[#3A5131] transition-colors"
              >
                View Details
              </button>
            </div>
          `;
          
          marker.bindPopup(popupContent, {
            maxWidth: 280,
            className: 'property-popup'
          });
          
          // Handle marker click to navigate to property
          marker.on('click', () => {
            setLocation(`/property/${property.id}`);
          });
          
          // Open popup if this property is selected
          if (isSelected) {
            marker.openPopup();
          }
        });
        
        // Fit map to show all markers if we have multiple properties
        if (properties.length > 1 && bounds.isValid()) {
          map.fitBounds(bounds, { padding: [20, 20] });
        }
      } catch (error) {
        console.error('Error initializing interactive map:', error);
      }
    }, 100);
    
    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
        } catch (error) {
          console.error('Error cleaning up interactive map:', error);
        }
        mapInstanceRef.current = null;
      }
    };
  }, [properties, selectedPropertyId, setLocation]);
  
  return (
    <div className="relative">
      <div ref={mapRef} className={className} />
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg z-[1000]">
        <div className="flex items-center gap-2 text-sm">
          <div className="w-4 h-4 bg-[#4A6741] rounded-full"></div>
          <span>Available Properties ({properties.length})</span>
        </div>
        {selectedPropertyId && (
          <div className="flex items-center gap-2 text-sm mt-1">
            <div className="w-4 h-4 bg-red-500 rounded-full"></div>
            <span>Selected Property</span>
          </div>
        )}
      </div>
    </div>
  );
}