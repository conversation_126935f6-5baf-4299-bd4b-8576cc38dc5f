import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { 
  Calendar,
  Clock,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Zap,
  Info
} from 'lucide-react';

interface PricingDemoProps {
  propertyId: number;
  onDateSelect?: (date: Date) => void;
}

export function PricingIntegrationDemo({ propertyId, onDateSelect }: PricingDemoProps) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [bookingType, setBookingType] = useState<'morning' | 'full_day'>('morning');

  // Fetch dynamic pricing for selected date
  const { data: pricingData, isLoading: pricingLoading } = useQuery({
    queryKey: ['pricing-demo', propertyId, selectedDate?.toISOString()?.split('T')[0]],
    queryFn: async () => {
      if (!selectedDate) return null;
      const dateStr = selectedDate.toISOString().split('T')[0];
      const response = await fetch(`/api/properties/${propertyId}/pricing/${dateStr}`);
      if (!response.ok) throw new Error('Failed to fetch pricing');
      const result = await response.json();
      return result.data;
    },
    enabled: !!selectedDate,
  });

  // Sample dates to demonstrate different pricing scenarios
  const sampleDates = [
    { 
      date: new Date(2024, 11, 12), // Thursday
      label: 'Thursday (Weekday)',
      type: 'weekday' as const
    },
    { 
      date: new Date(2024, 11, 14), // Saturday  
      label: 'Saturday (Weekend)',
      type: 'weekend' as const
    },
    { 
      date: new Date(2024, 11, 15), // Sunday
      label: 'Sunday (Weekend)', 
      type: 'weekend' as const
    }
  ];

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    onDateSelect?.(date);
  };

  const getPricingExplanation = () => {
    if (!pricingData) return null;

    const { isWeekend, pricing, basePricing, weekdayPricing, weekendPricing } = pricingData;
    const currentPrice = bookingType === 'morning' ? pricing.halfDay : pricing.fullDay;
    const basePrice = bookingType === 'morning' ? basePricing.halfDay : basePricing.fullDay;

    let explanation = '';
    let pricingTier = 'Base';

    if (isWeekend) {
      const weekendPrice = bookingType === 'morning' ? weekendPricing.halfDay : weekendPricing.fullDay;
      const weekdayPrice = bookingType === 'morning' ? weekdayPricing.halfDay : weekdayPricing.fullDay;
      
      if (weekendPrice && weekendPrice > 0) {
        explanation = 'Using dedicated weekend pricing rates (Friday-Sunday)';
        pricingTier = 'Weekend Premium';
      } else if (weekdayPrice && weekdayPrice > 0) {
        explanation = 'Weekend pricing not set - using weekday rates as fallback';
        pricingTier = 'Weekday Fallback';
      } else {
        explanation = 'Weekend pricing not set - using base rates as fallback';
        pricingTier = 'Base Fallback';
      }
    } else {
      const weekdayPrice = bookingType === 'morning' ? weekdayPricing.halfDay : weekdayPricing.fullDay;
      
      if (weekdayPrice && weekdayPrice > 0) {
        explanation = 'Using dedicated weekday pricing rates (Monday-Thursday)';
        pricingTier = 'Weekday';
      } else {
        explanation = 'Weekday pricing not set - using base rates';
        pricingTier = 'Base';
      }
    }

    return { explanation, pricingTier, currentPrice, basePrice };
  };

  const pricingExplanation = getPricingExplanation();

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-blue-600" />
          Dynamic Pricing Integration Demo
          <Badge variant="outline" className="ml-auto">
            Live Demo
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Date Selection */}
        <div>
          <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            1. Select a Date to See Dynamic Pricing
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {sampleDates.map((sample, index) => (
              <Button
                key={index}
                variant={selectedDate?.toDateString() === sample.date.toDateString() ? 'default' : 'outline'}
                onClick={() => handleDateSelect(sample.date)}
                className="flex flex-col items-center p-4 h-auto"
              >
                <span className="font-medium">{sample.label}</span>
                <span className="text-xs opacity-70">
                  {format(sample.date, 'MMM dd, yyyy')}
                </span>
                <Badge 
                  variant="secondary" 
                  className={`text-xs mt-1 ${
                    sample.type === 'weekend' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'
                  }`}
                >
                  {sample.type}
                </Badge>
              </Button>
            ))}
          </div>
        </div>

        {/* Booking Type Selection */}
        {selectedDate && (
          <div>
            <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              2. Choose Booking Duration
            </h3>
            <div className="flex gap-3">
              <Button
                variant={bookingType === 'morning' ? 'default' : 'outline'}
                onClick={() => setBookingType('morning')}
                className="flex-1"
              >
                12-Hour Access
                <span className="text-xs ml-2">(8am-8pm)</span>
              </Button>
              <Button
                variant={bookingType === 'full_day' ? 'default' : 'outline'}
                onClick={() => setBookingType('full_day')}
                className="flex-1"
              >
                24-Hour Access
                <span className="text-xs ml-2">(8am-8am next day)</span>
              </Button>
            </div>
          </div>
        )}

        {/* Dynamic Pricing Results */}
        {selectedDate && pricingData && pricingExplanation && (
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
            <h3 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              3. Dynamic Pricing Result
              <CheckCircle className="h-4 w-4 text-green-600" />
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-4">
                  <div className="flex items-baseline gap-2 mb-2">
                    <span className="text-3xl font-bold text-green-700">
                      ₹{pricingExplanation.currentPrice.toLocaleString('en-IN')}
                    </span>
                    <Badge className="bg-green-100 text-green-700">
                      {pricingExplanation.pricingTier}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    For {bookingType === 'morning' ? '12-hour' : '24-hour'} access
                  </p>
                </div>

                {pricingExplanation.currentPrice !== pricingExplanation.basePrice && (
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-gray-500 line-through">
                      ₹{pricingExplanation.basePrice.toLocaleString('en-IN')}
                    </span>
                    <ArrowRight className="h-3 w-3 text-gray-400" />
                    <span className="text-green-600 font-medium">
                      ₹{pricingExplanation.currentPrice.toLocaleString('en-IN')}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {pricingExplanation.currentPrice > pricingExplanation.basePrice ? 'Premium' : 'Discounted'}
                    </Badge>
                  </div>
                )}
              </div>

              <div className="bg-white rounded-lg p-4 border">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      Pricing Logic Applied
                    </p>
                    <p className="text-xs text-gray-600">
                      {pricingExplanation.explanation}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-sm text-blue-700">
                <Zap className="h-4 w-4" />
                <span className="font-medium">
                  This pricing will be automatically used when guests book for {format(selectedDate, 'MMMM dd, yyyy')}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* How It Works */}
        <div className="border-t pt-6">
          <h3 className="font-medium text-gray-900 mb-3">How Dynamic Pricing Works</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-start gap-2">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">1</div>
              <div>
                <p className="font-medium">Date Analysis</p>
                <p className="text-gray-600">System determines if booking date is weekday (Mon-Thu) or weekend (Fri-Sun)</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">2</div>
              <div>
                <p className="font-medium">Price Selection</p>
                <p className="text-gray-600">Uses appropriate pricing tier with fallback to base rates when specific rates aren't set</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">3</div>
              <div>
                <p className="font-medium">Live Booking</p>
                <p className="text-gray-600">Booking system automatically applies the calculated price for seamless user experience</p>
              </div>
            </div>
          </div>
        </div>

        {pricingLoading && (
          <div className="text-center py-4">
            <div className="inline-flex items-center gap-2 text-blue-600">
              <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
              Calculating dynamic pricing...
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default PricingIntegrationDemo;