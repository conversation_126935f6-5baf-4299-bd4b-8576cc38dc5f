import React, { useState, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { PropertyFormData } from "@shared/validations";
import { ChevronDown, ChevronRight, Calendar, Clock } from "lucide-react";

interface PricingSectionProps {
  form: UseFormReturn<PropertyFormData>;
}

export function PricingSection({ form }: PricingSectionProps) {
  // Initialize toggle state based on existing weekend pricing data
  const hasInitialWeekendPricing = form.getValues("weekendHalfDayPrice") || form.getValues("weekendFullDayPrice");
  const [useDifferentWeekendPricing, setUseDifferentWeekendPricing] = useState(!!hasInitialWeekendPricing);
  const [weekdayExpanded, setWeekdayExpanded] = useState(true);
  const [weekendExpanded, setWeekendExpanded] = useState(true);

  const formatCurrency = (value: number | undefined) => {
    if (!value) return "₹0";
    // Use Indian comma formatting
    const parts = value.toString().split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1];
    
    // Indian number formatting: 1,23,45,678
    let formatted = '';
    const len = integerPart.length;
    
    for (let i = 0; i < len; i++) {
      if (i > 0 && (len - i) % 2 === 1 && (len - i) > 3) {
        formatted += ',';
      } else if (i > 0 && (len - i) === 3) {
        formatted += ',';
      }
      formatted += integerPart[i];
    }
    
    const finalFormatted = decimalPart ? `${formatted}.${decimalPart}` : formatted;
    return `₹${finalFormatted}`;
  };

  // Helper function to get effective pricing (with fallback logic)
  const getEffectivePrice = (weekendPrice: number | undefined, weekdayPrice: number | undefined, basePrice: number) => {
    // If weekend price is set and not zero, use it
    if (weekendPrice && weekendPrice > 0) return weekendPrice;
    // If weekday price is set and not zero, use it  
    if (weekdayPrice && weekdayPrice > 0) return weekdayPrice;
    // Fall back to base price
    return basePrice;
  };

  // Handle toggle changes - clear weekend pricing when disabled
  const handleToggleChange = (enabled: boolean) => {
    setUseDifferentWeekendPricing(enabled);
    if (!enabled) {
      // Clear weekend pricing fields when toggle is disabled
      form.setValue("weekendHalfDayPrice", undefined);
      form.setValue("weekendFullDayPrice", undefined);
    } else {
      // Auto-expand weekend section when enabled
      setWeekendExpanded(true);
    }
  };

  // Sync toggle state with form data changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "weekendHalfDayPrice" || name === "weekendFullDayPrice") {
        const hasWeekendData = value.weekendHalfDayPrice || value.weekendFullDayPrice;
        if (hasWeekendData && !useDifferentWeekendPricing) {
          setUseDifferentWeekendPricing(true);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, useDifferentWeekendPricing]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-[#2D3C2D] flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Pricing Management
          <Badge variant="secondary" className="text-xs">
            Dynamic Rates
          </Badge>
        </CardTitle>
        <p className="text-sm text-gray-600">
          Configure rental pricing based on duration and day type. Set competitive rates for different periods.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Weekend Pricing Toggle */}
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Clock className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-[#2D3C2D]">Use different weekend pricing</p>
                <p className="text-sm text-gray-600">Enable to set premium rates for weekends</p>
              </div>
            </div>
            <Switch
              checked={useDifferentWeekendPricing}
              onCheckedChange={handleToggleChange}
              className="data-[state=checked]:bg-[#4A6741]"
            />
          </div>
          
          {/* Contextual Help Text */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <span className="text-amber-600 text-sm">💡</span>
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">Pricing Logic:</p>
                <p>Weekend rates apply to <strong>Fridays, Saturdays, and Sundays</strong>. Leave weekend fields blank or set to ₹0 to automatically use weekday pricing for those days.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Base/Weekday Pricing */}
        <Collapsible open={weekdayExpanded} onOpenChange={setWeekdayExpanded}>
          <div className="space-y-4">
            <CollapsibleTrigger asChild>
              <Button 
                variant="ghost" 
                className="flex items-center justify-between w-full p-4 bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-[#2D3C2D]">
                      {useDifferentWeekendPricing ? "Weekday Rates" : "Base Pricing"}
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {useDifferentWeekendPricing ? "Mon - Thu" : "All Days"}
                    </Badge>
                  </div>
                </div>
                {weekdayExpanded ? (
                  <ChevronDown className="h-4 w-4 text-[#4A6741]" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-[#4A6741]" />
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="space-y-4">
              <p className="text-sm text-gray-600 px-4">
                {useDifferentWeekendPricing 
                  ? "Pricing for Monday to Thursday bookings" 
                  : "Standard pricing for all bookings (when weekend pricing is not enabled)"
                }
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-4">
                <FormField
                  control={form.control}
                  name={useDifferentWeekendPricing ? "weekdayHalfDayPrice" : "halfDayPrice"}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D] font-medium">
                        Half-Day Price (12h)
                      </FormLabel>
                      <FormControl>
                        <CurrencyInput
                          value={field.value || 0}
                          onChange={(value) => field.onChange(value || (useDifferentWeekendPricing ? undefined : 0))}
                          currency="INR"
                          minValue={100}
                          maxValue={999999}
                          placeholder="12,000"
                          fallbackPlaceholder={useDifferentWeekendPricing ? "Leave blank to use base pricing" : ""}
                          className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                        />
                      </FormControl>
                      <FormDescription>
                        12-hour rental rate
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={useDifferentWeekendPricing ? "weekdayFullDayPrice" : "fullDayPrice"}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2D3C2D] font-medium">
                        Full-Day Price (24h)
                      </FormLabel>
                      <FormControl>
                        <CurrencyInput
                          value={field.value || 0}
                          onChange={(value) => field.onChange(value || (useDifferentWeekendPricing ? undefined : 0))}
                          currency="INR"
                          minValue={100}
                          maxValue={999999}
                          placeholder="20,000"
                          fallbackPlaceholder={useDifferentWeekendPricing ? "Leave blank to use base pricing" : ""}
                          className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                        />
                      </FormControl>
                      <FormDescription>
                        Full-day rental rate
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CollapsibleContent>
          </div>
        </Collapsible>

        {/* Weekend Pricing - Only show when toggle is enabled */}
        {useDifferentWeekendPricing && (
          <Collapsible open={weekendExpanded} onOpenChange={setWeekendExpanded}>
            <div className="space-y-4">
              <CollapsibleTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="flex items-center justify-between w-full p-4 bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-[#2D3C2D]">Weekend Rates</h3>
                      <Badge variant="outline" className="text-xs bg-purple-100">
                        Fri - Sun
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        Premium
                      </Badge>
                    </div>
                  </div>
                  {weekendExpanded ? (
                    <ChevronDown className="h-4 w-4 text-purple-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-purple-600" />
                  )}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="space-y-4">
                <div className="px-4 space-y-3">
                  <p className="text-sm text-gray-600">
                    Premium pricing for Friday to Sunday bookings
                  </p>
                  <div className="bg-purple-50 border border-purple-200 rounded-md p-3">
                    <p className="text-xs text-purple-700">
                      <strong>Tip:</strong> Leave fields blank or enter ₹0 to use weekday rates for weekends. Only fill in the rates you want to be different from weekdays.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-4">
                  <FormField
                    control={form.control}
                    name="weekendHalfDayPrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#2D3C2D] font-medium">
                          Half-Day Price (12h)
                        </FormLabel>
                        <FormControl>
                          <CurrencyInput
                            value={field.value || 0}
                            onChange={(value) => field.onChange(value || undefined)}
                            currency="INR"
                            minValue={0}
                            maxValue={999999}
                            placeholder="15,000"
                            fallbackPlaceholder="Leave blank to use weekday rates"
                            className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                          />
                        </FormControl>
                        <FormDescription>
                          12-hour weekend rate (leave blank to use weekday rate)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="weekendFullDayPrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#2D3C2D] font-medium">
                          Full-Day Price (24h)
                        </FormLabel>
                        <FormControl>
                          <CurrencyInput
                            value={field.value || 0}
                            onChange={(value) => field.onChange(value || undefined)}
                            currency="INR"
                            minValue={0}
                            maxValue={999999}
                            placeholder="25,000"
                            fallbackPlaceholder="Leave blank to use weekday rates"
                            className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                          />
                        </FormControl>
                        <FormDescription>
                          Full-day weekend rate (leave blank to use weekday rate)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CollapsibleContent>
            </div>
          </Collapsible>
        )}

        {/* Fallback Base Pricing - Only show when weekend pricing is enabled */}
        {useDifferentWeekendPricing && (
          <div className="space-y-4 border-t pt-6">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-[#2D3C2D]">Fallback Pricing</h3>
              <Badge variant="secondary" className="text-xs">
                Safety Net
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              Default rates used when weekday/weekend specific pricing is not set
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="halfDayPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#2D3C2D] font-medium">
                      Half-Day Base Rate
                    </FormLabel>
                    <FormControl>
                      <CurrencyInput
                        value={field.value || 0}
                        onChange={(value) => field.onChange(value || 0)}
                        currency="INR"
                        minValue={100}
                        maxValue={999999}
                        placeholder="10,000"
                        className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                      />
                    </FormControl>
                    <FormDescription>
                      Backup 12-hour rate
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fullDayPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#2D3C2D] font-medium">
                      Full-Day Base Rate
                    </FormLabel>
                    <FormControl>
                      <CurrencyInput
                        value={field.value || 0}
                        onChange={(value) => field.onChange(value || 0)}
                        currency="INR"
                        minValue={100}
                        maxValue={999999}
                        placeholder="10,000"
                        className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                      />
                    </FormControl>
                    <FormDescription>
                      Backup full-day rate
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        )}

        {/* Pricing Summary */}
        <div className="bg-gradient-to-r from-[#F7F4F1] to-blue-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center gap-2">
            <h4 className="font-semibold text-[#2D3C2D] text-sm">Pricing Summary</h4>
            <Badge variant="outline" className="text-xs">
              Live Preview
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            {useDifferentWeekendPricing ? (
              <>
                <div>
                  <p className="text-gray-600 font-medium">Weekday 12h:</p>
                  <p className="font-semibold text-[#2D3C2D]">
                    {formatCurrency(getEffectivePrice(undefined, form.watch("weekdayHalfDayPrice"), form.watch("halfDayPrice")))}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 font-medium">Weekday 24h:</p>
                  <p className="font-semibold text-[#2D3C2D]">
                    {formatCurrency(getEffectivePrice(undefined, form.watch("weekdayFullDayPrice"), form.watch("fullDayPrice")))}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 font-medium">Weekend 12h:</p>
                  <div className="flex items-center gap-2">
                    <p className="font-semibold text-[#2D3C2D]">
                      {formatCurrency(getEffectivePrice(form.watch("weekendHalfDayPrice"), form.watch("weekdayHalfDayPrice"), form.watch("halfDayPrice")))}
                    </p>
                    {(!form.watch("weekendHalfDayPrice") || form.watch("weekendHalfDayPrice") === 0) && (
                      <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">fallback</span>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-gray-600 font-medium">Weekend 24h:</p>
                  <div className="flex items-center gap-2">
                    <p className="font-semibold text-[#2D3C2D]">
                      {formatCurrency(getEffectivePrice(form.watch("weekendFullDayPrice"), form.watch("weekdayFullDayPrice"), form.watch("fullDayPrice")))}
                    </p>
                    {(!form.watch("weekendFullDayPrice") || form.watch("weekendFullDayPrice") === 0) && (
                      <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">fallback</span>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div>
                  <p className="text-gray-600 font-medium">Half-Day (12h):</p>
                  <p className="font-semibold text-[#2D3C2D]">
                    {formatCurrency(form.watch("halfDayPrice"))}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 font-medium">Full-Day (24h):</p>
                  <p className="font-semibold text-[#2D3C2D]">
                    {formatCurrency(form.watch("fullDayPrice"))}
                  </p>
                </div>
              </>
            )}
          </div>
          
          <div className="flex items-center gap-2 pt-2 border-t">
            <span className="text-xs">💡</span>
            <p className="text-xs text-gray-500">
              {useDifferentWeekendPricing 
                ? "Weekend pricing enabled - premium rates for Fri-Sun bookings"
                : "Using uniform pricing for all days - enable weekend pricing for premium rates"
              }
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}