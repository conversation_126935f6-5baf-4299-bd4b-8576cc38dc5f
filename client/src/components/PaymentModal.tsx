import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Shield, CreditCard, Clock, Info, CheckCircle2, XCircle } from "lucide-react";
import { DEFAULT_BOOKING_CONFIG } from "@/config/booking";
import { getSecurityConfig, isHighValueTransaction } from "@/config/security";
import { useSessionSecurity } from "@/lib/session";
import { logPaymentAttempt, logPaymentSuccess, logPaymentFailure } from "@/lib/audit";
import TwoFactorModal, { TwoFactorContent, TwoFactorData } from "./TwoFactorModal";
import { RateLimitAlert } from "./SecurityAlerts";
import { 
  PaymentOrder, 
  PaymentBreakdown, 
  RazorpayPaymentResponse, 
  PaymentData, 
  BookingDetails, 
  ApiError,
  PaymentVerificationRequest,
  PaymentCaptureRequest
} from "@/types/payment";

declare global {
  interface Window {
    Razorpay: any;
  }
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  bookingDetails: BookingDetails;
  onPaymentSuccess: (paymentData: PaymentData) => void;
  onPaymentFailure: (error: ApiError) => void;
}


export default function PaymentModal({
  isOpen,
  onClose,
  bookingDetails,
  onPaymentSuccess,
  onPaymentFailure
}: PaymentModalProps) {
  const [loading, setLoading] = useState(false);
  const [paymentBreakdown, setPaymentBreakdown] = useState<PaymentBreakdown | null>(null);
  const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [scriptLoading, setScriptLoading] = useState(false);
  const [paymentInProgress, setPaymentInProgress] = useState(false);
  
  // Security state management
  const [show2FA, setShow2FA] = useState(false);
  const [twoFactorData, setTwoFactorData] = useState<TwoFactorData | null>(null);
  const [verificationToken, setVerificationToken] = useState<string | null>(null);
  
  const { toast } = useToast();
  const securityConfig = getSecurityConfig();
  const {
    isRateLimited,
    getSecurityContext,
    startPaymentSession,
    recordPaymentAttempt,
  } = useSessionSecurity();

  // Global Razorpay script loader with race condition protection
  const loadRazorpayScript = async (): Promise<boolean> => {
    // Check if already loaded
    if (window.Razorpay) {
      return true;
    }

    // Check if already loading
    if (scriptLoading) {
      // Wait for existing load to complete
      let attempts = 0;
      while (attempts < 50 && !window.Razorpay) { // Max 5 seconds
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
      return !!window.Razorpay;
    }

    // Check if script already exists in DOM
    const existingScript = document.querySelector('script[src*="razorpay"]');
    if (existingScript && !window.Razorpay) {
      // Script exists but not loaded yet, wait for it
      let attempts = 0;
      while (attempts < 50 && !window.Razorpay) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
      return !!window.Razorpay;
    }

    setScriptLoading(true);
    try {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.async = true;
        script.onload = () => {
          setScriptLoading(false);
          resolve(true);
        };
        script.onerror = () => {
          setScriptLoading(false);
          resolve(false);
        };
        document.head.appendChild(script);
      });
    } catch (error) {
      setScriptLoading(false);
      return false;
    }
  };

  // Load script when modal opens
  useEffect(() => {
    if (isOpen) {
      loadRazorpayScript();
    }
  }, [isOpen]);

  // Create payment order when modal opens
  useEffect(() => {
    if (isOpen && bookingDetails) {
      createPaymentOrder();
    }
  }, [isOpen, bookingDetails]);

  const createPaymentOrder = async (retryCount = 0): Promise<void> => {
    const maxRetries = 2;
    setLoading(true);
    
    try {
      // Security: Let server calculate all payment amounts
      // Sanitize customer details before sending to server
      const sanitizedCustomerDetails = {
        name: bookingDetails.customerDetails.name?.trim().slice(0, 100) || '',
        email: bookingDetails.customerDetails.email?.trim().slice(0, 100) || '',
        phone: bookingDetails.customerDetails.phone?.trim().slice(0, 15) || ''
      };
      
      const response = await apiRequest("POST", "/api/payments/create-order", {
        bookingId: bookingDetails.id,
        currency: "INR",
        customerDetails: sanitizedCustomerDetails,
        gstDetails: bookingDetails.gstDetails
      });

      const data = await response.json();
      
      if (data.success) {
        setPaymentOrder(data.data.paymentOrder);
        setPaymentBreakdown(data.data.breakdown);
      } else {
        throw new Error(data.message || "Failed to create payment order");
      }
    } catch (error: unknown) {
      const apiError = error as ApiError;
      console.error(`Payment order creation failed (attempt ${retryCount + 1}/${maxRetries + 1}):`, apiError);
      
      // Retry for network errors
      if (retryCount < maxRetries && (
        apiError.message?.includes('network') || 
        apiError.message?.includes('timeout') ||
        apiError.statusCode === 503 ||
        apiError.statusCode === 502
      )) {
        const retryDelay = 2000 * (retryCount + 1);
        console.log(`Retrying payment order creation in ${retryDelay}ms...`);
        setTimeout(() => {
          createPaymentOrder(retryCount + 1);
        }, retryDelay);
        return;
      }
      
      // Show specific validation errors instead of generic messages
      let title = "Payment Setup Failed";
      let description = "Unable to setup payment. Please try again or contact support.";
      
      // Handle validation errors specifically
      if (apiError.statusCode === 400 || apiError.message?.includes('validation') || apiError.message?.includes('character')) {
        title = "Invalid Information";
        if (apiError.message?.includes('phone') || apiError.message?.includes('10 character')) {
          description = "Please add a valid phone number to your profile before making a payment.";
        } else if (apiError.message?.includes('email')) {
          description = "Please add a valid email address to your profile.";
        } else if (apiError.message?.includes('name')) {
          description = "Please add your full name to your profile.";
        } else {
          description = "Please check your information and try again.";
        }
      }
      
      const toastConfig: any = {
        title,
        description,
        variant: "destructive" as const
      };
      
      if (retryCount >= maxRetries) {
        toastConfig.action = (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => createPaymentOrder(0)}
          >
            Retry
          </Button>
        );
      }
      
      toast(toastConfig);
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    // Development bypass for testing - MUST be first to avoid rate limiting
    if (process.env.NODE_ENV === 'development') {
      setProcessingPayment(true);
      
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful payment data
      const mockPaymentData: PaymentData = {
        razorpay_payment_id: `pay_dev_${Date.now()}`,
        razorpay_order_id: paymentOrder?.id?.toString() || `order_dev_${Date.now()}`,
        razorpay_signature: `sig_dev_${Date.now()}`,
        verificationData: {
          paymentId: `pay_dev_${Date.now()}`,
          orderId: paymentOrder?.id?.toString() || `order_dev_${Date.now()}`,
          status: 'success' as const,
          amount: bookingDetails.totalPrice,
          currency: 'INR',
          method: 'development_bypass',
          timestamp: new Date().toISOString()
        }
      };
      
      // Call backend to process payment and update booking status
      try {
        const processResponse = await apiRequest("POST", "/api/payments/process", {
          razorpay_order_id: mockPaymentData.razorpay_order_id,
          razorpay_payment_id: mockPaymentData.razorpay_payment_id,
          razorpay_signature: mockPaymentData.razorpay_signature,
          orderId: paymentOrder?.id?.toString() || `order_dev_${Date.now()}`,
          bookingId: bookingDetails.id,
          idempotencyKey: `dev_payment_${Date.now()}`,
        });
        
        const result = await processResponse.json();
        console.log('🔧 [DEV] Payment processed:', result);
        
        toast({
          title: "Payment Successful! 🎉",
          description: "Development mode: Payment bypassed and booking confirmed",
          duration: 3000,
        });
        
        setProcessingPayment(false);
        onPaymentSuccess({
          ...mockPaymentData,
          verificationData: result.data?.verificationData || mockPaymentData.verificationData
        });
        
      } catch (error) {
        console.error('🔧 [DEV] Payment processing failed:', error);
        toast({
          title: "Payment Processing Failed",
          description: "Development mode: Payment succeeded but booking update failed",
          variant: "destructive",
        });
        setProcessingPayment(false);
        onPaymentFailure(error as any);
      }
      
      return;
    }

    // Strong race condition protection - prevent concurrent payments
    if (paymentInProgress || processingPayment) {
      return; // Silently ignore additional clicks
    }

    // Check if payment order is available first (may have failed due to validation)
    if (!paymentOrder) {
      toast({
        title: "Payment Setup Required", 
        description: "Please check your profile information and refresh the page to try again.",
        variant: "destructive"
      });
      return;
    }

    // Security: Check if user is rate limited (only for actual payment attempts)
    // Skip rate limiting in development mode
    if (process.env.NODE_ENV !== 'development' && isRateLimited()) {
      toast({
        title: "Too Many Attempts",
        description: "Please wait before trying again.",
        variant: "destructive",
      });
      return;
    }

    // Security: Start payment session and check if 2FA is required
    const paymentSession = startPaymentSession(bookingDetails.id, bookingDetails.totalPrice);
    if (!paymentSession) {
      toast({
        title: "Session Error",
        description: "Unable to start payment session. Please try again.",
        variant: "destructive",
      });
      return;
    }

    // Check if 2FA is required for high-value transactions
    if (isHighValueTransaction(bookingDetails.totalPrice, securityConfig)) {
      setTwoFactorData({
        sessionId: paymentSession.id,
        amount: bookingDetails.totalPrice,
        bookingId: bookingDetails.id,
        propertyTitle: bookingDetails.propertyTitle,
      });
      setShow2FA(true);
      return;
    }

    // Proceed with normal payment flow
    await initiateRazorpayPayment();
  };

  const initiateRazorpayPayment = async () => {
    // Log payment attempt
    logPaymentAttempt(
      {
        bookingId: bookingDetails.id,
        propertyId: 0, // Would be passed from props in real implementation
        amount: bookingDetails.totalPrice,
        paymentMethod: "advance",
        orderId: paymentOrder?.id?.toString() || "",
      },
      getSecurityContext() || undefined
    );

    // Ensure Razorpay script is loaded
    const scriptLoaded = await loadRazorpayScript();
    if (!scriptLoaded || !window.Razorpay) {
      toast({
        title: "Payment Error",
        description: "Payment system not ready. Please refresh and try again.",
        variant: "destructive"
      });
      return;
    }

    setPaymentInProgress(true);
    setProcessingPayment(true);

    // Razorpay public key must be provided by server - never use environment variables
    if (!paymentOrder?.razorpayKeyId) {
      toast({
        title: "Payment Configuration Error",
        description: "Payment system not configured. Please contact support.",
        variant: "destructive"
      });
      setProcessingPayment(false);
      setPaymentInProgress(false);
      return;
    }

    const options = {
      key: paymentOrder?.razorpayKeyId, // Server-provided public key
      amount: paymentOrder?.amount,
      currency: paymentOrder?.currency,
      name: "Farmhouse Booking",
      description: `Booking for ${bookingDetails.propertyTitle}`,
      order_id: paymentOrder?.razorpayOrderId,
      handler: async (response: RazorpayPaymentResponse) => {
        // Security: Never verify payments on client-side
        // Simply forward response to server for verification
        setProcessingPayment(true);
        try {
          await handlePaymentResponse(response);
        } catch (error) {
          setProcessingPayment(false);
          setPaymentInProgress(false);
          onPaymentFailure(error as ApiError);
        }
      },
      prefill: {
        name: bookingDetails.customerDetails.name,
        email: bookingDetails.customerDetails.email,
        contact: bookingDetails.customerDetails.phone
      },
      notes: {
        booking_id: bookingDetails.id.toString(),
        property_title: bookingDetails.propertyTitle,
        booking_date: bookingDetails.bookingDate,
        booking_type: bookingDetails.bookingType
      },
      theme: {
        color: "#4A6741"
      },
      modal: {
        ondismiss: () => {
          setProcessingPayment(false);
          setPaymentInProgress(false);
          toast({
            title: "Payment Cancelled",
            description: "You cancelled the payment. Your booking is still pending.",
            variant: "destructive"
          });
        }
      }
    };

    const razorpay = new window.Razorpay(options);
    razorpay.open();
  };

  // Secure payment handling - only forward response to server
  const handlePaymentResponse = async (paymentResponse: RazorpayPaymentResponse): Promise<void> => {
    if (!paymentOrder) {
      throw new Error("Payment order not available");
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);
      
      // Generate idempotency key for server-side race condition protection
      const idempotencyKey = `payment_${paymentResponse.razorpay_payment_id}_${Date.now()}`;
      
      const response = await apiRequest("POST", "/api/payments/process", {
        razorpay_order_id: paymentResponse.razorpay_order_id,
        razorpay_payment_id: paymentResponse.razorpay_payment_id,
        razorpay_signature: paymentResponse.razorpay_signature,
        orderId: paymentOrder.id,
        bookingId: bookingDetails.id,
        idempotencyKey: idempotencyKey
      });

      clearTimeout(timeoutId);
      const data = await response.json();
      
      if (data.success) {
        onPaymentSuccess({
          ...paymentResponse,
          verificationData: data.data
        });
        
        toast({
          title: "Payment Successful!",
          description: "Your booking has been confirmed.",
          duration: 5000
        });
      } else {
        throw new Error("Payment processing failed");
      }
    } catch (error: unknown) {
      const apiError = error as ApiError;
      console.error("Payment processing failed:", apiError);
      
      // Generic error message for security
      toast({
        title: "Payment Processing Failed",
        description: "Unable to process payment. Please contact support if needed.",
        variant: "destructive",
      });
      
      throw apiError;
    } finally {
      setProcessingPayment(false);
      setPaymentInProgress(false);
    }
  };

  // 2FA Success Handler
  const handle2FASuccess = (token: string) => {
    setVerificationToken(token);
    setShow2FA(false);
    
    // Proceed with payment after 2FA verification
    initiateRazorpayPayment();
  };

  // 2FA Failure Handler
  const handle2FAFailure = (error: Error) => {
    setShow2FA(false);
    recordPaymentAttempt(false, error.message);
    
    toast({
      title: "Two-Factor Authentication Failed",
      description: "Unable to verify your identity. Please try again.",
      variant: "destructive",
    });
  };


  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount / 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {show2FA ? 'Two-Factor Authentication' : 'Complete Payment'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Show 2FA content inside the same dialog */}
          {show2FA && twoFactorData ? (
            <TwoFactorContent
              onClose={() => setShow2FA(false)}
              onSuccess={handle2FASuccess}
              onFailure={handle2FAFailure}
              data={twoFactorData}
            />
          ) : (
            <>
              {/* Security Alerts - Skip in development mode */}
              {process.env.NODE_ENV !== 'development' && isRateLimited() && (
                <RateLimitAlert
                  isBlocked={true}
                  remainingTime={0}
                  attempts={getSecurityContext()?.paymentAttempts || 0}
                  maxAttempts={securityConfig.payment.maxAttemptsPerHour}
                  context="payment"
                />
              )}

          {/* High Value Transaction Notice */}
          {isHighValueTransaction(bookingDetails.totalPrice, securityConfig) && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertTitle>Enhanced Security Required</AlertTitle>
              <AlertDescription>
                This high-value transaction (₹{bookingDetails.totalPrice.toLocaleString()}) requires additional verification for your security.
              </AlertDescription>
            </Alert>
          )}

          {/* Booking Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Booking Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Property</span>
                <span className="font-medium">{bookingDetails.propertyTitle}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Date</span>
                <span className="font-medium">{bookingDetails.bookingDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Type</span>
                <Badge variant="outline">
                  {bookingDetails.bookingType === 'morning' ? '12h Access' : '24h Access'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Guests</span>
                <span className="font-medium">{bookingDetails.guests}</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Breakdown */}
          {paymentBreakdown && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Payment Breakdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Advance Amount (30%)</span>
                  <span>{formatCurrency(paymentBreakdown.baseAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">GST</span>
                  <span>{formatCurrency(paymentBreakdown.gstAmount)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold">
                  <span>Total Amount</span>
                  <span className="text-[#4A6741]">{formatCurrency(paymentBreakdown.totalAmount)}</span>
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  <Info className="h-3 w-3 inline mr-1" />
                  Remaining amount will be collected at the property
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security Info */}
          <Card className="bg-green-50 border-green-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Secure Payment</span>
              </div>
              <div className="text-xs text-green-700 space-y-1">
                <div>• SSL encrypted payment gateway</div>
                <div>• PCI DSS compliant processing</div>
                <div>• Your payment details are never stored</div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Button */}
          <div className="space-y-3">
            <Button 
              onClick={handlePayment}
              disabled={loading || processingPayment || paymentInProgress || !paymentOrder || scriptLoading}
              className="w-full bg-[#4A6741] hover:bg-[#3A5235] text-white"
              size="lg"
            >
              {loading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Setting up payment...
                </>
              ) : scriptLoading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Loading payment system...
                </>
              ) : processingPayment || paymentInProgress ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Processing payment...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Pay {paymentBreakdown ? formatCurrency(paymentBreakdown.totalAmount) : 'Now'}
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={onClose}
              disabled={processingPayment || paymentInProgress}
              className="w-full"
            >
              Cancel
            </Button>
          </div>

          {/* Payment Methods Info */}
          <div className="text-center text-xs text-gray-500">
            <div className="flex justify-center items-center gap-2 flex-wrap">
              <span>Accepts:</span>
              <span>UPI • Cards • Net Banking • Wallets</span>
            </div>
          </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}