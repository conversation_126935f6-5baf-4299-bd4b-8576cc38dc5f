import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Calendar from "./Calendar";
import SwiggyStyleAuthModal from "./SwiggyStyleAuthModal";
import PaymentModal from "./PaymentModal";
import PaymentSummary from "./PaymentSummary";
import PaymentStatus from "./PaymentStatus";
import { DEFAULT_BOOKING_CONFIG, BookingConfig, calculateAdvanceAmountDisplay } from "@/config/booking";
import { PaymentData, BookingDetails, ApiError } from "@/types/payment";

interface BookingFormProps {
  propertyId: number;
  propertyTitle?: string;
  halfDayPrice: number;
  fullDayPrice: number;
  initialDate?: Date;
  config?: Partial<BookingConfig>;
}

const bookingSchema = z.object({
  bookingDate: z.date({
    required_error: "Please select a date",
  }),
  bookingType: z.enum(["morning", "full_day"], {
    required_error: "Please select a booking type",
  }),
  guests: z.string().min(1, "Please select number of guests"),
  paymentMethod: z.enum(["advance", "full_amount"], {
    required_error: "Please select a payment method",
  }),
});

type BookingFormValues = z.infer<typeof bookingSchema>;

export default function BookingForm({ propertyId, propertyTitle, halfDayPrice, fullDayPrice, initialDate, config }: BookingFormProps) {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'success' | 'failed' | 'pending'>('idle');
  const [paymentData, setPaymentData] = useState<{
    paymentId: string;
    orderId: string;
    amount: number;
    currency: string;
    method?: string;
    timestamp: string;
  } | null>(null);
  const [bookingData, setBookingData] = useState<BookingDetails | null>(null);
  
  // Merge provided config with defaults
  const bookingConfig: BookingConfig = {
    ...DEFAULT_BOOKING_CONFIG,
    ...config,
    fees: { ...DEFAULT_BOOKING_CONFIG.fees, ...config?.fees },
    payment: { ...DEFAULT_BOOKING_CONFIG.payment, ...config?.payment },
    gst: { ...DEFAULT_BOOKING_CONFIG.gst, ...config?.gst },
    ui: { ...DEFAULT_BOOKING_CONFIG.ui, ...config?.ui },
  };
  
  const cleaningFee = bookingConfig.fees.cleaning;
  
  // Fetch property bookings to hide confirmed dates
  const [propertyBookings, setPropertyBookings] = useState<{ date: string; type: string; status: string }[]>([]);
  
  // Fetch bookings for this property on component mount
  useEffect(() => {
    const fetchPropertyBookings = async () => {
      try {
        const response = await apiRequest("GET", `/api/bookings/property/${propertyId}/availability`);
        const bookingsData = await response.json();
        const formattedBookings = bookingsData.map((booking: any) => ({
          date: booking.date,
          type: booking.type,
          status: booking.status
        }));
        setPropertyBookings(formattedBookings);
      } catch (error) {
        console.error("Failed to fetch property bookings:", error);
        // Don't show error to user as this is non-critical
      }
    };

    fetchPropertyBookings();
  }, [propertyId]);
  


  const form = useForm<BookingFormValues>({
    resolver: zodResolver(bookingSchema),
    defaultValues: {
      bookingType: bookingConfig.ui.defaultBookingType,
      guests: "1",
      paymentMethod: bookingConfig.ui.defaultPaymentMethod,
    },
  });

  const selectedDate = form.watch("bookingDate");
  const selectedType = form.watch("bookingType");
  const selectedPaymentMethod = form.watch("paymentMethod");

  // Fetch real-time pricing when date is selected
  const { data: dynamicPricing, isLoading: pricingLoading } = useQuery({
    queryKey: ['property-pricing', propertyId, selectedDate?.toISOString()?.split('T')[0]],
    queryFn: async () => {
      if (!selectedDate) return null;
      const dateStr = selectedDate.toISOString().split('T')[0];
      const response = await fetch(`/api/properties/${propertyId}/pricing/${dateStr}`);
      if (!response.ok) throw new Error('Failed to fetch pricing');
      const result = await response.json();
      return result.data;
    },
    enabled: !!selectedDate,
    staleTime: 60000, // Cache for 1 minute
  });

  // Use dynamic pricing if available, otherwise fall back to static props
  const currentPricing = dynamicPricing?.pricing || { 
    halfDay: halfDayPrice, 
    fullDay: fullDayPrice 
  };
  
  // Determine pricing source for transparency
  const pricingSource = dynamicPricing ? {
    source: dynamicPricing.isWeekend ? 'Weekend' : 'Weekday',
    isWeekend: dynamicPricing.isWeekend,
    date: dynamicPricing.date,
    dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dynamicPricing.dayOfWeek]
  } : null;
  
  const basePrice = selectedType === "morning" ? currentPricing.halfDay : currentPricing.fullDay;
  const totalPrice = basePrice + cleaningFee;
  
  // Calculate advance amount for display (30% of total)
  const advanceAmount = Math.round(totalPrice * bookingConfig.payment.advancePercentage / 100);
  const displayPrice = selectedPaymentMethod === "advance" ? advanceAmount : totalPrice;

  // Modified approach: Create booking silently, show confirmation only after payment
  const bookingMutation = useMutation({
    mutationFn: async (data: BookingFormValues) => {
      if (!user) {
        throw new Error("User not authenticated");
      }

      const response = await apiRequest("POST", "/api/bookings", {
        propertyId,
        userId: user.id,
        bookingDate: data.bookingDate.toISOString(), // Send as ISO string
        bookingType: data.bookingType,
        guests: parseInt(data.guests),
        totalPrice: totalPrice,
        paymentMethod: data.paymentMethod,
      });
      const result = await response.json();
      return { ...result, formData: data };
    },
    onSuccess: (data) => {
      const booking = data.data;
      const formData = data.formData;
      
      if (formData.paymentMethod === "advance") {
        // Advance payment - show payment modal
        setBookingData({
          id: booking.id,
          propertyTitle: propertyTitle || `Property #${propertyId}`,
          bookingDate: booking.bookingDate,
          bookingType: booking.bookingType,
          guests: booking.guests,
          totalPrice: Math.round(booking.totalPrice * bookingConfig.payment.advancePercentage / 100),
          customerDetails: {
            name: user?.fullName || user?.username || '',
            email: user?.email || '',
            phone: user?.phone || ''
          },
          gstDetails: {
            supplierState: bookingConfig.gst.defaultSupplierState,
            recipientState: bookingConfig.gst.defaultRecipientState,
            serviceType: bookingConfig.gst.defaultServiceType
          }
        });
        
        setShowPaymentModal(true);
        
        // NO "Booking Created" toast - only show after payment confirmation
      } else {
        // Full amount payment - show payment modal
        setBookingData({
          id: booking.id,
          propertyTitle: propertyTitle || `Property #${propertyId}`,
          bookingDate: booking.bookingDate,
          bookingType: booking.bookingType,
          guests: booking.guests,
          totalPrice: booking.totalPrice, // Full amount instead of advance
          customerDetails: {
            name: user?.fullName || user?.username || '',
            email: user?.email || '',
            phone: user?.phone || ''
          },
          gstDetails: {
            supplierState: bookingConfig.gst.defaultSupplierState,
            recipientState: bookingConfig.gst.defaultRecipientState,
            serviceType: bookingConfig.gst.defaultServiceType
          }
        });
        
        setShowPaymentModal(true);
        
        // NO "Booking Created" toast - only show after payment confirmation
      }
    },
    onError: (error: ApiError) => {
      console.error("Booking error:", error);
      
      // Generic error message for security - no sensitive information leaked
      toast({
        title: "Booking Failed",
        description: "Unable to create booking. Please try again or contact support.",
        variant: "destructive",
        duration: 3000,
      });
    },
  });

  // Payment handlers
  const handlePaymentSuccess = (paymentData: PaymentData) => {
    // Transform PaymentData to match PaymentStatus component expectations
    const transformedPaymentData = {
      paymentId: paymentData.razorpay_payment_id,
      orderId: paymentData.verificationData?.orderId || paymentData.razorpay_order_id,
      amount: paymentData.verificationData?.amount || 0,
      currency: paymentData.verificationData?.currency || 'INR',
      ...(paymentData.verificationData?.method && { method: paymentData.verificationData.method }),
      timestamp: paymentData.verificationData?.timestamp || new Date().toISOString()
    };
    
    setPaymentData(transformedPaymentData);
    setPaymentStatus('success');
    setShowPaymentModal(false);
    
    // Show booking confirmation message only after payment succeeds
    toast({
      title: "Booking Confirmed! 🎉",
      description: "Your payment was successful and booking has been confirmed!",
      duration: 5000,
    });
    
    // Refresh property bookings to hide the newly confirmed date
    const fetchPropertyBookings = async () => {
      try {
        const response = await apiRequest("GET", `/api/bookings/property/${propertyId}/availability`);
        const bookingsData = await response.json();
        const formattedBookings = bookingsData.map((booking: any) => ({
          date: booking.date,
          type: booking.type,
          status: booking.status
        }));
        setPropertyBookings(formattedBookings);
      } catch (error) {
        console.error("Failed to refresh property bookings:", error);
      }
    };
    fetchPropertyBookings();
    
    // Reset form
    form.reset({
      bookingType: bookingConfig.ui.defaultBookingType,
      guests: "1",
      paymentMethod: bookingConfig.ui.defaultPaymentMethod,
    });
  };

  const handlePaymentFailure = (error: ApiError) => {
    setPaymentStatus('failed');
    setShowPaymentModal(false);
    
    // Generic error message for security
    toast({
      title: "Payment Failed",
      description: "Payment could not be processed. Please try again or contact support.",
      variant: "destructive",
    });
  };

  const handlePaymentRetry = () => {
    setPaymentStatus('idle');
    setShowPaymentModal(true);
  };

  const handlePaymentClose = () => {
    setPaymentStatus('idle');
    setPaymentData(null);
    setBookingData(null);
  };

  const onSubmit = (data: BookingFormValues) => {
    // Prevent multiple submissions
    if (bookingMutation.isPending) {
      console.log('Booking already in progress, ignoring duplicate submission');
      return;
    }

    // Security: Sanitize and validate all inputs before processing
    try {
      // Input sanitization
      const sanitizedData = {
        ...data,
        guests: data.guests.trim(),
        bookingType: data.bookingType.trim() as "morning" | "full_day",
        paymentMethod: data.paymentMethod.trim() as "advance" | "full_amount"
      };
      
      // Enhanced validation and error handling
      // Authentication check
      if (!isAuthenticated) {
        toast({
          title: "Login Required",
          description: "Please sign in to book this farmhouse.",
          variant: "destructive",
        });
        setShowAuthModal(true);
        return;
      }

      // Enhanced data validation with sanitized data
      if (!sanitizedData.bookingDate) {
        toast({
          title: "Date Required",
          description: "Please select a booking date.",
          variant: "destructive",
        });
        form.setFocus("bookingDate");
        return;
      }

      // Check if date is in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (sanitizedData.bookingDate < today) {
        toast({
          title: "Invalid Date",
          description: "Please select a future date for booking.",
          variant: "destructive",
        });
        form.setFocus("bookingDate");
        return;
      }

      // Check if date is too far in future (e.g., 1 year)
      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() + 1);
      if (sanitizedData.bookingDate > maxDate) {
        toast({
          title: "Date Too Far",
          description: "Please select a date within the next year.",
          variant: "destructive",
        });
        form.setFocus("bookingDate");
        return;
      }

      // Validate booking type against allowed values
      if (!sanitizedData.bookingType || !['morning', 'full_day'].includes(sanitizedData.bookingType)) {
        toast({
          title: "Booking Type Required",
          description: "Please select a booking type (12h or 24h access).",
          variant: "destructive",
        });
        return;
      }

      // Validate guests with comprehensive checks
      const guestCount = parseInt(sanitizedData.guests);
      if (!sanitizedData.guests || isNaN(guestCount) || guestCount < 1) {
        toast({
          title: "Guests Required",
          description: "Please select the number of guests.",
          variant: "destructive",
        });
        form.setFocus("guests");
        return;
      }

      if (guestCount > bookingConfig.ui.maxGuests) {
        toast({
          title: "Too Many Guests",
          description: `Maximum ${bookingConfig.ui.maxGuests} guests allowed.`,
          variant: "destructive",
        });
        form.setFocus("guests");
        return;
      }

      // Validate payment method against allowed values
      if (!sanitizedData.paymentMethod || !['advance', 'full_amount'].includes(sanitizedData.paymentMethod)) {
        toast({
          title: "Payment Method Required",
          description: "Please select a payment method.",
          variant: "destructive",
        });
        return;
      }

      // Additional security validations
      if (!propertyId || typeof propertyId !== 'number' || propertyId <= 0) {
        toast({
          title: "Invalid Property",
          description: "Property information is invalid.",
          variant: "destructive",
        });
        return;
      }

      // All validation passed, create booking and proceed to payment
      bookingMutation.mutate(sanitizedData);
      
    } catch (error) {
      console.error("Form submission error:", error);
      toast({
        title: "Submission Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Show payment status if payment completed
  if ((paymentStatus === 'success' || paymentStatus === 'failed') && paymentData && bookingData) {
    return (
      <Card className="bg-white rounded-lg border border-[#EBE6E1] shadow-md p-6 sticky top-4">
        <CardContent className="p-0">
          <PaymentStatus
            status={paymentStatus}
            paymentData={paymentData}
            bookingData={bookingData}
            onRetry={handlePaymentRetry}
            onClose={handlePaymentClose}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="bg-white rounded-lg border border-[#EBE6E1] shadow-md p-6 sticky top-4">
        <CardContent className="p-0">
          <div className="mb-6">
            <div className="flex justify-between items-baseline mb-2">
              <div className="flex items-baseline">
                <span className="text-2xl font-bold text-[#4A6741]">
                  ₹{halfDayPrice.toLocaleString('en-IN')}
                </span>
                <span className="text-[#766C63] ml-1">/ 12h access</span>
              </div>
            </div>
            <div className="flex justify-between text-[#605045] text-sm">
              <span className="font-semibold">24h access:</span>
              <span>₹{fullDayPrice.toLocaleString('en-IN')}</span>
            </div>
          </div>

          <Tabs defaultValue="booking" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="booking">Booking</TabsTrigger>
              <TabsTrigger value="payment">Payment</TabsTrigger>
            </TabsList>
            
            <TabsContent value="booking" className="mt-4">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                control={form.control}
                name="bookingDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="block text-sm font-semibold text-[#605045] mb-2">Select Date</FormLabel>
                    <FormControl>
                      <Calendar
                        selectedDate={field.value}
                        onSelectDate={(date) => field.onChange(date)}
                        bookings={propertyBookings.map((b) => ({
                          date: b.date,
                          type: b.type as "morning" | "full_day",
                          status: b.status
                        }))}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="bookingType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="block text-sm font-semibold text-[#605045] mb-2">Booking Type</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex space-x-2"
                      >
                        <div className={`flex-1 border rounded-md p-3 cursor-pointer relative ${
                          field.value === "morning" 
                            ? "border-[#4A6741] bg-[#4A6741] bg-opacity-5" 
                            : "border-[#EBE6E1] hover:border-[#D5CCC4]"
                        }`}>
                          <RadioGroupItem 
                            value="morning" 
                            id="morning"
                            className="absolute opacity-0"
                          />
                          <div className="flex flex-col items-center">
                            <label htmlFor="morning" className="text-sm font-medium text-[#2D3C2D]">12-Hour Access</label>
                            <span className="text-xs text-[#766C63]">8am - 8pm</span>
                          </div>
                          {field.value === "morning" && (
                            <div className="absolute top-2 right-2 text-[#4A6741]">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </div>
                        
                        <div className={`flex-1 border rounded-md p-3 cursor-pointer relative ${
                          field.value === "full_day" 
                            ? "border-[#4A6741] bg-[#4A6741] bg-opacity-5" 
                            : "border-[#EBE6E1] hover:border-[#D5CCC4]"
                        }`}>
                          <RadioGroupItem 
                            value="full_day" 
                            id="full_day"
                            className="absolute opacity-0"
                          />
                          <div className="flex flex-col items-center">
                            <label htmlFor="full_day" className="text-sm font-medium text-[#2D3C2D]">24-Hour Access</label>
                            <span className="text-xs text-[#766C63]">8am - 8am (next day)</span>
                          </div>
                          {field.value === "full_day" && (
                            <div className="absolute top-2 right-2 text-[#4A6741]">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="guests"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="block text-sm font-semibold text-[#605045] mb-2">Guests</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="border-[#EBE6E1]">
                          <SelectValue placeholder="Select number of guests" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: bookingConfig.ui.maxGuests }, (_, i) => i + 1).map((num) => (
                          <SelectItem key={num} value={num.toString()}>
                            {num} {num === 1 ? "guest" : "guests"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="block text-sm font-semibold text-[#605045] mb-2">Payment Method</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="space-y-3"
                      >
                        <div className={`border rounded-md p-4 cursor-pointer ${
                          field.value === "advance" 
                            ? "border-[#4A6741] bg-[#4A6741] bg-opacity-5" 
                            : "border-[#EBE6E1] hover:border-[#D5CCC4]"
                        }`}>
                          <RadioGroupItem 
                            value="advance" 
                            id="advance"
                            className="sr-only"
                          />
                          <label htmlFor="advance" className="cursor-pointer">
                            <div className="flex items-start justify-between">
                              <div>
                                <div className="font-medium text-[#2D3C2D]">Pay Advance ({bookingConfig.payment.advancePercentage}%)</div>
                                <div className="text-sm text-[#766C63] mt-1">
                                  Pay ₹{calculateAdvanceAmountDisplay(totalPrice, bookingConfig.payment.advancePercentage)} now, rest at property
                                </div>
                                <div className="text-xs text-[#766C63] mt-1">
                                  • Secure your booking instantly
                                  • Convenient online payment
                                </div>
                              </div>
                              {field.value === "advance" && (
                                <div className="text-[#4A6741]">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                            </div>
                          </label>
                        </div>
                        
                        <div className={`border rounded-md p-4 cursor-pointer ${
                          field.value === "full_amount" 
                            ? "border-[#4A6741] bg-[#4A6741] bg-opacity-5" 
                            : "border-[#EBE6E1] hover:border-[#D5CCC4]"
                        }`}>
                          <RadioGroupItem 
                            value="full_amount" 
                            id="full_amount"
                            className="sr-only"
                          />
                          <label htmlFor="full_amount" className="cursor-pointer">
                            <div className="flex items-start justify-between">
                              <div>
                                <div className="font-medium text-[#2D3C2D]">Pay Full Amount</div>
                                <div className="text-sm text-[#766C63] mt-1">
                                  Pay full amount ₹{totalPrice} online now
                                </div>
                                <div className="text-xs text-[#766C63] mt-1">
                                  • Complete payment online
                                  • Instant booking confirmation
                                </div>
                              </div>
                              {field.value === "full_amount" && (
                                <div className="text-[#4A6741]">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                            </div>
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
              
                  <div className="border-t border-[#EBE6E1] pt-4 mb-6">
                    {selectedPaymentMethod === "advance" ? (
                      <>
                        <div className="flex justify-between mb-2">
                          <span>Advance Amount (30%)</span>
                          <span>₹{advanceAmount}</span>
                        </div>
                        <div className="text-xs text-[#766C63] mb-3">
                          Full amount: ₹{totalPrice} (₹{basePrice} + ₹{cleaningFee} cleaning fee)
                        </div>
                        <div className="flex justify-between font-semibold text-[#2D3C2D] pt-2 border-t border-[#EBE6E1] mt-2">
                          <span>Pay Now</span>
                          <span>₹{advanceAmount}</span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex justify-between mb-2">
                          <span>₹{basePrice} x 1 {selectedType === "morning" ? "12h access" : "24h access"}</span>
                          <span>₹{basePrice}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span>Cleaning fee</span>
                          <span>₹{cleaningFee}</span>
                        </div>
                        <div className="flex justify-between font-semibold text-[#2D3C2D] pt-2 border-t border-[#EBE6E1] mt-2">
                          <span>Total</span>
                          <span>₹{totalPrice}</span>
                        </div>
                      </>
                    )}
                  </div>
                  
                  <Button 
                    type="submit"
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white font-semibold py-3 px-4 rounded-md"
                    disabled={bookingMutation.isPending || !selectedDate}
                  >
                    {bookingMutation.isPending ? "Processing..." : "Proceed to Payment"}
                  </Button>
                </form>
              </Form>
            </TabsContent>
            
            <TabsContent value="payment" className="mt-4">
              <PaymentSummary
                basePrice={basePrice}
                cleaningFee={cleaningFee}
                bookingType={selectedType}
                gstRate={bookingConfig.payment.gstRate}
                advancePercentage={bookingConfig.payment.advancePercentage}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      <SwiggyStyleAuthModal 
        isOpen={showAuthModal} 
        onClose={() => setShowAuthModal(false)} 
      />
      
      {bookingData && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          bookingDetails={bookingData}
          onPaymentSuccess={handlePaymentSuccess}
          onPaymentFailure={handlePaymentFailure}
        />
      )}
    </>
  );
}
