import { useState, useRef, KeyboardEvent, ChangeEvent } from "react";
import { Input } from "@/components/ui/input";

interface OTPInputProps {
  length: number;
  value: string;
  onChange: (otp: string) => void;
  disabled?: boolean;
  onComplete?: (otp: string) => void;
}

export function OTPInput({ 
  length, 
  value, 
  onChange, 
  disabled = false, 
  onComplete 
}: OTPInputProps) {
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const values = value.split('').concat(Array(length - value.length).fill(''));

  const handleChange = (index: number, inputValue: string) => {
    if (disabled) return;

    const digit = inputValue.replace(/\D/g, '').slice(-1);
    const newValues = [...values];
    newValues[index] = digit;
    
    const newOTP = newValues.join('').slice(0, length);
    onChange(newOTP);

    // Move to next input if digit was entered
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Call onComplete if all digits are filled
    if (newOTP.length === length && onComplete) {
      onComplete(newOTP);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    if (e.key === 'Backspace' && !values[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    
    if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  return (
    <div className="flex gap-3 justify-center">
      {Array(length).fill(0).map((_, index) => (
        <Input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          maxLength={1}
          value={values[index] || ''}
          onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          disabled={disabled}
          className="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 focus:border-orange-500 focus:ring-orange-500 rounded-lg"
          autoComplete="off"
        />
      ))}
    </div>
  );
}