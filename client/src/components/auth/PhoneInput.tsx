import { UseFormReturn } from "react-hook-form";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface PhoneInputProps {
  form: UseFormReturn<any>;
  disabled?: boolean;
  placeholder?: string;
}

export function PhoneInput({ form, disabled = false, placeholder = "Enter your mobile number" }: PhoneInputProps) {
  return (
    <FormField
      control={form.control}
      name="phone"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-gray-700 font-medium">Mobile Number</FormLabel>
          <FormControl>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm">+91</span>
              </div>
              <Input
                {...field}
                type="tel"
                placeholder={placeholder}
                disabled={disabled}
                className="pl-12 py-3 text-lg border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                maxLength={10}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '');
                  field.onChange(value);
                }}
              />
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}