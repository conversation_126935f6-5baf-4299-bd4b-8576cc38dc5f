import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AuthStep } from "./types";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { TIME_UNITS } from "../../../../shared/constants";

interface OTPVerificationProps {
  identifier?: string;
  type?: 'email' | 'sms';
  userData?: any;
  mode?: string;
  onVerify?: (code: string) => void;
  onSuccess?: () => void;
  onResend?: () => void;
  onBack?: () => void;
  isLoading?: boolean;
  resendCooldown?: number;
}

export function OTPVerification({ 
  identifier = '', 
  type = 'email', 
  userData,
  mode,
  onVerify, 
  onSuccess,
  onResend, 
  onBack, 
  isLoading,
  resendCooldown = 0 
}: OTPVerificationProps) {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [countdown, setCountdown] = useState(resendCooldown);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    setCountdown(resendCooldown);
  }, [resendCooldown]);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), TIME_UNITS.SECOND);
      return () => clearTimeout(timer);
    }
    return () => {};
  }, [countdown]);

  const handleChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Submit if all fields are filled
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      onVerify?.(newOtp.join(''));
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    const newOtp = pastedData.split('').concat(Array(6 - pastedData.length).fill(''));
    setOtp(newOtp.slice(0, 6));

    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      onVerify?.(newOtp.join(''));
    }
  };

  const isComplete = otp.every(digit => digit !== '');

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold">Verify Your {type === 'email' ? 'Email' : 'Phone'}</h3>
        <p className="text-sm text-gray-600">
          We've sent a 6-digit code to{" "}
          <span className="font-medium">{identifier}</span>
        </p>
      </div>

      <div className="space-y-4">
        <Label className="text-center block">Enter verification code</Label>
        <div className="flex gap-2 justify-center" onPaste={handlePaste}>
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={(el) => (inputRefs.current[index] = el)}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              className="w-12 h-12 text-center text-lg font-semibold"
            />
          ))}
        </div>
      </div>

      <div className="space-y-3">
        <Button
          onClick={() => onVerify?.(otp.join(''))}
          disabled={!isComplete || isLoading}
          className="w-full"
        >
          {isLoading ? "Verifying..." : "Verify Code"}
        </Button>

        <div className="text-center space-y-2">
          <button
            type="button"
            onClick={onResend}
            disabled={countdown > 0}
            className="text-sm text-blue-600 hover:underline disabled:text-gray-400 disabled:no-underline"
          >
            {countdown > 0 ? `Resend code in ${countdown}s` : "Resend code"}
          </button>

          <p className="text-sm">
            <button
              type="button"
              onClick={onBack}
              className="text-blue-600 hover:underline"
            >
              Use a different {type === 'email' ? 'email' : 'phone number'}
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}