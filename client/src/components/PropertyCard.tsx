import React, { useState } from "react";
import { Link } from "wouter";
import { Property } from "@shared/schema";
import { useFavorites } from "@/contexts/FavoritesContext";
import { useToast } from "@/hooks/use-toast";
import PropertyQuickPreview from "./PropertyQuickPreview";
import SocialShare from "./SocialShare";
import { getProxiedImageUrl } from "@/lib/imageProxy";

interface PropertyCardProps {
  property: Property;
}

export default function PropertyCard({ property }: PropertyCardProps) {
  const {
    id,
    title,
    location,
    images,
    halfDayPrice,
    bedrooms,
    bathrooms,
    amenities,
    featured
  } = property;

  const { isFavorite, toggleFavorite } = useFavorites();
  const { toast } = useToast();
  const [showQuickPreview, setShowQuickPreview] = useState(false);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const wasAlreadyFavorite = isFavorite(id);
    toggleFavorite(id);
    
    // Show correct toast message based on previous state
    toast({
      title: wasAlreadyFavorite ? "Removed from favorites" : "Added to favorites",
      description: wasAlreadyFavorite 
        ? `${title} has been removed from your wishlist` 
        : `${title} has been added to your wishlist`,
      duration: 3000, // Auto-dismiss after 3 seconds
      className: wasAlreadyFavorite 
        ? "bg-red-50 border-red-200 text-red-800" 
        : "bg-green-50 border-green-200 text-green-800",
    });
  };

  const handleCardClick = () => {
    setShowQuickPreview(true);
  };

  return (
    <>
      <div 
        data-testid="property-card"
        className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out group cursor-pointer"
        onClick={handleCardClick}
      >
        {/* Property Image */}
        <div className="relative overflow-hidden">
          <img 
            src={getProxiedImageUrl(images[0] || "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80")}
            alt={title}
            className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500 ease-in-out"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = getProxiedImageUrl("https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80");
            }}
          />
        </div>
          {featured && (
            <div className="absolute top-4 left-4 transform group-hover:scale-105 transition-transform duration-300">
              <span className="bg-white px-2 py-1 rounded text-sm font-semibold text-[#4A6741] shadow-sm">Featured</span>
            </div>
          )}
          <div className="absolute top-4 right-4 z-10 flex flex-col space-y-2">
            <button 
              onClick={handleFavoriteClick}
              className={`w-10 h-10 rounded-full transition-all duration-300 hover:scale-110 shadow-md flex items-center justify-center ${
                isFavorite(id) 
                  ? 'bg-red-500 text-white' 
                  : 'bg-white/90 text-red-400 hover:bg-white hover:text-red-500'
              }`}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className={`h-5 w-5 transition-all duration-300 ${
                  isFavorite(id) ? 'animate-pulse' : ''
                }`} 
                fill={isFavorite(id) ? "currentColor" : "none"} 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={isFavorite(id) ? 0 : 2} 
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
                />
              </svg>
            </button>
            
            <div 
              className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              onClick={(e) => e.stopPropagation()}
            >
              <SocialShare 
                url={`${window.location.origin}/property/${id}`}
                title={`${title} - Beautiful Farmhouse in ${location}`}
                description={`Experience rural life at ${title}. Starting from ₹${halfDayPrice} for morning access.`}
                image={images[0]}
                className="bg-white text-[#4A6741] hover:bg-[#F7F4F1] shadow-lg !p-2"
              />
            </div>
          </div>
          
          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
        </div>
      
      <div className="p-5 group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-gray-50 transition-all duration-300">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-heading text-xl font-bold text-[#2D3C2D] group-hover:text-[#4A6741] transition-colors duration-300">{title}</h3>
        </div>
        
        <p className="text-[#605045] mb-3 group-hover:text-[#4A6741] transition-colors duration-300">{location}</p>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="px-2 py-1 bg-[#F7F4F1] text-[#605045] rounded-md text-xs group-hover:bg-[#4A6741] group-hover:text-white transition-all duration-300 transform group-hover:scale-105">{bedrooms} Bedrooms</span>
          <span className="px-2 py-1 bg-[#F7F4F1] text-[#605045] rounded-md text-xs group-hover:bg-[#4A6741] group-hover:text-white transition-all duration-300 transform group-hover:scale-105">{bathrooms} Bathrooms</span>
          {amenities.slice(0, 1).map((amenity, index) => (
            <span key={index} className="px-2 py-1 bg-[#F7F4F1] text-[#605045] rounded-md text-xs group-hover:bg-[#4A6741] group-hover:text-white transition-all duration-300 transform group-hover:scale-105">{amenity}</span>
          ))}
        </div>
        
        <div className="flex justify-between items-end">
          <div className="transform group-hover:scale-105 transition-transform duration-300">
            <span className="block text-sm text-[#766C63] group-hover:text-[#4A6741] transition-colors duration-300">Starting from</span>
            <div className="flex items-baseline">
              <span className="text-xl font-bold text-[#4A6741] group-hover:text-[#2D3C2D] transition-colors duration-300">₹{halfDayPrice.toLocaleString()}</span>
              <span className="text-[#766C63] ml-1 group-hover:text-[#4A6741] transition-colors duration-300">/ 12h access</span>
            </div>
          </div>
          <Link href={`/property/${id}`} className="px-4 py-2 bg-[#4A6741] text-white rounded-md hover:bg-[#3A5131] hover:shadow-lg transform hover:-translate-y-1 hover:scale-105 transition-all duration-300 ease-in-out">
            View Details
          </Link>
        </div>
      </div>

      {/* Quick Preview Modal */}
      <PropertyQuickPreview 
        property={property}
        isOpen={showQuickPreview}
        onClose={() => setShowQuickPreview(false)}
      />
    </>
  );
}
