import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/contexts/AuthContext";

export default function CookieConsent() {
  const [isVisible, setIsVisible] = useState(false);
  const [, navigate] = useLocation();
  const { user, isAuthenticated, loading } = useAuth();
  
  useEffect(() => {
    // Only show cookie banner if user is logged in
    if (loading) return undefined; // Wait for auth to finish loading
    
    if (!isAuthenticated || !user) {
      setIsVisible(false);
      return undefined;
    }
    
    // Check if this specific user has already given consent
    const userConsentKey = `cookieConsent_${user.id}`;
    const hasConsent = localStorage.getItem(userConsentKey);
    
    if (!hasConsent) {
      // Show the banner after a short delay for better user experience
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1500);
      
      return () => clearTimeout(timer);
    }
    
    return undefined;
  }, [isAuthenticated, user, loading]);
  
  const acceptAll = () => {
    if (user) {
      const userConsentKey = `cookieConsent_${user.id}`;
      localStorage.setItem(userConsentKey, "all");
      setIsVisible(false);
    }
  };
  
  const acceptEssential = () => {
    if (user) {
      const userConsentKey = `cookieConsent_${user.id}`;
      localStorage.setItem(userConsentKey, "essential");
      setIsVisible(false);
    }
  };
  
  const viewPrivacyPolicy = () => {
    navigate("/privacy-policy");
  };
  
  if (!isVisible) return null;
  
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white p-4 shadow-lg border-t border-[#D5CCC4] animate-in slide-in-from-bottom duration-300">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-[#2D3C2D] mb-2">We Value Your Privacy</h3>
            <p className="text-[#605045] text-sm">
              We use cookies to enhance your browsing experience, serve personalized ads or content, 
              and analyze our traffic. By clicking "Accept All", you consent to our use of cookies as 
              described in our <button 
                onClick={viewPrivacyPolicy}
                className="text-[#4A6741] underline hover:text-[#3A5131]"
              >
                Privacy Policy
              </button>.
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
            <Button 
              variant="outline" 
              onClick={acceptEssential}
              className="border-[#D5CCC4] text-[#605045] hover:bg-[#F7F4F1] hover:text-[#4A6741]"
            >
              Essential Only
            </Button>
            
            <Button 
              onClick={acceptAll}
              className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
            >
              Accept All
            </Button>
            
            <button 
              onClick={() => setIsVisible(false)} 
              className="p-2 text-[#605045] hover:text-[#4A6741] rounded-full"
              aria-label="Close cookie consent"
            >
              <X size={20} />
            </button>
          </div>
        </div>
        
        <Separator className="my-3 bg-[#D5CCC4]" />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-[#605045]">
          <div>
            <h4 className="font-semibold mb-1">Essential Cookies</h4>
            <p>These cookies are necessary for the website to function and cannot be switched off.</p>
          </div>
          
          <div>
            <h4 className="font-semibold mb-1">Analytics Cookies</h4>
            <p>Help us understand how visitors interact with our website, helping us improve the user experience.</p>
          </div>
          
          <div>
            <h4 className="font-semibold mb-1">Marketing Cookies</h4>
            <p>These cookies are used to track visitors across websites to display relevant advertisements.</p>
          </div>
        </div>
      </div>
    </div>
  );
}