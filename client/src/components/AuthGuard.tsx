import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ShieldX, Lock, UserX } from 'lucide-react';
import { Property } from '@shared/schema';

interface BaseAuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showMessage?: boolean;
  messageType?: 'alert' | 'button' | 'text';
  customMessage?: string;
}

interface RoleAuthGuardProps extends BaseAuthGuardProps {
  roles: string[];
  requireAll?: boolean;
}

interface PropertyAuthGuardProps extends BaseAuthGuardProps {
  property?: Property | null;
  requireOwnership?: boolean;
}

interface ActionAuthGuardProps extends BaseAuthGuardProps {
  action: 'view' | 'edit' | 'delete' | 'manage' | 'approve' | 'reject' | 'create';
  resource?: 'property' | 'booking' | 'media' | 'pricing' | 'analytics';
  resourceData?: any;
}

// Base authentication guard
export function AuthGuard({ 
  children, 
  fallback, 
  showMessage = true, 
  messageType = 'alert',
  customMessage
}: BaseAuthGuardProps) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin h-6 w-6 border-2 border-blue-600 border-t-transparent rounded-full" />
        <span className="ml-2 text-sm text-gray-600">Checking permissions...</span>
      </div>
    );
  }

  if (!user) {
    if (fallback) return <>{fallback}</>;
    
    if (!showMessage) return null;

    const message = customMessage || "You need to be logged in to access this feature.";
    
    switch (messageType) {
      case 'alert':
        return (
          <Alert className="border-yellow-200 bg-yellow-50">
            <Lock className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              {message}
            </AlertDescription>
          </Alert>
        );
      case 'text':
        return (
          <div className="flex items-center text-sm text-gray-600 p-2">
            <Lock className="h-4 w-4 mr-2" />
            {message}
          </div>
        );
      case 'button':
        return (
          <div className="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <Lock className="h-6 w-6 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600">{message}</p>
          </div>
        );
      default:
        return null;
    }
  }

  return <>{children}</>;
}

// Role-based authorization guard
export function RoleGuard({ 
  children, 
  roles, 
  requireAll = false, 
  fallback, 
  showMessage = true,
  messageType = 'alert',
  customMessage 
}: RoleAuthGuardProps) {
  const { user } = useAuth();

  const hasRequiredRoles = user ? (
    requireAll 
      ? roles.every(role => user.role === role)
      : roles.includes(user.role)
  ) : false;

  if (!hasRequiredRoles) {
    if (fallback) return <>{fallback}</>;
    
    if (!showMessage) return null;

    const message = customMessage || `You need ${roles.join(' or ')} privileges to access this feature.`;
    
    switch (messageType) {
      case 'alert':
        return (
          <Alert className="border-red-200 bg-red-50">
            <ShieldX className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {message}
            </AlertDescription>
          </Alert>
        );
      case 'text':
        return (
          <div className="flex items-center text-sm text-gray-600 p-2">
            <ShieldX className="h-4 w-4 mr-2" />
            {message}
          </div>
        );
      case 'button':
        return (
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <ShieldX className="h-6 w-6 mx-auto mb-2 text-red-400" />
            <p className="text-sm text-red-600">{message}</p>
          </div>
        );
      default:
        return null;
    }
  }

  return <>{children}</>;
}

// Property ownership authorization guard
export function PropertyGuard({ 
  children, 
  property, 
  requireOwnership = true,
  fallback, 
  showMessage = true,
  messageType = 'alert',
  customMessage 
}: PropertyAuthGuardProps) {
  const { user } = useAuth();

  if (!property) {
    if (fallback) return <>{fallback}</>;
    return null;
  }

  const isOwner = user?.role === 'owner' && user?.id === property.ownerId;
  const isAdmin = user?.role === 'admin';
  const hasAccess = !requireOwnership || isOwner || isAdmin;

  if (!hasAccess) {
    if (fallback) return <>{fallback}</>;
    
    if (!showMessage) return null;

    const message = customMessage || "You can only manage properties that you own.";
    
    switch (messageType) {
      case 'alert':
        return (
          <Alert className="border-red-200 bg-red-50">
            <UserX className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {message}
            </AlertDescription>
          </Alert>
        );
      case 'text':
        return (
          <div className="flex items-center text-sm text-gray-600 p-2">
            <UserX className="h-4 w-4 mr-2" />
            {message}
          </div>
        );
      case 'button':
        return (
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <UserX className="h-6 w-6 mx-auto mb-2 text-red-400" />
            <p className="text-sm text-red-600">{message}</p>
          </div>
        );
      default:
        return null;
    }
  }

  return <>{children}</>;
}

// Action-based authorization guard
export function ActionGuard({ 
  children, 
  action, 
  resource = 'property', 
  resourceData,
  fallback, 
  showMessage = true,
  messageType = 'alert',
  customMessage 
}: ActionAuthGuardProps) {
  const { user } = useAuth();

  const hasPermission = checkActionPermission(user, action, resource, resourceData);

  if (!hasPermission) {
    if (fallback) return <>{fallback}</>;
    
    if (!showMessage) return null;

    const message = customMessage || `You are not authorized to ${action} this ${resource}.`;
    
    switch (messageType) {
      case 'alert':
        return (
          <Alert className="border-red-200 bg-red-50">
            <ShieldX className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {message}
            </AlertDescription>
          </Alert>
        );
      case 'text':
        return (
          <div className="flex items-center text-sm text-gray-600 p-2">
            <ShieldX className="h-4 w-4 mr-2" />
            {message}
          </div>
        );
      case 'button':
        return (
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <ShieldX className="h-6 w-6 mx-auto mb-2 text-red-400" />
            <p className="text-sm text-red-600">{message}</p>
          </div>
        );
      default:
        return null;
    }
  }

  return <>{children}</>;
}

// Owner-specific guard for dashboard features
export function OwnerGuard({ 
  children, 
  fallback, 
  showMessage = true,
  messageType = 'alert',
  customMessage = "This feature is only available to property owners."
}: BaseAuthGuardProps) {
  return (
    <RoleGuard 
      roles={['owner', 'admin']} 
      fallback={fallback}
      showMessage={showMessage}
      messageType={messageType}
      customMessage={customMessage}
    >
      {children}
    </RoleGuard>
  );
}

// Admin-specific guard
export function AdminGuard({ 
  children, 
  fallback, 
  showMessage = true,
  messageType = 'alert',
  customMessage = "This feature is only available to administrators."
}: BaseAuthGuardProps) {
  return (
    <RoleGuard 
      roles={['admin']} 
      fallback={fallback}
      showMessage={showMessage}
      messageType={messageType}
      customMessage={customMessage}
    >
      {children}
    </RoleGuard>
  );
}

// Helper function to check action permissions
function checkActionPermission(
  user: any,
  action: string,
  resource: string,
  resourceData?: any
): boolean {
  if (!user) return false;

  const { role, id: userId } = user;

  // Admins can do everything
  if (role === 'admin') return true;

  switch (action) {
    case 'view':
      return true; // Most things can be viewed by authenticated users

    case 'create':
      switch (resource) {
        case 'property':
          return role === 'owner';
        case 'booking':
          return true; // Any authenticated user can create bookings
        default:
          return role === 'owner';
      }

    case 'edit':
    case 'delete':
    case 'manage':
      switch (resource) {
        case 'property':
          return role === 'owner' && resourceData?.ownerId === userId;
        case 'booking':
          // Owners can manage bookings for their properties
          if (role === 'owner' && resourceData?.property?.ownerId === userId) {
            return true;
          }
          // Users can manage their own bookings
          return resourceData?.userId === userId;
        case 'media':
        case 'pricing':
          return role === 'owner' && resourceData?.ownerId === userId;
        default:
          return false;
      }

    case 'approve':
    case 'reject':
      switch (resource) {
        case 'booking':
          return role === 'owner' && resourceData?.property?.ownerId === userId;
        default:
          return false;
      }

    default:
      return false;
  }
}

// Compound export for convenience
export const Guards = {
  Auth: AuthGuard,
  Role: RoleGuard,
  Property: PropertyGuard,
  Action: ActionGuard,
  Owner: OwnerGuard,
  Admin: AdminGuard
};

export default AuthGuard;