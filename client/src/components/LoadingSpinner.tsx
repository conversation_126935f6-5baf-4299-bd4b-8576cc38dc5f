import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  theme?: "tractor" | "windmill" | "barn" | "harvest";
  text?: string;
}

export default function LoadingSpinner({ 
  size = "md", 
  className,
  theme = "tractor",
  text
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12", 
    lg: "w-16 h-16",
    xl: "w-24 h-24"
  };

  const textSizes = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg", 
    xl: "text-xl"
  };

  const TractorLoader = () => (
    <div className="relative">
      <svg 
        className={cn("animate-bounce", sizeClasses[size])} 
        viewBox="0 0 120 80" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Farm field ground */}
        <rect x="0" y="70" width="120" height="10" fill="#8B4513" opacity="0.3" />
        
        {/* Tractor body */}
        <rect x="35" y="35" width="40" height="25" rx="4" fill="#4A6741" />
        
        {/* Cabin */}
        <rect x="55" y="20" width="20" height="20" rx="3" fill="#2D3C2D" />
        <rect x="57" y="22" width="6" height="6" fill="#87CEEB" opacity="0.7" />
        <rect x="66" y="22" width="6" height="6" fill="#87CEEB" opacity="0.7" />
        
        {/* Large rear wheel */}
        <circle 
          cx="60" 
          cy="65" 
          r="12" 
          fill="#8B4513" 
          className="animate-spin origin-center" 
          style={{animationDuration: '2s'}} 
        />
        <circle cx="60" cy="65" r="8" fill="#654321" />
        <circle cx="60" cy="65" r="4" fill="#8B4513" />
        
        {/* Small front wheel */}
        <circle 
          cx="40" 
          cy="65" 
          r="8" 
          fill="#8B4513" 
          className="animate-spin origin-center" 
          style={{animationDuration: '1.5s'}} 
        />
        <circle cx="40" cy="65" r="5" fill="#654321" />
        <circle cx="40" cy="65" r="2" fill="#8B4513" />
        
        {/* Exhaust pipe */}
        <rect x="32" y="30" width="3" height="10" fill="#666" />
        
        {/* Animated exhaust smoke */}
        <circle cx="33.5" cy="25" r="2" fill="#E6E6FA" className="animate-ping" />
        <circle cx="31" cy="22" r="1.5" fill="#E6E6FA" className="animate-pulse" style={{animationDelay: '0.5s'}} />
        
        {/* Front loader */}
        <rect x="25" y="40" width="15" height="3" fill="#C0C0C0" />
        <rect x="22" y="37" width="8" height="6" fill="#A0A0A0" />
      </svg>
    </div>
  );

  const WindmillLoader = () => (
    <div className="relative">
      <svg 
        className={sizeClasses[size]} 
        viewBox="0 0 100 100" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Base/Foundation */}
        <ellipse cx="50" cy="85" rx="15" ry="5" fill="#8B4513" opacity="0.5" />
        
        {/* Tower */}
        <rect x="47" y="40" width="6" height="50" fill="#D2B48C" />
        
        {/* Tower segments */}
        <line x1="44" y1="50" x2="56" y2="50" stroke="#8B4513" strokeWidth="1" />
        <line x1="44" y1="65" x2="56" y2="65" stroke="#8B4513" strokeWidth="1" />
        <line x1="44" y1="80" x2="56" y2="80" stroke="#8B4513" strokeWidth="1" />
        
        {/* Rotating blades */}
        <g 
          className="animate-spin origin-center" 
          style={{transformOrigin: '50px 40px', animationDuration: '3s', animationTimingFunction: 'ease-in-out'}}
        >
          {/* Blade 1 */}
          <path d="M50 15 L48 35 L52 35 Z" fill="#4A6741" />
          <ellipse cx="50" cy="20" rx="8" ry="3" fill="#228B22" />
          
          {/* Blade 2 */}
          <path d="M25 38 L45 42 L45 38 Z" fill="#4A6741" />
          <ellipse cx="30" cy="40" rx="3" ry="8" fill="#228B22" />
          
          {/* Blade 3 */}
          <path d="M50 65 L52 45 L48 45 Z" fill="#4A6741" />
          <ellipse cx="50" cy="60" rx="8" ry="3" fill="#228B22" />
          
          {/* Blade 4 */}
          <path d="M75 42 L55 38 L55 42 Z" fill="#4A6741" />
          <ellipse cx="70" cy="40" rx="3" ry="8" fill="#228B22" />
        </g>
        
        {/* Central hub */}
        <circle cx="50" cy="40" r="4" fill="#2D3C2D" />
        <circle cx="50" cy="40" r="2" fill="#FFD700" className="animate-pulse" />
      </svg>
    </div>
  );

  const BarnLoader = () => (
    <div className="relative">
      <svg 
        className={cn("animate-pulse", sizeClasses[size])} 
        viewBox="0 0 100 100" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Ground */}
        <rect x="0" y="85" width="100" height="15" fill="#228B22" opacity="0.3" />
        
        {/* Barn structure */}
        <rect x="20" y="50" width="60" height="40" fill="#B22222" />
        
        {/* Roof */}
        <polygon points="50,25 15,50 85,50" fill="#8B0000" />
        
        {/* Roof ridge */}
        <rect x="15" y="48" width="70" height="4" fill="#654321" />
        
        {/* Main doors */}
        <rect x="40" y="60" width="20" height="30" fill="#654321" />
        <rect x="49" y="60" width="2" height="30" fill="#4A6741" />
        <circle cx="44" cy="75" r="1" fill="#FFD700" />
        <circle cx="56" cy="75" r="1" fill="#FFD700" />
        
        {/* Hay loft window */}
        <rect x="45" y="35" width="10" height="8" fill="#FFD700" className="animate-pulse" />
        
        {/* Side window */}
        <circle cx="70" cy="65" r="4" fill="#FFD700" className="animate-pulse" style={{animationDelay: '0.5s'}} />
        
        {/* Weather vane */}
        <rect x="49" y="25" width="2" height="8" fill="#C0C0C0" />
        <polygon points="50,20 48,25 52,25" fill="#FFD700" className="animate-spin" style={{animationDuration: '4s'}} />
      </svg>
    </div>
  );

  const HarvestLoader = () => (
    <div className="relative">
      <div className={cn("flex items-end justify-center space-x-1", sizeClasses[size])}>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex flex-col items-center">
            <svg 
              className="animate-bounce w-4 h-8" 
              style={{animationDelay: `${i * 0.15}s`}}
              viewBox="0 0 20 40" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* Wheat head */}
              <ellipse cx="10" cy="8" rx="5" ry="8" fill="#DAA520" />
              <ellipse cx="10" cy="6" rx="3" ry="4" fill="#FFD700" />
              
              {/* Wheat kernels */}
              <circle cx="8" cy="6" r="1" fill="#B8860B" />
              <circle cx="12" cy="8" r="1" fill="#B8860B" />
              <circle cx="8" cy="10" r="1" fill="#B8860B" />
              <circle cx="12" cy="12" r="1" fill="#B8860B" />
              
              {/* Stem */}
              <rect x="9" y="16" width="2" height="20" fill="#228B22" />
              
              {/* Leaves */}
              <ellipse cx="6" cy="20" rx="3" ry="1.5" fill="#32CD32" transform="rotate(-30 6 20)" />
              <ellipse cx="14" cy="24" rx="3" ry="1.5" fill="#32CD32" transform="rotate(30 14 24)" />
              <ellipse cx="6" cy="28" rx="2.5" ry="1" fill="#228B22" transform="rotate(-20 6 28)" />
            </svg>
          </div>
        ))}
      </div>
    </div>
  );

  const renderLoader = () => {
    switch (theme) {
      case 'tractor':
        return <TractorLoader />;
      case 'windmill':
        return <WindmillLoader />;
      case 'barn':
        return <BarnLoader />;
      case 'harvest':
        return <HarvestLoader />;
      default:
        return <TractorLoader />;
    }
  };

  return (
    <div className={cn("flex flex-col items-center justify-center space-y-3", className)}>
      {renderLoader()}
      {text && (
        <p className={cn("text-[#605045] font-medium animate-pulse", textSizes[size])}>
          {text}
        </p>
      )}
    </div>
  );
}