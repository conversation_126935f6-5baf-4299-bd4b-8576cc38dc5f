import { useState, useRef, useCallback, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Upload, FileVideo, ImageIcon, CheckCircle, AlertCircle, X, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CloudinaryConfig {
  cloudName: string;
  apiKey: string;
  uploadUrl: string;
  maxFileSize: {
    image: number;
    video: number;
  };
  allowedFormats: {
    image: string[];
    video: string[];
  };
}

interface UploadResult {
  public_id: string;
  secure_url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  bytes: number;
  duration?: number;
  thumbnail_url?: string;
}

interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  result?: UploadResult;
  error?: string;
  preview?: string;
}

interface CloudinaryUploadProps {
  onUploadComplete: (results: UploadResult[]) => void;
  propertyId: number;
  maxFiles?: number;
  acceptedTypes?: string[];
  folder?: string;
}

export const CloudinaryUpload = ({ 
  onUploadComplete, 
  propertyId, 
  maxFiles = 10, 
  acceptedTypes = ['image/jpeg', 'image/png', 'video/mp4', 'video/webm'],
  folder = 'farmhouse-properties'
}: CloudinaryUploadProps) => {
  const [config, setConfig] = useState<CloudinaryConfig | null>(null);
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Fetch Cloudinary configuration
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/cloudinary/config', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem("token")}`,
          },
        });
        
        if (response.ok) {
          const data = await response.json();
          setConfig(data);
        }
      } catch (error) {
        console.error('Failed to fetch Cloudinary config:', error);
      }
    };
    
    fetchConfig();
  }, []);

  // Generate file preview
  const generatePreview = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }, []);

  // Validate file
  const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
    if (!config) return { valid: false, error: 'Configuration not loaded' };
    
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      return { 
        valid: false, 
        error: `Invalid file type. Accepted: ${acceptedTypes.join(', ')}` 
      };
    }
    
    // Check file size
    const maxSize = isImage ? config.maxFileSize.image : config.maxFileSize.video;
    if (file.size > maxSize) {
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(1);
      return { 
        valid: false, 
        error: `File too large. Maximum ${maxSizeMB}MB for ${isImage ? 'images' : 'videos'}` 
      };
    }
    
    return { valid: true };
  }, [config, acceptedTypes]);

  // Upload single file to Cloudinary
  const uploadToCloudinary = useCallback(async (
    file: File, 
    signedParams: any, 
    onProgress: (progress: number) => void
  ): Promise<UploadResult> => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      
      // Add all signed parameters
      Object.keys(signedParams).forEach(key => {
        if (key !== 'upload_url') {
          formData.append(key, signedParams[key]);
        }
      });
      
      formData.append('file', file);
      
      const xhr = new XMLHttpRequest();
      
      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 80); // Reserve 20% for processing
          onProgress(progress);
        }
      });
      
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const result = JSON.parse(xhr.responseText);
            
            // Generate thumbnail URL for videos
            if (result.resource_type === 'video') {
              result.thumbnail_url = `https://res.cloudinary.com/${config?.cloudName}/video/upload/so_0,w_400,h_300,c_fill/${result.public_id}.jpg`;
            }
            
            onProgress(100);
            resolve(result);
          } catch (error) {
            reject(new Error('Failed to parse upload response'));
          }
        } else {
          reject(new Error(`Upload failed: ${xhr.statusText}`));
        }
      });
      
      xhr.addEventListener('error', () => {
        reject(new Error('Upload request failed'));
      });
      
      xhr.open('POST', signedParams.upload_url);
      xhr.send(formData);
    });
  }, [config]);

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || !config) return;
    
    const selectedFiles = Array.from(files).slice(0, maxFiles);
    
    // Validate all files first
    const validationResults = selectedFiles.map(file => ({
      file,
      validation: validateFile(file)
    }));
    
    const invalidFiles = validationResults.filter(r => !r.validation.valid);
    if (invalidFiles.length > 0) {
      invalidFiles.forEach(({ file, validation }) => {
        toast({
          title: "Upload Error",
          description: `${file.name}: ${validation.error}`,
          variant: "destructive",
        });
      });
      return;
    }
    
    const validFiles = validationResults.map(r => r.file);
    setIsUploading(true);
    
    // Create upload progress entries
    const uploadEntries: UploadProgress[] = await Promise.all(
      validFiles.map(async (file) => ({
        file,
        progress: 0,
        status: 'uploading' as const,
        preview: await generatePreview(file)
      }))
    );
    
    setUploads(uploadEntries);
    
    toast({
      title: "Upload Started",
      description: `Processing ${validFiles.length} file(s) with Cloudinary optimization...`,
      duration: 3000,
    });
    
    try {
      const results: UploadResult[] = [];
      
      // Upload files sequentially to avoid overwhelming the server
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];
        const isVideo = file.type.startsWith('video/');
        
        try {
          // Get signed upload parameters
          const signatureResponse = await fetch('/api/cloudinary/upload-signature', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem("token")}`,
            },
            body: JSON.stringify({
              folder,
              resourceType: isVideo ? 'video' : 'image',
              tags: [`property_${propertyId}`, isVideo ? 'video' : 'image'],
              context: {
                property_id: propertyId
              }
            }),
          });
          
          if (!signatureResponse.ok) {
            throw new Error('Failed to get upload signature');
          }
          
          const signedParams = await signatureResponse.json();
          
          // Update status to processing
          setUploads(prev => prev.map((upload, index) => 
            index === i ? { ...upload, status: 'processing' } : upload
          ));
          
          // Upload to Cloudinary
          const result = await uploadToCloudinary(
            file, 
            signedParams, 
            (progress) => {
              setUploads(prev => prev.map((upload, index) => 
                index === i ? { ...upload, progress } : upload
              ));
            }
          );
          
          results.push(result);
          
          // Update to complete
          setUploads(prev => prev.map((upload, index) => 
            index === i ? { 
              ...upload, 
              status: 'complete', 
              result,
              progress: 100
            } : upload
          ));
          
          // Show individual success for videos (they take longer)
          if (isVideo) {
            toast({
              title: "Video Optimized",
              description: `${file.name} processed successfully with multiple quality options!`,
              duration: 4000,
            });
          }
          
        } catch (error: any) {
          console.error(`Upload failed for ${file.name}:`, error);
          
          setUploads(prev => prev.map((upload, index) => 
            index === i ? { 
              ...upload, 
              status: 'error',
              error: error.message
            } : upload
          ));
          
          toast({
            title: "Upload Failed",
            description: `${file.name}: ${error.message}`,
            variant: "destructive",
            duration: 5000,
          });
        }
      }
      
      // Final success notification
      if (results.length > 0) {
        const videoCount = results.filter(r => r.resource_type === 'video').length;
        const imageCount = results.filter(r => r.resource_type === 'image').length;
        
        let successMessage = `Successfully uploaded ${results.length} file(s)!`;
        if (videoCount > 0 && imageCount > 0) {
          successMessage = `✅ Uploaded ${imageCount} image(s) and ${videoCount} video(s) with Cloudinary optimization!`;
        } else if (videoCount > 0) {
          successMessage = `🎬 Uploaded ${videoCount} video(s) with streaming optimization!`;
        } else {
          successMessage = `📸 Uploaded ${imageCount} image(s) with auto-optimization!`;
        }
        
        toast({
          title: "Upload Complete",
          description: successMessage,
          duration: 5000,
        });
        
        onUploadComplete(results);
        
        // Clear uploads after a delay
        setTimeout(() => {
          setUploads([]);
        }, 3000);
      }
      
    } catch (error: any) {
      console.error('Upload process failed:', error);
      toast({
        title: "Upload Process Failed",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [config, maxFiles, validateFile, generatePreview, uploadToCloudinary, folder, propertyId, toast, onUploadComplete]);

  const removeUpload = useCallback((index: number) => {
    setUploads(prev => prev.filter((_, i) => i !== index));
  }, []);

  if (!config) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
        <div className="animate-pulse">
          <div className="w-8 h-8 bg-gray-300 rounded-full mx-auto mb-2"></div>
          <div className="text-sm text-gray-500">Loading Cloudinary services...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Enhanced Upload Area */}
      <div 
        className="border-2 border-dashed border-blue-200 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer bg-gradient-to-br from-blue-50 to-indigo-50"
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="flex flex-col items-center space-y-3">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Upload className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="font-medium text-blue-900 mb-1">Upload with Cloudinary</h3>
            <p className="text-sm text-blue-700">
              Advanced optimization • Progress tracking • Instant thumbnails
            </p>
          </div>
          <div className="grid grid-cols-2 gap-4 text-xs text-blue-600 mt-2">
            <div className="flex items-center space-x-1">
              <ImageIcon className="w-3 h-3" />
              <span>Images: {config.allowedFormats.image.join(', ').toUpperCase()}</span>
            </div>
            <div className="flex items-center space-x-1">
              <FileVideo className="w-3 h-3" />
              <span>Videos: {config.allowedFormats.video.join(', ').toUpperCase()}</span>
            </div>
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">
              Upload Progress ({uploads.filter(u => u.status === 'complete').length}/{uploads.length})
            </h4>
            {!isUploading && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setUploads([])}
                className="text-gray-600"
              >
                Clear All
              </Button>
            )}
          </div>
          
          {uploads.map((upload, index) => (
            <div key={index} className="bg-white border rounded-lg p-4">
              <div className="flex items-center space-x-3">
                {/* Preview */}
                <div className="w-16 h-12 bg-gray-100 rounded flex items-center justify-center flex-shrink-0 overflow-hidden">
                  {upload.preview ? (
                    upload.file.type.startsWith('image/') ? (
                      <img 
                        src={upload.preview} 
                        alt={upload.file.name}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <div className="relative w-full h-full bg-gray-900 rounded">
                        <video 
                          src={upload.preview} 
                          className="w-full h-full object-cover rounded"
                          muted
                        />
                        <FileVideo className="absolute inset-0 m-auto w-4 h-4 text-white" />
                      </div>
                    )
                  ) : (
                    upload.file.type.startsWith('image/') ? (
                      <ImageIcon className="w-6 h-6 text-gray-400" />
                    ) : (
                      <FileVideo className="w-6 h-6 text-gray-400" />
                    )
                  )}
                </div>
                
                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {upload.file.name}
                    </p>
                    <div className="flex items-center space-x-2">
                      {upload.status === 'complete' && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                      {upload.status === 'error' && (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      )}
                      {upload.status !== 'uploading' && (
                        <button
                          onClick={() => removeUpload(index)}
                          className="p-1 hover:bg-gray-100 rounded"
                        >
                          <X className="w-3 h-3 text-gray-400" />
                        </button>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-2">
                    {(upload.file.size / 1024 / 1024).toFixed(1)}MB • {upload.file.type}
                    {upload.status === 'processing' && ' • Optimizing...'}
                    {upload.status === 'complete' && ' • Ready!'}
                    {upload.error && ` • Error: ${upload.error}`}
                  </div>
                  
                  {upload.status !== 'error' && (
                    <Progress value={upload.progress} className="h-2" />
                  )}
                  
                  {upload.result && (
                    <div className="mt-2 flex items-center space-x-4 text-xs text-gray-600">
                      <span>{upload.result.width}×{upload.result.height}</span>
                      <span>{upload.result.format.toUpperCase()}</span>
                      {upload.result.duration && (
                        <span>{Math.round(upload.result.duration)}s</span>
                      )}
                      <span className="text-green-600 font-medium">Optimized ✓</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CloudinaryUpload;