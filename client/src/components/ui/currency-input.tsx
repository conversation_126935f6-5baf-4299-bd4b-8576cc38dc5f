import React, { useState, useEffect, forwardRef } from 'react';
import { Input } from './input';
import { cn } from '@/lib/utils';

// Currency configuration for future extensibility
export interface CurrencyConfig {
  symbol: string;
  code: string;
  locale: string;
  position: 'prefix' | 'suffix';
  decimalPlaces: number;
  thousandsSeparator: string;
  decimalSeparator: string;
}

// Default currency configurations
export const CURRENCIES: Record<string, CurrencyConfig> = {
  INR: {
    symbol: '₹',
    code: 'INR',
    locale: 'en-IN',
    position: 'prefix',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  USD: {
    symbol: '$',
    code: 'USD',
    locale: 'en-US',
    position: 'prefix',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  EUR: {
    symbol: '€',
    code: 'EUR',
    locale: 'en-DE',
    position: 'suffix',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  }
};

export interface CurrencyInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'> {
  value?: number | string;
  onChange?: (value: number) => void;
  onValueChange?: (value: number, formattedValue: string) => void;
  currency?: keyof typeof CURRENCIES;
  allowDecimals?: boolean;
  allowNegative?: boolean;
  maxValue?: number;
  minValue?: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showCurrencySymbol?: boolean;
  formatWhileTyping?: boolean;
  fallbackPlaceholder?: string;
}

// Utility function to format number with Indian comma system
const formatIndianNumber = (num: number): string => {
  const parts = num.toString().split('.');
  const integerPart = parts[0];
  const decimalPart = parts[1];
  
  // Indian number formatting: 1,23,45,678
  let formatted = '';
  const len = integerPart.length;
  
  for (let i = 0; i < len; i++) {
    if (i > 0 && (len - i) % 2 === 1 && (len - i) > 3) {
      formatted += ',';
    } else if (i > 0 && (len - i) === 3) {
      formatted += ',';
    }
    formatted += integerPart[i];
  }
  
  return decimalPart ? `${formatted}.${decimalPart}` : formatted;
};

// Format currency value
const formatCurrencyValue = (
  value: number,
  config: CurrencyConfig,
  showSymbol: boolean = true
): string => {
  if (isNaN(value) || value === 0) return '';
  
  // Use Indian formatting for INR
  const formattedNumber = config.code === 'INR' 
    ? formatIndianNumber(Number(value.toFixed(config.decimalPlaces)))
    : new Intl.NumberFormat(config.locale, {
        minimumFractionDigits: 0,
        maximumFractionDigits: config.decimalPlaces,
      }).format(value);
  
  if (!showSymbol) return formattedNumber;
  
  return config.position === 'prefix'
    ? `${config.symbol}${formattedNumber}`
    : `${formattedNumber}${config.symbol}`;
};

// Parse currency string to number
const parseCurrencyValue = (value: string, config: CurrencyConfig): number => {
  if (!value) return 0;
  
  // Remove currency symbol and spaces
  let cleanValue = value.replace(config.symbol, '').trim();
  
  // Remove thousand separators
  cleanValue = cleanValue.replace(new RegExp(`\\${config.thousandsSeparator}`, 'g'), '');
  
  // Convert decimal separator if needed
  if (config.decimalSeparator !== '.') {
    cleanValue = cleanValue.replace(config.decimalSeparator, '.');
  }
  
  const parsed = parseFloat(cleanValue);
  return isNaN(parsed) ? 0 : parsed;
};

export const CurrencyInput = forwardRef<HTMLInputElement, CurrencyInputProps>(({
  value = '',
  onChange,
  onValueChange,
  currency = 'INR',
  allowDecimals = true,
  allowNegative = false,
  maxValue = 999999,
  minValue = 0,
  placeholder,
  disabled = false,
  className,
  showCurrencySymbol = true,
  formatWhileTyping = true,
  fallbackPlaceholder = "Leave blank to use base pricing",
  ...props
}, ref) => {
  const config = CURRENCIES[currency];
  const [displayValue, setDisplayValue] = useState<string>('');
  const [isFocused, setIsFocused] = useState(false);

  // Initialize display value
  useEffect(() => {
    const numericValue = typeof value === 'string' ? parseFloat(value) || 0 : Number(value) || 0;
    if (numericValue > 0) {
      const formatted = formatCurrencyValue(numericValue, config, showCurrencySymbol && !isFocused);
      setDisplayValue(formatted);
    } else {
      setDisplayValue('');
    }
  }, [value, config, showCurrencySymbol, isFocused]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Allow empty input
    if (!inputValue) {
      setDisplayValue('');
      onChange?.(0);
      onValueChange?.(0, '');
      return;
    }

    // Parse the numeric value
    const numericValue = parseCurrencyValue(inputValue, config);
    
    // Validate range
    if (numericValue < minValue || numericValue > maxValue) {
      return; // Don't update if outside range
    }
    
    // Validate decimals
    if (!allowDecimals && numericValue % 1 !== 0) {
      return; // Don't update if decimals not allowed
    }
    
    // Validate negative
    if (!allowNegative && numericValue < 0) {
      return; // Don't update if negative not allowed
    }

    // Update display value
    if (formatWhileTyping && !isFocused) {
      const formatted = formatCurrencyValue(numericValue, config, showCurrencySymbol);
      setDisplayValue(formatted);
    } else {
      setDisplayValue(inputValue);
    }

    // Notify parent components
    onChange?.(numericValue);
    onValueChange?.(numericValue, formatCurrencyValue(numericValue, config, showCurrencySymbol));
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    
    // Remove currency symbol when focused for easier editing
    if (showCurrencySymbol && displayValue) {
      const numericValue = parseCurrencyValue(displayValue, config);
      if (numericValue > 0) {
        setDisplayValue(numericValue.toString());
      }
    }
    
    props.onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    
    // Format with currency symbol when blurred
    if (displayValue) {
      const numericValue = parseCurrencyValue(displayValue, config);
      if (numericValue > 0) {
        const formatted = formatCurrencyValue(numericValue, config, showCurrencySymbol);
        setDisplayValue(formatted);
      }
    }
    
    props.onBlur?.(e);
  };

  const placeholderText = placeholder || 
    (showCurrencySymbol ? `${config.symbol}0` : '0') + 
    (fallbackPlaceholder ? ` (${fallbackPlaceholder})` : '');

  return (
    <div className="relative">
      <Input
        {...props}
        ref={ref}
        type="text"
        value={displayValue}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholderText}
        disabled={disabled}
        className={cn(
          "text-right pr-3",
          showCurrencySymbol && config.position === 'prefix' && "pl-8",
          showCurrencySymbol && config.position === 'suffix' && "pr-8",
          className
        )}
        inputMode="decimal"
        autoComplete="off"
      />
      
      {/* Currency symbol overlay */}
      {showCurrencySymbol && !isFocused && (
        <div 
          className={cn(
            "absolute inset-y-0 flex items-center pointer-events-none text-muted-foreground",
            config.position === 'prefix' ? "left-3" : "right-3"
          )}
        >
          {config.symbol}
        </div>
      )}
      
      {/* Validation hint */}
      {minValue > 0 && (
        <div className="text-xs text-muted-foreground mt-1">
          Minimum: {formatCurrencyValue(minValue, config, showCurrencySymbol)}
        </div>
      )}
    </div>
  );
});

CurrencyInput.displayName = 'CurrencyInput';

export default CurrencyInput;