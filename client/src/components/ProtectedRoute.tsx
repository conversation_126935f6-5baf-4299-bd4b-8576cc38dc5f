import React, { useEffect } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/contexts/AuthContext';
import { Guards } from '@/components/AuthGuard';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ShieldX, Lock, AlertTriangle } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
  requireOwnership?: boolean;
  resourceId?: number | string;
  redirectTo?: string;
  showFallback?: boolean;
  fallbackMessage?: string;
}

interface LoadingFallbackProps {
  message?: string;
}

function LoadingFallback({ message = "Checking access..." }: LoadingFallbackProps) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="animate-spin h-12 w-12 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4" />
        <p className="text-gray-600">{message}</p>
        <div className="mt-4 space-y-2">
          <Skeleton className="h-4 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
        </div>
      </div>
    </div>
  );
}

interface UnauthorizedFallbackProps {
  message?: string;
  canRetry?: boolean;
  onRetry?: () => void;
  showLogin?: boolean;
}

function UnauthorizedFallback({ 
  message = "You are not authorized to access this page.", 
  canRetry = false,
  onRetry,
  showLogin = true
}: UnauthorizedFallbackProps) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="mb-6">
          <ShieldX className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">{message}</p>
        </div>
        
        <div className="space-y-3">
          {showLogin && (
            <a 
              href="/login"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Lock className="h-4 w-4 mr-2" />
              Go to Login
            </a>
          )}
          
          {canRetry && onRetry && (
            <button
              onClick={onRetry}
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors ml-3"
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Try Again
            </button>
          )}
          
          <div className="mt-4">
            <a 
              href="/"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Return to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export function ProtectedRoute({
  children,
  requiredRole,
  requireOwnership = false,
  resourceId,
  redirectTo,
  showFallback = true,
  fallbackMessage
}: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const [_, navigate] = useLocation();

  useEffect(() => {
    if (loading) return;

    if (!user && redirectTo) {
      navigate(redirectTo);
      return;
    }

    if (!user) {
      navigate('/login');
      return;
    }

    if (requiredRole) {
      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      const hasRequiredRole = roles.includes(user.role);
      
      if (!hasRequiredRole && redirectTo) {
        navigate(redirectTo);
        return;
      }
      
      if (!hasRequiredRole) {
        navigate('/');
        return;
      }
    }
  }, [user, loading, requiredRole, redirectTo, navigate]);

  // Show loading state
  if (loading) {
    return <LoadingFallback message="Verifying your access..." />;
  }

  // Not authenticated
  if (!user) {
    if (!showFallback) return null;
    return (
      <UnauthorizedFallback 
        message="You need to be logged in to access this page."
        showLogin={true}
      />
    );
  }

  // Role check failed
  if (requiredRole) {
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    const hasRequiredRole = roles.includes(user.role);
    
    if (!hasRequiredRole) {
      if (!showFallback) return null;
      return (
        <UnauthorizedFallback 
          message={fallbackMessage || `This page requires ${roles.join(' or ')} privileges.`}
          showLogin={false}
        />
      );
    }
  }

  // All checks passed
  return <>{children}</>;
}

// Specific route protectors
export function OwnerRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute 
      requiredRole={['owner', 'admin']} 
      fallbackMessage="This page is only accessible to property owners."
      {...props}
    >
      {children}
    </ProtectedRoute>
  );
}

export function AdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute 
      requiredRole="admin" 
      fallbackMessage="This page is only accessible to administrators."
      {...props}
    >
      {children}
    </ProtectedRoute>
  );
}

export function AuthenticatedRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute 
      fallbackMessage="Please log in to access this page."
      {...props}
    >
      {children}
    </ProtectedRoute>
  );
}

// Higher-order component for wrapping components with protection
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  protectionOptions: Omit<ProtectedRouteProps, 'children'> = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...protectionOptions}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Utility component for inline protection with custom fallback
export function AuthorizeAction({
  children,
  action,
  resource,
  resourceData,
  fallback,
  onUnauthorized
}: {
  children: React.ReactNode;
  action: string;
  resource?: string;
  resourceData?: any;
  fallback?: React.ReactNode;
  onUnauthorized?: () => void;
}) {
  const { user } = useAuth();

  const isAuthorized = checkActionPermission(user, action, resource, resourceData);

  if (!isAuthorized) {
    if (onUnauthorized) {
      onUnauthorized();
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <Alert className="border-red-200 bg-red-50">
        <ShieldX className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          You are not authorized to {action} this {resource || 'resource'}.
        </AlertDescription>
      </Alert>
    );
  }

  return <>{children}</>;
}

// Helper function for checking action permissions
function checkActionPermission(
  user: any,
  action: string,
  resource?: string,
  resourceData?: any
): boolean {
  if (!user) return false;

  const { role, id: userId } = user;

  // Admins can do everything
  if (role === 'admin') return true;

  // Owner-specific checks
  if (role === 'owner') {
    switch (resource) {
      case 'property':
        // Owners can manage their own properties
        return !resourceData || resourceData.ownerId === userId;
      case 'booking':
        // Owners can manage bookings for their properties
        return !resourceData || 
               (resourceData.property && resourceData.property.ownerId === userId);
      default:
        // Default owner permissions
        return ['view', 'create', 'edit', 'manage'].includes(action);
    }
  }

  // Regular user permissions
  switch (action) {
    case 'view':
      return true;
    case 'create':
      return resource === 'booking'; // Users can create bookings
    case 'edit':
    case 'delete':
      // Users can edit/delete their own resources
      return resourceData && resourceData.userId === userId;
    default:
      return false;
  }
}

export default ProtectedRoute;