import { useState, useRef, useCallback, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup } from "@/components/ui/select";
import { Upload, ImageIcon, X, FileVideo, AlertCircle, CheckCircle, Trash2, Download, Eye, Move, ArrowUp, ArrowDown, Edit3, Save, MoreVertical, Cloud } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { CloudinaryUpload } from "./CloudinaryUpload";

interface Property {
  id: number;
  title: string;
  location: string;
  images?: string[];
  videos?: string[];
}

interface MediaFile {
  id: string;
  url: string;
  name: string;
  type: 'image' | 'video';
  size: number;
  uploadDate: string;
  order: number;
  title?: string;
  caption?: string;
}

interface ValidationError {
  file: string;
  message: string;
  type: 'size' | 'format' | 'general' | 'optimization';
}

interface MediaManagementSectionProps {
  properties?: Property[];
  onMediaUpdate?: () => void;
}

// File validation constants
const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
const MAX_VIDEO_SIZE = 10 * 1024 * 1024; // 10MB in bytes
const LARGE_VIDEO_WARNING_SIZE = 7 * 1024 * 1024; // 7MB - warn for optimization
const OPTIMAL_VIDEO_SIZE = 5 * 1024 * 1024; // 5MB - optimal size threshold
const ACCEPTED_IMAGE_FORMATS = ['image/jpeg', 'image/png'];
const ACCEPTED_VIDEO_FORMATS = ['video/mp4', 'video/webm'];
const ACCEPTED_FORMATS = [...ACCEPTED_IMAGE_FORMATS, ...ACCEPTED_VIDEO_FORMATS];

// Minimum photo requirement for published listings
const MIN_PHOTOS_REQUIRED = 6;

export const MediaManagementSection = ({ properties = [], onMediaUpdate = () => {} }: MediaManagementSectionProps) => {
  const [selectedPropertyId, setSelectedPropertyId] = useState<number | null>(null);
  const [uploading, setUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [activeMediaTab, setActiveMediaTab] = useState("upload");
  const [uploadMethod, setUploadMethod] = useState<"basic" | "cloudinary">("cloudinary");
  const [isDragging, setIsDragging] = useState(false);
  const [previewFile, setPreviewFile] = useState<string | null>(null);
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [editingMedia, setEditingMedia] = useState<{ url: string; title: string; caption: string } | null>(null);
  const [tempEditData, setTempEditData] = useState<{ title: string; caption: string }>({ title: '', caption: '' });
  const [editPhotosMode, setEditPhotosMode] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{ url: string; type: 'image' | 'video' } | null>(null);
  const [selectedForDeletion, setSelectedForDeletion] = useState<string[]>([]);
  const [youtubeUrl, setYoutubeUrl] = useState<string>('');
  const [youtubeVideos, setYoutubeVideos] = useState<string[]>([]);
  const [deletingMedia, setDeletingMedia] = useState<string[]>([]);
  const [newlyAddedMedia, setNewlyAddedMedia] = useState<string[]>([]);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [selectedMediaItems, setSelectedMediaItems] = useState<string[]>([]);
  const [bulkActionMode, setBulkActionMode] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const selectedProperty = properties?.find(p => p.id === selectedPropertyId);

  // Helper functions for minimum photo requirement
  const getTotalPhotoCount = (property: Property) => {
    return (property.images?.length || 0) + (property.videos?.length || 0);
  };

  const getImageCount = (property: Property) => {
    return property.images?.length || 0;
  };

  const wouldViolateMinimumRequirement = (property: Property, deleteCount: number) => {
    const currentTotal = getTotalPhotoCount(property);
    return currentTotal - deleteCount < MIN_PHOTOS_REQUIRED;
  };

  const getPhotoCountStatus = (property: Property) => {
    const total = getTotalPhotoCount(property);
    const remaining = MIN_PHOTOS_REQUIRED - total;
    return {
      total,
      remaining: remaining > 0 ? remaining : 0,
      meetsRequirement: total >= MIN_PHOTOS_REQUIRED
    };
  };

  // File validation function
  const validateFiles = (files: File[]): ValidationError[] => {
    const errors: ValidationError[] = [];
    
    files.forEach((file) => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      
      // Check file size based on type
      if (isImage && file.size > MAX_IMAGE_SIZE) {
        errors.push({
          file: file.name,
          message: `Image size exceeds 5MB limit (${(file.size / 1024 / 1024).toFixed(2)}MB)`,
          type: 'size'
        });
      } else if (isVideo && file.size > MAX_VIDEO_SIZE) {
        errors.push({
          file: file.name,
          message: `Video size exceeds 10MB limit (${(file.size / 1024 / 1024).toFixed(2)}MB). Please compress before uploading.`,
          type: 'size'
        });
      }
      
      // Video optimization warnings
      if (isVideo && file.size <= MAX_VIDEO_SIZE) {
        if (file.size > LARGE_VIDEO_WARNING_SIZE) {
          errors.push({
            file: file.name,
            message: `Large video file (${(file.size / 1024 / 1024).toFixed(2)}MB). Consider compressing for faster upload & streaming performance.`,
            type: 'optimization'
          });
        } else if (file.size > OPTIMAL_VIDEO_SIZE) {
          errors.push({
            file: file.name,
            message: `💡 Tip: Video is ${(file.size / 1024 / 1024).toFixed(2)}MB. For best performance, keep videos under 2 minutes and 5MB when possible.`,
            type: 'optimization'
          });
        }
      }
      
      // Check file format
      if (!ACCEPTED_FORMATS.includes(file.type)) {
        const expectedFormats = isImage ? 'JPEG, PNG' : 'MP4, WebM';
        errors.push({
          file: file.name,
          message: `Invalid file format. Expected: ${expectedFormats}`,
          type: 'format'
        });
      }
    });
    
    return errors;
  };

  // YouTube URL validation function
  const validateYouTubeUrl = (url: string): { isValid: boolean; videoId?: string; error?: string } => {
    if (!url.trim()) {
      return { isValid: false, error: 'Please enter a YouTube URL' };
    }

    // YouTube URL patterns
    const patterns = [
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/,
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
      /(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})/,
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return { isValid: true, videoId: match[1] };
      }
    }

    return { isValid: false, error: 'Please enter a valid YouTube URL' };
  };

  // Get YouTube embed URL from video ID
  const getYouTubeEmbedUrl = (videoId: string) => {
    return `https://www.youtube.com/embed/${videoId}`;
  };

  // Get YouTube thumbnail URL from video ID
  const getYouTubeThumbnail = (videoId: string) => {
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  };

  // Check if a video URL is a YouTube embed
  const isYouTubeVideo = (url: string) => {
    return url.includes('youtube.com/embed/');
  };

  // Get YouTube video ID from embed URL
  const getYouTubeVideoId = (embedUrl: string) => {
    const match = embedUrl.match(/youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/);
    return match ? match[1] : null;
  };

  // Enhanced file selection with validation
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    handleFilesSelection(files);
  };

  const handleFilesSelection = (files: File[]) => {
    const errors = validateFiles(files);
    setValidationErrors(errors);
    
    if (errors.length === 0) {
      setSelectedFiles(files);
      toast({
        title: "Files Selected",
        description: `${files.length} file(s) ready for upload`,
      });
    } else {
      setSelectedFiles([]);
      toast({
        title: "Validation Errors",
        description: `${errors.length} file(s) failed validation`,
        variant: "destructive",
      });
    }
  };

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFilesSelection(files);
  }, []);

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file type icon
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <ImageIcon className="w-6 h-6 text-blue-500" />;
    } else if (file.type.startsWith('video/')) {
      return <FileVideo className="w-6 h-6 text-purple-500" />;
    }
    return <Upload className="w-6 h-6 text-gray-500" />;
  };

  // Clear files and reset validation
  const clearFiles = () => {
    setSelectedFiles([]);
    setValidationErrors([]);
    setYoutubeVideos([]);
    setYoutubeUrl('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle Cloudinary upload completion
  const handleCloudinaryUploadComplete = async (results: any[]) => {
    if (!selectedPropertyId) return;

    try {
      // Convert Cloudinary results to the format expected by the backend
      const mediaUrls = results.map(result => result.secure_url);
      const imageUrls = results.filter(r => r.resource_type === 'image').map(r => r.secure_url);
      const videoUrls = results.filter(r => r.resource_type === 'video').map(r => r.secure_url);

      // Show immediate visual feedback - add to newly added state
      setNewlyAddedMedia(prev => [...prev, ...mediaUrls]);

      // Update the property with new media URLs
      const response = await fetch('/api/properties/media/add-urls', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          propertyId: selectedPropertyId,
          images: imageUrls,
          videos: videoUrls
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update property media');
      }

      // Enhanced success notifications
      const imageCount = imageUrls.length;
      const videoCount = videoUrls.length;

      // Show instant success message
      if (imageCount > 0 && videoCount > 0) {
        toast({
          title: "🎉 Upload Complete!",
          description: `✅ ${imageCount} photo(s) and ${videoCount} video(s) added to your listing`,
          duration: 4000,
        });
      } else if (imageCount > 0) {
        toast({
          title: "📸 Photos Added!",
          description: `✅ ${imageCount} photo(s) successfully uploaded and optimized`,
          duration: 4000,
        });
      } else if (videoCount > 0) {
        toast({
          title: "🎬 Videos Added!",
          description: `✅ ${videoCount} video(s) uploaded with streaming optimization`,
          duration: 4000,
        });
      }

      // Show additional optimization info for videos
      if (videoCount > 0) {
        setTimeout(() => {
          toast({
            title: "⚡ Auto-Optimization Applied",
            description: "Videos optimized for fast streaming with multiple quality options",
            duration: 3000,
          });
        }, 2000);
      }

      // Update the property data
      onMediaUpdate();
      
      // Clear newly added state after animation
      setTimeout(() => {
        setNewlyAddedMedia(prev => prev.filter(url => !mediaUrls.includes(url)));
      }, 3000);

    } catch (error: any) {
      console.error('Failed to update property media:', error);
      
      // Remove from newly added on error
      setNewlyAddedMedia(prev => prev.filter(url => !results.map(r => r.secure_url).includes(url)));
      
      toast({
        title: "❌ Upload Failed",
        description: error.message || "Failed to save media to property",
        variant: "destructive",
        duration: 6000,
      });
    }
  };

  // Add YouTube video handler
  const handleAddYouTubeVideo = () => {
    const validation = validateYouTubeUrl(youtubeUrl);
    
    if (!validation.isValid) {
      toast({
        title: "Invalid YouTube URL",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    if (!validation.videoId) {
      toast({
        title: "Error",
        description: "Could not extract video ID from URL",
        variant: "destructive",
      });
      return;
    }

    // Check if video is already added
    const embedUrl = getYouTubeEmbedUrl(validation.videoId);
    if (youtubeVideos.includes(embedUrl)) {
      toast({
        title: "Video Already Added",
        description: "This YouTube video is already in your list",
        variant: "destructive",
      });
      return;
    }

    // Add to YouTube videos list
    setYoutubeVideos(prev => [...prev, embedUrl]);
    setYoutubeUrl('');
    
    toast({
      title: "YouTube Video Added",
      description: "Video added successfully. Click 'Upload' to save to your property.",
    });
  };

  // Remove YouTube video from pending list
  const handleRemoveYouTubeVideo = (embedUrl: string) => {
    setYoutubeVideos(prev => prev.filter(url => url !== embedUrl));
  };

  const handleUpload = async () => {
    if (!selectedPropertyId || (selectedFiles.length === 0 && youtubeVideos.length === 0)) {
      toast({
        title: "Error",
        description: "Please select a property and files to upload or add YouTube videos.",
        variant: "destructive",
      });
      return;
    }

    // Final validation before upload
    const errors = validateFiles(selectedFiles);
    const criticalErrors = errors.filter(error => error.type !== 'optimization');
    
    setValidationErrors(errors); // Show all errors including optimization warnings
    
    if (criticalErrors.length > 0) {
      toast({
        title: "Upload Blocked",
        description: `${criticalErrors.length} critical error(s) must be fixed before uploading.`,
        variant: "destructive",
      });
      return;
    }

    // Show optimization warnings but allow upload
    if (errors.length > 0 && criticalErrors.length === 0) {
      toast({
        title: "Optimization Tips Available",
        description: "Check the suggestions below - your files will upload with auto-optimization!",
        duration: 3000,
      });
    }

    setUploading(true);
    
    // Show immediate upload start feedback
    const imageCount = selectedFiles.filter(f => f.type.startsWith('image/')).length;
    const videoCount = selectedFiles.filter(f => f.type.startsWith('video/')).length + youtubeVideos.length;
    
    let uploadStartMessage = "Starting upload...";
    if (videoCount > 0 && imageCount > 0) {
      uploadStartMessage = `📤 Uploading ${imageCount} image(s) and ${videoCount} video(s)...`;
    } else if (videoCount > 0) {
      uploadStartMessage = `🎬 Uploading ${videoCount} video(s) - optimizing for streaming...`;
    } else if (imageCount > 0) {
      uploadStartMessage = `📸 Uploading ${imageCount} image(s)...`;
    }
    
    toast({
      title: "Upload Started",
      description: uploadStartMessage,
      duration: 3000,
    });

    try {
      const formData = new FormData();
      selectedFiles.forEach(file => {
        if (file.type.startsWith('image/')) {
          formData.append('images', file);
        } else if (file.type.startsWith('video/')) {
          formData.append('videos', file);
        }
      });
      
      // Add YouTube videos as JSON
      if (youtubeVideos.length > 0) {
        formData.append('youtubeVideos', JSON.stringify(youtubeVideos));
      }
      
      formData.append('propertyId', String(selectedPropertyId));

      // Show processing feedback for large files
      if (selectedFiles.some(file => file.size > LARGE_VIDEO_WARNING_SIZE)) {
        setTimeout(() => {
          toast({
            title: "🔄 Processing Large Files",
            description: "Compressing and optimizing your media for best quality...",
            duration: 4000,
          });
        }, 1500);
      }

      const response = await fetch('/api/properties/media/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload media');
      }

      const result = await response.json();
      const uploadedCount = result.uploadedCount || selectedFiles.length;
      
      // Enhanced success message with details
      const videoCount = selectedFiles.filter(f => f.type.startsWith('video/')).length + youtubeVideos.length;
      const imageCount = selectedFiles.filter(f => f.type.startsWith('image/')).length;
      
      // Enhanced success notifications for basic upload
      if (imageCount > 0 && videoCount > 0) {
        toast({
          title: "🎉 Upload Complete!",
          description: `✅ ${imageCount} photo(s) and ${videoCount} video(s) added to your listing`,
          duration: 5000,
        });
      } else if (imageCount > 0) {
        toast({
          title: "📸 Photos Uploaded!",
          description: `✅ ${imageCount} photo(s) successfully added to your property`,
          duration: 4000,
        });
      } else if (videoCount > 0) {
        toast({
          title: "🎬 Videos Uploaded!",
          description: `✅ ${videoCount} video(s) uploaded and ready for streaming`,
          duration: 4000,
        });
      }

      // Show additional success details
      setTimeout(() => {
        toast({
          title: "📊 Media Library Updated",
          description: `Your property now has enhanced visual content to attract more bookings!`,
          duration: 4000,
        });
      }, 2000);
      
      onMediaUpdate();
      clearFiles();
      
      // Show additional info for video uploads
      if (videoCount > 0) {
        setTimeout(() => {
          toast({
            title: "🎬 Video Upload Complete!",
            description: "Videos optimized for streaming. Check the 'Manage' tab for preview options.",
            duration: 5000,
          });
        }, 2000);
        
        // Show optimization success message
        setTimeout(() => {
          toast({
            title: "⚡ Auto-Optimization Applied",
            description: "Your videos have been compressed and optimized for fast streaming and better user experience.",
            duration: 4000,
          });
        }, 4000);
        
        // Auto-switch to manage tab after video upload
        setTimeout(() => {
          setActiveMediaTab('manage');
        }, 6000);
      }
    } catch (error: any) {
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Enhanced media deletion with confirmation
  const handleDeleteMedia = async (mediaUrl: string, mediaType: 'image' | 'video' = 'image') => {
    if (!selectedPropertyId || !selectedProperty) {
      toast({
        title: "Error",
        description: "Please select a property.",
        variant: "destructive",
      });
      return;
    }

    // Check if deletion would violate minimum photo requirement
    if (wouldViolateMinimumRequirement(selectedProperty, 1)) {
      toast({
        title: "Cannot Delete Photo",
        description: `You must have at least ${MIN_PHOTOS_REQUIRED} photos in your listing. Upload more photos before deleting this one.`,
        variant: "destructive",
      });
      return;
    }

    // Show custom confirmation dialog instead of window.confirm
    setDeleteConfirmation({ url: mediaUrl, type: mediaType });
  };

  // Confirm deletion with enhanced dialog
  const confirmDelete = async () => {
    if (!deleteConfirmation || !selectedPropertyId) return;

    const mediaToDelete = deleteConfirmation;
    
    // Add to deleting state for immediate visual feedback (fade out effect)
    setDeletingMedia(prev => [...prev, mediaToDelete.url]);
    
    // Close the confirmation dialog immediately
    setDeleteConfirmation(null);

    // Show immediate deletion start feedback
    const mediaTypeName = mediaToDelete.type.charAt(0).toUpperCase() + mediaToDelete.type.slice(1);
    toast({
      title: `🗑️ Removing ${mediaTypeName}`,
      description: `${mediaTypeName} is being removed from your listing...`,
      duration: 2000,
    });

    try {
      // Show cloud cleanup feedback for Cloudinary images
      if (mediaToDelete.url.includes('cloudinary.com')) {
        setTimeout(() => {
          toast({
            title: "☁️ Cloud Storage Cleanup",
            description: "Removing from cloud storage for complete deletion...",
            duration: 2500,
          });
        }, 800);
      }

      const response = await fetch('/api/properties/media/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({ 
          propertyId: selectedPropertyId, 
          mediaUrl: mediaToDelete.url,
          mediaType: mediaToDelete.type 
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete media');
      }

      // Enhanced success feedback with emojis
      const successIcon = mediaToDelete.type === 'video' ? '🎬' : '📸';
      toast({
        title: `${successIcon} ${mediaTypeName} Removed!`,
        description: `✅ Successfully removed from your property listing`,
        duration: 4000,
      });

      // Update the property data
      onMediaUpdate();
      
      // Remove from deleting state after successful deletion
      setTimeout(() => {
        setDeletingMedia(prev => prev.filter(url => url !== mediaToDelete.url));
      }, 1000);

      // Show updated count feedback
      setTimeout(() => {
        const remainingCount = Math.max(0, (selectedProperty?.images?.length || 0) + (selectedProperty?.videos?.length || 0) - 1);
        if (remainingCount === 0) {
          toast({
            title: "📂 Media Library Empty",
            description: "No media files remaining. Upload new photos or videos to enhance your listing!",
            duration: 4000,
          });
        } else {
          toast({
            title: "📊 Media Updated",
            description: `${remainingCount} media file(s) remaining in your property`,
            duration: 3000,
          });
        }
      }, 2500);
      
    } catch (error: any) {
      // Remove from deleting state on error
      setDeletingMedia(prev => prev.filter(url => url !== mediaToDelete.url));
      
      toast({
        title: "❌ Deletion Failed",
        description: error.message || `Failed to remove ${mediaToDelete.type}. Please try again.`,
        variant: "destructive",
        duration: 6000,
      });
      
      // Offer retry suggestion
      setTimeout(() => {
        toast({
          title: "💡 Tip",
          description: "Try refreshing the page if the issue persists, or contact support.",
          duration: 4000,
        });
      }, 3000);
    }
  };

  // Cancel deletion
  const cancelDelete = () => {
    setDeleteConfirmation(null);
  };

  // Drag and drop handlers for reordering
  const handleDragStart = (e: React.DragEvent, mediaUrl: string) => {
    setDraggedItem(mediaUrl);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOverReorder = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeaveReorder = () => {
    setDragOverIndex(null);
  };

  const handleDropReorder = async (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (!draggedItem || !selectedPropertyId || !selectedProperty) return;

    const allMedia = [...(selectedProperty.images || []), ...(selectedProperty.videos || [])];
    const dragIndex = allMedia.indexOf(draggedItem);
    
    if (dragIndex === dropIndex) {
      setDraggedItem(null);
      setDragOverIndex(null);
      return;
    }

    try {
      // Create reordered array
      const newMediaArray = [...allMedia];
      const [draggedElement] = newMediaArray.splice(dragIndex, 1);
      newMediaArray.splice(dropIndex, 0, draggedElement);

      // Update on server
      const response = await fetch('/api/properties/media/reorder-bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          propertyId: selectedPropertyId,
          newOrder: newMediaArray
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reorder media');
      }

      toast({
        title: "📋 Gallery Updated",
        description: "Media has been reordered successfully",
        duration: 3000,
      });
      
      onMediaUpdate();
    } catch (error: any) {
      toast({
        title: "❌ Reorder Failed",
        description: error.message || "Failed to reorder media",
        variant: "destructive",
      });
    } finally {
      setDraggedItem(null);
      setDragOverIndex(null);
    }
  };

  // Bulk selection handlers
  const toggleBulkActionMode = () => {
    setBulkActionMode(!bulkActionMode);
    setSelectedMediaItems([]);
  };

  const toggleMediaSelection = (mediaUrl: string) => {
    setSelectedMediaItems(prev => 
      prev.includes(mediaUrl) 
        ? prev.filter(url => url !== mediaUrl)
        : [...prev, mediaUrl]
    );
  };

  const selectAllMedia = () => {
    if (!selectedProperty) return;
    const allMedia = [...(selectedProperty.images || []), ...(selectedProperty.videos || [])];
    setSelectedMediaItems(allMedia);
  };

  const clearAllSelections = () => {
    setSelectedMediaItems([]);
  };

  const bulkDeleteSelected = () => {
    if (selectedMediaItems.length === 0) return;

    // Check minimum photo requirement
    if (selectedProperty && wouldViolateMinimumRequirement(selectedProperty, selectedMediaItems.length)) {
      toast({
        title: "Cannot Delete Media",
        description: `You must have at least ${MIN_PHOTOS_REQUIRED} photos in your listing. Reduce your selection or upload more photos first.`,
        variant: "destructive",
      });
      return;
    }

    const mediaCount = selectedMediaItems.length;
    const confirmationMessage = `Are you sure you want to delete ${mediaCount} selected ${mediaCount === 1 ? 'item' : 'items'}? This action cannot be undone.`;
    
    if (window.confirm(confirmationMessage)) {
      // Process bulk deletions
      selectedMediaItems.forEach(mediaUrl => {
        const mediaType = selectedProperty?.images?.includes(mediaUrl) ? 'image' : 'video';
        handleDeleteMedia(mediaUrl, mediaType);
      });
      
      setBulkActionMode(false);
      setSelectedMediaItems([]);
    }
  };

  // Keyboard navigation for preview modal
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!previewFile || !selectedProperty) return;
      
      const allMedia = [...(selectedProperty.images || []), ...(selectedProperty.videos || [])];
      const currentIndex = allMedia.indexOf(previewFile);
      
      switch (e.key) {
        case 'ArrowLeft':
          if (currentIndex > 0) {
            setPreviewFile(allMedia[currentIndex - 1]);
          }
          break;
        case 'ArrowRight':
          if (currentIndex < allMedia.length - 1) {
            setPreviewFile(allMedia[currentIndex + 1]);
          }
          break;
        case 'Escape':
          setPreviewFile(null);
          break;
        case 'Delete':
        case 'Backspace':
          const mediaType = selectedProperty.images?.includes(previewFile) ? 'image' : 'video';
          handleDeleteMedia(previewFile, mediaType);
          setPreviewFile(null);
          break;
      }
    };

    if (previewFile) {
      window.addEventListener('keydown', handleKeyPress);
      return () => window.removeEventListener('keydown', handleKeyPress);
    }
    return undefined;
  }, [previewFile, selectedProperty]);

  // Toggle edit photos mode
  const toggleEditPhotosMode = () => {
    setEditPhotosMode(!editPhotosMode);
    setSelectedForDeletion([]);
  };

  // Toggle selection for bulk delete
  const toggleSelection = (mediaUrl: string) => {
    setSelectedForDeletion(prev => 
      prev.includes(mediaUrl) 
        ? prev.filter(url => url !== mediaUrl)
        : [...prev, mediaUrl]
    );
  };

  // Media reordering functions
  const handleReorderMedia = async (mediaUrl: string, direction: 'up' | 'down') => {
    if (!selectedPropertyId || !selectedProperty) return;

    const currentMedia = [...(selectedProperty.images || []), ...(selectedProperty.videos || [])];
    const currentIndex = currentMedia.indexOf(mediaUrl);
    
    if (currentIndex === -1) return;
    
    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= currentMedia.length) return;

    try {
      const response = await fetch('/api/properties/media/reorder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          propertyId: selectedPropertyId,
          mediaUrl: mediaUrl,
          newOrder: newIndex
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reorder media');
      }

      toast({
        title: "Success",
        description: "Media order updated successfully!",
      });
      onMediaUpdate();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Download media file
  const handleDownloadMedia = (mediaUrl: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = mediaUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Edit media title/caption functionality
  const handleEditMedia = (mediaUrl: string, currentTitle?: string, currentCaption?: string) => {
    setEditingMedia({ url: mediaUrl, title: currentTitle || '', caption: currentCaption || '' });
    setTempEditData({ title: currentTitle || '', caption: currentCaption || '' });
  };

  const handleSaveMediaEdit = async () => {
    if (!editingMedia || !selectedPropertyId) return;

    try {
      const response = await fetch('/api/properties/media/edit', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          propertyId: selectedPropertyId,
          mediaUrl: editingMedia.url,
          title: tempEditData.title,
          caption: tempEditData.caption
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update media details');
      }

      toast({
        title: "Success",
        description: "Media details updated successfully!",
      });
      
      setEditingMedia(null);
      setTempEditData({ title: '', caption: '' });
      onMediaUpdate();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update media details",
        variant: "destructive",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingMedia(null);
    setTempEditData({ title: '', caption: '' });
  };


  // Show loading/empty state if no properties
  if (!properties || properties.length === 0) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <div className="mx-auto w-20 h-20 mb-6 rounded-full bg-[#F7F4F1] flex items-center justify-center">
            <ImageIcon className="w-10 h-10 text-[#766C63]" />
          </div>
          <h3 className="font-medium text-[#2D3C2D] mb-2">No Properties Found</h3>
          <p className="text-[#766C63] mb-6">You need to create properties first before managing media files</p>
          <Button
            onClick={() => window.location.href = '/owner/property/create'}
            className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
          >
            Create Your First Property
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="font-heading text-xl font-bold text-[#2D3C2D] mb-2">Media Management</h2>
            <p className="text-[#605045]">Upload, organize, and manage photos & videos for your properties</p>
          </div>
          <div className="mt-4 md:mt-0 bg-[#F7F4F1] rounded-lg p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-[#4A6741]">
                {properties?.reduce((total, prop) => total + (prop.images?.length || 0) + (prop.videos?.length || 0), 0) || 0}
              </p>
              <p className="text-sm text-[#766C63]">Total Media Files</p>
              <div className="flex justify-center space-x-4 mt-2 text-xs text-[#766C63]">
                <span>Images: {properties?.reduce((total, prop) => total + (prop.images?.length || 0), 0) || 0}</span>
                <span>Videos: {properties?.reduce((total, prop) => total + (prop.videos?.length || 0), 0) || 0}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Property Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-[#2D3C2D] mb-2">Select Property</label>
          <Select onValueChange={(value) => setSelectedPropertyId(Number(value))}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose a property to manage its media..." />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {properties?.map((property) => (
                  <SelectItem key={property.id} value={String(property.id)}>
                    <div className="flex items-center space-x-2">
                      <span>{property.title}</span>
                      <span className="text-xs text-[#766C63]">
                        ({(property.images?.length || 0) + (property.videos?.length || 0)} files: {property.images?.length || 0} images, {property.videos?.length || 0} videos)
                      </span>
                      <span className="text-xs text-[#766C63]">- {property.location}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {selectedProperty && (
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-medium text-[#2D3C2D]">{selectedProperty.title}</h3>
                <p className="text-sm text-[#766C63]">{selectedProperty.location}</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-[#4A6741]">{(selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)}</p>
                <p className="text-xs text-[#766C63]">media files</p>
                <div className="flex justify-end space-x-2 mt-1 text-xs text-[#766C63]">
                  <span>{selectedProperty.images?.length || 0} images</span>
                  <span>{selectedProperty.videos?.length || 0} videos</span>
                </div>
              </div>
            </div>

            {/* Media Management Tabs */}
            <div className="flex border-b border-[#EBE6E1] mb-4">
              <button
                onClick={() => setActiveMediaTab('upload')}
                className={`px-4 py-2 mr-4 font-medium text-sm transition-all duration-200 ${
                  activeMediaTab === 'upload'
                    ? 'text-[#4A6741] border-b-2 border-[#4A6741]'
                    : 'text-[#605045] hover:text-[#2D3C2D]'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Upload className="w-4 h-4" />
                  <span>Upload Photos & Videos</span>
                </div>
              </button>
              <button
                onClick={() => setActiveMediaTab('manage')}
                className={`px-4 py-2 font-medium text-sm transition-all duration-200 ${
                  activeMediaTab === 'manage'
                    ? 'text-[#4A6741] border-b-2 border-[#4A6741]'
                    : 'text-[#605045] hover:text-[#2D3C2D]'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <ImageIcon className="w-4 h-4" />
                  <span>View & Delete Media</span>
                </div>
              </button>
            </div>

            {/* Upload Status Bar */}
            {uploading && (
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 mt-6 mb-4">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                    <div className="absolute inset-0 rounded-full border-2 border-green-600 opacity-30"></div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-green-900 mb-1">🚀 Upload in Progress</h3>
                    <div className="flex items-center space-x-4 text-sm text-green-800">
                      <span>Processing {selectedFiles.length + youtubeVideos.length} file(s)</span>
                      <span>•</span>
                      <span>Optimizing for web delivery</span>
                      <span>•</span>
                      <span>Please wait...</span>
                    </div>
                    <div className="mt-2 w-full bg-green-100 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full animate-pulse" style={{width: '70%'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Always Visible Media Guidelines */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-0 mt-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-semibold text-blue-900 mb-2">📋 Media Upload Guidelines</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-blue-800">
                        <strong>Photos:</strong> JPG or PNG format, up to 5 MB each
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-blue-800">
                        <strong>Videos:</strong> MP4 format, up to 10 MB each
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-blue-800">
                        <strong>Best Practice:</strong> Keep videos under 2 minutes
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-blue-800">
                        <strong>Minimum:</strong> At least 6 photos required per property
                      </span>
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-blue-700 bg-blue-100 rounded px-3 py-2">
                    💡 <strong>Pro Tip:</strong> High-quality images and short videos (30-90 seconds) get more bookings! We'll automatically optimize your uploads for fast loading.
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {selectedProperty && (
        <div className="bg-white rounded-lg shadow-md p-6">
          {activeMediaTab === "upload" && (
            <div className="space-y-6">
              {/* Upload Method Toggle */}
              <div className="flex items-center justify-center space-x-4 bg-gray-50 rounded-lg p-4">
                <Button
                  variant={uploadMethod === "cloudinary" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUploadMethod("cloudinary")}
                  className={uploadMethod === "cloudinary" ? "bg-[#4A6741] text-white" : ""}
                >
                  <Cloud className="w-4 h-4 mr-2" />
                  Advanced Upload (Recommended)
                </Button>
                <Button
                  variant={uploadMethod === "basic" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUploadMethod("basic")}
                  className={uploadMethod === "basic" ? "bg-[#4A6741] text-white" : ""}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Basic Upload
                </Button>
              </div>

              {/* Upload Areas */}
              {uploadMethod === "cloudinary" && selectedPropertyId && (
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <Cloud className="w-4 h-4 text-green-600" />
                      </div>
                      <h3 className="font-semibold text-green-900">Advanced Cloudinary Upload</h3>
                    </div>
                    <div className="text-sm text-green-800 space-y-1">
                      <p>✨ Built-in file validation and optimization</p>
                      <p>📊 Real-time upload progress with thumbnails</p>
                      <p>🎬 Automatic video streaming optimization</p>
                      <p>⚡ Multiple quality profiles generated</p>
                    </div>
                  </div>
                  
                  <CloudinaryUpload
                    onUploadComplete={handleCloudinaryUploadComplete}
                    propertyId={selectedPropertyId}
                    maxFiles={10}
                    folder={`farmhouse-properties/${selectedPropertyId}`}
                  />
                </div>
              )}

              {uploadMethod === "basic" && (
                <div>
                  {/* Basic Upload Area */}
                  <div 
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-all ${
                      isDragging 
                        ? 'border-[#4A6741] bg-[#F7F4F1]' 
                        : 'border-[#EBE6E1] hover:border-[#4A6741]'
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <div className="mx-auto w-16 h-16 mb-4 rounded-full bg-[#F7F4F1] flex items-center justify-center">
                      <Upload className="w-8 h-8 text-[#4A6741]" />
                    </div>
                    <h3 className="font-medium text-[#2D3C2D] mb-2">Upload Photos & Videos</h3>
                    <p className="text-sm text-[#766C63] mb-4">
                      Drag and drop your files here, or click to browse
                    </p>
                    <Input 
                      ref={fileInputRef}
                      type="file" 
                      multiple 
                      onChange={handleFileSelect} 
                      accept="image/jpeg,image/png,video/mp4,video/webm"
                      className="max-w-md mx-auto" 
                    />
                    <div className="mt-4 text-xs text-[#766C63]">
                      <p className="font-medium mb-2">File Requirements:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Images: JPEG, PNG (Max 5MB each)</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Videos: MP4, WebM (Max 10MB each)</span>
                        </div>
                      </div>
                      
                      {/* Video Optimization Guidance */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <FileVideo className="w-4 h-4 text-blue-600" />
                          <p className="font-medium text-blue-800 text-sm">📹 Video Optimization Tips</p>
                        </div>
                        <div className="space-y-1 text-blue-700">
                          <div className="flex items-start space-x-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span><strong>Keep videos short:</strong> Under 2 minutes recommended for best quality & performance</span>
                          </div>
                          <div className="flex items-start space-x-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span><strong>Compress large files:</strong> We'll auto-optimize during upload for faster streaming</span>
                          </div>
                          <div className="flex items-start space-x-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span><strong>Best formats:</strong> MP4 (H.264) provides excellent quality-to-size ratio</span>
                          </div>
                          <div className="flex items-start space-x-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span><strong>Resolution:</strong> 1080p or 720p work great for property showcases</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* YouTube Link Section - Only for basic upload */}
              {uploadMethod === "basic" && (
                <div className="border-2 border-dashed border-[#EBE6E1] rounded-lg p-6 text-center">
                <div className="mx-auto w-16 h-16 mb-4 rounded-full bg-[#F7F4F1] flex items-center justify-center">
                  <FileVideo className="w-8 h-8 text-[#4A6741]" />
                </div>
                <h3 className="font-medium text-[#2D3C2D] mb-2">Add YouTube Video</h3>
                <p className="text-sm text-[#766C63] mb-4">
                  Paste a YouTube video link to add promotional content
                </p>
                <div className="max-w-md mx-auto space-y-3">
                  <Input
                    type="url"
                    placeholder="https://www.youtube.com/watch?v=..."
                    value={youtubeUrl}
                    onChange={(e) => setYoutubeUrl(e.target.value)}
                    className="w-full"
                  />
                  <Button
                    onClick={handleAddYouTubeVideo}
                    disabled={!youtubeUrl.trim()}
                    variant="outline"
                    className="w-full border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white"
                  >
                    <FileVideo className="w-4 h-4 mr-2" />
                    Add YouTube Video
                  </Button>
                </div>
                <div className="mt-4 text-xs text-[#766C63] space-y-3">
                  <div>
                    <p className="font-medium mb-1">Supported YouTube URL formats:</p>
                    <div className="space-y-1">
                      <p>• https://www.youtube.com/watch?v=VIDEO_ID</p>
                      <p>• https://youtu.be/VIDEO_ID</p>
                      <p>• https://www.youtube.com/embed/VIDEO_ID</p>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded p-2">
                    <div className="flex items-center space-x-1 mb-1">
                      <FileVideo className="w-3 h-3 text-blue-600" />
                      <p className="font-medium text-blue-800">💡 YouTube Video Tips:</p>
                    </div>
                    <div className="space-y-1 text-blue-700">
                      <p>• Use short, engaging property tours (under 2 minutes ideal)</p>
                      <p>• High-quality videos perform better for property listings</p>
                      <p>• Consider adding captions for better accessibility</p>
                    </div>
                  </div>
                </div>

                {/* YouTube Videos Preview */}
                {youtubeVideos.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <FileVideo className="w-5 h-5 text-blue-500" />
                      <h4 className="font-medium text-blue-700">YouTube Videos Ready for Upload ({youtubeVideos.length})</h4>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                      {youtubeVideos.map((embedUrl, index) => {
                        const videoId = embedUrl.split('/').pop();
                        const thumbnailUrl = getYouTubeThumbnail(videoId || '');
                        return (
                          <div key={index} className="bg-white rounded-lg p-3 flex items-center space-x-3">
                            <div className="w-16 h-12 rounded bg-gray-100 flex items-center justify-center flex-shrink-0">
                              <img 
                                src={thumbnailUrl} 
                                alt="YouTube thumbnail" 
                                className="w-full h-full object-cover rounded"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi0vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjQ4IiByeD0iNCIgZmlsbD0iI0Y3RjRGMSIvPjxwYXRoIGQ9Im0yNiAyMCA2IDQtNiA0VjIwWiIgZmlsbD0iIzc2NkM2MyIvPjwvc3ZnPg==';
                                }}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-[#2D3C2D] truncate">YouTube Video {index + 1}</p>
                              <p className="text-xs text-[#766C63] truncate">{embedUrl}</p>
                              <p className="text-xs text-[#766C63]">YouTube Embed</p>
                            </div>
                            <button
                              onClick={() => handleRemoveYouTubeVideo(embedUrl)}
                              className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
              )}

              {/* Validation Errors & Optimization Warnings - Only for basic upload */}
              {uploadMethod === "basic" && validationErrors.length > 0 && (
                <div className="space-y-4">
                  {/* Critical Errors (size, format) */}
                  {validationErrors.filter(error => error.type !== 'optimization').length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <AlertCircle className="w-5 h-5 text-red-500" />
                        <h4 className="font-medium text-red-700">Validation Errors</h4>
                        <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                          Must Fix to Upload
                        </span>
                      </div>
                      <div className="space-y-2">
                        {validationErrors.filter(error => error.type !== 'optimization').map((error, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <X className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                            <div className="text-sm text-red-600">
                              <span className="font-medium">{error.file}:</span> {error.message}
                            </div>
                          </div>
                        ))}
                      </div>
                      <Button 
                        onClick={clearFiles}
                        variant="outline"
                        size="sm"
                        className="mt-3 text-red-600 border-red-300 hover:bg-red-50"
                      >
                        Clear All Files
                      </Button>
                    </div>
                  )}

                  {/* Optimization Warnings */}
                  {validationErrors.filter(error => error.type === 'optimization').length > 0 && (
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <FileVideo className="w-5 h-5 text-amber-600" />
                        <h4 className="font-medium text-amber-700">📊 Video Optimization Suggestions</h4>
                        <span className="text-xs bg-amber-100 text-amber-600 px-2 py-1 rounded-full">
                          Optional - Will Upload
                        </span>
                      </div>
                      <div className="space-y-2 mb-3">
                        {validationErrors.filter(error => error.type === 'optimization').map((error, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <AlertCircle className="w-4 h-4 text-amber-500 mt-0.5 flex-shrink-0" />
                            <div className="text-sm text-amber-700">
                              <span className="font-medium">{error.file}:</span> {error.message}
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="bg-amber-100 rounded-lg p-3">
                        <div className="flex items-start space-x-2">
                          <FileVideo className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                          <div className="text-xs text-amber-700">
                            <p className="font-medium mb-1">🚀 Auto-Optimization Enabled:</p>
                            <p>Don't worry! We'll automatically compress and optimize your videos during upload for the best streaming experience.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {uploadMethod === "basic" && ((selectedFiles.length > 0 && validationErrors.filter(error => error.type !== 'optimization').length === 0) || youtubeVideos.length > 0) && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <h4 className="font-medium text-green-700">
                      Media Ready for Upload ({selectedFiles.length + youtubeVideos.length} items)
                    </h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
                    {/* Regular files */}
                    {selectedFiles.map((file, index) => (
                      <div key={`file-${index}`} className="bg-white rounded-lg p-3 flex items-center space-x-3">
                        <div className="w-12 h-12 rounded bg-[#EBE6E1] flex items-center justify-center flex-shrink-0">
                          {getFileIcon(file)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-[#2D3C2D] truncate">{file.name}</p>
                          <p className="text-xs text-[#766C63]">{formatFileSize(file.size)}</p>
                          <p className="text-xs text-[#766C63] capitalize">
                            {file.type.startsWith('image/') ? 'Image' : 'Video'} • {file.type.split('/')[1].toUpperCase()}
                          </p>
                        </div>
                        <button
                          onClick={() => {
                            const newFiles = selectedFiles.filter((_, i) => i !== index);
                            setSelectedFiles(newFiles);
                          }}
                          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                    
                    {/* YouTube videos */}
                    {youtubeVideos.map((embedUrl, index) => {
                      const videoId = embedUrl.split('/').pop();
                      const thumbnailUrl = getYouTubeThumbnail(videoId || '');
                      return (
                        <div key={`youtube-${index}`} className="bg-white rounded-lg p-3 flex items-center space-x-3">
                          <div className="w-12 h-12 rounded bg-red-100 flex items-center justify-center flex-shrink-0">
                            <FileVideo className="w-6 h-6 text-red-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-[#2D3C2D] truncate">YouTube Video {index + 1}</p>
                            <p className="text-xs text-[#766C63]">YouTube Embed</p>
                            <p className="text-xs text-[#766C63] truncate">{embedUrl}</p>
                          </div>
                          <button
                            onClick={() => handleRemoveYouTubeVideo(embedUrl)}
                            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      );
                    })}
                  </div>
                  <Button
                    onClick={handleUpload}
                    disabled={uploading}
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white"
                  >
                    {uploading ? (
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <div className="absolute inset-0 rounded-full border-2 border-white opacity-30"></div>
                        </div>
                        <div className="flex flex-col items-start">
                          <span className="font-medium">Processing Upload...</span>
                          <span className="text-xs opacity-90">
                            {selectedFiles.filter(f => f.type.startsWith('image/')).length > 0 && '📸 Images '}
                            {selectedFiles.filter(f => f.type.startsWith('video/')).length > 0 && '🎬 Videos '}
                            {youtubeVideos.length > 0 && '📺 YouTube '}
                            • Optimizing for web
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <div className="relative">
                          <Upload className="w-5 h-5" />
                          <div className="absolute -top-1 -right-1 bg-white text-[#4A6741] text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                            {selectedFiles.length + youtubeVideos.length}
                          </div>
                        </div>
                        <div className="flex flex-col items-start">
                          <span className="font-medium">
                            Upload Media to Property
                          </span>
                          <span className="text-xs opacity-90">
                            {selectedFiles.filter(f => f.type.startsWith('image/')).length > 0 && 
                              `${selectedFiles.filter(f => f.type.startsWith('image/')).length} image(s) `
                            }
                            {selectedFiles.filter(f => f.type.startsWith('video/')).length > 0 && 
                              `${selectedFiles.filter(f => f.type.startsWith('video/')).length} video(s) `
                            }
                            {youtubeVideos.length > 0 && `${youtubeVideos.length} YouTube video(s)`}
                          </span>
                        </div>
                      </div>
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}

          {activeMediaTab === "manage" && (
            <div className="space-y-6">
              {/* Enhanced Gallery Header */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-[#2D3C2D] mb-1 flex items-center space-x-2">
                      <span>Media Gallery</span>
                      {bulkActionMode && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                          {selectedMediaItems.length} selected
                        </span>
                      )}
                    </h3>
                    <p className="text-sm text-[#766C63]">
                      {(selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)} photos and videos • 
                      Drag to reorder • Click to preview
                    </p>
                  </div>
                  {((selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)) > 0 && (
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-4 text-sm text-[#766C63]">
                        <div className="flex items-center space-x-2">
                          <ImageIcon className="w-4 h-4" />
                          <span>{selectedProperty.images?.length || 0} Images</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <FileVideo className="w-4 h-4" />
                          <span>{selectedProperty.videos?.length || 0} Videos</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant={bulkActionMode ? "default" : "outline"}
                          size="sm"
                          onClick={toggleBulkActionMode}
                          className={`font-medium ${bulkActionMode ? 'bg-blue-600 text-white' : 'border-[#2D3C2D] text-[#2D3C2D] hover:bg-gray-50'}`}
                        >
                          {bulkActionMode ? (
                            <>
                              <CheckCircle className="w-4 h-4 mr-2" />
                              Cancel
                            </>
                          ) : (
                            <>
                              <Edit3 className="w-4 h-4 mr-2" />
                              Manage
                            </>
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onMediaUpdate()}
                          className="border-gray-300 text-gray-600 hover:bg-gray-50"
                        >
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Refresh
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Bulk Action Toolbar */}
                {bulkActionMode && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-blue-900">
                            {selectedMediaItems.length === 0 ? 'Select media to manage' : `${selectedMediaItems.length} items selected`}
                          </span>
                        </div>
                        {((selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)) > 0 && (
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={selectAllMedia}
                              className="text-blue-700 border-blue-300 hover:bg-blue-100"
                            >
                              Select All
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={clearAllSelections}
                              className="text-blue-700 border-blue-300 hover:bg-blue-100"
                              disabled={selectedMediaItems.length === 0}
                            >
                              Clear
                            </Button>
                          </div>
                        )}
                      </div>
                      {selectedMediaItems.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={bulkDeleteSelected}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete {selectedMediaItems.length} {selectedMediaItems.length === 1 ? 'Item' : 'Items'}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* VRBO-Style Photo Grid */}
              {((selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)) > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {/* Images */}
                  {selectedProperty.images?.map((image, index) => {
                    const allMediaIndex = index;
                    const isNewlyAdded = newlyAddedMedia.includes(image);
                    const isDeleting = deletingMedia.includes(image);
                    const isSelected = selectedMediaItems.includes(image);
                    const isDragOver = dragOverIndex === allMediaIndex;
                    
                    return (
                      <div 
                        key={`image-${index}`} 
                        draggable={!bulkActionMode && !isDeleting}
                        onDragStart={(e) => handleDragStart(e, image)}
                        onDragOver={(e) => handleDragOverReorder(e, allMediaIndex)}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDropReorder(e, allMediaIndex)}
                        className={`group relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-500 cursor-pointer ${
                          isNewlyAdded ? 'animate-pulse border-2 border-green-400 shadow-green-200' : ''
                        } ${
                          isDeleting ? 'opacity-30 scale-95 grayscale' : ''
                        } ${
                          isSelected ? 'ring-2 ring-blue-500 border-blue-500' : ''
                        } ${
                          isDragOver ? 'scale-105 shadow-lg border-2 border-blue-400' : ''
                        } ${
                          draggedItem === image ? 'opacity-50 scale-95' : ''
                        }`}
                        onClick={() => bulkActionMode ? toggleMediaSelection(image) : setPreviewFile(image)}
                      >
                        {/* Drag Handle */}
                        {!bulkActionMode && !isDeleting && (
                          <div className="absolute top-2 left-2 bg-gray-800 bg-opacity-70 text-white p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity z-10">
                            <Move className="w-3 h-3" />
                          </div>
                        )}

                        {/* Selection Checkbox */}
                        {bulkActionMode && (
                          <div className="absolute top-2 left-2 z-10">
                            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                              isSelected 
                                ? 'bg-blue-500 border-blue-500' 
                                : 'bg-white bg-opacity-80 border-gray-300 hover:border-blue-400'
                            }`}>
                              {isSelected && (
                                <CheckCircle className="w-4 h-4 text-white" />
                              )}
                            </div>
                          </div>
                        )}
                        <div className="aspect-w-16 aspect-h-12">
                          <img 
                            src={image} 
                            alt={`${selectedProperty.title} - Image ${index + 1}`} 
                            className={`w-full h-48 object-cover transition-all duration-300 ${
                              isNewlyAdded ? 'ring-2 ring-green-400' : ''
                            }`}
                          />
                          {isNewlyAdded && (
                            <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium animate-bounce z-10">
                              ✨ New!
                            </div>
                          )}
                          {isDeleting && (
                            <div className="absolute inset-0 bg-red-500 bg-opacity-20 flex items-center justify-center z-10">
                              <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                                🗑️ Removing...
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Quick Action Buttons */}
                        {!isDeleting && !bulkActionMode && (
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setPreviewFile(image);
                                }}
                                className="bg-white text-gray-700 hover:bg-gray-100 shadow-md"
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDownloadMedia(image, `image-${index + 1}.jpg`);
                                }}
                                className="bg-white text-gray-700 hover:bg-gray-100 shadow-md"
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteMedia(image, 'image');
                                }}
                                className="bg-red-500 hover:bg-red-700 text-white shadow-md"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )}

                        <div className="p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs font-medium text-[#2D3C2D]">Image {index + 1}</p>
                              <p className="text-xs text-[#766C63]">
                                {isSelected && bulkActionMode ? 'Selected' : 'JPEG/PNG'}
                              </p>
                            </div>
                            {!bulkActionMode && (
                              <div className="flex items-center space-x-1 opacity-60 group-hover:opacity-100 transition-opacity">
                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                                </svg>
                                <span className="text-xs text-gray-400">Drag to reorder</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Videos */}
                  {selectedProperty.videos?.map((video, index) => {
                    const allMediaIndex = (selectedProperty.images?.length || 0) + index;
                    const isYoutube = isYouTubeVideo(video);
                    const youtubeVideoId = isYoutube ? getYouTubeVideoId(video) : null;
                    const thumbnailUrl = isYoutube && youtubeVideoId ? getYouTubeThumbnail(youtubeVideoId) : null;
                    const isNewlyAdded = newlyAddedMedia.includes(video);
                    const isDeleting = deletingMedia.includes(video);
                    const isSelected = selectedMediaItems.includes(video);
                    const isDragOver = dragOverIndex === allMediaIndex;
                    
                    return (
                      <div 
                        key={`video-${index}`}
                        draggable={!bulkActionMode && !isDeleting}
                        onDragStart={(e) => handleDragStart(e, video)}
                        onDragOver={(e) => handleDragOverReorder(e, allMediaIndex)}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDropReorder(e, allMediaIndex)}
                        className={`group relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-500 cursor-pointer ${
                          isNewlyAdded ? 'animate-pulse border-2 border-green-400 shadow-green-200' : ''
                        } ${
                          isDeleting ? 'opacity-30 scale-95 grayscale' : ''
                        } ${
                          isSelected ? 'ring-2 ring-blue-500 border-blue-500' : ''
                        } ${
                          isDragOver ? 'scale-105 shadow-lg border-2 border-blue-400' : ''
                        } ${
                          draggedItem === video ? 'opacity-50 scale-95' : ''
                        }`}
                        onClick={() => bulkActionMode ? toggleMediaSelection(video) : setPreviewFile(video)}
                      >
                        {/* Drag Handle */}
                        {!bulkActionMode && !isDeleting && (
                          <div className="absolute top-2 left-2 bg-gray-800 bg-opacity-70 text-white p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity z-10">
                            <Move className="w-3 h-3" />
                          </div>
                        )}

                        {/* Selection Checkbox */}
                        {bulkActionMode && (
                          <div className="absolute top-2 left-2 z-10">
                            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                              isSelected 
                                ? 'bg-blue-500 border-blue-500' 
                                : 'bg-white bg-opacity-80 border-gray-300 hover:border-blue-400'
                            }`}>
                              {isSelected && (
                                <CheckCircle className="w-4 h-4 text-white" />
                              )}
                            </div>
                          </div>
                        )}
                        <div className="aspect-w-16 aspect-h-12 bg-gray-100 flex items-center justify-center">
                          {isYoutube && thumbnailUrl ? (
                            // YouTube video thumbnail
                            <div className="relative w-full h-48">
                              <img 
                                src={thumbnailUrl} 
                                alt={`YouTube video ${index + 1}`}
                                className="w-full h-48 object-cover"
                                onError={(e) => {
                                  // Fallback to a default YouTube thumbnail if the specific one fails
                                  const target = e.target as HTMLImageElement;
                                  target.src = `https://img.youtube.com/vi/${youtubeVideoId}/hqdefault.jpg`;
                                }}
                              />
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="bg-red-600 bg-opacity-90 rounded-full p-3">
                                  <FileVideo className="w-8 h-8 text-white" />
                                </div>
                              </div>
                              <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
                                YouTube
                              </div>
                            </div>
                          ) : (
                            // Regular video file - Enhanced preview
                            <div className="relative w-full h-48 bg-gray-900 rounded-lg overflow-hidden">
                              <video 
                                src={video} 
                                className="w-full h-full object-cover"
                                controls={false}
                                muted
                                preload="metadata"
                                onMouseEnter={(e) => {
                                  e.currentTarget.play();
                                  e.currentTarget.style.opacity = '0.8';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.pause();
                                  e.currentTarget.currentTime = 0;
                                  e.currentTarget.style.opacity = '1';
                                }}
                                onLoadedData={(e) => {
                                  // Generate thumbnail at 1 second mark
                                  e.currentTarget.currentTime = 1;
                                }}
                              />
                              {/* Video overlay icon */}
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="bg-black bg-opacity-60 rounded-full p-3 group-hover:bg-opacity-40 transition-all duration-200">
                                  <FileVideo className="w-8 h-8 text-white" />
                                </div>
                              </div>
                              {/* Video format badge */}
                              <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded font-medium">
                                VIDEO
                              </div>
                              {/* Duration badge (if available) */}
                              <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                {video.includes('.mp4') ? 'MP4' : 'Video'}
                              </div>
                            </div>
                          )}
                          {isNewlyAdded && (
                            <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium animate-bounce z-10">
                              ✨ New!
                            </div>
                          )}
                          {isDeleting && (
                            <div className="absolute inset-0 bg-red-500 bg-opacity-20 flex items-center justify-center z-10">
                              <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                                🗑️ Removing...
                              </div>
                            </div>
                          )}
                        </div>
                      {/* Quick Action Buttons */}
                      {!isDeleting && !bulkActionMode && (
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setPreviewFile(video);
                              }}
                              className="bg-white text-gray-700 hover:bg-gray-100 shadow-md"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadMedia(video, `video-${index + 1}.mp4`);
                              }}
                              className="bg-white text-gray-700 hover:bg-gray-100 shadow-md"
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteMedia(video, 'video');
                              }}
                              className="bg-red-500 hover:bg-red-700 text-white shadow-md"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                      <div className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <FileVideo className="w-3 h-3 text-[#4A6741]" />
                              <p className="text-sm font-medium text-[#2D3C2D]">Video {index + 1}</p>
                            </div>
                            <p className="text-xs text-[#766C63] mb-1">
                              {isSelected && bulkActionMode ? 'Selected' : isYoutube ? 'YouTube Video' : 'Uploaded Video'}
                            </p>
                            <p className="text-xs text-[#766C63]">
                              Format: {isYoutube ? 'YouTube Embed' : 'MP4/WebM'}
                            </p>
                            {!bulkActionMode && (
                              <div className="flex items-center space-x-1 mt-1">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-xs text-green-600">Ready for streaming</span>
                              </div>
                            )}
                          </div>
                          {!bulkActionMode && (
                            <div className="flex items-center space-x-1 opacity-60 group-hover:opacity-100 transition-opacity">
                              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                              </svg>
                              <span className="text-xs text-gray-400">Drag to reorder</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                  })}
                </div>
              )}

              {((selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)) === 0 && (
                <div className="text-center py-16 bg-gradient-to-br from-[#F7F4F1] to-[#EBE6E1] rounded-lg border-2 border-dashed border-[#D1C7BC]">
                  <div className="mx-auto w-20 h-20 mb-6 rounded-full bg-white shadow-lg flex items-center justify-center">
                    <ImageIcon className="w-10 h-10 text-[#4A6741]" />
                  </div>
                  <h3 className="font-medium text-[#2D3C2D] mb-3 text-lg">Your Gallery Awaits</h3>
                  <p className="text-[#766C63] mb-6 max-w-md mx-auto">
                    Start showcasing your property with stunning photos and engaging videos. Great visuals attract more bookings!
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      onClick={() => setActiveMediaTab('upload')}
                      className="bg-[#4A6741] hover:bg-[#3A5131] text-white shadow-lg"
                      size="lg"
                    >
                      <Upload className="w-5 h-5 mr-2" />
                      Upload Media Files
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setActiveMediaTab('upload')}
                      className="border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white"
                      size="lg"
                    >
                      <Cloud className="w-5 h-5 mr-2" />
                      Advanced Upload
                    </Button>
                  </div>
                </div>
              )}

              {/* Floating Add Media Button */}
              {((selectedProperty.images?.length || 0) + (selectedProperty.videos?.length || 0)) > 0 && !bulkActionMode && (
                <div className="fixed bottom-6 right-6 z-40">
                  <Button
                    onClick={() => setActiveMediaTab('upload')}
                    className="bg-[#4A6741] hover:bg-[#3A5131] text-white rounded-full w-14 h-14 shadow-xl hover:shadow-2xl transition-all duration-300 group"
                  >
                    <div className="flex flex-col items-center">
                      <Upload className="w-6 h-6 group-hover:scale-110 transition-transform" />
                    </div>
                  </Button>
                  <div className="absolute -top-12 -left-16 bg-gray-800 text-white text-xs px-3 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Add more media
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {!selectedProperty && (
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <div className="mx-auto w-20 h-20 mb-6 rounded-full bg-[#F7F4F1] flex items-center justify-center">
            <svg className="w-10 h-10 text-[#766C63]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="font-medium text-[#2D3C2D] mb-2">Select a Property</h3>
          <p className="text-[#766C63] mb-6">Choose one of your properties above to start managing its photos and videos</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
            <div className="bg-[#F7F4F1] p-4 rounded-lg">
              <div className="w-8 h-8 mb-2 rounded bg-[#4A6741] flex items-center justify-center">
                <Upload className="w-4 h-4 text-white" />
              </div>
              <h4 className="font-medium text-[#2D3C2D] mb-1">Upload Multiple Files</h4>
              <p className="text-sm text-[#766C63]">Add photos and videos in bulk</p>
            </div>
            <div className="bg-[#F7F4F1] p-4 rounded-lg">
              <div className="w-8 h-8 mb-2 rounded bg-[#4A6741] flex items-center justify-center">
                <ImageIcon className="w-4 h-4 text-white" />
              </div>
              <h4 className="font-medium text-[#2D3C2D] mb-1">Organize Gallery</h4>
              <p className="text-sm text-[#766C63]">View and manage all media</p>
            </div>
            <div className="bg-[#F7F4F1] p-4 rounded-lg">
              <div className="w-8 h-8 mb-2 rounded bg-[#4A6741] flex items-center justify-center">
                <X className="w-4 h-4 text-white" />
              </div>
              <h4 className="font-medium text-[#2D3C2D] mb-1">Easy Deletion</h4>
              <p className="text-sm text-[#766C63]">Remove unwanted images quickly</p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Media Preview Dialog */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden p-0">
          <div className="relative bg-black rounded-lg overflow-hidden">
            {/* Close Button */}
            <button
              onClick={() => setPreviewFile(null)}
              className="absolute top-4 right-4 z-50 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-2 transition-all"
            >
              <X className="w-6 h-6" />
            </button>

            {/* Navigation Arrows */}
            {selectedProperty && previewFile && (
              <>
                {(() => {
                  const allMedia = [...(selectedProperty.images || []), ...(selectedProperty.videos || [])];
                  const currentIndex = allMedia.indexOf(previewFile);
                  const hasPrevious = currentIndex > 0;
                  const hasNext = currentIndex < allMedia.length - 1;
                  
                  return (
                    <>
                      {hasPrevious && (
                        <button
                          onClick={() => setPreviewFile(allMedia[currentIndex - 1])}
                          className="absolute left-4 top-1/2 -translate-y-1/2 z-50 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                        >
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>
                      )}
                      {hasNext && (
                        <button
                          onClick={() => setPreviewFile(allMedia[currentIndex + 1])}
                          className="absolute right-4 top-1/2 -translate-y-1/2 z-50 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                        >
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      )}
                      
                      {/* Media Counter */}
                      <div className="absolute top-4 left-4 z-50 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                        {currentIndex + 1} of {allMedia.length}
                      </div>

                      {/* Keyboard Shortcuts Tooltip */}
                      <div className="absolute top-16 left-4 z-50 bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg text-xs opacity-0 hover:opacity-100 transition-opacity">
                        <div className="space-y-1">
                          <div className="font-medium mb-1">Keyboard Shortcuts:</div>
                          <div>← → Navigate</div>
                          <div>ESC Close</div>
                          <div>DEL Delete</div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </>
            )}
            {/* Media Content */}
            <div className="flex items-center justify-center min-h-[60vh] p-4">
              {previewFile && (
                <>
                  {isYouTubeVideo(previewFile) ? (
                    <iframe
                      src={previewFile}
                      className="w-full max-w-4xl aspect-video rounded-lg"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      title="YouTube video preview"
                    />
                  ) : previewFile.includes('video') || previewFile.includes('.mp4') || previewFile.includes('.webm') ? (
                    <video 
                      src={previewFile} 
                      controls 
                      className="w-full max-w-4xl max-h-[70vh] rounded-lg"
                      preload="metadata"
                      autoPlay
                    />
                  ) : (
                    <img 
                      src={previewFile} 
                      alt="Media preview" 
                      className="w-full max-w-4xl max-h-[70vh] rounded-lg object-contain"
                    />
                  )}
                </>
              )}
            </div>

            {/* Media Info Bar */}
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  {previewFile && selectedProperty && (
                    <>
                      {(() => {
                        const allMedia = [...(selectedProperty.images || []), ...(selectedProperty.videos || [])];
                        const currentIndex = allMedia.indexOf(previewFile);
                        const isImage = selectedProperty.images?.includes(previewFile);
                        const isVideo = selectedProperty.videos?.includes(previewFile);
                        
                        return (
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              {isYouTubeVideo(previewFile) ? (
                                <FileVideo className="w-5 h-5 text-red-400" />
                              ) : isVideo ? (
                                <FileVideo className="w-5 h-5 text-blue-400" />
                              ) : (
                                <ImageIcon className="w-5 h-5 text-green-400" />
                              )}
                              <span className="font-medium">
                                {isYouTubeVideo(previewFile) ? 'YouTube Video' : isVideo ? 'Video' : 'Image'} {currentIndex + 1}
                              </span>
                            </div>
                            <div className="text-sm opacity-75">
                              {isYouTubeVideo(previewFile) ? 'YouTube Embed' : isVideo ? 'MP4/WebM Format' : 'JPEG/PNG Format'}
                            </div>
                          </div>
                        );
                      })()}
                    </>
                  )}
                </div>
                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (!previewFile) return;
                      if (isYouTubeVideo(previewFile)) {
                        window.open(previewFile.replace('/embed/', '/watch?v='), '_blank');
                      } else {
                        handleDownloadMedia(previewFile, `media-${Date.now()}`);
                      }
                    }}
                    className="bg-white bg-opacity-20 text-white border-white border-opacity-30 hover:bg-opacity-30"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    {previewFile && isYouTubeVideo(previewFile) ? 'YouTube' : 'Download'}
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      if (!previewFile) return;
                      const mediaType = previewFile.includes('video') ? 'video' : 'image';
                      handleDeleteMedia(previewFile, mediaType);
                      setPreviewFile(null);
                    }}
                    className="bg-red-500 bg-opacity-80 hover:bg-opacity-100"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Media Edit Dialog */}
      <Dialog open={!!editingMedia} onOpenChange={() => setEditingMedia(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Media Details</DialogTitle>
            <DialogDescription>
              Add or edit title and caption for organizational purposes
            </DialogDescription>
          </DialogHeader>
          {editingMedia && (
            <div className="space-y-4">
              <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                {editingMedia.url.includes('video') || editingMedia.url.includes('.mp4') || editingMedia.url.includes('.webm') ? (
                  <video 
                    src={editingMedia.url} 
                    className="max-w-full h-32 rounded-lg object-cover"
                    muted
                  />
                ) : (
                  <img 
                    src={editingMedia.url} 
                    alt="Media thumbnail" 
                    className="max-w-full h-32 rounded-lg object-cover"
                  />
                )}
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                  </label>
                  <Input
                    value={tempEditData.title}
                    onChange={(e) => setTempEditData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter a title for this media item"
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Caption
                  </label>
                  <Textarea
                    value={tempEditData.caption}
                    onChange={(e) => setTempEditData(prev => ({ ...prev, caption: e.target.value }))}
                    placeholder="Add a caption or description"
                    className="w-full"
                    rows={3}
                  />
                </div>
              </div>
              
              <div className="flex space-x-2 justify-end">
                <Button
                  variant="outline"
                  onClick={handleCancelEdit}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveMediaEdit}
                  className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteConfirmation} onOpenChange={() => setDeleteConfirmation(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span>Remove {deleteConfirmation?.type || 'Media'}</span>
            </DialogTitle>
            <DialogDescription>
              {deleteConfirmation?.type === 'video' 
                ? "Are you sure you want to remove this video?"
                : `Are you sure you want to remove this ${deleteConfirmation?.type || 'photo'}?`
              }
            </DialogDescription>
          </DialogHeader>
          {deleteConfirmation && (
            <div className="space-y-4">
              <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                {deleteConfirmation.type === 'video' ? (
                  <video 
                    src={deleteConfirmation.url} 
                    className="max-w-full h-32 rounded-lg object-cover"
                    muted
                  />
                ) : (
                  <img 
                    src={deleteConfirmation.url} 
                    alt="Media to delete" 
                    className="max-w-full h-32 rounded-lg object-cover"
                  />
                )}
              </div>
              
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-4 h-4 text-amber-600" />
                  <p className="text-sm text-amber-800">
                    <strong>Warning:</strong> This action cannot be undone. The {deleteConfirmation.type} will be permanently removed from your property listing and cloud storage.
                  </p>
                </div>
              </div>
              
              <div className="flex space-x-2 justify-end">
                <Button
                  variant="outline"
                  onClick={cancelDelete}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmDelete}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Yes, Remove {deleteConfirmation.type}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};