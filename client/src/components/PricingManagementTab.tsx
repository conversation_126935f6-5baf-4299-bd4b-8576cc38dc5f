import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PropertyWithBookings } from '@/features/dashboard/types';
import { PropertyPricingCard } from './PropertyPricingCard';
import { PropertyPricingComparison } from './PropertyPricingComparison';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  DollarSign, 
  Building2, 
  TrendingUp, 
  Search,
  Filter,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Zap,
  Calendar,
  ArrowUpDown
} from 'lucide-react';

interface PricingManagementTabProps {
  properties: PropertyWithBookings[];
  isLoading: boolean;
  onPricingUpdate?: () => void;
}

type SortOption = 'name' | 'base_price' | 'weekend_pricing' | 'status' | 'last_updated';
type ViewMode = 'grid' | 'compact' | 'comparison';

export function PricingManagementTab({ 
  properties, 
  isLoading, 
  onPricingUpdate 
}: PricingManagementTabProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [pricingFilter, setPricingFilter] = useState<'all' | 'basic' | 'advanced' | 'weekend'>('all');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  // Calculate pricing statistics
  const pricingStats = useMemo(() => {
    if (!properties?.length) return null;

    const totalProperties = properties.length;
    const basicPricing = properties.filter(p => !p.weekdayHalfDayPrice && !p.weekendHalfDayPrice).length;
    const weekdayPricing = properties.filter(p => p.weekdayHalfDayPrice || p.weekdayFullDayPrice).length;
    const weekendPricing = properties.filter(p => p.weekendHalfDayPrice || p.weekendFullDayPrice).length;
    const averageHalfDay = Math.round(properties.reduce((sum, p) => sum + (p.halfDayPrice || 0), 0) / totalProperties);
    const averageFullDay = Math.round(properties.reduce((sum, p) => sum + (p.fullDayPrice || 0), 0) / totalProperties);

    return {
      totalProperties,
      basicPricing,
      weekdayPricing,
      weekendPricing,
      advancedPricing: weekdayPricing + weekendPricing,
      averageHalfDay,
      averageFullDay,
      pricingUtilization: Math.round(((weekdayPricing + weekendPricing) / totalProperties) * 100)
    };
  }, [properties]);

  // Filter and sort properties
  const filteredAndSortedProperties = useMemo(() => {
    if (!properties) return [];

    let filtered = properties.filter(property => {
      // Search filter
      const matchesSearch = !searchQuery || 
        property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        property.location.toLowerCase().includes(searchQuery.toLowerCase());

      // Pricing strategy filter
      const hasWeekday = property.weekdayHalfDayPrice || property.weekdayFullDayPrice;
      const hasWeekend = property.weekendHalfDayPrice || property.weekendFullDayPrice;
      
      const matchesPricingFilter = pricingFilter === 'all' || 
        (pricingFilter === 'basic' && !hasWeekday && !hasWeekend) ||
        (pricingFilter === 'advanced' && (hasWeekday || hasWeekend)) ||
        (pricingFilter === 'weekend' && hasWeekend);

      return matchesSearch && matchesPricingFilter;
    });

    // Sort properties
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'base_price':
          return (b.halfDayPrice || 0) - (a.halfDayPrice || 0);
        case 'weekend_pricing':
          const aWeekend = a.weekendHalfDayPrice || a.halfDayPrice || 0;
          const bWeekend = b.weekendHalfDayPrice || b.halfDayPrice || 0;
          return bWeekend - aWeekend;
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    return filtered;
  }, [properties, searchQuery, pricingFilter, sortBy]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Stats Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-4 w-1/2 mb-2" />
                <Skeleton className="h-8 w-1/3" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Properties Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Pricing Overview Stats */}
      {pricingStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Properties</p>
                  <p className="text-2xl font-bold text-gray-900">{pricingStats.totalProperties}</p>
                </div>
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Advanced Pricing</p>
                  <p className="text-2xl font-bold text-gray-900">{pricingStats.advancedPricing}</p>
                  <p className="text-xs text-gray-500">{pricingStats.pricingUtilization}% utilization</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Half-Day</p>
                  <p className="text-2xl font-bold text-gray-900">₹{pricingStats.averageHalfDay.toLocaleString('en-IN')}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Full-Day</p>
                  <p className="text-2xl font-bold text-gray-900">₹{pricingStats.averageFullDay.toLocaleString('en-IN')}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Property Pricing Management
            <Badge variant="outline" className="ml-auto">
              {filteredAndSortedProperties.length} properties
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search properties by name or location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>

            {/* Pricing Filter */}
            <Select value={pricingFilter} onValueChange={(value: any) => setPricingFilter(value)}>
              <SelectTrigger className="w-full lg:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by pricing" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Pricing Types</SelectItem>
                <SelectItem value="basic">Basic Pricing Only</SelectItem>
                <SelectItem value="advanced">Advanced Pricing</SelectItem>
                <SelectItem value="weekend">Weekend Pricing</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-full lg:w-48">
                <ArrowUpDown className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Property Name</SelectItem>
                <SelectItem value="base_price">Base Price (High to Low)</SelectItem>
                <SelectItem value="weekend_pricing">Weekend Price</SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>

            {/* View Mode Toggle */}
            <div className="flex border rounded-md">
              {(['grid', 'compact', 'comparison'] as ViewMode[]).map((mode) => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className="capitalize text-xs"
                >
                  {mode === 'comparison' ? 'Compare' : mode}
                </Button>
              ))}
            </div>
          </div>

          {/* Pricing Insights */}
          {pricingStats && pricingStats.pricingUtilization < 50 && (
            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-amber-800">Pricing Optimization Opportunity</p>
                  <p className="text-amber-700">
                    Only {pricingStats.pricingUtilization}% of your properties use advanced pricing strategies. 
                    Consider implementing weekend or seasonal pricing to maximize revenue.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Properties Grid */}
      {filteredAndSortedProperties.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              {searchQuery || pricingFilter !== 'all' 
                ? 'No properties match your search criteria.' 
                : 'No properties found.'
              }
            </p>
          </CardContent>
        </Card>
      ) : viewMode === 'comparison' ? (
        <PropertyPricingComparison properties={filteredAndSortedProperties} />
      ) : (
        <div className={
          viewMode === 'compact' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'
            : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
        }>
          {filteredAndSortedProperties.map((property) => {
            // Convert PropertyWithBookings to Property format for compatibility
            const compatibleProperty = {
              id: property.id,
              ownerId: property.ownerId,
              title: property.title,
              description: property.description,
              location: property.location,
              halfDayPrice: property.halfDayPrice,
              fullDayPrice: property.fullDayPrice,
              weekdayHalfDayPrice: property.weekdayHalfDayPrice ?? null,
              weekdayFullDayPrice: property.weekdayFullDayPrice ?? null,
              weekendHalfDayPrice: property.weekendHalfDayPrice ?? null,
              weekendFullDayPrice: property.weekendFullDayPrice ?? null,
              images: property.images,
              videos: property.videos || [],
              amenities: property.amenities,
              maxGuests: property.maxGuests,
              bedrooms: property.bedrooms || 0,
              bathrooms: property.bathrooms || 0,
              area: property.area || 0,
              status: property.status,
              featured: property.featured,
              createdAt: property.createdAt,
              latitude: null,
              longitude: null,
            };
            
            return (
              <PropertyPricingCard
                key={property.id}
                property={compatibleProperty}
                onPricingUpdate={onPricingUpdate || (() => {})}
                compact={viewMode === 'compact'}
              />
            );
          })}
        </div>
      )}

      {/* Pricing Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Zap className="h-4 w-4 text-blue-600" />
            Pricing Tips & Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium">Weekend Premium</p>
                <p className="text-gray-600">Set 15-30% higher rates for Friday-Sunday</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <Calendar className="h-4 w-4 text-blue-600 mt-0.5" />
              <div>
                <p className="font-medium">Seasonal Adjustment</p>
                <p className="text-gray-600">Adjust pricing based on local events and seasons</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <BarChart3 className="h-4 w-4 text-purple-600 mt-0.5" />
              <div>
                <p className="font-medium">Market Analysis</p>
                <p className="text-gray-600">Research competitor pricing in your area regularly</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PricingManagementTab;