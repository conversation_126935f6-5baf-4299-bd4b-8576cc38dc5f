import React, { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Shield, Clock, AlertTriangle, RefreshCw, LogOut, Eye, EyeOff } from "lucide-react";
import { useSessionSecurity } from "@/lib/session";
import { auditLogger, SecurityEventType } from "@/lib/audit";

// Rate Limiting Alert Component
interface RateLimitAlertProps {
  isBlocked: boolean;
  remainingTime: number; // in seconds
  attempts: number;
  maxAttempts: number;
  onRetry?: () => void;
  context?: string;
}

export const RateLimitAlert: React.FC<RateLimitAlertProps> = ({
  isBlocked,
  remainingTime,
  attempts,
  maxAttempts,
  onRetry,
  context = 'action'
}) => {
  const [timeLeft, setTimeLeft] = useState(remainingTime);

  useEffect(() => {
    if (!isBlocked || timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isBlocked, timeLeft]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isBlocked) {
    // Show warning when approaching limit
    const remainingAttempts = maxAttempts - attempts;
    if (remainingAttempts <= 2 && attempts > 0) {
      return (
        <Alert variant="default" className="border-yellow-200 bg-yellow-50">
          <Shield className="h-4 w-4 text-yellow-600" />
          <AlertTitle className="text-yellow-800">Security Notice</AlertTitle>
          <AlertDescription className="text-yellow-700">
            {remainingAttempts} {context} attempt{remainingAttempts !== 1 ? 's' : ''} remaining before temporary block.
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  }

  return (
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Too Many Attempts</AlertTitle>
      <AlertDescription>
        <div className="space-y-2">
          <p>You've exceeded the maximum number of {context} attempts ({maxAttempts}).</p>
          {timeLeft > 0 ? (
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Please wait {formatTime(timeLeft)} before trying again.</span>
            </div>
          ) : onRetry ? (
            <Button 
              onClick={onRetry} 
              variant="outline" 
              size="sm"
              className="mt-2"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          ) : (
            <p>You can try again now.</p>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};

// Session Timeout Warning Modal
interface SessionTimeoutProps {
  isOpen: boolean;
  timeLeft: number; // in seconds
  onExtend: () => void;
  onLogout: () => void;
}

export const SessionTimeoutWarning: React.FC<SessionTimeoutProps> = ({
  isOpen,
  timeLeft,
  onExtend,
  onLogout
}) => {
  const [secondsLeft, setSecondsLeft] = useState(timeLeft);

  useEffect(() => {
    if (!isOpen) return;

    setSecondsLeft(timeLeft);
    
    const timer = setInterval(() => {
      setSecondsLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          onLogout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen, timeLeft, onLogout]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent 
        className="sm:max-w-[400px]" 
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-yellow-600" />
            Session Expiring Soon
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="default" className="border-yellow-200 bg-yellow-50">
            <Clock className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              For your security, your session will expire in:
              <div className="text-2xl font-mono font-bold mt-2 text-center">
                {formatTime(secondsLeft)}
              </div>
            </AlertDescription>
          </Alert>

          <div className="flex gap-3">
            <Button
              onClick={onExtend}
              className="flex-1 bg-[#4A6741] hover:bg-[#3A5235]"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Extend Session
            </Button>
            
            <Button
              onClick={onLogout}
              variant="outline"
              className="flex-1"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout Now
            </Button>
          </div>

          <p className="text-xs text-gray-600 text-center">
            Sessions expire automatically for security reasons
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Security Activity Monitor
interface SecurityActivityProps {
  showMonitor?: boolean;
  className?: string;
}

export const SecurityActivityMonitor: React.FC<SecurityActivityProps> = ({
  showMonitor = false,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(showMonitor);
  const [activities, setActivities] = useState<Array<{
    id: string;
    type: string;
    message: string;
    timestamp: Date;
    severity: 'info' | 'warning' | 'error';
  }>>([]);

  const { getSecurityContext } = useSessionSecurity();

  useEffect(() => {
    const context = getSecurityContext();
    if (!context) return;

    // Mock security activities - in real implementation, this would come from your security system
    const severity: 'error' | 'warning' | 'info' = context.riskScore > 50 ? 'warning' : 'info';
    const mockActivity = {
      id: `activity_${Date.now()}`,
      type: 'session_activity',
      message: `Risk score: ${context.riskScore}%, Payment attempts: ${context.paymentAttempts}`,
      timestamp: new Date(),
      severity,
    };

    setActivities(prev => [mockActivity, ...prev.slice(0, 4)]);
  }, [getSecurityContext]);

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="ghost"
        size="sm"
        className={`fixed bottom-4 right-4 z-50 ${className}`}
      >
        <Eye className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 w-80 ${className}`}>
      <Alert className="bg-white border-gray-200 shadow-lg">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-semibold">Security Monitor</span>
          </div>
          <Button
            onClick={() => setIsVisible(false)}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <EyeOff className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {activities.map(activity => (
            <div
              key={activity.id}
              className={`text-xs p-2 rounded border-l-2 ${
                activity.severity === 'error' 
                  ? 'border-red-400 bg-red-50 text-red-800'
                  : activity.severity === 'warning'
                  ? 'border-yellow-400 bg-yellow-50 text-yellow-800'
                  : 'border-blue-400 bg-blue-50 text-blue-800'
              }`}
            >
              <div className="font-medium">{activity.type}</div>
              <div>{activity.message}</div>
              <div className="text-xs opacity-75 mt-1">
                {activity.timestamp.toLocaleTimeString()}
              </div>
            </div>
          ))}
          {activities.length === 0 && (
            <div className="text-xs text-gray-500 p-2">
              No recent security activity
            </div>
          )}
        </div>
      </Alert>
    </div>
  );
};

// Suspicious Activity Alert
interface SuspiciousActivityAlertProps {
  isOpen: boolean;
  activityType: string;
  description: string;
  riskScore: number;
  onAcknowledge: () => void;
  onSecureAccount: () => void;
}

export const SuspiciousActivityAlert: React.FC<SuspiciousActivityAlertProps> = ({
  isOpen,
  activityType,
  description,
  riskScore,
  onAcknowledge,
  onSecureAccount
}) => {
  const getSeverityColor = (risk: number) => {
    if (risk >= 80) return 'text-red-600 bg-red-50 border-red-200';
    if (risk >= 60) return 'text-orange-600 bg-orange-50 border-orange-200';
    if (risk >= 40) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-blue-600 bg-blue-50 border-blue-200';
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Suspicious Activity Detected
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Security Alert</AlertTitle>
            <AlertDescription>
              We've detected suspicious activity on your account: <strong>{activityType}</strong>
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <div className="text-sm text-gray-600">Description:</div>
            <p className="text-sm">{description}</p>
          </div>

          <div className={`p-3 rounded border ${getSeverityColor(riskScore)}`}>
            <div className="flex items-center justify-between">
              <span className="font-medium">Risk Score</span>
              <span className="font-bold text-lg">{riskScore}%</span>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              onClick={onSecureAccount}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              <Shield className="h-4 w-4 mr-2" />
              Secure Account
            </Button>
            
            <Button
              onClick={onAcknowledge}
              variant="outline"
              className="flex-1"
            >
              I Recognize This Activity
            </Button>
          </div>

          <p className="text-xs text-gray-600 text-center">
            If you don't recognize this activity, please secure your account immediately.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};