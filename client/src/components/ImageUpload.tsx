import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { X, Upload, Image as ImageIcon, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { INTERVALS } from '../../../shared/constants';

interface UploadedImage {
  url: string;
  publicId: string;
  width: number;
  height: number;
  format: string;
  bytes: number;
}

interface FileProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  url?: string;
}

interface ImageUploadProps {
  images: string[];
  onImagesChange: (urls: string[]) => void;
  maxImages?: number;
  className?: string;
}

export function ImageUpload({ 
  images, 
  onImagesChange, 
  maxImages = 10,
  className = "" 
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [fileProgress, setFileProgress] = useState<FileProgress[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const remainingSlots = maxImages - images.length;
    if (files.length > remainingSlots) {
      toast({
        title: "Too many images",
        description: `You can only upload ${remainingSlots} more image(s).`,
        variant: "destructive"
      });
      return;
    }

    // Calculate total size of selected files
    const totalSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);
    const maxTotalSize = 10 * 1024 * 1024; // 10MB total limit

    if (totalSize > maxTotalSize) {
      toast({
        title: "Total file size too large",
        description: `Total size of selected files (${(totalSize / 1024 / 1024).toFixed(1)}MB) exceeds the 10MB limit.`,
        variant: "destructive"
      });
      return;
    }

    // Validate file types and individual sizes
    const validFiles = Array.from(files).filter(file => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      const isValidType = isImage || isVideo;

      // Set size limits based on file type
      const maxIndividualSize = isImage ? 5 * 1024 * 1024 : isVideo ? 5 * 1024 * 1024 : 0; // 5MB for both images and videos
      const isValidSize = file.size <= maxIndividualSize;

      if (!isValidType) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a valid image or video file.`,
          variant: "destructive"
        });
        return false;
      }

      if (!isValidSize) {
        const fileType = isImage ? 'image' : 'video';
        toast({
          title: "File too large",
          description: `${file.name} (${fileType}) is larger than 5MB limit.`,
          variant: "destructive"
        });
        return false;
      }

      return true;
    });

    if (validFiles.length === 0) return;

    await uploadImages(validFiles);
  };

  const uploadImages = async (files: File[]) => {
    setUploading(true);

    // Initialize progress for each file
    const initialProgress = files.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const
    }));
    setFileProgress(initialProgress);

    try {
      // Upload files individually for better progress tracking
      const uploadedImages: string[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        
        // Use appropriate field name based on file type
        const isVideo = file.type.startsWith('video/');
        formData.append(isVideo ? 'videos' : 'images', file);

        // Simulate individual file progress
        const progressInterval = setInterval(() => {
          setFileProgress(prev => 
            prev.map((fp, index) => 
              index === i && fp.progress < 90 
                ? { ...fp, progress: fp.progress + 10 }
                : fp
            )
          );
        }, 150);

        try {
          // Get token from localStorage for authentication
          const token = localStorage.getItem("token");
          const headers: Record<string, string> = {};
          if (token) {
            headers["Authorization"] = `Bearer ${token}`;
          }

          const response = await fetch('/api/upload/media', {
            method: 'POST',
            headers,
            body: formData,
            credentials: 'include',
          });

          clearInterval(progressInterval);

          if (!response.ok) {
            throw new Error(`Upload failed: ${response.status}`);
          }

          const result = await response.json();

          if (result.success && result.images && result.images.length > 0) {
            const imageUrl = result.images[0].url;
            uploadedImages.push(imageUrl);

            // Mark this file as completed
            setFileProgress(prev => 
              prev.map((fp, index) => 
                index === i 
                  ? { ...fp, progress: 100, status: 'completed', url: imageUrl }
                  : fp
              )
            );
          } else {
            throw new Error(result.message || 'Upload failed');
          }
        } catch (error) {
          clearInterval(progressInterval);
          // Mark this file as error
          setFileProgress(prev => 
            prev.map((fp, index) => 
              index === i 
                ? { ...fp, status: 'error' }
                : fp
            )
          );
          throw error;
        }
      }

      // Update images array with all successfully uploaded images
      if (uploadedImages.length > 0) {
        onImagesChange([...images, ...uploadedImages]);

        toast({
          title: "Images uploaded successfully",
          description: `${uploadedImages.length} image(s) uploaded.`
        });
      }

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload images",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      // Clear progress after a short delay
      setTimeout(() => setFileProgress([]), INTERVALS.BATCH_UPDATE);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeImage = (indexToRemove: number) => {
    const newImages = images.filter((_, index) => index !== indexToRemove);
    onImagesChange(newImages);
  };

  const canUploadMore = images.length < maxImages && !uploading;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Button */}
      {canUploadMore && (
        <div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,video/*"
            multiple
            onChange={handleFileSelect}
            className="hidden"
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="w-full border-dashed border-2 h-32 bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <div className="flex flex-col items-center gap-2">
              {uploading ? (
                <Loader2 className="h-8 w-8 animate-spin text-[#7FA650]" />
              ) : (
                <Upload className="h-8 w-8 text-[#7FA650]" />
              )}
              <span className="text-sm font-medium">
                {uploading ? 'Uploading...' : 'Click to upload images & videos'}
              </span>
              <span className="text-xs text-gray-500">
                {images.length}/{maxImages} files • Max 5MB each • 10MB total
              </span>
            </div>
          </Button>
        </div>
      )}

      {/* Per-File Upload Progress */}
      {fileProgress.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Upload Progress:</h4>
          {fileProgress.map((fp, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between items-center text-xs">
                <span className="text-gray-600 dark:text-gray-400 truncate max-w-[60%]">
                  {fp.file.name}
                </span>
                <span className={`font-medium ${
                  fp.status === 'completed' ? 'text-green-600' :
                  fp.status === 'error' ? 'text-red-600' :
                  'text-blue-600'
                }`}>
                  {fp.status === 'completed' ? '✓ Complete' :
                   fp.status === 'error' ? '✗ Failed' :
                   `${fp.progress}%`}
                </span>
              </div>
              <Progress 
                value={fp.status === 'completed' ? 100 : fp.progress} 
                className="h-2"
              />
            </div>
          ))}
        </div>
      )}

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                <img
                  src={imageUrl}
                  alt={`Upload ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDNIMTNMMTEgMUg1QzMuODkgMSAzIDEuODkgMyAzVjE5QzMgMjAuMTEgMy44OSAyMSA1IDIxSDE5QzIwLjExIDIxIDIxIDIwLjExIDIxIDE5VjNaTTEyIDdMMTMuNSA5SDEwLjVMMTIgN1oiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                  }}
                />
              </div>
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeImage(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && !uploading && (
        <div className="text-center py-8 text-gray-500">
          <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No images uploaded yet</p>
        </div>
      )}

      {/* Max Images Reached */}
      {images.length >= maxImages && (
        <p className="text-sm text-gray-500 text-center">
          Maximum number of images reached ({maxImages}/{maxImages})
        </p>
      )}
    </div>
  );
}