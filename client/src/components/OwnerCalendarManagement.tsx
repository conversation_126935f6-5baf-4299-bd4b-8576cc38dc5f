import React, { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format, startOfMonth, endOfMonth, addMonths, subMonths } from 'date-fns';
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  Filter, 
  Download, 
  Upload, 
  Settings,
  BarChart3,
  Eye,
  EyeOff,
  Grid,
  List,
  Search,
  RefreshCw
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from './ui/select';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { PropertyFullCalendar } from './PropertyFullCalendar';
import { EnhancedBookingModal } from './EnhancedBookingModal';
import { useWebSocket } from '../contexts/WebSocketContext';
import { useToast } from '../hooks/use-toast';
import { cn } from '../lib/utils';

interface Property {
  id: string;
  title: string;
  location: string;
  isActive: boolean;
  pricePerNight: number;
}

interface CalendarStats {
  totalBookings: number;
  confirmedBookings: number;
  tentativeBookings: number;
  blockedDays: number;
  occupancyRate: number;
  revenue: number;
  upcomingCheckIns: number;
  conflictsCount: number;
}

interface OwnerCalendarManagementProps {
  properties: Property[];
  selectedPropertyId?: string;
  onPropertySelect?: (propertyId: string) => void;
}

export function OwnerCalendarManagement({ 
  properties, 
  selectedPropertyId,
  onPropertySelect 
}: OwnerCalendarManagementProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showInactive, setShowInactive] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<string>(selectedPropertyId || properties[0]?.id || '');
  const [isStatsExpanded, setIsStatsExpanded] = useState(true);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);

  const { toast } = useToast();
  const { isConnected } = useWebSocket();
  const queryClient = useQueryClient();

  // Filter properties based on search and status
  const filteredProperties = useMemo(() => {
    return properties.filter(property => {
      const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           property.location.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = showInactive || property.isActive;
      return matchesSearch && matchesStatus;
    });
  }, [properties, searchTerm, showInactive]);

  // Fetch calendar stats for selected property
  const { data: calendarStats, isLoading: statsLoading } = useQuery({
    queryKey: ['calendar-stats', selectedProperty, format(currentMonth, 'yyyy-MM')],
    queryFn: async () => {
      if (!selectedProperty) return null;
      
      const startDate = format(startOfMonth(currentMonth), 'yyyy-MM-dd');
      const endDate = format(endOfMonth(currentMonth), 'yyyy-MM-dd');
      
      const response = await fetch(`/api/calendar/stats/${selectedProperty}?startDate=${startDate}&endDate=${endDate}`);
      if (!response.ok) throw new Error('Failed to fetch stats');
      
      return response.json();
    },
    enabled: !!selectedProperty
  });

  // Fetch all bookings for property list view
  const { data: allBookings, isLoading: bookingsLoading } = useQuery({
    queryKey: ['all-calendar-bookings', statusFilter, format(currentMonth, 'yyyy-MM')],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: format(startOfMonth(currentMonth), 'yyyy-MM-dd'),
        endDate: format(endOfMonth(currentMonth), 'yyyy-MM-dd')
      });
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      
      const response = await fetch(`/api/calendar/bookings?${params}`);
      if (!response.ok) throw new Error('Failed to fetch bookings');
      
      return response.json();
    }
  });

  const handlePropertySelect = (propertyId: string) => {
    setSelectedProperty(propertyId);
    onPropertySelect?.(propertyId);
  };

  const handleMonthChange = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => 
      direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1)
    );
  };

  const exportCalendar = async (format: 'ical' | 'csv') => {
    try {
      const response = await fetch(`/api/calendar/${selectedProperty}/export?format=${format}`);
      if (!response.ok) throw new Error('Export failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `calendar-${selectedProperty}-${format(currentMonth, 'yyyy-MM')}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      
      toast({
        title: 'Export Successful',
        description: `Calendar exported as ${format.toUpperCase()}`
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export calendar. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const refreshData = () => {
    queryClient.invalidateQueries({ queryKey: ['calendar-bookings'] });
    queryClient.invalidateQueries({ queryKey: ['calendar-stats'] });
    queryClient.invalidateQueries({ queryKey: ['all-calendar-bookings'] });
    
    toast({
      title: 'Data Refreshed',
      description: 'Calendar data has been updated'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Calendar Management</h2>
          <p className="text-gray-600">Manage bookings across all your properties</p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Connection Status */}
          <div className={cn(
            "px-2 py-1 rounded-full text-xs font-medium",
            isConnected 
              ? "bg-green-100 text-green-700" 
              : "bg-red-100 text-red-700"
          )}>
            {isConnected ? '● Live' : '● Offline'}
          </div>
          
          <Button size="sm" variant="outline" onClick={refreshData}>
            <RefreshCw size={16} />
          </Button>
        </div>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Search properties..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="tentative">Tentative</SelectItem>
                  <SelectItem value="blocked">Blocked</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center gap-2">
                <Switch 
                  id="show-inactive"
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label htmlFor="show-inactive" className="text-sm">
                  Show inactive
                </Label>
              </div>

              {/* View Mode Toggle */}
              <div className="flex border rounded-lg p-1">
                <Button
                  size="sm"
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('grid')}
                  className="h-8 px-3"
                >
                  <Grid size={16} />
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('list')}
                  className="h-8 px-3"
                >
                  <List size={16} />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      {selectedProperty && (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {properties.find(p => p.id === selectedProperty)?.title} - {format(currentMonth, 'MMMM yyyy')}
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsStatsExpanded(!isStatsExpanded)}
                >
                  {isStatsExpanded ? <EyeOff size={16} /> : <Eye size={16} />}
                </Button>
                <div className="flex border rounded-lg overflow-hidden">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleMonthChange('prev')}
                    className="h-8 px-2 rounded-none"
                  >
                    <ChevronLeft size={16} />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleMonthChange('next')}
                    className="h-8 px-2 rounded-none"
                  >
                    <ChevronRight size={16} />
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          
          {isStatsExpanded && (
            <CardContent>
              {statsLoading ? (
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="bg-gray-100 rounded-lg h-20 animate-pulse" />
                  ))}
                </div>
              ) : calendarStats ? (
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-blue-600 text-sm font-medium">Total Bookings</p>
                    <p className="text-2xl font-bold text-blue-900">{calendarStats.totalBookings}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-green-600 text-sm font-medium">Occupancy Rate</p>
                    <p className="text-2xl font-bold text-green-900">{calendarStats.occupancyRate}%</p>
                  </div>
                  <div className="bg-amber-50 p-4 rounded-lg">
                    <p className="text-amber-600 text-sm font-medium">Revenue</p>
                    <p className="text-2xl font-bold text-amber-900">₹{calendarStats.revenue?.toLocaleString()}</p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <p className="text-purple-600 text-sm font-medium">Check-ins</p>
                    <p className="text-2xl font-bold text-purple-900">{calendarStats.upcomingCheckIns}</p>
                  </div>
                </div>
              ) : null}
            </CardContent>
          )}
        </Card>
      )}

      {/* Main Content */}
      <Tabs defaultValue="calendar" className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            <TabsTrigger value="bookings">All Bookings</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <div className="flex items-center gap-2">
            <Button size="sm" onClick={() => setIsBookingModalOpen(true)}>
              <Plus size={16} className="mr-1" />
              New Booking
            </Button>
            
            <Select onValueChange={(value) => exportCalendar(value as 'ical' | 'csv')}>
              <SelectTrigger className="w-32">
                <Download size={16} className="mr-1" />
                Export
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ical">iCal Format</SelectItem>
                <SelectItem value="csv">CSV Format</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Calendar Tab */}
        <TabsContent value="calendar" className="space-y-4">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Property Selector */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Properties</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {filteredProperties.map(property => (
                      <div
                        key={property.id}
                        className={cn(
                          "p-3 rounded-lg border cursor-pointer transition-colors",
                          selectedProperty === property.id
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        )}
                        onClick={() => handlePropertySelect(property.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{property.title}</p>
                            <p className="text-sm text-gray-600">{property.location}</p>
                            <p className="text-sm text-gray-500">₹{property.pricePerNight}/night</p>
                          </div>
                          <div className="flex flex-col items-end gap-1">
                            <Badge 
                              variant={property.isActive ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {property.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>

              {/* Calendar */}
              <div className="lg:col-span-2">
                {selectedProperty ? (
                  <PropertyFullCalendar 
                    propertyId={selectedProperty}
                    className="h-fit"
                  />
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-600">Select a property to view its calendar</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          ) : (
            // List View
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {filteredProperties.map(property => (
                    <div key={property.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="font-semibold">{property.title}</h3>
                          <p className="text-sm text-gray-600">{property.location}</p>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handlePropertySelect(property.id)}
                        >
                          View Calendar
                        </Button>
                      </div>
                      <PropertyFullCalendar 
                        propertyId={property.id}
                        className="h-96"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Bookings Tab */}
        <TabsContent value="bookings">
          <Card>
            <CardHeader>
              <CardTitle>All Bookings - {format(currentMonth, 'MMMM yyyy')}</CardTitle>
            </CardHeader>
            <CardContent>
              {bookingsLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="border rounded-lg p-4 animate-pulse">
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-48"></div>
                          <div className="h-3 bg-gray-200 rounded w-32"></div>
                        </div>
                        <div className="h-6 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : allBookings?.length > 0 ? (
                <div className="space-y-4">
                  {allBookings.map((booking: any) => (
                    <div key={booking.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{booking.guestName || 'No name'}</p>
                          <p className="text-sm text-gray-600">
                            {format(new Date(booking.startDate), 'MMM dd')} - {format(new Date(booking.endDate), 'MMM dd, yyyy')}
                          </p>
                          <p className="text-sm text-gray-500">{booking.property?.title}</p>
                        </div>
                        <div className="text-right">
                          <Badge 
                            variant={booking.status === 'confirmed' ? 'default' : 
                                   booking.status === 'tentative' ? 'secondary' : 'destructive'}
                          >
                            {booking.status}
                          </Badge>
                          <p className="text-sm text-gray-500 mt-1">{booking.guestCount} guests</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No bookings found for this period</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Calendar Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 size={48} className="mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">Analytics dashboard coming soon</p>
                <p className="text-sm text-gray-500 mt-2">
                  Detailed booking analytics, revenue reports, and occupancy trends
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Enhanced Booking Modal */}
      <EnhancedBookingModal
        isOpen={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
        onSubmit={(data) => {
          // Handle booking creation
          console.log('Creating booking:', data);
          setIsBookingModalOpen(false);
        }}
        mode="create"
        propertyTitle={properties.find(p => p.id === selectedProperty)?.title}
      />
    </div>
  );
}