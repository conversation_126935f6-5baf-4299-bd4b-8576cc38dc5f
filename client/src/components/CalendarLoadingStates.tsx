import React from 'react';
import { Calendar, Clock, User, Wifi, WifiOff, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader } from './ui/card';
import { Button } from './ui/button';
import { Alert, AlertDescription } from './ui/alert';
import { cn } from '../lib/utils';

// Skeleton components for different loading states
export function CalendarSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn("animate-pulse", className)}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div className="h-6 bg-gray-200 rounded w-48"></div>
          <div className="flex gap-2">
            <div className="h-6 w-6 bg-gray-200 rounded"></div>
            <div className="h-6 w-24 bg-gray-200 rounded"></div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Calendar grid skeleton */}
        <div className="space-y-4">
          {/* Header toolbar */}
          <div className="flex justify-between items-center mb-4">
            <div className="h-8 w-32 bg-gray-200 rounded"></div>
            <div className="h-8 w-48 bg-gray-200 rounded"></div>
            <div className="h-8 w-32 bg-gray-200 rounded"></div>
          </div>
          
          {/* Calendar grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Day headers */}
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="h-8 bg-gray-200 rounded"></div>
            ))}
            {/* Calendar days */}
            {Array.from({ length: 35 }).map((_, i) => (
              <div key={i} className="h-16 bg-gray-100 rounded border"></div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function BookingModalSkeleton() {
  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
      </div>
      
      {/* Step indicator */}
      <div className="flex items-center justify-center gap-2">
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
        <div className="w-16 h-0.5 bg-gray-200 animate-pulse"></div>
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
      
      {/* Form fields */}
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 w-full bg-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
      
      {/* Action buttons */}
      <div className="flex justify-between pt-4">
        <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

export function PropertyListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-5 w-48 bg-gray-200 rounded"></div>
                <div className="h-4 w-32 bg-gray-200 rounded"></div>
                <div className="h-4 w-24 bg-gray-200 rounded"></div>
              </div>
              <div className="space-y-2">
                <div className="h-6 w-16 bg-gray-200 rounded"></div>
                <div className="h-8 w-20 bg-gray-200 rounded"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export function StatsSkeleton() {
  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="bg-gray-100 p-4 rounded-lg animate-pulse">
          <div className="h-4 w-24 bg-gray-200 rounded mb-2"></div>
          <div className="h-8 w-16 bg-gray-200 rounded"></div>
        </div>
      ))}
    </div>
  );
}

// Error state components
interface ErrorStateProps {
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
  className?: string;
}

export function ErrorState({ 
  title, 
  message, 
  action, 
  icon = <AlertCircle size={48} />,
  className 
}: ErrorStateProps) {
  return (
    <Card className={cn("text-center p-8", className)}>
      <CardContent className="space-y-4">
        <div className="text-red-400 flex justify-center">
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600 mt-1">{message}</p>
        </div>
        {action && (
          <Button onClick={action.onClick} className="mt-4">
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

// Network status component
interface NetworkStatusProps {
  isOnline: boolean;
  isConnected?: boolean;
  onReconnect?: () => void;
}

export function NetworkStatus({ isOnline, isConnected = true, onReconnect }: NetworkStatusProps) {
  if (isOnline && isConnected) {
    return null; // Don't show anything when everything is working
  }

  return (
    <Alert className={cn(
      "mb-4",
      !isOnline ? "border-red-200 bg-red-50" : "border-amber-200 bg-amber-50"
    )}>
      <div className="flex items-center gap-2">
        {!isOnline ? (
          <WifiOff size={16} className="text-red-600" />
        ) : (
          <Wifi size={16} className="text-amber-600" />
        )}
        <AlertDescription className={cn(
          !isOnline ? "text-red-800" : "text-amber-800"
        )}>
          {!isOnline 
            ? "No internet connection. Changes will be saved when connection is restored."
            : "Real-time updates unavailable. Some features may be limited."
          }
          {onReconnect && (
            <Button 
              size="sm" 
              variant="outline" 
              className="ml-2" 
              onClick={onReconnect}
            >
              Retry
            </Button>
          )}
        </AlertDescription>
      </div>
    </Alert>
  );
}

// Loading overlay component
interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
}

export function LoadingOverlay({ isLoading, message = "Loading...", children }: LoadingOverlayProps) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Empty state component
interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export function EmptyState({ 
  icon = <Calendar size={48} />, 
  title, 
  message, 
  action,
  className 
}: EmptyStateProps) {
  return (
    <Card className={cn("text-center p-8", className)}>
      <CardContent className="space-y-4">
        <div className="text-gray-400 flex justify-center">
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600 mt-1">{message}</p>
        </div>
        {action && (
          <Button onClick={action.onClick} className="mt-4">
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

// Inline loading component
export function InlineLoading({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="flex items-center gap-2">
        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-sm text-gray-600">{message}</span>
      </div>
    </div>
  );
}

// Progress bar component
interface ProgressBarProps {
  progress: number; // 0-100
  message?: string;
  showPercentage?: boolean;
}

export function ProgressBar({ progress, message, showPercentage = true }: ProgressBarProps) {
  return (
    <div className="w-full space-y-2">
      {message && <p className="text-sm text-gray-600">{message}</p>}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {showPercentage && (
        <p className="text-xs text-gray-500 text-right">{Math.round(progress)}%</p>
      )}
    </div>
  );
}

// Retry component
interface RetryProps {
  onRetry: () => void;
  isRetrying?: boolean;
  message?: string;
}

export function Retry({ onRetry, isRetrying = false, message = "Something went wrong" }: RetryProps) {
  return (
    <div className="text-center p-4">
      <AlertCircle size={24} className="text-gray-400 mx-auto mb-2" />
      <p className="text-sm text-gray-600 mb-3">{message}</p>
      <Button onClick={onRetry} disabled={isRetrying} size="sm">
        {isRetrying ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            Retrying...
          </>
        ) : (
          'Try Again'
        )}
      </Button>
    </div>
  );
}