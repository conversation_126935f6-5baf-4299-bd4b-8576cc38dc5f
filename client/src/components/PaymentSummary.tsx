import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Info, Calculator, CreditCard } from "lucide-react";

interface PaymentSummaryProps {
  basePrice: number;
  cleaningFee: number;
  bookingType: string;
  gstRate?: number;
  advancePercentage?: number;
}

export default function PaymentSummary({
  basePrice,
  cleaningFee,
  bookingType,
  gstRate = 18,
  advancePercentage = 30
}: PaymentSummaryProps) {
  const subtotal = basePrice + cleaningFee;
  const advanceAmount = Math.round(subtotal * (advancePercentage / 100));
  const gstAmount = Math.round(advanceAmount * (gstRate / 100));
  const totalAdvance = advanceAmount + gstAmount;
  const remainingAmount = subtotal - advanceAmount;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Card className="bg-gradient-to-br from-[#4A6741]/5 to-[#4A6741]/10 border-[#4A6741]/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Calculator className="h-5 w-5 text-[#4A6741]" />
          Payment Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Booking Details */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">
              {bookingType === 'morning' ? '12h Access' : '24h Access'}
            </span>
            <span className="font-medium">{formatCurrency(basePrice)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Cleaning Fee</span>
            <span className="font-medium">{formatCurrency(cleaningFee)}</span>
          </div>
          <Separator />
          <div className="flex justify-between font-medium">
            <span>Subtotal</span>
            <span>{formatCurrency(subtotal)}</span>
          </div>
        </div>

        {/* Payment Breakdown */}
        <div className="bg-white rounded-lg p-4 border border-[#4A6741]/20">
          <div className="flex items-center gap-2 mb-3">
            <CreditCard className="h-4 w-4 text-[#4A6741]" />
            <span className="font-medium text-[#4A6741]">Payment Breakdown</span>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                Advance Payment ({advancePercentage}%)
              </span>
              <span>{formatCurrency(advanceAmount)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">GST ({gstRate}%)</span>
              <span>{formatCurrency(gstAmount)}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-bold text-[#4A6741]">
              <span>Pay Now</span>
              <span>{formatCurrency(totalAdvance)}</span>
            </div>
          </div>
        </div>

        {/* Remaining Amount */}
        <div className="bg-amber-50 rounded-lg p-3 border border-amber-200">
          <div className="flex justify-between text-sm">
            <span className="text-amber-700 font-medium">Pay at Property</span>
            <span className="text-amber-700 font-bold">{formatCurrency(remainingAmount)}</span>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <span className="text-xs text-gray-500">Accepted Payment Methods:</span>
          </div>
          <div className="flex justify-center gap-2 flex-wrap">
            <Badge variant="outline" className="text-xs">UPI</Badge>
            <Badge variant="outline" className="text-xs">Cards</Badge>
            <Badge variant="outline" className="text-xs">Net Banking</Badge>
            <Badge variant="outline" className="text-xs">Wallets</Badge>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-xs text-blue-700 space-y-1">
              <div>• Advance payment secures your booking</div>
              <div>• GST invoice will be provided</div>
              <div>• Remaining amount due at check-in</div>
              <div>• Refund as per cancellation policy</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}