import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format } from 'date-fns';
import { toast } from 'react-hot-toast';

import ConflictResolutionModal from './ConflictResolutionModal';
import { useConflictResolution } from '../../hooks/useConflictResolution';

interface BookingFormData {
  propertyId: number;
  guestName: string;
  guestPhone: string;
  guestCount: number;
  startDate: Date | null;
  endDate: Date | null;
  bookingType: 'Morning Visit' | 'Full Day' | 'Extended Stay';
  totalAmount: number;
  notes: string;
}

interface Property {
  id: number;
  title: string;
  location: string;
}

interface CalendarWithConflictResolutionProps {
  properties: Property[];
  onBookingCreate: (booking: BookingFormData) => Promise<void>;
}

const CalendarWithConflictResolution: React.FC<CalendarWithConflictResolutionProps> = ({
  properties,
  onBookingCreate
}) => {
  const [bookingDialogOpen, setBookingDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<BookingFormData>({
    propertyId: 0,
    guestName: '',
    guestPhone: '',
    guestCount: 2,
    startDate: null,
    endDate: null,
    bookingType: 'Full Day',
    totalAmount: 0,
    notes: ''
  });

  const {
    conflicts,
    loading: conflictsLoading,
    modalOpen: conflictModalOpen,
    resolveConflict,
    autoResolveAll,
    closeModal: closeConflictModal,
    checkForConflictsBeforeBooking,
    hasConflicts,
    criticalConflicts
  } = useConflictResolution();

  const handleCreateBooking = useCallback(async () => {
    if (!formData.propertyId || !formData.startDate || !formData.endDate || !formData.guestName.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      // Check for conflicts before creating booking
      const hasNoConflicts = await checkForConflictsBeforeBooking({
        propertyId: formData.propertyId,
        startDate: format(formData.startDate, 'yyyy-MM-dd'),
        endDate: format(formData.endDate, 'yyyy-MM-dd')
      });

      if (hasNoConflicts) {
        // No conflicts, proceed with booking
        await onBookingCreate(formData);
        toast.success('Booking created successfully');
        setBookingDialogOpen(false);
        resetForm();
      }
      // If conflicts detected, modal will automatically open from the hook
    } catch (error) {
      console.error('Error creating booking:', error);
      toast.error('Failed to create booking');
    } finally {
      setSubmitting(false);
    }
  }, [formData, checkForConflictsBeforeBooking, onBookingCreate]);

  const resetForm = () => {
    setFormData({
      propertyId: 0,
      guestName: '',
      guestPhone: '',
      guestCount: 2,
      startDate: null,
      endDate: null,
      bookingType: 'Full Day',
      totalAmount: 0,
      notes: ''
    });
  };

  const handleFormChange = (field: keyof BookingFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleConflictResolved = async () => {
    // After conflicts are resolved, proceed with booking creation
    try {
      await onBookingCreate(formData);
      toast.success('Booking created successfully after resolving conflicts');
      setBookingDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error creating booking after conflict resolution:', error);
      toast.error('Failed to create booking');
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        {/* Calendar View would go here */}
        <Button 
          variant="contained" 
          onClick={() => setBookingDialogOpen(true)}
          sx={{ mb: 2 }}
        >
          New Booking
        </Button>

        {/* Conflict Status Indicator */}
        {hasConflicts && (
          <Alert 
            severity={criticalConflicts > 0 ? 'error' : 'warning'} 
            sx={{ mb: 2 }}
            action={
              <Button color="inherit" size="small" onClick={() => closeConflictModal()}>
                View Conflicts
              </Button>
            }
          >
            {criticalConflicts > 0 
              ? `${criticalConflicts} critical booking conflicts need attention`
              : `${conflicts.length} booking conflicts detected`
            }
          </Alert>
        )}

        {/* Booking Creation Dialog */}
        <Dialog 
          open={bookingDialogOpen} 
          onClose={() => setBookingDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Create New Booking</DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" gap={3} pt={1}>
              {/* Property Selection */}
              <TextField
                label="Property"
                select
                value={formData.propertyId}
                onChange={(e) => handleFormChange('propertyId', parseInt(e.target.value))}
                required
                fullWidth
              >
                <MenuItem value={0}>Select a property</MenuItem>
                {properties.map((property) => (
                  <MenuItem key={property.id} value={property.id}>
                    {property.title} - {property.location}
                  </MenuItem>
                ))}
              </TextField>

              {/* Guest Information */}
              <Box display="flex" gap={2}>
                <TextField
                  label="Guest Name"
                  value={formData.guestName}
                  onChange={(e) => handleFormChange('guestName', e.target.value)}
                  required
                  fullWidth
                />
                <TextField
                  label="Phone"
                  value={formData.guestPhone}
                  onChange={(e) => handleFormChange('guestPhone', e.target.value)}
                  fullWidth
                />
              </Box>

              {/* Date Selection */}
              <Box display="flex" gap={2}>
                <DatePicker
                  label="Check-in Date"
                  value={formData.startDate}
                  onChange={(date) => handleFormChange('startDate', date)}
                  slotProps={{
                    textField: { fullWidth: true, required: true }
                  }}
                />
                <DatePicker
                  label="Check-out Date"
                  value={formData.endDate}
                  onChange={(date) => handleFormChange('endDate', date)}
                  minDate={formData.startDate || undefined}
                  slotProps={{
                    textField: { fullWidth: true, required: true }
                  }}
                />
              </Box>

              {/* Booking Details */}
              <Box display="flex" gap={2}>
                <TextField
                  label="Booking Type"
                  select
                  value={formData.bookingType}
                  onChange={(e) => handleFormChange('bookingType', e.target.value)}
                  fullWidth
                >
                  <MenuItem value="Morning Visit">Morning Visit</MenuItem>
                  <MenuItem value="Full Day">Full Day</MenuItem>
                  <MenuItem value="Extended Stay">Extended Stay</MenuItem>
                </TextField>
                <TextField
                  label="Guest Count"
                  type="number"
                  value={formData.guestCount}
                  onChange={(e) => handleFormChange('guestCount', parseInt(e.target.value))}
                  inputProps={{ min: 1 }}
                  fullWidth
                />
              </Box>

              <TextField
                label="Amount ($)"
                type="number"
                value={formData.totalAmount}
                onChange={(e) => handleFormChange('totalAmount', parseFloat(e.target.value) || 0)}
                inputProps={{ min: 0, step: 0.01 }}
                fullWidth
              />

              <TextField
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => handleFormChange('notes', e.target.value)}
                fullWidth
              />

              {/* Conflict Check Status */}
              {conflictsLoading && (
                <Alert 
                  severity="info" 
                  icon={<CircularProgress size={16} />}
                >
                  Checking for booking conflicts...
                </Alert>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={() => setBookingDialogOpen(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button 
              variant="contained"
              onClick={handleCreateBooking}
              disabled={submitting || conflictsLoading}
              startIcon={submitting ? <CircularProgress size={16} /> : undefined}
            >
              {submitting ? 'Creating...' : 'Create Booking'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Conflict Resolution Modal */}
        <ConflictResolutionModal
          open={conflictModalOpen}
          onClose={closeConflictModal}
          conflicts={conflicts}
          onResolveConflict={resolveConflict}
          onAutoResolve={autoResolveAll}
          loading={conflictsLoading}
        />
      </Box>
    </LocalizationProvider>
  );
};

export default CalendarWithConflictResolution;