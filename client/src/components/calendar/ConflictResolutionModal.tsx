import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>T<PERSON>le, 
  <PERSON>alogContent, 
  <PERSON>alog<PERSON><PERSON>, 
  <PERSON>ton, 
  <PERSON>po<PERSON>, 
  Box, 
  Alert, 
  Chip, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper, 
  FormControl, 
  Select, 
  MenuItem, 
  InputLabel,
  CircularProgress,
  Divider,
  IconButton
} from '@mui/material';
import {
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  AutoFixHigh as AutoFixIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { format, parseISO } from 'date-fns';

interface BookingConflict {
  id: string;
  type: 'overlap' | 'double_booking' | 'maintenance_conflict' | 'blocked_dates';
  severity: 'critical' | 'high' | 'medium' | 'low';
  property: {
    id: number;
    name: string;
  };
  conflictingBookings: Array<{
    id: number;
    guestName: string;
    startDate: string;
    endDate: string;
    source: string;
    amount?: number;
  }>;
  suggestedResolution: {
    action: 'cancel_new' | 'cancel_existing' | 'modify_dates' | 'split_booking' | 'manual_review';
    description: string;
    impact: string;
  };
  newBooking?: {
    guestName: string;
    startDate: string;
    endDate: string;
    source: string;
    amount?: number;
  };
}

interface ConflictResolutionModalProps {
  open: boolean;
  onClose: () => void;
  conflicts: BookingConflict[];
  onResolveConflict: (conflictId: string, resolution: ResolutionAction) => Promise<void>;
  onAutoResolve: () => Promise<void>;
  loading?: boolean;
}

interface ResolutionAction {
  action: 'cancel_new' | 'cancel_existing' | 'modify_dates' | 'split_booking' | 'manual_review';
  bookingId?: number;
  newDates?: {
    startDate: string;
    endDate: string;
  };
  notes?: string;
}

const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
  open,
  onClose,
  conflicts,
  onResolveConflict,
  onAutoResolve,
  loading = false
}) => {
  const [selectedResolutions, setSelectedResolutions] = useState<Record<string, ResolutionAction>>({});
  const [resolving, setResolving] = useState<string | null>(null);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getConflictTypeLabel = (type: string) => {
    switch (type) {
      case 'overlap': return 'Date Overlap';
      case 'double_booking': return 'Double Booking';
      case 'maintenance_conflict': return 'Maintenance Conflict';
      case 'blocked_dates': return 'Blocked Dates';
      default: return 'Unknown Conflict';
    }
  };

  const getActionLabel = (action: string) => {
    switch (action) {
      case 'cancel_new': return 'Cancel New Booking';
      case 'cancel_existing': return 'Cancel Existing Booking';
      case 'modify_dates': return 'Modify Dates';
      case 'split_booking': return 'Split Booking';
      case 'manual_review': return 'Manual Review';
      default: return 'Unknown Action';
    }
  };

  const handleResolutionChange = (conflictId: string, action: ResolutionAction) => {
    setSelectedResolutions(prev => ({
      ...prev,
      [conflictId]: action
    }));
  };

  const handleResolveConflict = async (conflictId: string) => {
    const resolution = selectedResolutions[conflictId];
    if (!resolution) return;

    setResolving(conflictId);
    try {
      await onResolveConflict(conflictId, resolution);
    } finally {
      setResolving(null);
    }
  };

  const handleAutoResolveAll = async () => {
    setResolving('auto');
    try {
      await onAutoResolve();
    } finally {
      setResolving(null);
    }
  };

  const criticalConflicts = conflicts.filter(c => c.severity === 'critical').length;
  const totalConflicts = conflicts.length;

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" gap={2}>
          <WarningIcon color="warning" />
          <Typography variant="h6">
            Calendar Conflicts Detected
          </Typography>
          <Chip 
            label={`${totalConflicts} conflicts`}
            color={criticalConflicts > 0 ? 'error' : 'warning'}
            size="small"
          />
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Analyzing conflicts...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Summary Alert */}
            <Alert 
              severity={criticalConflicts > 0 ? 'error' : 'warning'} 
              sx={{ mb: 3 }}
              action={
                <Button
                  color="inherit"
                  size="small"
                  onClick={handleAutoResolveAll}
                  disabled={resolving === 'auto'}
                  startIcon={resolving === 'auto' ? <CircularProgress size={16} /> : <AutoFixIcon />}
                >
                  Auto Resolve All
                </Button>
              }
            >
              <Typography variant="subtitle2">
                {criticalConflicts > 0 
                  ? `${criticalConflicts} critical conflicts require immediate attention`
                  : `${totalConflicts} conflicts detected and can be automatically resolved`
                }
              </Typography>
            </Alert>

            {/* Conflicts List */}
            <Box sx={{ maxHeight: '60vh', overflow: 'auto' }}>
              {conflicts.map((conflict, index) => (
                <Paper key={conflict.id} sx={{ mb: 2, p: 2 }} variant="outlined">
                  <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                    <Box flex={1}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Chip 
                          label={getConflictTypeLabel(conflict.type)}
                          color={getSeverityColor(conflict.severity) as any}
                          size="small"
                        />
                        <Chip 
                          label={conflict.severity.toUpperCase()}
                          variant="outlined"
                          size="small"
                        />
                        <Typography variant="body2" color="text.secondary">
                          {conflict.property.name}
                        </Typography>
                      </Box>

                      {/* Conflicting Bookings Table */}
                      <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Guest</TableCell>
                              <TableCell>Dates</TableCell>
                              <TableCell>Source</TableCell>
                              <TableCell>Amount</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {conflict.newBooking && (
                              <TableRow sx={{ backgroundColor: 'action.hover' }}>
                                <TableCell>
                                  <Box display="flex" alignItems="center" gap={1}>
                                    {conflict.newBooking.guestName}
                                    <Chip label="NEW" color="primary" size="small" />
                                  </Box>
                                </TableCell>
                                <TableCell>
                                  {format(parseISO(conflict.newBooking.startDate), 'MMM dd')} - {format(parseISO(conflict.newBooking.endDate), 'MMM dd')}
                                </TableCell>
                                <TableCell>{conflict.newBooking.source}</TableCell>
                                <TableCell>
                                  {conflict.newBooking.amount ? `$${conflict.newBooking.amount}` : '-'}
                                </TableCell>
                              </TableRow>
                            )}
                            {conflict.conflictingBookings.map((booking) => (
                              <TableRow key={booking.id}>
                                <TableCell>{booking.guestName}</TableCell>
                                <TableCell>
                                  {format(parseISO(booking.startDate), 'MMM dd')} - {format(parseISO(booking.endDate), 'MMM dd')}
                                </TableCell>
                                <TableCell>{booking.source}</TableCell>
                                <TableCell>
                                  {booking.amount ? `$${booking.amount}` : '-'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>

                      {/* Suggested Resolution */}
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Suggested Resolution
                        </Typography>
                        <Typography variant="body2">
                          <strong>{getActionLabel(conflict.suggestedResolution.action)}:</strong> {conflict.suggestedResolution.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Impact: {conflict.suggestedResolution.impact}
                        </Typography>
                      </Alert>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  {/* Resolution Actions */}
                  <Box display="flex" justifyContent="between" alignItems="center" gap={2}>
                    <FormControl size="small" sx={{ minWidth: 200 }}>
                      <InputLabel>Choose Resolution</InputLabel>
                      <Select
                        value={selectedResolutions[conflict.id]?.action || conflict.suggestedResolution.action}
                        onChange={(e) => handleResolutionChange(conflict.id, {
                          action: e.target.value as any
                        })}
                        label="Choose Resolution"
                      >
                        <MenuItem value="cancel_new">Cancel New Booking</MenuItem>
                        <MenuItem value="cancel_existing">Cancel Existing Booking</MenuItem>
                        <MenuItem value="modify_dates">Modify Dates</MenuItem>
                        <MenuItem value="split_booking">Split Booking</MenuItem>
                        <MenuItem value="manual_review">Manual Review</MenuItem>
                      </Select>
                    </FormControl>

                    <Box display="flex" gap={1}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleResolveConflict(conflict.id)}
                        disabled={resolving === conflict.id}
                        startIcon={resolving === conflict.id ? <CircularProgress size={16} /> : <CheckIcon />}
                      >
                        {resolving === conflict.id ? 'Resolving...' : 'Resolve'}
                      </Button>
                    </Box>
                  </Box>
                </Paper>
              ))}
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={!!resolving}>
          Close
        </Button>
        <Button
          variant="contained"
          onClick={handleAutoResolveAll}
          disabled={resolving === 'auto' || conflicts.length === 0}
          startIcon={resolving === 'auto' ? <CircularProgress size={16} /> : <AutoFixIcon />}
        >
          {resolving === 'auto' ? 'Auto Resolving...' : 'Auto Resolve All'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConflictResolutionModal;