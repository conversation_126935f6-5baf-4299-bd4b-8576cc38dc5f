import React, { useMemo } from 'react';
import { PropertyWithBookings } from '@/features/dashboard/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  AlertCircle,
  CheckCircle,
  Calendar,
  Building2
} from 'lucide-react';

interface PropertyPricingComparisonProps {
  properties: PropertyWithBookings[];
}

interface PricingAnalysis {
  property: PropertyWithBookings;
  baseHalfDay: number;
  baseFullDay: number;
  weekendHalfDay?: number;
  weekendFullDay?: number;
  hasWeekendPremium: boolean;
  weekendPremiumPercent?: number | undefined;
  competitivePosition: 'low' | 'medium' | 'high';
  revenueOptimization: string[];
}

export function PropertyPricingComparison({ properties }: PropertyPricingComparisonProps) {
  const pricingAnalysis = useMemo((): PricingAnalysis[] => {
    if (!properties?.length) return [];

    // Calculate market averages
    const validHalfDayPrices = properties.filter(p => p.halfDayPrice).map(p => p.halfDayPrice);
    const validFullDayPrices = properties.filter(p => p.fullDayPrice).map(p => p.fullDayPrice);
    
    const avgHalfDay = validHalfDayPrices.length > 0 
      ? validHalfDayPrices.reduce((sum, price) => sum + price, 0) / validHalfDayPrices.length
      : 0;
    
    const avgFullDay = validFullDayPrices.length > 0 
      ? validFullDayPrices.reduce((sum, price) => sum + price, 0) / validFullDayPrices.length
      : 0;

    return properties.map(property => {
      const baseHalfDay = property.halfDayPrice || 0;
      const baseFullDay = property.fullDayPrice || 0;
      const weekendHalfDay = property.weekendHalfDayPrice || 0;
      const weekendFullDay = property.weekendFullDayPrice || 0;
      
      const hasWeekendPremium = !!(weekendHalfDay || weekendFullDay);
      
      // Calculate weekend premium percentage
      let weekendPremiumPercent: number | undefined;
      if (hasWeekendPremium) {
        const weekendAvg = ((weekendHalfDay || baseHalfDay) + (weekendFullDay || baseFullDay)) / 2;
        const baseAvg = (baseHalfDay + baseFullDay) / 2;
        weekendPremiumPercent = Math.round(((weekendAvg - baseAvg) / baseAvg) * 100);
      }

      // Determine competitive position
      let competitivePosition: 'low' | 'medium' | 'high' = 'medium';
      const avgPrice = (baseHalfDay + baseFullDay) / 2;
      const marketAvg = (avgHalfDay + avgFullDay) / 2;
      
      if (avgPrice < marketAvg * 0.8) competitivePosition = 'low';
      else if (avgPrice > marketAvg * 1.2) competitivePosition = 'high';

      // Generate revenue optimization suggestions
      const revenueOptimization: string[] = [];
      
      if (!hasWeekendPremium) {
        revenueOptimization.push('Add weekend premium pricing (15-30% increase)');
      }
      
      if (competitivePosition === 'low') {
        revenueOptimization.push('Consider increasing rates to match market average');
      }
      
      if (!property.weekdayHalfDayPrice && !property.weekdayFullDayPrice) {
        revenueOptimization.push('Set dedicated weekday rates for better pricing control');
      }
      
      if (baseFullDay < baseHalfDay * 1.5) {
        revenueOptimization.push('Increase full-day rate to encourage longer stays');
      }

      return {
        property,
        baseHalfDay,
        baseFullDay,
        weekendHalfDay,
        weekendFullDay,
        hasWeekendPremium,
        weekendPremiumPercent,
        competitivePosition,
        revenueOptimization
      };
    });
  }, [properties]);

  const formatCurrency = (value: number) => {
    return `₹${value.toLocaleString('en-IN')}`;
  };

  const getCompetitiveColor = (position: 'low' | 'medium' | 'high') => {
    switch (position) {
      case 'low': return 'bg-red-100 text-red-700';
      case 'medium': return 'bg-blue-100 text-blue-700';
      case 'high': return 'bg-green-100 text-green-700';
    }
  };

  const getCompetitiveIcon = (position: 'low' | 'medium' | 'high') => {
    switch (position) {
      case 'low': return <TrendingDown className="h-4 w-4" />;
      case 'medium': return <BarChart3 className="h-4 w-4" />;
      case 'high': return <TrendingUp className="h-4 w-4" />;
    }
  };

  if (!properties?.length) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No properties available for pricing comparison.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Property Pricing Comparison & Optimization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pricingAnalysis.map((analysis) => (
              <div 
                key={analysis.property.id}
                className="border rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-gray-900">{analysis.property.title}</h3>
                    <p className="text-sm text-gray-600">{analysis.property.location}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getCompetitiveColor(analysis.competitivePosition)}>
                      {getCompetitiveIcon(analysis.competitivePosition)}
                      <span className="ml-1 capitalize">{analysis.competitivePosition} Pricing</span>
                    </Badge>
                    {analysis.hasWeekendPremium ? (
                      <Badge className="bg-purple-100 text-purple-700">
                        <Calendar className="h-3 w-3 mr-1" />
                        Weekend Premium
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-gray-600">
                        Base Pricing Only
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Current Pricing Display */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-600 uppercase tracking-wide">Half-Day</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(analysis.baseHalfDay)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-600 uppercase tracking-wide">Full-Day</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(analysis.baseFullDay)}
                    </p>
                  </div>
                  {analysis.weekendHalfDay && (
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <p className="text-xs text-purple-600 uppercase tracking-wide">Weekend 12h</p>
                      <p className="text-lg font-semibold text-purple-700">
                        {formatCurrency(analysis.weekendHalfDay)}
                      </p>
                    </div>
                  )}
                  {analysis.weekendFullDay && (
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <p className="text-xs text-purple-600 uppercase tracking-wide">Weekend 24h</p>
                      <p className="text-lg font-semibold text-purple-700">
                        {formatCurrency(analysis.weekendFullDay)}
                      </p>
                    </div>
                  )}
                </div>

                {/* Weekend Premium Analysis */}
                {analysis.hasWeekendPremium && analysis.weekendPremiumPercent !== undefined && (
                  <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-800">
                        Weekend premium: +{analysis.weekendPremiumPercent}%
                      </span>
                      {analysis.weekendPremiumPercent >= 15 && analysis.weekendPremiumPercent <= 30 ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-amber-600" />
                      )}
                    </div>
                  </div>
                )}

                {/* Revenue Optimization Suggestions */}
                {analysis.revenueOptimization.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      Revenue Optimization Opportunities
                    </h4>
                    <div className="space-y-2">
                      {analysis.revenueOptimization.map((suggestion, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
                          <p className="text-sm text-gray-700">{suggestion}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Market Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <BarChart3 className="h-4 w-4" />
            Market Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Properties with Weekend Pricing</p>
              <p className="text-xl font-semibold text-purple-600">
                {pricingAnalysis.filter(p => p.hasWeekendPremium).length}/{pricingAnalysis.length}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Avg Market Half-Day</p>
              <p className="text-xl font-semibold text-blue-600">
                {formatCurrency(Math.round(pricingAnalysis.reduce((sum, p) => sum + p.baseHalfDay, 0) / pricingAnalysis.length))}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Avg Market Full-Day</p>
              <p className="text-xl font-semibold text-blue-600">
                {formatCurrency(Math.round(pricingAnalysis.reduce((sum, p) => sum + p.baseFullDay, 0) / pricingAnalysis.length))}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Revenue Potential</p>
              <p className="text-xl font-semibold text-green-600">
                {pricingAnalysis.filter(p => p.revenueOptimization.length > 0).length > 0 ? '+High' : 'Optimized'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PropertyPricingComparison;