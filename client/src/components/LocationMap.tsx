import { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

interface LocationMapProps {
  location: string;
  latitude?: number;
  longitude?: number;
  className?: string;
}

export default function LocationMap({ location, latitude, longitude, className = "h-64 w-full" }: LocationMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  
  useEffect(() => {
    if (!mapRef.current) return;
    
    // Clean up existing map instance if it exists
    if (mapInstanceRef.current) {
      mapInstanceRef.current.remove();
      mapInstanceRef.current = null;
    }
    
    // Add a small delay to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      if (!mapRef.current) return;
    
      // Use actual coordinates if provided, otherwise fall back to hardcoded mapping
      const getCoordinates = (): [number, number] => {
        // Use actual property coordinates if available
        if (latitude !== undefined && longitude !== undefined) {
          return [latitude, longitude];
        }
        
        // Fallback to hardcoded coordinates based on location names
        const locationMap: Record<string, [number, number]> = {
          'Hyderabad': [17.3850, 78.4867],
          'Gandipet': [17.3801, 78.3513],
          'Kompally': [17.5054, 78.4800],
          'Shamirpet': [17.5789, 78.5686],
          'Moinabad': [17.2988, 78.2464],
          'Medchal': [17.6297, 78.4845],
          'Shamshabad': [17.2431, 78.4169],
          'Secunderabad': [17.4399, 78.4983],
          'Gachibowli': [17.4406, 78.3752],
          'HITEC City': [17.4503, 78.3809],
          'Miyapur': [17.4939, 78.3511],
          'Kukatpally': [17.4950, 78.4110],
          'Madhapur': [17.4479, 78.3916],
          'Kondapur': [17.4605, 78.3781],
          'Uppal': [17.4015, 78.5583],
          'Pocharam': [17.2536, 78.5689],
          'Tellapur': [17.4711, 78.3025],
        };
        
        // Check if the exact location is in our map
        for (const [key, coords] of Object.entries(locationMap)) {
          if (location.includes(key)) {
            return coords;
          }
        }
        
        // Default to Hyderabad coordinates if location not found
        return locationMap['Hyderabad'];
      };
      
      // Get coordinates for the location
      const coords = getCoordinates();
      
      try {
        // Initialize the map with correct coordinates
        const map = L.map(mapRef.current).setView(coords, 13);
        mapInstanceRef.current = map;
        
        // Add the OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Add a marker for the location
        const marker = L.marker(coords).addTo(map);
        marker.bindPopup(location).openPopup();
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    }, 100);
    
    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
        } catch (error) {
          console.error('Error cleaning up map:', error);
        }
        mapInstanceRef.current = null;
      }
    };
  }, [location, latitude, longitude]);
  
  return <div ref={mapRef} className={className} />;
}