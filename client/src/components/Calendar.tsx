import { useState, useEffect } from "react";
import { format, addDays, isSameDay, isBefore, startOfMonth, endOfMonth, eachDayOfInterval, getDay } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface CalendarProps {
  selectedDate: Date | null;
  onSelectDate: (date: Date) => void;
  bookings: { date: string; type: "morning" | "full_day"; status?: string }[];
  minDate?: Date;
}

export default function Calendar({ selectedDate, onSelectDate, bookings, minDate = new Date() }: CalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState<Date[]>([]);

  // Generate calendar days for the current month
  useEffect(() => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    
    // Add days from previous month to start from the beginning of the week
    const firstDayOfMonth = getDay(start);
    const prevMonthDays = Array.from({ length: firstDayOfMonth }, (_, i) => 
      addDays(start, -firstDayOfMonth + i)
    );
    
    // Days of the current month
    const daysInMonth = eachDayOfInterval({ start, end });
    
    // Add days from next month to complete the week
    const lastDayOfMonth = getDay(end);
    const nextMonthDays = Array.from({ length: 6 - lastDayOfMonth }, (_, i) => 
      addDays(end, i + 1)
    );
    
    setCalendarDays([...prevMonthDays, ...daysInMonth, ...nextMonthDays]);
  }, [currentMonth]);

  // Handler for month navigation
  const changeMonth = (increment: number) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + increment);
    setCurrentMonth(newMonth);
  };

  // Check if a date has bookings
  const getDateStatus = (date: Date) => {
    const formattedDate = format(date, "yyyy-MM-dd");
    const booking = bookings.find(b => {
      const bookingDate = new Date(b.date);
      return isSameDay(bookingDate, date);
    });

    if (!booking) return "available";
    return booking.type;
  };

  // Check if a date is disabled (in the past, fully booked, or has confirmed bookings)
  const isDateDisabled = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (isBefore(date, minDate) && !isSameDay(date, minDate)) return true;
    
    const booking = bookings.find(b => {
      const bookingDate = new Date(b.date);
      return isSameDay(bookingDate, date) && (
        b.type === "full_day" || 
        b.status === "confirmed" // Hide dates with confirmed bookings
      );
    });
    
    return !!booking;
  };

  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  return (
    <div className="p-4 bg-white rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="font-semibold text-[#2D3C2D]">{format(currentMonth, "MMMM yyyy")}</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => changeMonth(-1)}
            className="p-1 hover:bg-[#F7F4F1] rounded-full"
          >
            <ChevronLeft className="h-5 w-5 text-[#4A6741]" />
          </button>
          <button
            onClick={() => changeMonth(1)}
            className="p-1 hover:bg-[#F7F4F1] rounded-full"
          >
            <ChevronRight className="h-5 w-5 text-[#4A6741]" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-1 mb-1">
        {days.map((day) => (
          <div key={day} className="text-center text-sm font-medium text-[#605045]">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((date, i) => {
          const dateStatus = getDateStatus(date);
          const disabled = isDateDisabled(date);
          const isSelected = selectedDate && isSameDay(date, selectedDate);
          const isOutsideCurrentMonth = date.getMonth() !== currentMonth.getMonth();
          
          return (
            <button
              key={i}
              className={cn(
                "calendar-day w-[40px] h-[40px] flex items-center justify-center rounded-md text-sm transition-all",
                isOutsideCurrentMonth && "text-[#B4A89E] opacity-50",
                disabled ? "calendar-day-disabled bg-[#EBE6E1] text-[#D5CCC4] cursor-not-allowed" : 
                isSelected ? "calendar-day-selected bg-[#4A6741] text-white border border-[#4A6741]" :
                dateStatus === "morning" ? "calendar-day-morning bg-gradient-to-br from-[#F7F4F1] to-[#D9B382] border border-[#D5CCC4]" :
                dateStatus === "full_day" ? "bg-[#D9B382] border border-[#D5CCC4] text-white" :
                "calendar-day-available bg-[#F7F4F1] border border-[#D5CCC4] hover:bg-[#EBE6E1]"
              )}
              onClick={(e) => {
                e.preventDefault();
                if (!disabled) {
                  console.log("Calendar: Date selected:", date);
                  onSelectDate(date);
                }
              }}
              disabled={disabled}
            >
              {format(date, "d")}
            </button>
          );
        })}
      </div>

      <div className="mt-4 flex space-x-4 text-xs text-[#605045]">
        <div className="flex items-center">
          <div className="w-4 h-4 rounded-sm mr-2 bg-[#F7F4F1] border border-[#D5CCC4]"></div>
          <span>Available</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 rounded-sm mr-2 bg-gradient-to-br from-[#F7F4F1] to-[#D9B382] border border-[#D5CCC4]"></div>
          <span>12h access booked</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 rounded-sm mr-2 bg-[#EBE6E1] border border-[#D5CCC4]"></div>
          <span>Not available</span>
        </div>
      </div>
    </div>
  );
}
