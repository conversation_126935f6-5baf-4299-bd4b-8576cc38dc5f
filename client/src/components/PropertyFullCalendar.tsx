import React, { useState, useCallback, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import timeGridPlugin from '@fullcalendar/timegrid';
import { EventClickArg, EventContentArg, DateSelectArg, EventDropArg, EventResizeArg } from '@fullcalendar/core';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2, Calendar, Clock, User, Wifi, WifiOff, RotateCcw, XCircle } from 'lucide-react';
import { Button } from './ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { useToast } from '../hooks/use-toast';
import { useWebSocket } from '../contexts/WebSocketContext';
import { cn } from '../lib/utils';

interface Booking {
  id: string;
  property_id: string;
  start_date: string;
  end_date: string;
  guest_name?: string;
  guest_phone?: string;
  guest_count?: number;
  status: 'confirmed' | 'tentative' | 'blocked' | 'cancelled';
  booking_type: 'direct' | 'online' | 'whatsapp';
  notes?: string;
  source: string;
  created_at: string;
  updated_at: string;
}

interface PropertyFullCalendarProps {
  propertyId: string;
  className?: string;
}

interface BookingFormData {
  guestName: string;
  guestPhone: string;
  guestCount: number;
  notes: string;
  status: 'confirmed' | 'tentative' | 'blocked' | 'cancelled';
  bookingType: 'direct' | 'online' | 'whatsapp';
}

export function PropertyFullCalendar({ propertyId, className }: PropertyFullCalendarProps) {
  const [selectedDates, setSelectedDates] = useState<DateSelectArg | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragConflict, setDragConflict] = useState<string | null>(null);
  const [formData, setFormData] = useState<BookingFormData>({
    guestName: '',
    guestPhone: '',
    guestCount: 1,
    notes: '',
    status: 'confirmed',
    bookingType: 'direct'
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isConnected, connectionStatus } = useWebSocket();

  // Listen for WebSocket calendar updates
  useEffect(() => {
    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const message = JSON.parse(event.data);
        
        if (message.type === 'calendar_update' && 
            message.data.propertyId === parseInt(propertyId)) {
          
          // Invalidate and refetch calendar data
          queryClient.invalidateQueries({ queryKey: ['calendar-bookings', propertyId] });
          
          // Show notification based on action
          const booking = message.data.booking;
          if (booking.deleted) {
            toast({
              title: 'Booking Deleted',
              description: `Booking for ${booking.guest_name || 'Guest'} was removed`,
              variant: 'default'
            });
          } else {
            toast({
              title: 'Calendar Updated',
              description: `Booking for ${booking.guestName || booking.guest_name || 'Guest'} was updated`,
              variant: 'default'
            });
          }
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    // Add event listener for WebSocket messages (if available through context)
    // Note: In a real implementation, this would be handled through the WebSocket context
    // For now, we'll rely on the context's built-in query invalidation

    return () => {
      // Cleanup if needed
    };
  }, [propertyId, queryClient, toast]);

  // Fetch calendar bookings
  const { data: bookings = [], isLoading } = useQuery({
    queryKey: ['calendar-bookings', propertyId],
    queryFn: async () => {
      const response = await fetch(`/api/calendar/${propertyId}`);
      if (!response.ok) throw new Error('Failed to fetch bookings');
      const data = await response.json();
      return data.bookings || [];
    }
  });

  // Create booking mutation
  const createBookingMutation = useMutation({
    mutationFn: async (bookingData: any) => {
      const response = await fetch(`/api/calendar/${propertyId}/bookings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bookingData)
      });
      if (!response.ok) throw new Error('Failed to create booking');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-bookings', propertyId] });
      toast({ title: 'Booking created successfully' });
      setIsAddDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create booking',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  // Update booking mutation
  const updateBookingMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      const response = await fetch(`/api/calendar/bookings/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to update booking');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-bookings', propertyId] });
      toast({ title: 'Booking updated successfully' });
      setIsEditDialogOpen(false);
      setSelectedBooking(null);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to update booking',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  // Delete booking mutation
  const deleteBookingMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/calendar/bookings/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete booking');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-bookings', propertyId] });
      toast({ title: 'Booking deleted successfully' });
      setIsEditDialogOpen(false);
      setSelectedBooking(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to delete booking',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  // Transform bookings to FullCalendar events
  const events = bookings.map((booking: Booking) => ({
    id: booking.id,
    start: booking.start_date,
    end: booking.end_date,
    title: booking.guest_name || 'Booking',
    backgroundColor: getEventColor(booking.status),
    borderColor: getEventColor(booking.status),
    extendedProps: {
      ...booking
    }
  }));

  function getEventColor(status: string) {
    switch (status) {
      case 'confirmed': return '#22c55e';
      case 'tentative': return '#f59e0b';
      case 'blocked': return '#ef4444';
      case 'cancelled': return '#6b7280';
      default: return '#3b82f6';
    }
  }

  const resetForm = () => {
    setFormData({
      guestName: '',
      guestPhone: '',
      guestCount: 1,
      notes: '',
      status: 'confirmed',
      bookingType: 'direct'
    });
    setSelectedDates(null);
  };

  const handleDateSelect = useCallback((selectInfo: DateSelectArg) => {
    setSelectedDates(selectInfo);
    setIsAddDialogOpen(true);
  }, []);

  const handleEventClick = useCallback((clickInfo: EventClickArg) => {
    const booking = clickInfo.event.extendedProps as Booking;
    setSelectedBooking(booking);
    setFormData({
      guestName: booking.guest_name || '',
      guestPhone: booking.guest_phone || '',
      guestCount: booking.guest_count || 1,
      notes: booking.notes || '',
      status: booking.status,
      bookingType: booking.booking_type
    });
    setIsEditDialogOpen(true);
  }, []);

  const handleCreateBooking = () => {
    if (!selectedDates) return;

    createBookingMutation.mutate({
      startDate: selectedDates.startStr,
      endDate: selectedDates.endStr,
      guestName: formData.guestName,
      guestPhone: formData.guestPhone,
      guestCount: formData.guestCount,
      notes: formData.notes,
      status: formData.status,
      bookingType: formData.bookingType,
      source: 'dashboard'
    });
  };

  const handleUpdateBooking = () => {
    if (!selectedBooking) return;

    updateBookingMutation.mutate({
      id: selectedBooking.id,
      guestName: formData.guestName,
      guestPhone: formData.guestPhone,
      guestCount: formData.guestCount,
      notes: formData.notes,
      status: formData.status,
      bookingType: formData.bookingType
    });
  };

  const handleDeleteBooking = () => {
    if (!selectedBooking) return;
    deleteBookingMutation.mutate(selectedBooking.id);
  };

  // Drag and drop handlers
  const handleEventDrop = useCallback(async (dropInfo: EventDropArg) => {
    setIsDragging(true);
    setDragConflict(null);
    
    try {
      const booking = dropInfo.event.extendedProps as Booking;
      const newStart = dropInfo.event.startStr;
      const newEnd = dropInfo.event.endStr;
      
      // Check for conflicts before updating
      const response = await fetch(`/api/calendar/availability`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: parseInt(propertyId),
          startDate: newStart,
          endDate: newEnd,
          excludeBookingId: booking.id
        }),
      });
      
      const availabilityData = await response.json();
      
      if (!availabilityData.isAvailable) {
        // Revert the drop
        dropInfo.revert();
        setDragConflict(availabilityData.message || 'Booking conflicts detected');
        toast({
          title: 'Booking Conflict',
          description: availabilityData.message || 'The selected dates conflict with existing bookings',
          variant: 'destructive'
        });
        return;
      }
      
      // Update booking with new dates
      updateBookingMutation.mutate({
        id: booking.id,
        startDate: newStart,
        endDate: newEnd,
        guestName: booking.guest_name,
        guestPhone: booking.guest_phone,
        guestCount: booking.guest_count,
        notes: booking.notes,
        status: booking.status,
        bookingType: booking.booking_type
      });
      
    } catch (error) {
      console.error('Error during drag and drop:', error);
      dropInfo.revert();
      toast({
        title: 'Update Failed',
        description: 'Failed to update booking dates. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsDragging(false);
    }
  }, [propertyId, updateBookingMutation, toast]);

  const handleEventResize = useCallback(async (resizeInfo: EventResizeArg) => {
    setIsDragging(true);
    
    try {
      const booking = resizeInfo.event.extendedProps as Booking;
      const newStart = resizeInfo.event.startStr;
      const newEnd = resizeInfo.event.endStr;
      
      // Update booking with new dates
      updateBookingMutation.mutate({
        id: booking.id,
        startDate: newStart,
        endDate: newEnd,
        guestName: booking.guest_name,
        guestPhone: booking.guest_phone,
        guestCount: booking.guest_count,
        notes: booking.notes,
        status: booking.status,
        bookingType: booking.booking_type
      });
      
    } catch (error) {
      console.error('Error during resize:', error);
      resizeInfo.revert();
      toast({
        title: 'Update Failed',
        description: 'Failed to resize booking. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsDragging(false);
    }
  }, [updateBookingMutation, toast]);

  const renderEventContent = (eventInfo: EventContentArg) => {
    const booking = eventInfo.event.extendedProps as Booking;
    return (
      <div className="p-1 text-xs overflow-hidden">
        <div className="font-medium truncate">
          {booking.guest_name || 'Booking'}
        </div>
        {booking.guest_count && (
          <div className="flex items-center gap-1 text-xs opacity-80">
            <User size={10} />
            {booking.guest_count}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("bg-white rounded-lg shadow-lg p-6", className)}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-[#2D3C2D]">Property Calendar</h2>
        <div className="flex items-center gap-4">
          {/* Connection Status Indicator */}
          <div className="flex items-center gap-2 text-sm">
            {isConnected ? (
              <>
                <Wifi size={16} className="text-green-500" />
                <span className="text-green-600">Live</span>
              </>
            ) : connectionStatus === 'connecting' ? (
              <>
                <RotateCcw size={16} className="text-amber-500 animate-spin" />
                <span className="text-amber-600">Connecting...</span>
              </>
            ) : (
              <>
                <WifiOff size={16} className="text-red-500" />
                <span className="text-red-600">Offline</span>
              </>
            )}
          </div>
          
          {/* Legend */}
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Confirmed</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-amber-500 rounded"></div>
              <span>Tentative</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span>Blocked</span>
            </div>
          </div>
        </div>
      </div>

      <div className="fullcalendar-container">
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
          }}
          events={events}
          selectable={true}
          selectMirror={true}
          editable={true}
          eventResizableFromStart={true}
          eventDurationEditable={true}
          dayMaxEvents={3}
          select={handleDateSelect}
          eventClick={handleEventClick}
          eventDrop={handleEventDrop}
          eventResize={handleEventResize}
          eventContent={renderEventContent}
          height="auto"
          aspectRatio={1.35}
          eventDisplay="block"
          displayEventTime={false}
          eventClassNames={(arg) => {
            const booking = arg.event.extendedProps as Booking;
            return [
              'cursor-pointer',
              'hover:opacity-80',
              'transition-opacity',
              isDragging ? 'opacity-60' : '',
              booking.status === 'tentative' ? 'border-dashed' : ''
            ].filter(Boolean);
          }}
          loading={(loading) => console.log('Calendar loading:', loading)}
        />
      </div>

      {/* Drag Conflict Alert */}
      {dragConflict && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-800">
            <XCircle size={16} />
            <span className="text-sm font-medium">Booking Conflict</span>
          </div>
          <p className="text-sm text-red-700 mt-1">{dragConflict}</p>
          <Button 
            size="sm" 
            variant="outline" 
            className="mt-2 text-red-700 border-red-300"
            onClick={() => setDragConflict(null)}
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Real-time Status */}
      {!isConnected && (
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-center gap-2 text-amber-800">
            <WifiOff size={16} />
            <span className="text-sm font-medium">Limited Functionality</span>
          </div>
          <p className="text-sm text-amber-700 mt-1">
            Real-time updates are unavailable. Changes may not appear immediately.
          </p>
        </div>
      )}

      {/* Add Booking Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus size={18} />
              Add New Booking
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedDates && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-blue-900">Selected Dates</p>
                <p className="text-sm text-blue-700">
                  {selectedDates.startStr} to {selectedDates.endStr}
                </p>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="guestName">Guest Name</Label>
                <Input
                  id="guestName"
                  value={formData.guestName}
                  onChange={(e) => setFormData({ ...formData, guestName: e.target.value })}
                  placeholder="Enter guest name"
                />
              </div>
              <div>
                <Label htmlFor="guestPhone">Phone</Label>
                <Input
                  id="guestPhone"
                  value={formData.guestPhone}
                  onChange={(e) => setFormData({ ...formData, guestPhone: e.target.value })}
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="guestCount">Guest Count</Label>
                <Input
                  id="guestCount"
                  type="number"
                  min="1"
                  value={formData.guestCount}
                  onChange={(e) => setFormData({ ...formData, guestCount: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => setFormData({ ...formData, status: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="tentative">Tentative</SelectItem>
                    <SelectItem value="blocked">Blocked</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Add any notes..."
                rows={3}
              />
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleCreateBooking}
                disabled={createBookingMutation.isPending}
              >
                {createBookingMutation.isPending ? 'Creating...' : 'Create Booking'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Booking Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit size={18} />
              Edit Booking
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedBooking && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-gray-900">Booking Period</p>
                <p className="text-sm text-gray-700">
                  {selectedBooking.start_date} to {selectedBooking.end_date}
                </p>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="editGuestName">Guest Name</Label>
                <Input
                  id="editGuestName"
                  value={formData.guestName}
                  onChange={(e) => setFormData({ ...formData, guestName: e.target.value })}
                  placeholder="Enter guest name"
                />
              </div>
              <div>
                <Label htmlFor="editGuestPhone">Phone</Label>
                <Input
                  id="editGuestPhone"
                  value={formData.guestPhone}
                  onChange={(e) => setFormData({ ...formData, guestPhone: e.target.value })}
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="editGuestCount">Guest Count</Label>
                <Input
                  id="editGuestCount"
                  type="number"
                  min="1"
                  value={formData.guestCount}
                  onChange={(e) => setFormData({ ...formData, guestCount: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <Label htmlFor="editStatus">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => setFormData({ ...formData, status: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="tentative">Tentative</SelectItem>
                    <SelectItem value="blocked">Blocked</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="editNotes">Notes</Label>
              <Textarea
                id="editNotes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Add any notes..."
                rows={3}
              />
            </div>

            <div className="flex justify-between pt-4">
              <Button 
                variant="destructive" 
                onClick={handleDeleteBooking}
                disabled={deleteBookingMutation.isPending}
              >
                <Trash2 size={16} className="mr-1" />
                {deleteBookingMutation.isPending ? 'Deleting...' : 'Delete'}
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpdateBooking}
                  disabled={updateBookingMutation.isPending}
                >
                  {updateBookingMutation.isPending ? 'Updating...' : 'Update'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <style>{`
        .fullcalendar-container .fc {
          font-family: inherit;
        }
        .fullcalendar-container .fc-button-primary {
          background-color: #4A6741;
          border-color: #4A6741;
        }
        .fullcalendar-container .fc-button-primary:hover {
          background-color: #3d5436;
          border-color: #3d5436;
        }
        .fullcalendar-container .fc-today-button:disabled {
          background-color: #6b7280;
          border-color: #6b7280;
        }
        .fullcalendar-container .fc-day-today {
          background-color: #f3f4f6;
        }
        .fullcalendar-container .fc-event {
          cursor: pointer;
        }
      `}</style>
    </div>
  );
}