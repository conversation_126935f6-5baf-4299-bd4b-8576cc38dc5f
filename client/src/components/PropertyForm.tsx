import { useState } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Property } from "@shared/schema";
import { propertyFormSchema, PropertyFormData } from "@shared/validations";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ImageUpload } from "./ImageUpload";
import { PricingSection } from "./PricingSection";

interface PropertyFormProps {
  mode: "create" | "edit";
  initialData?: Property;
}

const amenitiesOptions = [
  { id: "wifi", label: "WiFi" },
  { id: "kitchen", label: "Full Kitchen" },
  { id: "fireplace", label: "Fireplace" },
  { id: "garden", label: "Garden Access" },
  { id: "parking", label: "Parking" },
  { id: "outdoor_seating", label: "Outdoor Seating" },
  { id: "lake_view", label: "Lake View" },
  { id: "mountain_view", label: "Mountain View" },
  { id: "orchard", label: "Orchard" },
  { id: "pond", label: "Pond" },
];


type FormValues = PropertyFormData;

export default function PropertyForm({ mode, initialData }: PropertyFormProps) {
  const [_, navigate] = useLocation();
  const { toast } = useToast();

  const form = useForm<PropertyFormData>({
    resolver: zodResolver(propertyFormSchema),
    defaultValues: initialData ? {
      title: initialData.title,
      description: initialData.description,
      location: initialData.location,
      halfDayPrice: initialData.halfDayPrice,
      fullDayPrice: initialData.fullDayPrice,
      weekdayHalfDayPrice: initialData.weekdayHalfDayPrice || 0,
      weekdayFullDayPrice: initialData.weekdayFullDayPrice || 0,
      weekendHalfDayPrice: initialData.weekendHalfDayPrice || 0,
      weekendFullDayPrice: initialData.weekendFullDayPrice || 0,
      bedrooms: initialData.bedrooms,
      bathrooms: initialData.bathrooms,
      amenities: initialData.amenities,
      images: initialData.images,
      status: initialData.status as "active" | "draft",
      featured: initialData.featured || false,
    } : {
      title: "",
      description: "",
      location: "",
      halfDayPrice: 0,
      fullDayPrice: 0,
      weekdayHalfDayPrice: undefined,
      weekdayFullDayPrice: undefined,
      weekendHalfDayPrice: undefined,
      weekendFullDayPrice: undefined,
      bedrooms: 1,
      bathrooms: 1,
      amenities: [],
      images: [],
      status: "draft" as const,
      featured: false,
    },
  });

  // Create property mutation
  const createMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      const response = await apiRequest("POST", "/api/properties", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Property created successfully",
      });
      navigate("/owner/dashboard");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update property mutation with real-time pricing sync
  const updateMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      const response = await apiRequest("PUT", `/api/properties/${initialData?.id}`, data);
      return response.json();
    },
    onSuccess: (updatedProperty) => {
      const hasPricingUpdate = updatedProperty.pricingUpdated;
      
      toast({
        title: "Success",
        description: hasPricingUpdate 
          ? "Property and pricing updated successfully - changes are live!" 
          : "Property updated successfully",
      });
      
      // Enhanced cache invalidation for real-time sync
      const queriesToInvalidate = [
        { queryKey: ["/api/properties/owner/me"] },
        { queryKey: [`/api/properties/${initialData?.id}`] },
        { queryKey: ["/api/properties"] }, // Public listings
      ];

      // If pricing was updated, also invalidate search and featured queries
      if (hasPricingUpdate) {
        queriesToInvalidate.push(
          { queryKey: ["/api/properties", "featured"] },
          { queryKey: ["/api/properties", "search"] }
        );
      }

      // Invalidate all relevant queries for immediate UI sync
      Promise.all(
        queriesToInvalidate.map(query => 
          queryClient.invalidateQueries(query)
        )
      );

      navigate("/owner/dashboard");
    },
    onError: (error: Error) => {
      toast({
        title: "Error", 
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: PropertyFormData) => {
    if (mode === "create") {
      createMutation.mutate(data);
    } else {
      updateMutation.mutate(data);
    }
  };

  return (
    <Card className="bg-white shadow-md">
      <CardContent className="p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#2D3C2D] font-semibold">Property Title</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g. Maple Grove Farmhouse" 
                      className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Give your property a catchy title
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#2D3C2D] font-semibold">Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe your property..."
                      className="min-h-32 border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed description of your property and its surroundings
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#2D3C2D] font-semibold">Location</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g. Buckinghamshire, UK" 
                      className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the location of your property
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <PricingSection form={form as UseFormReturn<PropertyFormData>} />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="bedrooms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#2D3C2D] font-semibold">Bedrooms</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        min="1"
                        className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                        value={field.value}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bathrooms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#2D3C2D] font-semibold">Bathrooms</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        min="1"
                        className="border-[#D5CCC4] focus:border-[#4A6741] focus:ring-[#4A6741]"
                        value={field.value}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="amenities"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-[#2D3C2D] font-semibold">Amenities</FormLabel>
                    <FormDescription>
                      Select all amenities available at your property
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {amenitiesOptions.map((amenity) => (
                      <FormField
                        key={amenity.id}
                        control={form.control}
                        name="amenities"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={amenity.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(amenity.label) || false}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...(field.value || []), amenity.label])
                                      : field.onChange(
                                          (field.value || []).filter(
                                            (value) => value !== amenity.label
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal text-[#605045]">
                                {amenity.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="images"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#2D3C2D] font-semibold">Property Images</FormLabel>
                  <FormDescription>
                    Upload images and videos of your property (max 10 files, 5MB each, 10MB total)
                  </FormDescription>
                  <FormControl>
                    <ImageUpload
                      images={field.value || []}
                      onImagesChange={field.onChange}
                      maxImages={10}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#2D3C2D] font-semibold">Status</FormLabel>
                  <FormDescription>
                    Set your property as active or draft
                  </FormDescription>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex space-x-4"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="active" />
                        </FormControl>
                        <FormLabel className="font-normal text-[#605045]">Active</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="draft" />
                        </FormControl>
                        <FormLabel className="font-normal text-[#605045]">Draft</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="featured"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4 bg-[#F7F4F1]">
                  <FormControl>
                    <Checkbox
                      checked={field.value || false}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-[#2D3C2D] font-semibold">
                      Featured Property
                    </FormLabel>
                    <FormDescription>
                      Mark this property as featured to show it on the homepage
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate("/owner/dashboard")}
                className="border-[#D5CCC4] text-[#605045]"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {createMutation.isPending || updateMutation.isPending
                  ? "Saving..."
                  : mode === "create"
                  ? "Create Property"
                  : "Update Property"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
