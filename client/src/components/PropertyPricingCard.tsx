import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'wouter';
import { Property } from '@shared/schema';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { PricingSection } from './PricingSection';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { propertyFormSchema, PropertyFormData } from '@shared/validations';
import { Form } from '@/components/ui/form';
import { 
  Calendar, 
  DollarSign, 
  Edit3, 
  MapPin, 
  Building2, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle,
  Zap
} from 'lucide-react';

interface PropertyPricingCardProps {
  property: Property;
  onPricingUpdate?: () => void;
  showQuickActions?: boolean;
  compact?: boolean;
}

export function PropertyPricingCard({ 
  property, 
  onPricingUpdate,
  showQuickActions = true,
  compact = false
}: PropertyPricingCardProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showPricingModal, setShowPricingModal] = useState(false);

  // Fetch current property data to ensure we have latest pricing
  const { data: currentProperty, isLoading: propertyLoading } = useQuery({
    queryKey: [`/api/properties/${property.id}`],
    queryFn: async () => {
      const response = await fetch(`/api/properties/${property.id}`, {
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        }
      });
      if (!response.ok) throw new Error('Failed to fetch property');
      const result = await response.json();
      return result.data || result;
    },
    initialData: property,
    staleTime: 30000, // Cache for 30 seconds
  });

  // Form for pricing updates
  const form = useForm<PropertyFormData>({
    resolver: zodResolver(propertyFormSchema),
    defaultValues: {
      title: currentProperty?.title || '',
      description: currentProperty?.description || '',
      location: currentProperty?.location || '',
      halfDayPrice: currentProperty?.halfDayPrice || 0,
      fullDayPrice: currentProperty?.fullDayPrice || 0,
      weekdayHalfDayPrice: currentProperty?.weekdayHalfDayPrice,
      weekdayFullDayPrice: currentProperty?.weekdayFullDayPrice,
      weekendHalfDayPrice: currentProperty?.weekendHalfDayPrice,
      weekendFullDayPrice: currentProperty?.weekendFullDayPrice,
      bedrooms: currentProperty?.bedrooms || 1,
      bathrooms: currentProperty?.bathrooms || 1,
      amenities: currentProperty?.amenities || [],
      images: currentProperty?.images || [],
      status: (currentProperty?.status as "active" | "draft") || "active",
      featured: currentProperty?.featured || false,
    },
  });

  // Update pricing mutation
  const updatePricingMutation = useMutation({
    mutationFn: async (data: Partial<PropertyFormData>) => {
      const response = await fetch(`/api/properties/${property.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        },
        credentials: 'include',
        body: JSON.stringify({
          // Only send pricing-related fields
          halfDayPrice: data.halfDayPrice,
          fullDayPrice: data.fullDayPrice,
          weekdayHalfDayPrice: data.weekdayHalfDayPrice,
          weekdayFullDayPrice: data.weekdayFullDayPrice,
          weekendHalfDayPrice: data.weekendHalfDayPrice,
          weekendFullDayPrice: data.weekendFullDayPrice,
        })
      });
      if (!response.ok) throw new Error('Failed to update pricing');
      return response.json();
    },
    onSuccess: (updatedProperty) => {
      const hasPricingUpdate = updatedProperty.pricingUpdated;
      
      // Enhanced post-save feedback with specific pricing details
      const feedbackMessage = hasPricingUpdate 
        ? 'Prices updated successfully. Your new rates are now live and will be used for all future bookings.'
        : 'Property updated successfully.';
      
      toast({
        title: hasPricingUpdate ? '✅ Prices Updated Successfully' : 'Success',
        description: feedbackMessage,
        duration: 5000, // Show longer for pricing updates
      });
      
      // Additional success notification for pricing-specific updates
      if (hasPricingUpdate) {
        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
          new Notification('Pricing Updated', {
            body: 'Your property prices have been updated and are now live for bookings.',
            icon: '/favicon.ico'
          });
        }
      }

      // Invalidate relevant queries for real-time sync
      queryClient.invalidateQueries({ queryKey: ['/api/properties/owner'] });
      queryClient.invalidateQueries({ queryKey: [`/api/properties/${property.id}`] });
      
      if (hasPricingUpdate) {
        queryClient.invalidateQueries({ queryKey: ['/api/properties'] });
        queryClient.invalidateQueries({ queryKey: ['/api/properties', 'featured'] });
      }

      setShowPricingModal(false);
      onPricingUpdate?.();
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Failed to update pricing. Please try again.';
      
      toast({
        title: '❌ Pricing Update Failed',
        description: `${errorMessage} Your current pricing remains unchanged.`,
        variant: 'destructive',
        duration: 6000,
      });
      
      console.error('Pricing update error:', error);
    }
  });

  const handlePricingSubmit = (data: PropertyFormData) => {
    updatePricingMutation.mutate(data);
  };

  // Helper functions
  const formatCurrency = (value: number | undefined) => {
    if (!value) return '₹0';
    return `₹${value.toLocaleString('en-IN')}`;
  };

  const hasWeekendPricing = currentProperty?.weekendHalfDayPrice || currentProperty?.weekendFullDayPrice;
  const hasWeekdayPricing = currentProperty?.weekdayHalfDayPrice || currentProperty?.weekdayFullDayPrice;

  const getPricingStatus = () => {
    if (hasWeekendPricing) return { status: 'advanced', label: 'Weekend Pricing', color: 'bg-purple-100 text-purple-700' };
    if (hasWeekdayPricing) return { status: 'weekday', label: 'Weekday Pricing', color: 'bg-blue-100 text-blue-700' };
    return { status: 'basic', label: 'Base Pricing', color: 'bg-gray-100 text-gray-700' };
  };

  const pricingStatus = getPricingStatus();

  if (propertyLoading && !currentProperty) {
    return (
      <Card className="w-full">
        <CardHeader>
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <Skeleton className="h-8 w-1/3" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card className="w-full hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-gray-500" />
              <h3 className="font-medium text-sm truncate">{currentProperty?.title}</h3>
            </div>
            <Badge variant="outline" className={`text-xs ${pricingStatus.color}`}>
              {pricingStatus.label}
            </Badge>
          </div>
          
          <div className="space-y-2 text-xs text-gray-600">
            <div className="flex justify-between">
              <span>Half-Day:</span>
              <span className="font-medium">{formatCurrency(currentProperty?.halfDayPrice)}</span>
            </div>
            <div className="flex justify-between">
              <span>Full-Day:</span>
              <span className="font-medium">{formatCurrency(currentProperty?.fullDayPrice)}</span>
            </div>
            {hasWeekendPricing && (
              <div className="flex justify-between text-purple-600">
                <span>Weekend:</span>
                <span className="font-medium">
                  {formatCurrency(currentProperty?.weekendHalfDayPrice || currentProperty?.halfDayPrice)} - 
                  {formatCurrency(currentProperty?.weekendFullDayPrice || currentProperty?.fullDayPrice)}
                </span>
              </div>
            )}
          </div>

          {showQuickActions && (
            <div className="mt-3 flex gap-2">
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => setShowPricingModal(true)}
                className="flex-1 text-xs"
              >
                <Edit3 className="h-3 w-3 mr-1" />
                Edit
              </Button>
              <Link href={`/property/${property.id}`}>
                <Button size="sm" variant="ghost" className="text-xs">
                  View
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="w-full hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                {currentProperty?.title}
              </CardTitle>
              <p className="text-sm text-gray-600 flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {currentProperty?.location}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={pricingStatus.color}>
                {pricingStatus.label}
              </Badge>
              {currentProperty?.status === 'active' ? (
                <Badge className="bg-green-100 text-green-700">Active</Badge>
              ) : (
                <Badge variant="secondary">Draft</Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Current Pricing Display */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <DollarSign className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-gray-900">Current Pricing</h4>
              <span title="Live pricing data">
                <Zap className="h-3 w-3 text-blue-500" />
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-gray-600 uppercase tracking-wide">Half-Day (12h)</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(currentProperty?.halfDayPrice)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-600 uppercase tracking-wide">Full-Day (24h)</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(currentProperty?.fullDayPrice)}
                </p>
              </div>
            </div>

            {/* Advanced Pricing Indicators */}
            {(hasWeekdayPricing || hasWeekendPricing) && (
              <div className="mt-4 pt-3 border-t border-blue-200">
                <div className="flex items-center gap-4 text-sm">
                  {hasWeekdayPricing && (
                    <div className="flex items-center gap-1 text-blue-700">
                      <Calendar className="h-3 w-3" />
                      <span>Weekday rates set</span>
                      <CheckCircle className="h-3 w-3" />
                    </div>
                  )}
                  {hasWeekendPricing && (
                    <div className="flex items-center gap-1 text-purple-700">
                      <TrendingUp className="h-3 w-3" />
                      <span>Weekend premium</span>
                      <CheckCircle className="h-3 w-3" />
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Pricing Insights */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1 text-gray-600">
              <Clock className="h-4 w-4" />
              <span>Last updated: {new Date().toLocaleDateString()}</span>
            </div>
            {!hasWeekendPricing && (
              <div className="flex items-center gap-1 text-amber-600">
                <AlertCircle className="h-4 w-4" />
                <span>Consider weekend pricing</span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {showQuickActions && (
            <div className="flex gap-3 pt-2">
              <Button 
                onClick={() => setShowPricingModal(true)}
                className="flex-1"
                variant="outline"
              >
                <Edit3 className="h-4 w-4 mr-2" />
                Manage Pricing
              </Button>
              <Link href={`/owner/property/edit/${property.id}`}>
                <Button variant="ghost">
                  Edit Property
                </Button>
              </Link>
              <Link href={`/property/${property.id}`}>
                <Button variant="ghost">
                  View Listing
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pricing Management Modal */}
      <Dialog open={showPricingModal} onOpenChange={setShowPricingModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Manage Pricing - {currentProperty?.title}</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Current vs New Pricing Comparison */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Current Live Pricing
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Base 12h:</p>
                  <p className="font-semibold">{formatCurrency(currentProperty?.halfDayPrice)}</p>
                </div>
                <div>
                  <p className="text-gray-600">Base 24h:</p>
                  <p className="font-semibold">{formatCurrency(currentProperty?.fullDayPrice)}</p>
                </div>
                {hasWeekendPricing && (
                  <>
                    <div>
                      <p className="text-gray-600">Weekend 12h:</p>
                      <p className="font-semibold text-purple-600">
                        {formatCurrency(currentProperty?.weekendHalfDayPrice)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Weekend 24h:</p>
                      <p className="font-semibold text-purple-600">
                        {formatCurrency(currentProperty?.weekendFullDayPrice)}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Pricing Form */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handlePricingSubmit)} className="space-y-6">
                <PricingSection form={form} />
                
                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setShowPricingModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit"
                    disabled={updatePricingMutation.isPending}
                  >
                    {updatePricingMutation.isPending ? 'Updating...' : 'Update Pricing'}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default PropertyPricingCard;