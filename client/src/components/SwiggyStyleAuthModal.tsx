import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON><PERSON><PERSON>, X } from "lucide-react";

interface SwiggyStyleAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'login' | 'signup';
}

// Login Schema - Only phone number required
const loginSchema = z.object({
  phone: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^[6-9]\d{9}$/, "Please enter a valid Indian mobile number")
});

// Sign Up Schema - Phone, Name, Email, Role required
const signUpSchema = z.object({
  phone: z.string()
    .length(10, "Phone number must be exactly 10 digits")
    .regex(/^[6-9]\d{9}$/, "Please enter a valid Indian mobile number starting with 6, 7, 8, or 9"),
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name cannot exceed 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces"),
  email: z.string()
    .email("Please enter a valid email address")
    .min(5, "Email must be at least 5 characters")
    .max(100, "Email cannot exceed 100 characters"),
  role: z.enum(['user', 'owner'], {
    required_error: "Please select your account type"
  })
});

// OTP Verification Schema
const otpSchema = z.object({
  code: z.string().length(6, "OTP must be 6 digits")
});

// Dual OTP Verification Schema for Sign Up
const dualOTPSchema = z.object({
  smsCode: z.string().length(6, "SMS OTP must be 6 digits"),
  emailCode: z.string().length(6, "Email OTP must be 6 digits")
});

type LoginFormValues = z.infer<typeof loginSchema>;
type SignUpFormValues = z.infer<typeof signUpSchema>;
type OTPFormValues = z.infer<typeof otpSchema>;
type DualOTPFormValues = z.infer<typeof dualOTPSchema>;

type FlowStep = 'method-select' | 'login-form' | 'signup-form' | 'otp-verify' | 'dual-otp-verify';

export default function SwiggyStyleAuthModal({ isOpen, onClose, initialMode = 'login' }: SwiggyStyleAuthModalProps) {
  const [currentStep, setCurrentStep] = useState<FlowStep>('method-select');
  const [authMode, setAuthMode] = useState<'login' | 'signup'>(initialMode);
  const [userPhone, setUserPhone] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userName, setUserName] = useState('');
  const [userRole, setUserRole] = useState<'user' | 'owner'>('user');
  const [resendCountdown, setResendCountdown] = useState(0);
  
  const { login, isAuthenticated } = useAuth();
  const { toast } = useToast();

  // Close modal when authenticated (but not during active login flow)
  useEffect(() => {
    if (isAuthenticated && isOpen) {
      // Don't auto-close if user is actively in login/OTP flow
      // This prevents interference with ongoing authentication
      if (currentStep === 'method-select' || currentStep === 'login-form' || currentStep === 'signup-form') {
        const pendingBookingStr = sessionStorage.getItem('pendingBooking');
        
        if (pendingBookingStr) {
          toast({
            title: "Authentication successful",
            description: "Processing your booking...",
          });
        } else {
          toast({
            title: "Welcome!",
            description: "You are already logged in.",
          });
        }
        
        onClose();
      }
    }
  }, [isAuthenticated, isOpen, onClose, toast, currentStep, userPhone]);

  // Set initial step and mode when modal opens
  useEffect(() => {
    if (isOpen) {
      setAuthMode(initialMode);
      setCurrentStep(initialMode === 'login' ? 'login-form' : 'signup-form');
      return;
    } else {
      // Only reset state if modal is actually closing (not just re-opening)
      // Add a delay to prevent clearing state during rapid open/close cycles
      const resetTimer = setTimeout(() => {
        // Additional check: don't clear if we have active phone state or are in OTP flow
        const hasActivePhone = userPhone || sessionStorage.getItem('otp_phone_number');
        const inActiveFlow = currentStep === 'otp-verify' || currentStep === 'dual-otp-verify';
        
        if (hasActivePhone && inActiveFlow) {
          return;
        }
        
        setCurrentStep('method-select');
        setAuthMode('login');
        setUserPhone('');
        setUserEmail('');
        setUserName('');
        setUserRole('user');
        setResendCountdown(0);
        // Clear sessionStorage backup
        sessionStorage.removeItem('otp_phone_number');
      }, 500); // 500ms delay
      
      // Cleanup timer if modal reopens quickly
      return () => {
        clearTimeout(resetTimer);
      };
    }
  }, [isOpen, initialMode]);

  // Countdown timer effect
  useEffect(() => {
    if (resendCountdown > 0) {
      const timer = setTimeout(() => {
        setResendCountdown(resendCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [resendCountdown]);

  // Form setups
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: { phone: "" },
  });

  const signUpForm = useForm<SignUpFormValues>({
    resolver: zodResolver(signUpSchema),
    defaultValues: { phone: "", name: "", email: "", role: "user" },
  });

  const otpForm = useForm<OTPFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: { code: "" },
  });

  const dualOTPForm = useForm<DualOTPFormValues>({
    resolver: zodResolver(dualOTPSchema),
    defaultValues: { smsCode: "", emailCode: "" },
  });

  // Send OTP mutation (for login)
  const sendOTPMutation = useMutation({
    mutationFn: async (data: { identifier: string; type: 'sms' }) => {
      const response = await apiRequest("POST", "/api/v1/auth/otp/send-otp", data);
      return response.json();
    },
    onSuccess: (data) => {
      // Ensure userPhone is set from the server response to avoid state timing issues
      setUserPhone(data.identifier);
      setCurrentStep('otp-verify');
      setResendCountdown(60); // Start 60-second countdown
      toast({
        title: "OTP Sent",
        description: "We've sent a 6-digit code to your phone"
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Send dual OTP mutation (for sign-up)
  const sendDualOTPMutation = useMutation({
    mutationFn: async (data: { phone: string; email: string }) => {
      // Send SMS OTP
      const smsResponse = await apiRequest("POST", "/api/v1/auth/otp/send-otp", {
        identifier: data.phone,
        type: 'sms'
      });
      
      // Send Email OTP
      const emailResponse = await apiRequest("POST", "/api/v1/auth/otp/send-otp", {
        identifier: data.email,
        type: 'email'
      });

      const [smsResult, emailResult] = await Promise.all([
        smsResponse.json(),
        emailResponse.json()
      ]);

      return { sms: smsResult, email: emailResult };
    },
    onSuccess: () => {
      setCurrentStep('dual-otp-verify');
      setResendCountdown(60); // Start 60-second countdown
      toast({
        title: "OTPs Sent",
        description: "We've sent verification codes to your phone and email"
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Verify single OTP mutation (for login)
  const verifyOTPMutation = useMutation({
    mutationFn: async (data: { identifier?: string; code: string; type: 'sms' }) => {
      // Ensure identifier is always present - get from multiple sources
      const phoneFromStorage = sessionStorage.getItem('otp_phone_number');
      const finalIdentifier = data.identifier || userPhone || phoneFromStorage;
      
      // If still no identifier, throw error to prevent bad API call
      if (!finalIdentifier) {
        const errorMsg = 'No identifier found from any source!';
        throw new Error(errorMsg);
      }
      
      const payload = {
        identifier: finalIdentifier,
        code: data.code,
        type: data.type
      };
      
      const response = await apiRequest("POST", "/api/v1/auth/otp/verify-otp-login", payload);
      return response.json();
    },
    onSuccess: (data) => {
      login(data.user, data.token);
      toast({
        title: "Success!",
        description: 'Welcome back!'
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Verification Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Verify dual OTP mutation (for sign-up)
  const verifyDualOTPMutation = useMutation({
    mutationFn: async (data: { smsCode: string; emailCode: string; userData: any }) => {
      // Create the user account with SMS verification (which includes dual verification internally)
      const registerResponse = await apiRequest("POST", "/api/v1/auth/otp/verify-otp-register", {
        identifier: userPhone,
        code: data.smsCode,
        type: 'sms',
        userData: {
          ...data.userData,
          emailCode: data.emailCode,
          email: userEmail
        }
      });

      return registerResponse.json();
    },
    onSuccess: (data) => {
      login(data.user, data.token);
      toast({
        title: "Success!",
        description: 'Account created successfully!'
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Verification Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const handleLoginSubmit = (data: LoginFormValues) => {
    const fullPhone = `+91${data.phone}`;
    
    // Store in both state and sessionStorage as backup
    setUserPhone(fullPhone);
    sessionStorage.setItem('otp_phone_number', fullPhone);
    
    sendOTPMutation.mutate({ identifier: fullPhone, type: 'sms' });
  };

  const handleSignUpSubmit = (data: SignUpFormValues) => {
    const fullPhone = `+91${data.phone}`;
    setUserPhone(fullPhone);
    setUserEmail(data.email);
    setUserName(data.name);
    setUserRole(data.role);
    sendDualOTPMutation.mutate({ phone: fullPhone, email: data.email });
  };

  const handleOTPSubmit = (data: OTPFormValues) => {
    if (!data.code || data.code.length !== 6) {
      toast({
        title: "Error", 
        description: "Please enter a valid 6-digit OTP code.",
        variant: "destructive"
      });
      return;
    }
    
    // The mutation function will handle getting the identifier
    verifyOTPMutation.mutate({
      code: data.code,
      type: 'sms'
    });
  };

  const handleDualOTPSubmit = (data: DualOTPFormValues) => {
    verifyDualOTPMutation.mutate({
      smsCode: data.smsCode,
      emailCode: data.emailCode,
      userData: {
        fullName: userName,
        email: userEmail,
        phone: userPhone,
        role: userRole,
        acceptTerms: true
      }
    });
  };

  const getTitle = () => {
    switch (currentStep) {
      case 'method-select': return '';
      case 'login-form': return 'Login';
      case 'signup-form': return 'Sign up';
      case 'otp-verify': return 'Enter Verification Code';
      case 'dual-otp-verify': return 'Verify Phone & Email';
      default: return '';
    }
  };

  const getSubtitle = () => {
    switch (currentStep) {
      case 'login-form': return 'or create an account';
      case 'signup-form': return 'or login to your account';
      case 'otp-verify': return `We sent a 6-digit code to ${userPhone}`;
      case 'dual-otp-verify': return `We sent verification codes to ${userPhone} and ${userEmail}`;
      default: return '';
    }
  };

  const goBack = () => {
    if (currentStep === 'otp-verify') {
      setCurrentStep('login-form');
    } else if (currentStep === 'dual-otp-verify') {
      setCurrentStep('signup-form');
    } else if (currentStep === 'login-form' || currentStep === 'signup-form') {
      setCurrentStep('method-select');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        {/* Header with back button */}
        <DialogHeader className="relative">
          <div className="flex items-center justify-between">
            {currentStep !== 'method-select' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={goBack}
                className="p-1 h-auto"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            )}
            <div className="flex-1 text-center">
              {getTitle() ? (
                <DialogTitle className="text-2xl font-bold text-[#2D3C2D]">
                  {getTitle()}
                </DialogTitle>
              ) : (
                <VisuallyHidden>
                  <DialogTitle>Authentication</DialogTitle>
                </VisuallyHidden>
              )}
              <VisuallyHidden>
                <DialogDescription>
                  {getSubtitle() || "Authentication modal for login and signup"}
                </DialogDescription>
              </VisuallyHidden>
              {getSubtitle() && (
                <div className="text-sm text-[#605045] mt-1">
                  {currentStep === 'login-form' && (
                    <button
                      onClick={() => setCurrentStep('signup-form')}
                      className="text-[#4A6741] hover:underline"
                    >
                      {getSubtitle()}
                    </button>
                  )}
                  {currentStep === 'signup-form' && (
                    <button
                      onClick={() => setCurrentStep('login-form')}
                      className="text-[#4A6741] hover:underline"
                    >
                      {getSubtitle()}
                    </button>
                  )}
                  {currentStep === 'otp-verify' && (
                    <span>{getSubtitle()}</span>
                  )}
                  {currentStep === 'dual-otp-verify' && (
                    <span>{getSubtitle()}</span>
                  )}
                </div>
              )}
            </div>
            {/* Spacer for alignment */}
            <div className="w-8"></div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Method Selection */}
          {currentStep === 'method-select' && (
            <div className="space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-[#2D3C2D]">Welcome to BookAFarm</h2>
                <p className="text-[#605045]">Choose how you'd like to continue</p>
              </div>
              
              <div className="space-y-3">
                <Button
                  onClick={() => setCurrentStep('login-form')}
                  className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white font-semibold py-3"
                >
                  LOGIN
                </Button>
                
                <Button
                  onClick={() => setCurrentStep('signup-form')}
                  variant="outline"
                  className="w-full border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white font-semibold py-3"
                >
                  CREATE ACCOUNT
                </Button>
              </div>
              
              <div className="text-xs text-[#888] mt-4">
                By continuing, you agree to our{' '}
                <a href="#" className="text-[#4A6741] hover:underline">Terms & Conditions</a>{' '}
                & <a href="#" className="text-[#4A6741] hover:underline">Privacy Policy</a>
              </div>
            </div>
          )}

          {/* Login Form */}
          {currentStep === 'login-form' && (
            <div className="space-y-4">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-[#FFF5F0] rounded-full flex items-center justify-center">
                  <span className="text-2xl">🏡</span>
                </div>
              </div>

              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(handleLoginSubmit)} className="space-y-4">
                  <FormField
                    control={loginForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#605045] font-medium">
                              +91
                            </div>
                            <Input
                              placeholder="Phone number"
                              className="pl-12 py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741] text-base"
                              maxLength={10}
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    disabled={sendOTPMutation.isPending}
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white font-semibold py-3 text-base"
                  >
                    {sendOTPMutation.isPending ? "SENDING..." : "LOGIN"}
                  </Button>
                </form>
              </Form>

              <div className="text-xs text-[#888] text-center">
                By clicking on Login, I accept the{' '}
                <a href="#" className="text-black hover:underline">Terms & Conditions</a>{' '}
                & <a href="#" className="text-black hover:underline">Privacy Policy</a>
              </div>
            </div>
          )}

          {/* Sign Up Form */}
          {currentStep === 'signup-form' && (
            <div className="space-y-4">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-[#FFF5F0] rounded-full flex items-center justify-center">
                  <span className="text-2xl">🏡</span>
                </div>
              </div>

              <Form {...signUpForm}>
                <form onSubmit={signUpForm.handleSubmit(handleSignUpSubmit)} className="space-y-4">
                  <FormField
                    control={signUpForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#605045] font-medium">
                              +91
                            </div>
                            <Input
                              placeholder="Phone number"
                              className="pl-12 py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741] text-base"
                              maxLength={10}
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={signUpForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="Name"
                            className="py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741] text-base"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={signUpForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="Email"
                            type="email"
                            className="py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741] text-base"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={signUpForm.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger className="py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741] text-base">
                              <SelectValue placeholder="I want to..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user">Book farmhouse experiences</SelectItem>
                              <SelectItem value="owner">List my property as an owner</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />



                  <Button
                    type="submit"
                    disabled={sendOTPMutation.isPending}
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white font-semibold py-3 text-base"
                  >
                    {sendOTPMutation.isPending ? "SENDING..." : "CONTINUE"}
                  </Button>
                </form>
              </Form>

              <div className="text-xs text-[#888] text-center">
                By creating an account, I accept the{' '}
                <a href="#" className="text-black hover:underline">Terms & Conditions</a>{' '}
                & <a href="#" className="text-black hover:underline">Privacy Policy</a>
              </div>
            </div>
          )}

          {/* OTP Verification */}
          {currentStep === 'otp-verify' && !isAuthenticated && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <p className="text-sm text-[#605045]">
                  Verification code sent to:
                </p>
                <p className="font-semibold text-[#2D3C2D]">{userPhone || sessionStorage.getItem('otp_phone_number')}</p>
              </div>

              <Form {...otpForm}>
                <form onSubmit={otpForm.handleSubmit(handleOTPSubmit)} className="space-y-4">
                  <FormField
                    control={otpForm.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#605045]">Verification Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="000000"
                            className="text-center text-lg tracking-widest py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741]"
                            maxLength={6}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    disabled={verifyOTPMutation.isPending}
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white font-semibold py-3"
                  >
                    {verifyOTPMutation.isPending ? "VERIFYING..." : "VERIFY & CONTINUE"}
                  </Button>
                </form>
              </Form>

              <div className="text-center space-y-2">
                <Button
                  variant="ghost"
                  onClick={() => {
                    const phoneFromStorage = sessionStorage.getItem('otp_phone_number');
                    const phoneToUse = userPhone || phoneFromStorage;
                    
                    if (!phoneToUse) {
                      toast({
                        title: "Error",
                        description: "Phone number is missing. Please refresh and try again.",
                        variant: "destructive"
                      });
                      return;
                    }
                    sendOTPMutation.mutate({ identifier: phoneToUse, type: 'sms' });
                  }}
                  disabled={sendOTPMutation.isPending || resendCountdown > 0}
                  className="text-[#4A6741] hover:text-[#3A5131]"
                >
                  {sendOTPMutation.isPending ? "Sending..." : resendCountdown > 0 ? `Resend Code (${resendCountdown}s)` : "Resend Code"}
                </Button>
                {resendCountdown > 0 && (
                  <p className="text-xs text-[#888] mt-1">
                    Please wait {resendCountdown} seconds before requesting a new code
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Dual OTP Verification */}
          {currentStep === 'dual-otp-verify' && !isAuthenticated && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <p className="text-sm text-[#605045]">
                  Verification codes sent to:
                </p>
                <p className="font-semibold text-[#2D3C2D]">{userPhone}</p>
                <p className="font-semibold text-[#2D3C2D]">{userEmail}</p>
              </div>

              <Form {...dualOTPForm}>
                <form onSubmit={dualOTPForm.handleSubmit(handleDualOTPSubmit)} className="space-y-4">
                  <FormField
                    control={dualOTPForm.control}
                    name="smsCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#605045]">SMS Verification Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="000000"
                            className="text-center text-lg tracking-widest py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741]"
                            maxLength={6}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={dualOTPForm.control}
                    name="emailCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#605045]">Email Verification Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="000000"
                            className="text-center text-lg tracking-widest py-3 border-[#E0E0E0] focus:border-[#4A6741] focus:ring-[#4A6741]"
                            maxLength={6}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    disabled={verifyDualOTPMutation.isPending}
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white font-semibold py-3"
                  >
                    {verifyDualOTPMutation.isPending ? "VERIFYING..." : "VERIFY & CREATE ACCOUNT"}
                  </Button>
                </form>
              </Form>

              <div className="text-center space-y-2">
                <Button
                  variant="ghost"
                  onClick={() => {
                    sendDualOTPMutation.mutate({ phone: userPhone, email: userEmail });
                  }}
                  disabled={sendDualOTPMutation.isPending || resendCountdown > 0}
                  className="text-[#4A6741] hover:text-[#3A5131]"
                >
                  {sendDualOTPMutation.isPending ? "Sending..." : resendCountdown > 0 ? `Resend Codes (${resendCountdown}s)` : "Resend Codes"}
                </Button>
                {resendCountdown > 0 && (
                  <p className="text-xs text-[#888] mt-1">
                    Please wait {resendCountdown} seconds before requesting new codes
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}