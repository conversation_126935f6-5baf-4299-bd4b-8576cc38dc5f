import { useState, useEffect } from "react";
import { Property } from "@shared/schema";
import { ChevronLeft, ChevronRight, MapPin, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";
import { useFavorites } from "@/contexts/FavoritesContext";
import { useCarouselKeyboard } from "@/hooks/useKeyboardNavigation";
import { TIMEOUTS } from "../../../shared/constants";
import LazyImage from "./LazyImage";

interface FeaturedCarouselProps {
  properties: Property[];
}

export default function FeaturedCarousel({ properties }: FeaturedCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const { isFavorite, toggleFavorite } = useFavorites();

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? properties.length - 1 : currentIndex - 1);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), TIMEOUTS.AUTO_SAVE);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === properties.length - 1 ? 0 : currentIndex + 1);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), TIMEOUTS.AUTO_SAVE);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), TIMEOUTS.AUTO_SAVE);
  };

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || properties.length <= 1) return undefined;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === properties.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, properties.length]);

  // Keyboard navigation support
  useCarouselKeyboard(
    currentIndex,
    properties.length,
    goToNext,
    goToPrevious
  );

  if (!properties || properties.length === 0) {
    return null;
  }

  const currentProperty = properties[currentIndex];

  return (
    <div className="relative bg-gradient-to-br from-white to-[#F7F4F1] rounded-2xl overflow-hidden shadow-2xl">
      {/* Main Carousel Content */}
      <div className="relative h-96 md:h-[500px] overflow-hidden">
        {/* Background Image with Fallback */}
        <div 
          className="absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out carousel-background"
          style={{ 
            backgroundImage: `url(${currentProperty.images?.[0] || "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80"})`,
            filter: 'brightness(0.7)'
          }}
        >
          {/* Hidden image preloader for error detection */}
          <img 
            src={currentProperty.images?.[0] || "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80"} 
            alt="" 
            className="hidden"
            onError={() => {
              // This will trigger if the background image fails to load
              const div = document.querySelector('.carousel-background') as HTMLElement;
              if (div) {
                div.style.backgroundImage = `url(https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80)`;
              }
            }} 
          />
        </div>
        
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />
        
        {/* Content Overlay */}
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-6">
            <div className="max-w-2xl text-white">
              {/* Featured Badge */}
              <div className="flex items-center mb-4">
                <span className="bg-[#4A6741] text-white px-4 py-2 rounded-full text-sm font-semibold mr-4">
                  ⭐ Featured Property
                </span>
                <div className="flex items-center text-yellow-400">
                  <Star className="w-5 h-5 fill-current mr-1" />
                  <span className="text-white">4.8 (25 reviews)</span>
                </div>
              </div>
              
              {/* Property Title */}
              <h2 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                {currentProperty.title}
              </h2>
              
              {/* Location */}
              <div className="flex items-center mb-6 text-xl">
                <MapPin className="w-6 h-6 mr-2" />
                <span>{currentProperty.location}</span>
              </div>
              
              {/* Features */}
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2">
                  <span className="text-sm font-medium">{currentProperty.bedrooms} Bedrooms</span>
                </div>
                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2">
                  <span className="text-sm font-medium">{currentProperty.bathrooms} Bathrooms</span>
                </div>
                {currentProperty.amenities.slice(0, 2).map((amenity, i) => (
                  <div key={i} className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2">
                    <span className="text-sm font-medium">{amenity}</span>
                  </div>
                ))}
              </div>
              
              {/* Price and CTA */}
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg opacity-90">Starting from</p>
                  <p className="text-3xl font-bold">₹{currentProperty.halfDayPrice.toLocaleString()}</p>
                  <p className="text-sm opacity-75">per 12h access</p>
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      toggleFavorite(currentProperty.id);
                    }}
                    className={`p-3 rounded-full transition-all duration-300 hover:scale-110 ${
                      isFavorite(currentProperty.id) 
                        ? 'bg-red-500 text-white' 
                        : 'bg-white/20 backdrop-blur-sm text-white hover:bg-white/30'
                    }`}
                  >
                    <svg className="w-6 h-6" fill={isFavorite(currentProperty.id) ? "currentColor" : "none"} viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </button>
                  
                  <Link href={`/property/${currentProperty.id}`}>
                    <Button className="bg-[#4A6741] hover:bg-[#3A5131] text-white px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Navigation Arrows */}
        <button
          onClick={goToPrevious}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 hover:scale-110"
          aria-label="Previous property"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
        
        <button
          onClick={goToNext}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 hover:scale-110"
          aria-label="Next property"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      </div>
      
      {/* Thumbnail Navigation */}
      <div className="p-6 bg-white">
        <div className="flex justify-center space-x-4 flex-wrap gap-y-2">
          {properties.map((property, index) => (
            <button
              key={property.id}
              onClick={() => goToSlide(index)}
              className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden transition-all duration-300 ${
                index === currentIndex 
                  ? 'ring-4 ring-[#4A6741] scale-110' 
                  : 'opacity-70 hover:opacity-100 hover:scale-105'
              }`}
            >
              <img 
                src={property.images?.[0] || "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80"} 
                alt={property.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  if (target.src !== "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80") {
                    target.src = "https://images.unsplash.com/photo-1505916349660-8d91a99c3e23?auto=format&fit=crop&w=800&q=80";
                  }
                }}
              />
            </button>
          ))}
        </div>
        
        {/* Property Counter */}
        <div className="text-center mt-4">
          <span className="text-[#605045] text-sm">
            {currentIndex + 1} of {properties.length} featured properties
          </span>
        </div>
      </div>
    </div>
  );
}