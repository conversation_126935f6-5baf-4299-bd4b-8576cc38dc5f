import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Zap, 
  Globe, 
  Calendar, 
  DollarSign,
  Users,
  Clock,
  ArrowRight,
  Sparkles
} from 'lucide-react';

interface PricingUpdateFeedbackProps {
  isVisible: boolean;
  onClose: () => void;
  updatedPricing?: {
    halfDayPrice: number;
    fullDayPrice: number;
    weekendHalfDayPrice?: number;
    weekendFullDayPrice?: number;
    hasWeekendPricing: boolean;
  };
  propertyName?: string;
}

export function PricingUpdateFeedback({ 
  isVisible, 
  onClose, 
  updatedPricing,
  propertyName = "Your Property"
}: PricingUpdateFeedbackProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      title: "Prices Updated Successfully",
      description: "Your pricing changes have been saved to the database",
      detail: "All new rates are now stored and ready for use"
    },
    {
      icon: <Zap className="h-5 w-5 text-blue-600" />,
      title: "Real-Time Sync Complete",
      description: "Changes synchronized across all system components", 
      detail: "Cache invalidation ensures immediate availability"
    },
    {
      icon: <Globe className="h-5 w-5 text-purple-600" />,
      title: "Public Listing Updated",
      description: "Your property listing shows the new pricing",
      detail: "Visitors will see updated rates immediately"
    },
    {
      icon: <Calendar className="h-5 w-5 text-orange-600" />,
      title: "Booking System Active",
      description: "New bookings will automatically use updated rates",
      detail: "Weekend/weekday logic fully integrated"
    }
  ];

  useEffect(() => {
    if (!isVisible) {
      setCurrentStep(0);
      return;
    }

    const timer = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < steps.length - 1) {
          return prev + 1;
        }
        clearInterval(timer);
        return prev;
      });
    }, 800);

    return () => clearInterval(timer);
  }, [isVisible, steps.length]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6">
          
          {/* Header */}
          <div className="text-center mb-6">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Sparkles className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              ✅ Prices Updated Successfully!
            </h2>
            <p className="text-gray-600">
              Your pricing changes for <strong>{propertyName}</strong> are now live
            </p>
          </div>

          {/* Updated Pricing Summary */}
          {updatedPricing && (
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 mb-6 border border-green-200">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                Your New Pricing
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center p-3 bg-white rounded-lg border">
                  <p className="text-gray-600">Half-Day (12h)</p>
                  <p className="text-xl font-bold text-gray-900">
                    ₹{updatedPricing.halfDayPrice.toLocaleString('en-IN')}
                  </p>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border">
                  <p className="text-gray-600">Full-Day (24h)</p>
                  <p className="text-xl font-bold text-gray-900">
                    ₹{updatedPricing.fullDayPrice.toLocaleString('en-IN')}
                  </p>
                </div>
              </div>
              
              {updatedPricing.hasWeekendPricing && (
                <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-purple-800">Weekend Premium Active</span>
                    <Badge className="bg-purple-100 text-purple-700">
                      Fri-Sun
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    {updatedPricing.weekendHalfDayPrice && (
                      <div>
                        <span className="text-purple-600">Weekend 12h:</span>
                        <span className="ml-2 font-medium">₹{updatedPricing.weekendHalfDayPrice.toLocaleString('en-IN')}</span>
                      </div>
                    )}
                    {updatedPricing.weekendFullDayPrice && (
                      <div>
                        <span className="text-purple-600">Weekend 24h:</span>
                        <span className="ml-2 font-medium">₹{updatedPricing.weekendFullDayPrice.toLocaleString('en-IN')}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Progress Steps */}
          <div className="space-y-4 mb-6">
            {steps.map((step, index) => (
              <div 
                key={index}
                className={`flex items-start gap-3 p-3 rounded-lg transition-all duration-500 ${
                  index <= currentStep 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-gray-50 border border-gray-200 opacity-50'
                }`}
              >
                <div className={`transition-all duration-300 ${
                  index <= currentStep ? 'scale-100' : 'scale-75'
                }`}>
                  {step.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{step.title}</h4>
                  <p className="text-sm text-gray-600">{step.description}</p>
                  <p className="text-xs text-gray-500 mt-1">{step.detail}</p>
                </div>
                {index <= currentStep && (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                )}
              </div>
            ))}
          </div>

          {/* Impact Summary */}
          {currentStep >= steps.length - 1 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <Users className="h-4 w-4" />
                What Happens Now
              </h3>
              <div className="space-y-2 text-sm text-blue-800">
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-3 w-3 mt-0.5" />
                  <span>New visitors will see your updated pricing immediately</span>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-3 w-3 mt-0.5" />
                  <span>Weekend bookings will automatically use weekend rates when set</span>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-3 w-3 mt-0.5" />
                  <span>Booking confirmations will show the correct pricing based on date selected</span>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-3 w-3 mt-0.5" />
                  <span>Your revenue optimization is now active across all booking channels</span>
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          {currentStep >= steps.length - 1 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-yellow-900 mb-2 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Recommended Next Steps
              </h3>
              <div className="space-y-1 text-sm text-yellow-800">
                <p>• Monitor your booking conversion rates with the new pricing</p>
                <p>• Consider seasonal adjustments during peak periods</p>
                <p>• Review competitor pricing regularly to stay competitive</p>
                {!updatedPricing?.hasWeekendPricing && (
                  <p>• Add weekend premium pricing to maximize weekend revenue</p>
                )}
              </div>
            </div>
          )}

          {/* Close Button */}
          <div className="flex justify-end">
            <Button 
              onClick={onClose}
              className="bg-green-600 hover:bg-green-700 text-white"
              disabled={currentStep < steps.length - 1}
            >
              {currentStep < steps.length - 1 ? 'Processing...' : 'Got it, thanks!'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PricingUpdateFeedback;