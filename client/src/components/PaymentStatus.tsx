import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  CreditCard, 
  FileText, 
  Download,
  RefreshCw,
  AlertTriangle 
} from "lucide-react";

interface PaymentStatusProps {
  status: 'success' | 'failed' | 'pending' | 'cancelled';
  paymentData?: {
    paymentId: string;
    orderId: string;
    amount: number;
    currency: string;
    method?: string;
    timestamp: string;
  };
  bookingData?: {
    id: number;
    propertyTitle: string;
    bookingDate: string;
    bookingType: string;
    guests: number;
  };
  onRetry?: () => void;
  onClose?: () => void;
  onDownloadReceipt?: () => void;
}

export default function PaymentStatus({
  status,
  paymentData,
  bookingData,
  onRetry,
  onClose,
  onDownloadReceipt
}: PaymentStatusProps) {
  const formatCurrency = (amount: number, currency: string = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-12 w-12 text-green-600" />;
      case 'failed':
        return <XCircle className="h-12 w-12 text-red-600" />;
      case 'pending':
        return <Clock className="h-12 w-12 text-yellow-600" />;
      case 'cancelled':
        return <AlertTriangle className="h-12 w-12 text-gray-600" />;
      default:
        return <Clock className="h-12 w-12 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'failed':
        return 'bg-red-50 border-red-200';
      case 'pending':
        return 'bg-yellow-50 border-yellow-200';
      case 'cancelled':
        return 'bg-gray-50 border-gray-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'success':
        return {
          title: 'Payment Successful!',
          description: 'Your booking has been confirmed. You will receive a confirmation email shortly.',
          textColor: 'text-green-800'
        };
      case 'failed':
        return {
          title: 'Payment Failed',
          description: 'We couldn\'t process your payment. Please try again or use a different payment method.',
          textColor: 'text-red-800'
        };
      case 'pending':
        return {
          title: 'Payment Pending',
          description: 'Your payment is being processed. This may take a few minutes.',
          textColor: 'text-yellow-800'
        };
      case 'cancelled':
        return {
          title: 'Payment Cancelled',
          description: 'You cancelled the payment. Your booking is still pending.',
          textColor: 'text-gray-800'
        };
      default:
        return {
          title: 'Payment Status Unknown',
          description: 'Please contact support for assistance.',
          textColor: 'text-gray-800'
        };
    }
  };

  const statusMessage = getStatusMessage();

  return (
    <Card className={`w-full max-w-md mx-auto ${getStatusColor()}`}>
      <CardHeader className="text-center pb-4">
        <div className="flex justify-center mb-4">
          {getStatusIcon()}
        </div>
        <CardTitle className={`text-xl ${statusMessage.textColor}`}>
          {statusMessage.title}
        </CardTitle>
        <p className={`text-sm ${statusMessage.textColor} opacity-80`}>
          {statusMessage.description}
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Payment Details */}
        {paymentData && (
          <div className="bg-white rounded-lg p-4 border">
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payment Details
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Payment ID</span>
                <span className="font-mono text-xs">{paymentData.paymentId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order ID</span>
                <span className="font-mono text-xs">{paymentData.orderId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Amount</span>
                <span className="font-semibold">
                  {formatCurrency(paymentData.amount, paymentData.currency)}
                </span>
              </div>
              {paymentData.method && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Method</span>
                  <Badge variant="outline" className="text-xs">
                    {paymentData.method.toUpperCase()}
                  </Badge>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Date & Time</span>
                <span className="text-xs">{formatDate(paymentData.timestamp)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Booking Details */}
        {bookingData && (
          <div className="bg-white rounded-lg p-4 border">
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Booking Details
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Booking ID</span>
                <span className="font-mono text-xs">#{bookingData.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Property</span>
                <span className="font-medium text-right max-w-48 truncate">
                  {bookingData.propertyTitle}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date</span>
                <span>{bookingData.bookingDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type</span>
                <Badge variant="outline" className="text-xs">
                  {bookingData.bookingType === 'morning' ? '12h Access' : '24h Access'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Guests</span>
                <span>{bookingData.guests}</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-2">
          {status === 'success' && (
            <div className="space-y-2">
              {onDownloadReceipt && (
                <Button
                  onClick={onDownloadReceipt}
                  variant="outline"
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Receipt
                </Button>
              )}
              <Button
                onClick={onClose}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Continue
              </Button>
            </div>
          )}

          {status === 'failed' && (
            <div className="space-y-2">
              {onRetry && (
                <Button
                  onClick={onRetry}
                  className="w-full bg-[#4A6741] hover:bg-[#3A5235]"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              )}
              <Button
                onClick={onClose}
                variant="outline"
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          )}

          {status === 'pending' && (
            <div className="space-y-2">
              <Button
                onClick={onClose}
                variant="outline"
                className="w-full"
              >
                <Clock className="h-4 w-4 mr-2 animate-pulse" />
                Check Status Later
              </Button>
            </div>
          )}

          {status === 'cancelled' && (
            <div className="space-y-2">
              {onRetry && (
                <Button
                  onClick={onRetry}
                  className="w-full bg-[#4A6741] hover:bg-[#3A5235]"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Payment Again
                </Button>
              )}
              <Button
                onClick={onClose}
                variant="outline"
                className="w-full"
              >
                Cancel Booking
              </Button>
            </div>
          )}
        </div>

        {/* Help Text */}
        <div className="text-center text-xs text-gray-500 pt-2">
          {status === 'success' && (
            <p>Need help? Contact support with your booking ID #{bookingData?.id}</p>
          )}
          {status === 'failed' && (
            <p>If you continue to face issues, please contact our support team</p>
          )}
          {status === 'pending' && (
            <p>Your payment may take 5-10 minutes to process</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}