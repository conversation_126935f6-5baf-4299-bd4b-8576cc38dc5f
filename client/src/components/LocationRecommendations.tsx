import { Property } from "@shared/schema";
import PropertyCard from "./PropertyCard";
import { MapPin, TrendingUp } from "lucide-react";

interface LocationRecommendationsProps {
  properties: Property[];
  currentLocation: string | undefined;
}

export default function LocationRecommendations({ properties, currentLocation }: LocationRecommendationsProps) {
  // Group properties by location
  const locationGroups = properties.reduce((acc, property) => {
    const location = property.location;
    if (!acc[location]) {
      acc[location] = [];
    }
    acc[location].push(property);
    return acc;
  }, {} as Record<string, Property[]>);

  // Get popular locations (those with most properties)
  const popularLocations = Object.entries(locationGroups)
    .sort(([, a], [, b]) => b.length - a.length)
    .slice(0, 3);

  if (popularLocations.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-gradient-to-br from-[#F7F4F1] to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-heading text-3xl font-bold text-[#2D3C2D] mb-4">
            Popular Destinations
          </h2>
          <p className="text-[#605045] text-lg max-w-2xl mx-auto">
            Discover trending farmhouse locations loved by our guests
          </p>
        </div>

        <div className="space-y-12">
          {popularLocations.map(([location, locationProperties], index) => (
            <div key={location} className="bg-white rounded-2xl shadow-lg overflow-hidden">
              {/* Location Header */}
              <div className="bg-gradient-to-r from-[#4A6741] to-[#3A5131] text-white p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
                      <MapPin className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold">{location}</h3>
                      <p className="text-green-100">
                        {locationProperties.length} {locationProperties.length === 1 ? 'property' : 'properties'} available
                      </p>
                    </div>
                  </div>
                  
                  {index === 0 && (
                    <div className="flex items-center bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      <span className="text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Properties Grid */}
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {locationProperties.slice(0, 3).map((property) => (
                    <div key={property.id} className="transform hover:scale-105 transition-transform duration-300">
                      <PropertyCard property={property} />
                    </div>
                  ))}
                </div>
                
                {locationProperties.length > 3 && (
                  <div className="text-center mt-6">
                    <button className="text-[#4A6741] hover:text-[#3A5131] font-semibold flex items-center mx-auto">
                      View all {locationProperties.length} properties in {location}
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-[#4A6741] to-[#3A5131] text-white rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">Can't Find Your Perfect Location?</h3>
            <p className="text-green-100 mb-6">
              We're constantly adding new farmhouses in beautiful locations across India. 
              Let us know where you'd like to stay!
            </p>
            <button className="bg-white text-[#4A6741] px-8 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-300">
              Request New Location
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}