import { Component, ReactNode, ErrorInfo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Home } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { auditLogger, SecurityEventType } from "@/lib/audit";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  context?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorId: string | null;
  isRetrying: boolean;
}

export default class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: null,
      isRetrying: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error(`[ErrorBoundary] Caught error in ${this.props.context || 'unknown'}:`, error, errorInfo);

    auditLogger.logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, {
      errorType: 'react_error_boundary',
      errorMessage: error.message,
      errorName: error.name,
      errorStack: error.stack?.split('\n').slice(0, 10),
      componentStack: errorInfo.componentStack?.split('\n').slice(0, 10),
      context: this.props.context,
      errorId: this.state.errorId,
      retryCount: this.retryCount,
      userAgent: navigator.userAgent,
      url: window.location.href,
    });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    this.reportToMonitoring(error, errorInfo);
  }

  private reportToMonitoring(error: Error, errorInfo: ErrorInfo) {
    try {
      fetch('/api/monitoring/error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          errorId: this.state.errorId,
          error: {
            message: error.message,
            name: error.name,
            stack: error.stack,
          },
          errorInfo,
          context: this.props.context,
          metadata: {
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString(),
            retryCount: this.retryCount,
          },
        }),
      }).catch(reportingError => {
        console.error('[ErrorBoundary] Failed to report error:', reportingError);
      });
    } catch (reportingError) {
      console.error('[ErrorBoundary] Error reporting failed:', reportingError);
    }
  }

  private handleRetry = () => {
    if (this.retryCount >= this.maxRetries) {
      return;
    }

    this.setState({ isRetrying: true });
    this.retryCount++;

    auditLogger.logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, {
      action: 'error_boundary_retry',
      errorId: this.state.errorId,
      retryCount: this.retryCount,
      context: this.props.context,
    });

    setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorId: null,
        isRetrying: false,
      });
    }, 1000);
  };

  private getErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    if (error.message.includes('payment') || error.message.includes('transaction')) {
      return 'critical';
    }
    if (error.message.includes('auth') || error.message.includes('login')) {
      return 'high';
    }
    if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {
      return 'medium';
    }
    return 'low';
  }

  private getErrorDescription(error: Error): string {
    const severity = this.getErrorSeverity(error);
    
    switch (severity) {
      case 'critical':
        return "A critical error occurred during payment processing. Please contact support immediately.";
      case 'high':
        return "An authentication error occurred. Please refresh the page and try signing in again.";
      case 'medium':
        return "A loading error occurred. This usually resolves by refreshing the page.";
      default:
        return "An unexpected error occurred. Please try refreshing the page.";
    }
  }

  override render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorId, isRetrying } = this.state;
      const canRetry = this.retryCount < this.maxRetries;
      const severity = error ? this.getErrorSeverity(error) : 'low';

      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F7F4F1] to-white p-4">
          <div className="max-w-md w-full text-center">
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 ${
                severity === 'critical' ? 'bg-red-100' : 'bg-yellow-100'
              }`}>
                <AlertTriangle className={`w-8 h-8 ${
                  severity === 'critical' ? 'text-red-500' : 'text-yellow-600'
                }`} />
              </div>
              
              <h1 className="text-2xl font-bold text-[#2D3C2D] mb-4">
                {severity === 'critical' ? 'Critical Error' : 'Oops! Something went wrong'}
              </h1>
              
              <p className="text-[#605045] mb-6">
                {error ? this.getErrorDescription(error) : 'Our farmhouse booking system encountered an unexpected error.'}
              </p>
              
              {severity === 'critical' && (
                <Alert variant="destructive" className="mb-6 text-left">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Payment System Error</AlertTitle>
                  <AlertDescription>
                    If you were making a payment, please check your account and contact support before retrying.
                    Reference ID: {errorId?.slice(-8)}
                  </AlertDescription>
                </Alert>
              )}
              
              <div className="space-y-3">
                {canRetry && (
                  <Button
                    onClick={this.handleRetry}
                    disabled={isRetrying}
                    className="w-full bg-[#4A6741] hover:bg-[#3A5131] text-white flex items-center justify-center"
                  >
                    {isRetrying ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Retrying...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Try Again ({this.maxRetries - this.retryCount} attempts left)
                      </>
                    )}
                  </Button>
                )}
                
                <Button
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                  className="w-full border-[#4A6741] text-[#4A6741] hover:bg-[#4A6741] hover:text-white flex items-center justify-center"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </Button>
              </div>
              
              <p className="text-xs text-[#605045] mt-6">
                If the problem persists, please contact support with reference: {errorId?.slice(-8)}
              </p>

              {process.env.NODE_ENV === 'development' && error && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                    Technical Details (Dev Only)
                  </summary>
                  <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-800 overflow-auto text-left">
                    <div><strong>Error ID:</strong> {errorId}</div>
                    <div><strong>Context:</strong> {this.props.context || 'unknown'}</div>
                    <div><strong>Error:</strong> {error.name}: {error.message}</div>
                    {error.stack && (
                      <div className="mt-2">
                        <strong>Stack:</strong>
                        <pre className="mt-1 whitespace-pre-wrap text-xs">{error.stack.slice(0, 1000)}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

export const PaymentErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    context="payment"
    onError={(error) => {
      auditLogger.logSecurityEvent(SecurityEventType.PAYMENT_FAILURE, {
        errorSource: 'react_error_boundary',
        errorMessage: error.message,
        critical: true,
      });
    }}
  >
    {children}
  </ErrorBoundary>
);

export const AuthErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    context="authentication"
    onError={(error) => {
      auditLogger.logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, {
        activity: 'auth_component_error',
        errorMessage: error.message,
      });
    }}
  >
    {children}
  </ErrorBoundary>
);
