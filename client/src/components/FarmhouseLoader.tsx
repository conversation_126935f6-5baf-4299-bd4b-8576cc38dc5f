import { cn } from "@/lib/utils";

interface FarmhouseLoaderProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  type?: "tractor" | "barn" | "windmill" | "crops";
}

export default function FarmhouseLoader({ 
  size = "md", 
  className,
  type = "tractor" 
}: FarmhouseLoaderProps) {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  if (type === "tractor") {
    return (
      <div className={cn("flex items-center justify-center", className)}>
        <div className={cn("relative", sizeClasses[size])}>
          {/* Tractor SVG with moving animation */}
          <svg 
            className="animate-bounce" 
            viewBox="0 0 100 100" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
          >
            {/* Tractor Body */}
            <rect x="30" y="40" width="35" height="25" rx="3" fill="#4A6741" />
            {/* Cabin */}
            <rect x="50" y="25" width="15" height="15" rx="2" fill="#2D3C2D" />
            {/* Large Rear Wheel */}
            <circle cx="55" cy="70" r="12" fill="#8B4513" className="animate-spin" style={{animationDuration: '2s'}} />
            <circle cx="55" cy="70" r="8" fill="#654321" />
            {/* Small Front Wheel */}
            <circle cx="35" cy="70" r="8" fill="#8B4513" className="animate-spin" style={{animationDuration: '1.5s'}} />
            <circle cx="35" cy="70" r="5" fill="#654321" />
            {/* Exhaust */}
            <rect x="28" y="35" width="3" height="8" fill="#666" />
            <circle cx="29.5" cy="32" r="2" fill="#DDD" className="animate-pulse" />
          </svg>
        </div>
      </div>
    );
  }

  if (type === "barn") {
    return (
      <div className={cn("flex items-center justify-center", className)}>
        <div className={cn("relative", sizeClasses[size])}>
          <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Barn Base */}
            <rect x="20" y="50" width="60" height="40" fill="#8B4513" />
            {/* Barn Roof */}
            <polygon points="50,20 15,50 85,50" fill="#654321" className="animate-pulse" />
            {/* Door */}
            <rect x="40" y="60" width="20" height="30" fill="#4A6741" />
            {/* Window */}
            <circle cx="65" cy="65" r="5" fill="#FFD700" className="animate-pulse" />
            {/* Silo */}
            <rect x="85" y="30" width="10" height="60" fill="#C0C0C0" />
            <ellipse cx="90" cy="30" rx="5" ry="3" fill="#A0A0A0" />
          </svg>
        </div>
      </div>
    );
  }

  if (type === "windmill") {
    return (
      <div className={cn("flex items-center justify-center", className)}>
        <div className={cn("relative", sizeClasses[size])}>
          <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Windmill Tower */}
            <rect x="47" y="40" width="6" height="50" fill="#8B4513" />
            {/* Windmill Blades */}
            <g className="animate-spin origin-center" style={{transformOrigin: '50px 40px', animationDuration: '3s'}}>
              <rect x="48" y="15" width="4" height="25" fill="#4A6741" />
              <rect x="35" y="38" width="25" height="4" fill="#4A6741" />
              <rect x="48" y="40" width="4" height="25" fill="#4A6741" />
              <rect x="50" y="38" width="25" height="4" fill="#4A6741" />
            </g>
            {/* Center Hub */}
            <circle cx="50" cy="40" r="3" fill="#2D3C2D" />
          </svg>
        </div>
      </div>
    );
  }

  if (type === "crops") {
    return (
      <div className={cn("flex items-center justify-center", className)}>
        <div className={cn("relative flex space-x-1", sizeClasses[size])}>
          {[...Array(3)].map((_, i) => (
            <svg 
              key={i}
              className="animate-pulse" 
              style={{animationDelay: `${i * 0.2}s`}}
              viewBox="0 0 20 40" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* Crop Stem */}
              <rect x="9" y="20" width="2" height="20" fill="#228B22" />
              {/* Crop Head */}
              <ellipse cx="10" cy="15" rx="4" ry="8" fill="#FFD700" />
              {/* Leaves */}
              <ellipse cx="6" cy="25" rx="3" ry="2" fill="#32CD32" />
              <ellipse cx="14" cy="25" rx="3" ry="2" fill="#32CD32" />
            </svg>
          ))}
        </div>
      </div>
    );
  }

  return null;
}