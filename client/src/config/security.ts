// Security Configuration for Payment System
// This file defines security policies and configurations

export interface SecurityConfig {
  payment: {
    maxAttemptsPerHour: number;
    maxAmountFor2FA: number; // Amount in INR above which 2FA is required
    sessionTimeoutMinutes: number;
    maxTransactionAmount: number;
    suspiciousAmountThreshold: number;
  };
  session: {
    timeoutWarningMinutes: number;
    maxIdleMinutes: number;
    requireReauthForPayment: boolean;
  };
  rateLimit: {
    paymentAttempts: {
      maxAttempts: number;
      windowMinutes: number;
      blockDurationMinutes: number;
    };
    loginAttempts: {
      maxAttempts: number;
      windowMinutes: number;
      blockDurationMinutes: number;
    };
  };
  audit: {
    logPaymentEvents: boolean;
    logUserActions: boolean;
    logSecurityEvents: boolean;
    retentionDays: number;
  };
  fraud: {
    enableDetection: boolean;
    maxFailureRate: number; // Percentage
    velocityCheckMinutes: number;
    maxTransactionsPerDay: number;
  };
}

// Default security configuration - should be overridden by server config
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  payment: {
    maxAttemptsPerHour: 3,
    maxAmountFor2FA: 10000, // ₹10,000
    sessionTimeoutMinutes: 30,
    maxTransactionAmount: 100000, // ₹1,00,000
    suspiciousAmountThreshold: 50000, // ₹50,000
  },
  session: {
    timeoutWarningMinutes: 5,
    maxIdleMinutes: 30,
    requireReauthForPayment: true,
  },
  rateLimit: {
    paymentAttempts: {
      maxAttempts: 3,
      windowMinutes: 60,
      blockDurationMinutes: 60,
    },
    loginAttempts: {
      maxAttempts: 5,
      windowMinutes: 15,
      blockDurationMinutes: 30,
    },
  },
  audit: {
    logPaymentEvents: true,
    logUserActions: true,
    logSecurityEvents: true,
    retentionDays: 90,
  },
  fraud: {
    enableDetection: true,
    maxFailureRate: 20, // 20%
    velocityCheckMinutes: 10,
    maxTransactionsPerDay: 10,
  },
};

// Environment-specific security settings
export const getSecurityConfig = (): SecurityConfig => {
  // In production, this would fetch from secure backend endpoint
  // Never store sensitive security config in frontend
  return DEFAULT_SECURITY_CONFIG;
};

// Security event types for logging
export enum SecurityEventType {
  PAYMENT_ATTEMPT = 'PAYMENT_ATTEMPT',
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  PAYMENT_FAILURE = 'PAYMENT_FAILURE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  SESSION_TIMEOUT = 'SESSION_TIMEOUT',
  TWO_FA_REQUIRED = 'TWO_FA_REQUIRED',
  TWO_FA_SUCCESS = 'TWO_FA_SUCCESS',
  TWO_FA_FAILURE = 'TWO_FA_FAILURE',
  FRAUD_DETECTION = 'FRAUD_DETECTION',
}

// Security context for tracking user sessions
export interface SecurityContext {
  userId: string;
  sessionId: string;
  ipAddress?: string;
  userAgent?: string;
  lastActivity: Date;
  paymentAttempts: number;
  riskScore: number;
  requires2FA: boolean;
}

// Utility functions for security checks
export const isHighValueTransaction = (amount: number, config: SecurityConfig): boolean => {
  return amount >= config.payment.maxAmountFor2FA;
};

export const isSuspiciousAmount = (amount: number, config: SecurityConfig): boolean => {
  return amount >= config.payment.suspiciousAmountThreshold;
};

export const isMaxTransactionExceeded = (amount: number, config: SecurityConfig): boolean => {
  return amount > config.payment.maxTransactionAmount;
};

export const calculateRiskScore = (context: SecurityContext): number => {
  let riskScore = 0;
  
  // High payment attempts increase risk
  if (context.paymentAttempts > 2) riskScore += 30;
  if (context.paymentAttempts > 5) riskScore += 50;
  
  // Old sessions are riskier
  const sessionAge = Date.now() - context.lastActivity.getTime();
  if (sessionAge > 30 * 60 * 1000) riskScore += 20; // 30 minutes
  if (sessionAge > 60 * 60 * 1000) riskScore += 40; // 1 hour
  
  return Math.min(riskScore, 100);
};