// Booking and Payment Configuration
export interface BookingConfig {
  fees: {
    cleaning: number;
  };
  payment: {
    advancePercentage: number;
    gstRate: number;
  };
  gst: {
    defaultSupplierState: string;
    defaultRecipientState: string;
    defaultServiceType: string;
  };
  ui: {
    maxGuests: number;
    defaultBookingType: 'morning' | 'full_day';
    defaultPaymentMethod: 'advance' | 'full_amount';
  };
}

// Default configuration - can be overridden by props or API
export const DEFAULT_BOOKING_CONFIG: BookingConfig = {
  fees: {
    cleaning: 15,
  },
  payment: {
    advancePercentage: 30, // 30%
    gstRate: 18, // 18% GST
  },
  gst: {
    defaultSupplierState: "MH", // Maharashtra
    defaultRecipientState: "MH", // Maharashtra  
    defaultServiceType: "accommodation",
  },
  ui: {
    maxGuests: 6,
    defaultBookingType: 'morning',
    defaultPaymentMethod: 'advance',
  },
};

// Helper functions for UI display only - NOT for payment calculations
export const calculateAdvanceAmountDisplay = (
  totalPrice: number, 
  advancePercentage: number = DEFAULT_BOOKING_CONFIG.payment.advancePercentage
): number => {
  // WARNING: For display purposes only - server must calculate actual payment amounts
  return Math.round(totalPrice * (advancePercentage / 100));
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};