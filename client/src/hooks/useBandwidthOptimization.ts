/**
 * ✅ BANDWIDTH OPTIMIZATION HOOK: React integration for bandwidth optimization
 * 
 * This hook provides easy integration with the bandwidth optimizer service,
 * offering optimized fetch capabilities and real-time bandwidth monitoring.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { INTERVALS } from '../../../shared/constants';
import { bandwidthOptimizer, optimizedFetch, type BandwidthStats } from '../services/bandwidthOptimizer';

/**
 * Hook for bandwidth optimization and monitoring
 */
export const useBandwidthOptimization = () => {
  const [stats, setStats] = useState<BandwidthStats>(bandwidthOptimizer.getStats());
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Update stats periodically
  useEffect(() => {
    const updateStats = () => {
      setStats(bandwidthOptimizer.getStats());
    };

    const interval = setInterval(updateStats, INTERVALS.BANDWIDTH_MONITOR);
    
    return () => clearInterval(interval);
  }, []);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Optimized fetch function
  const fetch = useCallback(optimizedFetch, []);

  // Smart prefetch function
  const prefetch = useCallback(
    (urls: string | string[], priority: 'high' | 'medium' | 'low' = 'medium') => {
      const urlArray = Array.isArray(urls) ? urls : [urls];
      return bandwidthOptimizer.smartPrefetch(urlArray, priority);
    },
    []
  );

  // Get detailed stats
  const getDetailedStats = useCallback(() => {
    return bandwidthOptimizer.getDetailedStats();
  }, []);

  // Clear cache
  const clearCache = useCallback(() => {
    bandwidthOptimizer.clearCache();
  }, []);

  return {
    // Core functionality
    fetch,
    prefetch,
    
    // Monitoring
    stats,
    isOnline,
    getDetailedStats,
    
    // Cache management
    clearCache,
    
    // Computed values
    dataUsageOptimized: stats.totalBytesSaved > 0,
    compressionEffective: stats.compressionRatio > 1.1,
    cachePerformingWell: stats.cacheHitRate > 0.3,
    networkQuality: stats.networkQuality,
    adaptiveQuality: stats.adaptiveQuality
  };
};

/**
 * Hook for API-specific optimizations
 */
export const useOptimizedAPI = (baseURL: string = '/api') => {
  const { fetch } = useBandwidthOptimization();

  const optimizedAPI = useMemo(() => {
    const api = {
      get: async (endpoint: string, options?: RequestInit) => {
        return fetch(`${baseURL}${endpoint}`, {
          method: 'GET',
          ...options
        });
      },

      post: async (endpoint: string, data?: any, options?: RequestInit) => {
        const requestOptions: RequestInit = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...options?.headers
          },
          ...options
        };
        
        if (data) {
          requestOptions.body = JSON.stringify(data);
        }
        
        return fetch(`${baseURL}${endpoint}`, requestOptions);
      },

      put: async (endpoint: string, data?: any, options?: RequestInit) => {
        const requestOptions: RequestInit = {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...options?.headers
          },
          ...options
        };
        
        if (data) {
          requestOptions.body = JSON.stringify(data);
        }
        
        return fetch(`${baseURL}${endpoint}`, requestOptions);
      },

      delete: async (endpoint: string, options?: RequestInit) => {
        return fetch(`${baseURL}${endpoint}`, {
          method: 'DELETE',
          ...options
        });
      },

      // Batch multiple requests efficiently
      batch: async (requests: Array<{ endpoint: string; method: string; data?: any }>) => {
        return Promise.all(
          requests.map(req => {
            const method = req.method.toLowerCase();
            if (method === 'get') {
              return api.get(req.endpoint);
            } else if (method === 'post') {
              return api.post(req.endpoint, req.data);
            } else if (method === 'put') {
              return api.put(req.endpoint, req.data);
            } else if (method === 'delete') {
              return api.delete(req.endpoint);
            }
            throw new Error(`Unsupported method: ${method}`);
          })
        );
      }
    };
    
    return api;
  }, [fetch, baseURL]);

  return optimizedAPI;
};

/**
 * Hook for monitoring data usage in components
 */
export const useDataUsageMonitor = (componentName: string) => {
  const [dataUsage, setDataUsage] = useState({
    requests: 0,
    bytesSaved: 0,
    cacheHits: 0
  });

  const { stats } = useBandwidthOptimization();

  useEffect(() => {
    // Track component-specific data usage
    const prevRequests = dataUsage.requests;
    const newRequests = stats.totalRequests - prevRequests;
    
    if (newRequests > 0) {
      setDataUsage(prev => ({
        requests: stats.totalRequests,
        bytesSaved: stats.totalBytesSaved,
        cacheHits: Math.round(stats.cacheHitRate * stats.totalRequests)
      }));
    }
  }, [stats, componentName]);

  return {
    ...dataUsage,
    efficiency: dataUsage.requests > 0 ? dataUsage.bytesSaved / dataUsage.requests : 0
  };
};

/**
 * Hook for adaptive image loading based on network conditions
 */
export const useAdaptiveImageLoading = () => {
  const { stats } = useBandwidthOptimization();

  const getOptimalImageURL = useCallback((
    baseURL: string,
    sizes: { high: string; medium: string; low: string }
  ): string => {
    const quality = stats.adaptiveQuality;
    
    switch (quality) {
      case 'low':
        return sizes.low;
      case 'medium':
        return sizes.medium;
      case 'high':
      default:
        return sizes.high;
    }
  }, [stats.adaptiveQuality]);

  const shouldLoadImage = useCallback((priority: 'high' | 'medium' | 'low' = 'medium'): boolean => {
    if (stats.networkQuality === 'offline') return false;
    if (stats.networkQuality === 'poor' && priority === 'low') return false;
    return true;
  }, [stats.networkQuality]);

  return {
    getOptimalImageURL,
    shouldLoadImage,
    imageQuality: stats.adaptiveQuality
  };
};

/**
 * Hook for smart prefetching based on user behavior
 */
export const useSmartPrefetch = () => {
  const { prefetch, stats } = useBandwidthOptimization();

  const prefetchOnHover = useCallback((url: string) => {
    if (stats.networkQuality === 'excellent') {
      // Only prefetch on hover for excellent connections
      prefetch(url, 'low');
    }
  }, [prefetch, stats.networkQuality]);

  const prefetchCritical = useCallback((urls: string[]) => {
    if (stats.networkQuality !== 'poor') {
      prefetch(urls, 'high');
    }
  }, [prefetch, stats.networkQuality]);

  const prefetchRoute = useCallback((routePath: string) => {
    // Prefetch likely resources for a route
    const commonResources = [
      `/api/properties/route${routePath}`,
      `/api/bookings/route${routePath}`,
      `/api/user/route${routePath}`
    ];

    if (stats.networkQuality !== 'poor') {
      prefetch(commonResources, 'medium');
    }
  }, [prefetch, stats.networkQuality]);

  return {
    prefetchOnHover,
    prefetchCritical,
    prefetchRoute
  };
};