import { useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';

export interface BookingConflict {
  id: string;
  type: 'overlap' | 'double_booking' | 'maintenance_conflict' | 'blocked_dates';
  severity: 'critical' | 'high' | 'medium' | 'low';
  property: {
    id: number;
    name: string;
  };
  conflictingBookings: Array<{
    id: number;
    guestName: string;
    startDate: string;
    endDate: string;
    source: string;
    amount?: number;
  }>;
  suggestedResolution: {
    action: 'cancel_new' | 'cancel_existing' | 'modify_dates' | 'split_booking' | 'manual_review';
    description: string;
    impact: string;
  };
  newBooking?: {
    guestName: string;
    startDate: string;
    endDate: string;
    source: string;
    amount?: number;
  };
}

export interface ResolutionAction {
  action: 'cancel_new' | 'cancel_existing' | 'modify_dates' | 'split_booking' | 'manual_review';
  bookingId?: number;
  newDates?: {
    startDate: string;
    endDate: string;
  };
  notes?: string;
}

interface ConflictDetectionOptions {
  propertyId: number;
  startDate: string;
  endDate: string;
  excludeBookingId?: number;
}

export const useConflictResolution = () => {
  const [conflicts, setConflicts] = useState<BookingConflict[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);

  const detectConflicts = useCallback(async (options: ConflictDetectionOptions): Promise<BookingConflict[]> => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/calendar-advanced/conflicts/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(options)
      });

      if (!response.ok) {
        throw new Error('Failed to detect conflicts');
      }

      const data = await response.json();
      const detectedConflicts = data.data.conflicts || [];
      
      setConflicts(detectedConflicts);
      
      if (detectedConflicts.length > 0) {
        setModalOpen(true);
      }
      
      return detectedConflicts;
    } catch (error) {
      console.error('Error detecting conflicts:', error);
      toast.error('Failed to detect calendar conflicts');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  const resolveConflict = useCallback(async (conflictId: string, resolution: ResolutionAction): Promise<void> => {
    try {
      const response = await fetch(`/api/v1/calendar-advanced/conflicts/${conflictId}/resolve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ resolution })
      });

      if (!response.ok) {
        throw new Error('Failed to resolve conflict');
      }

      // Remove resolved conflict from state
      setConflicts(prev => prev.filter(c => c.id !== conflictId));
      
      toast.success('Conflict resolved successfully');

      // Close modal if no more conflicts
      if (conflicts.length <= 1) {
        setModalOpen(false);
      }
    } catch (error) {
      console.error('Error resolving conflict:', error);
      toast.error('Failed to resolve conflict');
      throw error;
    }
  }, [conflicts.length]);

  const autoResolveAll = useCallback(async (): Promise<void> => {
    try {
      const response = await fetch('/api/v1/calendar-advanced/conflicts/auto-resolve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ conflicts })
      });

      if (!response.ok) {
        throw new Error('Failed to auto-resolve conflicts');
      }

      const data = await response.json();
      const result = data.data.result;

      // Clear resolved conflicts
      const unresolvedConflicts = conflicts.filter(conflict => 
        !result.resolved.some((resolved: any) => resolved.conflictId === conflict.id)
      );
      
      setConflicts(unresolvedConflicts);
      
      toast.success(`${result.resolved.length} conflicts resolved automatically`);

      if (result.failed.length > 0) {
        toast.error(`${result.failed.length} conflicts require manual resolution`);
      }

      // Close modal if all conflicts resolved
      if (unresolvedConflicts.length === 0) {
        setModalOpen(false);
      }
    } catch (error) {
      console.error('Error auto-resolving conflicts:', error);
      toast.error('Failed to auto-resolve conflicts');
      throw error;
    }
  }, [conflicts]);

  const closeModal = useCallback(() => {
    setModalOpen(false);
  }, []);

  const checkForConflictsBeforeBooking = useCallback(async (bookingData: {
    propertyId: number;
    startDate: string;
    endDate: string;
    excludeBookingId?: number;
  }): Promise<boolean> => {
    const detectedConflicts = await detectConflicts(bookingData);
    return detectedConflicts.length === 0;
  }, [detectConflicts]);

  return {
    conflicts,
    loading,
    modalOpen,
    detectConflicts,
    resolveConflict,
    autoResolveAll,
    closeModal,
    checkForConflictsBeforeBooking,
    hasConflicts: conflicts.length > 0,
    criticalConflicts: conflicts.filter(c => c.severity === 'critical').length
  };
};