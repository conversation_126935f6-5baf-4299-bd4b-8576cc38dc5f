import React from 'react';
/**
 * ✅ UPDATE BATCHING HOOKS: React integration for batched updates
 * 
 * These hooks provide easy integration with the update batching system,
 * helping components optimize their re-render frequency and performance.
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { INTERVALS } from '../../../shared/constants';
import { 
  updateBatcher, 
  queueUpdate, 
  type UpdateOperation, 
  type BatchingStats 
} from '../services/updateBatcher';
import { queryClient } from '../lib/queryClient';

/**
 * Hook for batched state updates
 */
export const useBatchedState = <T>(
  initialState: T,
  options: {
    batchDelay?: number;
    priority?: 'critical' | 'high' | 'medium' | 'low';
    deduplication?: boolean;
  } = {}
) => {
  const [state, setState] = useState<T>(initialState);
  const stateRef = useRef<T>(state);
  const updateId = useRef(0);
  
  const { 
    batchDelay = 100, 
    priority = 'medium', 
    deduplication = true 
  } = options;

  // Update ref when state changes
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  const batchedSetState = useCallback((updater: T | ((prev: T) => T)) => {
    const currentId = ++updateId.current;
    const newState = typeof updater === 'function' 
      ? (updater as (prev: T) => T)(stateRef.current)
      : updater;

    // Queue the update
    queueUpdate({
      id: `state-${Date.now()}-${currentId}`,
      type: 'state-update',
      target: `component-state-${currentId}`,
      operation: 'set',
      data: newState,
      priority,
      timestamp: Date.now(),
      source: 'useBatchedState',
      metadata: { deduplication, batchDelay }
    });

    // Apply immediately to ref for consistency
    stateRef.current = newState;
    
    // Schedule React state update
    setTimeout(() => setState(newState), batchDelay);
  }, [priority, batchDelay, deduplication]);

  return [state, batchedSetState] as const;
};

/**
 * Hook for batched query updates
 */
export const useBatchedQuery = <TData = unknown>(
  queryKey: any[],
  options: {
    priority?: 'critical' | 'high' | 'medium' | 'low';
    mergeStrategy?: 'replace' | 'merge' | 'append';
  } = {}
) => {
  const { priority = 'medium', mergeStrategy = 'replace' } = options;
  const queryKeyString = JSON.stringify(queryKey);

  const updateQueryData = useCallback((data: TData | ((prev: TData | undefined) => TData)) => {
    const operation = typeof data === 'function' ? 'merge' : 'set';
    const updateData = typeof data === 'function' && 'call' in data
      ? (data as (prev: TData | undefined) => TData)(queryClient.getQueryData(queryKey))
      : data;

    queueUpdate({
      id: `query-${Date.now()}-${Math.random()}`,
      type: 'query-update',
      target: queryKeyString,
      operation,
      data: updateData,
      priority,
      timestamp: Date.now(),
      source: 'useBatchedQuery',
      metadata: { mergeStrategy }
    });
  }, [queryKeyString, priority, mergeStrategy]);

  const invalidateQuery = useCallback((options?: { priority?: 'critical' | 'high' | 'medium' | 'low' }) => {
    queueUpdate({
      id: `invalidate-${Date.now()}-${Math.random()}`,
      type: 'cache-invalidation',
      target: queryKeyString,
      operation: 'invalidate',
      priority: options?.priority || priority,
      timestamp: Date.now(),
      source: 'useBatchedQuery'
    });
  }, [queryKeyString, priority]);

  return {
    updateQueryData,
    invalidateQuery
  };
};

/**
 * Hook for batched UI updates (prevents excessive DOM updates)
 */
export const useBatchedUI = (
  componentName: string,
  options: {
    batchDelay?: number;
    priority?: 'critical' | 'high' | 'medium' | 'low';
  } = {}
) => {
  const [updateTrigger, setUpdateTrigger] = useState(0);
  const pendingUpdates = useRef<any[]>([]);
  const { batchDelay = 16, priority = 'low' } = options; // 16ms = ~60fps

  const queueUIUpdate = useCallback((updateData: any) => {
    pendingUpdates.current?.push(updateData);

    queueUpdate({
      id: `ui-${componentName}-${Date.now()}-${Math.random()}`,
      type: 'ui-update',
      target: componentName,
      operation: 'set',
      data: updateData,
      priority,
      timestamp: Date.now(),
      source: 'useBatchedUI',
      metadata: { batchDelay }
    });
  }, [componentName, priority, batchDelay]);

  const flushUpdates = useCallback(() => {
    if (pendingUpdates.current?.length > 0) {
      setUpdateTrigger(prev => prev + 1);
      pendingUpdates.current = [];
    }
  }, []);

  // Listen for batched UI updates
  useEffect(() => {
    const handleUIUpdate = (event: CustomEvent) => {
      const { operation } = event.detail;
      if (operation.target === componentName) {
        flushUpdates();
      }
    };

    window.addEventListener('batched-ui-update', handleUIUpdate as any);
    return () => window.removeEventListener('batched-ui-update', handleUIUpdate as any);
  }, [componentName, flushUpdates]);

  return {
    queueUIUpdate,
    flushUpdates,
    updateTrigger,
    pendingUpdatesCount: pendingUpdates.current?.length
  };
};

/**
 * Hook for monitoring update batching performance
 */
export const useUpdateBatchingStats = () => {
  const [stats, setStats] = useState<BatchingStats>(updateBatcher.getStats());

  useEffect(() => {
    const updateStats = () => {
      setStats(updateBatcher.getStats());
    };

    const interval = setInterval(updateStats, INTERVALS.BATCH_UPDATE);
    updateStats(); // Initial update

    return () => clearInterval(interval);
  }, []);

  const detailedStats = useMemo(() => updateBatcher.getDetailedStats(), [stats]);

  return {
    stats,
    detailedStats,
    batchingEfficiency: stats.batchingEfficiency,
    rendersSaved: stats.rendersSaved,
    isPerformingWell: stats.batchingEfficiency > 0.7 && stats.rendersSaved > 10
  };
};

/**
 * Hook for component-level render optimization
 */
export const useRenderOptimization = (
  componentName: string,
  dependencies: any[] = [],
  options: {
    maxRenderRate?: number; // Max renders per second
    batchSimilarUpdates?: boolean;
  } = {}
) => {
  const { maxRenderRate = 60, batchSimilarUpdates = true } = options;
  const lastRenderTime = useRef(0);
  const renderCount = useRef(0);
  const [shouldRender, setShouldRender] = useState(true);

  const minFrameTime = 1000 / maxRenderRate;

  useEffect(() => {
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;

    if (timeSinceLastRender < minFrameTime) {
      // Rate limit: delay this render
      const delay = minFrameTime - timeSinceLastRender;
      const timeoutId = setTimeout(() => {
        setShouldRender(prev => !prev);
        lastRenderTime.current = Date.now();
        renderCount.current++;
      }, delay);

      return () => clearTimeout(timeoutId);
    } else {
      // Allow immediate render
      lastRenderTime.current = now;
      renderCount.current++;
      return undefined; // No cleanup needed for immediate render
    }
  }, dependencies);

  // Register render callback for monitoring
  useEffect(() => {
    return updateBatcher.onRender(() => {
      // Track component renders for analytics
      queueUpdate({
        id: `render-${componentName}-${Date.now()}`,
        type: 'ui-update',
        target: `render-tracking-${componentName}`,
        operation: 'set',
        data: { renderCount: renderCount.current, timestamp: Date.now() },
        priority: 'low',
        timestamp: Date.now(),
        source: 'useRenderOptimization'
      });
    });
  }, [componentName]);

  return {
    shouldRender,
    renderCount: renderCount.current,
    averageRenderRate: renderCount.current > 0 ? 
      renderCount.current / ((Date.now() - lastRenderTime.current) / 1000) : 0
  };
};

/**
 * Hook for bulk operations with automatic batching
 */
export const useBulkOperations = () => {
  const operationQueue = useRef<(() => void)[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  const queueOperation = useCallback((operation: () => void) => {
    operationQueue.current?.push(operation);
  }, []);

  const executeBulk = useCallback(async (
    priority: 'critical' | 'high' | 'medium' | 'low' = 'medium'
  ) => {
    if (operationQueue.current?.length === 0 || isExecuting) {
      return;
    }

    setIsExecuting(true);
    
    const operations = [...operationQueue.current];
    operationQueue.current = [];

    // Queue bulk execution
    queueUpdate({
      id: `bulk-${Date.now()}`,
      type: 'state-update',
      target: 'bulk-operations',
      operation: 'set',
      data: { operationCount: operations.length },
      priority,
      timestamp: Date.now(),
      source: 'useBulkOperations'
    });

    // Execute all operations in batch
    try {
      operations.forEach(op => op());
    } finally {
      setIsExecuting(false);
    }
  }, [isExecuting]);

  const clearQueue = useCallback(() => {
    operationQueue.current = [];
  }, []);

  return {
    queueOperation,
    executeBulk,
    clearQueue,
    queueSize: operationQueue.current?.length,
    isExecuting
  };
};

/**
 * Hook for smart debouncing with batching
 */
export const useSmartDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number = 300,
  options: {
    priority?: 'critical' | 'high' | 'medium' | 'low';
    maxWait?: number;
    leading?: boolean;
    trailing?: boolean;
  } = {}
) => {
  const {
    priority = 'medium',
    maxWait,
    leading = false,
    trailing = true
  } = options;

  const lastCallTime = useRef<number>(0);
  const lastInvokeTime = useRef<number>(0);
  const timerId = useRef<NodeJS.Timeout>();
  const lastArgs = useRef<Parameters<T>>();

  const debouncedCallback = useCallback((...args: Parameters<T>) => {
    const time = Date.now();
    const timeSinceLastCall = time - lastCallTime.current;
    
    lastCallTime.current = time;
    lastArgs.current = args;

    const invokeFunc = () => {
      lastInvokeTime.current = time;
      
      // Queue the callback execution
      queueUpdate({
        id: `debounce-${Date.now()}-${Math.random()}`,
        type: 'state-update',
        target: 'debounced-callback',
        operation: 'set',
        data: { args },
        priority,
        timestamp: Date.now(),
        source: 'useSmartDebounce'
      });

      return callback(...args);
    };

    const shouldInvoke = () => {
      if (leading && timeSinceLastCall >= delay) return true;
      if (maxWait && (time - lastInvokeTime.current) >= maxWait) return true;
      return false;
    };

    if (shouldInvoke()) {
      if (timerId.current) {
        clearTimeout(timerId.current);
        timerId.current = undefined;
      }
      return invokeFunc();
    }

    if (trailing) {
      if (timerId.current) {
        clearTimeout(timerId.current);
      }
      timerId.current = setTimeout(invokeFunc, delay);
    }
  }, [callback, delay, priority, maxWait, leading, trailing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerId.current) {
        clearTimeout(timerId.current);
      }
    };
  }, []);

  const cancel = useCallback(() => {
    if (timerId.current) {
      clearTimeout(timerId.current);
      timerId.current = undefined;
    }
  }, []);

  const flush = useCallback(() => {
    if (timerId.current && lastArgs.current) {
      cancel();
      return callback(...lastArgs.current);
    }
  }, [callback, cancel]);

  return {
    debouncedCallback,
    cancel,
    flush,
    isPending: !!timerId.current
  };
};