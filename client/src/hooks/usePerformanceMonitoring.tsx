import React, { useState, useEffect, forwardRef } from 'react';
import { INTERVALS } from '../../../shared/constants';
import { 
  performanceMonitor, 
  type PerformanceMetric, 
  type APICallMetric, 
  type PerformanceSummary 
} from '../services/performanceMonitor';

/**
 * ✅ PERFORMANCE MONITORING: React hooks for tracking N+1 query optimizations
 */

/**
 * React hook for performance monitoring
 */
export const usePerformanceMonitoring = () => {
  const [summary, setSummary] = useState<PerformanceSummary | null>(null);

  useEffect(() => {
    const updateSummary = () => {
      setSummary(performanceMonitor.getPerformanceSummary());
    };

    const interval = setInterval(updateSummary, INTERVALS.STATS_UPDATE);
    
    // Initial update
    updateSummary();

    return () => {
      clearInterval(interval);
    };
  }, []);

  return {
    summary,
    recordMetric: (metric: PerformanceMetric) => performanceMonitor.recordMetric(metric),
    trackAPICall: (call: APICallMetric) => performanceMonitor.trackAPICall(call),
    measureDashboardLoad: (propertyCount: number) => 
      performanceMonitor.measureDashboardLoad(propertyCount),
    exportMetrics: () => performanceMonitor.exportMetrics()
  };
};

/**
 * Hook for measuring component render performance
 */
export const useRenderPerformance = (componentName: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      performanceMonitor.recordMetric({
        name: `${componentName}_render_time`,
        value: duration,
        unit: 'ms',
        timestamp: Date.now(),
        metadata: { componentName }
      });
    };
  }, [componentName]);
};

/**
 * Hook for measuring API call performance with automatic tracking
 */
export const useAPIPerformance = () => {
  const trackAPICall = (
    endpoint: string,
    method: string,
    startTime: number,
    response: Response,
    cached: boolean = false
  ) => {
    const duration = performance.now() - startTime;
    
    // Estimate response size (approximate)
    const size = response.headers.get('content-length') ? 
      parseInt(response.headers.get('content-length')!) : 
      0;
    
    performanceMonitor.trackAPICall({
      endpoint,
      method,
      duration,
      size,
      status: response.status,
      timestamp: Date.now(),
      cached
    });
  };

  const measureFetch = async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    const startTime = performance.now();
    const method = options.method || 'GET';
    
    try {
      const response = await fetch(url, options);
      trackAPICall(url, method, startTime, response);
      return response;
    } catch (error) {
      // Track failed requests too
      const duration = performance.now() - startTime;
      performanceMonitor.trackAPICall({
        endpoint: url,
        method,
        duration,
        size: 0,
        status: 0, // 0 indicates network error
        timestamp: Date.now(),
        cached: false
      });
      throw error;
    }
  };

  return { measureFetch, trackAPICall };
};

/**
 * Hook for measuring dashboard load performance
 */
export const useDashboardPerformance = (propertyCount: number) => {
  const [loadMetrics, setLoadMetrics] = useState<{
    totalLoadTime: number;
    apiCallsCount: number;
    efficiency: number;
  } | null>(null);

  const measureLoad = () => {
    const measurement = performanceMonitor.measureDashboardLoad(propertyCount);
    
    return {
      start: () => {
        measurement.startMeasurement();
      },
      end: () => {
        const metrics = measurement.endMeasurement();
        setLoadMetrics({
          totalLoadTime: metrics.totalLoadTime,
          apiCallsCount: metrics.apiCallsCount,
          efficiency: metrics.efficiency
        });
        return metrics;
      }
    };
  };

  return { loadMetrics, measureLoad };
};

/**
 * Hook for detecting N+1 query patterns
 */
export const useN1Detection = () => {
  const [n1Patterns, setN1Patterns] = useState<{
    count: number;
    lastDetected: number | null;
  }>({ count: 0, lastDetected: null });

  useEffect(() => {
    const checkForN1Patterns = () => {
      const summary = performanceMonitor.getPerformanceSummary();
      setN1Patterns({
        count: summary.n1PatternsDetected,
        lastDetected: summary.n1PatternsDetected > 0 ? Date.now() : null
      });
    };

    // Check every 5 seconds
    const interval = setInterval(checkForN1Patterns, INTERVALS.PERFORMANCE_CHECK);
    checkForN1Patterns(); // Initial check

    return () => clearInterval(interval);
  }, []);

  return n1Patterns;
};

/**
 * Higher-order component for automatic performance tracking
 */
export const withPerformanceTracking = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  return forwardRef<any, P>((props, ref) => {
    useRenderPerformance(componentName);
    return <WrappedComponent {...(props as P)} ref={ref} />;
  });
};