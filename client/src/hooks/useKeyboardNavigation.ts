import { useEffect, useCallback } from 'react'

interface KeyboardNavigationOptions {
  onEscape: (() => void) | undefined;
  onEnter: (() => void) | undefined;
  onArrowUp: (() => void) | undefined;
  onArrowDown: (() => void) | undefined;
  onArrowLeft: (() => void) | undefined;
  onArrowRight: (() => void) | undefined;
  onTab: (() => void) | undefined;
  enabled: boolean | undefined;
}

// New structured keyboard navigation hook
export const useKeyboardNavigation = (options: KeyboardNavigationOptions): void => {
  const memoizedCallback = useCallback((event: KeyboardEvent) => {
    if (!options || !options.enabled) return

    switch (event.key) {
      case 'Escape':
        options.onEscape?.()
        break
      case 'Enter':
        options.onEnter?.()
        break
      case 'ArrowUp':
        options.onArrowUp?.()
        break
      case 'ArrowDown':
        options.onArrowDown?.()
        break
      case 'ArrowLeft':
        options.onArrowLeft?.()
        break
      case 'ArrowRight':
        options.onArrowRight?.()
        break
      case 'Tab':
        options.onTab?.()
        break
    }
  }, [options])

  useEffect(() => {
    if (!options || !options.enabled) return

    document.addEventListener('keydown', memoizedCallback)

    return () => {
      document.removeEventListener('keydown', memoizedCallback)
    }
  }, [memoizedCallback, options?.enabled])
}

// Legacy callback-based hook for backward compatibility
export const useKeyboardNavigationLegacy = (callback?: (event: KeyboardEvent) => void): void => {
  const memoizedCallback = useCallback((event: KeyboardEvent) => {
    if (!callback) return

    const targetKeys = ['Enter', ' ', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Escape', 'Tab']

    if (targetKeys.includes(event.key)) {
      callback(event)
    }
  }, [callback])

  useEffect(() => {
    if (!callback) return

    document.addEventListener('keydown', memoizedCallback)

    return () => {
      document.removeEventListener('keydown', memoizedCallback)
    }
  }, [memoizedCallback, callback])
}

// Hook for carousel navigation
export function useCarouselKeyboard(
  currentIndex: number,
  totalItems: number,
  onNext: () => void,
  onPrevious: () => void,
  onClose?: () => void
) {
  useKeyboardNavigation({
    onArrowLeft: onPrevious,
    onArrowRight: onNext,
    onEscape: onClose || undefined,
    onEnter: undefined,
    onArrowUp: undefined,
    onArrowDown: undefined,
    onTab: undefined,
    enabled: true
  });
}
