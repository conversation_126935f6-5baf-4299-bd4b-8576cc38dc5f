import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Property } from "@shared/schema";

interface AuthorizationResult {
  canAccess: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canManage: boolean;
  canViewFinancials: boolean;
  showAction: (action: string) => boolean;
  handleUnauthorized: () => void;
}

export function useAuthorization(): AuthorizationResult {
  const { user, loading } = useAuth();
  const { toast } = useToast();

  const handleUnauthorized = () => {
    toast({
      title: "Access Denied",
      description: "You are not authorized to perform this action.",
      variant: "destructive",
      duration: 5000
    });
  };

  // Check if user is authenticated and has required role
  const isOwner = Boolean(!loading && user && user.role === 'owner');
  const isAdmin = Boolean(!loading && user && user.role === 'admin');
  const isAuthenticated = !loading && !!user;

  return {
    canAccess: isAuthenticated,
    canEdit: isOwner || isAdmin,
    canDelete: isOwner || isAdmin,
    canManage: isOwner || isAdmin,
    canViewFinancials: isOwner || isAdmin,
    showAction: (action: string) => {
      switch (action) {
        case 'view':
          return isAuthenticated;
        case 'edit':
        case 'update':
        case 'pricing':
        case 'media':
          return isOwner || isAdmin;
        case 'delete':
        case 'remove':
          return isOwner || isAdmin;
        case 'approve':
        case 'reject':
        case 'manage_bookings':
          return isOwner || isAdmin;
        case 'analytics':
        case 'revenue':
          return isOwner || isAdmin;
        default:
          return false;
      }
    },
    handleUnauthorized
  };
}

interface PropertyAuthorizationResult extends AuthorizationResult {
  canEditProperty: boolean;
  canDeleteProperty: boolean;
  canManageBookings: boolean;
  canUpdatePricing: boolean;
  canManageMedia: boolean;
  showPropertyAction: (action: string) => boolean;
}

export function usePropertyAuthorization(property?: Property | null): PropertyAuthorizationResult {
  const { user, loading } = useAuth();
  const { toast } = useToast();
  const baseAuth = useAuthorization();

  const handleUnauthorized = () => {
    toast({
      title: "Access Denied", 
      description: "You can only manage properties that you own.",
      variant: "destructive",
      duration: 5000
    });
  };

  // Check if user owns this specific property
  const ownsProperty = Boolean(!loading && user && property && property.ownerId === user.id);
  const isAdmin = Boolean(!loading && user && user.role === 'admin');
  const hasPropertyAccess = Boolean(ownsProperty || isAdmin);

  return {
    ...baseAuth,
    canEditProperty: hasPropertyAccess,
    canDeleteProperty: hasPropertyAccess,
    canManageBookings: hasPropertyAccess,
    canUpdatePricing: hasPropertyAccess,
    canManageMedia: hasPropertyAccess,
    showPropertyAction: (action: string) => {
      if (!property) return false;
      
      switch (action) {
        case 'view':
          return true; // Anyone can view properties
        case 'edit':
        case 'update':
        case 'pricing':
        case 'media':
        case 'delete':
          return hasPropertyAccess;
        default:
          return baseAuth.showAction(action);
      }
    },
    handleUnauthorized
  };
}

interface BookingAuthorizationResult extends AuthorizationResult {
  canApproveBooking: boolean;
  canRejectBooking: boolean;
  canViewBookingDetails: boolean;
  canManageBookingStatus: boolean;
  showBookingAction: (action: string, booking: any) => boolean;
}

export function useBookingAuthorization(): BookingAuthorizationResult {
  const { user, loading } = useAuth();
  const { toast } = useToast();
  const baseAuth = useAuthorization();

  const handleUnauthorized = () => {
    toast({
      title: "Access Denied",
      description: "You can only manage bookings for properties that you own.",
      variant: "destructive",
      duration: 5000
    });
  };

  const isOwner = Boolean(!loading && user && user.role === 'owner');
  const isAdmin = Boolean(!loading && user && user.role === 'admin');

  const canManageBooking = (booking: any) => {
    if (!user || !booking) return false;
    
    // Admins can manage any booking
    if (isAdmin) return true;
    
    // Owners can manage bookings for their properties
    if (isOwner && booking.property && booking.property.ownerId === user.id) {
      return true;
    }
    
    // Users can view their own bookings
    if (booking.userId === user.id) return true;
    
    return false;
  };

  return {
    ...baseAuth,
    canApproveBooking: isOwner || isAdmin,
    canRejectBooking: isOwner || isAdmin,
    canViewBookingDetails: isOwner || isAdmin,
    canManageBookingStatus: isOwner || isAdmin,
    showBookingAction: (action: string, booking: any) => {
      switch (action) {
        case 'view':
        case 'details':
          return canManageBooking(booking);
        case 'approve':
        case 'reject':
        case 'complete':
          return canManageBooking(booking) && booking.status !== 'cancelled';
        case 'cancel':
          return booking.userId === user?.id || canManageBooking(booking);
        default:
          return false;
      }
    },
    handleUnauthorized
  };
}

// Utility hook for API authorization headers
export function useAPIHeaders() {
  const { user } = useAuth();
  
  return {
    getAuthHeaders: () => {
      const token = localStorage.getItem('token');
      return {
        'Authorization': `Bearer ${token || ''}`,
        'Content-Type': 'application/json'
      };
    },
    getMultipartHeaders: () => {
      const token = localStorage.getItem('token');
      return {
        'Authorization': `Bearer ${token || ''}`
        // Don't set Content-Type for multipart, let browser set it with boundary
      };
    },
    hasValidToken: () => {
      const token = localStorage.getItem('token');
      return !!token && token !== '';
    },
    isAuthenticated: !!user
  };
}

// Error boundary hook for authorization errors
export function useAuthorizationErrorHandler() {
  const { toast } = useToast();
  
  return {
    handleAuthError: (error: any, context?: string) => {
      if (error?.status === 401 || error?.message?.includes('unauthorized')) {
        toast({
          title: "Authentication Required",
          description: "Please log in to access this feature.",
          variant: "destructive"
        });
        // Redirect to login
        window.location.href = '/login';
      } else if (error?.status === 403 || error?.message?.includes('forbidden')) {
        toast({
          title: "Access Denied",
          description: `You don't have permission to ${context || 'perform this action'}.`,
          variant: "destructive"
        });
      } else {
        toast({
          title: "Error",
          description: error?.message || "An unexpected error occurred.",
          variant: "destructive"
        });
      }
    },
    
    handleNetworkError: (error: any) => {
      toast({
        title: "Connection Error",
        description: "Please check your internet connection and try again.",
        variant: "destructive"
      });
    }
  };
}

export default useAuthorization;