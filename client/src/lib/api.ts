import { apiRequest } from "./queryClient";
import { Property, User, Booking } from "@shared/schema";
import { optimizedFetch } from "../services/bandwidthOptimizer";

// Auth API functions
export const login = async (email: string, password: string) => {
  const response = await apiRequest("POST", "/api/v1/auth/login", { email, password });
  const result = await response.json();
  return result.data || result; // Handle both structured and direct responses
};

export const register = async (userData: {
  username: string;
  fullName: string;
  email: string;
  password: string;
  role: string;
}) => {
  const response = await apiRequest("POST", "/api/v1/auth/register", userData);
  const result = await response.json();
  return result.data || result; // Handle both structured and direct responses
};

export const getCurrentUser = async (token: string) => {
  const response = await optimizedFetch("/api/v1/users/me", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch user data");
  }

  const result = await response.json();
  return result.data || result; // Handle both structured and direct responses
};

// Property API functions
export const getProperties = async (
  featured?: boolean,
  location?: string,
  date?: string
) => {
  const params = new URLSearchParams();
  if (featured) params.append("featured", "true");
  if (location) params.append("location", location);
  if (date) params.append("date", date);

  const response = await optimizedFetch(`/api/v1/properties?${params.toString()}`);

  if (!response.ok) {
    throw new Error("Failed to fetch properties");
  }

  const result = await response.json();
  return (result.data || result) as Property[]; // Handle both structured and direct responses
};

export const getProperty = async (id: number) => {
  const response = await optimizedFetch(`/api/v1/properties/${id}`);

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error("Property not found");
    }
    throw new Error("Failed to fetch property details");
  }

  return response.json() as Promise<Property>;
};

export const getOwnerProperties = async (token: string) => {
  const response = await optimizedFetch("/api/v1/users/me/properties", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch owner properties");
  }

  return response.json() as Promise<Property[]>;
};

export const createProperty = async (token: string, propertyData: any) => {
  const response = await apiRequest("POST", "/api/v1/properties", propertyData);

  return response.json() as Promise<Property>;
};

export const updateProperty = async (token: string, id: number, propertyData: any) => {
  const response = await apiRequest("PUT", `/api/v1/properties/${id}`, propertyData);

  return response.json() as Promise<Property>;
};

// Booking API functions
export const createBooking = async (token: string, bookingData: {
  propertyId: number;
  bookingDate: string;
  bookingType: "morning" | "full_day";
  guests: number;
}) => {
  const response = await apiRequest("POST", "/api/v1/bookings", bookingData);

  return response.json() as Promise<Booking>;
};

export const getUserBookings = async (token: string) => {
  const response = await optimizedFetch("/api/v1/users/me/bookings", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch user bookings");
  }

  return response.json() as Promise<Booking[]>;
};

export const getOwnerBookings = async (token: string) => {
  const response = await optimizedFetch("/api/v1/bookings?role=owner", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch owner bookings");
  }

  return response.json() as Promise<(Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } })[]>;
};

export const getPropertyBookings = async (propertyId: number) => {
  const response = await fetch(`/api/v1/properties/${propertyId}/availability`);
  
  if (!response.ok) {
    throw new Error("Failed to fetch property bookings");
  }

  return response.json() as Promise<{ date: string; type: string }[]>;
};

// ✅ PERFORMANCE: Batch API to fetch bookings for multiple properties at once
// This solves the N+1 query problem by making a single API call instead of one per property
export const getBatchPropertyBookings = async (
  token: string, 
  propertyIds: number[]
): Promise<{
  bookingsByProperty: Record<number, (Booking & { property: Property; guest: { id: number; fullName: string; username: string; email: string; phone: string | null } })[]>;
  propertyCount: number;
  totalBookings: number;
}> => {
  const response = await optimizedFetch("/api/v1/bookings/by-property-ids", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ propertyIds }),
  });

  if (!response.ok) {
    if (response.status === 401) {
      throw new Error("Unauthorized: Please log in again");
    }
    if (response.status === 403) {
      throw new Error("Forbidden: You can only view bookings for your own properties");
    }
    throw new Error("Failed to fetch batch property bookings");
  }

  const result = await response.json();
  return result.data || result;
};

export const checkAvailability = async (
  propertyId: number,
  date: string,
  type: "morning" | "full_day"
) => {
  const params = new URLSearchParams({
    date,
    type,
  });

  const response = await optimizedFetch(`/api/v1/properties/${propertyId}/availability?${params.toString()}`);

  if (!response.ok) {
    throw new Error("Failed to check availability");
  }

  return response.json() as Promise<{ available: boolean }>;
};

// Image upload
export const uploadImages = async (token: string, files: File[]) => {
  const formData = new FormData();
  files.forEach(file => {
    formData.append("images", file);
  });

  const response = await optimizedFetch("/api/v1/media/upload", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error("Failed to upload images");
  }

  return response.json() as Promise<{ urls: string[] }>;
};
export const api = {
  async getProperties(): Promise<Property[]> {
    try {
      const response = await optimizedFetch('/api/v1/properties');
      if (!response.ok) {
        console.warn('Failed to fetch properties:', response.status);
        return []; // Return empty array instead of throwing
      }
      const data = await response.json();
      // Ensure we always return an array
      return Array.isArray(data.data) ? data.data : [];
    } catch (error) {
      console.error('Error fetching properties:', error);
      return []; // Return empty array instead of throwing
    }
  },
}