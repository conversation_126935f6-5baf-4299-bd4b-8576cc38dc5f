import { z } from 'zod';
import { 
  validationSchemas,
  type UserRegistrationData,
  type UserLoginData,
  type OTPRequestData,
  type OTPVerificationData,
  type PropertyFormData,
  type BookingFormData,
  type ReviewFormData,
  type ReviewResponseData,
  type PropertySearchData,
  type ContactFormData,
  type ProfileUpdateData,
  type PasswordChangeData
} from '../../../shared/validations';
import { 
  safeValidate, 
  formatValidationError, 
  validateFile, 
  ValidationPresets 
} from '../../../shared/validation-utils';

// Client-side validation utilities
export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class ValidationService {
  // Generic validation method - use consolidated safe validate
  static validate<T>(schema: z.ZodSchema<T>, data: unknown): T {
    const result = safeValidate(schema, data);
    
    if (result.success) {
      return result.data;
    } else {
      const firstError = result.errors[0];
      throw new ValidationError(
        firstError.message,
        firstError.field,
        'validation_failed'
      );
    }
  }

  // Safe validation that returns result with errors - use consolidated utility
  static safeValidate<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: boolean;
    data?: T;
    errors?: Array<{ field: string; message: string }>;
  } {
    return safeValidate(schema, data) as any;
  }

  // Specific validation methods for each form
  static validateUserRegistration(data: unknown): UserRegistrationData {
    return this.validate(validationSchemas.userRegistration, data) as UserRegistrationData;
  }

  static validateUserLogin(data: unknown): UserLoginData {
    return this.validate(validationSchemas.userLogin, data);
  }

  static validateOTPRequest(data: unknown): OTPRequestData {
    return this.validate(validationSchemas.otpRequest, data);
  }

  static validateOTPVerification(data: unknown): OTPVerificationData {
    return this.validate(validationSchemas.otpVerification, data);
  }

  static validatePropertyForm(data: unknown): PropertyFormData {
    return this.validate(validationSchemas.propertyForm, data) as PropertyFormData;
  }

  static validateBookingForm(data: unknown): BookingFormData {
    return this.validate(validationSchemas.bookingForm, data);
  }

  static validateReviewForm(data: unknown): ReviewFormData {
    return this.validate(validationSchemas.reviewForm, data);
  }

  static validateReviewResponse(data: unknown): ReviewResponseData {
    return this.validate(validationSchemas.reviewResponse, data);
  }

  static validatePropertySearch(data: unknown): PropertySearchData {
    return this.validate(validationSchemas.propertySearch, data);
  }

  static validateContactForm(data: unknown): ContactFormData {
    return this.validate(validationSchemas.contactForm, data);
  }

  static validateProfileUpdate(data: unknown): ProfileUpdateData {
    return this.validate(validationSchemas.profileUpdate, data);
  }

  static validatePasswordChange(data: unknown): PasswordChangeData {
    return this.validate(validationSchemas.passwordChange, data);
  }

  // Real-time field validation for better UX
  static validateField<T>(
    schema: z.ZodSchema<T>, 
    fieldName: string, 
    value: unknown
  ): { isValid: boolean; error?: string } {
    try {
      // Create a minimal object to validate just this field
      // Use safeParse with full object validation instead of pick
      const testObject = { [fieldName]: value } as any;
      const result = schema.safeParse(testObject);
      
      if (result.success) {
        return { isValid: true };
      } else {
        const fieldError = result.error.errors.find(e => e.path.includes(fieldName));
        return { 
          isValid: false, 
          error: fieldError?.message || 'Invalid value' 
        };
      }
    } catch (error) {
      return { isValid: false, error: 'Validation error' };
    }
  }

  // Email validation helper
  static isValidEmail(email: string): boolean {
    try {
      validationSchemas.userRegistration.shape.email.parse(email);
      return true;
    } catch {
      return false;
    }
  }

  // Phone validation helper
  static isValidPhone(phone: string): boolean {
    try {
      validationSchemas.userRegistration.shape.phone.parse(phone);
      return true;
    } catch {
      return false;
    }
  }

  // Password strength checker
  static getPasswordStrength(password: string): {
    score: number;
    feedback: string[];
    isValid: boolean;
  } {
    const feedback: string[] = [];
    let score = 0;

    // Length check
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Password must be at least 8 characters');
    }

    // Uppercase check
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Add at least one uppercase letter');
    }

    // Lowercase check
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Add at least one lowercase letter');
    }

    // Number check
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Add at least one number');
    }

    // Special character check (bonus)
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    }

    const isValid = score >= 4;
    
    return { score, feedback, isValid };
  }

  // File validation for image uploads - use consolidated validation
  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    const result = validateFile(file);
    
    if (result.isValid) {
      return { isValid: true };
    } else {
      return { isValid: false, error: result.errors[0] };
    }
  }

  // Date validation helper
  static isValidFutureDate(date: string): boolean {
    try {
      const selectedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return selectedDate >= today;
    } catch {
      return false;
    }
  }

  // Price range validation
  static isValidPriceRange(minPrice?: number, maxPrice?: number): boolean {
    if (minPrice !== undefined && maxPrice !== undefined) {
      return maxPrice >= minPrice;
    }
    return true;
  }
}

// Export validation schemas for use in forms
export { validationSchemas };

// Export types for TypeScript
export type {
  UserRegistrationData,
  UserLoginData,
  OTPRequestData,
  OTPVerificationData,
  PropertyFormData,
  BookingFormData,
  ReviewFormData,
  ReviewResponseData,
  PropertySearchData,
  ContactFormData,
  ProfileUpdateData,
  PasswordChangeData
};