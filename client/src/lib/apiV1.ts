/**
 * Standardized API Client for v1 endpoints
 * Follows RESTful design principles with consistent response handling
 */

import { optimizedFetch } from "../services/bandwidthOptimizer";
import { Property, User, Booking } from "@shared/schema";

// Standardized API Response interface
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    timestamp: string;
    requestId: string;
  };
}

// Error class for API errors
export class ApiError extends Error {
  public code: string;
  public status: number;
  public details?: any;

  constructor(message: string, code: string, status: number, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.status = status;
    this.details = details;
  }
}

// Base API client class
class ApiClient {
  private baseUrl = '/api/v1';

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token') || '';
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    };

    // Remove Content-Type for FormData
    if (options.body instanceof FormData) {
      delete (config.headers as any)['Content-Type'];
    }

    const response = await optimizedFetch(`${this.baseUrl}${endpoint}`, config);
    
    if (!response.ok) {
      let errorData: any = {};
      try {
        errorData = await response.json();
      } catch {
        // If JSON parsing fails, create a generic error
        errorData = {
          success: false,
          message: `Request failed with status ${response.status}`,
          error: {
            code: 'NETWORK_ERROR',
            message: response.statusText
          }
        };
      }

      // Handle authentication errors
      if (response.status === 401) {
        localStorage.removeItem('token');
        throw new ApiError(
          'Authentication required. Please log in again.',
          'AUTHENTICATION_ERROR',
          401,
          errorData.error?.details
        );
      }

      throw new ApiError(
        errorData.message || 'Request failed',
        errorData.error?.code || 'API_ERROR',
        response.status,
        errorData.error?.details
      );
    }

    const result: ApiResponse<T> = await response.json();
    
    // Return data if available, otherwise return the whole response
    return result.data !== undefined ? result.data : (result as any);
  }

  // Authentication endpoints
  auth = {
    login: async (email: string, password: string) => {
      return this.request<{ user: User; token: string }>('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });
    },

    register: async (userData: {
      username: string;
      fullName: string;
      email: string;
      password: string;
      role: string;
      consentData?: any;
    }) => {
      return this.request<{ user: User; token: string }>('/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData),
      });
    },

    refresh: async () => {
      return this.request<{ token: string }>('/auth/refresh', {
        method: 'POST',
      });
    },

    logout: async () => {
      return this.request<void>('/auth/logout', {
        method: 'DELETE',
      });
    },

    logoutAll: async () => {
      return this.request<void>('/auth/sessions', {
        method: 'DELETE',
      });
    },
  };

  // User management endpoints
  users = {
    me: async () => {
      return this.request<User>('/users/me');
    },

    updateMe: async (userData: Partial<User>) => {
      return this.request<User>('/users/me', {
        method: 'PUT',
        body: JSON.stringify(userData),
      });
    },

    getMyProperties: async () => {
      return this.request<Property[]>('/users/me/properties');
    },

    getMyBookings: async () => {
      return this.request<Booking[]>('/users/me/bookings');
    },

    getMyReviews: async () => {
      return this.request<any[]>('/users/me/reviews');
    },
  };

  // Properties endpoints
  properties = {
    list: async (params?: {
      featured?: boolean;
      location?: string;
      date?: string;
      minPrice?: number;
      maxPrice?: number;
      amenities?: string[];
      page?: number;
      limit?: number;
    }) => {
      const searchParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              searchParams.append(key, value.join(','));
            } else {
              searchParams.append(key, String(value));
            }
          }
        });
      }
      
      const query = searchParams.toString();
      return this.request<Property[]>(`/properties${query ? `?${query}` : ''}`);
    },

    get: async (id: number) => {
      return this.request<Property>(`/properties/${id}`);
    },

    create: async (propertyData: any) => {
      return this.request<Property>('/properties', {
        method: 'POST',
        body: JSON.stringify(propertyData),
      });
    },

    update: async (id: number, propertyData: any) => {
      return this.request<Property>(`/properties/${id}`, {
        method: 'PUT',
        body: JSON.stringify(propertyData),
      });
    },

    delete: async (id: number) => {
      return this.request<void>(`/properties/${id}`, {
        method: 'DELETE',
      });
    },

    getAvailability: async (id: number, date?: string) => {
      const query = date ? `?date=${date}` : '';
      return this.request<any>(`/properties/${id}/availability${query}`);
    },

    getBookings: async (id: number) => {
      return this.request<Booking[]>(`/properties/${id}/bookings`);
    },

    getReviews: async (id: number) => {
      return this.request<any>(`/properties/${id}/reviews`);
    },

    uploadMedia: async (id: number, files: File[]) => {
      const formData = new FormData();
      files.forEach(file => formData.append('images', file));
      
      return this.request<any>(`/properties/${id}/media`, {
        method: 'POST',
        body: formData,
      });
    },

    updateMedia: async (id: number, mediaId: string, mediaData: any) => {
      return this.request<any>(`/properties/${id}/media/${mediaId}`, {
        method: 'PUT',
        body: JSON.stringify(mediaData),
      });
    },

    deleteMedia: async (id: number, mediaId: string) => {
      return this.request<void>(`/properties/${id}/media/${mediaId}`, {
        method: 'DELETE',
      });
    },

    getPricing: async (id: number, date?: string) => {
      const query = date ? `?date=${date}` : '';
      return this.request<any>(`/properties/${id}/pricing${query}`);
    },

    updatePricing: async (id: number, pricingData: any) => {
      return this.request<any>(`/properties/${id}/pricing`, {
        method: 'PUT',
        body: JSON.stringify(pricingData),
      });
    },
  };

  // Bookings endpoints
  bookings = {
    list: async (params?: { page?: number; limit?: number }) => {
      const searchParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            searchParams.append(key, String(value));
          }
        });
      }
      
      const query = searchParams.toString();
      return this.request<Booking[]>(`/bookings${query ? `?${query}` : ''}`);
    },

    get: async (id: number) => {
      return this.request<Booking>(`/bookings/${id}`);
    },

    create: async (bookingData: {
      propertyId: number;
      bookingDate: string;
      bookingType: 'morning' | 'full_day';
      guests: number;
      paymentMethod?: string;
    }) => {
      return this.request<Booking>('/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData),
      });
    },

    update: async (id: number, bookingData: any) => {
      return this.request<Booking>(`/bookings/${id}`, {
        method: 'PUT',
        body: JSON.stringify(bookingData),
      });
    },

    cancel: async (id: number) => {
      return this.request<void>(`/bookings/${id}`, {
        method: 'DELETE',
      });
    },

    updateStatus: async (id: number, status: string) => {
      return this.request<Booking>(`/bookings/${id}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status }),
      });
    },

    getPayments: async (id: number) => {
      return this.request<any[]>(`/bookings/${id}/payments`);
    },

    addReview: async (id: number, reviewData: any) => {
      return this.request<any>(`/bookings/${id}/reviews`, {
        method: 'POST',
        body: JSON.stringify(reviewData),
      });
    },
  };

  // Reviews endpoints
  reviews = {
    list: async () => {
      return this.request<any[]>('/reviews');
    },

    get: async (id: number) => {
      return this.request<any>(`/reviews/${id}`);
    },

    create: async (reviewData: any) => {
      return this.request<any>('/reviews', {
        method: 'POST',
        body: JSON.stringify(reviewData),
      });
    },

    update: async (id: number, reviewData: any) => {
      return this.request<any>(`/reviews/${id}`, {
        method: 'PUT',
        body: JSON.stringify(reviewData),
      });
    },

    delete: async (id: number) => {
      return this.request<void>(`/reviews/${id}`, {
        method: 'DELETE',
      });
    },

    addResponse: async (id: number, response: string) => {
      return this.request<any>(`/reviews/${id}/response`, {
        method: 'PUT',
        body: JSON.stringify({ response }),
      });
    },
  };

  // Payment endpoints
  payments = {
    createOrder: async (orderData: any) => {
      return this.request<any>('/payments/orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });
    },

    process: async (paymentData: any) => {
      return this.request<any>('/payments/process', {
        method: 'POST',
        body: JSON.stringify(paymentData),
      });
    },

    get: async (id: string) => {
      return this.request<any>(`/payments/${id}`);
    },

    refund: async (id: string, refundData: any) => {
      return this.request<any>(`/payments/${id}/refund`, {
        method: 'POST',
        body: JSON.stringify(refundData),
      });
    },
  };

  // Media endpoints
  media = {
    upload: async (files: File[]) => {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      
      return this.request<any>('/media/upload', {
        method: 'POST',
        body: formData,
      });
    },

    get: async (id: string) => {
      return this.request<any>(`/media/${id}`);
    },

    delete: async (id: string) => {
      return this.request<void>(`/media/${id}`, {
        method: 'DELETE',
      });
    },

    bulkUpload: async (files: File[]) => {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      
      return this.request<any>('/media/bulk-upload', {
        method: 'POST',
        body: formData,
      });
    },
  };

  // Utility methods
  checkAvailability = async (propertyId: number, date: string, type: 'morning' | 'full_day') => {
    const params = new URLSearchParams({ date, type });
    return this.request<{ available: boolean }>(`/properties/${propertyId}/availability?${params}`);
  };
}

// Export singleton instance
export const apiV1 = new ApiClient();

// Export legacy compatibility functions
export const {
  auth: authApi,
  users: usersApi,
  properties: propertiesApi,
  bookings: bookingsApi,
  reviews: reviewsApi,
  payments: paymentsApi,
  media: mediaApi,
} = apiV1;

export default apiV1;