/**
 * Image proxy utility to handle external images that might be blocked by network policies
 */

/**
 * Converts an external image URL to use our image proxy
 * This helps bypass network blocking issues like "orb network error"
 */
export function getProxiedImageUrl(originalUrl: string): string {
  // Check if it's already a local URL (starts with /)
  if (originalUrl.startsWith('/')) {
    return originalUrl;
  }
  
  // Check if it's a data URL
  if (originalUrl.startsWith('data:')) {
    return originalUrl;
  }
  
  // Check if it's already using our proxy
  if (originalUrl.includes('/api/proxy-image')) {
    return originalUrl;
  }
  
  // List of domains that might be blocked
  const blockedDomains = [
    'images.unsplash.com',
    'unsplash.com'
  ];
  
  try {
    const urlObj = new URL(originalUrl);
    
    // If it's a potentially blocked domain, use our proxy
    if (blockedDomains.includes(urlObj.hostname)) {
      return `/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
    }
    
    // Otherwise, return the original URL
    return originalUrl;
  } catch (error) {
    // If URL parsing fails, return original
    console.warn('Failed to parse image URL:', originalUrl);
    return originalUrl;
  }
}

/**
 * Processes an array of image URLs to use proxy where needed
 */
export function getProxiedImageUrls(urls: string[]): string[] {
  return urls.map(getProxiedImageUrl);
}