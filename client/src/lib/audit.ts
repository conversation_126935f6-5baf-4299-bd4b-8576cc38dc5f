// Audit Logging System for Security Events
// This handles client-side audit event collection and forwarding to backend

import { SecurityEventType, SecurityContext } from "@/config/security";

export interface AuditEvent {
  id: string;
  type: SecurityEventType;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  details: Record<string, any>;
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    url: string;
    referrer?: string;
  };
  riskScore?: number;
}

export interface PaymentAuditDetails {
  bookingId: number;
  propertyId: number;
  amount: number;
  paymentMethod: string;
  orderId?: string;
  paymentId?: string;
  errorCode?: string;
  errorMessage?: string;
  processingTime?: number;
}

class AuditLogger {
  private events: AuditEvent[] = [];
  private batchSize = 10;
  private flushInterval = 30000; // 30 seconds
  private maxRetries = 3;

  constructor() {
    // Start periodic flush
    setInterval(() => {
      this.flush();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flush(true); // Synchronous flush on unload
    });
  }

  // Log security events
  logSecurityEvent(
    type: SecurityEventType,
    details: Record<string, any>,
    context?: SecurityContext,
    riskScore?: number
  ): void {
    const event: AuditEvent = {
      id: this.generateEventId(),
      type,
      timestamp: new Date(),
      ...(context?.userId && { userId: context.userId }),
      ...(context?.sessionId && { sessionId: context.sessionId }),
      details: this.sanitizeDetails(details),
      metadata: {
        ...(context?.ipAddress && { ipAddress: context.ipAddress }),
        userAgent: context?.userAgent || navigator.userAgent,
        url: window.location.href,
        referrer: document.referrer,
      },
      ...(riskScore && { riskScore }),
      ...(context?.riskScore && !riskScore && { riskScore: context.riskScore }),
    };

    this.events.push(event);
    console.log(`[AUDIT] ${type}:`, event);

    // Immediate flush for critical events
    if (this.isCriticalEvent(type)) {
      this.flush();
    }

    // Auto-flush if batch is full
    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  // Log payment-specific events with structured data
  logPaymentEvent(
    type: SecurityEventType,
    paymentDetails: PaymentAuditDetails,
    context?: SecurityContext,
    error?: Error
  ): void {
    const details: Record<string, any> = {
      ...paymentDetails,
      timestamp: new Date().toISOString(),
    };

    if (error) {
      details.error = {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5), // Limit stack trace
      };
    }

    this.logSecurityEvent(type, details, context);
  }

  // Flush events to backend
  private async flush(synchronous = false): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToFlush = [...this.events];
    this.events = [];

    try {
      const response = await fetch('/api/audit/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          events: eventsToFlush,
          clientTimestamp: new Date().toISOString(),
        }),
        ...(synchronous && { keepalive: true }), // For page unload
      });

      if (!response.ok) {
        throw new Error(`Audit flush failed: ${response.status}`);
      }
    } catch (error) {
      console.error('[AUDIT] Failed to flush events:', error);
      
      // Re-queue events for retry (unless it's a critical error)
      if (!synchronous && eventsToFlush.length < 100) {
        this.events.unshift(...eventsToFlush);
      }
    }
  }

  // Generate unique event ID
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Sanitize sensitive data from audit details
  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    const sanitized = { ...details };
    
    // Remove sensitive fields
    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'signature',
      'razorpay_signature',
      'card_number',
      'cvv',
      'pin',
    ];
    
    sensitiveFields.forEach(field => {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    });

    // Truncate long strings
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'string' && sanitized[key].length > 1000) {
        sanitized[key] = sanitized[key].substring(0, 1000) + '...[TRUNCATED]';
      }
    });

    return sanitized;
  }

  // Check if event requires immediate flushing
  private isCriticalEvent(type: SecurityEventType): boolean {
    const criticalEvents = [
      SecurityEventType.PAYMENT_SUCCESS,
      SecurityEventType.FRAUD_DETECTION,
      SecurityEventType.RATE_LIMIT_EXCEEDED,
      SecurityEventType.SUSPICIOUS_ACTIVITY,
    ];
    return criticalEvents.includes(type);
  }

  // Force flush all pending events
  forceFlush(): Promise<void> {
    return this.flush();
  }

  // Get pending events count (for debugging)
  getPendingCount(): number {
    return this.events.length;
  }
}

// Singleton instance
export const auditLogger = new AuditLogger();

// Convenience functions for common audit events
export const logPaymentAttempt = (details: PaymentAuditDetails, context?: SecurityContext) => {
  auditLogger.logPaymentEvent(SecurityEventType.PAYMENT_ATTEMPT, details, context);
};

export const logPaymentSuccess = (details: PaymentAuditDetails, context?: SecurityContext) => {
  auditLogger.logPaymentEvent(SecurityEventType.PAYMENT_SUCCESS, details, context);
};

export const logPaymentFailure = (details: PaymentAuditDetails, context?: SecurityContext, error?: Error) => {
  auditLogger.logPaymentEvent(SecurityEventType.PAYMENT_FAILURE, details, context, error);
};

export const logSuspiciousActivity = (details: Record<string, any>, context?: SecurityContext) => {
  auditLogger.logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, details, context);
};

export const logRateLimitExceeded = (details: Record<string, any>, context?: SecurityContext) => {
  auditLogger.logSecurityEvent(SecurityEventType.RATE_LIMIT_EXCEEDED, details, context);
};

export { SecurityEventType };
