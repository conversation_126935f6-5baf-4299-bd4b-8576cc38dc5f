// Secure Session Management for Payment Security
// Handles session timeouts, security context, and payment session management

import { SecurityContext, SecurityConfig, getSecurityConfig, calculateRiskScore } from "@/config/security";
import { auditLogger, SecurityEventType } from "./audit";

export interface SessionState {
  isActive: boolean;
  lastActivity: Date;
  warningShown: boolean;
  paymentSession?: PaymentSession;
}

export interface PaymentSession {
  id: string;
  bookingId: number;
  amount: number;
  startTime: Date;
  expiryTime: Date;
  requires2FA: boolean;
  attempts: number;
  isLocked: boolean;
}

class SessionManager {
  private securityContext: SecurityContext | null = null;
  private sessionState: SessionState = {
    isActive: true,
    lastActivity: new Date(),
    warningShown: false,
  };
  
  private config: SecurityConfig;
  private timeoutWarningTimer?: NodeJS.Timeout;
  private sessionTimeoutTimer?: NodeJS.Timeout;
  private activityListeners: (() => void)[] = [];

  constructor() {
    this.config = getSecurityConfig();
    this.setupActivityTracking();
    this.startSessionMonitoring();
  }

  // Initialize security context
  initializeContext(userId: string, sessionId: string): void {
    this.securityContext = {
      userId,
      sessionId,
      // ipAddress omitted - will be set by backend
      userAgent: navigator.userAgent,
      lastActivity: new Date(),
      paymentAttempts: 0,
      riskScore: 0,
      requires2FA: false,
    };

    this.updateActivity();
    this.auditLog(SecurityEventType.SESSION_TIMEOUT, { action: 'session_initialized' });
  }

  // Start a payment session with security checks
  startPaymentSession(bookingId: number, amount: number): PaymentSession | null {
    if (!this.securityContext) {
      throw new Error('Security context not initialized');
    }

    // Check if user is rate limited
    if (this.isRateLimited()) {
      this.auditLog(SecurityEventType.RATE_LIMIT_EXCEEDED, {
        bookingId,
        amount,
        attempts: this.securityContext.paymentAttempts,
      });
      return null;
    }

    // Check session validity
    if (!this.isSessionValid()) {
      this.auditLog(SecurityEventType.SESSION_TIMEOUT, {
        bookingId,
        amount,
        sessionAge: Date.now() - this.sessionState.lastActivity.getTime(),
      });
      return null;
    }

    const sessionId = this.generateSessionId();
    const expiryTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    const paymentSession: PaymentSession = {
      id: sessionId,
      bookingId,
      amount,
      startTime: new Date(),
      expiryTime,
      requires2FA: amount >= this.config.payment.maxAmountFor2FA,
      attempts: 0,
      isLocked: false,
    };

    this.sessionState.paymentSession = paymentSession;
    
    // Update security context
    this.securityContext.paymentAttempts++;
    this.securityContext.riskScore = calculateRiskScore(this.securityContext);

    this.auditLog(SecurityEventType.PAYMENT_ATTEMPT, {
      paymentSessionId: sessionId,
      bookingId,
      amount,
      requires2FA: paymentSession.requires2FA,
      riskScore: this.securityContext.riskScore,
    });

    return paymentSession;
  }

  // Record payment attempt
  recordPaymentAttempt(success: boolean, errorCode?: string): void {
    if (!this.sessionState.paymentSession || !this.securityContext) {
      return;
    }

    const session = this.sessionState.paymentSession;
    session.attempts++;

    if (success) {
      this.auditLog(SecurityEventType.PAYMENT_SUCCESS, {
        paymentSessionId: session.id,
        bookingId: session.bookingId,
        amount: session.amount,
        attempts: session.attempts,
      });
      
      // Reset payment attempts on success
      this.securityContext.paymentAttempts = 0;
    } else {
      this.auditLog(SecurityEventType.PAYMENT_FAILURE, {
        paymentSessionId: session.id,
        bookingId: session.bookingId,
        amount: session.amount,
        attempts: session.attempts,
        errorCode,
      });

      // Lock session if too many attempts
      if (session.attempts >= this.config.payment.maxAttemptsPerHour) {
        session.isLocked = true;
        this.auditLog(SecurityEventType.RATE_LIMIT_EXCEEDED, {
          paymentSessionId: session.id,
          attempts: session.attempts,
        });
      }
    }

    // Update risk score
    this.securityContext.riskScore = calculateRiskScore(this.securityContext);
  }

  // Check if current session is valid
  isSessionValid(): boolean {
    if (!this.sessionState.isActive) return false;

    const sessionAge = Date.now() - this.sessionState.lastActivity.getTime();
    const maxAge = this.config.session.maxIdleMinutes * 60 * 1000;

    return sessionAge < maxAge;
  }

  // Check if user is rate limited
  isRateLimited(): boolean {
    if (!this.securityContext) return true;

    const maxAttempts = this.config.rateLimit.paymentAttempts.maxAttempts;
    const isLimited = this.securityContext.paymentAttempts >= maxAttempts;
    
    // TEMP: Bypass rate limiting in development for testing
    const isDev = import.meta.env.DEV || 
                  import.meta.env.MODE === 'development' || 
                  process.env.NODE_ENV === 'development' ||
                  window.location.hostname === 'localhost' ||
                  window.location.hostname.includes('localhost');
    
    if (isDev) {
      console.log('🔧 [DEV] Rate limiting bypassed in development mode');
      return false;
    }
    
    return isLimited;
  }

  // TEMP DEBUG: Reset payment attempts for testing
  resetPaymentAttempts(): void {
    if (this.securityContext) {
      console.log('🔧 [DEV] Resetting payment attempts from', this.securityContext.paymentAttempts, 'to 0');
      this.securityContext.paymentAttempts = 0;
      this.securityContext.riskScore = calculateRiskScore(this.securityContext);
    }
  }

  // Check if payment session is expired
  isPaymentSessionExpired(): boolean {
    if (!this.sessionState.paymentSession) return true;
    return new Date() > this.sessionState.paymentSession.expiryTime;
  }

  // Get security context
  getSecurityContext(): SecurityContext | null {
    return this.securityContext;
  }

  // Get current payment session
  getPaymentSession(): PaymentSession | null {
    return this.sessionState.paymentSession || null;
  }

  // Update last activity
  updateActivity(): void {
    this.sessionState.lastActivity = new Date();
    this.sessionState.warningShown = false;
    
    if (this.securityContext) {
      this.securityContext.lastActivity = new Date();
    }

    this.resetTimeoutTimers();
  }

  // End payment session
  endPaymentSession(): void {
    if (this.sessionState.paymentSession) {
      this.auditLog(SecurityEventType.SESSION_TIMEOUT, {
        action: 'payment_session_ended',
        paymentSessionId: this.sessionState.paymentSession.id,
      });
    }
    this.sessionState.paymentSession = null as any;
  }

  // Force session expiry
  expireSession(): void {
    this.sessionState.isActive = false;
    this.endPaymentSession();
    
    if (this.timeoutWarningTimer) clearTimeout(this.timeoutWarningTimer);
    if (this.sessionTimeoutTimer) clearTimeout(this.sessionTimeoutTimer);

    this.auditLog(SecurityEventType.SESSION_TIMEOUT, {
      action: 'session_expired',
    });

    // Notify listeners
    this.activityListeners.forEach(listener => listener());
  }

  // Add activity listener
  onActivityChange(listener: () => void): void {
    this.activityListeners.push(listener);
  }

  // Remove activity listener
  removeActivityListener(listener: () => void): void {
    const index = this.activityListeners.indexOf(listener);
    if (index > -1) {
      this.activityListeners.splice(index, 1);
    }
  }

  // Private methods
  private setupActivityTracking(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        if (this.sessionState.isActive) {
          this.updateActivity();
        }
      }, { passive: true });
    });
  }

  private startSessionMonitoring(): void {
    this.resetTimeoutTimers();
  }

  private resetTimeoutTimers(): void {
    if (this.timeoutWarningTimer) clearTimeout(this.timeoutWarningTimer);
    if (this.sessionTimeoutTimer) clearTimeout(this.sessionTimeoutTimer);

    // Warning timer
    const warningTime = (this.config.session.maxIdleMinutes - this.config.session.timeoutWarningMinutes) * 60 * 1000;
    this.timeoutWarningTimer = setTimeout(() => {
      if (!this.sessionState.warningShown && this.sessionState.isActive) {
        this.sessionState.warningShown = true;
        this.showTimeoutWarning();
      }
    }, warningTime);

    // Timeout timer
    const timeoutTime = this.config.session.maxIdleMinutes * 60 * 1000;
    this.sessionTimeoutTimer = setTimeout(() => {
      this.expireSession();
    }, timeoutTime);
  }

  private showTimeoutWarning(): void {
    // This would trigger a UI warning component
    this.auditLog(SecurityEventType.SESSION_TIMEOUT, {
      action: 'timeout_warning_shown',
      remainingMinutes: this.config.session.timeoutWarningMinutes,
    });
  }

  private generateSessionId(): string {
    return `ps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private auditLog(type: SecurityEventType, details: Record<string, any>): void {
    auditLogger.logSecurityEvent(type, details, this.securityContext || undefined);
  }
}

// Singleton instance
export const sessionManager = new SessionManager();

// TEMP DEBUG: Expose globally for debugging
const isDev = import.meta.env.DEV || 
              import.meta.env.MODE === 'development' || 
              process.env.NODE_ENV === 'development' ||
              window.location.hostname === 'localhost';
if (isDev) {
  (window as any).sessionManager = sessionManager;
  (window as any).resetPaymentAttempts = () => {
    sessionManager.resetPaymentAttempts();
    console.log('🔧 [DEV] Payment attempts reset! You can now retry payment.');
  };
}

// Hooks for React components
export const useSessionSecurity = () => {
  return {
    isSessionValid: () => sessionManager.isSessionValid(),
    isRateLimited: () => sessionManager.isRateLimited(),
    getSecurityContext: () => sessionManager.getSecurityContext(),
    updateActivity: () => sessionManager.updateActivity(),
    startPaymentSession: (bookingId: number, amount: number) => 
      sessionManager.startPaymentSession(bookingId, amount),
    recordPaymentAttempt: (success: boolean, errorCode?: string) =>
      sessionManager.recordPaymentAttempt(success, errorCode),
    endPaymentSession: () => sessionManager.endPaymentSession(),
    resetPaymentAttempts: () => sessionManager.resetPaymentAttempts(), // TEMP DEBUG
  };
};