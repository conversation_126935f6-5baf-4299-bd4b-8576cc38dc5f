// Additional type definitions that extend the schema types

export interface UserWithToken {
  user: {
    id: number;
    username: string;
    email: string;
    fullName?: string;
    role: "user" | "owner";
  };
  token: string;
}

export interface PropertyWithAvailability {
  id: number;
  title: string;
  description: string;
  location: string;
  halfDayPrice: number;
  fullDayPrice: number;
  bedrooms: number;
  bathrooms: number;
  amenities: string[];
  images: string[];
  status: string;
  featured: boolean;
  owner: {
    id: number;
    username: string;
    fullName?: string;
  };
  availableDates: {
    date: string;
    morning: boolean;
    fullDay: boolean;
  }[];
}

export interface BookingDetails {
  id: number;
  propertyId: number;
  userId: number;
  bookingDate: string;
  bookingType: "morning" | "full_day";
  guests: number;
  totalPrice: number;
  status: string;
  createdAt: string;
  property: {
    id: number;
    title: string;
    location: string;
    images: string[];
  };
  user?: {
    id: number;
    username: string;
    fullName?: string;
    email: string;
    phone?: string;
  };
}

export interface PropertySearchFilters {
  location?: string;
  date?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
}

export interface DateAvailability {
  date: string;
  morning: boolean;
  fullDay: boolean;
}

export interface UploadResponse {
  urls: string[];
}

export interface ApiError {
  message: string;
  errors?: { [key: string]: string[] };
}
