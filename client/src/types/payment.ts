// Payment-related TypeScript interfaces

export interface RazorpayPaymentResponse {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
}

export interface PaymentOrder {
  id: string;
  razorpayOrderId: string;
  amount: number;
  currency: string;
  razorpayKeyId?: string;
}

export interface PaymentVerificationData {
  paymentId: string;
  orderId: string;
  status: 'success' | 'failed';
  amount: number;
  currency: string;
  method?: string;
  timestamp: string;
}

export interface PaymentData {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
  verificationData?: PaymentVerificationData;
}

export interface BookingDetails {
  id: number;
  propertyTitle: string;
  bookingDate: string;
  bookingType: string;
  guests: number;
  totalPrice: number;
  customerDetails: {
    name: string;
    email: string;
    phone: string;
  };
  gstDetails: {
    supplierState: string;
    recipientState: string;
    serviceType: string;
    supplierGstin?: string;
    recipientGstin?: string;
  };
}

export interface PaymentBreakdown {
  baseAmount: number;
  gstAmount: number;
  totalAmount: number;
  gstCalculation: {
    rate: number;
    type: string;
    breakdown: Record<string, number>;
  };
}

export interface ApiError {
  message: string;
  code?: string;
  statusCode?: number;
}

export interface PaymentCaptureRequest {
  paymentId: string;
  amount: number;
  idempotencyKey: string;
}

export interface PaymentCreateOrderRequest {
  bookingId: number;
  amount: number;
  currency: string;
  customerDetails: BookingDetails['customerDetails'];
  gstDetails: BookingDetails['gstDetails'];
}

export interface PaymentVerificationRequest {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
  orderId: string;
}