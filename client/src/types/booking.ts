export interface BookingWithPropertyAndGuest {
  id: number;
  propertyId: number;
  userId: number;
  bookingDate: string;
  bookingType: "morning" | "full_day";
  guests: number;
  totalPrice: number;
  status: string;
  specialRequests?: string;
  createdAt: string;
  property: {
    id: number;
    title: string;
    location: string;
    images: string[];
  };
  guest: {
    id: number;
    fullName: string;
    username: string;
    email: string;
    phone: string | null;
  };
}

export interface BookingListData {
  bookings: BookingWithPropertyAndGuest[];
  total: number;
  hasMore: boolean;
}