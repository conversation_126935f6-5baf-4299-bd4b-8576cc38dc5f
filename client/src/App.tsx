import { Switch, Route, useLocation } from "wouter";
import { lazy, Suspense } from "react";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "./contexts/AuthContext";
import { FavoritesProvider } from "./contexts/FavoritesContext";
import ErrorBoundary from "./components/ErrorBoundary";
import PageTransition from "./components/PageTransition";
import Header from "./components/Header";
import Footer from "./components/Footer";
import CookieConsent from "./components/CookieConsent";
import FarmhouseLoader from "./components/FarmhouseLoader";

// Lazy load pages for better bundle splitting
const Home = lazy(() => import("@/pages/Home"));
const PropertyDetail = lazy(() => import("@/pages/PropertyDetail"));
const Login = lazy(() => import("@/pages/Login"));
const Register = lazy(() => import("@/pages/Register"));
const VerifyRegistration = lazy(() => import("@/pages/VerifyRegistration"));
const OwnerDashboard = lazy(() => import("@/pages/OwnerDashboard"));
const CreateProperty = lazy(() => import("@/pages/CreateProperty"));
const EditProperty = lazy(() => import("@/pages/EditProperty"));
const OwnerAnalytics = lazy(() => import("@/pages/OwnerAnalytics"));
const Profile = lazy(() => import("@/pages/Profile"));
const Bookings = lazy(() => import("@/pages/Bookings"));
const PrivacyPolicy = lazy(() => import("@/pages/PrivacyPolicy"));
const TermsOfService = lazy(() => import("@/pages/TermsOfService"));
const CookiePolicy = lazy(() => import("@/pages/CookiePolicy"));
const AdminConsentDashboard = lazy(() => import("@/pages/AdminConsentDashboard"));
const NotFound = lazy(() => import("@/pages/not-found"));

function Router() {
  return (
    <Suspense fallback={<FarmhouseLoader />}>
      <Switch>
      <Route path="/">
        {() => (
          <PageTransition>
            <Home />
          </PageTransition>
        )}
      </Route>
      <Route path="/property/:id">
        {() => (
          <PageTransition>
            <PropertyDetail />
          </PageTransition>
        )}
      </Route>
      <Route path="/login">
        {() => (
          <PageTransition>
            <Login />
          </PageTransition>
        )}
      </Route>
      <Route path="/register">
        {() => (
          <PageTransition>
            <Register />
          </PageTransition>
        )}
      </Route>
      <Route path="/verify-registration">
        {() => (
          <PageTransition>
            <VerifyRegistration />
          </PageTransition>
        )}
      </Route>
      <Route path="/profile">
        {() => (
          <PageTransition>
            <Profile />
          </PageTransition>
        )}
      </Route>
      <Route path="/bookings">
        {() => (
          <PageTransition>
            <Bookings />
          </PageTransition>
        )}
      </Route>
      <Route path="/privacy-policy">
        {() => (
          <PageTransition>
            <PrivacyPolicy />
          </PageTransition>
        )}
      </Route>
      <Route path="/terms-of-service">
        {() => (
          <PageTransition>
            <TermsOfService />
          </PageTransition>
        )}
      </Route>
      <Route path="/cookie-policy">
        {() => (
          <PageTransition>
            <CookiePolicy />
          </PageTransition>
        )}
      </Route>
      <Route path="/owner/dashboard">
        {() => (
          <PageTransition>
            <OwnerDashboard />
          </PageTransition>
        )}
      </Route>
      <Route path="/admin/consent-dashboard">
        {() => (
          <PageTransition>
            <AdminConsentDashboard />
          </PageTransition>
        )}
      </Route>
      <Route path="/owner/analytics">
        {() => (
          <PageTransition>
            <OwnerAnalytics />
          </PageTransition>
        )}
      </Route>
      <Route path="/owner/property/create">
        {() => (
          <PageTransition>
            <CreateProperty />
          </PageTransition>
        )}
      </Route>
      <Route path="/owner/property/edit/:id">
        {() => (
          <PageTransition>
            <EditProperty />
          </PageTransition>
        )}
      </Route>
      <Route>
        {() => (
          <PageTransition>
            <NotFound />
          </PageTransition>
        )}
      </Route>
    </Switch>
    </Suspense>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>
        <AuthProvider>
          <FavoritesProvider>
            <TooltipProvider>
              <Toaster />
              <div className="min-h-screen flex flex-col bg-[#F7F4F1] custom-scrollbar">
                <Header />
                <main className="flex-grow">
                  <Router />
                </main>
                <Footer />
                <CookieConsent />
              </div>
            </TooltipProvider>
          </FavoritesProvider>
        </AuthProvider>
      </ErrorBoundary>
    </QueryClientProvider>
  );
}

export default App;