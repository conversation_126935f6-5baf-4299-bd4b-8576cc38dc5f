/**
 * ✅ PERFORMANCE MONITORING: Track N+1 Query Optimizations
 * 
 * This service monitors the performance improvements from solving the N+1 query problem
 * and provides metrics to validate the optimization effectiveness.
 * 
 * Metrics Tracked:
 * - API call reduction (N+1 → 1)
 * - Load time improvements
 * - Network payload optimization
 * - Cache hit rates
 * - Real-time update efficiency
 */

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface APICallMetric {
  endpoint: string;
  method: string;
  duration: number;
  size: number;
  status: number;
  timestamp: number;
  cached: boolean;
}

interface OptimizationStats {
  // Before optimization (N+1 problem)
  beforeOptimization: {
    apiCalls: number;
    totalLoadTime: number;
    networkPayload: number;
  };
  
  // After optimization (batch endpoint)
  afterOptimization: {
    apiCalls: number;
    totalLoadTime: number;
    networkPayload: number;
  };
  
  // Improvement metrics
  improvements: {
    apiCallReduction: number; // percentage
    loadTimeImprovement: number; // percentage
    payloadReduction: number; // percentage
  };
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private apiCalls: APICallMetric[] = [];
  private startTimes = new Map<string, number>();
  private optimizationStats: OptimizationStats | null = null;

  /**
   * Start measuring an operation
   */
  startMeasurement(operationName: string): void {
    this.startTimes.set(operationName, performance.now());
  }

  /**
   * End measuring an operation and record the metric
   */
  endMeasurement(operationName: string, metadata?: Record<string, any>): number {
    const startTime = this.startTimes.get(operationName);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operationName}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.startTimes.delete(operationName);

    const metric: PerformanceMetric = {
      name: operationName,
      value: duration,
      unit: 'ms',
      timestamp: Date.now()
    };
    
    if (metadata) {
      metric.metadata = metadata;
    }
    
    this.recordMetric(metric);

    return duration;
  }

  /**
   * Record a custom metric
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Track API call performance
   */
  trackAPICall(call: APICallMetric): void {
    this.apiCalls.push(call);
    
    // Keep only last 500 API calls
    if (this.apiCalls.length > 500) {
      this.apiCalls = this.apiCalls.slice(-500);
    }

    // Auto-detect N+1 patterns
    this.detectN1Patterns();
  }

  /**
   * Detect N+1 query patterns in API calls
   */
  private detectN1Patterns(): void {
    const recentCalls = this.apiCalls.slice(-20); // Last 20 calls
    const callGroups = new Map<string, APICallMetric[]>();

    // Group calls by endpoint pattern
    recentCalls.forEach(call => {
      const pattern = this.getEndpointPattern(call.endpoint);
      if (!callGroups.has(pattern)) {
        callGroups.set(pattern, []);
      }
      callGroups.get(pattern)!.push(call);
    });

    // Check for N+1 patterns
    callGroups.forEach((calls, pattern) => {
      if (calls.length > 3 && this.isN1Pattern(calls)) {
        console.warn(`🚨 Potential N+1 pattern detected for ${pattern}:`, {
          callCount: calls.length,
          calls: calls.map(c => ({ endpoint: c.endpoint, duration: c.duration }))
        });
        
        this.recordMetric({
          name: 'n1_pattern_detected',
          value: calls.length,
          unit: 'calls',
          timestamp: Date.now(),
          metadata: { pattern, calls: calls.length }
        });
      }
    });
  }

  /**
   * Get endpoint pattern (remove dynamic IDs)
   */
  private getEndpointPattern(endpoint: string): string {
    return endpoint.replace(/\/\d+/g, '/:id');
  }

  /**
   * Check if calls represent an N+1 pattern
   */
  private isN1Pattern(calls: APICallMetric[]): boolean {
    // Simple heuristic: multiple calls to similar endpoints within a short time
    const timeWindow = 5000; // 5 seconds
    const now = Date.now();
    
    const recentCalls = calls.filter(call => now - call.timestamp < timeWindow);
    return recentCalls.length >= 3;
  }

  /**
   * Measure dashboard load performance
   */
  measureDashboardLoad(propertyCount: number): {
    startMeasurement: () => void;
    endMeasurement: () => DashboardLoadMetrics;
  } {
    let startTime: number;
    let apiCallsBefore: number;

    return {
      startMeasurement: () => {
        startTime = performance.now();
        apiCallsBefore = this.apiCalls.length;
      },
      
      endMeasurement: (): DashboardLoadMetrics => {
        const totalTime = performance.now() - startTime;
        const apiCallsAfter = this.apiCalls.length;
        const apiCallsMade = apiCallsAfter - apiCallsBefore;

        const metrics: DashboardLoadMetrics = {
          totalLoadTime: totalTime,
          apiCallsCount: apiCallsMade,
          propertyCount,
          timestamp: Date.now(),
          // Calculate theoretical N+1 calls (1 for properties + N for bookings)
          theoreticalN1Calls: 1 + propertyCount,
          // Calculate efficiency improvement
          efficiency: propertyCount > 0 ? 
            (1 - (apiCallsMade / (1 + propertyCount))) * 100 : 100
        };

        this.recordMetric({
          name: 'dashboard_load',
          value: totalTime,
          unit: 'ms',
          timestamp: Date.now(),
          metadata: metrics
        });

        return metrics;
      }
    };
  }

  /**
   * Set optimization comparison stats
   */
  setOptimizationStats(stats: OptimizationStats): void {
    this.optimizationStats = stats;
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): PerformanceSummary {
    const recentMetrics = this.metrics.filter(m => 
      Date.now() - m.timestamp < 300000 // Last 5 minutes
    );

    const dashboardLoads = recentMetrics
      .filter(m => m.name === 'dashboard_load')
      .map(m => m.metadata as DashboardLoadMetrics);

    const apiCallsByEndpoint = this.apiCalls
      .reduce((acc, call) => {
        const pattern = this.getEndpointPattern(call.endpoint);
        acc[pattern] = (acc[pattern] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const avgLoadTime = dashboardLoads.length > 0 ?
      dashboardLoads.reduce((sum, load) => sum + load.totalLoadTime, 0) / dashboardLoads.length :
      0;

    const cacheHitRate = this.apiCalls.length > 0 ?
      (this.apiCalls.filter(call => call.cached).length / this.apiCalls.length) * 100 :
      0;

    return {
      avgDashboardLoadTime: avgLoadTime,
      totalAPICallsLast5Min: this.apiCalls.filter(call => 
        Date.now() - call.timestamp < 300000
      ).length,
      cacheHitRate,
      apiCallsByEndpoint,
      recentDashboardLoads: dashboardLoads.slice(-10),
      optimizationStats: this.optimizationStats,
      n1PatternsDetected: recentMetrics.filter(m => 
        m.name === 'n1_pattern_detected'
      ).length
    };
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): {
    metrics: PerformanceMetric[];
    apiCalls: APICallMetric[];
    summary: PerformanceSummary;
  } {
    return {
      metrics: [...this.metrics],
      apiCalls: [...this.apiCalls],
      summary: this.getPerformanceSummary()
    };
  }

  /**
   * Clear old metrics to free memory
   */
  cleanup(): void {
    const fiveMinutesAgo = Date.now() - 300000;
    this.metrics = this.metrics.filter(m => m.timestamp > fiveMinutesAgo);
    this.apiCalls = this.apiCalls.filter(c => c.timestamp > fiveMinutesAgo);
  }
}

// Interfaces
interface DashboardLoadMetrics {
  totalLoadTime: number;
  apiCallsCount: number;
  propertyCount: number;
  timestamp: number;
  theoreticalN1Calls: number;
  efficiency: number;
}

interface PerformanceSummary {
  avgDashboardLoadTime: number;
  totalAPICallsLast5Min: number;
  cacheHitRate: number;
  apiCallsByEndpoint: Record<string, number>;
  recentDashboardLoads: DashboardLoadMetrics[];
  optimizationStats: OptimizationStats | null;
  n1PatternsDetected: number;
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Note: React hooks should be imported in the component files that use them
// This file exports the monitoring functions for use in React components

// Export types
export type { 
  PerformanceMetric, 
  APICallMetric, 
  OptimizationStats, 
  DashboardLoadMetrics,
  PerformanceSummary 
};