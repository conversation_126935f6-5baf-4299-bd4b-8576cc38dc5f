/**
 * API Service with built-in authorization and error handling
 */

interface APIResponse<T = any> {
  data?: T;
  message?: string;
  success?: boolean;
  error?: string;
}

interface RequestOptions extends RequestInit {
  skipAuth?: boolean;
  customHeaders?: Record<string, string>;
}

class APIService {
  private baseURL: string;

  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
  }

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token || ''}`,
      'Content-Type': 'application/json'
    };
  }

  private getMultipartHeaders(): Record<string, string> {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token || ''}`
      // Don't set Content-Type for multipart, let browser set boundary
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle specific authorization errors
      if (response.status === 401) {
        // Clear invalid token
        localStorage.removeItem('token');
        throw new Error('Authentication required. Please log in again.');
      }
      
      if (response.status === 403) {
        throw new Error('You are not authorized to perform this action.');
      }

      if (response.status === 404) {
        throw new Error('Resource not found.');
      }

      if (response.status >= 500) {
        throw new Error('Server error. Please try again later.');
      }

      throw new Error(errorData.error || errorData.message || `Request failed: ${response.status}`);
    }

    const data = await response.json();
    return data.data || data;
  }

  private buildURL(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.baseURL}/${cleanEndpoint}`;
  }

  async get<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const { skipAuth, customHeaders, ...fetchOptions } = options;
    
    const headers = {
      ...(skipAuth ? {} : this.getAuthHeaders()),
      ...customHeaders
    };

    const response = await fetch(this.buildURL(endpoint), {
      method: 'GET',
      headers,
      credentials: 'include',
      ...fetchOptions
    });

    return this.handleResponse<T>(response);
  }

  async post<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const { skipAuth, customHeaders, ...fetchOptions } = options;
    
    const headers = {
      ...(skipAuth ? {} : this.getAuthHeaders()),
      ...customHeaders
    };

    const response = await fetch(this.buildURL(endpoint), {
      method: 'POST',
      headers,
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
      ...fetchOptions
    });

    return this.handleResponse<T>(response);
  }

  async put<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const { skipAuth, customHeaders, ...fetchOptions } = options;
    
    const headers = {
      ...(skipAuth ? {} : this.getAuthHeaders()),
      ...customHeaders
    };

    const response = await fetch(this.buildURL(endpoint), {
      method: 'PUT',
      headers,
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
      ...fetchOptions
    });

    return this.handleResponse<T>(response);
  }

  async patch<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const { skipAuth, customHeaders, ...fetchOptions } = options;
    
    const headers = {
      ...(skipAuth ? {} : this.getAuthHeaders()),
      ...customHeaders
    };

    const response = await fetch(this.buildURL(endpoint), {
      method: 'PATCH',
      headers,
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
      ...fetchOptions
    });

    return this.handleResponse<T>(response);
  }

  async delete<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const { skipAuth, customHeaders, ...fetchOptions } = options;
    
    const headers = {
      ...(skipAuth ? {} : this.getAuthHeaders()),
      ...customHeaders
    };

    const response = await fetch(this.buildURL(endpoint), {
      method: 'DELETE',
      headers,
      credentials: 'include',
      ...fetchOptions
    });

    return this.handleResponse<T>(response);
  }

  async upload<T>(endpoint: string, formData: FormData, options: RequestOptions = {}): Promise<T> {
    const { skipAuth, customHeaders, ...fetchOptions } = options;
    
    const headers = {
      ...(skipAuth ? {} : this.getMultipartHeaders()),
      ...customHeaders
    };

    const response = await fetch(this.buildURL(endpoint), {
      method: 'POST',
      headers,
      credentials: 'include',
      body: formData,
      ...fetchOptions
    });

    return this.handleResponse<T>(response);
  }

  // Property-specific methods
  async getOwnerProperties(): Promise<any[]> {
    return this.get('properties/owner/me');
  }

  async createProperty(propertyData: any): Promise<any> {
    return this.post('properties', propertyData);
  }

  async updateProperty(propertyId: number, propertyData: any): Promise<any> {
    return this.put(`properties/${propertyId}`, propertyData);
  }

  async deleteProperty(propertyId: number): Promise<any> {
    return this.delete(`properties/${propertyId}`);
  }

  async updatePropertyPricing(propertyId: number, pricingData: any): Promise<any> {
    return this.put(`properties/${propertyId}/pricing`, pricingData);
  }

  // Booking-specific methods
  async getOwnerBookings(): Promise<any[]> {
    return this.get('bookings/owner/me');
  }

  async getDetailedOwnerBookings(): Promise<any[]> {
    return this.get('bookings/owner/me/detailed');
  }

  async updateBookingStatus(bookingId: number, status: string): Promise<any> {
    return this.patch(`bookings/${bookingId}/status`, { status });
  }

  async getBooking(bookingId: number): Promise<any> {
    return this.get(`bookings/${bookingId}`);
  }

  // Media-specific methods
  async uploadMedia(formData: FormData): Promise<any> {
    return this.upload('properties/media/upload', formData);
  }

  async deleteMedia(propertyId: number, mediaUrl: string, mediaType = 'image'): Promise<any> {
    return this.delete('properties/media/delete', {
      body: JSON.stringify({ propertyId, mediaUrl, mediaType }),
      headers: this.getAuthHeaders()
    });
  }

  async reorderMedia(propertyId: number, mediaUrl: string, newOrder: number, mediaType = 'image'): Promise<any> {
    return this.put('properties/media/reorder', { propertyId, mediaUrl, newOrder, mediaType });
  }

  // Analytics-specific methods  
  async getOwnerAnalytics(): Promise<any> {
    return this.get('analytics/owner/dashboard');
  }

  async getPropertyAnalytics(propertyId: number): Promise<any> {
    return this.get(`analytics/properties/${propertyId}`);
  }

  // Utility methods
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    return !!token && token !== '';
  }

  clearAuth(): void {
    localStorage.removeItem('token');
  }

  setToken(token: string): void {
    localStorage.setItem('token', token);
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }
}

// Create singleton instance
export const apiService = new APIService();

// Export specific API functions for common operations
export const propertyAPI = {
  getOwnerProperties: () => apiService.getOwnerProperties(),
  create: (data: any) => apiService.createProperty(data),
  update: (id: number, data: any) => apiService.updateProperty(id, data),
  delete: (id: number) => apiService.deleteProperty(id),
  updatePricing: (id: number, data: any) => apiService.updatePropertyPricing(id, data)
};

export const bookingAPI = {
  getOwnerBookings: () => apiService.getOwnerBookings(),
  getDetailedOwnerBookings: () => apiService.getDetailedOwnerBookings(),
  updateStatus: (id: number, status: string) => apiService.updateBookingStatus(id, status),
  get: (id: number) => apiService.getBooking(id)
};

export const mediaAPI = {
  upload: (formData: FormData) => apiService.uploadMedia(formData),
  delete: (propertyId: number, mediaUrl: string, mediaType?: string) => 
    apiService.deleteMedia(propertyId, mediaUrl, mediaType),
  reorder: (propertyId: number, mediaUrl: string, newOrder: number, mediaType?: string) =>
    apiService.reorderMedia(propertyId, mediaUrl, newOrder, mediaType)
};

export const analyticsAPI = {
  getOwnerDashboard: () => apiService.getOwnerAnalytics(),
  getPropertyAnalytics: (propertyId: number) => apiService.getPropertyAnalytics(propertyId)
};

export default apiService;