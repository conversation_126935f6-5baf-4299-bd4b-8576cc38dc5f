import { useMemo } from 'react';
import { useQuery, useMutation, useQueryClient, QueryClient } from '@tanstack/react-query';
import { useAPIHeaders } from '@/hooks/useAuthorization';
import { 
  ApiResponse, 
  PaginatedResponse, 
  BatchOperation,
  DashboardSummary,
  BookingWithPropertyAndGuest,
  PropertyWithBookings 
} from '../types';

/**
 * Modular API Client - Reusable HTTP Layer for Dashboard
 * Provides CRUD operations with authentication and error handling
 */
export class DashboardApiClient {
  private baseUrl: string = '/api';
  private authHeaders: () => HeadersInit;

  constructor(authHeadersFn: () => HeadersInit) {
    this.authHeaders = authHeadersFn;
  }

  /**
   * Generic GET request with query parameters
   */
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.authHeaders(),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Generic POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.authHeaders(),
      },
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Generic PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...this.authHeaders(),
      },
      credentials: 'include',
      body: data ? JSON.stringify(data) : null,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Generic DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.authHeaders(),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Batch operations for efficiency
   * Note: Requires backend batch endpoint implementation
   */
  async batch<T>(operations: BatchOperation[]): Promise<T[]> {
    const response = await fetch(`${this.baseUrl}/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.authHeaders(),
      },
      credentials: 'include',
      body: JSON.stringify({ operations }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Batch API Error ${response.status}: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  // Dashboard-specific API methods
  
  /**
   * Get dashboard summary statistics
   */
  async getDashboardSummary(): Promise<ApiResponse<DashboardSummary>> {
    return this.get<ApiResponse<DashboardSummary>>('/owner/dashboard/summary');
  }

  /**
   * Get owner's properties with pagination
   */
  async getOwnerProperties(page: number = 1, search?: string): Promise<PaginatedResponse<PropertyWithBookings>> {
    const params: Record<string, any> = { page, limit: 10 };
    if (search) params.search = search;
    
    return this.get<PaginatedResponse<PropertyWithBookings>>('/properties/owner/me', params);
  }

  /**
   * Get properties - alias for getOwnerProperties for consistency
   */
  async getProperties(page: number = 1, search?: string): Promise<PaginatedResponse<PropertyWithBookings>> {
    return this.getOwnerProperties(page, search);
  }

  /**
   * Get owner's bookings with filtering
   */
  async getOwnerBookings(
    page: number = 1, 
    status?: string
  ): Promise<PaginatedResponse<BookingWithPropertyAndGuest>> {
    const params: Record<string, any> = { page, limit: 10 };
    if (status && status !== 'all') params.status = status;
    
    return this.get<PaginatedResponse<BookingWithPropertyAndGuest>>('/bookings/owner/me', params);
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(bookingId: number, status: string): Promise<ApiResponse<BookingWithPropertyAndGuest>> {
    return this.put<ApiResponse<BookingWithPropertyAndGuest>>(`/bookings/${bookingId}/status`, { status });
  }

  /**
   * Delete property
   */
  async deleteProperty(propertyId: number): Promise<ApiResponse<any>> {
    return this.delete<ApiResponse<any>>(`/properties/${propertyId}`);
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<ApiResponse<any>> {
    return this.get<ApiResponse<any>>('/owner/notification-settings');
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(settings: any): Promise<ApiResponse<any>> {
    return this.put<ApiResponse<any>>('/owner/notification-settings', settings);
  }
}

/**
 * Hook to create authenticated API client
 */
export const useDashboardApiClient = (): DashboardApiClient => {
  const { getAuthHeaders } = useAPIHeaders();
  
  const apiClient = useMemo(
    () => new DashboardApiClient(getAuthHeaders), 
    [getAuthHeaders]
  );
  
  return apiClient;
};

/**
 * Generic Resource Hook - Works with any CRUD resource
 */
export function useResource<T>(
  resourceName: string,
  options: {
    params?: Record<string, any>;
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
  } = {}
) {
  const api = useDashboardApiClient();
  const { params, enabled = true, refetchInterval, staleTime = 1000 * 60 * 5 } = options;

  return useQuery<T>({
    queryKey: [resourceName, params],
    queryFn: () => api.get<T>(`/${resourceName}`, params),
    enabled,
    refetchInterval: refetchInterval || false,
    staleTime,
  });
}

/**
 * Generic Mutation Hook - Works with any resource mutations
 */
export function useResourceMutation<T>(
  resourceName: string,
  method: 'POST' | 'PUT' | 'DELETE' = 'POST'
) {
  const api = useDashboardApiClient();
  const queryClient = useQueryClient();

  return useMutation<T, Error, { id?: string | number; data?: any }>({
    mutationFn: ({ id, data }) => {
      const endpoint = id ? `/${resourceName}/${id}` : `/${resourceName}`;
      switch (method) {
        case 'POST':
          return api.post<T>(endpoint, data);
        case 'PUT':
          return api.put<T>(endpoint, data);
        case 'DELETE':
          return api.delete<T>(endpoint);
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: [resourceName] });
    },
  });
}

/**
 * Optimistic update helper
 */
export const useOptimisticMutation = <T>(
  queryKey: string[],
  updateFn: (data: T, variables: any) => T
) => {
  const queryClient = useQueryClient();

  return {
    onMutate: async (variables: any) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<T>(queryKey);

      // Optimistically update the cache
      if (previousData) {
        queryClient.setQueryData<T>(queryKey, (old) => 
          old ? updateFn(old, variables) : old
        );
      }

      return { previousData };
    },
    onError: (_error: any, _variables: any, context: any) => {
      // If the mutation fails, roll back to the previous data
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData);
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey });
    },
  };
};

export default {
  DashboardApiClient,
  useDashboardApiClient,
  useResource,
  useResourceMutation,
  useOptimisticMutation,
};