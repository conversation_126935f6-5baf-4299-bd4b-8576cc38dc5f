/**
 * Dashboard Feature Module
 * Modular, maintainable dashboard architecture following SOLID principles
 * 
 * Extracted from monolithic OwnerDashboard.tsx (3100+ lines) into focused,
 * reusable components with clear separation of concerns.
 */

// Components
export * from './components';

// Services  
export * from './services/DashboardApiClient';

// Hooks
export * from './hooks/useRealTimeUpdates';
export * from './hooks/useDashboardData';

// Utils
export * from './utils/soundEffects';

// Types
export * from './types';

// Default export for convenience
export { DashboardLayout } from './components/DashboardLayout';
export { BookingList } from './components/BookingManagement';

export default {
  components: {
    DashboardLayout: () => import('./components/DashboardLayout'),
    BookingList: () => import('./components/BookingManagement/BookingList'),
  },
  hooks: {
    useRealTimeUpdates: () => import('./hooks/useRealTimeUpdates'),
    useDashboardData: () => import('./hooks/useDashboardData'),
  },
  services: {
    DashboardApiClient: () => import('./services/DashboardApiClient'),
  },
  utils: {
    soundEffects: () => import('./utils/soundEffects'),
  },
};