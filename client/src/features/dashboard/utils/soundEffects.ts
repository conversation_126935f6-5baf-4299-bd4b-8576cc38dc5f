import { useCallback } from 'react';

/**
 * Sound Effects Utility for Dashboard Notifications
 * Provides audio feedback for different types of user interactions and events
 */

export type SoundType = 'success' | 'error' | 'info' | 'warning' | 'booking';

/**
 * Sound configuration for different notification types
 */
const SOUND_CONFIG = {
  success: {
    tones: [
      { frequency: 523.25, duration: 0.15, delay: 0 },    // C5
      { frequency: 659.25, duration: 0.15, delay: 100 },  // E5
      { frequency: 783.99, duration: 0.2, delay: 200 },   // G5
    ],
    volume: 0.1,
  },
  error: {
    tones: [
      { frequency: 880, duration: 0.2, delay: 0 },     // A5
      { frequency: 659.25, duration: 0.2, delay: 150 }, // E5
    ],
    volume: 0.15,
  },
  warning: {
    tones: [
      { frequency: 659.25, duration: 0.15, delay: 0 },   // E5
      { frequency: 523.25, duration: 0.15, delay: 200 }, // C5
    ],
    volume: 0.12,
  },
  info: {
    tones: [
      { frequency: 523.25, duration: 0.1, delay: 0 },    // C5
      { frequency: 659.25, duration: 0.15, delay: 120 }, // E5
    ],
    volume: 0.08,
  },
  booking: {
    tones: [
      { frequency: 523.25, duration: 0.15, delay: 0 },     // C5
      { frequency: 659.25, duration: 0.15, delay: 150 },   // E5
      { frequency: 783.99, duration: 0.15, delay: 300 },   // G5
      { frequency: 1046.5, duration: 0.25, delay: 450 },   // C6
    ],
    volume: 0.1,
  },
} as const;

/**
 * Creates and plays a tone using Web Audio API
 */
const playTone = (audioContext: AudioContext, frequency: number, duration: number, volume: number) => {
  const oscillator = audioContext.createOscillator();
  const gainNode = audioContext.createGain();
  
  oscillator.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  oscillator.frequency.value = frequency;
  oscillator.type = 'sine';
  
  gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
  gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
  
  oscillator.start(audioContext.currentTime);
  oscillator.stop(audioContext.currentTime + duration);
};

/**
 * Plays a notification sound based on the specified type
 */
export const playNotificationSound = (type: SoundType = 'info'): void => {
  try {
    // Check if Web Audio API is supported
    if (!window.AudioContext && !(window as any).webkitAudioContext) {
      console.warn('Web Audio API not supported in this browser');
      return;
    }

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const config = SOUND_CONFIG[type];
    
    // Play each tone with its specified delay
    config.tones.forEach(({ frequency, duration, delay }) => {
      setTimeout(() => {
        playTone(audioContext, frequency, duration, config.volume);
      }, delay);
    });
    
  } catch (error) {
    console.warn('Failed to play notification sound:', error);
  }
};

/**
 * Custom hook for sound notifications
 * Provides consistent sound feedback across dashboard components
 */
export const useSoundNotifications = () => {
  const playSound = useCallback((type: SoundType = 'info') => {
    playNotificationSound(type);
  }, []);

  const playBookingNotificationSound = useCallback(() => {
    playNotificationSound('booking');
  }, []);

  return { 
    playNotificationSound: playSound, 
    playBookingNotificationSound 
  };
};

/**
 * Utility function to test all sound types
 * Useful for debugging and user preference testing
 */
export const testAllSounds = () => {
  const soundTypes: SoundType[] = ['success', 'error', 'warning', 'info', 'booking'];
  
  soundTypes.forEach((type, index) => {
    setTimeout(() => {
      console.log(`Playing ${type} sound`);
      playNotificationSound(type);
    }, index * 1000);
  });
};

export default {
  playNotificationSound,
  useSoundNotifications,
  testAllSounds,
};