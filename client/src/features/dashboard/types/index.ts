import { ReactComponentElement } from 'react';

/**
 * Core Dashboard Types
 */

export interface FeatureModule {
  id: string;
  name: string;
  description?: string;
  version: string;
  component: React.ComponentType<any>;
  apiEndpoints?: string[];
  dependencies?: string[];
  enabled: boolean;
}

export interface DashboardSummary {
  propertyCount: number;
  activeBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  recentBookings: BookingWithPropertyAndGuest[];
}

export interface BookingWithPropertyAndGuest {
  id: number;
  propertyId: number;
  propertyName: string;  
  propertyLocation?: string;
  propertyImage?: string;
  guestId: number;
  guestName: string;
  guestPhone: string;
  guestEmail: string;
  checkInDate: string;
  checkOutDate: string;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'rejected' | 'cancelled' | 'completed';
  createdAt: string;
  specialRequests?: string;
}

export interface PropertyWithBookings {
  id: number;
  title: string;
  description: string;
  location: string;
  halfDayPrice: number;
  fullDayPrice: number;
  weekdayHalfDayPrice?: number | null;
  weekdayFullDayPrice?: number | null;
  weekendHalfDayPrice?: number | null;
  weekendFullDayPrice?: number | null;
  images: string[];
  videos?: string[];
  amenities: string[];
  maxGuests: number;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  status: 'active' | 'inactive' | 'pending';
  featured: boolean;
  ownerId: number;
  createdAt: Date;
  latitude?: number | null;
  longitude?: number | null;
  bookingCount?: number;
  avgRating?: number;
  isDeleted?: boolean;
}

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export type DashboardTab = 'properties' | 'pricing' | 'bookings' | 'analytics' | 'communications' | 'media' | 'settings';

export type BookingStatus = 'all' | 'pending' | 'confirmed' | 'rejected' | 'cancelled' | 'completed';

export interface RealTimeUpdate {
  type: 'booking_created' | 'booking_status_changed' | 'booking_modified' | 'property_inquiry';
  payload: any;
  timestamp: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface BatchOperation {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  endpoint: string;
  data?: any;
}

export interface NotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  soundNotifications: boolean;
  newBookingNotifications: boolean;
  bookingStatusNotifications: boolean;
  paymentNotifications: boolean;
  propertyInquiryNotifications: boolean;
}

export interface ConfirmationDialog {
  isOpen: boolean;
  title: string;
  description: string;
  onConfirm: () => void;
  onCancel: () => void;
  isDestructive?: boolean;
  confirmText?: string;
  cancelText?: string;
}

export interface DashboardFilters {
  propertySearch: string;
  bookingStatusFilter: BookingStatus;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface DashboardPagination {
  propertyPage: number;
  bookingPage: number;
  pageSize: number;
}

export interface OptimisticUpdate {
  type: 'booking_status_update' | 'property_delete';
  id: string | number;
  oldData: any;
  newData: any;
  timestamp: number;
}

export interface FeatureFlag {
  enabled: boolean;
  rolloutPercentage?: number;
  userGroups?: string[];
  startDate?: string;
  endDate?: string;
}

export interface FeatureFlags {
  core: {
    dashboardStats: FeatureFlag;
    basicBookingManagement: FeatureFlag;
    propertyListing: FeatureFlag;
    notifications: FeatureFlag;
  };
  realtime: {
    enabled: FeatureFlag;
    websocketEnabled: FeatureFlag;
    pollingFallback: FeatureFlag;
    reconnectAttempts: number;
    reconnectInterval: number;
    pollingInterval: number;
  };
  future: {
    advancedAnalytics: FeatureFlag;
    guestMessaging: FeatureFlag;
    bulkOperations: FeatureFlag;
    automatedResponses: FeatureFlag;
    advancedReporting: FeatureFlag;
  };
}

// Types cannot be exported as values - use named type exports above