import React, { useState, useEffect, useRef } from 'react';
import { Camera } from 'lucide-react';

interface LazyImageProps {
  src: string;
  alt: string;
  className: string;
  sizes?: string;
  priority?: boolean; // For above-the-fold images
}

/**
 * Image optimization utilities
 */
const getOptimizedImageUrl = (originalUrl: string, width?: number, quality = 80): string => {
  if (!originalUrl) return originalUrl;
  
  // If it's already a CDN URL (like Cloudinary, ImageKit, etc.), add transformations
  if (originalUrl.includes('cloudinary.com')) {
    // Cloudinary transformations
    const baseUrl = originalUrl.split('/upload/')[0] + '/upload/';
    const imagePath = originalUrl.split('/upload/')[1];
    const transformations = `w_${width || 800},q_${quality},f_auto,c_fill`;
    return `${baseUrl}${transformations}/${imagePath}`;
  } else if (originalUrl.includes('imagekit.io')) {
    // ImageKit transformations
    return `${originalUrl}?tr=w-${width || 800},q-${quality},f-auto`;
  } else if (originalUrl.includes('res.cloudinary.com') || originalUrl.includes('cdn.')) {
    // Generic CDN with query parameters
    const separator = originalUrl.includes('?') ? '&' : '?';
    return `${originalUrl}${separator}w=${width || 800}&q=${quality}&f=auto`;
  }
  
  return originalUrl;
};

const getThumbnailUrl = (originalUrl: string): string => getOptimizedImageUrl(originalUrl, 300, 60);
const getFullResUrl = (originalUrl: string): string => getOptimizedImageUrl(originalUrl, 1200, 85);

/**
 * LazyImage Component
 * Progressive lazy image loading with CDN optimization and connection speed detection
 */
export const LazyImage: React.FC<LazyImageProps> = ({ 
  src, 
  alt, 
  className,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  priority = false
}) => {
  const [thumbnailLoaded, setThumbnailLoaded] = useState(false);
  const [fullResLoaded, setFullResLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority); // If priority, start in view
  const [loadFullRes, setLoadFullRes] = useState(priority);
  const [connectionSpeed, setConnectionSpeed] = useState<'slow' | 'fast'>('fast');
  const imgRef = useRef<HTMLDivElement>(null);

  const thumbnailUrl = getThumbnailUrl(src);
  const fullResUrl = getFullResUrl(src);

  // Detect connection speed
  useEffect(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        const isSlowConnection = 
          connection.effectiveType === 'slow-2g' || 
          connection.effectiveType === '2g' ||
          connection.saveData === true;
        setConnectionSpeed(isSlowConnection ? 'slow' : 'fast');
      }
    }
  }, []);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) return; // Skip intersection observer for priority images

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          // Delay full-res loading based on connection speed
          const delay = connectionSpeed === 'slow' ? 500 : 100;
          setTimeout(() => setLoadFullRes(true), delay);
          observer.disconnect();
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, connectionSpeed]);

  return (
    <div ref={imgRef} className={`${className} relative overflow-hidden`}>
      {!isInView ? (
        // Placeholder while not in view
        <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
          <Camera className="h-8 w-8 text-gray-400" />
        </div>
      ) : (
        <>
          {/* Thumbnail (loads first) */}
          <img
            src={thumbnailUrl}
            alt={alt}
            className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
              thumbnailLoaded ? 'opacity-100' : 'opacity-0'
            } ${fullResLoaded && connectionSpeed === 'fast' ? 'opacity-0' : 'opacity-100'}`}
            onLoad={() => setThumbnailLoaded(true)}
            loading={priority ? "eager" : "lazy"}
          />
          
          {/* Full resolution image (only load on fast connections or user preference) */}
          {loadFullRes && connectionSpeed === 'fast' && (
            <img
              src={fullResUrl}
              alt={alt}
              className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-700 ${
                fullResLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => setFullResLoaded(true)}
              loading={priority ? "eager" : "lazy"}
              sizes={sizes}
            />
          )}
          
          {/* Loading overlay */}
          {!thumbnailLoaded && (
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
              <div className="flex flex-col items-center gap-2">
                <div className="w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-xs text-gray-500">
                  {connectionSpeed === 'slow' ? 'Optimizing...' : 'Loading...'}
                </span>
              </div>
            </div>
          )}
          
          {/* Progressive loading indicator */}
          {thumbnailLoaded && !fullResLoaded && loadFullRes && connectionSpeed === 'fast' && (
            <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
              HD Loading...
            </div>
          )}
          
          {/* Connection speed indicator */}
          {thumbnailLoaded && connectionSpeed === 'slow' && (
            <div className="absolute bottom-2 right-2 bg-blue-500/80 text-white text-xs px-2 py-1 rounded">
              Data Saver
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default LazyImage;