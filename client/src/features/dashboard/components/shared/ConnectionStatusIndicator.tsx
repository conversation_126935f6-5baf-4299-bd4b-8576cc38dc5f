import React from 'react';
import { Wifi, WifiOff, RotateCcw } from 'lucide-react';
import { ConnectionStatus } from '../../types';

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus;
  isConnected: boolean;
  reconnectAttempts: number;
  enabled?: boolean;
  className?: string;
}

/**
 * ConnectionStatusIndicator Component
 * Shows the current real-time connection status with appropriate icons and colors
 */
export const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({ 
  status, 
  isConnected, 
  reconnectAttempts,
  enabled = true,
  className = '',
}) => {
  if (!enabled) return null;

  const getStatusInfo = () => {
    switch (status) {
      case 'connected':
        return {
          icon: <Wifi className="h-3 w-3 text-green-600" />,
          text: 'Live updates active',
          className: 'bg-green-50 text-green-700 border-green-200'
        };
      case 'connecting':
        return {
          icon: <RotateCcw className="h-3 w-3 text-blue-600 animate-spin" />,
          text: 'Connecting...',
          className: 'bg-blue-50 text-blue-700 border-blue-200'
        };
      case 'error':
        return {
          icon: <WifiOff className="h-3 w-3 text-red-600" />,
          text: `Connection error${reconnectAttempts > 0 ? ` (${reconnectAttempts}/5)` : ''}`,
          className: 'bg-red-50 text-red-700 border-red-200'
        };
      case 'disconnected':
        return {
          icon: <WifiOff className="h-3 w-3 text-yellow-600" />,
          text: 'Using polling updates',
          className: 'bg-yellow-50 text-yellow-700 border-yellow-200'
        };
      default:
        return {
          icon: <WifiOff className="h-3 w-3 text-gray-600" />,
          text: 'Offline',
          className: 'bg-gray-50 text-gray-700 border-gray-200'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium border ${statusInfo.className} ${className}`}>
      {statusInfo.icon}
      <span className="hidden sm:inline">{statusInfo.text}</span>
    </div>
  );
};

export default ConnectionStatusIndicator;