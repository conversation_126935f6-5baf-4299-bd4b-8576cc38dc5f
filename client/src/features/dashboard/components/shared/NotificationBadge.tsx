import React from 'react';

interface NotificationBadgeProps {
  count: number;
  type?: 'default' | 'warning' | 'error' | 'success';
  pulse?: boolean;
  className?: string;
}

/**
 * NotificationBadge Component
 * Displays a notification count badge with different styling based on type
 */
export const NotificationBadge: React.FC<NotificationBadgeProps> = ({ 
  count, 
  type = 'default',
  pulse = false,
  className = '',
}) => {
  if (count <= 0) return null;

  const getBadgeStyles = () => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-500 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'success':
        return 'bg-green-500 text-white';
      default:
        return 'bg-blue-500 text-white';
    }
  };

  return (
    <span className={`
      inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full
      ${getBadgeStyles()}
      ${pulse ? 'animate-pulse' : ''}
      ${count > 99 ? 'px-1' : ''}
      ${className}
    `}>
      {count > 99 ? '99+' : count}
    </span>
  );
};

export default NotificationBadge;