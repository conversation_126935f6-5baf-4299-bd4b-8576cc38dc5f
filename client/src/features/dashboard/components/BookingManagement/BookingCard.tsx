import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, MapPin } from 'lucide-react';
import { format } from 'date-fns';
import { BookingWithPropertyAndGuest } from '../../types';
import { BookingStatusButton } from './BookingStatusButton';

interface BookingCardProps {
  booking: BookingWithPropertyAndGuest;
  isRecentlyUpdated: boolean;
  isActionInProgress: boolean;
  onViewDetails: (booking: BookingWithPropertyAndGuest) => void;
  onStatusUpdate: (bookingId: number, status: 'confirmed' | 'rejected') => void;
}

/**
 * Utility function to get status color classes
 */
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'confirmed': return 'bg-green-100 text-green-800 border-green-300';
    case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    case 'rejected': return 'bg-red-100 text-red-800 border-red-300';
    case 'cancelled': return 'bg-gray-100 text-gray-800 border-gray-300';
    case 'completed': return 'bg-blue-100 text-blue-800 border-blue-300';
    default: return 'bg-blue-100 text-blue-800 border-blue-300';
  }
};

/**
 * Utility function to format Indian currency
 */
const formatPrice = (price: number): string => `₹${price.toLocaleString('en-IN')}`;

/**
 * BookingCard Component
 * Displays individual booking information in a card format
 */
export const BookingCard: React.FC<BookingCardProps> = ({
  booking,
  isRecentlyUpdated,
  isActionInProgress,
  onViewDetails,
  onStatusUpdate,
}) => {
  return (
    <div
      className={`border rounded-lg p-6 ${
        isRecentlyUpdated ? 'border-green-300 bg-green-50' : 'border-gray-200'
      } transition-all duration-300`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {booking.propertyName}
              </h3>
              <p className="text-sm text-gray-600 flex items-center mt-1">
                <MapPin className="h-4 w-4 mr-1" />
                {booking.propertyLocation || 'Location not specified'}
              </p>
            </div>
            <Badge className={getStatusColor(booking.status)}>
              {booking.status}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div>
              <p className="text-sm text-gray-500">Guest</p>
              <p className="font-medium text-gray-900">{booking.guestName}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Date</p>
              <p className="font-medium text-gray-900">
                {format(new Date(booking.checkInDate), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <p className="font-medium text-gray-900">
                {booking.checkOutDate !== booking.checkInDate ? 'Multi-day' : 'Day Booking'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Amount</p>
              <p className="font-medium text-gray-900">{formatPrice(booking.totalPrice)}</p>
            </div>
          </div>
          
          {booking.specialRequests && (
            <div className="mt-4">
              <p className="text-sm text-gray-500">Special Requests</p>
              <p className="text-sm text-gray-700 mt-1">{booking.specialRequests}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center justify-between mt-6 pt-4 border-t">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewDetails(booking)}
        >
          <Eye className="h-4 w-4 mr-2" />
          View Details
        </Button>
        
        <BookingStatusButton
          booking={booking}
          isLoading={isActionInProgress}
          onStatusUpdate={onStatusUpdate}
        />
      </div>
    </div>
  );
};

export default BookingCard;