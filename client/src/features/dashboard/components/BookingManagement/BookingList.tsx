import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthorization } from '@/hooks/useAuthorization';
import { BookingWithPropertyAndGuest, BookingStatus, PaginatedResponse } from '../../types';
import { useDashboardApiClient } from '../../services/DashboardApiClient';
import { useEnhancedToast, useOptimisticUpdates } from '../../hooks/useDashboardData';
import { playNotificationSound } from '../../utils/soundEffects';
import { BookingFilters } from './BookingFilters';
import { BookingCard } from './BookingCard';
import { BookingDetailsModal } from './BookingDetailsModal';

interface BookingListProps {
  className?: string;
}

/**
 * BookingList Component
 * Main component that manages all booking-related functionality including:
 * - Data fetching and caching
 * - Status updates with optimistic UI
 * - Filtering and pagination
 * - Modal dialogs
 */
export const BookingList: React.FC<BookingListProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const auth = useAuthorization();
  const queryClient = useQueryClient();
  const apiClient = useDashboardApiClient();
  const { showErrorToast } = useEnhancedToast();
  const { updateBookingOptimistically } = useOptimisticUpdates();

  // State management
  const [bookingStatusFilter, setBookingStatusFilter] = useState<BookingStatus>('all');
  const [bookingPage, setBookingPage] = useState(1);
  const [showBookingDetails, setShowBookingDetails] = useState<BookingWithPropertyAndGuest | null>(null);
  const [actionInProgress, setActionInProgress] = useState<number | null>(null);
  const [recentlyUpdated, setRecentlyUpdated] = useState<Set<number>>(new Set());

  const BOOKINGS_PER_PAGE = 10;

  // Reset page when filter changes
  useEffect(() => {
    setBookingPage(1);
  }, [bookingStatusFilter]);

  // Fetch bookings with React Query
  const { 
    data: bookingsData, 
    isLoading: bookingsLoading,
    isError: bookingsError 
  } = useQuery({
    queryKey: ['/api/bookings/owner', { page: bookingPage, status: bookingStatusFilter }],
    queryFn: () => apiClient.getOwnerBookings(bookingPage, bookingStatusFilter === 'all' ? undefined : bookingStatusFilter),
    enabled: !!user && user.role === 'owner' && auth.canAccess,
    placeholderData: (previousData: any) => previousData,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });

  // Extract bookings from API response
  const bookings = bookingsData?.data || [];
  const hasMore = bookingsData?.hasNext || false;
  const total = bookingsData?.total || 0;
  const totalPages = bookingsData?.totalPages || 1;

  // Update booking status mutation
  const updateBookingStatus = useMutation({
    mutationFn: async ({ bookingId, status }: { bookingId: number; status: string }) => {
      if (!auth.canManage) {
        throw new Error('You are not authorized to manage bookings');
      }

      // Apply optimistic update
      updateBookingOptimistically(bookingId, status, () => {
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
      });

      return apiClient.updateBookingStatus(bookingId, status);
    },
    onMutate: ({ bookingId }) => {
      setActionInProgress(bookingId);
    },
    onSuccess: (_, { bookingId }) => {
      setActionInProgress(null);
      
      // Mark as recently updated for visual feedback
      setRecentlyUpdated(prev => new Set(Array.from(prev).concat(bookingId)));
      
      // Remove the visual feedback after animation
      setTimeout(() => {
        setRecentlyUpdated(prev => {
          const newSet = new Set(prev);
          newSet.delete(bookingId);
          return newSet;
        });
      }, 3000);
      
      // Refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
      queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
    },
    onError: (error: any, { bookingId }) => {
      setActionInProgress(null);
      
      if (error.message?.includes('authorized')) {
        showErrorToast('❌ Authorization Error', 'You do not have permission to update this booking.');
      } else {
        showErrorToast('❌ Update Failed', 'Unable to update booking status. Please try again.');
      }
      
      playNotificationSound('error');
      
      // Revert optimistic update
      queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
      queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
    }
  });

  // Handler functions
  const handleStatusUpdate = (bookingId: number, status: 'confirmed' | 'rejected') => {
    updateBookingStatus.mutate({ bookingId, status });
  };

  const handleViewDetails = (booking: BookingWithPropertyAndGuest) => {
    setShowBookingDetails(booking);
  };

  const handleCloseDetails = () => {
    setShowBookingDetails(null);
  };

  // Loading state
  if (bookingsLoading && bookings.length === 0) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <BookingFilters
              statusFilter={bookingStatusFilter}
              onStatusFilterChange={setBookingStatusFilter}
            />
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-32" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (bookingsError) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <BookingFilters
              statusFilter={bookingStatusFilter}
              onStatusFilterChange={setBookingStatusFilter}
            />
          </div>
          <div className="p-6">
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-red-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Error Loading Bookings</h3>
              <p className="mt-1 text-sm text-gray-500">
                Unable to load booking data. Please refresh the page.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Empty state
  if (bookings.length === 0) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <BookingFilters
              statusFilter={bookingStatusFilter}
              onStatusFilterChange={setBookingStatusFilter}
            />
          </div>
          <div className="p-6">
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
              <p className="mt-1 text-sm text-gray-500">
                {bookingStatusFilter === 'all' 
                  ? 'Your booking requests will appear here.'
                  : `No ${bookingStatusFilter} bookings found.`
                }
              </p>
            </div>
          </div>
        </div>
        
        {/* Booking Details Modal */}
        <BookingDetailsModal
          booking={showBookingDetails}
          isOpen={!!showBookingDetails}
          onClose={handleCloseDetails}
        />
      </div>
    );
  }

  // Main render
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <BookingFilters
            statusFilter={bookingStatusFilter}
            onStatusFilterChange={setBookingStatusFilter}
          />
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {bookings.map((booking) => (
              <BookingCard
                key={booking.id}
                booking={booking}
                isRecentlyUpdated={recentlyUpdated.has(booking.id)}
                isActionInProgress={actionInProgress === booking.id}
                onViewDetails={handleViewDetails}
                onStatusUpdate={handleStatusUpdate}
              />
            ))}
          </div>
          
          {/* Pagination */}
          {(hasMore || bookingPage > 1) && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="text-sm text-gray-600">
                Showing page {bookingPage} of {totalPages} pages ({total} total bookings)
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setBookingPage(prev => Math.max(1, prev - 1))}
                  disabled={bookingPage <= 1 || bookingsLoading}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setBookingPage(prev => prev + 1)}
                  disabled={!hasMore || bookingsLoading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={showBookingDetails}
        isOpen={!!showBookingDetails}
        onClose={handleCloseDetails}
      />
    </div>
  );
};

export default BookingList;