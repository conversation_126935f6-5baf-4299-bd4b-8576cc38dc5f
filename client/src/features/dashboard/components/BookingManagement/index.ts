/**
 * BookingManagement Components
 * Modular booking management functionality extracted from monolithic OwnerDashboard
 */

// Import and re-export components
import BookingListComponent from './BookingList';
import BookingCardComponent from './BookingCard';
import BookingFiltersComponent from './BookingFilters';
import BookingDetailsModalComponent from './BookingDetailsModal';
import BookingStatusButtonComponent from './BookingStatusButton';

export { BookingListComponent as BookingList };
export { BookingCardComponent as BookingCard };
export { BookingFiltersComponent as BookingFilters };
export { BookingDetailsModalComponent as BookingDetailsModal };
export { BookingStatusButtonComponent as BookingStatusButton };

// Re-export types for convenience
export type { BookingWithPropertyAndGuest, BookingStatus } from '../../types';

export default {
  BookingList: BookingListComponent,
  BookingCard: BookingCardComponent,  
  BookingFilters: BookingFiltersComponent,
  BookingDetailsModal: BookingDetailsModalComponent,
  BookingStatusButton: BookingStatusButtonComponent,
};