import React from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, Mail, Phone, MapPin, IndianRupee } from 'lucide-react';
import { format } from 'date-fns';
import { BookingWithPropertyAndGuest } from '../../types';

interface BookingDetailsModalProps {
  booking: BookingWithPropertyAndGuest | null;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Utility function to get status color classes
 */
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'confirmed': return 'bg-green-100 text-green-800 border-green-300';
    case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    case 'rejected': return 'bg-red-100 text-red-800 border-red-300';
    case 'cancelled': return 'bg-gray-100 text-gray-800 border-gray-300';
    case 'completed': return 'bg-blue-100 text-blue-800 border-blue-300';
    default: return 'bg-blue-100 text-blue-800 border-blue-300';
  }
};

/**
 * Utility function to format Indian currency
 */
const formatPrice = (price: number): string => `₹${price.toLocaleString('en-IN')}`;

/**
 * BookingDetailsModal Component
 * Displays detailed booking information in a modal dialog
 */
export const BookingDetailsModal: React.FC<BookingDetailsModalProps> = ({
  booking,
  isOpen,
  onClose,
}) => {
  if (!booking) return null;

  const checkInDate = new Date(booking.checkInDate);
  const checkOutDate = new Date(booking.checkOutDate);
  const isMultiDay = booking.checkOutDate !== booking.checkInDate;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Booking Details</DialogTitle>
          <DialogDescription>
            Complete information about this booking request
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Property Information */}
          <div>
            <h4 className="font-semibold text-gray-900">Property</h4>
            <p className="text-sm text-gray-600">{booking.propertyName}</p>
            <p className="text-sm text-gray-600 flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              {booking.propertyLocation || 'Location not specified'}
            </p>
          </div>
          
          {/* Guest Information */}
          <div>
            <h4 className="font-semibold text-gray-900">Guest Information</h4>
            <p className="text-sm text-gray-600">{booking.guestName}</p>
            <p className="text-sm text-gray-600 flex items-center">
              <Mail className="h-4 w-4 mr-1" />
              {booking.guestEmail}
            </p>
            {booking.guestPhone && (
              <p className="text-sm text-gray-600 flex items-center">
                <Phone className="h-4 w-4 mr-1" />
                {booking.guestPhone}
              </p>
            )}
          </div>
          
          {/* Booking Details */}
          <div>
            <h4 className="font-semibold text-gray-900">Booking Details</h4>
            <p className="text-sm text-gray-600 flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {isMultiDay 
                ? `${format(checkInDate, 'MMMM dd, yyyy')} - ${format(checkOutDate, 'MMMM dd, yyyy')}`
                : format(checkInDate, 'MMMM dd, yyyy')
              }
            </p>
            <p className="text-sm text-gray-600 flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {isMultiDay ? 'Multi-day booking' : 'Day booking'}
            </p>
            <p className="text-sm text-gray-600 flex items-center">
              <IndianRupee className="h-4 w-4 mr-1" />
              {formatPrice(booking.totalPrice)}
            </p>
          </div>
          
          {/* Special Requests */}
          {booking.specialRequests && (
            <div>
              <h4 className="font-semibold text-gray-900">Special Requests</h4>
              <p className="text-sm text-gray-600">{booking.specialRequests}</p>
            </div>
          )}
          
          {/* Status and Created Date */}
          <div className="flex items-center justify-between pt-4">
            <Badge className={getStatusColor(booking.status)}>
              {booking.status}
            </Badge>
            <p className="text-xs text-gray-500">
              Booked {format(new Date(booking.createdAt), 'MMM dd, yyyy')}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BookingDetailsModal;