import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { CheckCircle, XCircle } from 'lucide-react';
import { BookingWithPropertyAndGuest } from '../../types';

interface BookingStatusButtonProps {
  booking: BookingWithPropertyAndGuest;
  isLoading: boolean;
  onStatusUpdate: (bookingId: number, status: 'confirmed' | 'rejected') => void;
}

/**
 * BookingStatusButton Component
 * Renders action buttons for updating booking status (Confirm/Reject)
 * Only shows for pending bookings
 */
export const BookingStatusButton: React.FC<BookingStatusButtonProps> = ({
  booking,
  isLoading,
  onStatusUpdate,
}) => {
  // Only render for pending bookings
  if (booking.status !== 'pending') {
    return null;
  }

  return (
    <div className="flex gap-2">
      <Button
        size="sm"
        variant="outline"
        onClick={() => onStatusUpdate(booking.id, 'rejected')}
        disabled={isLoading}
      >
        <XCircle className="h-4 w-4 mr-2" />
        Reject
      </Button>
      <Button
        size="sm"
        onClick={() => onStatusUpdate(booking.id, 'confirmed')}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Skeleton className="h-4 w-4 mr-2" />
            Processing...
          </>
        ) : (
          <>
            <CheckCircle className="h-4 w-4 mr-2" />
            Confirm
          </>
        )}
      </Button>
    </div>
  );
};

export default BookingStatusButton;