import React from 'react';
import { 
  Building2, 
  Calendar, 
  BarChart3, 
  Camera, 
  MessageSquare, 
  ShieldCheck,
  IndianRupee,
  PlusCircle 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLocation } from 'wouter';
import { ConnectionStatusIndicator } from './shared/ConnectionStatusIndicator';
import { NotificationBadge } from './shared/NotificationBadge';
import { DashboardTab, ConnectionStatus } from '../types';

interface DashboardLayoutProps {
  children: React.ReactNode;
  selectedTab: DashboardTab;
  onTabChange: (tab: DashboardTab) => void;
  dashboardSummary?: {
    propertyCount: number;
    activeBookings: number;
    pendingBookings: number;
    totalRevenue: number;
  } | undefined;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  reconnectAttempts: number;
  className?: string;
}

/**
 * Utility function to format Indian currency
 */
const formatPrice = (price: number): string => `₹${price.toLocaleString('en-IN')}`;

/**
 * DashboardLayout Component
 * Main layout component that provides the dashboard structure including:
 * - Header with navigation and stats
 * - Tab navigation
 * - Connection status indicator
 * - Responsive layout
 */
export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  selectedTab,
  onTabChange,
  dashboardSummary,
  connectionStatus,
  isConnected,
  reconnectAttempts,
  className = '',
}) => {
  const [, navigate] = useLocation();

  const {
    propertyCount = 0,
    activeBookings = 0,
    pendingBookings = 0,
    totalRevenue = 0,
  } = dashboardSummary || {};

  const confirmedBookings = activeBookings;

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto p-4">
        {/* Header with Add Property button and connection status */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Owner Dashboard</h1>
            <p className="text-gray-600 mt-1">Manage your properties and bookings</p>
          </div>
          <div className="flex items-center gap-4">
            <ConnectionStatusIndicator
              status={connectionStatus}
              isConnected={isConnected}
              reconnectAttempts={reconnectAttempts}
            />
            <Button
              onClick={() => navigate('/properties/create')}
              className="bg-[#4A6741] hover:bg-[#3A5131] text-white"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Property
            </Button>
          </div>
        </div>

        {/* Dashboard Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Properties</p>
              <p className="text-2xl font-bold text-gray-900">{propertyCount}</p>
            </div>
            <Building2 className="h-8 w-8 text-blue-600" />
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Bookings</p>
              <p className="text-2xl font-bold text-gray-900">{pendingBookings}</p>
            </div>
            <Calendar className="h-8 w-8 text-orange-600" />
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Confirmed Bookings</p>
              <p className="text-2xl font-bold text-gray-900">{confirmedBookings}</p>
            </div>
            <Calendar className="h-8 w-8 text-green-600" />
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">{formatPrice(totalRevenue)}</p>
            </div>
            <IndianRupee className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        {/* Tab Navigation */}
        <nav className="flex space-x-8 border-b border-gray-200 mb-6">
          <button
            onClick={() => onTabChange('properties')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
              selectedTab === 'properties'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Building2 className="h-4 w-4 inline mr-2" />
            Properties
          </button>
          
          <button
            onClick={() => onTabChange('pricing')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
              selectedTab === 'pricing'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <IndianRupee className="h-4 w-4" />
            Pricing
          </button>
          
          <button
            onClick={() => onTabChange('bookings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
              selectedTab === 'bookings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Calendar className="h-4 w-4" />
            Bookings
            {pendingBookings > 0 && (
              <NotificationBadge
                count={pendingBookings}
                type="warning"
                className="ml-1"
              />
            )}
          </button>
          
          <button
            onClick={() => onTabChange('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
              selectedTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BarChart3 className="h-4 w-4 inline mr-2" />
            Analytics
          </button>
          
          <button
            onClick={() => onTabChange('media')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
              selectedTab === 'media'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Camera className="h-4 w-4" />
            Media Management
          </button>
          
          <button
            onClick={() => onTabChange('communications')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
              selectedTab === 'communications'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <MessageSquare className="h-4 w-4" />
            Communications
            {/* Future: Dynamic notification count */}
          </button>
          
          <button
            onClick={() => onTabChange('settings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
              selectedTab === 'settings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <ShieldCheck className="h-4 w-4" />
            Settings
          </button>
        </nav>

        {/* Tab Content */}
        <div className="space-y-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;