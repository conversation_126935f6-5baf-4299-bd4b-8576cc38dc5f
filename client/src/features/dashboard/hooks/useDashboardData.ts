import { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { BookingWithPropertyAndGuest } from '../types';

/**
 * Enhanced Toast Hook - Provides consistent toast notifications across dashboard
 */
export const useEnhancedToast = () => {
  const { toast } = useToast();

  const showSuccessToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-green-50 border-green-200 text-green-800",
      duration: 4000,
    });
  };

  const showErrorToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: "destructive",
      duration: 6000,
    });
  };

  const showWarningToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-yellow-50 border-yellow-200 text-yellow-800",
      duration: 5000,
    });
  };

  const showInfoToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-blue-50 border-blue-200 text-blue-800",
      duration: 4000,
    });
  };

  return { showSuccessToast, showErrorToast, showWarningToast, showInfoToast };
};

/**
 * Optimistic Updates Hook
 * Provides immediate UI feedback for user actions
 */
export const useOptimisticUpdates = () => {
  const queryClient = useQueryClient();
  const { showErrorToast } = useEnhancedToast();

  const updateBookingOptimistically = useCallback(async (
    bookingId: number,
    newStatus: string,
    rollbackFn: () => void
  ) => {
    // Get current bookings data
    const currentBookings = queryClient.getQueryData<{ bookings: BookingWithPropertyAndGuest[] }>(['/api/bookings/owner']);
    
    if (currentBookings) {
      // Create optimistic update
      const optimisticBookings = {
        ...currentBookings,
        bookings: currentBookings.bookings.map(booking =>
          booking.id === bookingId 
            ? { ...booking, status: newStatus as any }
            : booking
        )
      };

      // Apply optimistic update
      queryClient.setQueryData(['/api/bookings/owner'], optimisticBookings);

      // Update dashboard summary optimistically
      const currentSummary = queryClient.getQueryData(['/api/owner/dashboard/summary']);
      if (currentSummary) {
        const updatedSummary = {
          ...currentSummary,
          pendingBookings: newStatus === 'confirmed' || newStatus === 'cancelled' 
            ? Math.max(0, (currentSummary as any).pendingBookings - 1)
            : (currentSummary as any).pendingBookings,
          confirmedBookings: newStatus === 'confirmed'
            ? ((currentSummary as any).confirmedBookings || 0) + 1
            : (currentSummary as any).confirmedBookings,
        };
        queryClient.setQueryData(['/api/owner/dashboard/summary'], updatedSummary);
      }

      // Set up rollback timeout (in case the server update fails)
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
      }, 5000);
    }
  }, [queryClient]);

  return { updateBookingOptimistically };
};

/**
 * Feature Disclosure Hook
 * Manages progressive feature disclosure for better UX
 */
export const useFeatureDisclosure = () => {
  const [disclosedFeatures, setDisclosedFeatures] = useState<Set<string>>(new Set());

  const discloseFeature = (feature: string) => {
    setDisclosedFeatures(prev => new Set(Array.from(prev).concat(feature)));
  };

  const isFeatureDisclosed = (feature: string) => {
    return disclosedFeatures.has(feature);
  };

  return { discloseFeature, isFeatureDisclosed };
};

/**
 * Image Preloader Hook
 * Preloads images for better performance
 */
export const useImagePreloader = (urls: string[]) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  
  // Helper function to get thumbnail URL
  const getThumbnailUrl = (url: string) => {
    if (url.includes('cloudinary.com')) {
      return url.replace('/upload/', '/upload/w_300,h_200,c_fill,q_auto/');
    }
    if (url.includes('imagekit.io')) {
      return `${url}?tr=w-300,h-200,c-maintain_ratio,q-80`;
    }
    return url;
  };
  
  useEffect(() => {
    if (urls.length === 0) return;
    
    const preloadImage = (url: string) => {
      return new Promise<void>((resolve) => {
        const img = new Image();
        img.onload = () => {
          setLoadedImages(prev => new Set(prev).add(url));
          resolve();
        };
        img.onerror = () => resolve(); // Still resolve on error to continue
        img.src = url;
      });
    };

    // Preload thumbnail URLs first
    const thumbnailUrls = urls.map(getThumbnailUrl);
    Promise.all(thumbnailUrls.map(preloadImage));
  }, [urls]);

  return loadedImages;
};

/**
 * Dashboard State Hook
 * Manages central dashboard state and actions
 */
export const useDashboardState = () => {
  const [selectedTab, setSelectedTab] = useState<'properties' | 'pricing' | 'bookings' | 'analytics' | 'communications' | 'media' | 'settings'>('properties');
  const [bookingStatusFilter, setBookingStatusFilter] = useState<string>('all');
  const [propertySearch, setPropertySearch] = useState('');
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);
  
  // Pagination state
  const [propertyPage, setPropertyPage] = useState(1);
  const [bookingPage, setBookingPage] = useState(1);
  
  // Action states
  const [actionInProgress, setActionInProgress] = useState<number | null>(null);
  const [propertyActionInProgress, setPropertyActionInProgress] = useState<number | null>(null);
  const [recentlyUpdated, setRecentlyUpdated] = useState<Set<number>>(new Set());
  
  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
    onCancel: () => void;
    isDestructive?: boolean;
    confirmText?: string;
    cancelText?: string;
  }>({
    isOpen: false,
    title: '',
    description: '',
    onConfirm: () => {},
    onCancel: () => {},
  });

  // Reset pagination when filters change
  useEffect(() => {
    setBookingPage(1);
  }, [bookingStatusFilter]);

  useEffect(() => {
    setPropertyPage(1);
  }, [propertySearch]);

  // Mark item as recently updated
  const markAsRecentlyUpdated = useCallback((id: number) => {
    setRecentlyUpdated(prev => new Set(prev).add(id));
    
    // Remove the mark after animation
    setTimeout(() => {
      setRecentlyUpdated(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 2000);
  }, []);

  return {
    // Tab state
    selectedTab,
    setSelectedTab,
    
    // Filter state
    bookingStatusFilter,
    setBookingStatusFilter,
    propertySearch,
    setPropertySearch,
    
    // Modal state
    showNotificationSettings,
    setShowNotificationSettings,
    
    // Pagination state
    propertyPage,
    setPropertyPage,
    bookingPage,
    setBookingPage,
    
    // Action state
    actionInProgress,
    setActionInProgress,
    propertyActionInProgress,
    setPropertyActionInProgress,
    recentlyUpdated,
    markAsRecentlyUpdated,
    
    // Confirmation dialog
    confirmationDialog,
    setConfirmationDialog,
  };
};

/**
 * Image Optimization Utilities
 */
export const useImageOptimization = () => {
  const getOptimizedImageUrl = useCallback((url: string, width?: number, height?: number) => {
    if (!url) return '';
    
    // Cloudinary optimization
    if (url.includes('cloudinary.com')) {
      const transforms = [];
      if (width) transforms.push(`w_${width}`);
      if (height) transforms.push(`h_${height}`);
      transforms.push('c_fill', 'q_auto', 'f_auto');
      
      return url.replace('/upload/', `/upload/${transforms.join(',')}/`);
    }
    
    // ImageKit optimization
    if (url.includes('imagekit.io')) {
      const params = [];
      if (width) params.push(`w-${width}`);
      if (height) params.push(`h-${height}`);
      params.push('c-maintain_ratio', 'q-80');
      
      return `${url}?tr=${params.join(',')}`;
    }
    
    return url;
  }, []);

  const getThumbnailUrl = useCallback((url: string) => {
    return getOptimizedImageUrl(url, 300, 200);
  }, [getOptimizedImageUrl]);

  const getFullResUrl = useCallback((url: string) => {
    return getOptimizedImageUrl(url, 1200, 800);
  }, [getOptimizedImageUrl]);

  return {
    getOptimizedImageUrl,
    getThumbnailUrl,
    getFullResUrl,
  };
};

export default {
  useEnhancedToast,
  useOptimisticUpdates,
  useFeatureDisclosure,
  useImagePreloader,
  useDashboardState,
  useImageOptimization,
};