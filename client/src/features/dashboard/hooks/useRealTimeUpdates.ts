import { useState, useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useSoundNotifications } from '../utils/soundEffects';
import { ConnectionStatus, RealTimeUpdate } from '../types';

/**
 * Feature flags for real-time functionality
 */
const REALTIME_FEATURE_FLAGS = {
  enabled: true,
  websocketEnabled: true,
  pollingEnabled: true,
  reconnectAttempts: 5,
  reconnectInterval: 3000,
  pollingInterval: 30000,
};

/**
 * Enhanced Toast Hook - Provides consistent toast notifications
 */
const useEnhancedToast = () => {
  const { toast } = useToast();

  const showSuccessToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-green-50 border-green-200 text-green-800",
      duration: 4000,
    });
  };

  const showErrorToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: "destructive",
      duration: 6000,
    });
  };

  const showWarningToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-yellow-50 border-yellow-200 text-yellow-800",
      duration: 5000,
    });
  };

  const showInfoToast = (title: string, description?: string) => {
    toast({
      title,
      description,
      className: "bg-blue-50 border-blue-200 text-blue-800",
      duration: 4000,
    });
  };

  return { showSuccessToast, showErrorToast, showWarningToast, showInfoToast };
};

/**
 * Real-time Updates Hook
 * Manages WebSocket connections with polling fallback for dashboard updates
 */
export const useRealTimeUpdates = (userId: number | null) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const queryClient = useQueryClient();
  const { showInfoToast } = useEnhancedToast();
  const { playBookingNotificationSound, playNotificationSound } = useSoundNotifications();
  
  const wsRef = useRef<WebSocket | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * WebSocket connection handler
   */
  const connectWebSocket = useCallback(() => {
    if (!userId || !REALTIME_FEATURE_FLAGS.enabled || !REALTIME_FEATURE_FLAGS.websocketEnabled) return;

    try {
      setConnectionStatus('connecting');
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/ws/owner/${userId}`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setConnectionStatus('connected');
        setReconnectAttempts(0);
        
        // Clear polling since WebSocket is active
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleRealTimeUpdate(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setConnectionStatus('disconnected');
        
        // Attempt reconnection if not manual close
        if (event.code !== 1000 && reconnectAttempts < REALTIME_FEATURE_FLAGS.reconnectAttempts) {
          scheduleReconnect();
        } else {
          // Fall back to polling if WebSocket fails
          startPolling();
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      setConnectionStatus('error');
      startPolling();
    }
  }, [userId, reconnectAttempts]);

  /**
   * Schedule WebSocket reconnection with exponential backoff
   */
  const scheduleReconnect = useCallback(() => {
    const delay = REALTIME_FEATURE_FLAGS.reconnectInterval * Math.pow(2, reconnectAttempts);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      setReconnectAttempts(prev => prev + 1);
      connectWebSocket();
    }, delay);
  }, [connectWebSocket, reconnectAttempts]);

  /**
   * Polling fallback for when WebSocket is unavailable
   */
  const startPolling = useCallback(() => {
    if (!REALTIME_FEATURE_FLAGS.enabled || !REALTIME_FEATURE_FLAGS.pollingEnabled || pollingIntervalRef.current) return;

    console.log('Starting polling fallback');
    pollingIntervalRef.current = setInterval(async () => {
      try {
        // Poll for dashboard summary updates
        await queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
        
        // Poll for booking updates if on bookings tab
        if (document.querySelector('[data-tab="bookings"][data-active="true"]')) {
          await queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        }
      } catch (error) {
        console.error('Polling update failed:', error);
      }
    }, REALTIME_FEATURE_FLAGS.pollingInterval);
  }, [queryClient]);

  /**
   * Handle real-time updates from WebSocket
   */
  const handleRealTimeUpdate = useCallback((data: RealTimeUpdate) => {
    const { type, payload } = data;

    switch (type) {
      case 'booking_created':
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
        
        showInfoToast(
          '🔔 New Booking Request!',
          `${payload.guestName} wants to book "${payload.propertyName}" for ${payload.date}.`
        );
        playBookingNotificationSound();

        // Browser notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('New Booking Request', {
            body: `${payload.guestName} wants to book ${payload.propertyName}`,
            icon: '/favicon.ico',
            tag: `booking-${payload.bookingId}`,
          });
        }
        break;

      case 'booking_status_changed':
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        queryClient.invalidateQueries({ queryKey: ['/api/owner/dashboard/summary'] });
        
        const statusMessages = {
          confirmed: `✅ Booking confirmed for "${payload.propertyName}"`,
          cancelled: `❌ Booking cancelled for "${payload.propertyName}"`,
          completed: `🏁 Booking completed for "${payload.propertyName}"`,
        };

        const message = statusMessages[payload.newStatus as keyof typeof statusMessages];
        if (message) {
          showInfoToast('📋 Status Update', message);
          playNotificationSound('info');
        }
        break;

      case 'booking_modified':
        queryClient.invalidateQueries({ queryKey: ['/api/bookings/owner'] });
        
        showInfoToast(
          '✏️ Booking Modified',
          `${payload.guestName} updated their booking for "${payload.propertyName}".`
        );
        playNotificationSound('info');
        break;

      case 'property_inquiry':
        showInfoToast(
          '💬 New Inquiry',
          `Someone is interested in "${payload.propertyName}". Check your messages.`
        );
        playNotificationSound('info');
        break;

      default:
        console.log('Unknown real-time update type:', type);
    }
  }, [queryClient, showInfoToast, playBookingNotificationSound, playNotificationSound]);

  /**
   * Initialize connection and cleanup
   */
  useEffect(() => {
    if (!userId || !REALTIME_FEATURE_FLAGS.enabled) return;

    connectWebSocket();

    return () => {
      // Cleanup on unmount
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounting');
      }
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [userId, connectWebSocket]);

  /**
   * Request notification permissions on first use
   */
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  return {
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    reconnectAttempts,
    connectWebSocket,
    startPolling,
  };
};

export default useRealTimeUpdates;