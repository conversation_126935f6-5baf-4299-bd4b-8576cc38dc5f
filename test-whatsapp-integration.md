# WhatsApp Integration Testing Guide

## Phase 4 MVP - Testing Checklist

### 1. Environment Setup

**Required Environment Variables:**
```bash
# Add to your .env file
TWILIO_WHATSAPP_NUMBER=whatsapp:+14155238886
WHATSAPP_WEBHOOK_URL=https://your-domain.com/api/webhooks/whatsapp  
WHATSAPP_WEBHOOK_TOKEN=your-webhook-verification-token
```

### 2. Webhook Testing

**Test webhook verification (GET):**
```bash
curl "http://localhost:5000/api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=your-webhook-verification-token&hub.challenge=test123"
# Should return: test123
```

**Test incoming message (POST):**
```bash
curl -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "Body": "book farmhouse for Dec 15-17, 8 people, Gurgaon",
    "From": "whatsapp:+************",
    "MessageSid": "test123",
    "To": "whatsapp:+14155238886"
  }'
```

### 3. Message Parsing Tests

**Test different booking intents:**

1. **Simple booking request:**
   - Input: `"book farmhouse for Dec 15"`
   - Expected: Booking intent with date extraction

2. **Availability check:**
   - Input: `"check availability this weekend"`
   - Expected: Availability intent with date parsing

3. **Cancellation request:**
   - Input: `"cancel booking REF123"`
   - Expected: Cancel intent with booking reference

4. **Help request:**
   - Input: `"help"`
   - Expected: Help intent with welcome template

### 4. Template Response Testing

**Verify template responses work correctly:**

1. **Booking flow templates:**
   - Date collection
   - Guest count
   - Location selection
   - Availability results

2. **Quick reply functionality:**
   - Template with options shows correctly
   - Options are formatted properly

### 5. Integration with Booking Service

**Test booking creation flow:**

```bash
# Test endpoint (development only)
curl -X POST http://localhost:5000/api/whatsapp/send-test \
  -H "Content-Type: application/json" \
  -d '{
    "to": "whatsapp:+************",
    "message": "Test message from booking system"
  }'
```

### 6. Error Handling Tests

1. **Invalid webhook signature** (production)
2. **Malformed message payload**
3. **Service unavailable scenarios**
4. **Rate limiting tests**

### 7. End-to-End Booking Flow

**Complete user journey:**

1. User: `"hi"`
   - Response: Welcome template with options

2. User: `"book farmhouse"`
   - Response: Date collection template

3. User: `"Dec 15-17"`
   - Response: Guest count template

4. User: `"8 people"`
   - Response: Location selection template

5. User: `"Gurgaon"`
   - Response: Availability results (when integrated with booking service)

### 8. Performance & Monitoring

**Check logs for:**
- Message processing times
- Template rendering
- Error rates
- Response delivery

**Health check endpoint:**
```bash
curl http://localhost:5000/api/whatsapp/status
```

### 9. Production Readiness

**Before going live:**

1. ✅ Configure real Twilio WhatsApp number
2. ✅ Set up webhook URL with HTTPS
3. ✅ Enable webhook signature validation
4. ✅ Set production environment variables
5. ⏳ Connect with actual booking service endpoints
6. ⏳ Set up monitoring and alerting
7. ⏳ Load test webhook endpoints

### 10. Known Limitations (MVP)

- Templates use basic text formatting (no rich media)
- No integration with actual booking database yet
- Date parsing is basic (needs enhancement)
- No user session management
- No booking payment processing

### Next Steps (Phase 5)

- WhatsApp Flows implementation
- Rich media support (images, documents)
- Advanced booking workflow
- Payment integration
- Multi-language support