import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Helper function to safely load optional plugins
function createSafePlugin(moduleName) {
  return {
    name: `safe-${moduleName}`,
    configResolved() {
      // This plugin does nothing, just a placeholder for missing Replit plugins
    }
  };
}

export default defineConfig({
    plugins: [
        react(),
        // Skip Replit-specific plugins for deployment compatibility
        ...(process.env.REPL_ID ? [] : []),
    ],
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "client", "src"),
            "@shared": path.resolve(__dirname, "shared"),
            "@assets": path.resolve(__dirname, "attached_assets"),
        },
    },
    root: path.resolve(__dirname, "client"),
    build: {
        outDir: path.resolve(__dirname, "dist/public"),
        emptyOutDir: true,
    },
    server: {
        host: "0.0.0.0",
        port: 3000,
        strictPort: true,
    },
    preview: {
        host: "0.0.0.0",
        port: 3000,
    },
});
