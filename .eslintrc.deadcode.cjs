/**
 * ESLint Configuration for Dead Code Detection
 * 
 * This configuration detects and helps eliminate dead code patterns:
 * - Unused imports and exports
 * - Unused variables and functions
 * - Unreachable code
 * - Deprecated patterns
 */

module.exports = {
  extends: [
    'eslint:recommended'
  ],
  
  plugins: [
    '@typescript-eslint',
    'unused-imports'
  ],
  
  rules: {
    // =============================================
    // UNUSED IMPORT AND EXPORT DETECTION
    // =============================================
    
    // Automatically remove unused imports
    'unused-imports/no-unused-imports': 'error',
    
    // Detect unused variables but allow underscore prefix
    'unused-imports/no-unused-vars': [
      'error',
      {
        vars: 'all', 
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
        ignoreRestSiblings: true
      }
    ],
    
    // Standard TypeScript unused variable detection
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
        destructuredArrayIgnorePattern: '^_',
        ignoreRestSiblings: true
      }
    ],
    
    // =============================================
    // DEAD CODE PATTERNS
    // =============================================
    
    // Detect unreachable code
    'no-unreachable': 'error',
    
    // Detect functions/variables that are never used
    'no-unused-expressions': [
      'error',
      {
        allowShortCircuit: true,
        allowTernary: true,
        allowTaggedTemplates: true
      }
    ],
    
    // Detect empty functions (potential dead code)
    '@typescript-eslint/no-empty-function': [
      'warn',
      {
        allow: [
          'arrowFunctions',
          'functions',
          'methods',
          'constructors'
        ]
      }
    ],
    
    // =============================================
    // DEPRECATED AND OBSOLETE PATTERNS
    // =============================================
    
    // Detect usage of deprecated APIs (plugin not available yet)
    // 'deprecation/deprecation': 'warn',
    
    // Prefer const for never-reassigned variables
    'prefer-const': 'error',
    
    // Detect variables that are never reassigned
    '@typescript-eslint/prefer-readonly': 'warn',
    
    // =============================================
    // TYPESCRIPT-SPECIFIC DEAD CODE
    // =============================================
    
    // Detect unused type parameters
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        varsIgnorePattern: '^_',
        argsIgnorePattern: '^_'
      }
    ],
    
    // Detect unnecessary type assertions
    '@typescript-eslint/no-unnecessary-type-assertion': 'error',
    
    // Detect unnecessary conditions
    '@typescript-eslint/no-unnecessary-condition': 'warn',
    
    // =============================================
    // IMPORT/EXPORT OPTIMIZATIONS
    // =============================================
    
    // Prefer default export when module exports single value
    'import/prefer-default-export': 'off', // We prefer named exports
    
    // Import optimizations (disabled due to missing plugin config)
    // 'import/no-duplicates': 'error',
    // 'import/order': 'warn',
    
    // =============================================
    // PERFORMANCE OPTIMIZATIONS
    // =============================================
    
    // Detect expensive operations in loops
    'no-await-in-loop': 'warn',
    
    // Detect expensive operations in loops is built-in
    
    // Performance optimizations (these rules may not exist in current ESLint)
    // 'prefer-for-of': 'warn',
    // 'no-unsafe-regex': 'error'
  },
  
  // =============================================
  // OVERRIDES FOR SPECIFIC FILE PATTERNS
  // =============================================
  overrides: [
    
    // Test files - more lenient unused variable rules
    {
      files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
      rules: {
        '@typescript-eslint/no-unused-vars': 'off',
        'unused-imports/no-unused-vars': 'off',
        '@typescript-eslint/no-empty-function': 'off'
      }
    },
    
    // Type definition files - allow unused exports
    {
      files: ['**/*.d.ts', '**/*.types.ts'],
      rules: {
        '@typescript-eslint/no-unused-vars': 'off',
        'unused-imports/no-unused-imports': 'off'
      }
    },
    
    // Configuration files - allow unused imports for optional configs
    {
      files: ['*.config.js', '*.config.ts', '.eslintrc.js'],
      rules: {
        '@typescript-eslint/no-unused-vars': 'off',
        'unused-imports/no-unused-imports': 'warn'
      }
    },
    
    // Legacy code - gradual migration warnings
    {
      files: ['**/legacy/**/*.ts', '**/deprecated/**/*.ts'],
      rules: {
        '@typescript-eslint/no-unused-vars': 'warn',
        'unused-imports/no-unused-imports': 'warn',
        'deprecation/deprecation': 'error'
      }
    }
  ],
  
  // =============================================
  // PARSER AND ENVIRONMENT
  // =============================================
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    project: './tsconfig.json'
  },
  
  env: {
    node: true,
    es2022: true
  },
  
  settings: {
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json'
      }
    }
  }
};

/**
 * USAGE INSTRUCTIONS:
 * 
 * 1. Run dead code detection:
 *    npx eslint --config .eslintrc.deadcode.js --ext .ts,.tsx src/
 * 
 * 2. Auto-fix unused imports:
 *    npx eslint --config .eslintrc.deadcode.js --ext .ts,.tsx --fix src/
 * 
 * 3. Generate dead code report:
 *    npx eslint --config .eslintrc.deadcode.js --ext .ts,.tsx --format json src/ > deadcode-report.json
 * 
 * 4. Add to package.json scripts:
 *    "deadcode": "eslint --config .eslintrc.deadcode.js --ext .ts,.tsx src/",
 *    "deadcode:fix": "eslint --config .eslintrc.deadcode.js --ext .ts,.tsx --fix src/"
 */