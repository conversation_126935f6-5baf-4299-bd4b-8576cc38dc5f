version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: farmhouse-db-dev
    environment:
      POSTGRES_DB: farmhouse_rental
      POSTGRES_USER: farmhouse_user
      POSTGRES_PASSWORD: farmhouse_password
    ports:
      - "5432:5432"
    volumes:
      - farmhouse_db_dev_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U farmhouse_user -d farmhouse_rental"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Backend Development Server
  backend:
    build:
      context: .
      target: backend
    container_name: farmhouse-backend-dev
    ports:
      - "5000:5000"
    environment:
      DATABASE_URL: ************************************************************/farmhouse_rental
      NODE_ENV: development
      JWT_SECRET: dev-jwt-secret-key-change-in-production
      COOKIE_SECRET: dev-cookie-secret-key-change-in-production
    depends_on:
      database:
        condition: service_healthy
    volumes:
      - ./server:/app/server
      - ./shared:/app/shared
      - ./uploads:/app/uploads
      - ./migrations:/app/migrations
      - ./drizzle.config.ts:/app/drizzle.config.ts
    command: npm run dev
    restart: unless-stopped

  # Frontend Development Server
  frontend:
    image: node:18-alpine
    container_name: farmhouse-frontend-dev
    working_dir: /app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./:/app
      - /app/node_modules
    command: sh -c "npm install && npm run dev:client"
    depends_on:
      - backend
    restart: unless-stopped

  # Database Management
  adminer:
    image: adminer:latest
    container_name: farmhouse-adminer-dev
    ports:
      - "8080:8080"
    depends_on:
      - database
    environment:
      ADMINER_DEFAULT_SERVER: database

volumes:
  farmhouse_db_dev_data: