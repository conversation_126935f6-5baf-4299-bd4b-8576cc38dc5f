# Environment Configuration
# Set this to 'development', 'production', or 'test'
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://farmhouse_user:farmhouse_password@localhost:5432/farmhouse_rental

# Database Connection Tuning (Optional - Phase 1 optimizations)
# DB_MAX_CONNECTIONS=8              # Maximum pool connections (default: 6 prod, 3 dev)
# DB_CONNECTION_TIMEOUT=30000       # Connection timeout in ms (default: 30000)
# DB_QUERY_TIMEOUT=30000           # Query timeout in ms (default: 30000)
# DB_HEALTH_CHECK_INTERVAL=30000   # Health check interval in ms (default: 30000)
# DB_IDLE_TIMEOUT=60000            # Idle connection timeout in ms (default: 60000)

# Security Secrets (CHANGE IN PRODUCTION!)
JWT_SECRET=your-super-secure-jwt-secret-here
COOKIE_SECRET=your-super-secure-cookie-secret-here
SESSION_SECRET=your-super-secure-session-secret-here

# Application Configuration
PORT=5000

# Twilio SMS & WhatsApp Service (Optional)
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=992e4453d12380312563dfb212ccc775
TWILIO_VERIFY_SERVICE_SID=your-twilio-verify-service-sid
TWILIO_MESSAGING_SID=your-twilio-messaging-service-sid

# WhatsApp Business API Configuration
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
WHATSAPP_WEBHOOK_URL=https://your-domain.com/api/webhooks/whatsapp
WHATSAPP_WEBHOOK_TOKEN=your-webhook-verification-token

# SendGrid Email Service (Optional)
SENDGRID_API_KEY=your-sendgrid-api-key

# Cloudinary Image Storage (Optional)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret


