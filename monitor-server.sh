#!/bin/bash

# Check if server is running
check_server() {
    if [ -f server.pid ]; then
        PID=$(cat server.pid)
        if ps -p $PID > /dev/null 2>&1; then
            if curl -s http://localhost:5000/api/health > /dev/null 2>&1; then
                echo "✅ Server is running (PID: $PID)"
                return 0
            else
                echo "⚠️ Server process exists but not responding"
                return 1
            fi
        else
            echo "❌ Server process not found"
            return 1
        fi
    else
        echo "❌ No server PID file found"
        return 1
    fi
}

# Restart server if needed
restart_if_needed() {
    if ! check_server; then
        echo "🔄 Restarting server..."
        ./start-server.sh
    fi
}

# Main monitoring loop
if [ "$1" = "watch" ]; then
    echo "🔍 Starting server monitor..."
    while true; do
        restart_if_needed
        sleep 30
    done
else
    check_server
fi