# Farmhouse Rental Platform

A full-stack web application for farmhouse rental listings built with React, TypeScript, Express, and PostgreSQL.

## 🚀 Quick Start

### Local Development with Docker (Recommended)

1. **Start the application:**
   ```bash
   npm run docker:up
   ```

2. **Access the application:**
   - **Frontend & Backend**: http://localhost:3000
   - **Database Admin (Adminer)**: http://localhost:8080
     - Server: `database`
     - Username: `farmhouse_user`
     - Password: `farmhouse_password`
     - Database: `farmhouse_rental`

3. **Stop the application:**
   ```bash
   npm run docker:down
   ```

### Other Docker Commands

```bash
# View application logs
npm run docker:logs

# Rebuild containers (after code changes)
npm run docker:build

# Start development mode with hot reloading
npm run docker:dev

# Stop development containers
npm run docker:dev:down
```

## 🛠️ Development

### Development Authentication System

For cost-free development and testing, use these pre-configured test numbers:

#### Test Phone Numbers
- `+************` - Test User
- `+919000999888` - Test User 2  
- `+919391010188` - Test Property Owner

#### Master Code: `999999`
- **No SMS charges** for test numbers in development
- **Enhanced rate limits** (50 requests/minute vs 5 in production)
- **Automatic detection** of development environment

#### Quick Test Example
```bash
# Send OTP to test number
curl -X POST http://localhost:5000/api/auth/otp/send-otp \
  -H "Content-Type: application/json" \
  -d '{"identifier": "+************", "type": "sms"}'

# Response: "Development OTP sent. Use code: 999999"

# Verify with master code
curl -X POST http://localhost:5000/api/auth/otp/verify-otp-login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "+************", "code": "999999", "type": "sms"}'
```

📖 **Complete Documentation**: 
- [Development Guide](docs/setup/DEVELOPMENT_GUIDE.md)
- [Environment Configuration](docs/setup/ENVIRONMENT_CONFIGURATION.md)

### Traditional Local Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   # Copy and customize environment files
   cp .env.development.example .env.development
   cp .env.production.example .env.production
   ```

3. **Start the database:**
   ```bash
   # Using Docker for database only
   docker run -d \
     --name farmhouse-db \
     -e POSTGRES_DB=farmhouse_rental \
     -e POSTGRES_USER=farmhouse_user \
     -e POSTGRES_PASSWORD=farmhouse_password \
     -p 5432:5432 \
     postgres:15-alpine
   ```

4. **Run database migrations:**
   ```bash
   npm run db:migrate
   ```

5. **Start development servers:**
   ```bash
   # Terminal 1: Backend
   npm run dev:server

   # Terminal 2: Frontend
   npm run dev:client
   ```

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with httpOnly cookies
- **File Upload**: Multer for local storage
- **State Management**: TanStack React Query

### Project Structure
```
farmhouse/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── contexts/      # React contexts
│   │   ├── hooks/         # Custom hooks
│   │   └── lib/           # Utilities
├── server/                # Express backend
│   ├── routes.ts          # API routes
│   ├── storage.ts         # Database operations
│   ├── db.ts             # Database connection
│   └── vite.ts           # Development server setup
├── shared/                # Shared types and schemas
├── migrations/            # Database migrations
└── docker-compose.yml     # Docker configuration
```

## 🔧 Available Scripts

### Development
- `npm run dev` - Start both frontend and backend
- `npm run dev:client` - Start frontend only
- `npm run dev:server` - Start backend only

### Build & Deploy
- `npm run build` - Build for production
- `npm run build:frontend` - Build frontend only
- `npm run build:backend` - Build backend only
- `npm run start` - Start production server

### Database
- `npm run db:push` - Push schema changes
- `npm run db:migrate` - Run migrations

### Docker
- `npm run docker:up` - Start all services
- `npm run docker:down` - Stop all services
- `npm run docker:dev` - Start development mode
- `npm run docker:dev:down` - Stop development mode
- `npm run docker:logs` - View logs
- `npm run docker:build` - Rebuild containers

### Testing
- `npm run test` - Run unit tests
- `npm run test:unit` - Run unit tests once
- `npm run test:unit:watch` - Run unit tests in watch mode
- `npm run test:integration` - Run integration tests
- `npm run test:integration:watch` - Run integration tests in watch mode
- `npm run test:e2e` - Run E2E tests headlessly
- `npm run test:e2e:ui` - Run E2E tests with interactive UI
- `npm run test:all` - Run all tests (unit + integration + E2E)
- `npm run test:coverage` - Generate test coverage report

### Code Quality
- `npm run check` - TypeScript type checking

## 🧪 Testing

This project implements a comprehensive testing strategy following the testing pyramid (70% unit tests, 20% integration tests, 10% E2E tests).

### Quick Testing Commands

```bash
# Unit Tests
npm run test:unit              # Run all unit tests once
npm run test:unit:watch        # Run unit tests in watch mode
npm run test                   # Run unit tests (default vitest command)

# Integration Tests  
npm run test:integration       # Run integration tests once
npm run test:integration:watch # Run integration tests in watch mode

# E2E Tests
npm run test:e2e              # Run all E2E tests headlessly
npm run test:e2e:ui           # Run E2E tests with interactive UI

# Combined Testing
npm run test:all              # Run unit + integration + E2E tests
npm run test:coverage         # Run unit tests with coverage report
```

### Test Coverage

- **📋 Unit Tests (70%)**: 161+ tests covering backend API routes, authentication, schema validation, React hooks, and components
- **🔄 Integration Tests (20%)**: 50+ tests for complete API workflows with database interactions
- **🌐 E2E Tests (10%)**: 35+ tests for critical user journeys across multiple browsers

### Testing Structure

```
tests/
├── unit/                      # Unit tests (fast, isolated)
│   ├── backend/              # Backend unit tests
│   │   ├── auth-middleware.test.ts    # Authentication & authorization (15 tests)
│   │   ├── auth-routes.test.ts        # Auth API endpoints (25 tests)
│   │   └── schema-validation.test.ts   # Zod schema validation (41 tests)
│   └── frontend/             # Frontend unit tests
│       ├── useKeyboardNavigation.test.tsx  # Keyboard navigation hook (28 tests)
│       ├── useToast.test.tsx            # Toast notification system (52 tests)
│       └── AuthContext.test.tsx         # Authentication context tests
├── integration/              # Integration tests (medium speed, database)
│   ├── auth-endpoints.test.ts          # Complete auth workflows
│   ├── property-endpoints.test.ts      # Property CRUD with database
│   └── booking-endpoints.test.ts       # Booking system business logic
├── e2e/                      # End-to-end tests (slow, full workflows)
│   ├── user-registration-flow.spec.ts  # Registration & login journey
│   ├── property-booking-flow.spec.ts   # Property search & booking
│   ├── owner-management-flow.spec.ts   # Property owner dashboard
│   └── accessibility-responsive.spec.ts # Accessibility & responsive design
├── fixtures/                 # Test data factories
├── mocks/                    # Mock service worker handlers
└── utils/                    # Testing utilities
```

### Development Testing Workflow

```bash
# 1. Start development server (required for E2E tests)
npm run dev

# 2. Run tests in watch mode during development
npm run test:unit:watch        # Terminal 2: Unit tests
npm run test:integration:watch # Terminal 3: Integration tests

# 3. Run specific test files
npx vitest tests/unit/backend/auth-routes.test.ts
npx playwright test tests/e2e/user-registration-flow.spec.ts

# 4. Debug tests
npm run test:e2e:ui           # Interactive E2E debugging
npx vitest --reporter=verbose # Detailed unit test output
```

### Test Categories

#### 🔧 **Unit Tests** (Fast - seconds)
- **Backend**: Authentication middleware, API route handlers, schema validation
- **Frontend**: React hooks, context providers, utility functions
- **Coverage**: Individual functions and components in isolation

#### 🔄 **Integration Tests** (Medium - 30-60s)
- **API Endpoints**: Full request/response cycles with database
- **Authentication**: Complete login/logout workflows
- **Business Logic**: Property booking, availability checking, pricing calculations

#### 🌐 **E2E Tests** (Slow - 2-5 minutes)
- **User Journeys**: Registration → Property Search → Booking → Review
- **Owner Workflows**: Property Creation → Management → Analytics
- **Cross-browser**: Chrome, Firefox, Safari, Mobile devices
- **Accessibility**: Keyboard navigation, screen readers, ARIA compliance

### Advanced Testing Commands

```bash
# Run specific test suites
npx vitest tests/unit/backend/               # Backend unit tests only
npx vitest tests/unit/frontend/              # Frontend unit tests only
npx vitest tests/integration/auth-endpoints.test.ts  # Specific integration test

# E2E test options
npx playwright test --headed                 # Run E2E with visible browser
npx playwright test --project=chromium       # Specific browser
npx playwright test --grep "authentication"  # Pattern matching

# Coverage and reporting
npm run test:coverage                        # Generate HTML coverage report
npx playwright show-report                  # View E2E test results

# Performance testing
npx vitest --reporter=verbose --bail=1      # Stop on first failure
npx playwright test --timeout=60000         # Custom timeout
```

### Troubleshooting Tests

```bash
# Clear test cache
npx vitest --run --clearCache

# Debug failing tests
npm run test:unit -- --reporter=verbose --bail=1
npx playwright test --debug

# Check test environment
npm run check                               # TypeScript type checking
npm run db:push                            # Reset database for integration tests

# Common fixes
npm run dev                                 # Ensure dev server running (for E2E)
npm install                                 # Refresh dependencies
```

### CI/CD Integration

```bash
# Commands for continuous integration
npm run test:all              # Run complete test suite
npm run test:coverage         # Generate coverage reports
npm run check                 # Type checking
npm run build                 # Ensure build passes
```

### Test Configuration Files

- **`vitest.config.ts`** - Unit test configuration with 80% coverage thresholds
- **`vitest.integration.config.ts`** - Integration tests with database setup
- **`playwright.config.ts`** - E2E tests across browsers and devices
- **`tests/setup.ts`** - Global test environment setup

## 🔄 CI/CD Pipeline

This project uses GitHub Actions for automated testing and deployment with mandatory checks for branch protection.

### 🛡️ Branch Protection Strategy

- **✅ Mandatory**: Unit Tests + Integration Tests + Build Check (must pass for merge)
- **⚪ Optional**: E2E Tests (informational only, won't block merge)

### Quick CI Commands

```bash
# Validate changes locally before pushing
npm run ci:validate              # Run all mandatory CI checks locally

# Individual test commands (same as CI)
npm run check                    # TypeScript compilation
npm run test:unit               # Unit tests (mandatory)
npm run test:integration        # Integration tests (mandatory)
npm run build                   # Build check (mandatory)
npm run test:e2e                # E2E tests (optional)
```

### 🚀 CI Pipeline Jobs

1. **🧪 Unit Tests** (Mandatory, ~2-3 min)
   - TypeScript compilation check
   - 199+ unit tests with coverage reporting
   - Codecov integration for coverage tracking

2. **🔗 Integration Tests** (Mandatory, ~3-5 min)
   - PostgreSQL service container setup
   - Database migration and seeding
   - 83+ integration tests with real database

3. **🌐 E2E Tests** (Optional, ~8-12 min)
   - Multi-browser testing (Chrome, Firefox, Safari)
   - Visual regression and accessibility testing
   - Won't block merge if failing

4. **🏗️ Build Check** (Mandatory, ~2-3 min)
   - Frontend and backend build validation
   - Artifact generation for deployment

### Development Workflow

```bash
# 1. Create feature branch
git checkout -b feature/my-feature

# 2. Make changes and validate locally
npm run ci:validate  # Ensures all mandatory checks pass

# 3. Push and create PR
git push origin feature/my-feature
# → CI automatically runs all checks

# 4. Merge when ✅ mandatory checks pass
# (E2E failures won't block merge)
```

### Branch Protection Rules

**Main Branch Requirements**:
- ✅ `🧪 Unit Tests (Mandatory)` - Must pass
- ✅ `🔗 Integration Tests (Mandatory)` - Must pass  
- ✅ `🏗️ Build Check` - Must pass
- ⚪ `🌐 E2E Tests (Optional)` - Informational only

**Setup Instructions**: See [CI/CD Setup Guide](docs/setup/CI_CD_SETUP.md) for detailed configuration.

### Performance Metrics

- **Unit Tests**: Target < 3 minutes
- **Integration Tests**: Target < 5 minutes  
- **Total Pipeline**: Target < 20 minutes
- **Success Rate**: 100% for mandatory checks

## 🚢 Deployment

### Repl.it Deployment
This application is configured for Repl.it deployment with automatic detection of the runtime environment.

### Docker Production
For production deployment using Docker:

1. Build the production image:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. Environment variables for production:
   - `DATABASE_URL` - PostgreSQL connection string
   - `JWT_SECRET` - Strong secret for JWT tokens
   - `COOKIE_SECRET` - Strong secret for cookies
   - `NODE_ENV=production`

## 🔐 Security Features

- **Rate Limiting**: API endpoints are rate-limited
- **CORS**: Properly configured cross-origin requests
- **Helmet**: Security headers
- **JWT Authentication**: Secure token-based auth
- **Input Validation**: Zod schema validation
- **SQL Injection Protection**: Parameterized queries

## 📊 Performance Features

- **Database Indexing**: Optimized queries
- **Image Optimization**: Lazy loading
- **Code Splitting**: Optimized bundle size
- **Caching**: Response caching headers

## 🗃️ Database Schema

Key entities:
- **Users**: User accounts and authentication
- **Properties**: Farmhouse listings
- **Bookings**: Rental bookings
- **Reviews**: Property reviews

## 📝 API Documentation

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Properties
- `GET /api/properties` - List properties
- `GET /api/properties/:id` - Get property details
- `POST /api/properties` - Create property (auth required)
- `PUT /api/properties/:id` - Update property (auth required)

### Bookings
- `GET /api/bookings` - List user bookings (auth required)
- `POST /api/bookings` - Create booking (auth required)
- `PUT /api/bookings/:id` - Update booking (auth required)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.