# Development Environment Configuration
NODE_ENV=development

# Development Database (Local or Development Server)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Development Secrets (These are safe for development)
JWT_SECRET=dev-jwt-secret-change-in-production
COOKIE_SECRET=dev-cookie-secret-change-in-production
SESSION_SECRET=dev-session-secret-change-in-production

# Application Configuration
PORT=5000
PGDATABASE="neondb"
PGHOST="ep-cool-boat-afjnxpz5.c-2.us-west-2.aws.neon.tech"
PGPORT="5432"
PGUSER="neondb_owner"
PGPASSWORD="npg_mRTWZCuU49bO"

# Cloudinary Configuration
CLOUDINARY_API_KEY="***************"
CLOUDINARY_API_SECRET="0h_4eBelAYNRo8z8o1Um7Qxjih8"
CLOUDINARY_CLOUD_NAME="di985ryw1"

# Twilio SMS Configuration
TWILIO_MESSAGING_SID="MGb8238b86c48986f1f20894266ab730a9"
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="992e4453d12380312563dfb212ccc775"

# SendGrid Email Configuration
SENDGRID_API_KEY="*********************************************************************"

# Application Configuration
PORT=5000
NODE_ENV="development"
USE_SECURE_COOKIES=true

# Security Headers
ENABLE_SECURITY_HEADERS=true
ENABLE_RATE_LIMITING=true