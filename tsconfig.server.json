{"extends": "./tsconfig.json", "compilerOptions": {"module": "ESNext", "target": "ES2022", "outDir": "./dist", "rootDir": ".", "noEmit": false, "declaration": false, "moduleResolution": "node", "allowImportingTsExtensions": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictPropertyInitialization": true, "strictBindCallApply": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true}, "include": ["server/**/*", "shared/**/*"], "exclude": ["node_modules", "dist", "client"]}