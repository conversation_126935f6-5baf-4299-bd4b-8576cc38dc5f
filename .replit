modules = ["nodejs-20", "bash", "web", "postgresql-16"]
run = "npm run dev"
hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"
packages = ["jq", "lsof"]

[deployment]
deploymentTarget = "autoscale"
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "npm run start"]

[workflows]
runButton = "Development Server"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run start"

[[workflows.workflow]]
name = "Production Build & Start"
author = 43175130
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run build"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run start"

[[workflows.workflow]]
name = "Development Server"
author = 37099219
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 24678
externalPort = 3000

[[ports]]
localPort = 33421
externalPort = 3001

[[ports]]
localPort = 33529
externalPort = 9000

[[ports]]
localPort = 34693
externalPort = 4200

[[ports]]
localPort = 35183
externalPort = 5173

[[ports]]
localPort = 36095
externalPort = 8099

[[ports]]
localPort = 37087
externalPort = 8000

[[ports]]
localPort = 37329
externalPort = 3003

[[ports]]
localPort = 39799
externalPort = 8008

[[ports]]
localPort = 40155
externalPort = 6000

[[ports]]
localPort = 40777
externalPort = 6800

[[ports]]
localPort = 42563
externalPort = 5000

[[ports]]
localPort = 43087
externalPort = 8080

[[ports]]
localPort = 45417
externalPort = 3002

[[ports]]
localPort = 46529
externalPort = 8081
