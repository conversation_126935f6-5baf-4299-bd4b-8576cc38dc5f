# Booking Calendar Integration System Design

Author: Fazeel Usmani  
Date: August 6, 2025

## 🎯 Overview

This document outlines the complete design for integrating a unified booking calendar system into the Farmhouse platform. The system provides venue owners with a single source of truth for all bookings while enabling management through WhatsApp, website interface, and external calendar systems (Google Calendar, Airbnb, etc.).

## 🌟 Key Integration Points

### Unified Calendar System

The booking calendar serves as the **single source of truth** across all platforms:

1. **BookAFarm.com Public View**
   - Interactive calendar on property details page
   - Real-time availability display
   - Date selection for booking requests
   - Mobile-responsive design

2. **Owner Dashboard**
   - Full calendar management interface
   - Add/edit/remove bookings
   - View booking details and guest information
   - Sync status monitoring

3. **WhatsApp Integration**
   - Natural language booking creation
   - Availability queries
   - Booking confirmations
   - Calendar updates via messaging

4. **External Calendar Sync**
   - iCal feed for Google Calendar, Outlook, etc.
   - Airbnb/VRBO integration ready
   - Two-way sync capabilities

### Real-Time Synchronization

All calendar updates are instantly reflected across all platforms:

```typescript
// Real-time update flow
Owner adds booking via WhatsApp → Database update → Cache invalidation → 
WebSocket broadcast → All UI components update → iCal feed regenerated
```

## 🏗️ System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Data Layer"
        DB[(PostgreSQL Database)]
        Redis[(Redis Cache)]
    end
    
    subgraph "Core Services"
        CS[Calendar Service]
        BS[Booking Service]
        WS[WhatsApp Service]
        IS[iCal Service]
    end
    
    subgraph "API Layer"
        REST[REST API]
        WH[Webhook Handler]
        ICAL[iCal Feed]
    end
    
    subgraph "External Integrations"
        WA[WhatsApp Business API]
        GC[Google Calendar]
        AB[Airbnb Calendar]
        OTA[Other OTAs]
    end
    
    subgraph "Frontend"
        WEB[React Web App]
        FC[FullCalendar Component]
    end
    
    DB --> CS
    Redis --> CS
    CS --> BS
    CS --> IS
    BS --> REST
    BS --> WH
    IS --> ICAL
    WS --> WH
    REST --> WEB
    ICAL --> GC
    ICAL --> AB
    ICAL --> OTA
    WA --> WS
    FC --> WEB
```

## 📊 Database Schema

### Core Tables

```sql
-- Enhanced bookings table with calendar integration
CREATE TABLE calendar_bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'confirmed',
    booking_type VARCHAR(50) DEFAULT 'direct',
    guest_name VARCHAR(255),
    guest_phone VARCHAR(20),
    guest_count INTEGER,
    notes TEXT,
    source VARCHAR(50) DEFAULT 'website',
    external_id VARCHAR(255),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_date_range CHECK (end_date >= start_date),
    CONSTRAINT valid_status CHECK (status IN ('confirmed', 'tentative', 'blocked', 'cancelled'))
);

-- Index for fast overlap queries
CREATE INDEX idx_calendar_bookings_overlap ON calendar_bookings 
    (property_id, start_date, end_date) 
    WHERE status != 'cancelled';

-- Calendar sync metadata
CREATE TABLE calendar_sync_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id),
    calendar_type VARCHAR(50) NOT NULL,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_token VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    webhook_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(property_id, calendar_type)
);

-- WhatsApp conversation tracking
CREATE TABLE whatsapp_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id),
    phone_number VARCHAR(20) NOT NULL,
    conversation_state JSONB DEFAULT '{}',
    last_message_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Core Calendar Service

### `/server/services/CalendarService.ts`

```typescript
import { db } from '../db';
import { calendarBookings } from '../schema';
import { Redis } from 'ioredis';
import ical from 'ical-generator';
import { format, parseISO } from 'date-fns';

export class CalendarService {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }

  // Core booking operations
  async createBooking(data: CreateBookingDTO): Promise<Booking> {
    // Check for conflicts
    const hasConflict = await this.checkBookingConflict(
      data.propertyId,
      data.startDate,
      data.endDate
    );
    
    if (hasConflict) {
      throw new ConflictError('Booking dates overlap with existing booking');
    }
    
    // Create booking
    const booking = await db.transaction(async (tx) => {
      const [newBooking] = await tx.insert(calendarBookings)
        .values({
          ...data,
          status: data.status || 'confirmed',
          source: data.source || 'website'
        })
        .returning();
      
      // Invalidate cache
      await this.invalidateCalendarCache(data.propertyId);
      
      // Trigger webhooks
      await this.triggerBookingWebhooks(newBooking);
      
      return newBooking;
    });
    
    return booking;
  }

  async checkBookingConflict(
    propertyId: string,
    startDate: Date,
    endDate: Date,
    excludeBookingId?: string
  ): Promise<boolean> {
    const query = db.select()
      .from(calendarBookings)
      .where(
        and(
          eq(calendarBookings.propertyId, propertyId),
          ne(calendarBookings.status, 'cancelled'),
          // Overlap check: start < new_end AND end > new_start
          sql`${calendarBookings.startDate} < ${endDate}`,
          sql`${calendarBookings.endDate} > ${startDate}`
        )
      );
    
    if (excludeBookingId) {
      query.where(ne(calendarBookings.id, excludeBookingId));
    }
    
    const conflicts = await query;
    return conflicts.length > 0;
  }

  async getPropertyCalendar(
    propertyId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<CalendarData> {
    // Try cache first
    const cacheKey = `calendar:${propertyId}:${startDate}:${endDate}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }
    
    // Fetch from database
    const bookings = await db.select()
      .from(calendarBookings)
      .where(
        and(
          eq(calendarBookings.propertyId, propertyId),
          ne(calendarBookings.status, 'cancelled'),
          startDate ? gte(calendarBookings.endDate, startDate) : undefined,
          endDate ? lte(calendarBookings.startDate, endDate) : undefined
        )
      )
      .orderBy(calendarBookings.startDate);
    
    const calendarData = {
      propertyId,
      bookings,
      availability: this.calculateAvailability(bookings, startDate, endDate)
    };
    
    // Cache for 5 minutes
    await this.redis.setex(cacheKey, 300, JSON.stringify(calendarData));
    
    return calendarData;
  }

  async generateICalFeed(propertyId: string): Promise<string> {
    const cacheKey = `ical:${propertyId}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return cached;
    }
    
    const property = await this.getPropertyDetails(propertyId);
    const bookings = await this.getPropertyBookings(propertyId);
    
    const calendar = ical({
      name: `${property.name} - Booking Calendar`,
      prodId: '//BookAFarm//Calendar//EN',
      timezone: 'Asia/Kolkata'
    });
    
    bookings.forEach(booking => {
      calendar.createEvent({
        start: booking.startDate,
        end: booking.endDate,
        summary: booking.guestName || 'Booked',
        description: `Booking ID: ${booking.id}\nStatus: ${booking.status}\n${booking.notes || ''}`,
        status: booking.status === 'tentative' ? 'TENTATIVE' : 'CONFIRMED',
        uid: booking.id
      });
    });
    
    const icalString = calendar.toString();
    
    // Cache for 1 hour
    await this.redis.setex(cacheKey, 3600, icalString);
    
    return icalString;
  }

  private async invalidateCalendarCache(propertyId: string): Promise<void> {
    const pattern = `calendar:${propertyId}:*`;
    const keys = await this.redis.keys(pattern);
    
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
    
    // Also invalidate iCal cache
    await this.redis.del(`ical:${propertyId}`);
  }
}
```

## 📱 WhatsApp Integration

### WhatsApp Service Architecture

```typescript
// /server/services/WhatsAppService.ts
import { Twilio } from 'twilio';
import { CalendarService } from './CalendarService';
import { parseMessage, formatBookingConfirmation } from '../utils/whatsapp';

export class WhatsAppService {
  private client: Twilio;
  private calendarService: CalendarService;
  
  constructor() {
    this.client = new Twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );
    this.calendarService = new CalendarService();
  }

  async handleIncomingMessage(message: WhatsAppMessage): Promise<void> {
    const { from, body, profileName } = message;
    
    try {
      // Parse the message intent
      const intent = await this.parseMessageIntent(body);
      
      switch (intent.type) {
        case 'ADD_BOOKING':
          await this.handleAddBooking(from, intent.data);
          break;
          
        case 'CHECK_AVAILABILITY':
          await this.handleCheckAvailability(from, intent.data);
          break;
          
        case 'LIST_BOOKINGS':
          await this.handleListBookings(from, intent.data);
          break;
          
        case 'CANCEL_BOOKING':
          await this.handleCancelBooking(from, intent.data);
          break;
          
        default:
          await this.sendHelpMessage(from);
      }
    } catch (error) {
      await this.sendErrorMessage(from, error.message);
    }
  }

  async handleAddBooking(to: string, data: BookingData): Promise<void> {
    // Get property associated with this phone number
    const property = await this.getPropertyByPhone(to);
    
    // Create booking
    const booking = await this.calendarService.createBooking({
      propertyId: property.id,
      startDate: data.startDate,
      endDate: data.endDate,
      guestName: data.guestName,
      notes: data.notes,
      source: 'whatsapp',
      createdBy: property.ownerId
    });
    
    // Send confirmation
    const message = formatBookingConfirmation(booking);
    await this.sendMessage(to, message);
    
    // Send calendar link
    await this.sendMessage(
      to,
      `📅 View your calendar: ${process.env.APP_URL}/calendar/${property.id}`
    );
  }

  async sendInteractiveBookingFlow(to: string): Promise<void> {
    const message = {
      to,
      type: 'interactive',
      interactive: {
        type: 'flow',
        header: {
          type: 'text',
          text: 'Add New Booking'
        },
        body: {
          text: 'Fill in the booking details'
        },
        action: {
          name: 'flow',
          parameters: {
            flow_message_version: '3',
            flow_token: 'booking_flow_token',
            flow_id: process.env.WHATSAPP_BOOKING_FLOW_ID,
            flow_cta: 'Book Now',
            flow_action: 'navigate',
            flow_action_payload: {
              screen: 'BOOKING_FORM'
            }
          }
        }
      }
    };
    
    await this.client.messages.create(message);
  }

  private parseMessageIntent(message: string): MessageIntent {
    const normalizedMessage = message.toLowerCase().trim();
    
    // Booking patterns
    const bookingPatterns = [
      /block\s+(\d{1,2})\s*-\s*(\d{1,2})\s+(\w+)/i,
      /book\s+(\d{4}-\d{2}-\d{2})\s+to\s+(\d{4}-\d{2}-\d{2})/i,
      /add\s+booking/i
    ];
    
    for (const pattern of bookingPatterns) {
      const match = normalizedMessage.match(pattern);
      if (match) {
        return {
          type: 'ADD_BOOKING',
          data: this.extractBookingData(match)
        };
      }
    }
    
    // Check availability patterns
    if (normalizedMessage.includes('available') || normalizedMessage.includes('check')) {
      return { type: 'CHECK_AVAILABILITY', data: {} };
    }
    
    // List bookings
    if (normalizedMessage.includes('list') || normalizedMessage.includes('show')) {
      return { type: 'LIST_BOOKINGS', data: {} };
    }
    
    return { type: 'UNKNOWN', data: {} };
  }
}
```

### WhatsApp Webhook Handler

```typescript
// /server/routes/whatsapp.ts
import { Router } from 'express';
import { WhatsAppService } from '../services/WhatsAppService';

const router = Router();
const whatsappService = new WhatsAppService();

// Webhook verification
router.get('/webhook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];
  
  if (mode === 'subscribe' && token === process.env.WHATSAPP_VERIFY_TOKEN) {
    res.status(200).send(challenge);
  } else {
    res.sendStatus(403);
  }
});

// Handle incoming messages
router.post('/webhook', async (req, res) => {
  try {
    const { entry } = req.body;
    
    for (const item of entry) {
      const { changes } = item;
      
      for (const change of changes) {
        if (change.field === 'messages') {
          const { messages, contacts } = change.value;
          
          for (const message of messages) {
            await whatsappService.handleIncomingMessage({
              ...message,
              profileName: contacts[0]?.profile?.name
            });
          }
        }
      }
    }
    
    res.sendStatus(200);
  } catch (error) {
    console.error('WhatsApp webhook error:', error);
    res.sendStatus(500);
  }
});

export default router;
```

## 🖥️ Frontend Calendar Integration

### Public Property Details Calendar

```typescript
// /client/src/components/PropertyDetailsCalendar.tsx
import React, { useState } from 'react';
import { format, addMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isWithinInterval } from 'date-fns';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeft, ChevronRight, Calendar, Info } from 'lucide-react';
import { api } from '../lib/api';
import { cn } from '../lib/utils';

interface PropertyDetailsCalendarProps {
  propertyId: string;
  pricePerNight: number;
  onDateSelect?: (startDate: Date, endDate: Date) => void;
}

export function PropertyDetailsCalendar({ 
  propertyId, 
  pricePerNight,
  onDateSelect 
}: PropertyDetailsCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);

  // Fetch calendar data for 3 months
  const { data: calendarData, isLoading } = useQuery({
    queryKey: ['property-calendar', propertyId, format(currentMonth, 'yyyy-MM')],
    queryFn: async () => {
      const startDate = startOfMonth(currentMonth);
      const endDate = endOfMonth(addMonths(currentMonth, 2));
      
      const response = await api.get(`/calendar/${propertyId}`, {
        params: {
          startDate: format(startDate, 'yyyy-MM-dd'),
          endDate: format(endDate, 'yyyy-MM-dd')
        }
      });
      
      return response.data;
    }
  });

  // Get all days in current month
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Check if a date is booked
  const isDateBooked = (date: Date): boolean => {
    if (!calendarData?.bookings) return false;
    
    return calendarData.bookings.some(booking => {
      const bookingStart = new Date(booking.startDate);
      const bookingEnd = new Date(booking.endDate);
      return isWithinInterval(date, { start: bookingStart, end: bookingEnd });
    });
  };

  // Check if date is in selection range
  const isInSelectionRange = (date: Date): boolean => {
    if (!selectedStartDate || !hoveredDate) return false;
    const effectiveEndDate = selectedEndDate || hoveredDate;
    return isWithinInterval(date, { 
      start: selectedStartDate, 
      end: effectiveEndDate 
    });
  };

  const handleDateClick = (date: Date) => {
    if (isDateBooked(date)) return;

    if (!selectedStartDate || (selectedStartDate && selectedEndDate)) {
      // Start new selection
      setSelectedStartDate(date);
      setSelectedEndDate(null);
    } else if (date < selectedStartDate) {
      // Reset if selecting earlier date
      setSelectedStartDate(date);
      setSelectedEndDate(null);
    } else {
      // Complete selection
      setSelectedEndDate(date);
      if (onDateSelect) {
        onDateSelect(selectedStartDate, date);
      }
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2">Availability Calendar</h3>
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-500 rounded"></div>
            <span>Available</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-red-500 rounded"></div>
            <span>Booked</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded"></div>
            <span>Selected</span>
          </div>
        </div>
      </div>

      {/* Calendar Navigation */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => setCurrentMonth(prev => addMonths(prev, -1))}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <ChevronLeft className="w-5 h-5" />
        </button>
        
        <h4 className="text-lg font-medium">
          {format(currentMonth, 'MMMM yyyy')}
        </h4>
        
        <button
          onClick={() => setCurrentMonth(prev => addMonths(prev, 1))}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <ChevronRight className="w-5 h-5" />
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1 mb-4">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-sm font-medium py-2">
            {day}
          </div>
        ))}
        
        {/* Empty cells for start of month */}
        {Array.from({ length: monthStart.getDay() }).map((_, index) => (
          <div key={`empty-${index}`} />
        ))}
        
        {/* Calendar days */}
        {monthDays.map(day => {
          const isBooked = isDateBooked(day);
          const isSelected = selectedStartDate && 
            (isSameDay(day, selectedStartDate) || 
             (selectedEndDate && isSameDay(day, selectedEndDate)));
          const isInRange = isInSelectionRange(day);
          const isToday = isSameDay(day, new Date());
          
          return (
            <button
              key={day.toString()}
              onClick={() => handleDateClick(day)}
              onMouseEnter={() => setHoveredDate(day)}
              onMouseLeave={() => setHoveredDate(null)}
              disabled={isBooked || day < new Date()}
              className={cn(
                "aspect-square p-2 text-sm rounded-lg transition-colors",
                "hover:bg-gray-100 disabled:cursor-not-allowed",
                isBooked && "bg-red-100 text-red-900 hover:bg-red-100",
                isSelected && "bg-blue-600 text-white hover:bg-blue-700",
                isInRange && !isSelected && "bg-blue-100 text-blue-900",
                isToday && "ring-2 ring-blue-600",
                day < new Date() && !isBooked && "text-gray-400"
              )}
            >
              {format(day, 'd')}
            </button>
          );
        })}
      </div>

      {/* Selected Dates Info */}
      {selectedStartDate && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-blue-900">
                {selectedEndDate ? 'Selected Dates:' : 'Select check-out date'}
              </p>
              <p className="text-sm text-blue-700 mt-1">
                {format(selectedStartDate, 'MMM dd, yyyy')}
                {selectedEndDate && ` - ${format(selectedEndDate, 'MMM dd, yyyy')}`}
              </p>
              {selectedEndDate && (
                <p className="text-sm text-blue-700 mt-1">
                  {Math.ceil((selectedEndDate.getTime() - selectedStartDate.getTime()) / (1000 * 60 * 60 * 24))} nights
                  · ₹{pricePerNight * Math.ceil((selectedEndDate.getTime() - selectedStartDate.getTime()) / (1000 * 60 * 60 * 24))} total
                </p>
              )}
            </div>
            {selectedEndDate && (
              <button
                onClick={() => {
                  setSelectedStartDate(null);
                  setSelectedEndDate(null);
                }}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear
              </button>
            )}
          </div>
        </div>
      )}

      {/* Booking Info */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg flex items-start gap-2">
        <Info className="w-5 h-5 text-gray-500 flex-shrink-0 mt-0.5" />
        <div className="text-sm text-gray-600">
          <p>For half-day bookings or special requests, please contact the property owner directly.</p>
          <p className="mt-1">Bookings are updated in real-time across all platforms.</p>
        </div>
      </div>
    </div>
  );
}
```

### Property Details Page Integration

```typescript
// /client/src/pages/PropertyDetail.tsx
import React, { useState } from 'react';
import { useParams, useNavigate } from 'wouter';
import { PropertyDetailsCalendar } from '../components/PropertyDetailsCalendar';
import { BookingModal } from '../components/BookingModal';
import { useProperty } from '../hooks/useProperty';

export function PropertyDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data: property, isLoading } = useProperty(id);
  const [bookingDates, setBookingDates] = useState<{ start: Date; end: Date } | null>(null);
  const [showBookingModal, setShowBookingModal] = useState(false);

  const handleDateSelect = (startDate: Date, endDate: Date) => {
    setBookingDates({ start: startDate, end: endDate });
    setShowBookingModal(true);
  };

  if (isLoading) return <div>Loading...</div>;
  if (!property) return <div>Property not found</div>;

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Property images and info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* ... existing property details ... */}
      </div>

      {/* Calendar Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        <div className="lg:col-span-2">
          <PropertyDetailsCalendar
            propertyId={property.id}
            pricePerNight={property.price}
            onDateSelect={handleDateSelect}
          />
        </div>
        
        {/* Booking Summary Card */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-lg p-6 sticky top-4">
            <h3 className="text-lg font-semibold mb-4">Booking Summary</h3>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span>₹{property.price} x 1 night</span>
                <span>₹{property.price}</span>
              </div>
              <div className="flex justify-between">
                <span>Cleaning fee</span>
                <span>₹{property.cleaningFee || 0}</span>
              </div>
              <div className="border-t pt-3 flex justify-between font-semibold">
                <span>Total</span>
                <span>₹{property.price + (property.cleaningFee || 0)}</span>
              </div>
            </div>

            <button
              onClick={() => setShowBookingModal(true)}
              className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Reserve Now
            </button>

            <p className="text-sm text-gray-600 text-center mt-4">
              You won't be charged yet
            </p>
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      {showBookingModal && (
        <BookingModal
          property={property}
          dates={bookingDates}
          onClose={() => setShowBookingModal(false)}
          onSuccess={() => {
            setShowBookingModal(false);
            // Show success message
          }}
        />
      )}
    </div>
  );
}
```

### Mobile-Responsive Calendar Component

```typescript
// /client/src/components/MobileCalendar.tsx
import React from 'react';
import { format, isSameDay } from 'date-fns';
import { Calendar } from 'lucide-react';

interface MobileCalendarProps {
  bookings: Booking[];
  onDateSelect: (date: Date) => void;
}

export function MobileCalendar({ bookings, onDateSelect }: MobileCalendarProps) {
  return (
    <div className="lg:hidden">
      <div className="bg-white rounded-lg shadow-lg p-4">
        <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          Availability
        </h3>
        
        {/* Simplified mobile view */}
        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            Select dates to check availability
          </p>
          
          <button
            onClick={() => {
              // Open date picker modal
            }}
            className="w-full py-3 px-4 border border-gray-300 rounded-lg text-left"
          >
            <span className="text-gray-500">Check-in → Check-out</span>
          </button>
          
          {/* Quick availability summary */}
          <div className="mt-4 space-y-1">
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>Available for booking</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>{bookings.length} dates already booked</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Owner Dashboard Calendar Component

```typescript
// /client/src/components/PropertyCalendar.tsx
import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { useQuery, useMutation } from '@tanstack/react-query';
import { api } from '../lib/api';
import { useToast } from '../hooks/use-toast';

interface PropertyCalendarProps {
  propertyId: string;
  isOwner?: boolean;
}

export function PropertyCalendar({ propertyId, isOwner }: PropertyCalendarProps) {
  const { toast } = useToast();
  const [selectedDates, setSelectedDates] = useState<{ start: Date; end: Date } | null>(null);
  
  // Fetch calendar data
  const { data: calendarData, refetch } = useQuery({
    queryKey: ['calendar', propertyId],
    queryFn: () => api.get(`/calendar/${propertyId}`).then(res => res.data)
  });
  
  // Create booking mutation
  const createBooking = useMutation({
    mutationFn: (data: CreateBookingData) => 
      api.post('/bookings/calendar', data),
    onSuccess: () => {
      toast({ title: 'Booking created successfully' });
      refetch();
      setSelectedDates(null);
    },
    onError: (error) => {
      toast({ 
        title: 'Failed to create booking',
        description: error.response?.data?.message || 'Please try again',
        variant: 'destructive'
      });
    }
  });
  
  // Transform bookings to FullCalendar events
  const events = calendarData?.bookings.map(booking => ({
    id: booking.id,
    start: booking.startDate,
    end: booking.endDate,
    title: booking.guestName || 'Booked',
    backgroundColor: booking.status === 'confirmed' ? '#ef4444' : '#f59e0b',
    borderColor: booking.status === 'confirmed' ? '#dc2626' : '#d97706',
    extendedProps: {
      status: booking.status,
      notes: booking.notes,
      source: booking.source
    }
  })) || [];
  
  const handleDateSelect = (selectInfo: any) => {
    if (!isOwner) return;
    
    setSelectedDates({
      start: selectInfo.start,
      end: selectInfo.end
    });
  };
  
  const handleEventClick = (clickInfo: any) => {
    if (!isOwner) return;
    
    const booking = clickInfo.event.extendedProps;
    // Show booking details modal
  };
  
  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow p-6">
        <FullCalendar
          plugins={[dayGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          events={events}
          selectable={isOwner}
          select={handleDateSelect}
          eventClick={handleEventClick}
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,dayGridWeek'
          }}
          height="auto"
          eventDisplay="block"
          displayEventTime={false}
          dayMaxEvents={3}
        />
      </div>
      
      {/* Quick booking form for owners */}
      {isOwner && selectedDates && (
        <QuickBookingForm
          dates={selectedDates}
          propertyId={propertyId}
          onSubmit={createBooking.mutate}
          onCancel={() => setSelectedDates(null)}
        />
      )}
      
      {/* Calendar sync info */}
      <CalendarSyncInfo propertyId={propertyId} />
    </div>
  );
}

function CalendarSyncInfo({ propertyId }: { propertyId: string }) {
  const icalUrl = `${window.location.origin}/api/calendar/${propertyId}/ical`;
  
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 className="font-semibold text-blue-900 mb-2">Sync with External Calendars</h3>
      <p className="text-sm text-blue-700 mb-3">
        Add this calendar to Google Calendar, Outlook, or any calendar app:
      </p>
      <div className="flex items-center space-x-2">
        <input
          type="text"
          value={icalUrl}
          readOnly
          className="flex-1 px-3 py-2 border rounded-md text-sm"
        />
        <button
          onClick={() => navigator.clipboard.writeText(icalUrl)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Copy
        </button>
      </div>
    </div>
  );
}
```

## 🔌 API Endpoints

### Calendar REST API

```typescript
// /server/routes/calendar.ts
import { Router } from 'express';
import { CalendarService } from '../services/CalendarService';
import { authenticateToken, authorizeOwner } from '../middlewares/auth';

const router = Router();
const calendarService = new CalendarService();

// Get property calendar
router.get('/:propertyId', async (req, res) => {
  try {
    const { propertyId } = req.params;
    const { startDate, endDate } = req.query;
    
    const calendarData = await calendarService.getPropertyCalendar(
      propertyId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined
    );
    
    res.json(calendarData);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get iCal feed
router.get('/:propertyId/ical', async (req, res) => {
  try {
    const { propertyId } = req.params;
    const icalFeed = await calendarService.generateICalFeed(propertyId);
    
    res.setHeader('Content-Type', 'text/calendar');
    res.setHeader('Content-Disposition', `attachment; filename="${propertyId}.ics"`);
    res.send(icalFeed);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create booking (authenticated)
router.post('/:propertyId/bookings', authenticateToken, authorizeOwner, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const booking = await calendarService.createBooking({
      ...req.body,
      propertyId,
      createdBy: req.user.id
    });
    
    res.status(201).json(booking);
  } catch (error) {
    if (error.name === 'ConflictError') {
      res.status(409).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

// Update booking
router.patch('/bookings/:bookingId', authenticateToken, authorizeOwner, async (req, res) => {
  try {
    const { bookingId } = req.params;
    const booking = await calendarService.updateBooking(bookingId, req.body);
    
    res.json(booking);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete/Cancel booking
router.delete('/bookings/:bookingId', authenticateToken, authorizeOwner, async (req, res) => {
  try {
    const { bookingId } = req.params;
    await calendarService.cancelBooking(bookingId, req.user.id);
    
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
```

## 🚀 Implementation Roadmap

### Phase 1: Core Calendar (Week 1)
- [ ] Create database schema and migrations
- [ ] Implement CalendarService with basic CRUD
- [ ] Build REST API endpoints
- [ ] Add conflict detection logic
- [ ] Set up Redis caching

### Phase 2: Frontend Integration (Week 1-2)
- [ ] Integrate FullCalendar component
- [ ] Build booking forms and modals
- [ ] Add owner calendar management UI
- [ ] Implement real-time updates with WebSocket

### Phase 4: WhatsApp MVP (Week 2-3)
- [ ] Set up Twilio/WhatsApp Business API
- [ ] Create webhook handlers
- [ ] Implement basic message parsing
- [ ] Add quick reply templates
- [ ] Test booking creation flow

### Phase 5: WhatsApp Flows (Week 3-4)
- [ ] Design WhatsApp Flow screens
- [ ] Implement date picker flow
- [ ] Add guest information collection
- [ ] Test end-to-end booking via WhatsApp

### Phase 6: Advanced Features (Week 4+)
- [ ] Two-way calendar sync
- [ ] Bulk import from external calendars
- [ ] Advanced conflict resolution
- [ ] Multi-property calendar views
- [ ] Analytics and reporting

## 🔒 Security Considerations

1. **Authentication**: 
   - API endpoints require JWT authentication
   - WhatsApp numbers verified against property owners

2. **Authorization**:
   - Owners can only manage their own properties
   - Public calendar view is read-only

3. **Rate Limiting**:
   - API endpoints rate-limited per user
   - WhatsApp messages throttled to prevent spam

4. **Data Validation**:
   - All inputs sanitized and validated
   - Date ranges checked for validity
   - Booking conflicts prevented at database level

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('CalendarService', () => {
  it('should detect booking conflicts correctly', async () => {
    // Test overlap detection logic
  });
  
  it('should generate valid iCal feeds', async () => {
    // Test iCal generation
  });
});
```

### Integration Tests
```typescript
describe('Calendar API', () => {
  it('should create bookings via API', async () => {
    // Test full booking creation flow
  });
  
  it('should handle WhatsApp webhooks', async () => {
    // Test WhatsApp message processing
  });
});
```

### E2E Tests
```typescript
describe('Calendar Booking Flow', () => {
  it('should allow owners to create bookings', async () => {
    // Test UI booking flow
  });
  
  it('should sync with external calendars', async () => {
    // Test iCal subscription
  });
});
```

## 📊 Performance Optimizations

1. **Caching Strategy**:
   - Redis cache for calendar data (5-minute TTL)
   - iCal feeds cached for 1 hour
   - Invalidate on booking changes

2. **Database Optimization**:
   - Composite indexes for overlap queries
   - Partial indexes for active bookings
   - Query optimization for date ranges

3. **API Response Optimization**:
   - Pagination for large date ranges
   - Compressed responses
   - ETags for calendar feeds

## 🔧 Configuration

### Environment Variables
```env
# Calendar Service
REDIS_URL=redis://localhost:6379
CALENDAR_TIMEZONE=Asia/Kolkata

# WhatsApp Integration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
WHATSAPP_NUMBER=+***********
WHATSAPP_VERIFY_TOKEN=your_verify_token
WHATSAPP_BOOKING_FLOW_ID=your_flow_id

# External Calendar Sync
GOOGLE_CALENDAR_API_KEY=your_api_key
ENABLE_TWO_WAY_SYNC=false
```

## 📚 References

- [WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp)
- [Twilio WhatsApp API](https://www.twilio.com/docs/whatsapp)
- [FullCalendar Documentation](https://fullcalendar.io/docs)
- [iCal Specification (RFC 5545)](https://tools.ietf.org/html/rfc5545)

## 🎯 Complete Integration Summary

This booking calendar system creates a seamless experience across all touchpoints:

### For Guests (BookAFarm.com)
- **Property Details Page**: Interactive calendar showing real-time availability
- **Visual Selection**: Click and drag to select booking dates
- **Instant Feedback**: See pricing calculations as dates are selected
- **Mobile Optimized**: Responsive design with touch-friendly interface
- **Booking Flow**: Selected dates flow directly into booking form

### For Property Owners
- **WhatsApp Management**: Add bookings without leaving WhatsApp
- **Dashboard Control**: Full calendar management in owner portal
- **External Sync**: Connect with existing Google Calendar/Outlook
- **Real-time Updates**: Changes reflect instantly everywhere
- **Conflict Prevention**: System prevents double bookings automatically

### Technical Benefits
- **Single Source of Truth**: One database, multiple interfaces
- **Scalable Architecture**: Redis caching, optimized queries
- **Pluggable Design**: Easy to add new calendar sources
- **API-First**: RESTful endpoints for all operations
- **Real-time Sync**: WebSocket updates for instant changes

### Business Impact
- **Reduced Support**: Owners self-manage through familiar WhatsApp
- **Increased Bookings**: Clear availability increases conversion
- **Platform Trust**: Real-time accuracy builds confidence
- **Operational Efficiency**: Automated sync reduces manual work
- **Multi-channel Presence**: Same availability on all platforms

The calendar becomes the central hub connecting guests, owners, and external platforms - all updating in real-time from a single, reliable source.