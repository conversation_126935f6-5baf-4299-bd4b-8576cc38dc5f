# Dynamic Pricing Update Flow - Implementation Guide

Author: Fazeel Usmani  
Date: July 21, 2025

## 🎯 Overview

The Dynamic Pricing Update Flow ensures that price changes made by property owners are immediately reflected across the entire system - from the owner dashboard to public listings to booking calculations.

## 🔄 Real-Time Sync Architecture

### **1. Pricing Update Triggers**

#### **Owner Dashboard Updates**
```typescript
// PropertyForm.tsx - Enhanced mutation with pricing detection
const updateMutation = useMutation({
  mutationFn: async (data: FormValues) => {
    const response = await apiRequest("PUT", `/api/properties/${propertyId}`, data);
    return response.json();
  },
  onSuccess: (updatedProperty) => {
    const hasPricingUpdate = updatedProperty.pricingUpdated;
    
    // Enhanced cache invalidation for immediate sync
    if (hasPricingUpdate) {
      // Invalidate all pricing-related caches
      queryClient.invalidateQueries({ queryKey: ["/api/properties"] });
      queryClient.invalidateQueries({ queryKey: ["/api/properties", "search"] });
      queryClient.invalidateQueries({ queryKey: ["/api/properties", "featured"] });
    }
  }
});
```

### **2. Backend Pricing Detection**

#### **Property Update Route Enhancement**
```typescript
// properties.ts - Smart pricing change detection
router.put("/:id", async (req, res) => {
  // Detect if pricing fields were modified
  const hasPricingChanges = [
    'halfDayPrice', 'fullDayPrice', 
    'weekdayHalfDayPrice', 'weekdayFullDayPrice',
    'weekendHalfDayPrice', 'weekendFullDayPrice'
  ].some(field => req.body[field] !== undefined);

  const updatedProperty = await storage.updateProperty(propertyId, req.body);

  // Multi-layer cache invalidation
  await Promise.all([
    CacheInvalidator.invalidateProperty(propertyId),
    propertyCacheService.invalidatePropertyCache(propertyId, 'update'),
    hasPricingChanges ? propertyCacheService.invalidateSearchCache() : Promise.resolve(),
    hasPricingChanges ? propertyCacheService.invalidatePricingCache(propertyId) : Promise.resolve()
  ]);

  // Response includes pricing sync metadata
  const response = {
    ...updatedProperty,
    pricingUpdated: hasPricingChanges,
    lastPriceUpdate: hasPricingChanges ? new Date().toISOString() : null
  };

  return sendSuccess(res, response, "Property updated successfully");
});
```

### **3. Dynamic Pricing API Endpoint**

#### **Real-Time Pricing Calculation**
```typescript
// GET /api/properties/:id/pricing/:date
router.get("/:id/pricing/:date", async (req, res) => {
  const propertyId = parseInt(req.params.id);
  const date = req.params.date;

  // Always fetch latest data from database (no stale cache)
  const property = await storage.getProperty(propertyId);
  
  // Calculate effective pricing with fallback logic
  const bookingDate = new Date(date);
  const pricing = {
    date: date,
    dayOfWeek: bookingDate.getDay(),
    isWeekend: [0, 5, 6].includes(bookingDate.getDay()),
    pricing: {
      halfDay: propertyService.getEffectivePrice(property, bookingDate, '12h'),
      fullDay: propertyService.getEffectivePrice(property, bookingDate, '24h')
    },
    // Include all pricing tiers for transparency
    basePricing: { halfDay: property.halfDayPrice, fullDay: property.fullDayPrice },
    weekdayPricing: { halfDay: property.weekdayHalfDayPrice, fullDay: property.weekdayFullDayPrice },
    weekendPricing: { halfDay: property.weekendHalfDayPrice, fullDay: property.weekendFullDayPrice }
  };

  return sendSuccess(res, pricing);
});
```

### **4. Enhanced Booking Calculations**

#### **Dynamic Price Integration**
```typescript
// BookingService.ts - Updated to use dynamic pricing
async createBooking(bookingData: any, userId: number): Promise<Booking> {
  const property = await propertyService.getPropertyById(bookingData.propertyId);
  const bookingDate = new Date(bookingData.bookingDate);
  
  // Use dynamic pricing logic instead of static prices
  const duration = bookingData.bookingType === 'morning' ? '12h' : '24h';
  const effectivePrice = propertyService.getEffectivePrice(property, bookingDate, duration);
  
  const totalPrice = effectivePrice * bookingData.guests;
  // ... rest of booking creation
}
```

#### **Client-Side Real-Time Pricing**
```typescript
// BookingForm.tsx - Live price updates
const { data: dynamicPricing } = useQuery({
  queryKey: ['property-pricing', propertyId, selectedDate?.toISOString()?.split('T')[0]],
  queryFn: async () => {
    const dateStr = selectedDate.toISOString().split('T')[0];
    const response = await fetch(`/api/properties/${propertyId}/pricing/${dateStr}`);
    return response.json();
  },
  enabled: !!selectedDate,
  staleTime: 60000, // 1-minute cache for real-time feel
});

// Always use the latest pricing
const currentPricing = dynamicPricing?.pricing || fallbackPricing;
```

## 🚀 Sync Flow Sequence

### **Step 1: Owner Updates Pricing**
1. Owner modifies pricing fields in dashboard
2. Form submission triggers PropertyForm mutation
3. Backend detects pricing field changes
4. Database updated with new pricing

### **Step 2: Immediate Cache Invalidation**
```typescript
// Multi-layer cache invalidation strategy
await Promise.all([
  // Property-specific caches
  CacheInvalidator.invalidateProperty(propertyId),
  propertyCacheService.invalidatePropertyCache(propertyId, 'update'),
  
  // Search and listing caches (pricing affects filtering)
  propertyCacheService.invalidateSearchCache(),
  
  // Owner dashboard caches
  propertyCacheService.invalidateOwnerCache(req.user.userId),
  
  // Pricing-specific caches
  propertyCacheService.invalidatePricingCache(propertyId)
]);
```

### **Step 3: Public Listing Sync**
1. Property detail pages automatically refresh (cache invalidated)
2. Search results update to reflect new pricing
3. Featured property listings sync immediately
4. Price filtering in search works with latest prices

### **Step 4: Booking System Sync**
1. New bookings use `getEffectivePrice()` method
2. Real-time pricing API provides latest rates
3. BookingForm displays updated prices immediately
4. All booking calculations use current pricing

### **Step 5: User Feedback**
```typescript
// Enhanced success message
toast({
  title: "Success",
  description: hasPricingUpdate 
    ? "Property and pricing updated successfully - changes are live!" 
    : "Property updated successfully",
});
```

## 📊 Cache Strategy

### **Cache Layers & TTL**
- **Property Details**: 30 minutes (invalidated on updates)
- **Search Results**: 10 minutes (invalidated on pricing changes)
- **Pricing API**: 1 minute (short TTL for real-time feel)
- **Owner Properties**: 15 minutes (invalidated on owner updates)

### **Invalidation Triggers**
- **Property Update**: All property-related caches
- **Pricing Update**: Property + Search + Pricing caches  
- **Status Change**: Property + Search caches
- **Media Update**: Property cache only

## 🔧 Implementation Highlights

### **Real-Time Pricing Service**
```typescript
// PropertyService.ts - Smart pricing calculation
getEffectivePrice(property: Property, date: Date, duration: '12h' | '24h'): number {
  const dayOfWeek = date.getDay();
  const isWeekend = [0, 5, 6].includes(dayOfWeek); // Fri, Sat, Sun

  if (duration === '12h') {
    if (isWeekend) {
      return property.weekendHalfDayPrice || property.weekdayHalfDayPrice || property.halfDayPrice;
    } else {
      return property.weekdayHalfDayPrice || property.halfDayPrice;
    }
  } else {
    if (isWeekend) {
      return property.weekendFullDayPrice || property.weekdayFullDayPrice || property.fullDayPrice;
    } else {
      return property.weekdayFullDayPrice || property.fullDayPrice;
    }
  }
}
```

### **Frontend Query Invalidation**
```typescript
// Comprehensive cache invalidation for pricing updates
const queriesToInvalidate = [
  { queryKey: ["/api/properties/owner/me"] },     // Owner dashboard
  { queryKey: [`/api/properties/${propertyId}`] }, // Property details  
  { queryKey: ["/api/properties"] },              // Public listings
  { queryKey: ["/api/properties", "featured"] },  // Featured listings
  { queryKey: ["/api/properties", "search"] }     // Search results
];
```

## ✅ Verification & Testing

### **End-to-End Test Flow**
1. **Update Pricing**: Change weekend rates in dashboard
2. **Verify Dashboard**: Pricing summary updates immediately  
3. **Check Public Listing**: Property page shows new prices
4. **Test Booking**: Booking form uses updated pricing
5. **Search Verification**: Price filters work with new rates

### **Performance Monitoring**
- Cache hit rates for pricing queries
- Response times for pricing API endpoint
- Cache invalidation success rates
- Real-time sync latency measurements

## 🎯 Benefits Delivered

### **For Property Owners**
- **Immediate Visibility**: Price changes reflect instantly
- **Confidence**: Clear feedback on successful updates  
- **Control**: Full pricing flexibility with real-time sync

### **For Guests/Users**
- **Accurate Pricing**: Always see current rates
- **Consistent Experience**: No price discrepancies
- **Real-Time Updates**: Latest prices in booking flow

### **For System Performance**
- **Smart Caching**: Efficient invalidation strategy
- **Minimal Latency**: Strategic cache TTLs
- **Scalable**: Handles multiple concurrent pricing updates

## 🚀 Implementation Status: **COMPLETE**

The dynamic pricing update flow provides seamless, real-time synchronization of pricing changes across the entire system with:

- ✅ Intelligent pricing change detection
- ✅ Multi-layer cache invalidation strategy  
- ✅ Real-time pricing API endpoint
- ✅ Enhanced booking calculation logic
- ✅ Optimistic UI updates with feedback
- ✅ Comprehensive end-to-end sync workflow

Property owners can now update pricing with confidence knowing that changes are immediately reflected in public listings and used in all new booking calculations.