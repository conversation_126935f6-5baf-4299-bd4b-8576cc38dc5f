# Farmhouse Rental Application - Architecture Diagram

Author: Fazeel Usmani  
Date: July 13, 2025

## System Architecture Overview

```mermaid
graph TB
    %% Frontend Layer
    subgraph "Frontend Layer"
        UI[React Frontend<br/>Vite + TypeScript]
        UI --> |API Calls| API
        UI --> |Static Files| Static[Static File Server]
    end

    %% API Layer
    subgraph "API Layer"
        API[Express.js REST API<br/>TypeScript]
        API --> MW[Middleware Stack]
        
        subgraph MW [Middleware Stack]
            CORS[CORS Handler]
            Auth[Authentication]
            Valid[Validation]
            Log[Logging]
            Cache[Caching]
            Sec[Security]
            CSRF[CSRF Protection]
        end
    end

    %% Service Layer
    subgraph "Service Layer"
        UserSvc[UserService]
        PropSvc[PropertyService]
        BookSvc[BookingService]
        ReviewSvc[ReviewService]
        TempSvc[TemplateService]
        SMSSvc[DLTSMSService]
        EmailSvc[EmailService]
        OTPSvc[OTPService]
        CacheSvc[CacheService]
        
        API --> UserSvc
        API --> PropSvc
        API --> BookSvc
        API --> ReviewSvc
        API --> TempSvc
    end

    %% Database Layer
    subgraph "Database Layer"
        DB[(PostgreSQL Database)]
        
        subgraph "Database Tables"
            Users[users]
            Props[properties]
            Books[bookings]
            Reviews[reviews]
            OTP[otp_tokens]
            SMS_T[sms_templates]
            SMS_L[sms_logs]
            Owner[owner_interest_requests]
        end
        
        UserSvc --> Users
        PropSvc --> Props
        BookSvc --> Books
        ReviewSvc --> Reviews
        OTPSvc --> OTP
        TempSvc --> SMS_T
        SMSSvc --> SMS_L
    end

    %% External Services
    subgraph "External Services"
        Twilio[Twilio SMS API<br/>DLT Compliant]
        Email[Email Service<br/>SendGrid]
        Storage[File Storage<br/>Local/Cloud]
        Maps[Maps API<br/>Location Services]
    end

    %% Service Connections
    SMSSvc --> |SMS Delivery| Twilio
    EmailSvc --> |Email Delivery| Email
    PropSvc --> |Image Upload| Storage
    UI --> |Location Data| Maps

    %% Authentication Flow
    subgraph "Authentication Flow"
        Login[Login Process]
        OTPFlow[OTP Verification]
        JWT[JWT Token]
        
        Login --> OTPFlow
        OTPFlow --> |SMS/Email| OTPSvc
        OTPSvc --> |Generate| Twilio
        OTPSvc --> |Generate| Email
        OTPFlow --> JWT
    end

    %% SMS Template System
    subgraph "SMS Template System"
        Templates[Template Management]
        Variables[Variable Replacement]
        DLT[DLT Compliance]
        Logs[Audit Logging]
        
        Templates --> Variables
        Variables --> DLT
        DLT --> Logs
        
        BookSvc --> |Booking Confirmation| SMSSvc
        SMSSvc --> Templates
    end

    %% Data Flow
    UI -.->|User Actions| API
    API -.->|Business Logic| UserSvc
    API -.->|Business Logic| PropSvc
    API -.->|Business Logic| BookSvc
    BookSvc -.->|SMS Trigger| SMSSvc
    SMSSvc -.->|Template Lookup| TempSvc
    SMSSvc -.->|Send SMS| Twilio

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef service fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef external fill:#ffebee
    classDef auth fill:#f1f8e9
    classDef sms fill:#e3f2fd

    class UI,Static frontend
    class API,MW,CORS,Auth,Valid,Log,Cache,Sec,CSRF api
    class UserSvc,PropSvc,BookSvc,ReviewSvc,TempSvc,SMSSvc,EmailSvc,OTPSvc,CacheSvc service
    class DB,Users,Props,Books,Reviews,OTP,SMS_T,SMS_L,Owner database
    class Twilio,Email,Storage,Maps external
    class Login,OTPFlow,JWT auth
    class Templates,Variables,DLT,Logs sms
```

## Component Details

### Frontend Layer
- **React + TypeScript**: Modern UI framework with type safety
- **Vite**: Fast build tool and development server
- **Component Architecture**: Reusable UI components
- **State Management**: React Context + Custom hooks

### API Layer
- **Express.js**: RESTful API server
- **Middleware Stack**: 
  - CORS handling for cross-origin requests
  - JWT authentication
  - Request validation (Zod schemas)
  - Request/response logging
  - Caching middleware
  - Security headers
  - CSRF protection

### Service Layer
- **UserService**: User management, authentication
- **PropertyService**: Property CRUD operations
- **BookingService**: Booking management + SMS notifications
- **ReviewService**: Review system
- **TemplateService**: SMS template management
- **DLTSMSService**: DLT-compliant SMS delivery
- **EmailService**: Email notifications
- **OTPService**: OTP generation and verification

### Database Layer
- **PostgreSQL**: Primary database
- **Drizzle ORM**: Type-safe database operations
- **Migration System**: Version-controlled schema changes
- **Indexing**: Optimized for query performance

### External Integrations
- **Twilio**: SMS delivery with DLT compliance
- **Email Service**: Transactional emails
- **File Storage**: Property images
- **Maps API**: Location services

## Data Flow Examples

### Booking Creation Flow
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant BookingService
    participant TemplateService
    participant DLTSMSService
    participant Twilio
    participant Database

    User->>Frontend: Create Booking
    Frontend->>API: POST /api/bookings
    API->>BookingService: createBooking()
    BookingService->>Database: Save booking
    BookingService->>TemplateService: getTemplate('booking_confirmation')
    TemplateService->>Database: Fetch template
    TemplateService-->>BookingService: Return template
    BookingService->>DLTSMSService: sendBookingConfirmation()
    DLTSMSService->>TemplateService: replaceVariables()
    DLTSMSService->>Database: Log SMS attempt
    DLTSMSService->>Twilio: Send SMS
    Twilio-->>DLTSMSService: Delivery status
    DLTSMSService->>Database: Update SMS log
    DLTSMSService-->>BookingService: SMS result
    BookingService-->>API: Booking created
    API-->>Frontend: Success response
    Frontend-->>User: Booking confirmed
```

### SMS Template System Flow
```mermaid
flowchart TD
    A[Booking Created] --> B[Trigger SMS]
    B --> C[Load Template from DB]
    C --> D[Replace Variables]
    D --> E[Validate DLT Compliance]
    E --> F[Send via Twilio]
    F --> G[Log Result]
    G --> H[Update Status]
    
    C --> C1[Template: booking_confirmation]
    C1 --> C2[DLT ID: 1207175138826492810]
    C2 --> C3[Variables: property_name, booking_date]
    
    D --> D1[BookAFarm booking for {property} on {date} confirmed]
    D1 --> D2[BookAFarm booking for Green Valley on 2024-12-25 confirmed]
    
    F --> F1[SMS Delivered]
    F --> F2[SMS Failed]
    
    F1 --> G
    F2 --> G
```

## Technology Stack

### Backend
- **Runtime**: Node.js + TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL + Drizzle ORM
- **Authentication**: JWT + OTP
- **SMS**: Twilio (DLT compliant)
- **Email**: SendGrid/SMTP
- **Testing**: Vitest + Playwright

### Frontend
- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + shadcn/ui
- **State**: React Context + React Query
- **Routing**: React Router
- **Forms**: React Hook Form + Zod

### DevOps & Infrastructure
- **Containerization**: Docker + Docker Compose
- **Database Migrations**: Drizzle Kit
- **Testing**: Unit + Integration + E2E
- **CI/CD**: GitHub Actions (if configured)
- **Monitoring**: Custom logging + health checks

## Security Features

- **Authentication**: JWT tokens + OTP verification
- **Authorization**: Role-based access control
- **CSRF Protection**: Token-based CSRF prevention
- **Data Validation**: Zod schema validation
- **SQL Injection Prevention**: Parameterized queries via ORM
- **Rate Limiting**: Built-in request throttling
- **CORS**: Configured origin whitelist
- **Security Headers**: Helmet.js integration
- **Password Hashing**: Secure OTP hashing
- **DLT Compliance**: SMS regulatory compliance

## Performance Optimizations

- **Database Indexing**: Strategic indexes on frequently queried columns
- **Caching**: Template caching + query result caching
- **Connection Pooling**: PostgreSQL connection pool
- **Asset Optimization**: Vite build optimization
- **Lazy Loading**: Component-level code splitting
- **Image Optimization**: Compressed property images
- **API Pagination**: Large dataset pagination
- **Materialized Views**: Pre-computed analytics