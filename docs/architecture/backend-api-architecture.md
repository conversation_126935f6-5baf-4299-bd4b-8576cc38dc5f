# MERN Stack Backend API Architecture

Author: Fazeel Usmani  
Date: July 21, 2025

## Overview
This document outlines the scalable backend API structure that supports the modular Owner Dashboard frontend. The architecture is designed for extensibility, performance, and maintainability.

## Core Architecture Principles

### 1. Modular Route Structure
```javascript
// routes/index.js - Main router with plugin system
const express = require('express');
const { FeatureRouter } = require('../middleware/featureRouter');

const app = express();

// Core API routes
app.use('/api/auth', require('./auth'));
app.use('/api/users', require('./users'));
app.use('/api/properties', require('./properties'));
app.use('/api/bookings', require('./bookings'));

// Modular feature routes with feature flags
const featureRouter = new FeatureRouter();
featureRouter.registerModule('analytics', require('./modules/analytics'));
featureRouter.registerModule('messaging', require('./modules/messaging'));
featureRouter.registerModule('media', require('./modules/media'));

app.use('/api/features', featureRouter.router);
```

### 2. Resource-Based Controllers
```javascript
// controllers/BaseResourceController.js
class BaseResourceController {
  constructor(model, options = {}) {
    this.model = model;
    this.options = {
      populate: options.populate || [],
      searchFields: options.searchFields || ['title', 'description'],
      sortField: options.sortField || 'createdAt',
      ...options
    };
  }

  // GET /api/resource
  async list(req, res, next) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        sort = this.options.sortField,
        ...filters 
      } = req.query;

      const query = this.buildQuery(filters, search);
      const sortObj = this.buildSort(sort);

      const [data, total] = await Promise.all([
        this.model
          .find(query)
          .populate(this.options.populate)
          .sort(sortObj)
          .limit(limit * 1)
          .skip((page - 1) * limit),
        this.model.countDocuments(query)
      ]);

      res.json({
        data,
        pagination: {
          page: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/resource/:id
  async get(req, res, next) {
    try {
      const item = await this.model
        .findById(req.params.id)
        .populate(this.options.populate);
      
      if (!item) {
        return res.status(404).json({ error: 'Resource not found' });
      }
      
      res.json(item);
    } catch (error) {
      next(error);
    }
  }

  // POST /api/resource
  async create(req, res, next) {
    try {
      const item = new this.model(req.body);
      await item.save();
      
      const populated = await this.model
        .findById(item._id)
        .populate(this.options.populate);
      
      res.status(201).json(populated);
    } catch (error) {
      if (error.name === 'ValidationError') {
        return res.status(400).json({ 
          error: 'Validation failed', 
          details: error.errors 
        });
      }
      next(error);
    }
  }

  // PATCH /api/resource/:id
  async update(req, res, next) {
    try {
      const item = await this.model
        .findByIdAndUpdate(
          req.params.id, 
          req.body, 
          { new: true, runValidators: true }
        )
        .populate(this.options.populate);
      
      if (!item) {
        return res.status(404).json({ error: 'Resource not found' });
      }
      
      res.json(item);
    } catch (error) {
      if (error.name === 'ValidationError') {
        return res.status(400).json({ 
          error: 'Validation failed', 
          details: error.errors 
        });
      }
      next(error);
    }
  }

  // DELETE /api/resource/:id
  async delete(req, res, next) {
    try {
      const item = await this.model.findByIdAndDelete(req.params.id);
      
      if (!item) {
        return res.status(404).json({ error: 'Resource not found' });
      }
      
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }

  // POST /api/resource/batch
  async batchOperation(req, res, next) {
    try {
      const { operations } = req.body;
      const results = [];

      for (const operation of operations) {
        try {
          let result;
          switch (operation.type) {
            case 'create':
              result = await this.model.create(operation.data);
              break;
            case 'update':
              result = await this.model.findByIdAndUpdate(
                operation.id, 
                operation.data, 
                { new: true, runValidators: true }
              );
              break;
            case 'delete':
              result = await this.model.findByIdAndDelete(operation.id);
              break;
          }
          results.push({ success: true, data: result, operation });
        } catch (error) {
          results.push({ success: false, error: error.message, operation });
        }
      }

      res.json({ results });
    } catch (error) {
      next(error);
    }
  }

  buildQuery(filters, search) {
    const query = { ...filters };
    
    if (search && this.options.searchFields.length > 0) {
      query.$or = this.options.searchFields.map(field => ({
        [field]: { $regex: search, $options: 'i' }
      }));
    }
    
    return query;
  }

  buildSort(sortParam) {
    const [field, direction = 'desc'] = sortParam.split(':');
    return { [field]: direction === 'asc' ? 1 : -1 };
  }
}

module.exports = BaseResourceController;
```

### 3. Feature-Specific Route Modules
```javascript
// routes/modules/analytics.js
const express = require('express');
const { requireAuth, requireRole } = require('../../middleware/auth');
const AnalyticsController = require('../../controllers/AnalyticsController');

const router = express.Router();
const analytics = new AnalyticsController();

// Dashboard summary - optimized for fast loading
router.get('/dashboard/summary', 
  requireAuth, 
  requireRole('owner'), 
  analytics.getDashboardSummary
);

// Property performance analytics
router.get('/properties/:id/performance', 
  requireAuth, 
  requireRole('owner'), 
  analytics.getPropertyPerformance
);

// Revenue forecasting
router.get('/revenue/forecast', 
  requireAuth, 
  requireRole('owner'), 
  analytics.getRevenueForecast
);

// Export data in various formats
router.get('/export/:type', 
  requireAuth, 
  requireRole('owner'), 
  analytics.exportData
);

module.exports = router;
```

## Database Schema Design for Extensibility

### 1. Core Models with Plugin Support
```javascript
// models/Property.js
const mongoose = require('mongoose');

const propertySchema = new mongoose.Schema({
  title: { type: String, required: true, index: true },
  description: { type: String, required: true },
  location: { type: String, required: true, index: true },
  pricePerDay: { type: Number, required: true, index: true },
  maxGuests: { type: Number, required: true },
  amenities: [String],
  images: [{
    url: String,
    alt: String,
    order: { type: Number, default: 0 }
  }],
  owner: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  
  // Extensible metadata for future features
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  
  // Feature flags for property-specific features
  features: {
    instantBooking: { type: Boolean, default: false },
    selfCheckin: { type: Boolean, default: false },
    advancedPricing: { type: Boolean, default: false }
  },
  
  // Analytics tracking
  stats: {
    views: { type: Number, default: 0, index: true },
    bookings: { type: Number, default: 0, index: true },
    rating: { type: Number, default: 0, index: true },
    revenue: { type: Number, default: 0, index: true }
  },
  
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'draft'], 
    default: 'draft',
    index: true 
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
propertySchema.index({ owner: 1, status: 1, createdAt: -1 });
propertySchema.index({ location: 1, pricePerDay: 1, maxGuests: 1 });
propertySchema.index({ 'stats.rating': -1, 'stats.bookings': -1 });

// Virtual for average daily revenue
propertySchema.virtual('averageDailyRevenue').get(function() {
  return this.stats.bookings > 0 ? this.stats.revenue / this.stats.bookings : 0;
});

module.exports = mongoose.model('Property', propertySchema);
```

### 2. Extensible User Model
```javascript
// models/User.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true, lowercase: true },
  fullName: { type: String, required: true },
  phone: String,
  role: { 
    type: String, 
    enum: ['guest', 'owner', 'admin'], 
    default: 'guest',
    index: true 
  },
  
  // Owner-specific data
  ownerProfile: {
    businessName: String,
    businessLicense: String,
    bankDetails: {
      accountNumber: String,
      routingNumber: String,
      accountType: String
    },
    preferences: {
      notifications: {
        email: { type: Boolean, default: true },
        sms: { type: Boolean, default: false },
        push: { type: Boolean, default: true }
      },
      autoAcceptBookings: { type: Boolean, default: false },
      instantBookingEnabled: { type: Boolean, default: false }
    },
    stats: {
      totalProperties: { type: Number, default: 0 },
      totalBookings: { type: Number, default: 0 },
      totalRevenue: { type: Number, default: 0 },
      averageRating: { type: Number, default: 0 }
    }
  },
  
  // Guest-specific data
  guestProfile: {
    dateOfBirth: Date,
    emergencyContact: {
      name: String,
      phone: String
    },
    preferences: {
      amenities: [String],
      maxBudget: Number,
      preferredLocations: [String]
    }
  },
  
  // Extensible user metadata
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  
  // Feature access control
  features: {
    type: Map,
    of: Boolean,
    default: new Map()
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      return ret;
    }
  }
});

userSchema.index({ email: 1 });
userSchema.index({ role: 1, createdAt: -1 });

module.exports = mongoose.model('User', userSchema);
```

### 3. Notification System Schema
```javascript
// models/Notification.js
const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  recipient: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  type: { 
    type: String, 
    required: true,
    enum: ['booking', 'payment', 'message', 'system', 'marketing'],
    index: true 
  },
  title: { type: String, required: true },
  message: { type: String, required: true },
  
  // Related resource information
  relatedResource: {
    type: { type: String, enum: ['booking', 'property', 'user', 'payment'] },
    id: mongoose.Schema.Types.ObjectId,
    data: mongoose.Schema.Types.Mixed
  },
  
  // Delivery channels
  channels: {
    inApp: { 
      delivered: { type: Boolean, default: false },
      read: { type: Boolean, default: false },
      readAt: Date 
    },
    email: { 
      delivered: { type: Boolean, default: false },
      opened: { type: Boolean, default: false },
      openedAt: Date 
    },
    sms: { 
      delivered: { type: Boolean, default: false },
      status: String 
    },
    push: { 
      delivered: { type: Boolean, default: false },
      clicked: { type: Boolean, default: false } 
    }
  },
  
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'urgent'], 
    default: 'medium',
    index: true 
  },
  
  // Scheduling
  scheduledFor: Date,
  deliveredAt: Date,
  expiresAt: Date,
  
  status: { 
    type: String, 
    enum: ['pending', 'delivered', 'failed', 'expired'], 
    default: 'pending',
    index: true 
  }
}, {
  timestamps: true
});

notificationSchema.index({ recipient: 1, status: 1, createdAt: -1 });
notificationSchema.index({ type: 1, priority: 1, scheduledFor: 1 });

module.exports = mongoose.model('Notification', notificationSchema);
```

## WebSocket Server Implementation

### 1. Real-Time Event System
```javascript
// services/websocket.js
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { EventEmitter } = require('events');

class WebSocketServer extends EventEmitter {
  constructor(server) {
    super();
    this.wss = new WebSocket.Server({ server });
    this.clients = new Map();
    this.rooms = new Map();
    
    this.wss.on('connection', this.handleConnection.bind(this));
    this.setupEventHandlers();
  }

  async handleConnection(ws, req) {
    try {
      const token = this.extractToken(req);
      const user = await this.authenticateToken(token);
      
      const clientId = this.generateClientId();
      const client = {
        id: clientId,
        ws,
        user,
        rooms: new Set(),
        lastActivity: Date.now()
      };
      
      this.clients.set(clientId, client);
      
      // Join user to their personal room
      this.joinRoom(clientId, `user:${user.id}`);
      
      // Join owner to their properties room
      if (user.role === 'owner') {
        this.joinRoom(clientId, `owner:${user.id}`);
      }
      
      ws.on('message', (data) => this.handleMessage(clientId, data));
      ws.on('close', () => this.handleDisconnection(clientId));
      ws.on('error', (error) => this.handleError(clientId, error));
      
      // Send connection confirmation
      this.sendToClient(clientId, {
        type: 'connection',
        status: 'connected',
        clientId,
        user: { id: user.id, role: user.role }
      });
      
    } catch (error) {
      ws.close(1008, 'Authentication failed');
    }
  }

  handleMessage(clientId, data) {
    try {
      const message = JSON.parse(data);
      const client = this.clients.get(clientId);
      
      if (!client) return;
      
      client.lastActivity = Date.now();
      
      switch (message.type) {
        case 'subscribe':
          this.handleSubscription(clientId, message.rooms);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(clientId, message.rooms);
          break;
        case 'ping':
          this.sendToClient(clientId, { type: 'pong', timestamp: Date.now() });
          break;
        default:
          this.emit('message', { clientId, message, client });
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  }

  // Broadcast to specific rooms
  broadcastToRoom(room, message) {
    const clients = this.rooms.get(room);
    if (!clients) return;
    
    const payload = JSON.stringify(message);
    clients.forEach(clientId => {
      const client = this.clients.get(clientId);
      if (client && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(payload);
      }
    });
  }

  // Send to specific client
  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  // Send to all clients of a user
  sendToUser(userId, message) {
    this.broadcastToRoom(`user:${userId}`, message);
  }

  // Send to all owner's clients
  sendToOwner(ownerId, message) {
    this.broadcastToRoom(`owner:${ownerId}`, message);
  }
}

module.exports = WebSocketServer;
```

### 2. Event Broadcasting Service
```javascript
// services/eventBroadcaster.js
const { EventEmitter } = require('events');

class EventBroadcaster extends EventEmitter {
  constructor(wsServer) {
    super();
    this.wsServer = wsServer;
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // Booking events
    this.on('booking:created', this.handleBookingCreated.bind(this));
    this.on('booking:updated', this.handleBookingUpdated.bind(this));
    this.on('booking:cancelled', this.handleBookingCancelled.bind(this));
    
    // Property events
    this.on('property:viewed', this.handlePropertyViewed.bind(this));
    this.on('property:updated', this.handlePropertyUpdated.bind(this));
    
    // Payment events
    this.on('payment:completed', this.handlePaymentCompleted.bind(this));
    this.on('payment:failed', this.handlePaymentFailed.bind(this));
  }

  handleBookingCreated(booking) {
    // Notify property owner
    this.wsServer.sendToUser(booking.property.owner, {
      type: 'notification',
      category: 'booking',
      action: 'created',
      data: {
        booking: booking,
        message: `New booking request for ${booking.property.title}`,
        requiresAction: booking.status === 'pending'
      }
    });

    // Update dashboard stats
    this.wsServer.sendToOwner(booking.property.owner, {
      type: 'dashboard:update',
      section: 'bookings',
      action: 'increment',
      data: { status: booking.status }
    });
  }

  handleBookingUpdated(booking, previousStatus) {
    // Notify guest about status change
    this.wsServer.sendToUser(booking.guest._id, {
      type: 'notification',
      category: 'booking',
      action: 'status_changed',
      data: {
        booking: booking,
        previousStatus,
        message: `Your booking for ${booking.property.title} is now ${booking.status}`
      }
    });

    // Update owner dashboard
    this.wsServer.sendToOwner(booking.property.owner, {
      type: 'dashboard:update',
      section: 'bookings',
      action: 'status_change',
      data: { 
        bookingId: booking._id,
        newStatus: booking.status,
        previousStatus 
      }
    });
  }

  // Trigger events from controllers
  static getInstance() {
    if (!this.instance) {
      this.instance = new EventBroadcaster();
    }
    return this.instance;
  }
}

module.exports = EventBroadcaster;
```

## API Endpoint Structure

### 1. Dashboard Summary Endpoint (Optimized)
```javascript
// controllers/DashboardController.js
const mongoose = require('mongoose');

class DashboardController {
  async getSummary(req, res, next) {
    try {
      const ownerId = req.user.id;
      
      // Use aggregation pipeline for optimal performance
      const [summary] = await mongoose.connection.db.collection('properties')
        .aggregate([
          { $match: { owner: mongoose.Types.ObjectId(ownerId) } },
          {
            $lookup: {
              from: 'bookings',
              localField: '_id',
              foreignField: 'property',
              as: 'bookings'
            }
          },
          {
            $project: {
              totalProperties: { $literal: 1 },
              totalBookings: { $size: '$bookings' },
              confirmedBookings: {
                $size: {
                  $filter: {
                    input: '$bookings',
                    cond: { $eq: ['$$this.status', 'confirmed'] }
                  }
                }
              },
              pendingBookings: {
                $size: {
                  $filter: {
                    input: '$bookings',
                    cond: { $eq: ['$$this.status', 'pending'] }
                  }
                }
              },
              totalRevenue: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$bookings',
                        cond: { $in: ['$$this.status', ['confirmed', 'completed']] }
                      }
                    },
                    as: 'booking',
                    in: '$$booking.totalPrice'
                  }
                }
              }
            }
          },
          {
            $group: {
              _id: null,
              totalProperties: { $sum: '$totalProperties' },
              totalBookings: { $sum: '$totalBookings' },
              confirmedBookings: { $sum: '$confirmedBookings' },
              pendingBookings: { $sum: '$pendingBookings' },
              totalRevenue: { $sum: '$totalRevenue' }
            }
          }
        ]).toArray();

      // Add recent activity
      const recentBookings = await mongoose.connection.db.collection('bookings')
        .find(
          { 'property.owner': mongoose.Types.ObjectId(ownerId) },
          { 
            sort: { createdAt: -1 }, 
            limit: 5,
            projection: { 
              _id: 1, 
              status: 1, 
              totalPrice: 1, 
              bookingDate: 1,
              'guest.fullName': 1,
              'property.title': 1,
              createdAt: 1 
            }
          }
        ).toArray();

      res.json({
        summary: summary || {
          totalProperties: 0,
          totalBookings: 0,
          confirmedBookings: 0,
          pendingBookings: 0,
          totalRevenue: 0
        },
        recentActivity: recentBookings,
        generatedAt: new Date()
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = DashboardController;
```

This backend API architecture provides:

1. **Modular Structure**: Plugin-based route system for easy feature addition
2. **Scalable Controllers**: Base resource controller with CRUD operations
3. **Extensible Database Schema**: Flexible models with metadata support
4. **Real-Time Communication**: WebSocket server with room-based broadcasting
5. **Performance Optimization**: Aggregation pipelines for fast dashboard loading
6. **Feature Flag Support**: Backend support for progressive feature rollout

The architecture supports the modular frontend components and provides a solid foundation for future enhancements.