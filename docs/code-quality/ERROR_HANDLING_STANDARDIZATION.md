# Error Handling Standardization – Backend Stability Initiative

Author: Fazeel Usmani  
Date: July 28, 2025

## Overview
This document describes the comprehensive error handling standardization implemented across the backend codebase. The new system ensures robust, consistent error handling that prevents unhandled exceptions, simplifies debugging, and maintains clean control flow throughout asynchronous operations.

## ✅ Implemented Solutions

### 1. Enhanced Error Class Hierarchy (`server/errors/index.ts`)

A complete set of typed error classes with:
- **BaseError**: Abstract base class with severity, category, and context tracking
- **Specific Error Types**:
  - `ValidationError` (400) - Input validation failures
  - `AuthenticationError` (401) - Authentication required
  - `AuthorizationError` (403) - Insufficient permissions
  - `NotFoundError` (404) - Resource not found
  - `ConflictError` (409) - Resource conflicts
  - `RateLimitError` (429) - Rate limit exceeded
  - `BusinessLogicError` (422) - Business rule violations
  - `ExternalServiceError` (502) - Third-party service failures
  - `DatabaseError` (500) - Database operation failures
  - `InternalError` (500) - Unexpected server errors
  - `PaymentError` (400) - Payment processing failures

Each error includes:
```typescript
{
  statusCode: number;
  code: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  context: ErrorContext;
  timestamp: Date;
}
```

### 2. Enhanced Global Error Middleware (`server/middlewares/enhancedErrorHandler.ts`)

Features:
- **Automatic Error Conversion**: Unknown errors are converted to typed errors
- **Request Context Preservation**: Every error includes request ID, user ID, and metadata
- **Performance Tracking**: Monitors error processing times
- **Rate Limiting**: Prevents error spam from individual users
- **Critical Error Notifications**: Queues critical errors for admin alerts
- **Structured Logging**: Logs errors with appropriate severity levels

### 3. Async Error Handling Patterns

#### Route Handlers
All routes use the `asyncHandler` wrapper:
```typescript
router.get("/endpoint", authenticate, asyncHandler(async (req, res) => {
  // Your async code here
  // Errors are automatically caught and passed to global handler
}));
```

#### Service Layer
Services throw specific error types:
```typescript
async function getBooking(id: number): Promise<Booking> {
  const booking = await storage.getBooking(id);
  if (!booking) {
    throw new NotFoundError('Booking');
  }
  return booking;
}
```

#### Storage Layer
Enhanced with retry logic and error conversion:
```typescript
const wrappedMethod = wrapStorageMethod(
  'getUser',
  originalMethod,
  { 
    throwNotFound: true,
    retryConfig: { maxAttempts: 3 }
  }
);
```

### 4. Error Response Format

Consistent API error responses:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Email is required",
    "requestId": "abc123",
    "details": [...] // Only in development
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 5. Storage Error Wrapper (`server/utils/storageErrorWrapper.ts`)

Provides:
- **Automatic Retry Logic**: Retries transient failures with exponential backoff
- **Error Type Conversion**: Converts database errors to appropriate types
- **Transaction Support**: Handles batch operations with rollback
- **Sensitive Data Sanitization**: Redacts passwords and tokens from logs

## Usage Guide

### 1. In Route Handlers

```typescript
import { asyncHandler, NotFoundError, ValidationError } from '../middlewares/errorHandler';

router.get("/:id", authenticate, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('Invalid ID format');
  }
  
  const resource = await service.getResource(parseInt(id));
  if (!resource) {
    throw new NotFoundError('Resource');
  }
  
  return sendSuccess(res, resource);
}));
```

### 2. In Service Layer

```typescript
import { BusinessLogicError, ConflictError, ExternalServiceError } from '../errors';

class BookingService {
  async createBooking(data: BookingData): Promise<Booking> {
    // Check business rules
    if (data.guests > property.maxGuests) {
      throw new BusinessLogicError(
        `Guest count exceeds maximum of ${property.maxGuests}`,
        'GUEST_LIMIT_EXCEEDED'
      );
    }
    
    // Check availability
    const isAvailable = await this.checkAvailability(data);
    if (!isAvailable) {
      throw new ConflictError('Property is not available for selected dates');
    }
    
    // External service call
    try {
      const payment = await paymentService.process(data);
      return payment;
    } catch (error) {
      throw new ExternalServiceError(
        'Payment Gateway',
        'Payment processing failed',
        error
      );
    }
  }
}
```

### 3. In Storage Layer

```typescript
import { wrapStorageMethod, createStorageMethod } from '../utils/storageErrorWrapper';

class Storage {
  // Wrap existing methods
  getUser = wrapStorageMethod('getUser', this._getUser, { 
    throwNotFound: true 
  });
  
  // Create new methods with logging
  updateUser = createStorageMethod('updateUser', 
    async (id: number, data: UpdateUserData) => {
      // Implementation
    },
    { 
      logArgs: true,
      sensitiveFields: ['password', 'email']
    }
  );
}
```

## Error Categories and Severity Levels

### Categories
- `VALIDATION`: Input validation errors
- `AUTHENTICATION`: Auth failures
- `AUTHORIZATION`: Permission errors
- `BUSINESS_LOGIC`: Business rule violations
- `DATABASE`: Database errors
- `EXTERNAL_SERVICE`: Third-party failures
- `SYSTEM`: Internal errors

### Severity Levels
- `LOW`: User errors, validation failures
- `MEDIUM`: Business logic errors, auth failures
- `HIGH`: Database errors, external service failures
- `CRITICAL`: Unhandled errors, system failures

## Monitoring and Debugging

### Error Statistics Endpoint
```
GET /api/admin/error-stats
```

Returns:
```json
{
  "errorRateTracking": {
    "trackedUsers": 25,
    "window": 60000,
    "maxErrorsPerUser": 50
  },
  "performance": {
    "averageProcessingTime": 2.5,
    "sampledRequests": 100
  },
  "criticalErrors": {
    "queued": 2,
    "recent": [...]
  }
}
```

### Structured Logs
Errors are logged with rich context:
```json
{
  "level": "error",
  "message": "Database connection failed",
  "error": {
    "name": "DatabaseError",
    "code": "DATABASE_ERROR",
    "statusCode": 500,
    "severity": "high",
    "category": "database"
  },
  "context": {
    "requestId": "abc123",
    "userId": 456,
    "operation": "getBooking",
    "query": "SELECT * FROM bookings WHERE id = $1"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Migration Guide

### Before (Inconsistent)
```typescript
router.post("/booking", async (req, res) => {
  try {
    const booking = await createBooking(req.body);
    res.json({ success: true, data: booking });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Something went wrong' });
  }
});
```

### After (Standardized)
```typescript
router.post("/booking", authenticate, asyncHandler(async (req, res) => {
  const booking = await createBooking(req.body);
  return sendSuccess(res, booking, "Booking created", 201);
}));
```

## Best Practices

1. **Always use specific error types** - Don't throw generic Error objects
2. **Include context** - Add user ID, operation name, and relevant metadata
3. **Use asyncHandler** - Wrap all async route handlers
4. **Throw early** - Validate inputs and check preconditions first
5. **Log appropriately** - Use correct severity levels
6. **Don't catch and hide** - Let errors bubble up to global handler
7. **Test error paths** - Write tests for error scenarios

## Benefits Achieved

1. **Consistent Error Responses**: All endpoints return errors in the same format
2. **Better Debugging**: Request IDs and context make tracing errors easier
3. **Improved Monitoring**: Structured logs enable better alerting and dashboards
4. **Reduced Downtime**: Retry logic handles transient failures automatically
5. **Enhanced Security**: Sensitive data is sanitized from error messages
6. **Performance Tracking**: Error processing times are monitored
7. **User Experience**: Clear, actionable error messages for clients

## Future Enhancements

1. **Error Recovery Strategies**: Implement circuit breakers for external services
2. **Error Analytics**: Dashboard for error trends and patterns
3. **Automated Alerts**: Slack/email notifications for critical errors
4. **Error Replay**: Ability to replay failed requests after fixes
5. **Client SDK**: Generate TypeScript types for error responses

## Conclusion

The standardized error handling system provides a robust foundation for backend stability. By following these patterns, developers can ensure consistent error handling across all services, making the system more maintainable and reliable.