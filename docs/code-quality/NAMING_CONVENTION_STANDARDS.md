# Naming Convention Standards

Author: <PERSON><PERSON>eel Usmani  
Date: July 28, 2025

## Overview
This document establishes unified naming conventions across the entire codebase to eliminate confusion, reduce bugs, and improve developer experience.

## 🎯 Core Principles

1. **Consistency First**: All similar entities follow the same naming pattern
2. **Predictability**: Developers can predict names without looking them up
3. **Clarity**: Names should be self-documenting and unambiguous
4. **Scalability**: Conventions should work for small and large codebases

## 📋 Naming Standards by Context

### 1. **API Layer**

#### Response Format: **camelCase**
```typescript
// ✅ CORRECT - All API responses use camelCase
{
  "success": true,
  "data": {
    "id": 1,
    "fullName": "John <PERSON>",           // NOT: full_name
    "createdAt": "2024-01-01",        // NOT: created_at
    "bookingType": "full_day",        // NOT: booking_type
    "ownerId": 123,                   // NOT: owner_id
    "halfDayPrice": 1500,             // NOT: half_day_price
    "propertyData": {
      "images": [...],
      "amenities": [...]
    }
  }
}
```

#### Request Format: **camelCase**
```typescript
// ✅ CORRECT - All API requests use camelCase
const requestData = {
  fullName: "John Doe",
  bookingType: "full_day",
  specialRequests: "Late checkout",
  propertyId: 123
};
```

#### Endpoint URLs: **snake_case** (Following REST conventions)
```typescript
// ✅ CORRECT - URL paths use snake_case
GET /api/v1/booking_requests
POST /api/v1/property_listings  
PUT /api/v1/user_profiles/:id
DELETE /api/v1/booking_requests/:id
```

### 2. **Frontend Components**

#### File Names: **PascalCase**
```
✅ CORRECT:
/components/BookingForm/index.tsx
/components/PropertyCard/PropertyCard.tsx
/components/UserProfile/UserProfile.tsx
/components/PaymentModal/PaymentModal.tsx

❌ INCORRECT:
/components/booking-form.tsx
/components/propertyCard.tsx
/components/user_profile.tsx
```

#### Component Names: **PascalCase**
```typescript
// ✅ CORRECT
export const BookingForm: React.FC<BookingFormProps> = () => { ... };
export const PropertyCard: React.FC<PropertyCardProps> = () => { ... };
export const UserProfileModal: React.FC<UserProfileModalProps> = () => { ... };

// ❌ INCORRECT
export const bookingForm = () => { ... };
export const property_card = () => { ... };
export const userprofilemodal = () => { ... };
```

#### Props & Variables: **camelCase**
```typescript
// ✅ CORRECT
interface BookingFormProps {
  initialData?: BookingData;
  onSubmit: (data: BookingData) => void;
  isLoading: boolean;
  errorMessage?: string;
  showCancelButton: boolean;
}

const [isFormValid, setIsFormValid] = useState(false);
const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
```

### 3. **Database & Schema**

#### Database Columns: **snake_case** (SQL Convention)
```sql
-- ✅ CORRECT - Database columns use snake_case
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE properties (
  id SERIAL PRIMARY KEY,
  owner_id INTEGER REFERENCES users(id),
  half_day_price DECIMAL(10,2),
  full_day_price DECIMAL(10,2),
  booking_type VARCHAR(50)
);
```

#### TypeScript Schema: **camelCase** (After transformation)
```typescript
// ✅ CORRECT - TypeScript interfaces use camelCase
export interface User {
  id: number;
  fullName: string;        // Transformed from full_name
  email: string;
  createdAt: Date;         // Transformed from created_at
  updatedAt: Date;         // Transformed from updated_at
}

export interface Property {
  id: number;
  ownerId: number;         // Transformed from owner_id
  halfDayPrice: number;    // Transformed from half_day_price
  fullDayPrice: number;    // Transformed from full_day_price
  bookingType: 'morning' | 'full_day';  // Transformed from booking_type
}
```

### 4. **File & Directory Structure**

#### Component Organization: **Feature-based + PascalCase**
```
src/
├── components/
│   ├── BookingManagement/
│   │   ├── index.ts                    # Barrel export
│   │   ├── BookingForm/
│   │   │   ├── index.tsx
│   │   │   ├── BookingForm.tsx
│   │   │   ├── BookingForm.test.tsx
│   │   │   └── BookingForm.stories.tsx
│   │   ├── BookingList/
│   │   │   ├── index.tsx
│   │   │   ├── BookingList.tsx
│   │   │   └── BookingCard/
│   │   │       ├── index.tsx
│   │   │       └── BookingCard.tsx
│   │   └── BookingStatusButton/
│   │       ├── index.tsx
│   │       └── BookingStatusButton.tsx
│   ├── PropertyManagement/
│   │   ├── index.ts
│   │   ├── PropertyForm/
│   │   ├── PropertyCard/
│   │   └── PropertyList/
│   └── shared/                         # Reusable UI components
│       ├── Button/
│       ├── Modal/
│       └── LoadingSpinner/
```

#### Page Components: **PascalCase**
```
src/pages/
├── HomePage/
│   ├── index.tsx
│   ├── HomePage.tsx
│   └── components/           # Page-specific components
├── BookingPage/
│   ├── index.tsx  
│   └── BookingPage.tsx
└── PropertyDetailPage/
    ├── index.tsx
    └── PropertyDetailPage.tsx
```

### 5. **Hooks & Utilities**

#### Custom Hooks: **camelCase with 'use' prefix**
```typescript
// ✅ CORRECT
export const useBookingForm = () => { ... };
export const usePropertySearch = () => { ... };
export const useApiCall = () => { ... };
export const useAuthState = () => { ... };

// ❌ INCORRECT
export const UseBookingForm = () => { ... };
export const use_property_search = () => { ... };
export const bookingFormHook = () => { ... };
```

#### Utility Functions: **camelCase**
```typescript
// ✅ CORRECT
export const formatPrice = (price: number) => { ... };
export const validateEmail = (email: string) => { ... };
export const transformApiResponse = (data: any) => { ... };
export const calculateBookingTotal = (data: BookingData) => { ... };

// ❌ INCORRECT  
export const FormatPrice = (price: number) => { ... };
export const validate_email = (email: string) => { ... };
export const TransformApiResponse = (data: any) => { ... };
```

### 6. **Constants & Enums**

#### Constants: **SCREAMING_SNAKE_CASE**
```typescript
// ✅ CORRECT
export const API_ENDPOINTS = {
  BOOKINGS: '/api/v1/bookings',
  PROPERTIES: '/api/v1/properties',
  USER_PROFILE: '/api/v1/user_profile'
} as const;

export const BOOKING_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  REJECTED: 'rejected',
  CANCELLED: 'cancelled'
} as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const DEFAULT_PAGE_SIZE = 20;
```

#### Enums: **PascalCase**
```typescript
// ✅ CORRECT
export enum BookingType {
  Morning = 'morning',
  FullDay = 'full_day'
}

export enum UserRole {
  User = 'user',
  Owner = 'owner',
  Admin = 'admin'
}
```

### 7. **Types & Interfaces**

#### Interface Names: **PascalCase**
```typescript
// ✅ CORRECT
export interface BookingFormData {
  guestName: string;
  bookingType: BookingType;
  specialRequests?: string;
  agreedToTerms: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: ValidationError[];
}

// ❌ INCORRECT
export interface bookingFormData { ... }
export interface booking_form_data { ... }
export interface IBookingFormData { ... } // Hungarian notation - avoid
```

#### Type Aliases: **PascalCase**
```typescript
// ✅ CORRECT
export type BookingStatus = 'pending' | 'confirmed' | 'rejected' | 'cancelled';
export type PropertyType = 'farmhouse' | 'villa' | 'cottage';
export type UserPermissions = 'read' | 'write' | 'admin';
```

## 🔄 Transformation Strategy

### API Response Transformation

#### Database → API Response
```typescript
// Database query result (snake_case)
const dbResult = {
  id: 1,
  full_name: "John Doe",
  created_at: "2024-01-01T00:00:00Z",
  booking_type: "full_day"
};

// Transformed API response (camelCase)
const apiResponse = {
  success: true,
  data: {
    id: 1,
    fullName: "John Doe",      // full_name → fullName
    createdAt: "2024-01-01T00:00:00Z",  // created_at → createdAt
    bookingType: "full_day"    // booking_type → bookingType
  }
};
```

#### Frontend → API Request
```typescript
// Frontend form data (camelCase)
const formData = {
  fullName: "John Doe",
  bookingType: "full_day",
  specialRequests: "Late checkout"
};

// API request (camelCase - no transformation needed)
const apiRequest = {
  fullName: "John Doe",
  bookingType: "full_day", 
  specialRequests: "Late checkout"
};
```

## 🛠️ Implementation Tools

### 1. **Automatic Case Conversion**
```typescript
// Utility for database → API transformation
export const toCamelCase = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      result[camelKey] = toCamelCase(obj[key]);
      return result;
    }, {} as any);
  }
  return obj;
};

// Utility for API → database transformation  
export const toSnakeCase = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      result[snakeKey] = toSnakeCase(obj[key]);
      return result;
    }, {} as any);
  }
  return obj;
};
```

### 2. **ESLint Rules for Enforcement**
```json
{
  "rules": {
    "@typescript-eslint/naming-convention": [
      "error",
      { "selector": "interface", "format": ["PascalCase"] },
      { "selector": "typeAlias", "format": ["PascalCase"] },
      { "selector": "enum", "format": ["PascalCase"] },
      { "selector": "variable", "format": ["camelCase", "UPPER_CASE"] },
      { "selector": "function", "format": ["camelCase"] },
      { "selector": "parameter", "format": ["camelCase"] },
      { "selector": "classMethod", "format": ["camelCase"] },
      { "selector": "objectLiteralProperty", "format": ["camelCase"] }
    ]
  }
}
```

## 📊 Benefits of Standardization

### 1. **Developer Experience**
- ✅ Predictable naming reduces cognitive load
- ✅ Faster development with consistent patterns
- ✅ Better IDE autocomplete and refactoring
- ✅ Easier onboarding for new developers

### 2. **Code Quality**
- ✅ Reduced bugs from naming mismatches
- ✅ Improved code readability and maintainability
- ✅ Better type safety with consistent interfaces
- ✅ Easier code reviews and debugging

### 3. **Team Collaboration**
- ✅ Clear communication with shared vocabulary
- ✅ Consistent expectations across team members
- ✅ Reduced bikeshedding in code reviews
- ✅ Professional, polished codebase

## 🎯 Migration Checklist

### Phase 1: Foundation
- [ ] Create case conversion utilities
- [ ] Update API response transformation layer
- [ ] Implement ESLint naming rules
- [ ] Document standards (this document)

### Phase 2: API Standardization
- [ ] Audit all API endpoints for naming consistency
- [ ] Implement automatic camelCase transformation
- [ ] Update TypeScript interfaces to match
- [ ] Test API response formats

### Phase 3: Frontend Standardization
- [ ] Rename components to PascalCase
- [ ] Organize files into feature-based structure
- [ ] Update component props to camelCase
- [ ] Standardize hook and utility naming

### Phase 4: Validation & Enforcement
- [ ] Set up automated linting in CI/CD
- [ ] Update development tooling
- [ ] Create naming convention documentation
- [ ] Train team on new standards

## 🔍 Common Pitfalls to Avoid

### 1. **Mixed Case Systems**
```typescript
// ❌ WRONG - Mixing snake_case and camelCase
interface UserData {
  fullName: string;      // camelCase
  created_at: string;    // snake_case - inconsistent!
  user_id: number;       // snake_case - inconsistent!
  isActive: boolean;     // camelCase
}

// ✅ CORRECT - Consistent camelCase
interface UserData {
  fullName: string;
  createdAt: string;
  userId: number;
  isActive: boolean;
}
```

### 2. **Inconsistent File Organization**
```
❌ WRONG - Mixed patterns
/components/booking-form.tsx
/components/PropertyCard.tsx  
/components/user_profile.tsx
/components/PaymentModal/index.tsx

✅ CORRECT - Consistent PascalCase + feature organization
/components/BookingManagement/BookingForm/index.tsx
/components/PropertyManagement/PropertyCard/index.tsx
/components/UserManagement/UserProfile/index.tsx
/components/PaymentManagement/PaymentModal/index.tsx
```

### 3. **Ambiguous Abbreviations**
```typescript
// ❌ WRONG - Unclear abbreviations
const usrMgmt = new UserManagement();
const propSvc = new PropertyService();
const bkgReq = createBookingRequest();

// ✅ CORRECT - Clear, descriptive names
const userManagement = new UserManagement();
const propertyService = new PropertyService();
const bookingRequest = createBookingRequest();
```

This naming convention standard ensures consistency, clarity, and maintainability across the entire codebase while following industry best practices.