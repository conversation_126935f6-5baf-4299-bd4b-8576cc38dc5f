# WhatsApp Booking Integration for BookAFarm

This document describes the WhatsApp integration that allows property owners to receive booking requests directly through WhatsApp messages and automatically create bookings in the BookAFarm calendar system.

## 🎯 Overview

The WhatsApp integration enables property owners to:
- Receive natural language booking commands via WhatsApp
- Automatically parse dates, guest names, and booking details
- Create bookings directly in the BookAFarm calendar system
- Send instant confirmations back to customers
- Sync bookings in real-time with the web platform

## 📋 Example Usage

**Customer sends to property owner:**
```
Book August 20 to August 22 for John
```

**System automatically:**
1. Parses the message to extract dates and guest name
2. Finds the property owner by phone number
3. Checks calendar availability
4. Creates the booking in BookAFarm
5. Sends confirmation via WhatsApp

**Confirmation sent to customer:**
```
✅ Booking Confirmed!

🏡 Property: Sunny Farmhouse
📅 Dates: Aug 20, 2025 to Aug 22, 2025
👤 Guest: John
📱 Reference: #12345

📞 For any changes, contact [Owner Name] directly.
🔗 View calendar: https://bookafarm.com/calendar/123
```

## 🛠️ Technical Implementation

### Architecture Components

1. **WhatsApp Webhook** (`/api/whatsapp/webhook`)
   - Receives incoming WhatsApp messages from <PERSON>wilio
   - Validates webhook signatures in production
   - Routes messages to appropriate service handlers

2. **WhatsAppMessageParser** (`server/services/WhatsAppMessageParser.ts`)
   - Parses natural language booking commands
   - Extracts dates, guest names, and booking details
   - Supports multiple date formats and patterns

3. **OwnerWhatsAppService** (`server/services/OwnerWhatsAppService.ts`)
   - Handles owner-specific booking flows
   - Maps phone numbers to property owners
   - Manages booking creation and confirmations

4. **CalendarService Integration**
   - Uses existing `CalendarService.createCalendarBooking()`
   - Checks availability before creating bookings
   - Maintains real-time sync with web calendar

### Supported Message Formats

The system can parse various natural language formats:

```
Book August 20 to August 22 for John
Book Aug 15-17 for Sarah
book December 25 to December 28 for Maria
Book September 10 to September 12 for Alex
Book 20 August to 22 August for John Smith
```

### Date Format Support

- **Month Day to Month Day**: "August 20 to August 22"
- **Day Month to Day Month**: "20 August to 22 August"  
- **Month Day-Day**: "Aug 20-22"
- **Day-Day Month**: "20-22 Aug"
- **Mixed formats**: Various combinations

### Name Extraction

- Extracts guest names after "for" keyword
- Supports single names: "John"
- Supports full names: "John Smith", "Maria Garcia"
- Proper case formatting: "john" → "John"

## ⚙️ Setup and Configuration

### 1. Environment Variables

Add to your `.env` file:

```env
# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
WHATSAPP_WEBHOOK_URL=https://your-domain.com/api/whatsapp/webhook
WHATSAPP_WEBHOOK_TOKEN=your_webhook_verification_token
```

### 2. Database Setup

Ensure your `users` table has a `whatsappNumber` field:

```sql
ALTER TABLE users ADD COLUMN whatsapp_number VARCHAR(20);

-- Example owner setup
UPDATE users 
SET whatsapp_number = '+************' 
WHERE id = 1 AND role = 'owner';
```

### 3. Twilio Webhook Configuration

In your Twilio Console:
- Set webhook URL: `https://your-domain.com/api/whatsapp/webhook`
- Method: POST
- Include webhook token for verification

### 4. Owner Phone Number Mapping

Property owners must be mapped to WhatsApp numbers in the database:

```javascript
// Example: Setting up an owner
const owner = await db.update(users)
  .set({ whatsappNumber: '+************' })
  .where(eq(users.id, ownerId));
```

## 🔄 Booking Flow

### Single Property Owner

1. Customer sends: "Book August 20 to August 22 for John"
2. System finds owner by phone number
3. Owner has one property → Direct booking
4. Check availability → Create booking → Send confirmation

### Multiple Properties Owner

1. Customer sends: "Book August 20 to August 22 for John"
2. System finds owner by phone number  
3. Owner has multiple properties → Show property list
4. Customer selects property → Create booking → Send confirmation

### Flow Diagram

```
WhatsApp Message
       ↓
Parse Intent & Entities
       ↓
Find Property Owner
       ↓
Single Property? ──No──→ Show Property List
       ↓ Yes                    ↓
Check Availability         Customer Selects
       ↓                       ↓
Create Booking ←───────────────┘
       ↓
Send Confirmation
       ↓
Real-time Calendar Sync
```

## 🧪 Testing

### Run Tests

```bash
npm test -- tests/unit/backend/whatsapp-booking.test.ts
```

### Demo Script

```bash
node scripts/whatsapp-demo.js
```

### Manual Testing

1. Set up test owner with WhatsApp number
2. Use Twilio sandbox for testing
3. Send test messages to webhook
4. Verify bookings appear in calendar

## 📱 API Endpoints

### Webhook Endpoints

- `GET /api/whatsapp/webhook` - Webhook verification
- `POST /api/whatsapp/webhook` - Receive WhatsApp messages

### Development Endpoints

- `POST /api/whatsapp/send-test` - Send test messages (dev only)
- `POST /api/whatsapp/test-owner-flow` - Test owner booking flow
- `GET /api/whatsapp/test-calendar/:propertyId` - Test calendar generation

### Status Endpoint

- `GET /api/whatsapp/status` - Service health check

## 🔒 Security Considerations

1. **Webhook Signature Validation**
   - Validates Twilio signatures in production
   - Prevents unauthorized webhook calls

2. **Rate Limiting**
   - 100 requests per minute per IP for webhook endpoint
   - Prevents spam and abuse

3. **Input Validation**
   - Sanitizes all user inputs
   - Validates date ranges and guest names
   - Prevents injection attacks

4. **Fail-Safe Behavior**
   - Graceful handling of parsing errors
   - Fallback to interactive flow if direct parsing fails
   - Error notifications without exposing system details

## 🚀 Advanced Features

### Interactive Flows

For complex bookings, the system falls back to interactive flows:
- Property selection for multi-property owners
- Date selection with calendar view
- Guest details collection
- Confirmation workflows

### Calendar Integration

- Real-time availability checking
- Automatic iCal feed updates
- Webhook triggers for external integrations
- Conflict detection and prevention

### Message Templates

Support for WhatsApp Business templates:
- Booking confirmations
- Availability responses
- Error notifications
- Welcome messages

## 📊 Monitoring and Analytics

### Logging

All WhatsApp interactions are logged with:
- Message parsing results
- Booking creation events
- Error conditions
- Performance metrics

### Metrics

Track key metrics:
- Message parsing accuracy
- Booking conversion rates
- Response times
- Error rates

## 🛠️ Troubleshooting

### Common Issues

1. **Messages not being received**
   - Check Twilio webhook configuration
   - Verify webhook URL is accessible
   - Check webhook token matches

2. **Parsing failures**
   - Review message format
   - Check date extraction patterns
   - Verify guest name patterns

3. **Booking creation failures**
   - Verify property owner mapping
   - Check calendar service connectivity
   - Review availability conflicts

### Debug Tools

```javascript
// Test message parsing
const parser = new WhatsAppMessageParser();
const result = parser.parseMessage("Book August 20 to August 22 for John");
console.log(result);

// Test owner lookup
const owner = await ownerWhatsAppService.findOwnerByWhatsApp("+************");
console.log(owner);
```

## 🔄 Future Enhancements

### Planned Features

1. **Multi-language Support**
   - Support for Hindi, regional languages
   - Localized date formats

2. **Smart Scheduling**
   - Suggest alternative dates if unavailable
   - Optimal pricing recommendations

3. **Payment Integration**
   - WhatsApp Pay integration
   - Payment links in confirmations

4. **Advanced Analytics**
   - Booking source tracking
   - Customer behavior analysis
   - Revenue attribution

### Integration Opportunities

- **CRM Integration**: Sync with customer management systems
- **Marketing Automation**: Follow-up messages and promotions
- **IoT Integration**: Smart property management features

## 📞 Support

For technical support or questions about the WhatsApp integration:

1. Check the test files: `tests/unit/backend/whatsapp-booking.test.ts`
2. Review the demo script: `scripts/whatsapp-demo.js`
3. Examine service logs for debugging information
4. Use development endpoints for testing and validation

---

*This integration seamlessly connects WhatsApp communication with BookAFarm's booking system, enabling property owners to manage bookings directly through familiar messaging interfaces.*