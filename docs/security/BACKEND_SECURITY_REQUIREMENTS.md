# Backend Security Requirements for Payment System

**Author: Fazeel Usmani**  
**Date: July 22, 2025**

## 🚨 Critical Security Implementation Requirements

This document outlines the **mandatory** backend security requirements to support the frontend security enhancements. All requirements marked as **CRITICAL** must be implemented before production deployment.

---

## 1. 🔐 Payment Security Implementation

### 1.1 Server-Side Payment Processing **[CRITICAL]**

**Required API Endpoints:**

```
POST /api/payments/create-order
POST /api/payments/process
POST /api/payments/webhook (Razorpay webhook)
POST /api/payments/refund
```

**Implementation Requirements:**

- **ALL payment amount calculations must be server-side only**
- **Never trust client-side amount data**
- Implement idempotency keys for duplicate prevention
- Server-side signature verification for all Razorpay responses
- Automatic payment capture with retry logic
- Comprehensive audit logging for all payment events

**Example Implementation:**

```python
@app.route('/api/payments/create-order', methods=['POST'])
@require_auth
@rate_limit(max_attempts=3, window_minutes=60)
def create_payment_order():
    # Server calculates ALL amounts - never trust client
    booking = get_booking(request.json['bookingId'])
    
    # Calculate amounts server-side with validation
    base_amount = calculate_base_amount(booking)
    gst_amount = calculate_gst(base_amount, booking.gst_rate)
    advance_amount = calculate_advance_amount(base_amount + gst_amount)
    
    # Create Razorpay order with server-calculated amount
    order = razorpay_client.order.create({
        'amount': advance_amount * 100,  # Amount in paise
        'currency': 'INR',
        'receipt': f'booking_{booking.id}_{int(time.time())}',
    })
    
    # Store order details in database with audit trail
    payment_order = PaymentOrder.create(
        booking_id=booking.id,
        razorpay_order_id=order['id'],
        amount=advance_amount,
        created_by=current_user.id,
        razorpay_key_id=get_razorpay_public_key()  # Server provides key
    )
    
    # Log security event
    audit_log('PAYMENT_ORDER_CREATED', {
        'booking_id': booking.id,
        'amount': advance_amount,
        'user_id': current_user.id
    })
    
    return jsonify({
        'success': True,
        'data': {
            'paymentOrder': payment_order.to_dict(),
            'breakdown': calculate_payment_breakdown(booking)
        }
    })
```

### 1.2 Razorpay Webhook Security **[CRITICAL]**

**Requirements:**
- Verify webhook signatures using Razorpay webhook secret
- Implement idempotency to prevent duplicate processing
- Handle all webhook events (payment.captured, payment.failed, etc.)
- Update booking status atomically
- Send confirmation emails/SMS

```python
@app.route('/api/payments/webhook', methods=['POST'])
def razorpay_webhook():
    # Verify webhook signature
    signature = request.headers.get('X-Razorpay-Signature')
    if not verify_razorpay_signature(request.data, signature):
        audit_log('WEBHOOK_SIGNATURE_FAILED', {'ip': request.remote_addr})
        return '', 400
    
    payload = request.json
    event_type = payload['event']
    
    # Handle payment events
    if event_type == 'payment.captured':
        handle_payment_success(payload['payload']['payment']['entity'])
    elif event_type == 'payment.failed':
        handle_payment_failure(payload['payload']['payment']['entity'])
    
    return '', 200
```

---

## 2. 🛡️ Authentication & Authorization

### 2.1 Two-Factor Authentication **[HIGH PRIORITY]**

**Required API Endpoints:**

```
POST /api/auth/2fa/send
POST /api/auth/2fa/verify
```

**Implementation Requirements:**

- SMS/Email 2FA for transactions > ₹10,000
- Generate cryptographically secure 6-digit codes
- Codes expire after 5 minutes
- Max 3 attempts per session
- Rate limiting: 3 2FA requests per hour per user

```python
@app.route('/api/auth/2fa/send', methods=['POST'])
@require_auth
@rate_limit(max_attempts=3, window_minutes=60)
def send_2fa_code():
    method = request.json['method']  # 'sms' or 'email'
    amount = request.json['amount']
    
    # Generate secure 6-digit code
    code = secrets.randbelow(900000) + 100000
    
    # Store in Redis with 5-minute expiry
    redis_client.setex(
        f'2fa:{current_user.id}:{method}',
        300,  # 5 minutes
        code
    )
    
    if method == 'sms':
        send_sms(current_user.phone, f'Your verification code: {code}')
    else:
        send_email(current_user.email, f'Your verification code: {code}')
    
    audit_log('2FA_CODE_SENT', {
        'user_id': current_user.id,
        'method': method,
        'amount': amount
    })
    
    return jsonify({
        'success': True,
        'data': {
            'maskedContact': mask_contact_info(
                current_user.phone if method == 'sms' else current_user.email
            )
        }
    })
```

### 2.2 Session Security **[CRITICAL]**

**Requirements:**
- Session timeout: 30 minutes of inactivity
- Payment session timeout: 10 minutes
- Secure session tokens (JWT with short expiry)
- Session invalidation on suspicious activity
- Device fingerprinting for fraud detection

---

## 3. 📊 Audit Logging & Monitoring

### 3.1 Security Event Logging **[CRITICAL]**

**Required API Endpoint:**

```
POST /api/audit/events
```

**Log All Security Events:**
- Payment attempts/successes/failures
- Login/logout events
- 2FA events
- Rate limiting violations
- Suspicious activities
- API access patterns

**Implementation:**

```python
def audit_log(event_type, details, user_id=None, risk_score=None):
    event = SecurityEvent(
        event_type=event_type,
        user_id=user_id or (current_user.id if current_user else None),
        details=details,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent'),
        timestamp=datetime.utcnow(),
        risk_score=risk_score
    )
    
    # Store in database
    db.session.add(event)
    db.session.commit()
    
    # Send to external monitoring (Datadog, Sentry, etc.)
    send_to_monitoring_service(event)
    
    # Check for suspicious patterns
    if is_suspicious_pattern(event_type, user_id):
        trigger_fraud_alert(user_id, event_type)
```

### 3.2 Real-time Fraud Detection **[HIGH PRIORITY]**

**Requirements:**
- Monitor payment velocity (max 3 payments per hour)
- Detect unusual amounts or patterns
- Geographic anomaly detection
- Device fingerprint analysis
- Risk scoring algorithm

```python
def calculate_fraud_risk_score(user_id, amount, device_info):
    risk_score = 0
    
    # Check payment velocity
    recent_payments = count_recent_payments(user_id, hours=1)
    if recent_payments > 3:
        risk_score += 30
    
    # Check amount patterns
    avg_amount = get_user_avg_transaction_amount(user_id)
    if amount > avg_amount * 3:
        risk_score += 25
    
    # Check device/location
    if is_new_device(user_id, device_info):
        risk_score += 20
    
    # Check time patterns
    if is_unusual_time(user_id):
        risk_score += 15
    
    return min(risk_score, 100)
```

---

## 4. 🔒 Rate Limiting & Security Controls

### 4.1 API Rate Limiting **[CRITICAL]**

**Implementation Requirements:**

```python
from functools import wraps
import redis

redis_client = redis.Redis()

def rate_limit(max_attempts, window_minutes, block_duration_minutes=60):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = current_user.id if current_user else request.remote_addr
            key = f'rate_limit:{f.__name__}:{user_id}'
            
            current_attempts = redis_client.get(key)
            
            if current_attempts and int(current_attempts) >= max_attempts:
                audit_log('RATE_LIMIT_EXCEEDED', {
                    'endpoint': f.__name__,
                    'attempts': current_attempts,
                    'user_id': user_id
                })
                return jsonify({'error': 'Rate limit exceeded'}), 429
            
            # Increment attempts
            pipeline = redis_client.pipeline()
            pipeline.incr(key)
            pipeline.expire(key, window_minutes * 60)
            pipeline.execute()
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
```

### 4.2 Input Validation & Sanitization **[CRITICAL]**

**Requirements:**
- Validate all input parameters
- Sanitize string inputs
- Check data types and ranges
- Prevent SQL injection and XSS

```python
from marshmallow import Schema, fields, validate

class CreatePaymentSchema(Schema):
    booking_id = fields.Int(required=True, validate=validate.Range(min=1))
    currency = fields.Str(required=True, validate=validate.OneOf(['INR']))
    customer_details = fields.Dict(required=True)
    gst_details = fields.Dict(required=True)

@app.route('/api/payments/create-order', methods=['POST'])
@require_auth
def create_payment_order():
    schema = CreatePaymentSchema()
    try:
        data = schema.load(request.json)
    except ValidationError as err:
        audit_log('INPUT_VALIDATION_FAILED', {'errors': err.messages})
        return jsonify({'error': 'Invalid input'}), 400
    
    # Process validated data...
```

---

## 5. 🚨 Error Handling & Monitoring

### 5.1 Secure Error Handling **[CRITICAL]**

**Requirements:**
- Never expose sensitive information in error messages
- Log detailed errors server-side
- Return generic error messages to clients
- Implement error monitoring and alerting

```python
@app.errorhandler(Exception)
def handle_error(error):
    error_id = str(uuid.uuid4())
    
    # Log detailed error server-side
    logger.error(f'Error {error_id}: {str(error)}', exc_info=True)
    
    # Audit log for security monitoring
    audit_log('APPLICATION_ERROR', {
        'error_id': error_id,
        'error_type': type(error).__name__,
        'endpoint': request.endpoint
    })
    
    # Return generic error to client
    return jsonify({
        'error': 'An unexpected error occurred. Please try again.',
        'error_id': error_id
    }), 500
```

### 5.2 External Monitoring Integration **[HIGH PRIORITY]**

**Required API Endpoint:**

```
POST /api/monitoring/error
```

**Integration with monitoring services:**
- Sentry for error tracking
- DataDog for metrics and alerting
- LogRocket for session replay
- CloudWatch for AWS infrastructure

---

## 6. 🔐 Data Protection & Encryption

### 6.1 Data Encryption **[CRITICAL]**

**Requirements:**
- Encrypt sensitive data at rest (AES-256)
- Use TLS 1.3 for data in transit
- Hash passwords with bcrypt
- Encrypt payment-related PII

```python
from cryptography.fernet import Fernet
import bcrypt

# Initialize encryption key (store securely)
encryption_key = Fernet.generate_key()
cipher_suite = Fernet(encryption_key)

def encrypt_sensitive_data(data):
    return cipher_suite.encrypt(data.encode()).decode()

def decrypt_sensitive_data(encrypted_data):
    return cipher_suite.decrypt(encrypted_data.encode()).decode()

def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
```

### 6.2 PCI DSS Compliance **[CRITICAL]**

**Requirements:**
- Never store card details
- Use Razorpay's secure payment flow
- Implement proper access controls
- Regular security assessments
- Network segmentation

---

## 7. 📋 Deployment & Infrastructure Security

### 7.1 Environment Configuration **[CRITICAL]**

```python
# Environment-specific configuration
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY')
    RAZORPAY_KEY_ID = os.environ.get('RAZORPAY_KEY_ID')
    RAZORPAY_KEY_SECRET = os.environ.get('RAZORPAY_KEY_SECRET')  # Never expose
    RAZORPAY_WEBHOOK_SECRET = os.environ.get('RAZORPAY_WEBHOOK_SECRET')
    
    # Security headers
    SECURITY_HEADERS = {
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'",
    }
```

### 7.2 Infrastructure Security **[HIGH PRIORITY]**

**Requirements:**
- WAF (Web Application Firewall) configuration
- DDoS protection
- Regular security updates
- Backup and disaster recovery
- Network monitoring and intrusion detection

---

## 8. 🧪 Testing & Quality Assurance

### 8.1 Security Testing **[CRITICAL]**

**Required Tests:**
- Penetration testing before production
- Automated security scanning (OWASP ZAP)
- Load testing for DDoS resilience
- Input validation testing
- Authentication bypass testing

### 8.2 Code Security Review **[HIGH PRIORITY]**

**Requirements:**
- Static code analysis (SonarQube, CodeQL)
- Dependency vulnerability scanning
- Secret detection in code
- Regular security audits

---

## 9. 📈 Performance & Scalability

### 9.1 Database Security **[CRITICAL]**

```sql
-- Example: Secure database configuration
-- Use prepared statements to prevent SQL injection
-- Implement proper indexing for audit logs
-- Set up database encryption at rest
-- Configure database access controls

CREATE INDEX idx_security_events_user_timestamp 
ON security_events (user_id, timestamp);

CREATE INDEX idx_payment_orders_status 
ON payment_orders (status, created_at);
```

### 9.2 Caching Security **[HIGH PRIORITY]**

**Requirements:**
- Secure Redis configuration
- Encrypt cached sensitive data
- Implement cache invalidation policies
- Monitor cache hit rates for anomalies

---

## 10. 📞 Incident Response

### 10.1 Security Incident Playbook **[CRITICAL]**

**Automated Responses:**
- Block suspicious IP addresses
- Disable compromised user accounts
- Revoke suspicious sessions
- Alert security team immediately

**Manual Response Procedures:**
1. Identify and contain the incident
2. Preserve evidence for investigation
3. Notify affected users and authorities
4. Implement fixes and monitoring
5. Post-incident review and improvements

---

## Implementation Priority

### Phase 1 (Critical - Week 1)
- [ ] Server-side payment processing
- [ ] Razorpay webhook security
- [ ] Input validation and rate limiting
- [ ] Secure error handling
- [ ] Basic audit logging

### Phase 2 (High Priority - Week 2)
- [ ] Two-factor authentication
- [ ] Fraud detection system
- [ ] Session security
- [ ] Data encryption
- [ ] Monitoring integration

### Phase 3 (Medium Priority - Week 3-4)
- [ ] Advanced fraud detection
- [ ] Security testing
- [ ] Performance optimization
- [ ] Incident response procedures
- [ ] Documentation and training

---

## 🔗 Integration with Frontend

The frontend security components expect these specific API responses:

```typescript
// Expected API response formats
interface PaymentOrderResponse {
  success: boolean;
  data: {
    paymentOrder: {
      id: string;
      razorpayOrderId: string;
      amount: number;
      currency: string;
      razorpayKeyId: string; // Server provides key
    };
    breakdown: PaymentBreakdown;
  };
}

interface SecurityEventRequest {
  events: AuditEvent[];
  clientTimestamp: string;
}
```

---

## 📊 Monitoring & Alerting Setup

Set up alerts for:
- Payment failures > 5% in 10 minutes
- Rate limiting violations > 10 per minute
- Failed 2FA attempts > 10 per hour
- New device logins from unusual locations
- Database query response time > 2 seconds
- Memory/CPU usage > 80%

---

**⚠️ CRITICAL REMINDER: This is not optional security enhancement - it's mandatory for production deployment. The current frontend implementation REQUIRES these backend security measures to function properly and securely.**