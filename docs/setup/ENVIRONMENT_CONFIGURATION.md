# Environment Configuration Guide

Author: Fazeel Usmani  
Date: August 5, 2025

## 🔧 **Dynamic Environment Loading System**

The application supports dynamic environment file loading based on the standard `NODE_ENV` variable, allowing you to maintain separate configurations for development, production, and testing.

## 📁 **Environment Files Structure**

```
.env.development      # Development environment settings
.env.production       # Production environment settings
.env.test            # Test environment settings
.env.local           # Local overrides (not committed)
.env                 # Default fallback (not committed)
```

## 🎯 **How It Works**

### **Priority Order**
The system loads environment files in this order:
1. `.env.{NODE_ENV}` (e.g., `.env.production`, `.env.development`, or `.env.test`)
2. `.env.local` (local overrides)
3. `.env` (default fallback)

### **Environment Detection**
```typescript
// The system uses the standard NODE_ENV variable
const nodeEnv = process.env.NODE_ENV || 'development';
```

## 🚀 **Usage**

### **Development Mode**
```bash
# Option 1: Set NODE_ENV
NODE_ENV=development npm run dev

# Option 2: Use predefined script
npm run dev                    # Automatically sets NODE_ENV=development
npm run start:dev             # Explicitly sets NODE_ENV=development
```

### **Production Mode**
```bash
# Option 1: Set NODE_ENV
NODE_ENV=production npm run start

# Option 2: Use predefined script
npm run start                 # Automatically sets NODE_ENV=production
npm run start:prod           # Explicitly sets NODE_ENV=production
```

### **Test Mode**
```bash
# Option 1: Set NODE_ENV
NODE_ENV=test npm test

# Option 2: Testing will automatically use test environment
npm run test                  # Uses .env.test if available
```

## ⚙️ **Available Scripts**

| Script | NODE_ENV | Description |
|--------|----------|-------------|
| `npm run dev` | development | Development server with hot reloading |
| `npm run start` | production | Production server |
| `npm run start:dev` | development | Explicit development mode |
| `npm run start:prod` | production | Explicit production mode |
| `npm test` | test | Run tests (uses .env.test) |

## 📝 **Environment File Examples**

### **.env.development**
```env
NODE_ENV=development

# Development Database
DATABASE_URL=postgresql://farmhouse_user:farmhouse_password@localhost:5432/farmhouse_rental

# Development Secrets (safe for development)
JWT_SECRET=dev-jwt-secret-change-in-production
COOKIE_SECRET=dev-cookie-secret-change-in-production

# Logging Configuration
LOG_LEVEL=debug    # Show all logs including DEBUG (development default)
# LOG_LEVEL=info   # Set to reduce log noise in development

# Development services (optional)
# TWILIO_ACCOUNT_SID=your-dev-twilio-sid
# SENDGRID_API_KEY=your-dev-sendgrid-key
```

### **.env.production**
```env
NODE_ENV=production

# Production Database
DATABASE_URL=************************************************/farmhouse_rental

# Production Secrets (SECURE RANDOM VALUES!)
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters
COOKIE_SECRET=your-super-secure-cookie-secret-minimum-32-characters

# Logging Configuration (production optimized)
LOG_LEVEL=info     # Reduces log volume by 70% (production default)
# LOG_LEVEL=warn   # Further reduce logs to warnings and errors only

# Production Services (required)
TWILIO_ACCOUNT_SID=your-production-twilio-sid
TWILIO_AUTH_TOKEN=your-production-twilio-token
SENDGRID_API_KEY=your-production-sendgrid-key
CLOUDINARY_CLOUD_NAME=your-production-cloudinary-name
```

### **.env.test**
```env
NODE_ENV=test

# Test Database (separate from development)
DATABASE_URL=postgresql://farmhouse_user:farmhouse_password@localhost:5433/farmhouse_rental_test

# Test Secrets (safe for testing)
JWT_SECRET=test-jwt-secret-for-testing-only
COOKIE_SECRET=test-cookie-secret-for-testing-only

# Logging Configuration (minimal for testing)
LOG_LEVEL=warn     # Show only warnings and errors (test default)
# LOG_LEVEL=error  # Show only errors for quiet test runs

# Test services (optional - can use mocks)
# TWILIO_ACCOUNT_SID=test-twilio-sid
# SENDGRID_API_KEY=test-sendgrid-key
```

## 🔒 **Security Best Practices**

### **✅ Do's**
- Use strong, random secrets in production
- Keep `.env.production` secure and never commit it
- Use different database credentials for dev/prod
- Store production secrets in secure vaults when possible

### **❌ Don'ts**
- Never commit actual `.env` files to version control
- Don't use development secrets in production
- Don't share production environment files

## 📊 **Logging Configuration**

The platform includes a structured logging system with environment-specific optimization.

### **Available Log Levels**

| Level | Description | Use Case |
|-------|-------------|----------|
| `debug` | All logs including detailed debugging | Development debugging |
| `info` | Informational logs and above | Development default |
| `warn` | Warnings and errors only | Test environment |
| `error` | Errors only | Minimal logging |

### **Environment Defaults**

- **Development**: `LOG_LEVEL=debug` (full debugging)
- **Production**: `LOG_LEVEL=info` (70% log reduction)
- **Test**: `LOG_LEVEL=warn` (minimal noise)

### **Performance Impact**

| Environment | Log Level | Volume Reduction | Benefits |
|-------------|-----------|------------------|----------|
| Development | `debug` | 0% (baseline) | Full debugging info |
| Development | `info` | ~50% | Reduced console noise |
| Production | `info` | ~70% | Optimized performance |
| Production | `warn` | ~85% | Minimal overhead |

### **Log Format Examples**

#### Development (LOG_LEVEL=debug):
```
[2025-07-24T12:00:00.000Z] DEBUG [database] Client acquired from pool
[2025-07-24T12:00:00.001Z] INFO [http] [req:abc123] GET /api/properties 200 - 150ms
[2025-07-24T12:00:00.002Z] DEBUG [cache] Cache HIT for key: search:mumbai:2025-07-24
```

#### Production (LOG_LEVEL=info):
```
[2025-07-24T12:00:00.001Z] INFO [http] [req:abc123] GET /api/properties 200 - 150ms
[2025-07-24T12:00:00.045Z] WARN [payment] Payment verification timeout for order 12345
[2025-07-24T12:00:00.046Z] ERROR [database] Connection pool exhausted
```

## 🛠️ **Development Setup**

### **1. Copy Example Files**
```bash
# Copy and customize for your environment
cp .env.development.example .env.development
cp .env.production.example .env.production
```

### **2. Configure Development**
Edit `.env.development`:
```env
NODE_ENV=development
DATABASE_URL=postgresql://farmhouse_user:farmhouse_password@localhost:5432/farmhouse_rental
LOG_LEVEL=debug    # Full debugging (can change to 'info' to reduce noise)
# Add your development configuration...
```

### **3. Configure Production**
Edit `.env.production`:
```env
NODE_ENV=production
DATABASE_URL=your-production-database-url
JWT_SECRET=your-secure-jwt-secret
LOG_LEVEL=info     # Optimized for production (70% log reduction)
# Add your production configuration...
```

### **4. Configure Testing (Optional)**
Edit `.env.test`:
```env
NODE_ENV=test
DATABASE_URL=postgresql://farmhouse_user:farmhouse_password@localhost:5433/farmhouse_rental_test
LOG_LEVEL=warn     # Minimal test output (warnings and errors only)
# Add your test configuration...
```

### **5. Start Application**
```bash
# Development
npm run dev      # NODE_ENV=development

# Production  
npm run start    # NODE_ENV=production

# Testing
npm test         # NODE_ENV=test
```

## 🔍 **Verification**

The application will show environment loading details on startup:

```bash
🔧 Loading environment configuration for: production
  📄 Loading: .env.production
  ✅ Loaded: .env.production
  ⏭️ Skipped: .env.local (file not found)
  📄 Loading: .env
  ✅ Loaded: .env
  🎯 Final configuration: NODE_ENV=production
```

## 🚨 **Troubleshooting**

### **Issue: Environment file not loading**
- Check file exists: `.env.development`, `.env.production`, or `.env.test`
- Verify `NODE_ENV` variable is set correctly
- Check file permissions (readable)

### **Issue: Wrong environment detected**
- Set `NODE_ENV` explicitly: `NODE_ENV=production npm run start`
- Verify environment file contains correct `NODE_ENV` value
- Check for conflicting environment variable settings

### **Issue: Missing environment variables**
- Check the application startup logs for missing variables
- Ensure all required variables are in your environment file
- Use `.env.example` as a reference for required variables

### **Issue: Too many/too few logs**
- **Too verbose**: Set `LOG_LEVEL=info` or `LOG_LEVEL=warn`
- **Missing debug info**: Set `LOG_LEVEL=debug`
- **Check current level**: Logs show configured level on startup
- **Restart required**: Changes take effect after server restart

### **Issue: Logs missing structure/request IDs**
- Ensure using NODE_ENV-based environment files
- Check for [req:xxxxx] format in HTTP logs
- Verify structured logging is enabled (should show JSON metadata)
- Update from legacy console.log statements if needed

## 🔄 **Migration from Old System**

If you have an existing `.env` file:
1. **Backup** your current `.env` file
2. **Copy** to `.env.development` and `.env.production`
3. **Customize** each file for its respective environment
4. **Test** with `npm run dev` and `npm run start`

This new system provides better separation between development and production configurations while maintaining backward compatibility.