# CI/CD Pipeline Documentation

Author: Fazeel Usmani  
Date: August 5, 2025

## 🚀 Overview

This project implements a comprehensive CI/CD pipeline using GitHub Actions that enforces the testing pyramid methodology with mandatory and optional test gates for production readiness.

## 🧪 Testing Pyramid Strategy

Our CI pipeline follows the inverted cone testing strategy:

- **70% Unit Tests** ✅ **Mandatory** - Must pass for merge
- **20% Integration Tests** ✅ **Mandatory** - Must pass for merge  
- **10% E2E Tests** ⚪ **Optional** - Informational, won't block merge

## 🛠️ CI Pipeline Jobs

### 1. Unit Tests (Mandatory)
- **Purpose**: Fast feedback on code logic and component behavior
- **Requirements**: Must pass for branch merge to main
- **Coverage**: Generates coverage reports uploaded to Codecov
- **Runtime**: ~2-3 minutes
- **Commands**:
  ```bash
  npm run check      # TypeScript compilation
  npm run test:unit  # Run unit tests
  npm run test:coverage # Generate coverage
  ```

### 2. Integration Tests (Mandatory)
- **Purpose**: Validate API endpoints and database interactions
- **Requirements**: Must pass for branch merge to main
- **Database**: PostgreSQL 15 service container
- **Runtime**: ~3-5 minutes
- **Commands**:
  ```bash
  npm run db:push          # Run migrations
  npm run test:integration # Run integration tests
  ```

### 3. E2E Tests (Optional)
- **Purpose**: End-to-end user workflow validation
- **Requirements**: Informational only, won't block merge
- **Browsers**: Multi-browser testing (Chrome, Firefox, Safari)
- **Database**: PostgreSQL 15 service container
- **Runtime**: ~8-12 minutes
- **Commands**:
  ```bash
  npm run test:e2e # Run E2E tests across browsers
  ```

### 4. Build Check (Mandatory)
- **Purpose**: Ensure production build succeeds
- **Requirements**: Must pass for branch merge to main
- **Artifacts**: Uploads build artifacts for deployment
- **Commands**:
  ```bash
  npm run build:frontend # Build React app
  npm run build:backend  # Build Node.js server
  ```

## 🛡️ Branch Protection Rules

### Main Branch Protection
To configure branch protection for the `main` branch in GitHub:

1. Go to **Settings** → **Branches**
2. Add rule for `main` branch
3. Enable these required status checks:
   - `🧪 Unit Tests (Mandatory)`
   - `🔗 Integration Tests (Mandatory)`
   - `🏗️ Build Check`
4. **Do NOT require**: `🌐 E2E Tests (Optional)`
5. Enable:
   - ✅ Require branches to be up to date before merging
   - ✅ Restrict pushes that create files larger than 100MB
   - ✅ Require signed commits (optional but recommended)

### Development Workflow
```bash
# 1. Create feature branch
git checkout -b feature/my-feature

# 2. Make changes and test locally
npm run test:unit
npm run test:integration

# 3. Push and create PR
git push origin feature/my-feature

# 4. CI automatically runs:
#    - Unit tests (must pass)
#    - Integration tests (must pass)
#    - Build check (must pass)
#    - E2E tests (informational)

# 5. Merge when mandatory checks pass
```

## 🌍 Environment Configuration

### CI Environment Variables
```yaml
NODE_VERSION: '18'
DATABASE_URL: 'postgresql://farmhouse_user:farmhouse_password@localhost:5433/farmhouse_rental'
JWT_SECRET: 'test-jwt-secret-key-12345'
COOKIE_SECRET: 'test-cookie-secret-key-12345'
```

### Database Setup
- **Service**: PostgreSQL 15 container
- **Port**: 5433 (mapped from 5432)
- **Health checks**: Automated with retry logic
- **Migrations**: Automatic via `drizzle-kit push`

## 📊 Test Reports & Artifacts

### Coverage Reports
- **Tool**: Codecov integration
- **Format**: LCOV format
- **Upload**: Automatic on unit test completion
- **Badge**: Available for README display

### E2E Test Artifacts
- **Screenshots**: Failure screenshots automatically captured
- **Videos**: Test execution recordings (on failure)
- **HTML Reports**: Playwright HTML reports
- **Retention**: 7 days

### Build Artifacts
- **Frontend**: Optimized React build in `dist/`
- **Backend**: Compiled TypeScript server
- **Retention**: 7 days for deployment

## 🚨 Failure Handling

### Unit Test Failures
- **Action**: ❌ Blocks merge
- **Next Steps**: Fix failing tests before merge
- **Debug**: Review test output and coverage reports

### Integration Test Failures  
- **Action**: ❌ Blocks merge
- **Next Steps**: Check database schema, API contracts
- **Debug**: Review database connection and migration logs

### E2E Test Failures
- **Action**: ⚠️ Shows warning but allows merge
- **Next Steps**: Review for UI/UX issues (can be fixed post-merge)
- **Debug**: Check screenshots and browser compatibility

### Build Failures
- **Action**: ❌ Blocks merge
- **Next Steps**: Fix TypeScript errors or build configuration
- **Debug**: Review build logs and dependency issues

## 🔧 Local Development

### Run Tests Locally
```bash
# Quick unit tests
npm run test:unit

# Integration tests (requires Docker)
docker-compose up -d  # Start PostgreSQL
npm run test:integration

# E2E tests (requires browsers)
npx playwright install --with-deps
npm run test:e2e

# Full test suite with summary
npm run test:all
```

### Database Setup for Tests
```bash
# Start test database
docker-compose -f docker-compose.dev.yml up -d

# Run migrations
npm run db:push

# Stop database
docker-compose -f docker-compose.dev.yml down
```

## 📈 Performance Optimization

### CI Pipeline Optimizations
- **Caching**: Node.js dependencies cached between runs
- **Parallel Jobs**: Unit, integration, and build run in parallel
- **Service Containers**: Database starts in parallel with checkout
- **Artifact Management**: Build outputs cached for deployment

### Test Execution Optimizations
- **Unit Tests**: Run in parallel with jsdom for speed
- **Integration Tests**: Database connections pooled and reused
- **E2E Tests**: Parallel browser execution across workers
- **Coverage**: Only generated once to avoid redundancy

## 🎯 Success Metrics

### Pipeline Health
- **Unit Test Pass Rate**: Target 100%
- **Integration Test Pass Rate**: Target 100%
- **E2E Test Pass Rate**: Target 90%+ (informational)
- **Build Success Rate**: Target 100%

### Performance Targets
- **Unit Tests**: < 3 minutes
- **Integration Tests**: < 5 minutes
- **E2E Tests**: < 15 minutes
- **Total Pipeline**: < 20 minutes

## 🔮 Future Enhancements

### Planned Improvements
- [ ] Parallel E2E test execution across multiple OS
- [ ] Visual regression testing integration
- [ ] Performance budget enforcement
- [ ] Security vulnerability scanning
- [ ] Dependency update automation
- [ ] Staging deployment automation

### Advanced Features
- [ ] Matrix testing across Node.js versions
- [ ] Cross-browser visual testing
- [ ] Load testing integration
- [ ] Mobile device testing
- [ ] Accessibility audit automation