# Razorpay Payment Integration Setup Guide

Author: Fazeel Usmani  
Date: August 5, 2025

## Prerequisites

1. **Razorpay Account**: Create an account at [https://razorpay.com](https://razorpay.com)
2. **API Keys**: Get your API keys from the Razorpay dashboard

## Environment Configuration

### Server Environment Variables

Add these variables to your server `.env` file:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Encryption Configuration
ENCRYPTION_MASTER_KEY=your_32_character_encryption_key
```

### Client Environment Variables

Add these variables to your client `.env` file:

```env
# Razorpay Configuration
VITE_RAZORPAY_KEY_ID=your_razorpay_key_id

# API Configuration
VITE_API_BASE_URL=http://localhost:3000
```

## Setting Up Razorpay

### 1. Create Razorpay Account

1. Go to [https://razorpay.com](https://razorpay.com)
2. Sign up for a new account
3. Complete the verification process
4. Activate your account

### 2. Get API Keys

1. Go to **Settings** → **API Keys**
2. Generate new API keys
3. Copy the **Key ID** and **Key Secret**
4. Add them to your environment variables

### 3. Configure Webhooks

1. Go to **Settings** → **Webhooks**
2. Create a new webhook with URL: `https://your-domain.com/api/webhooks/razorpay`
3. Select these events:
   - payment.authorized
   - payment.captured
   - payment.failed
   - order.paid
   - refund.created
   - refund.processed
4. Copy the webhook secret and add it to your environment variables

## Database Setup

### 1. Run Database Migrations

```bash
npm run db:migrate
```

### 2. Verify Payment Tables

Ensure these tables are created:
- `payment_orders`
- `payment_transactions`
- `gst_records`
- `gst_rate_configurations`
- `payment_audit_logs`
- `webhook_events`
- `idempotency_keys`

### 3. Initialize GST Rate Configurations

The GST calculation service will automatically create default rate configurations on first run.

## Development Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Start Development Server

```bash
npm run dev
```

### 3. Test Payment Flow

1. Navigate to a property page
2. Fill out the booking form
3. Click "Book Now"
4. Complete payment using Razorpay test credentials

## Test Cards for Development

Use these test cards for development:

```
Card Number: 4111 1111 1111 1111
Expiry: 12/25
CVV: 123
Name: Test User

Card Number: 5555 5555 5555 4444
Expiry: 12/25
CVV: 123
Name: Test User
```

## Production Deployment

### 1. Environment Variables

Ensure all production environment variables are set:

```env
NODE_ENV=production
RAZORPAY_KEY_ID=your_live_razorpay_key_id
RAZORPAY_KEY_SECRET=your_live_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_live_webhook_secret
ENCRYPTION_MASTER_KEY=your_secure_32_character_key
```

### 2. Database Backup

Always backup your database before deployment:

```bash
pg_dump your_database > backup.sql
```

### 3. SSL Certificate

Ensure your domain has a valid SSL certificate for secure payments.

### 4. Webhook URL

Update your webhook URL in Razorpay dashboard to your production domain.

## Security Considerations

### 1. Environment Variables

- Never commit environment variables to version control
- Use different keys for development and production
- Rotate keys regularly

### 2. Database Security

- Use strong database passwords
- Enable SSL connections
- Regularly backup data
- Monitor for unusual activity

### 3. API Security

- Enable rate limiting
- Use HTTPS only
- Validate all inputs
- Log all transactions

## Monitoring and Logs

### 1. Payment Logs

Monitor these log files:
- Payment transactions
- Webhook events
- Security incidents
- GST calculations

### 2. Alerts

Set up alerts for:
- Payment failures
- Webhook failures
- Security violations
- Database errors

### 3. Health Checks

Regular health checks:
- Database connectivity
- Memory cache status
- Razorpay API status
- Application uptime

## Troubleshooting

### Common Issues

1. **Payment Signature Verification Failed**
   - Check webhook secret
   - Verify signature calculation
   - Check for special characters in keys

2. **Database Connection Error**
   - Check database credentials
   - Verify database is running
   - Check connection string

3. **GST Calculation Error**
   - Verify state codes are correct
   - Check rate configurations
   - Ensure service types are valid

### Support

For technical support:
- Check application logs
- Review Razorpay dashboard
- Contact development team
- Review this documentation

## API Endpoints

### Payment Endpoints

- `POST /api/payments/create-order` - Create payment order
- `POST /api/payments/verify` - Verify payment signature
- `POST /api/payments/capture` - Capture payment
- `POST /api/payments/refund` - Process refund
- `GET /api/payments/order/:id` - Get payment order
- `GET /api/payments/transaction/:id` - Get payment transaction
- `GET /api/payments/gst/:bookingId` - Get GST record

### Webhook Endpoints

- `POST /api/webhooks/razorpay` - Handle Razorpay webhooks
- `GET /api/webhooks/status` - Webhook service status

## Testing

### Unit Tests

```bash
npm run test:unit
```

### Integration Tests

```bash
npm run test:integration
```

### End-to-End Tests

```bash
npm run test:e2e
```

---

**Note**: This setup guide assumes you have a basic understanding of Node.js, Express, and PostgreSQL. For additional help, refer to the [Razorpay documentation](https://razorpay.com/docs/).