# Farmhouse Rental Platform - Docker Setup Guide

Author: Fazeel Usmani  
Date: August 5, 2025

## 🏗️ Architecture Overview

This project uses Docker Compose to orchestrate a multi-container setup with the following services:

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Compose Setup                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   PostgreSQL    │  │   Farmhouse     │  │   Adminer   │  │
│  │   Database      │  │   Application   │  │   (Optional)│  │
│  │                 │  │                 │  │             │  │
│  │ Port: 5432      │  │ Port: 3001      │  │ Port: 8080  │  │
│  │ Container:      │  │ Container:      │  │ Container:  │  │
│  │ farmhouse-db    │  │ farmhouse-app   │  │ farmhouse-  │  │
│  │                 │  │                 │  │ adminer     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│           │                     │                   │       │
│           └─────────────────────┼───────────────────┘       │
│                                 │                           │
│                    ┌─────────────────────────────┐          │
│                    │     Host Machine            │          │
│                    │  localhost:3001 → App       │          │
│                    │  localhost:5432 → Database  │          │
│                    │  localhost:8080 → Adminer   │          │
│                    └─────────────────────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start - How to Run the App

### Prerequisites
- Docker Desktop installed and running
- Git (to clone the repository)

### Step-by-Step Instructions

1. **<PERSON><PERSON> and Navigate to Project**
   ```bash
   cd /path/to/your/Farmhouse
   ```

2. **Start All Services**
   ```bash
   docker-compose up -d
   ```

3. **Access the Application**
   - **Frontend (React)**: http://localhost:3001
   - **Backend API**: http://localhost:3001/api/* 
   - **Database Admin**: http://localhost:8080 (optional)
   - **Direct Database**: localhost:5432

4. **Stop All Services**
   ```bash
   docker-compose down
   ```

## 🏭 Multi-Container Architecture

### 1. Database Service (`farmhouse-db`)
```yaml
database:
  image: postgres:15-alpine
  container_name: farmhouse-db
  ports: ["5432:5432"]
  environment:
    POSTGRES_DB: farmhouse_rental
    POSTGRES_USER: farmhouse_user
    POSTGRES_PASSWORD: farmhouse_password
```

**Purpose**: Stores all application data (users, properties, bookings, reviews)
**Technology**: PostgreSQL 15 with Alpine Linux
**Data Persistence**: Uses Docker volume `farmhouse_db_data`
**Health Check**: Ensures database is ready before starting app

### 2. Application Service (`farmhouse-app`)

#### Multi-Stage Docker Build Process:

**Stage 1: Frontend Builder**
```dockerfile
FROM node:18-alpine AS frontend-builder
# Build React/TypeScript frontend
COPY client/ ./client/
RUN npm run build:frontend  # Creates dist/public/
```

**Stage 2: Backend Runtime**
```dockerfile
FROM node:18-alpine AS backend
# Copy backend code + built frontend
COPY server/ ./server/
COPY --from=frontend-builder /app/dist/public ./dist/public
COPY public/ ./public/  # Static assets & uploads
CMD ["npx", "tsx", "server/index.ts"]
```

**How Frontend & Backend Are Combined:**
1. **Build Time**: Vite builds React app → static files in `dist/public/`
2. **Runtime**: Express.js serves static files + API routes
3. **Production Mode**: Express serves pre-built frontend from `dist/public/`
4. **Development Mode**: Would use Vite dev server (not used in Docker)

### 3. Database Admin Service (`farmhouse-adminer`)
```yaml
adminer:
  image: adminer:latest
  ports: ["8080:8080"]
```

**Purpose**: Web-based database management interface
**Access**: http://localhost:8080

**Login Credentials**: 
| Field | Value |
|-------|-------|
| **System** | PostgreSQL |
| **Server** | `database` ← (Important: Use container name) |
| **Username** | `farmhouse_user` |
| **Password** | `farmhouse_password` |
| **Database** | `farmhouse_rental` |

> ⚠️ **Important**: Use `database` as server name, NOT `localhost`!

## 📁 Project Structure & Docker Integration

```
Farmhouse/
├── client/                 # React frontend source
│   ├── src/
│   ├── index.html
│   └── package.json
├── server/                 # Express.js backend source
│   ├── index.ts
│   ├── routes.ts
│   ├── db.ts
│   └── vite.ts
├── public/                 # Static assets
│   └── uploads/           # Property images
├── shared/                # Shared TypeScript schemas
│   └── schema.ts
├── migrations/            # Database migrations
├── Dockerfile            # Multi-stage build definition
├── docker-compose.yml    # Service orchestration
└── DOCKER_SETUP.md       # This documentation
```

## 🔧 Key Configuration Details

### Environment Variables (docker-compose.yml)
```yaml
environment:
  # Database Connection
  DATABASE_URL: ************************************************************/farmhouse_rental
  
  # Application Config
  NODE_ENV: production
  JWT_SECRET: dev-jwt-secret-key-change-in-production
  COOKIE_SECRET: dev-cookie-secret-key-change-in-production
```

### Port Mappings
- `3001:5000` - App (external:internal)
- `5432:5432` - Database (external:internal)  
- `8080:8080` - Adminer (external:internal)

### Volume Mounts
```yaml
volumes:
  - ./uploads:/app/uploads              # File uploads persistence
  - farmhouse_db_data:/var/lib/postgresql/data  # Database persistence
```

## 🗄️ Database Setup & Migrations

### Automatic Database Initialization
1. **Container Start**: PostgreSQL creates empty database
2. **Schema Application**: Drizzle ORM applies schema from `shared/schema.ts`
3. **Data Population**: Sample properties data inserted

### Manual Database Operations
```bash
# Run migrations manually
docker-compose exec app npx drizzle-kit push

# Access database directly
docker-compose exec database psql -U farmhouse_user -d farmhouse_rental

# Check database contents
docker-compose exec database psql -U farmhouse_user -d farmhouse_rental -c "SELECT COUNT(*) FROM properties;"
```

## 🛠️ Development Commands

### Build & Deploy
```bash
# Rebuild application (after code changes)
docker-compose build app

# Restart specific service
docker-compose restart app

# View logs
docker-compose logs app --tail=50

# View all container status
docker-compose ps
```

### Debugging
```bash
# Access application container shell
docker-compose exec app sh

# Access database container shell
docker-compose exec database psql -U farmhouse_user -d farmhouse_rental

# Check container resource usage
docker stats
```

## 🔍 How the System Works

### Single Container Architecture
The `farmhouse-app` container runs **both frontend and backend** on port 3001:

```
┌─────────────────────────────────────────────┐
│         farmhouse-app (localhost:3001)      │
├─────────────────────────────────────────────┤
│  📱 Frontend (React)                        │
│     GET localhost:3001/        → index.html │
│     GET localhost:3001/login   → index.html │  
│     GET localhost:3001/properties → index.html │
│                                             │
│  🔌 Backend API (Express.js)                │
│     GET localhost:3001/api/properties       │
│     POST localhost:3001/api/auth/login      │
│     POST localhost:3001/api/auth/register   │
└─────────────────────────────────────────────┘
```

### Request Flow
1. **User visits http://localhost:3001** → Frontend (React app)
2. **User clicks/interacts** → Frontend makes API calls to `/api/*`
3. **Same Express.js server** handles both static files and API routes
4. **API routes** connect to PostgreSQL via Drizzle ORM
5. **Frontend receives data** and updates the UI

### Static File Serving
- **Images**: `/uploads/` → served from `public/uploads/`
- **React Assets**: `/assets/` → served from `dist/public/assets/`
- **HTML**: All other routes → `dist/public/index.html` (SPA routing)

### Database Schema Management
- **Schema Definition**: `shared/schema.ts` (Drizzle ORM)
- **Migration Tool**: `drizzle-kit`
- **Connection**: Node.js pg driver → PostgreSQL

## 🚨 Troubleshooting

### Common Issues & Solutions

**1. Port Already in Use**
```bash
# Check what's using the port
lsof -i :3001
# Kill process or change port in docker-compose.yml
```

**2. Database Connection Failed**
```bash
# Ensure database is healthy
docker-compose ps
# Check database logs
docker-compose logs database
```

**3. Images Not Loading**
```bash
# Ensure public directory is copied
docker-compose exec app ls -la /app/public/uploads/
# Rebuild if needed
docker-compose build app
```

**4. CORS Errors**
- Ensure `http://localhost:3001` is in CORS allowed origins
- Check `server/routes.ts` CORS configuration

**5. Adminer "Connection Refused"**
```bash
# ❌ Wrong server name in Adminer login
Server: localhost (WRONG)

# ✅ Correct server name in Adminer login  
Server: database (CORRECT)
```
- Always use Docker service names for inter-container communication
- Check containers can communicate: `docker-compose exec adminer ping database`

## 📝 Production Considerations

### Security Updates Needed for Production
1. Change JWT_SECRET and COOKIE_SECRET
2. Update database credentials
3. Configure proper CORS origins
4. Enable HTTPS
5. Set up proper logging
6. Configure backup strategy

### Performance Optimizations
1. Add Redis for session storage
2. Configure database connection pooling
3. Set up CDN for static assets
4. Enable gzip compression
5. Add monitoring (health checks)

---

## 💡 Summary

This Docker Compose setup provides:
- ✅ **Isolated Services**: Database, application, and admin tools in separate containers
- ✅ **Multi-Stage Build**: Efficient frontend compilation + backend deployment
- ✅ **Data Persistence**: PostgreSQL data survives container restarts
- ✅ **Development Friendly**: Easy to start, stop, and debug
- ✅ **Production Ready**: Static file serving, proper environment configuration

**Single Command Deployment**: `docker-compose up -d` gives you a fully functional farmhouse rental platform!