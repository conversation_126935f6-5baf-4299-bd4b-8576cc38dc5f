# Development Guide - BookAFarm Platform

Author: <PERSON>azeel Usmani  
Date: August 5, 2025

## Development Authentication System

### Overview
The platform includes a development-friendly authentication system that eliminates SMS costs and rate limiting during development while maintaining production security.

### Test Phone Numbers
Use these pre-configured test numbers for development:
- `+************` - Generic test user
- `+************` - Test user 2
- `+************` - Test property owner

### Master Code
- **Development Code**: `999999`
- **Usage**: Works for all test phone numbers in development mode
- **Cost**: No SMS charges incurred for test numbers
- **Logging**: Console shows when master code is used

### Authentication Flow

#### 1. Send OTP Request
```bash
curl -X POST http://localhost:5000/api/auth/otp/send-otp \
  -H "Content-Type: application/json" \
  -d '{"identifier": "+************", "type": "sms"}'
```

**Response (Development):**
```json
{
  "success": true,
  "message": "Development OTP sent to your phone. Use code: 999999",
  "identifier": "+************"
}
```

#### 2. Verify OTP
```bash
curl -X POST http://localhost:5000/api/auth/otp/verify-otp-login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "+************", "code": "999999", "type": "sms"}'
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": 4,
    "email": "<EMAIL>",
    "phone": "+************",
    "fullName": "Test User One",
    "role": "user"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Rate Limiting

| Environment | OTP Send Limit | OTP Verify Limit | Window |
|-------------|----------------|------------------|---------|
| Development | 50 requests | 100 attempts | 1 minute |
| Production | 5 requests | 10 attempts | 10/5 minutes |

### Pre-created Test Users

#### Test User 1
- **Phone**: `+************`
- **Email**: `<EMAIL>`
- **Full Name**: Test User One
- **Username**: testuser1
- **Role**: user
- **Password**: TestPass123!

#### Test User 2
- **Phone**: `+************`
- **Email**: `<EMAIL>`
- **Role**: user

#### Test Owner
- **Phone**: `+************`
- **Email**: `<EMAIL>`
- **Role**: owner

### Frontend Integration

The master code system works seamlessly with the existing frontend authentication components:

1. **SwiggyStyleAuthModal**: Supports both regular and test number flows
2. **OTPAuthModal**: Handles master code verification automatically
3. **UnifiedAuthModal**: Compatible with development mode

### Development Endpoints

#### Authentication Info
```bash
GET /api/dev/auth-info
```
Returns current development authentication configuration.

#### Create Test Users
```bash
POST /api/dev/create-test-users
```
Automatically creates test users if they don't exist.

### Environment Detection

The system automatically detects development vs production environment:

```javascript
// Automatic detection based on NODE_ENV
const isDevelopment = process.env.NODE_ENV === 'development';

// Test number validation
const isTestNumber = ['+************', '+************', '+************']
  .includes(phoneNumber);

// Master code usage
if (isDevelopment && isTestNumber) {
  useCode = '999999'; // No SMS sent
} else {
  useCode = generateRandomOTP(); // Real SMS in production
}
```

### Console Logging

Development mode provides detailed logging:

```
🧪 Development check: identifier=+************, type=sms, isTest=true, isDev=true
🧪 Development Mode: Test number +************ using master code: 999999
✅ OTP verification successful (local)
```

### Structured Logging System

The platform uses a comprehensive structured logging system optimized for development and production environments.

#### Log Levels by Environment:
- **Development**: `DEBUG` level - Shows all logs including detailed debugging information
- **Production**: `INFO` level - Shows INFO, WARN, ERROR logs only
- **Test**: `WARN` level - Shows WARN, ERROR logs only

#### Structured Log Format:
```
[2025-07-24T12:00:00.000Z] INFO [http] [req:abc123] GET /api/properties 200 - 150ms
Metadata: {
  "method": "GET",
  "url": "/api/properties", 
  "statusCode": 200,
  "responseTime": 150,
  "requestId": "abc123"
}
```

#### Log Components:
- **Timestamp**: ISO 8601 format with milliseconds
- **Level**: DEBUG/INFO/WARN/ERROR with color coding
- **Component**: Service identifier (http, database, cache, etc.)
- **Message**: Human-readable log message
- **Metadata**: Structured JSON data for analysis

#### Environment Variable Configuration:
```env
# .env.development
LOG_LEVEL=debug    # Show all logs including DEBUG

# Set to reduce noise in development
LOG_LEVEL=info     # Hide DEBUG logs even in development
```

#### Development vs Production Logging:

**Development Features**:
- Color-coded console output
- Detailed debugging information
- Service initialization logs
- API documentation generation logs
- Configuration hot-reload logs

**Production Optimizations**:
- Minimal log volume (70% reduction)
- No development-specific logs
- Structured JSON format for log aggregation
- Error tracking with request correlation
- Performance-optimized log formatting

### Security Considerations

1. **Environment Isolation**: Master code only works in development mode
2. **Number Validation**: Only pre-defined test numbers use master code
3. **Production Safety**: Real SMS and strict rate limits in production
4. **Logging**: Clear indicators when development mode is active

### Image Proxy System

The platform includes an image proxy to handle external images that may be blocked:

#### Usage
Images are automatically routed through the proxy when needed:
```javascript
// Automatic proxy routing for blocked domains
const proxiedUrl = getProxiedImageUrl(originalImageUrl);
// /api/proxy-image?url=https://images.unsplash.com/photo-...
```

#### Supported Domains
- images.unsplash.com
- unsplash.com
- res.cloudinary.com

### Quick Setup for New Developers

1. **Clone and install**: Standard npm setup
2. **Environment**: Ensure NODE_ENV=development
3. **Test login**: Use `+************` with code `999999`
4. **No additional setup**: Master code system works immediately

### Troubleshooting

#### Issue: Still receiving SMS for test numbers
- **Check**: Ensure NODE_ENV=development
- **Verify**: Test number matches exactly (including +91 prefix)
- **Console**: Look for "Development Mode" log messages

#### Issue: Rate limiting in development
- **Solution**: Development allows 50 requests/minute
- **Check**: Verify environment detection in console logs

#### Issue: Images not loading
- **Solution**: Image proxy automatically handles blocked external images
- **Check**: Network tab should show /api/proxy-image requests returning 200

#### Issue: Too many/too few logs in console
- **Solution**: Adjust LOG_LEVEL environment variable
- **Debug level**: `LOG_LEVEL=debug` (shows all logs)
- **Info level**: `LOG_LEVEL=info` (reduces noise)
- **Check**: Restart server after changing LOG_LEVEL

#### Issue: Missing request IDs in logs
- **Check**: Ensure structured logging is enabled
- **Verify**: Look for [req:xxxxx] in log messages
- **Solution**: Request IDs are auto-generated for all HTTP requests

#### Issue: Production logs too verbose
- **Check**: Ensure NODE_ENV=production
- **Verify**: LOG_LEVEL should be 'info' or higher in production
- **Solution**: Development-specific logs are automatically filtered out

### Best Practices

1. **Use test numbers**: Always use predefined test numbers for development
2. **Monitor console**: Development logging provides useful debugging info
3. **Environment variables**: Ensure proper NODE_ENV setting
4. **Test real flow**: Occasionally test with real numbers in staging
5. **Rate limit testing**: Use development mode for extensive testing
6. **Log level management**: Use `LOG_LEVEL=info` to reduce noise when needed
7. **Request tracking**: Use request IDs in logs for debugging user issues
8. **Structured debugging**: Leverage metadata in logs for faster troubleshooting
9. **Production readiness**: Test with production log levels before deployment

This development system ensures smooth development experience while maintaining production security and reliability.