# Database Optimization Guide

Author: Fazeel Usmani  
Date: July 28, 2025

## Overview

This document outlines the optimizations implemented to reduce PostgreSQL compute usage and costs.

## Key Changes Made

### 1. Health Check Frequency Reduction
- **Before**: Health checks every 60 seconds (1 minute)
- **After**: Health checks every 300 seconds (5 minutes)
- **Impact**: 80% reduction in health check database queries

### 2. Connection Pool Optimization
- **Max Connections**: Reduced from 8 to 3 in production, 5 to 2 in development
- **Idle Timeout**: Reduced from 5 minutes to 1 minute
- **Keep-Alive**: Disabled to prevent persistent connections
- **Impact**: Faster connection cleanup, reduced idle compute time

### 3. Production Logging Optimization
- **Connection Events**: Only logged in development mode
- **Health Check Success**: Only logged in development or if slow (>1000ms)
- **Impact**: Reduced logging overhead and I/O operations

### 4. Performance Check Optimization
- **Production Mode**: Database performance checks are skipped by default
- **Development Mode**: Performance checks still run for debugging
- **Override**: Can be enabled in production with `ENABLE_DB_PERFORMANCE_CHECK=true`
- **Impact**: Eliminated unnecessary COUNT(*) queries in production

### 5. Quick Health Check Caching
- **Production Mode**: Uses cached health check results for up to 5 minutes
- **Development Mode**: Always performs fresh database checks
- **Impact**: Reduced database queries for load balancer health checks

## Expected Compute Reduction

Based on the optimizations:
- **Health Checks**: 80% reduction (from every minute to every 5 minutes)
- **Performance Checks**: 100% reduction in production (eliminated)
- **Connection Overhead**: 60% reduction (fewer max connections)
- **Idle Time**: 80% reduction (1 minute vs 5 minute idle timeout)

**Total Expected Reduction**: 60-70% of current database compute usage

## Environment Variables

### Production Optimization
```bash
NODE_ENV=production
DB_MAX_CONNECTIONS=3  # Optional: override default (3)
```

### Development/Debugging
```bash
NODE_ENV=development
ENABLE_DB_PERFORMANCE_CHECK=true  # Optional: enable in production
```

## Monitoring

Monitor your database compute usage after deployment. You should see:
- Significant reduction in compute hours
- Faster connection pool cleanup
- Reduced number of active connections
- Lower overall resource utilization

## Rollback Instructions

If issues arise, you can revert these optimizations by:

1. Increasing health check frequency:
   ```typescript
   healthCheckIntervalMs: 60000, // Back to 1 minute
   ```

2. Restoring connection pool limits:
   ```typescript
   const defaultMax = isProduction ? 8 : 5; // Original values
   ```

3. Re-enabling keep-alive:
   ```typescript
   keepAlive: true,
   ```

4. Restoring performance checks:
   ```bash
   ENABLE_DB_PERFORMANCE_CHECK=true
   ```

## Cost Impact

With the previous usage of 7.85 hours per day at $0.16/hour = $1.26/day:
- **Expected savings**: 60-70% reduction
- **New estimated cost**: $0.38-$0.50 per day
- **Monthly savings**: ~$23-$27 per month

These optimizations maintain application functionality while significantly reducing database compute costs.