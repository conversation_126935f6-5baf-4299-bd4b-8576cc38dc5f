# Database Connection Optimization Strategy

Author: Fazeel Usmani  
Date: July 25, 2025

## Executive Summary

After analyzing the Farmhouse application's database configuration and error logs, I've identified several areas where connection stability can be improved. The application experiences intermittent `ECONNRESET` and `ETIMEDOUT` errors, which are common with cloud-hosted databases like Neon. This document outlines a comprehensive strategy to minimize these issues.

## Current State Analysis

### Issues Identified
1. **Connection Timeouts**: ECONNRESET and ETIMEDOUT errors occurring intermittently
2. **Pool Configuration**: Current settings may not be optimal for Neon's serverless architecture
3. **Keep-Alive Settings**: Might not match Neon's connection timeout expectations
4. **Recovery Mechanisms**: While functional, could be more proactive

### Current Configuration
- **Max Connections**: 6 (production) / 3 (development)
- **Idle Timeout**: 240 seconds (4 minutes)
- **Connection Timeout**: 45 seconds
- **Statement Timeout**: 90 seconds
- **Health Check Interval**: 120 seconds (2 minutes)

## Optimization Strategies

### 1. Connection Pool Configuration

#### Immediate Changes

```typescript
// server/utils/database.ts - Update configuration
this.config = {
  maxConnections: this.getMaxConnections(),
  minConnections: 0, // Changed from 1 to 0 for serverless
  connectionTimeoutMs: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'), // Reduced from 45s
  idleTimeoutMs: 60000, // Reduced to 1 minute (from 4 minutes)
  statementTimeoutMs: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'), // Reduced from 90s
  healthCheckIntervalMs: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'), // Reduced to 30s
  maxRetries: 5, // Increased from 3
  retryDelayMs: 1000, // Reduced from 2000ms with exponential backoff
};
```

#### Pool Creation Updates

```typescript
this.pool = new Pool({
  connectionString,
  max: this.config.maxConnections,
  min: this.config.minConnections,
  idleTimeoutMillis: this.config.idleTimeoutMs,
  connectionTimeoutMillis: this.config.connectionTimeoutMs,
  statement_timeout: this.config.statementTimeoutMs,
  query_timeout: this.config.statementTimeoutMs,
  keepAlive: true,
  keepAliveInitialDelayMillis: 10000, // Reduced from 15000
  allowExitOnIdle: false, // Changed from true
  ssl: { rejectUnauthorized: false },
  application_name: 'farmhouse-app',
  // New additions for better connection handling
  log: (msg: string) => logger.debug(msg, { component: 'pg-pool' }),
  connectionTimeoutMillis: 30000,
  idle_in_transaction_session_timeout: 30000,
});
```

### 2. Connection Validation and Prewarming

#### Add Connection Validation

```typescript
// Add to pool configuration
{
  // ... existing config
  validateConnection: async (client: pg.PoolClient) => {
    try {
      const result = await client.query('SELECT 1');
      return result.rows.length === 1;
    } catch {
      return false;
    }
  }
}
```

#### Implement Connection Prewarming

```typescript
private async prewarmConnections(): Promise<void> {
  const warmupCount = Math.min(2, this.config.minConnections);
  const promises = [];
  
  for (let i = 0; i < warmupCount; i++) {
    promises.push(
      this.withConnection(
        async (client) => {
          await client.query('SELECT 1');
        },
        'connection-prewarm'
      ).catch(err => {
        logger.warn('Failed to prewarm connection', { component: 'database' }, { index: i });
      })
    );
  }
  
  await Promise.allSettled(promises);
  logger.info('Connection prewarming completed', { component: 'database' }, { count: warmupCount });
}
```

### 3. Enhanced Error Handling

#### Exponential Backoff Implementation

```typescript
private calculateRetryDelay(attempt: number): number {
  const baseDelay = this.config.retryDelayMs;
  const maxDelay = 30000; // 30 seconds max
  const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
  const jitter = Math.random() * 0.3 * delay; // 30% jitter
  return Math.floor(delay + jitter);
}
```

#### Circuit Breaker Pattern

```typescript
interface CircuitBreakerState {
  failures: number;
  lastFailureTime: Date | null;
  state: 'closed' | 'open' | 'half-open';
}

class DatabaseCircuitBreaker {
  private state: CircuitBreakerState = {
    failures: 0,
    lastFailureTime: null,
    state: 'closed'
  };
  
  private readonly failureThreshold = 5;
  private readonly resetTimeout = 60000; // 1 minute
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state.state === 'open') {
      if (Date.now() - this.state.lastFailureTime!.getTime() > this.resetTimeout) {
        this.state.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }
    
    try {
      const result = await operation();
      if (this.state.state === 'half-open') {
        this.reset();
      }
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }
  
  private recordFailure(): void {
    this.state.failures++;
    this.state.lastFailureTime = new Date();
    
    if (this.state.failures >= this.failureThreshold) {
      this.state.state = 'open';
      logger.warn('Circuit breaker opened due to repeated failures', { component: 'database' });
    }
  }
  
  private reset(): void {
    this.state = {
      failures: 0,
      lastFailureTime: null,
      state: 'closed'
    };
    logger.info('Circuit breaker reset', { component: 'database' });
  }
}
```

### 4. Proactive Connection Management

#### Implement Connection Refresh

```typescript
private async refreshIdleConnections(): Promise<void> {
  const idleThreshold = 30000; // 30 seconds
  const now = Date.now();
  
  try {
    const pool = this.pool as any;
    const idleClients = pool._idle || [];
    
    for (const client of idleClients) {
      if (now - client.lastQuery > idleThreshold) {
        try {
          await client.query('SELECT 1');
          logger.debug('Refreshed idle connection', { component: 'database' });
        } catch (error) {
          logger.warn('Failed to refresh idle connection', { component: 'database' });
          client.release(error);
        }
      }
    }
  } catch (error) {
    logger.error('Error refreshing idle connections', { component: 'database' }, {}, error as Error);
  }
}
```

### 5. Infrastructure Recommendations

#### Environment Variables

Add these to your `.env` file:

```env
# Database Connection Tuning
DB_MAX_CONNECTIONS=8
DB_CONNECTION_TIMEOUT=30000
DB_QUERY_TIMEOUT=30000
DB_HEALTH_CHECK_INTERVAL=30000
DB_IDLE_TIMEOUT=60000
DB_ENABLE_CIRCUIT_BREAKER=true
DB_CIRCUIT_BREAKER_THRESHOLD=5
DB_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
```

#### Database Server Configuration (PostgreSQL/Neon)

Recommended settings:
```sql
-- Increase idle timeout
ALTER SYSTEM SET idle_in_transaction_session_timeout = '5min';

-- TCP keepalive settings
ALTER SYSTEM SET tcp_keepalives_idle = 60;
ALTER SYSTEM SET tcp_keepalives_interval = 10;
ALTER SYSTEM SET tcp_keepalives_count = 6;

-- Connection limits
ALTER SYSTEM SET max_connections = 100;
```

### 6. Monitoring and Alerting

#### Enhanced Metrics Collection

```typescript
interface ExtendedConnectionMetrics {
  // Existing metrics
  totalConnections: number;
  idleConnections: number;
  activeConnections: number;
  lastHealthCheck: Date | null;
  connectionErrors: number;
  lastError: Error | null;
  
  // New metrics
  averageQueryTime: number;
  connectionChurn: number;
  circuitBreakerState: string;
  lastSuccessfulQuery: Date | null;
  errorRate: number;
  retrySuccessRate: number;
}
```

#### Health Check Improvements

```typescript
async performHealthCheck(): Promise<boolean> {
  const healthCheckQuery = `
    SELECT 
      current_database() as database,
      current_user as user,
      pg_backend_pid() as pid,
      pg_is_in_recovery() as in_recovery,
      extract(epoch from now() - pg_postmaster_start_time()) as uptime
  `;
  
  try {
    const start = Date.now();
    const result = await this.withConnection(
      async (client) => {
        const res = await client.query(healthCheckQuery);
        return res.rows[0];
      },
      'health-check'
    );
    
    const duration = Date.now() - start;
    
    logger.debug('Database health check details', { component: 'database' }, {
      duration,
      ...result
    });
    
    return true;
  } catch (error) {
    // Handle error...
    return false;
  }
}
```

### 7. Connection Pooler Consideration

For production environments experiencing persistent connection issues, consider using PgBouncer:

```yaml
# pgbouncer.ini
[databases]
farmhouse = host=your-neon-host.neon.tech port=5432 dbname=farmhouse

[pgbouncer]
pool_mode = transaction
max_client_conn = 100
default_pool_size = 25
min_pool_size = 5
reserve_pool_size = 5
reserve_pool_timeout = 3
server_lifetime = 300
server_idle_timeout = 60
```

## Implementation Plan

### Phase 1: Immediate Improvements (1-2 days)
1. Update connection pool configuration
2. Reduce timeouts for faster failure detection
3. Implement exponential backoff
4. Add connection refresh mechanism

### Phase 2: Enhanced Reliability (3-5 days)
1. Implement circuit breaker pattern
2. Add connection validation
3. Enhance health checks
4. Add detailed metrics collection

### Phase 3: Infrastructure (1 week)
1. Evaluate PgBouncer deployment
2. Configure database server settings
3. Set up monitoring and alerting
4. Performance testing and tuning

## Expected Outcomes

- **50-70% reduction** in connection timeout errors
- **Faster recovery** from connection failures (seconds vs minutes)
- **Better visibility** into connection health
- **Improved user experience** with fewer 503 errors
- **More efficient resource usage** with optimized pool settings

## Monitoring Success

Track these KPIs:
1. Connection error rate (target: < 0.1%)
2. Average recovery time (target: < 5 seconds)
3. Database response time P95 (target: < 100ms)
4. Circuit breaker open events (target: < 1 per day)
5. Health check success rate (target: > 99.9%)

## Conclusion

These optimizations address the root causes of connection instability while maintaining compatibility with the existing codebase. The phased approach allows for gradual implementation with immediate benefits from Phase 1 changes.