# Testing Phase 1 Database Optimizations

Author: Fazeel Usmani  
Date: July 25, 2025

## Quick Start

### 1. Real-time Monitoring
Open a terminal and run the health monitor to see live connection stats:

```bash
npm run monitor:db
```

This shows:
- Connection pool usage in real-time
- Error rates and health status
- Active connections graph
- Connection metrics

### 2. Automated Test Suite
Run comprehensive tests in another terminal:

```bash
npm run test:db
```

This tests:
- Basic connectivity
- Query performance
- Connection recovery with exponential backoff
- Concurrent connection handling
- Health check mechanisms

## Manual Testing Scenarios

### Test 1: Normal Operation
1. Start your app: `npm run dev`
2. Start monitor: `npm run monitor:db`
3. Use the app normally and watch connection patterns

### Test 2: Load Testing
```bash
# Terminal 1: Start app
npm run dev

# Terminal 2: Monitor
npm run monitor:db

# Terminal 3: Generate load
for i in {1..100}; do
  curl http://localhost:5000/api/properties &
done
wait
```

Watch the monitor to see:
- Connection pool scaling
- Any waiting queue buildup
- Error rates under load

### Test 3: Connection Recovery
Simulate network issues (macOS):

```bash
# Terminal 1: Monitor
npm run monitor:db

# Terminal 2: Block database (requires sudo)
# Find your database host first
nslookup your-database.neon.tech

# Block connections to that IP
sudo pfctl -e
echo "block drop out proto tcp to <database-ip> port 5432" | sudo pfctl -f -

# Watch errors appear in monitor for 10 seconds
sleep 10

# Restore connections
sudo pfctl -d

# Watch recovery happen with exponential backoff
```

### Test 4: Timeout Behavior
Test faster timeout detection:

```bash
# Set a very short timeout
export DB_CONNECTION_TIMEOUT=5000
npm run dev

# In another terminal
npm run test:db
```

## What to Look For

### ✅ Success Indicators
1. **Connection Prewarming**: Look for "Connection prewarming completed" in logs
2. **Fast Recovery**: Errors should recover within seconds, not minutes
3. **Exponential Backoff**: Retry delays should increase: 1s, 2s, 4s, 8s, 16s
4. **Stable Pool**: Monitor should show stable connection counts under normal load
5. **Health Checks**: Should run every 30 seconds

### ⚠️ Warning Signs
1. **High Waiting Queue**: Indicates pool exhaustion
2. **Increasing Error Rate**: More than 0.1 errors/sec is concerning
3. **Failed Health Checks**: Indicates persistent connection issues
4. **Memory Growth**: Check if connections are leaking

## Analyzing Results

### Check Logs
```bash
# Look for Phase 1 specific messages
grep -E "(prewarm|refresh|exponential|retry)" logs/*.log

# Check error patterns
grep -E "(ECONNRESET|ETIMEDOUT)" logs/*.log | tail -20

# Connection lifecycle
grep -E "(connect|acquire|release|remove)" logs/*.log | tail -50
```

### Metrics to Track
1. **Before Phase 1**:
   - Note average error rate
   - Time to recover from errors
   - Connection stability

2. **After Phase 1**:
   - Should see 50%+ reduction in errors
   - Recovery time < 5 seconds
   - More stable connection pool

## Environment Variables

Test different configurations:

```bash
# More aggressive settings
export DB_CONNECTION_TIMEOUT=15000
export DB_IDLE_TIMEOUT=30000
export DB_HEALTH_CHECK_INTERVAL=15000
npm run dev

# Conservative settings
export DB_CONNECTION_TIMEOUT=60000
export DB_IDLE_TIMEOUT=120000
export DB_HEALTH_CHECK_INTERVAL=60000
npm run dev
```

## Troubleshooting

### Monitor shows all zeros
- Check if database is reachable
- Ensure DATABASE_URL is correct
- Try `npm run test:db` for detailed diagnostics

### High error rate persists
- Check network latency: `ping your-database-host`
- Review database server logs
- Consider Phase 2 implementation (circuit breaker)

### Pool exhaustion
- Increase DB_MAX_CONNECTIONS
- Check for connection leaks in app code
- Review concurrent request patterns

## Next Steps

After 24-48 hours of monitoring:
1. Export metrics/logs
2. Analyze error patterns
3. Fine-tune environment variables
4. Document optimal settings
5. Plan Phase 2 implementation if needed