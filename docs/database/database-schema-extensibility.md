# Database Schema Design for Extensibility

Author: <PERSON><PERSON>eel Usmani  
Date: July 21, 2025

## Overview
This document outlines the extensible database schema design that supports the modular Owner Dashboard and plugin-based architecture. The schema is designed for scalability, performance, and future enhancement capability.

## Core Design Principles

### 1. Schema Versioning and Migrations
```javascript
// migrations/001_base_schema.js
const mongoose = require('mongoose');

const migrationSchema = new mongoose.Schema({
  version: { type: String, required: true, unique: true },
  appliedAt: { type: Date, default: Date.now },
  description: String,
  rollbackQueries: [String]
});

const Migration = mongoose.model('Migration', migrationSchema);

class MigrationRunner {
  async applyMigration(version, description, migrationFunc, rollbackFunc) {
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        await migrationFunc(session);
        
        await Migration.create([{
          version,
          description,
          rollbackQueries: rollbackFunc ? rollbackFunc.toString() : null
        }], { session });
      });
      
      console.log(`Migration ${version} applied successfully`);
    } catch (error) {
      console.error(`Migration ${version} failed:`, error);
      throw error;
    } finally {
      await session.endSession();
    }
  }
}
```

### 2. Plugin-Compatible Schema Structure

#### Base Property Schema with Extension Points
```javascript
// models/Property.js
const mongoose = require('mongoose');

// Core property schema that never changes
const corePropertySchema = {
  // Immutable core fields
  _id: mongoose.Schema.Types.ObjectId,
  title: { type: String, required: true, index: true },
  description: { type: String, required: true },
  location: { 
    address: String,
    coordinates: {
      type: { type: String, enum: ['Point'], default: 'Point' },
      coordinates: [Number] // [longitude, latitude]
    },
    city: String,
    state: String,
    country: { type: String, default: 'India' }
  },
  owner: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true,
    immutable: true // Prevent ownership transfers without explicit admin action
  },
  
  // Pricing structure - extensible for complex pricing models
  pricing: {
    basePrice: { type: Number, required: true },
    currency: { type: String, default: 'INR' },
    
    // Dynamic pricing extensions
    weekendMultiplier: { type: Number, default: 1.0 },
    seasonalRates: [{
      name: String,
      startDate: Date,
      endDate: Date,
      multiplier: Number,
      fixedPrice: Number
    }],
    
    // Future: Advanced pricing algorithms
    dynamicPricingEnabled: { type: Boolean, default: false },
    minimumPrice: Number,
    maximumPrice: Number
  },
  
  // Core amenities with extensibility
  amenities: {
    core: [{
      type: String,
      enum: ['wifi', 'parking', 'kitchen', 'ac', 'tv', 'pool', 'garden']
    }],
    
    // Plugin-provided amenities
    extended: [{
      category: String, // e.g., 'entertainment', 'safety', 'accessibility'
      name: String,
      description: String,
      icon: String,
      providedBy: String // Plugin ID that added this amenity
    }]
  },
  
  // Media management with plugin support
  media: {
    images: [{
      _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
      url: { type: String, required: true },
      cloudinaryId: String, // For cloud storage management
      alt: String,
      caption: String,
      order: { type: Number, default: 0 },
      tags: [String],
      uploadedBy: String, // Plugin or user ID
      metadata: {
        type: Map,
        of: mongoose.Schema.Types.Mixed
      }
    }],
    
    videos: [{
      _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
      url: String,
      thumbnail: String,
      duration: Number,
      type: { type: String, enum: ['tour', 'amenity', 'location'] },
      order: Number
    }],
    
    virtualTours: [{
      name: String,
      url: String,
      provider: String, // '360cities', 'matterport', etc.
      metadata: Map
    }]
  },
  
  // Availability and booking rules
  availability: {
    // Basic availability
    isActive: { type: Boolean, default: true, index: true },
    minimumStay: { type: Number, default: 1 },
    maximumStay: { type: Number, default: 30 },
    advanceBookingDays: { type: Number, default: 365 },
    
    // Time-based availability
    checkInTime: { type: String, default: '14:00' },
    checkOutTime: { type: String, default: '11:00' },
    
    // Blocked dates (for maintenance, personal use, etc.)
    blockedDates: [{
      startDate: Date,
      endDate: Date,
      reason: String,
      recurring: { // For recurring blocks like weekly maintenance
        enabled: Boolean,
        pattern: String, // 'weekly', 'monthly'
        endRecurrence: Date
      }
    }],
    
    // Custom availability rules (plugin extensible)
    customRules: [{
      ruleId: String,
      ruleName: String,
      conditions: mongoose.Schema.Types.Mixed,
      action: { type: String, enum: ['block', 'modify_price', 'require_approval'] },
      priority: { type: Number, default: 0 }
    }]
  },
  
  // Analytics and performance tracking
  analytics: {
    views: {
      total: { type: Number, default: 0, index: true },
      thisMonth: { type: Number, default: 0 },
      thisWeek: { type: Number, default: 0 },
      unique: { type: Number, default: 0 }
    },
    
    bookings: {
      total: { type: Number, default: 0, index: true },
      confirmed: { type: Number, default: 0 },
      cancelled: { type: Number, default: 0 },
      completed: { type: Number, default: 0 },
      revenue: { type: Number, default: 0, index: true }
    },
    
    ratings: {
      average: { type: Number, default: 0, index: true },
      count: { type: Number, default: 0 },
      breakdown: {
        1: { type: Number, default: 0 },
        2: { type: Number, default: 0 },
        3: { type: Number, default: 0 },
        4: { type: Number, default: 0 },
        5: { type: Number, default: 0 }
      }
    },
    
    // Performance metrics for optimization
    responseTime: { // How quickly owner responds to bookings
      average: Number, // in minutes
      lastUpdated: Date
    },
    
    conversionRate: { // Views to bookings
      rate: { type: Number, default: 0 },
      lastCalculated: Date
    }
  },
  
  // Plugin extension system
  pluginData: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  
  // Feature flags for property-specific features
  features: {
    instantBooking: { type: Boolean, default: false },
    selfCheckin: { type: Boolean, default: false },
    smartPricing: { type: Boolean, default: false },
    autoAcceptBookings: { type: Boolean, default: false },
    
    // Plugin-managed features
    pluginFeatures: {
      type: Map,
      of: Boolean,
      default: new Map()
    }
  },
  
  // Compliance and verification
  compliance: {
    licenseNumber: String,
    taxId: String,
    insurancePolicy: String,
    lastInspectionDate: Date,
    certifications: [String],
    
    // Regulatory compliance tracking
    regulations: [{
      type: String, // 'local', 'state', 'fire_safety', etc.
      status: { type: String, enum: ['compliant', 'pending', 'non_compliant'] },
      lastChecked: Date,
      expiryDate: Date,
      documents: [String] // Document URLs
    }]
  }
};

const propertySchema = new mongoose.Schema(corePropertySchema, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
  
  // Enable change tracking for audit logs
  trackRevisions: true,
  maxRevisions: 10
});

// Indexes for performance
propertySchema.index({ owner: 1, 'availability.isActive': 1, createdAt: -1 });
propertySchema.index({ 'location.coordinates': '2dsphere' });
propertySchema.index({ 'pricing.basePrice': 1, 'analytics.ratings.average': -1 });
propertySchema.index({ 'analytics.views.total': -1, 'analytics.bookings.total': -1 });

// Virtual for occupancy rate
propertySchema.virtual('occupancyRate').get(function() {
  const totalBookings = this.analytics.bookings.total || 0;
  const totalViews = this.analytics.views.total || 0;
  return totalViews > 0 ? (totalBookings / totalViews * 100) : 0;
});

// Plugin registration method
propertySchema.methods.registerPlugin = function(pluginId, data) {
  this.pluginData.set(pluginId, data);
  return this.save();
};

propertySchema.methods.getPluginData = function(pluginId) {
  return this.pluginData.get(pluginId);
};

module.exports = mongoose.model('Property', propertySchema);
```

#### Extended User Schema with Plugin Support
```javascript
// models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const userSchema = new mongoose.Schema({
  // Core authentication fields (immutable)
  email: { 
    type: String, 
    required: true, 
    unique: true, 
    lowercase: true,
    immutable: true // Email changes require special verification process
  },
  passwordHash: { 
    type: String, 
    required: true, 
    select: false // Never return in queries
  },
  emailVerified: { type: Boolean, default: false },
  phoneVerified: { type: Boolean, default: false },
  
  // Profile information
  profile: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    phone: String,
    dateOfBirth: Date,
    nationality: String,
    
    avatar: {
      url: String,
      cloudinaryId: String,
      uploadedAt: Date
    },
    
    // Address information
    addresses: [{
      type: { type: String, enum: ['home', 'business', 'billing'] },
      street: String,
      city: String,
      state: String,
      postalCode: String,
      country: String,
      isPrimary: { type: Boolean, default: false }
    }],
    
    // Social profiles for trust building
    socialProfiles: [{
      platform: { type: String, enum: ['facebook', 'instagram', 'linkedin'] },
      url: String,
      verified: { type: Boolean, default: false }
    }]
  },
  
  // Role-based access control
  role: {
    primary: { 
      type: String, 
      enum: ['guest', 'owner', 'admin'], 
      default: 'guest',
      index: true 
    },
    
    // Multi-role support for users who are both guests and owners
    secondary: [{ 
      type: String, 
      enum: ['guest', 'owner', 'admin']
    }],
    
    // Permission-based access control
    permissions: [{
      resource: String, // 'properties', 'bookings', 'users', etc.
      actions: [String], // 'create', 'read', 'update', 'delete', 'approve'
      scope: String, // 'own', 'assigned', 'all'
      conditions: mongoose.Schema.Types.Mixed // Additional conditions
    }]
  },
  
  // Owner-specific data with plugin extensibility
  ownerProfile: {
    businessInfo: {
      businessName: String,
      businessType: { type: String, enum: ['individual', 'company', 'llc'] },
      businessLicense: String,
      taxId: String,
      website: String,
      businessPhone: String,
      businessEmail: String
    },
    
    bankingDetails: {
      // Note: In production, this should be encrypted or stored via payment processor
      accountHolderName: String,
      bankName: String,
      accountNumber: String, // Should be encrypted
      routingNumber: String,
      accountType: { type: String, enum: ['checking', 'savings'] },
      isVerified: { type: Boolean, default: false }
    },
    
    preferences: {
      notifications: {
        email: {
          newBookings: { type: Boolean, default: true },
          cancellations: { type: Boolean, default: true },
          messages: { type: Boolean, default: true },
          reviews: { type: Boolean, default: true },
          marketing: { type: Boolean, default: false }
        },
        sms: {
          urgentOnly: { type: Boolean, default: true },
          newBookings: { type: Boolean, default: false },
          cancellations: { type: Boolean, default: true }
        },
        push: {
          enabled: { type: Boolean, default: true },
          quiet_hours: {
            enabled: { type: Boolean, default: false },
            start: String, // '22:00'
            end: String // '08:00'
          }
        }
      },
      
      autoAcceptBookings: { type: Boolean, default: false },
      requireApprovalFor: [{
        condition: String, // 'same_day', 'long_term', 'first_time_guest'
        enabled: Boolean
      }],
      
      responseTimeTarget: { type: Number, default: 60 } // minutes
    },
    
    statistics: {
      totalProperties: { type: Number, default: 0 },
      activeListings: { type: Number, default: 0 },
      totalBookings: { type: Number, default: 0 },
      totalRevenue: { type: Number, default: 0 },
      averageRating: { type: Number, default: 0 },
      responseRate: { type: Number, default: 0 },
      averageResponseTime: { type: Number, default: 0 }, // in minutes
      
      // Monthly statistics for trending
      monthlyStats: [{
        year: Number,
        month: Number,
        bookings: Number,
        revenue: Number,
        views: Number,
        occupancyRate: Number
      }]
    },
    
    // Subscription and payment plans
    subscription: {
      plan: { 
        type: String, 
        enum: ['free', 'basic', 'premium', 'enterprise'], 
        default: 'free' 
      },
      billingCycle: { type: String, enum: ['monthly', 'yearly'] },
      subscriptionId: String, // External payment processor subscription ID
      currentPeriodStart: Date,
      currentPeriodEnd: Date,
      isActive: { type: Boolean, default: true },
      
      // Feature access based on subscription
      features: {
        maxProperties: { type: Number, default: 1 },
        maxImages: { type: Number, default: 10 },
        advancedAnalytics: { type: Boolean, default: false },
        prioritySupport: { type: Boolean, default: false },
        customBranding: { type: Boolean, default: false },
        apiAccess: { type: Boolean, default: false }
      }
    }
  },
  
  // Guest-specific data
  guestProfile: {
    preferences: {
      amenities: [String],
      propertyTypes: [String],
      priceRange: {
        min: Number,
        max: Number,
        currency: { type: String, default: 'INR' }
      },
      locations: [String],
      groupSize: {
        typical: Number,
        maximum: Number
      }
    },
    
    emergencyContact: {
      name: String,
      relationship: String,
      phone: String,
      email: String
    },
    
    // Booking history and behavior
    bookingHistory: {
      totalBookings: { type: Number, default: 0 },
      completedBookings: { type: Number, default: 0 },
      cancelledBookings: { type: Number, default: 0 },
      noShowCount: { type: Number, default: 0 },
      averageRating: { type: Number, default: 0 }, // Rating given by owners
      
      // Behavioral insights
      averageBookingValue: Number,
      averageStayDuration: Number,
      preferredBookingAdvance: Number, // days in advance
      seasonalPatterns: mongoose.Schema.Types.Mixed
    },
    
    // Trust and safety
    verifications: [{
      type: { type: String, enum: ['email', 'phone', 'id_document', 'address'] },
      status: { type: String, enum: ['pending', 'verified', 'failed'] },
      verifiedAt: Date,
      documentId: String
    }],
    
    // Reviews and ratings given by this guest
    reviewsGiven: { type: Number, default: 0 },
    avgRatingGiven: { type: Number, default: 0 }
  },
  
  // Activity and audit trail
  activity: {
    lastLogin: Date,
    lastActiveAt: Date,
    loginCount: { type: Number, default: 0 },
    
    // Device and session tracking
    sessions: [{
      deviceId: String,
      deviceType: { type: String, enum: ['desktop', 'mobile', 'tablet'] },
      browser: String,
      os: String,
      lastActive: Date,
      ipAddress: String,
      location: {
        country: String,
        city: String,
        coordinates: [Number]
      }
    }],
    
    // Audit log for important actions
    auditLog: [{
      action: String,
      resource: String,
      resourceId: mongoose.Schema.Types.ObjectId,
      timestamp: { type: Date, default: Date.now },
      ipAddress: String,
      userAgent: String,
      details: mongoose.Schema.Types.Mixed
    }]
  },
  
  // Plugin extension system
  pluginData: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  
  // Feature flags per user
  features: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  
  // Account status and moderation
  status: {
    isActive: { type: Boolean, default: true, index: true },
    isSuspended: { type: Boolean, default: false },
    suspensionReason: String,
    suspensionEnd: Date,
    
    // Trust score (0-100)
    trustScore: { type: Number, default: 50, min: 0, max: 100 },
    
    // Account flags
    flags: [{
      type: String, // 'payment_issue', 'policy_violation', 'suspicious_activity'
      severity: { type: String, enum: ['low', 'medium', 'high', 'critical'] },
      description: String,
      flaggedAt: { type: Date, default: Date.now },
      flaggedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      resolved: { type: Boolean, default: false },
      resolvedAt: Date
    }]
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.passwordHash;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes for performance
userSchema.index({ email: 1 });
userSchema.index({ 'role.primary': 1, 'status.isActive': 1 });
userSchema.index({ 'ownerProfile.statistics.totalRevenue': -1 });
userSchema.index({ 'guestProfile.bookingHistory.totalBookings': -1 });

// Virtual for full name
userSchema.virtual('profile.fullName').get(function() {
  return `${this.profile.firstName} ${this.profile.lastName}`;
});

// Password methods
userSchema.methods.setPassword = async function(password) {
  this.passwordHash = await bcrypt.hash(password, 12);
};

userSchema.methods.checkPassword = async function(password) {
  return await bcrypt.compare(password, this.passwordHash);
};

// Plugin methods
userSchema.methods.registerPlugin = function(pluginId, data) {
  this.pluginData.set(pluginId, data);
  return this.save();
};

userSchema.methods.getPluginData = function(pluginId) {
  return this.pluginData.get(pluginId);
};

module.exports = mongoose.model('User', userSchema);
```

### 3. Plugin-Specific Collection Architecture

#### Dynamic Collections for Plugin Data
```javascript
// utils/PluginCollectionManager.js
class PluginCollectionManager {
  constructor() {
    this.pluginCollections = new Map();
  }

  // Register a new collection for a plugin
  registerPluginCollection(pluginId, collectionName, schema, options = {}) {
    const collection = {
      pluginId,
      collectionName: `plugin_${pluginId}_${collectionName}`,
      schema: this.extendSchemaWithPlugin(schema, pluginId),
      model: null,
      options
    };

    // Create Mongoose model
    collection.model = mongoose.model(collection.collectionName, collection.schema);
    
    this.pluginCollections.set(`${pluginId}:${collectionName}`, collection);
    
    return collection.model;
  }

  extendSchemaWithPlugin(schema, pluginId) {
    // Add common plugin fields to all plugin schemas
    const extendedSchema = new mongoose.Schema({
      ...schema.obj,
      
      // Plugin metadata
      _plugin: {
        id: { type: String, default: pluginId },
        version: String,
        createdAt: { type: Date, default: Date.now },
        lastModified: { type: Date, default: Date.now }
      },
      
      // Reference to core entities
      _refs: {
        userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
        propertyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Property' },
        bookingId: { type: mongoose.Schema.Types.ObjectId, ref: 'Booking' }
      },
      
      // Plugin-specific metadata
      _meta: {
        type: Map,
        of: mongoose.Schema.Types.Mixed
      }
    }, {
      timestamps: true,
      collection: `plugin_${pluginId}_${schema.options?.collection || 'data'}`
    });

    return extendedSchema;
  }

  getPluginCollection(pluginId, collectionName) {
    return this.pluginCollections.get(`${pluginId}:${collectionName}`);
  }

  // Clean up plugin data when plugin is uninstalled
  async cleanupPlugin(pluginId) {
    const collections = Array.from(this.pluginCollections.keys())
      .filter(key => key.startsWith(`${pluginId}:`));
    
    for (const key of collections) {
      const collection = this.pluginCollections.get(key);
      if (collection?.model) {
        await collection.model.collection.drop();
        this.pluginCollections.delete(key);
      }
    }
  }
}

module.exports = new PluginCollectionManager();
```

### 4. Example Plugin Schemas

#### Advanced Analytics Plugin Schema
```javascript
// plugins/advanced-analytics/models/PropertyAnalytics.js
const mongoose = require('mongoose');
const PluginCollectionManager = require('../../../utils/PluginCollectionManager');

const analyticsSchema = new mongoose.Schema({
  propertyId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Property', 
    required: true,
    index: true 
  },
  
  // Time-based analytics
  period: {
    type: { type: String, enum: ['daily', 'weekly', 'monthly'], required: true },
    year: { type: Number, required: true },
    month: Number, // For monthly/weekly
    week: Number,  // For weekly
    day: Number    // For daily
  },
  
  // Detailed metrics
  metrics: {
    views: {
      total: { type: Number, default: 0 },
      unique: { type: Number, default: 0 },
      returning: { type: Number, default: 0 },
      averageTimeOnPage: { type: Number, default: 0 }, // seconds
      bounceRate: { type: Number, default: 0 } // percentage
    },
    
    bookings: {
      inquiries: { type: Number, default: 0 },
      requests: { type: Number, default: 0 },
      confirmed: { type: Number, default: 0 },
      cancelled: { type: Number, default: 0 },
      conversionRate: { type: Number, default: 0 }
    },
    
    revenue: {
      gross: { type: Number, default: 0 },
      net: { type: Number, default: 0 },
      fees: { type: Number, default: 0 },
      refunds: { type: Number, default: 0 },
      averageBookingValue: { type: Number, default: 0 }
    },
    
    // Guest behavior insights
    guests: {
      newGuests: { type: Number, default: 0 },
      returningGuests: { type: Number, default: 0 },
      averagePartySize: { type: Number, default: 0 },
      averageStayDuration: { type: Number, default: 0 },
      leadTime: { type: Number, default: 0 } // Days between booking and stay
    },
    
    // Market comparison
    market: {
      averagePriceInArea: Number,
      competitorCount: Number,
      rankInArea: Number,
      marketShare: Number
    }
  },
  
  // Forecasting data
  predictions: {
    nextPeriodBookings: Number,
    nextPeriodRevenue: Number,
    seasonalTrends: [{
      period: String,
      predictedDemand: Number,
      suggestedPricing: Number
    }],
    confidence: { type: Number, min: 0, max: 100 }
  }
});

// Compound indexes for efficient querying
analyticsSchema.index({ propertyId: 1, 'period.type': 1, 'period.year': 1, 'period.month': 1 });
analyticsSchema.index({ 'period.year': 1, 'period.month': 1, 'metrics.revenue.gross': -1 });

// Register with plugin system
const PropertyAnalytics = PluginCollectionManager.registerPluginCollection(
  'advanced-analytics',
  'property_analytics',
  analyticsSchema
);

module.exports = PropertyAnalytics;
```

### 5. Data Relationships and Foreign Keys

#### Cross-Plugin Data References
```javascript
// models/relationships/PluginReferences.js
const mongoose = require('mongoose');

const pluginReferenceSchema = new mongoose.Schema({
  sourcePlugin: { type: String, required: true },
  targetPlugin: { type: String, required: true },
  
  sourceCollection: { type: String, required: true },
  targetCollection: { type: String, required: true },
  
  sourceField: { type: String, required: true },
  targetField: { type: String, required: true },
  
  relationship: {
    type: { type: String, enum: ['one-to-one', 'one-to-many', 'many-to-many'] },
    cascadeDelete: { type: Boolean, default: false },
    cascadeUpdate: { type: Boolean, default: false }
  },
  
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

pluginReferenceSchema.index({ sourcePlugin: 1, targetPlugin: 1 });

module.exports = mongoose.model('PluginReference', pluginReferenceSchema);
```

## Performance Optimization Strategies

### 1. Indexing Strategy
```javascript
// scripts/optimize-indexes.js
const indexStrategies = {
  // Core entity indexes
  properties: [
    { 'owner': 1, 'availability.isActive': 1, 'createdAt': -1 },
    { 'location.coordinates': '2dsphere' },
    { 'pricing.basePrice': 1, 'analytics.ratings.average': -1 },
    { 'analytics.views.total': -1 }
  ],
  
  users: [
    { 'email': 1 },
    { 'role.primary': 1, 'status.isActive': 1 },
    { 'ownerProfile.statistics.totalRevenue': -1 }
  ],
  
  bookings: [
    { 'property': 1, 'bookingDate': 1, 'status': 1 },
    { 'guest': 1, 'createdAt': -1 },
    { 'status': 1, 'createdAt': -1 }
  ]
};

// Dynamic index creation for plugin collections
async function createPluginIndexes(pluginId, indexes) {
  for (const [collection, indexList] of Object.entries(indexes)) {
    const model = PluginCollectionManager.getPluginCollection(pluginId, collection);
    
    if (model?.model) {
      for (const indexSpec of indexList) {
        await model.model.createIndexes([{ key: indexSpec }]);
      }
    }
  }
}
```

### 2. Archival and Data Lifecycle Management
```javascript
// utils/DataLifecycleManager.js
class DataLifecycleManager {
  // Archive old bookings to separate collection
  async archiveOldBookings(monthsOld = 12) {
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - monthsOld);
    
    const oldBookings = await Booking.find({
      status: 'completed',
      completedAt: { $lt: cutoffDate }
    });
    
    if (oldBookings.length > 0) {
      // Move to archive collection
      await ArchivedBooking.insertMany(oldBookings);
      
      // Remove from main collection
      await Booking.deleteMany({
        _id: { $in: oldBookings.map(b => b._id) }
      });
      
      console.log(`Archived ${oldBookings.length} old bookings`);
    }
  }
  
  // Clean up plugin data based on retention policies
  async cleanupPluginData(pluginId, retentionDays = 90) {
    const collections = PluginCollectionManager.getPluginCollections(pluginId);
    
    for (const collection of collections) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      
      await collection.model.deleteMany({
        createdAt: { $lt: cutoffDate }
      });
    }
  }
}
```

This extensible database schema provides:

1. **Plugin-Compatible Architecture**: Core schemas with extension points via Map fields and plugin collections
2. **Performance Optimization**: Strategic indexing and data lifecycle management  
3. **Audit Trail**: Complete tracking of changes and plugin interactions
4. **Scalable Relationships**: Cross-plugin references and cascade operations
5. **Migration Support**: Schema versioning for smooth upgrades

The schema supports the modular frontend architecture and ensures data consistency while allowing unlimited extensibility through plugins.