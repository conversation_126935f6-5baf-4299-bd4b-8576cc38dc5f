# WhatsApp Booking Integration Setup Guide

## 🎯 Overview

This guide explains how to set up and use the WhatsApp booking integration that allows property owners to receive bookings directly through WhatsApp, which automatically sync with BookAFarm.com in real-time.

## ✅ Features Already Implemented

### 🏗️ Backend Services
- **OwnerWhatsAppService**: Handles owner-specific booking flows
- **WhatsAppCalendarService**: Interactive calendar display in WhatsApp
- **CalendarService**: Single source of truth for all bookings
- **WebSocketService**: Real-time sync between WhatsApp and website

### 📱 WhatsApp Flow Features
- **Property listing** with prices and amenities
- **Interactive calendar** with availability display
- **Date selection** with conflict detection  
- **Guest details** collection and validation
- **Price calculation** (weekday/weekend pricing)
- **Booking confirmation** and calendar sync
- **Real-time notifications** to both customer and owner

### 🌐 Website Integration
- **Real-time calendar updates** via WebSocket
- **Conflict prevention** across all platforms
- **Owner dashboard** shows WhatsApp bookings
- **Unified booking management**

## 🚀 Setup Instructions

### 1. Twilio WhatsApp Setup

```bash
# 1. Get Twilio WhatsApp Sandbox or Business API
# 2. Configure webhook URL in Twilio Console
Webhook URL: https://your-domain.com/api/whatsapp/webhook

# 3. Set environment variables
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
WHATSAPP_NUMBER=your_twilio_whatsapp_number
WHATSAPP_VERIFY_TOKEN=your_webhook_verify_token
```

### 2. Owner WhatsApp Number Setup

```typescript
// Option A: Update via API endpoint (development)
POST /api/whatsapp/setup-test-owner
{
  "ownerId": 1,
  "whatsappNumber": "+************"
}

// Option B: Direct database update
UPDATE users 
SET whatsapp_number = '+************' 
WHERE id = 1 AND role = 'owner';
```

### 3. Test the Integration

```bash
# Run the comprehensive test script
npm run test:whatsapp-booking

# Or manually test
ts-node scripts/test-whatsapp-booking-flow.ts
```

## 📱 How It Works - Customer Experience

### Step 1: Initial Contact
```
Customer → Owner WhatsApp: "Hi, I want to book a farmhouse"

🏡 Welcome to [Owner Name]'s Farmhouse Rentals!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📋 Available Properties:

1. **Sunset Villa Farmhouse**
   📍 Lonavala, Maharashtra
   🛏️ 3 bedrooms, 2 bathrooms
   💰 Morning: ₹3,000/day
   💰 Full Day: ₹5,000/day
   ✨ Swimming Pool, Garden, Kitchen...

📝 To book:
Reply with property number (1, 2, 3...) you'd like to book
```

### Step 2: Property Selection
```
Customer: "1"

✅ Great choice! **Sunset Villa Farmhouse**

📅 Select Your Dates
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📅 **January 2025**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

```
 Mo Tu We Th Fr Sa Su
─────────────────────
      1  2  3  4  5
  6  7  8  9 10 11 12
 13 14 15❌17 18 19 20
 21 22 23 24[25]26 27
 28 29 30 31
```

✅ Available  ❌ Booked  🟡 Pending

📋 How to book:
• Reply with day number (e.g., "15")
• For range: "15-18" or "15 to 18"  
• Type "tomorrow" for next day
```

### Step 3: Date & Stay Type Selection
```
Customer: "25"

✅ Available!
Saturday, 25 January 2025 is available for booking.

⏰ Choose your stay type:

1. **Morning Visit** (9 AM - 6 PM)
2. **Full Day** (Check-in 2 PM, checkout next day)

Reply with "1" or "2"
```

### Step 4: Guest Details
```
Customer: "1"

📅 Dates confirmed:
Check-in: 2025-01-25
Stay type: Morning Visit

👥 Guest Information needed:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Please provide the following details:

📝 Format:
Name: [Your full name]
Phone: [Your contact number]
Guests: [Number of guests]

💡 Example:
Name: Rajesh Kumar
Phone: 9876543210
Guests: 8

📞 You can also add any special requests!
```

### Step 5: Confirmation
```
Customer: "Name: Rajesh Kumar
Phone: 9876543210
Guests: 8
Vegetarian meals preferred"

✅ Booking Summary
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🏡 Property: Sunset Villa Farmhouse
📅 Date: 2025-01-25
⏰ Stay: Morning Visit
👤 Guest: Rajesh Kumar
📞 Phone: 9876543210
👥 Guests: 8
📝 Special Requests: Vegetarian meals preferred

💰 Total Amount: ₹3,500
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

✅ To confirm booking, reply: "CONFIRM"
❌ To cancel, reply: "CANCEL"
✏️ To modify, reply: "CHANGE"
```

### Step 6: Booking Confirmation
```
Customer: "CONFIRM"

🎉 Booking Confirmed!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📝 Booking Reference: WA1704627234567
📅 Calendar ID: #1234
🏡 Property: Sunset Villa Farmhouse
📅 Date: 2025-01-25
👥 Guests: 8
💰 Total: ₹3,500

📞 Owner will contact you shortly for:
• Payment details
• Property directions  
• Final arrangements

✅ Your booking has been added to the owner's calendar!
```

## 🏠 Owner Experience

### Owner Notification
```
🔔 New Booking Alert!

📝 Reference: WA1704627234567
📅 Calendar Entry: #1234
🏡 Property: Sunset Villa Farmhouse
👤 Customer: Rajesh Kumar
📞 Phone: 9876543210
📅 Date: 2025-01-25
👥 Guests: 8
💰 Amount: ₹3,500
📱 Customer WhatsApp: whatsapp:+919876543210

✅ This booking has been automatically added to your calendar.
Please contact customer to confirm payment and arrangements.
```

### Website Dashboard Updates
- **Real-time calendar update** shows new booking
- **Booking appears in owner dashboard** immediately
- **Date becomes unavailable** for other bookings
- **WebSocket notification** shows booking status

## 🔧 Technical Implementation

### Key Components

#### 1. OwnerWhatsAppService (`/server/services/OwnerWhatsAppService.ts`)
- Manages complete booking flow state
- Handles owner property association
- Processes step-by-step customer interactions
- Creates calendar bookings with real-time sync

#### 2. WhatsAppCalendarService (`/server/services/WhatsAppCalendarService.ts`)  
- Generates interactive calendar views
- Handles date parsing and validation
- Checks availability and conflicts
- Provides quick date options

#### 3. CalendarService Integration
- Single source of truth for all bookings
- Conflict detection across all platforms
- WebSocket broadcasts for real-time updates
- Proper data validation and constraints

#### 4. Real-time Sync Architecture
```typescript
// Booking flow creates calendar entry
const calendarBooking = await calendarService.createCalendarBooking({
  propertyId: bookingFlow.data.propertyId!,
  startDate: bookingFlow.data.checkIn!,
  endDate: bookingFlow.data.checkOut || bookingFlow.data.checkIn!,
  guestName: bookingFlow.data.customerName!,
  guestPhone: bookingFlow.data.customerPhone!,
  guestCount: bookingFlow.data.guestCount!,
  bookingType: bookingFlow.data.stayType === 'morning' ? 'Morning Visit' : 'Full Day',
  status: 'confirmed',
  source: 'whatsapp',  // 🔑 Identifies WhatsApp origin
  externalId: bookingRef,
  notes: bookingFlow.data.specialRequests,
  createdBy: customerId
});

// Automatic WebSocket broadcast to all connected clients
webSocketService.broadcastCalendarUpdate(
  booking.propertyId,
  booking,
  validatedData.createdBy
);
```

## 🧪 Testing

### Automated Testing
```bash
# Run comprehensive flow test
npm run test:whatsapp-booking

# Test specific components
npm test -- --grep "WhatsApp"
```

### Manual Testing Checklist

#### ✅ Basic Flow
- [ ] Owner number association works
- [ ] Property list displays correctly
- [ ] Calendar shows real availability
- [ ] Date selection validates properly
- [ ] Guest details parsing works
- [ ] Price calculation is accurate
- [ ] Booking confirmation creates calendar entry

#### ✅ Real-time Sync
- [ ] Website calendar updates immediately
- [ ] Conflict detection prevents double bookings
- [ ] WebSocket notifications work
- [ ] Owner dashboard shows WhatsApp bookings

#### ✅ Edge Cases
- [ ] Invalid date formats handled gracefully
- [ ] Property ownership verification works
- [ ] Flow timeout and cleanup
- [ ] Error message handling

## 🚨 Troubleshooting

### Common Issues

#### 1. Owner Not Found
```bash
# Check owner WhatsApp number is set
SELECT id, full_name, whatsapp_number, phone 
FROM users 
WHERE role = 'owner' AND whatsapp_number IS NOT NULL;

# Update if needed
UPDATE users 
SET whatsapp_number = '+************' 
WHERE id = 1;
```

#### 2. Webhook Not Receiving Messages
```bash
# Verify webhook URL in Twilio Console
# Check ngrok/domain is accessible
# Verify WHATSAPP_VERIFY_TOKEN matches

# Test webhook manually
curl -X GET "https://your-domain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=your_token&hub.challenge=test"
```

#### 3. Calendar Not Syncing
```bash
# Check WebSocket connection
# Verify CalendarService integration
# Check database constraints

# Test calendar creation directly
npm run test:calendar
```

#### 4. Date Parsing Issues
```bash
# Check WhatsAppCalendarService date formats
# Verify timezone handling
# Test with different date inputs

# Debug date parsing
ts-node -e "
import { whatsAppCalendarService } from './server/services/WhatsAppCalendarService';
console.log(whatsAppCalendarService.parseDateSelection('25', 1, 2025));
"
```

## 📊 Analytics & Monitoring

### Key Metrics to Track
- **WhatsApp booking conversion rate**
- **Average flow completion time** 
- **Most common drop-off points**
- **Error rates by step**
- **Real-time sync success rate**

### Logging Points
```typescript
// Flow tracking
logger.info('Owner booking flow started', 'owner-whatsapp', {
  customer: customerWhatsApp,
  owner: owner.fullName,
  ownerId: owner.id
});

// Calendar sync tracking  
logger.info('Calendar booking created successfully', {
  component: 'CalendarService',
  bookingId: booking.id,
  source: 'whatsapp'
});
```

## 🔐 Security Considerations

### Input Validation
- All guest details are sanitized
- Phone numbers validated and formatted
- Date ranges checked for validity
- SQL injection prevention via Drizzle ORM

### Access Control
- Owner property association verified
- Customer phone number tracking
- Booking flow state isolation
- Webhook signature validation in production

### Data Privacy
- Customer data encrypted in transit
- Minimal data retention policy
- GDPR compliance for EU customers
- Secure token handling

## 🎯 Success Criteria

The WhatsApp booking integration is **fully implemented and working** when:

✅ **Property owners can receive bookings via WhatsApp**  
✅ **Interactive calendar displays availability in WhatsApp**  
✅ **Complete booking flow from inquiry to confirmation**  
✅ **Real-time sync with BookAFarm.com website**  
✅ **Conflict detection prevents double bookings**  
✅ **Owner notifications and customer confirmations**  
✅ **Unified calendar management across all platforms**

## 📞 Support

For technical issues or questions:
- Check logs in `/logs/` directory
- Run diagnostic script: `npm run test:whatsapp-booking`
- Review webhook delivery in Twilio Console
- Monitor WebSocket connections in browser dev tools

---

**🎉 The WhatsApp booking integration is now fully operational!** Property owners can receive bookings directly through WhatsApp, and everything syncs in real-time with the BookAFarm.com platform.