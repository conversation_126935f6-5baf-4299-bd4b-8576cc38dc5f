# Calendar Management API

Complete REST API endpoints for managing calendar bookings and sync configurations.

## Base URL
- **Version 1**: `/api/v1/calendar`
- **Legacy**: `/api/calendar`

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Rate Limits
- **Read Operations**: 60 requests/minute
- **Write Operations**: 10 requests/minute  
- **Availability Checks**: 100 requests/minute

---

## Calendar Bookings

### 📅 List Calendar Bookings
**GET** `/api/v1/calendar/bookings`

Query Parameters:
- `propertyId` (number, optional) - Filter by property ID
- `startDate` (string, optional) - Start date filter (YYYY-MM-DD)
- `endDate` (string, optional) - End date filter (YYYY-MM-DD)
- `status` (string, optional) - Comma-separated status values (confirmed,tentative,blocked,cancelled)
- `source` (string, optional) - Comma-separated source values (website,whatsapp,airbnb,etc.)
- `bookingType` (string, optional) - Comma-separated booking types (direct,external,blocked)
- `limit` (number, optional) - Max 100, default 20
- `offset` (number, optional) - Default 0

**Response**: Paginated list of calendar bookings with property and user details

### 📝 Create Calendar Booking
**POST** `/api/v1/calendar/bookings`

**Request Body**:
```json
{
  "propertyId": 1,
  "startDate": "2024-08-15",
  "endDate": "2024-08-16",
  "status": "confirmed",
  "bookingType": "direct",
  "guestName": "John Doe",
  "guestPhone": "+1234567890",
  "guestCount": 4,
  "notes": "Birthday celebration",
  "source": "website"
}
```

**Response**: Created booking details

### 🔍 Get Calendar Booking
**GET** `/api/v1/calendar/bookings/:id`

**Response**: Single booking with property and user details

### ✏️ Update Calendar Booking
**PUT** `/api/v1/calendar/bookings/:id`

**Request Body**: Partial booking data (same structure as create)

**Response**: Updated booking details

### 🗑️ Delete Calendar Booking
**DELETE** `/api/v1/calendar/bookings/:id`

**Response**: Deletion confirmation

---

## Property Calendar

### 📅 Get Property Calendar
**GET** `/api/v1/calendar/properties/:id`

Query Parameters:
- `startDate` (string, required) - Start date (YYYY-MM-DD)
- `endDate` (string, required) - End date (YYYY-MM-DD)

**Access**:
- Public: Returns basic availability info
- Authenticated: Returns detailed info if user has access

**Response**: Array of calendar bookings for the property

### ✅ Check Availability
**POST** `/api/v1/calendar/availability`

**Request Body**:
```json
{
  "propertyId": 1,
  "startDate": "2024-08-15",
  "endDate": "2024-08-16"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "isAvailable": false,
    "conflictingBookings": [...],
    "message": "2 conflicting booking(s) found"
  }
}
```

### 📊 Get Calendar Statistics
**GET** `/api/v1/calendar/stats/:propertyId`

Query Parameters:
- `year` (number, optional) - Filter by specific year

**Response**:
```json
{
  "success": true,
  "data": {
    "totalBookings": 45,
    "confirmedBookings": 40,
    "tentativeBookings": 3,
    "blockedDays": 2,
    "byMonth": {
      "2024-01": 5,
      "2024-02": 8,
      ...
    },
    "bySource": {
      "website": 25,
      "whatsapp": 15,
      "airbnb": 5
    }
  }
}
```

---

## Calendar Sync Management

### ⚙️ List Sync Configurations
**GET** `/api/v1/calendar/sync`

Query Parameters:
- `propertyId` (number, optional) - Filter by property
- `calendarType` (string, optional) - Filter by calendar type
- `isActive` (boolean, optional) - Filter by active status

**Response**: Array of sync configurations

### 🔗 Create Sync Configuration
**POST** `/api/v1/calendar/sync`

**Request Body**:
```json
{
  "propertyId": 1,
  "calendarType": "google",
  "isActive": true,
  "webhookUrl": "https://example.com/webhook",
  "syncSettings": {
    "autoSync": true,
    "syncInterval": "1h"
  }
}
```

**Response**: Created sync configuration

### 🔍 Get Sync Configuration
**GET** `/api/v1/calendar/sync/:id`

**Response**: Single sync configuration details

### ✏️ Update Sync Configuration
**PUT** `/api/v1/calendar/sync/:id`

**Request Body**: Partial sync configuration data

**Response**: Updated sync configuration

### 🔌 Disable Sync Configuration
**DELETE** `/api/v1/calendar/sync/:id`

**Response**: Disable confirmation

---

## Data Models

### Calendar Booking
```typescript
interface CalendarBooking {
  id: number;
  propertyId: number;
  startDate: string;
  endDate: string;
  status: 'confirmed' | 'tentative' | 'blocked' | 'cancelled';
  bookingType: string;
  guestName?: string;
  guestPhone?: string;
  guestCount?: number;
  notes?: string;
  source: string;
  externalId?: string;
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Calendar Sync Status
```typescript
interface CalendarSyncStatus {
  id: number;
  propertyId: number;
  calendarType: string;
  lastSyncAt?: Date;
  syncToken?: string;
  isActive: boolean;
  webhookUrl?: string;
  syncSettings: Record<string, any>;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

---

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Start date must be in YYYY-MM-DD format",
    "details": {...}
  },
  "timestamp": "2024-08-08T12:00:00.000Z",
  "requestId": "req_123"
}
```

## Security Features

- **Authentication**: JWT-based authentication required for all endpoints
- **Authorization**: Property ownership validation for management operations
- **Rate Limiting**: Configurable limits per operation type
- **Input Validation**: Comprehensive Zod schema validation
- **Conflict Detection**: Automatic booking overlap prevention
- **Audit Logging**: All operations logged with user context

## Integration Examples

### JavaScript/Fetch
```javascript
// Create a calendar booking
const response = await fetch('/api/v1/calendar/bookings', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    propertyId: 1,
    startDate: '2024-08-15',
    endDate: '2024-08-16',
    status: 'confirmed',
    guestName: 'John Doe'
  })
});

const booking = await response.json();
```

### cURL
```bash
# Check availability
curl -X POST "/api/v1/calendar/availability" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyId": 1,
    "startDate": "2024-08-15",
    "endDate": "2024-08-16"
  }'
```