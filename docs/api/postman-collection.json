{"info": {"name": "Farmhouse API", "description": "Property booking and management platform API", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "Register new user", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"phone\": \"+1234567890\",\n  \"role\": \"user\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "User login", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"name": "Properties", "item": [{"name": "List properties", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/properties", "host": ["{{baseUrl}}"], "path": ["api", "properties"], "query": [{"key": "page", "value": "", "description": "Page number for pagination", "disabled": true}, {"key": "limit", "value": "", "description": "Number of items per page", "disabled": true}, {"key": "location", "value": "", "description": "Filter by location", "disabled": true}, {"key": "minPrice", "value": "", "description": "Minimum price filter", "disabled": true}, {"key": "maxPrice", "value": "", "description": "Maximum price filter", "disabled": true}, {"key": "featured", "value": "", "description": "Filter featured properties only", "disabled": true}]}}, "response": []}, {"name": "Create property", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/properties", "host": ["{{baseUrl}}"], "path": ["api", "properties"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"name": "Bookings", "item": [{"name": "Create booking", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/bookings", "host": ["{{baseUrl}}"], "path": ["api", "bookings"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}}, "response": []}]}]}