# API Design Specification v1.0

Author: Fazeel Usmani  
Date: July 28, 2025

## Overview
This document outlines the standardized RESTful API design for the Farmhouse Rental Platform, implementing consistent patterns, versioning strategy, and response formats.

## Design Principles

### 1. RESTful Resource-Based URLs
- Use nouns for resources, not verbs
- Consistent parent-child relationships
- Predictable URI patterns
- Query parameters for filtering/pagination

### 2. Versioning Strategy
- All APIs prefixed with `/api/v1/`
- Major version in URL path
- Backward compatibility within major versions
- Clear deprecation policy

### 3. Standardized Response Format
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
    requestId: string;
  };
}
```

## API Structure

### Authentication & User Management
```
POST   /api/v1/auth/login
POST   /api/v1/auth/register  
POST   /api/v1/auth/refresh
DELETE /api/v1/auth/logout
DELETE /api/v1/auth/sessions    # Logout from all devices

GET    /api/v1/users/me
PUT    /api/v1/users/me
GET    /api/v1/users/me/properties
GET    /api/v1/users/me/bookings
GET    /api/v1/users/me/reviews
```

### Properties Management
```
# Core CRUD operations
GET    /api/v1/properties                    # List with filtering
POST   /api/v1/properties                    # Create new property
GET    /api/v1/properties/{id}               # Get specific property
PUT    /api/v1/properties/{id}               # Update property
DELETE /api/v1/properties/{id}               # Delete property

# Property sub-resources
GET    /api/v1/properties/{id}/availability  # Check availability
GET    /api/v1/properties/{id}/bookings      # Property bookings
GET    /api/v1/properties/{id}/reviews       # Property reviews
POST   /api/v1/properties/{id}/media         # Upload media
PUT    /api/v1/properties/{id}/media/{mediaId} # Update media
DELETE /api/v1/properties/{id}/media/{mediaId} # Delete media
GET    /api/v1/properties/{id}/pricing       # Dynamic pricing
PUT    /api/v1/properties/{id}/pricing       # Update pricing
```

### Bookings Management
```
# Core CRUD operations  
GET    /api/v1/bookings                      # List user's bookings
POST   /api/v1/bookings                      # Create booking
GET    /api/v1/bookings/{id}                 # Get booking details
PUT    /api/v1/bookings/{id}                 # Update booking
DELETE /api/v1/bookings/{id}                 # Cancel booking

# Booking sub-resources
PUT    /api/v1/bookings/{id}/status          # Update status (owners)
GET    /api/v1/bookings/{id}/payments        # Booking payments
POST   /api/v1/bookings/{id}/reviews         # Add review after booking
```

### Reviews Management
```
GET    /api/v1/reviews                       # List user's reviews
POST   /api/v1/reviews                       # Create review
GET    /api/v1/reviews/{id}                  # Get review details
PUT    /api/v1/reviews/{id}                  # Update review
DELETE /api/v1/reviews/{id}                  # Delete review
PUT    /api/v1/reviews/{id}/response         # Owner response
```

### Payment Management
```
POST   /api/v1/payments/orders               # Create payment order
POST   /api/v1/payments/process              # Process payment
GET    /api/v1/payments/{id}                 # Get payment details
POST   /api/v1/payments/{id}/refund          # Process refund
GET    /api/v1/payments/webhooks/razorpay    # Payment webhooks
```

### Media & File Management
```
POST   /api/v1/media/upload                  # Generic file upload
GET    /api/v1/media/{id}                    # Get media details
DELETE /api/v1/media/{id}                    # Delete media
POST   /api/v1/media/bulk-upload             # Bulk upload
```

### System & Health
```
GET    /api/v1/health                        # Health check
GET    /api/v1/health/detailed               # Detailed health status
GET    /api/v1/system/info                   # System information
```

## HTTP Methods & Status Codes

### HTTP Methods Usage
- `GET`: Retrieve resources (idempotent)
- `POST`: Create new resources
- `PUT`: Update/replace entire resource (idempotent)
- `PATCH`: Partial updates (non-idempotent)
- `DELETE`: Remove resources (idempotent)

### Standard Status Codes
- `200 OK`: Successful GET, PUT, PATCH
- `201 Created`: Successful POST
- `204 No Content`: Successful DELETE
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server errors

## Query Parameters Standards

### Filtering
```
GET /api/v1/properties?location=goa&featured=true&minPrice=1000&maxPrice=5000
```

### Pagination
```
GET /api/v1/properties?page=1&limit=20&sort=createdAt&order=desc
```

### Field Selection
```
GET /api/v1/properties?fields=id,title,price,location
```

### Date Ranges
```
GET /api/v1/bookings?dateFrom=2024-01-01&dateTo=2024-12-31
```

## Error Handling

### Error Response Format
```typescript
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more fields are invalid",
    "details": {
      "fields": [
        {
          "field": "email",
          "message": "Email format is invalid"
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123abc"
  }
}
```

### Error Codes
- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_ERROR`: Authentication failed
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource conflict
- `RATE_LIMIT_ERROR`: Rate limit exceeded
- `INTERNAL_ERROR`: Server error

## Response Examples

### Success Response
```typescript
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Luxury Villa",
    "location": "Goa",
    "price": 5000
  },
  "message": "Property retrieved successfully",
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123abc"
  }
}
```

### Paginated Response
```typescript
{
  "success": true,
  "data": [...],
  "message": "Properties retrieved successfully",
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123abc"
  }
}
```

## Migration Strategy

### Phase 1: Add v1 Endpoints (Parallel)
- Create new v1 endpoints alongside existing ones
- Implement standardized response format
- Test thoroughly with existing client

### Phase 2: Update Client Applications
- Update all client-side API calls to use v1 endpoints
- Implement proper error handling for new format
- Test all user flows

### Phase 3: Deprecate Old Endpoints
- Add deprecation warnings to old endpoints
- Monitor usage analytics
- Plan sunset timeline (6 months notice)

### Phase 4: Remove Legacy Endpoints
- Remove old endpoints after grace period
- Clean up unused middleware and utilities
- Update documentation

## Implementation Checklist

- [ ] Create versioned route structure
- [ ] Implement standardized response utilities
- [ ] Add comprehensive input validation
- [ ] Implement proper error handling
- [ ] Add request/response logging
- [ ] Create API documentation
- [ ] Add automated testing
- [ ] Implement rate limiting
- [ ] Add API analytics
- [ ] Create client SDK