openapi: 3.0.3
info:
  title: Farmhouse Rental API
  description: |
    REST API for Farmhouse Rental Platform
    
    ## Authentication
    This API uses JWT tokens for authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API endpoints are rate limited:
    - General API: 100 requests per 15 minutes
    - Authentication: 20 requests per 15 minutes  
    - Search: 30 requests per minute
    - Bookings: 5 requests per minute
    - OTP: Environment-specific limits
    
    ## Error Handling
    All errors follow a consistent format:
    ```json
    {
      "success": false,
      "error": {
        "code": "ERROR_CODE",
        "message": "Human readable error message",
        "requestId": "unique-request-id"
      },
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
    ```

  version: 1.0.0
  contact:
    name: Farmhouse Rental API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://farmhouse.replit.app/api/v1
    description: Production server
  - url: http://localhost:5000/api/v1
    description: Development server

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Properties
    description: Property management and search
  - name: Bookings
    description: Booking management and availability
  - name: Reviews
    description: Review and rating system
  - name: Uploads
    description: File upload and image management
  - name: OTP
    description: One-time password verification
  - name: Utilities
    description: Utility endpoints and services

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request was successful
        data:
          type: object
          description: Response data (present on success)
        message:
          type: string
          description: Human readable message
        error:
          $ref: '#/components/schemas/ApiError'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
      required: [success, timestamp]

    ApiError:
      type: object
      properties:
        code:
          type: string
          description: Machine readable error code
        message:
          type: string
          description: Human readable error message
        requestId:
          type: string
          description: Unique request identifier for tracking
      required: [code, message]

    User:
      type: object
      properties:
        id:
          type: integer
          description: Unique user identifier
        username:
          type: string
          description: Unique username
        email:
          type: string
          format: email
          description: User email address
        fullName:
          type: string
          description: User's full name
        phone:
          type: string
          description: User's phone number
        role:
          type: string
          enum: [user, owner]
          description: User role
        createdAt:
          type: string
          format: date-time
      required: [id, username, email, fullName, role]

    Property:
      type: object
      properties:
        id:
          type: integer
          description: Unique property identifier
        ownerId:
          type: integer
          description: Property owner's user ID
        title:
          type: string
          description: Property title
        description:
          type: string
          description: Property description
        location:
          type: string
          description: Property location
        halfDayPrice:
          type: number
          format: float
          description: Half-day rental price
        fullDayPrice:
          type: number
          format: float
          description: Full-day rental price
        bedrooms:
          type: integer
          description: Number of bedrooms
        bathrooms:
          type: integer
          description: Number of bathrooms
        amenities:
          type: array
          items:
            type: string
          description: List of available amenities
        images:
          type: array
          items:
            type: string
          description: List of property image URLs
        status:
          type: string
          enum: [active, inactive]
          description: Property status
        featured:
          type: boolean
          description: Whether property is featured
        createdAt:
          type: string
          format: date-time
      required: [id, ownerId, title, description, location, halfDayPrice, fullDayPrice, bedrooms, bathrooms, amenities, images]

    Booking:
      type: object
      properties:
        id:
          type: integer
          description: Unique booking identifier
        propertyId:
          type: integer
          description: Property ID
        userId:
          type: integer
          description: User ID who made the booking
        bookingDate:
          type: string
          format: date
          description: Booking date (YYYY-MM-DD)
        bookingType:
          type: string
          enum: [morning, full_day]
          description: Type of booking
        guests:
          type: integer
          minimum: 1
          description: Number of guests
        totalPrice:
          type: number
          format: float
          description: Total booking price
        status:
          type: string
          enum: [confirmed, cancelled, completed]
          description: Booking status
        specialRequests:
          type: string
          description: Special requests from user
        createdAt:
          type: string
          format: date-time
      required: [id, propertyId, userId, bookingDate, bookingType, guests, totalPrice, status]

    Review:
      type: object
      properties:
        id:
          type: integer
          description: Unique review identifier
        propertyId:
          type: integer
          description: Property ID
        userId:
          type: integer
          description: User ID who wrote the review
        bookingId:
          type: integer
          description: Related booking ID (optional)
        rating:
          type: string
          enum: ['1', '2', '3', '4', '5']
          description: Rating from 1 to 5 stars
        comment:
          type: string
          description: Review comment
        response:
          type: string
          description: Owner response to review
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required: [id, propertyId, userId, rating]

paths:
  # Authentication Endpoints
  /auth/register:
    post:
      tags: [Authentication]
      summary: Register a new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  minLength: 3
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                  pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]'
                  description: Must contain uppercase, lowercase, number, and special character
                fullName:
                  type: string
                  minLength: 2
                role:
                  type: string
                  enum: [user, owner]
                  default: user
              required: [username, email, password, fullName]
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          user:
                            $ref: '#/components/schemas/User'
                          token:
                            type: string
                            description: JWT authentication token
        '400':
          description: Validation error
        '409':
          description: User already exists

  /auth/login:
    post:
      tags: [Authentication]
      summary: Login user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
              required: [email, password]
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          user:
                            $ref: '#/components/schemas/User'
                          token:
                            type: string
        '401':
          description: Invalid credentials
        '429':
          description: Too many login attempts

  /auth/me:
    get:
      tags: [Authentication]
      summary: Get current user
      security:
        - BearerAuth: []
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '401':
          description: Authentication required

  /auth/logout:
    post:
      tags: [Authentication]
      summary: Logout user
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Logout successful

  # Property Endpoints
  /properties:
    get:
      tags: [Properties]
      summary: Search properties
      parameters:
        - name: featured
          in: query
          schema:
            type: boolean
          description: Filter by featured properties
        - name: location
          in: query
          schema:
            type: string
          description: Search by location
        - name: date
          in: query
          schema:
            type: string
            format: date
          description: Check availability for specific date
        - name: minPrice
          in: query
          schema:
            type: number
          description: Minimum price filter
        - name: maxPrice
          in: query
          schema:
            type: number
          description: Maximum price filter
        - name: amenities
          in: query
          schema:
            type: string
          description: Comma-separated list of amenities
      responses:
        '200':
          description: List of properties
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Property'

    post:
      tags: [Properties]
      summary: Create new property (owners only)
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  minLength: 5
                  maxLength: 100
                description:
                  type: string
                  minLength: 10
                  maxLength: 1000
                location:
                  type: string
                  minLength: 5
                  maxLength: 200
                halfDayPrice:
                  type: number
                  minimum: 0
                fullDayPrice:
                  type: number
                  minimum: 0
                bedrooms:
                  type: integer
                  minimum: 1
                bathrooms:
                  type: integer
                  minimum: 1
                amenities:
                  type: array
                  items:
                    type: string
                  minItems: 1
                images:
                  type: array
                  items:
                    type: string
                  minItems: 1
              required: [title, description, location, halfDayPrice, fullDayPrice, bedrooms, bathrooms, amenities, images]
      responses:
        '201':
          description: Property created successfully
        '401':
          description: Authentication required
        '403':
          description: Owner role required

  /properties/{id}:
    get:
      tags: [Properties]
      summary: Get property by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Property details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Property'
        '404':
          description: Property not found

  # Booking Endpoints
  /bookings:
    get:
      tags: [Bookings]
      summary: Get user's bookings
      security:
        - BearerAuth: []
      responses:
        '200':
          description: List of user's bookings
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - $ref: '#/components/schemas/Booking'
                            - type: object
                              properties:
                                property:
                                  $ref: '#/components/schemas/Property'

    post:
      tags: [Bookings]
      summary: Create new booking
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                propertyId:
                  type: integer
                bookingDate:
                  type: string
                  format: date
                bookingType:
                  type: string
                  enum: [morning, full_day]
                guests:
                  type: integer
                  minimum: 1
                totalPrice:
                  type: number
                  minimum: 0
                specialRequests:
                  type: string
              required: [propertyId, bookingDate, bookingType, guests, totalPrice]
      responses:
        '201':
          description: Booking created successfully
        '409':
          description: Property not available for selected date/time
        '429':
          description: Too many booking attempts

  /bookings/check-availability/{propertyId}:
    get:
      tags: [Bookings]
      summary: Check property availability
      parameters:
        - name: propertyId
          in: path
          required: true
          schema:
            type: integer
        - name: date
          in: query
          required: true
          schema:
            type: string
            format: date
        - name: type
          in: query
          required: true
          schema:
            type: string
            enum: [morning, full_day]
      responses:
        '200':
          description: Availability status
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          available:
                            type: boolean

  # Review Endpoints
  /reviews/property/{id}:
    get:
      tags: [Reviews]
      summary: Get reviews for a property
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of property reviews
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - $ref: '#/components/schemas/Review'
                            - type: object
                              properties:
                                user:
                                  $ref: '#/components/schemas/User'

  /reviews:
    post:
      tags: [Reviews]
      summary: Create new review
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                propertyId:
                  type: integer
                bookingId:
                  type: integer
                rating:
                  type: string
                  enum: ['1', '2', '3', '4', '5']
                comment:
                  type: string
                  minLength: 5
                  maxLength: 500
              required: [propertyId, rating, comment]
      responses:
        '201':
          description: Review created successfully

  # OTP Endpoints
  /otp/send-otp:
    post:
      tags: [OTP]
      summary: Send OTP for verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                identifier:
                  type: string
                  description: Email address or phone number
                type:
                  type: string
                  enum: [email, sms]
              required: [identifier, type]
      responses:
        '200':
          description: OTP sent successfully
        '429':
          description: Rate limit exceeded

  /otp/verify-otp-register:
    post:
      tags: [OTP]
      summary: Verify OTP and register user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                identifier:
                  type: string
                code:
                  type: string
                  minLength: 6
                  maxLength: 6
                type:
                  type: string
                  enum: [email, sms]
                userData:
                  type: object
                  properties:
                    fullName:
                      type: string
                    role:
                      type: string
                      enum: [user, owner]
                  required: [fullName]
              required: [identifier, code, type, userData]
      responses:
        '200':
          description: OTP verified and user registered
        '400':
          description: Invalid OTP or user data

  # Upload Endpoints
  /uploads/image:
    post:
      tags: [Uploads]
      summary: Upload single image (owners only)
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary
              required: [image]
      responses:
        '200':
          description: Image uploaded successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          url:
                            type: string
                            description: Image URL
                          provider:
                            type: string
                            enum: [cloudinary, local]
        '401':
          description: Authentication required
        '403':
          description: Owner role required