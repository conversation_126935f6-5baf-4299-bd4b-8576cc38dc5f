{"openapi": "3.0.3", "info": {"title": "Farmhouse API", "description": "Property booking and management platform API", "version": "1.0.0", "contact": {"name": "Farmhouse API Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:5000", "description": "Development server"}, {"url": "https://api.farmhouse.com", "description": "Production server"}], "paths": {"/api/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register new user", "description": "Create a new user account with email verification", "parameters": [], "requestBody": {"description": "User registration data", "required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password", "firstName", "lastName"], "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "description": "Password (min 8 characters)"}, "firstName": {"type": "string", "description": "User first name"}, "lastName": {"type": "string", "description": "User last name"}, "phone": {"type": "string", "description": "Phone number (optional)"}, "role": {"type": "string", "enum": ["user", "owner"], "description": "User role"}}}, "examples": {"user": {"summary": "Regular user registration", "value": {"email": "<EMAIL>", "password": "SecurePassword123!", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+**********", "role": "user"}}, "owner": {"summary": "Property owner registration", "value": {"email": "<EMAIL>", "password": "SecurePassword123!", "firstName": "<PERSON>", "lastName": "Owner", "phone": "+1234567891", "role": "owner"}}}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "description": "JWT authentication token"}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Validation error"}, "409": {"description": "Email already exists"}}}}, "/api/auth/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return JWT token", "parameters": [], "requestBody": {"description": "Login credentials", "required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string"}}}}}}}}, "401": {"description": "Invalid credentials"}}}}, "/api/properties": {"get": {"tags": ["Properties"], "summary": "List properties", "description": "Get a paginated list of properties with optional filtering", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "example": 1}, "description": "Page number for pagination"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "example": 10}, "description": "Number of items per page"}, {"name": "location", "in": "query", "required": false, "schema": {"type": "string", "example": "Goa"}, "description": "Filter by location"}, {"name": "minPrice", "in": "query", "required": false, "schema": {"type": "number", "example": 1000}, "description": "Minimum price filter"}, {"name": "maxPrice", "in": "query", "required": false, "schema": {"type": "number", "example": 10000}, "description": "Maximum price filter"}, {"name": "featured", "in": "query", "required": false, "schema": {"type": "boolean", "example": true}, "description": "Filter featured properties only"}], "responses": {"200": {"description": "Properties list retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Property"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "post": {"tags": ["Properties"], "summary": "Create property", "description": "Create a new property listing (owner only)", "parameters": [], "requestBody": {"description": "Property data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PropertyInput"}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/Property"}}}}}}, "401": {"description": "Authentication required"}, "403": {"description": "Owner role required"}}, "security": [{"type": "bearer", "bearerFormat": "JWT"}]}}, "/api/bookings": {"post": {"tags": ["Bookings"], "summary": "Create booking", "description": "Create a new property booking", "parameters": [], "requestBody": {"description": "Booking data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingInput"}}}}, "responses": {"201": {"description": "Booking created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/Booking"}}}}}}, "400": {"description": "Invalid booking data"}, "409": {"description": "Booking conflict"}}, "security": [{"type": "bearer", "bearerFormat": "JWT"}]}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer"}, "email": {"type": "string", "format": "email"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}, "role": {"type": "string", "enum": ["user", "owner", "admin"]}, "verified": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Property": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "string"}, "halfDayPrice": {"type": "number"}, "fullDayPrice": {"type": "number"}, "bedrooms": {"type": "integer"}, "bathrooms": {"type": "integer"}, "maxGuests": {"type": "integer"}, "amenities": {"type": "array", "items": {"type": "string"}}, "images": {"type": "array", "items": {"type": "string"}}, "featured": {"type": "boolean"}, "status": {"type": "string", "enum": ["active", "inactive", "draft"]}, "ownerId": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "PropertyInput": {"type": "object", "required": ["title", "description", "location", "halfDayPrice", "fullDayPrice"], "properties": {"title": {"type": "string", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "minLength": 1}, "location": {"type": "string"}, "halfDayPrice": {"type": "number", "minimum": 0}, "fullDayPrice": {"type": "number", "minimum": 0}, "bedrooms": {"type": "integer", "minimum": 1}, "bathrooms": {"type": "integer", "minimum": 1}, "maxGuests": {"type": "integer", "minimum": 1}, "amenities": {"type": "array", "items": {"type": "string"}}, "images": {"type": "array", "items": {"type": "string"}}}}, "Booking": {"type": "object", "properties": {"id": {"type": "integer"}, "propertyId": {"type": "integer"}, "userId": {"type": "integer"}, "checkIn": {"type": "string", "format": "date"}, "checkOut": {"type": "string", "format": "date"}, "guests": {"type": "integer"}, "totalAmount": {"type": "number"}, "status": {"type": "string", "enum": ["pending", "confirmed", "cancelled", "completed"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "BookingInput": {"type": "object", "required": ["propertyId", "checkIn", "checkOut", "guests"], "properties": {"propertyId": {"type": "integer"}, "checkIn": {"type": "string", "format": "date"}, "checkOut": {"type": "string", "format": "date"}, "guests": {"type": "integer", "minimum": 1}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "totalPages": {"type": "integer"}, "hasNext": {"type": "boolean"}, "hasPrev": {"type": "boolean"}}}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "tags": [{"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Properties", "description": "Property listing management"}, {"name": "Bookings", "description": "Booking creation and management"}]}