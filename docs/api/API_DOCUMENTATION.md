# Farmhouse Rental API Documentation

Author: Fazeel Usmani  
Date: August 5, 2025

## 🚀 **API Overview**

The Farmhouse Rental API provides comprehensive functionality for managing property bookings, user authentication, SMS notifications, and system monitoring.

**Base URL**: `https://your-domain.com/api`  
**Version**: v1  
**Authentication**: JWT <PERSON>er <PERSON>ken  
**Content-Type**: `application/json`

---

## 🔐 **Authentication**

### **OTP-Based Authentication Flow**

1. **Request OTP** → 2. **Verify OTP** → 3. **Receive JWT Token** → 4. **Use Token for API Calls**

### **Headers Required**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
x-correlation-id: optional-correlation-id
```

---

## 📋 **API Endpoints**

### **🔑 Authentication & OTP**

#### **POST /api/otp/request**
Request OTP for phone number verification.

**Request Body:**
```json
{
  "phone": "+************",
  "type": "login" // or "registration"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "otpSent": true,
    "expiresIn": 300,
    "canRetryAfter": 60
  }
}
```

**Rate Limits:**
- 5 requests per 15 minutes per IP
- 3 requests per day per phone number

**Error Responses:**
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many OTP requests. Please try after 15 minutes."
  }
}
```

---

#### **POST /api/otp/verify**
Verify OTP and receive JWT token.

**Request Body:**
```json
{
  "phone": "+************",
  "otp": "123456"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "phone": "+************",
      "fullName": "John Doe",
      "isVerified": true
    },
    "expiresIn": 86400
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_OTP",
    "message": "Invalid or expired OTP"
  }
}
```

---

### **👤 User Management**

#### **GET /api/users/profile**
Get current user profile.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "phone": "+************",
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "isVerified": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

---

#### **PUT /api/users/profile**
Update user profile.

**Request Body:**
```json
{
  "fullName": "John Smith",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "user_123",
    "phone": "+************",
    "fullName": "John Smith",
    "email": "<EMAIL>"
  }
}
```

---

### **🏠 Property Management**

#### **GET /api/properties**
Get list of properties with pagination and filtering.

**Query Parameters:**
```
?page=1
&limit=20
&location=goa
&minPrice=1000
&maxPrice=5000
&amenities=wifi,pool
&sortBy=price
&sortOrder=asc
```

**Response:**
```json
{
  "success": true,
  "data": {
    "properties": [
      {
        "id": "prop_123",
        "title": "Beautiful Beach Villa",
        "description": "Stunning villa with ocean view",
        "location": "Goa, India",
        "pricePerNight": 2500,
        "maxGuests": 6,
        "bedrooms": 3,
        "bathrooms": 2,
        "amenities": ["wifi", "pool", "kitchen"],
        "images": [
          "https://cloudinary.com/image1.jpg",
          "https://cloudinary.com/image2.jpg"
        ],
        "rating": 4.8,
        "reviewCount": 24,
        "isAvailable": true,
        "owner": {
          "id": "owner_456",
          "name": "Property Owner",
          "phone": "+************"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 95,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

#### **GET /api/properties/:id**
Get single property details.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "prop_123",
    "title": "Beautiful Beach Villa",
    "description": "Stunning villa with ocean view...",
    "location": "Goa, India",
    "coordinates": {
      "latitude": 15.2993,
      "longitude": 74.1240
    },
    "pricePerNight": 2500,
    "maxGuests": 6,
    "bedrooms": 3,
    "bathrooms": 2,
    "amenities": ["wifi", "pool", "kitchen", "parking"],
    "images": [
      "https://cloudinary.com/image1.jpg",
      "https://cloudinary.com/image2.jpg"
    ],
    "availability": {
      "checkIn": "2024-01-20",
      "checkOut": "2024-01-25",
      "blockedDates": ["2024-01-23"]
    },
    "policies": {
      "cancellation": "Free cancellation up to 24 hours before check-in",
      "houseRules": ["No smoking", "No pets", "No parties"]
    },
    "rating": 4.8,
    "reviewCount": 24,
    "owner": {
      "id": "owner_456",
      "name": "Property Owner",
      "phone": "+************",
      "verified": true
    }
  }
}
```

---

### **📅 Booking Management**

#### **POST /api/bookings**
Create a new booking.

**Request Body:**
```json
{
  "propertyId": "prop_123",
  "checkIn": "2024-01-20",
  "checkOut": "2024-01-25",
  "guests": 4,
  "totalAmount": 12500,
  "guestDetails": {
    "primaryGuest": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************"
    },
    "additionalGuests": 3
  },
  "specialRequests": "Late check-in requested"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Booking created successfully",
  "data": {
    "id": "booking_789",
    "propertyId": "prop_123",
    "userId": "user_123",
    "checkIn": "2024-01-20",
    "checkOut": "2024-01-25",
    "guests": 4,
    "status": "confirmed",
    "totalAmount": 12500,
    "bookingReference": "BK2024012001",
    "createdAt": "2024-01-15T10:30:00Z",
    "smsNotificationSent": true
  }
}
```

---

#### **GET /api/bookings**
Get user's bookings.

**Query Parameters:**
```
?status=confirmed
&page=1
&limit=10
```

**Response:**
```json
{
  "success": true,
  "data": {
    "bookings": [
      {
        "id": "booking_789",
        "property": {
          "id": "prop_123",
          "title": "Beautiful Beach Villa",
          "location": "Goa, India",
          "image": "https://cloudinary.com/image1.jpg"
        },
        "checkIn": "2024-01-20",
        "checkOut": "2024-01-25",
        "guests": 4,
        "status": "confirmed",
        "totalAmount": 12500,
        "bookingReference": "BK2024012001"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 15
    }
  }
}
```

---

### **💬 Reviews & Ratings**

#### **POST /api/reviews**
Add a review for a property.

**Request Body:**
```json
{
  "propertyId": "prop_123",
  "bookingId": "booking_789",
  "rating": 5,
  "comment": "Amazing property with beautiful views!",
  "aspects": {
    "cleanliness": 5,
    "location": 5,
    "value": 4,
    "communication": 5
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Review added successfully",
  "data": {
    "id": "review_456",
    "rating": 5,
    "comment": "Amazing property with beautiful views!",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

---

### **📊 System Monitoring**

#### **GET /api/health**
System health check.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00Z",
    "uptime": 86400,
    "version": "1.0.0",
    "services": {
      "database": "healthy",
      "sms": "healthy",
      "email": "healthy",
      "cache": "healthy"
    }
  }
}
```

---

#### **GET /api/performance/metrics**
Get system performance metrics (Admin only).

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "system": {
      "cpu": { "usage": 25.5, "loadAverage": [0.5, 0.3, 0.2] },
      "memory": { "used": 536870912, "total": **********, "percentage": 50.0 }
    },
    "database": {
      "connectionCount": 3,
      "activeQueries": 1,
      "avgResponseTime": 45
    },
    "sms": {
      "deliveryRate": 98.5,
      "avgProcessingTime": 1200,
      "failureRate": 1.5
    },
    "cache": {
      "hitRate": 85.2,
      "memoryUsage": 134217728,
      "keyCount": 1250
    },
    "queue": {
      "pendingJobs": 5,
      "processingJobs": 2,
      "failedJobs": 0
    }
  }
}
```

---

## 🚨 **Error Codes & Responses**

### **Standard Error Response Format**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error context (optional)"
  },
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "correlationId": "req_123456789"
  }
}
```

### **Common Error Codes**

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Invalid or missing authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_SERVER_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE` | 503 | External service down |

### **Authentication Errors**
| Code | Description |
|------|-------------|
| `INVALID_TOKEN` | JWT token is invalid or expired |
| `TOKEN_EXPIRED` | JWT token has expired |
| `INVALID_OTP` | OTP is incorrect or expired |
| `OTP_EXPIRED` | OTP has expired |
| `PHONE_NOT_VERIFIED` | Phone number not verified |

### **Business Logic Errors**
| Code | Description |
|------|-------------|
| `PROPERTY_NOT_AVAILABLE` | Property not available for selected dates |
| `BOOKING_CONFLICT` | Booking dates conflict with existing booking |
| `INSUFFICIENT_CAPACITY` | Too many guests for property |
| `DUPLICATE_REVIEW` | User already reviewed this property |

---

## 🔄 **Rate Limits**

### **Global Rate Limits**
- **General API**: 100 requests per minute per IP
- **Authentication**: 10 requests per minute per IP
- **OTP Requests**: 5 requests per 15 minutes per IP

### **Per-User Rate Limits**
- **OTP Requests**: 3 requests per day per phone number
- **Booking Creation**: 10 bookings per hour per user
- **Review Submission**: 1 review per booking

### **Rate Limit Headers**
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

---

## 📱 **SMS Notifications**

The API automatically sends SMS notifications for:

### **Booking Events**
- **Booking Confirmation**: Sent immediately after successful booking
- **Check-in Reminder**: Sent 24 hours before check-in
- **Booking Cancellation**: Sent when booking is cancelled

### **Authentication Events**
- **OTP Delivery**: OTP code for login/registration
- **Account Verification**: Welcome message after first successful login

### **SMS Template Examples**

**Booking Confirmation:**
```
BookAFarm booking for Beach Villa on 2024-01-20 confirmed. 
Booking ID: BK2024012001. 
Check-in: 3 PM. Contact: +************
```

**OTP Message:**
```
Your BookAFarm verification code is 123456. 
Valid for 5 minutes. Don't share this code.
```

---

## 🔧 **Development & Testing**

### **Testing Endpoints**
Use these endpoints for testing in development:

**Test OTP (Development Only):**
```bash
curl -X POST http://localhost:5000/api/otp/test \
  -H "Content-Type: application/json" \
  -d '{"phone": "+************"}'
```

### **Example Requests**

**Request OTP:**
```bash
curl -X POST https://api.farmhouse.com/api/otp/request \
  -H "Content-Type: application/json" \
  -d '{"phone": "+************", "type": "login"}'
```

**Create Booking:**
```bash
curl -X POST https://api.farmhouse.com/api/bookings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "propertyId": "prop_123",
    "checkIn": "2024-01-20",
    "checkOut": "2024-01-25",
    "guests": 4,
    "totalAmount": 12500
  }'
```

---

## 📋 **API Changelog**

### **Version 1.0.0** (Current)
- Initial API release
- OTP-based authentication
- Property and booking management
- SMS notifications with DLT compliance
- Performance monitoring endpoints
- Rate limiting and security features

---

## 🆘 **Support & Contact**

**Technical Support**: <EMAIL>  
**API Issues**: <EMAIL>  
**Documentation**: https://docs.farmhouse.com  

**Response Times**:
- Critical Issues: < 2 hours
- General Support: < 24 hours
- Feature Requests: < 48 hours