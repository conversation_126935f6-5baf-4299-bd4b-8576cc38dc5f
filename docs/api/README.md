# Farmhouse API

Property booking and management platform API

**Version:** 1.0.0

## Table of Contents

- [Authentication](#authentication)
- [Properties](#properties)
- [Bookings](#bookings)

## Base URLs

- **Development server**: `http://localhost:5000`
- **Production server**: `https://api.farmhouse.com`

## Authentication

This API uses JWT Bearer token authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Authentication

User authentication and authorization

### POST /api/auth/register

Create a new user account with email verification

#### Request Body

User registration data

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********",
  "role": "user"
}
```

#### Responses

**201**: User registered successfully

**400**: Validation error

**409**: Email already exists


### POST /api/auth/login

Authenticate user and return JWT token

#### Request Body

Login credentials

```json

```

#### Responses

**200**: Login successful

**401**: Invalid credentials


## Properties

Property listing management

### GET /api/properties

Get a paginated list of properties with optional filtering

#### Parameters

| Name | Type | In | Required | Description |
|------|------|----|---------|--------------|
| page | integer | query | No | Page number for pagination |
| limit | integer | query | No | Number of items per page |
| location | string | query | No | Filter by location |
| minPrice | number | query | No | Minimum price filter |
| maxPrice | number | query | No | Maximum price filter |
| featured | boolean | query | No | Filter featured properties only |

#### Responses

**200**: Properties list retrieved successfully


### POST /api/properties

Create a new property listing (owner only)

#### Request Body

Property data

```json

```

#### Responses

**201**: Property created successfully

**401**: Authentication required

**403**: Owner role required


## Bookings

Booking creation and management

### POST /api/bookings

Create a new property booking

#### Request Body

Booking data

```json

```

#### Responses

**201**: Booking created successfully

**400**: Invalid booking data

**409**: Booking conflict


