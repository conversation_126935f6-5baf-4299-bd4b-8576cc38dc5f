client/src/features/dashboard/OwnerDashboardRefactored.tsx(93,17): error TS2739: Type '{}' is missing the following properties from type 'PricingManagementTabProps': properties, isLoading
client/src/features/dashboard/OwnerDashboardRefactored.tsx(138,8): error TS2375: Type '{ children: Element | null; selectedTab: "media" | "bookings" | "properties" | "pricing" | "analytics" | "communications" | "settings"; onTabChange: Dispatch<SetStateAction<"media" | "bookings" | ... 4 more ... | "settings">>; dashboardSummary: DashboardSummary | undefined; connectionStatus: ConnectionStatus; isConn...' is not assignable to type 'DashboardLayoutProps' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'dashboardSummary' are incompatible.
    Type 'DashboardSummary | undefined' is not assignable to type '{ propertyCount: number; activeBookings: number; pendingBookings: number; totalRevenue: number; }'.
      Type 'undefined' is not assignable to type '{ propertyCount: number; activeBookings: number; pendingBookings: number; totalRevenue: number; }'.
client/src/features/dashboard/components/BookingManagement/BookingCard.tsx(62,26): error TS2551: Property 'property' does not exist on type 'BookingWithPropertyAndGuest'. Did you mean 'propertyId'?
client/src/features/dashboard/components/BookingManagement/BookingDetailsModal.tsx(65,24): error TS2551: Property 'property' does not exist on type 'BookingWithPropertyAndGuest'. Did you mean 'propertyId'?
client/src/features/dashboard/components/BookingManagement/index.ts(16,3): error TS18004: No value exists in scope for the shorthand property 'BookingList'. Either declare one or provide an initializer.
client/src/features/dashboard/components/BookingManagement/index.ts(17,3): error TS18004: No value exists in scope for the shorthand property 'BookingCard'. Either declare one or provide an initializer.
client/src/features/dashboard/components/BookingManagement/index.ts(18,3): error TS18004: No value exists in scope for the shorthand property 'BookingFilters'. Either declare one or provide an initializer.
client/src/features/dashboard/components/BookingManagement/index.ts(19,3): error TS18004: No value exists in scope for the shorthand property 'BookingDetailsModal'. Either declare one or provide an initializer.
client/src/features/dashboard/components/BookingManagement/index.ts(20,3): error TS18004: No value exists in scope for the shorthand property 'BookingStatusButton'. Either declare one or provide an initializer.
client/src/features/dashboard/components/index.ts(19,3): error TS18004: No value exists in scope for the shorthand property 'DashboardLayout'. Either declare one or provide an initializer.
client/src/features/dashboard/components/shared/index.ts(11,3): error TS2552: Cannot find name 'NotificationBadge'. Did you mean 'Notification'?
client/src/features/dashboard/components/shared/index.ts(12,3): error TS18004: No value exists in scope for the shorthand property 'ConnectionStatusIndicator'. Either declare one or provide an initializer.
client/src/features/dashboard/components/shared/index.ts(13,3): error TS18004: No value exists in scope for the shorthand property 'LazyImage'. Either declare one or provide an initializer.
client/src/features/dashboard/types/index.ts(172,3): error TS2693: 'FeatureModule' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(173,3): error TS2693: 'DashboardSummary' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(174,3): error TS2693: 'BookingWithPropertyAndGuest' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(175,3): error TS2693: 'PropertyWithBookings' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(176,3): error TS2693: 'ConnectionStatus' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(177,3): error TS2693: 'DashboardTab' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(178,3): error TS2693: 'BookingStatus' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(179,3): error TS2693: 'RealTimeUpdate' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(180,3): error TS2693: 'ApiResponse' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(181,3): error TS2693: 'PaginatedResponse' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(182,3): error TS2693: 'BatchOperation' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(183,3): error TS2693: 'NotificationSettings' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(184,3): error TS2693: 'ConfirmationDialog' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(185,3): error TS2693: 'DashboardFilters' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(186,3): error TS2693: 'DashboardPagination' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(187,3): error TS2693: 'OptimisticUpdate' only refers to a type, but is being used as a value here.
client/src/features/dashboard/types/index.ts(188,3): error TS2693: 'FeatureFlags' only refers to a type, but is being used as a value here.
client/src/hooks/useBandwidthOptimization.ts(95,5): error TS2353: Object literal may only specify known properties, and 'get' does not exist in type 'Function'.
client/src/hooks/useBandwidthOptimization.ts(103,45): error TS2379: Argument of type '{ body: BodyInit | null; cache?: RequestCache; credentials?: RequestCredentials; headers: HeadersInit | { length: number; toString(): string; toLocaleString(): string; toLocaleString(locales: string | string[], options?: Intl.NumberFormatOptions & Intl.DateTimeFormatOptions): string; ... 38 more ...; 'Content-Type':...' is not assignable to parameter of type 'RequestInit' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'body' are incompatible.
    Type 'BodyInit | null | undefined' is not assignable to type 'BodyInit | null'.
      Type 'undefined' is not assignable to type 'BodyInit | null'.
client/src/hooks/useBandwidthOptimization.ts(115,45): error TS2379: Argument of type '{ body: BodyInit | null; cache?: RequestCache; credentials?: RequestCredentials; headers: HeadersInit | { length: number; toString(): string; toLocaleString(): string; toLocaleString(locales: string | string[], options?: Intl.NumberFormatOptions & Intl.DateTimeFormatOptions): string; ... 38 more ...; 'Content-Type':...' is not assignable to parameter of type 'RequestInit' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'body' are incompatible.
    Type 'BodyInit | null | undefined' is not assignable to type 'BodyInit | null'.
      Type 'undefined' is not assignable to type 'BodyInit | null'.
client/src/hooks/useOptimizedBookings.ts(7,25): error TS2307: Cannot find module './useAuth' or its corresponding type declarations.
client/src/hooks/useOptimizedBookings.ts(236,13): error TS7030: Not all code paths return a value.
client/src/hooks/usePerformanceMonitoring.tsx(193,13): error TS2322: Type 'PropsWithoutRef<P> & { ref: ForwardedRef<any>; }' is not assignable to type 'IntrinsicAttributes & P'.
  Type 'PropsWithoutRef<P> & { ref: ForwardedRef<any>; }' is not assignable to type 'P'.
    'PropsWithoutRef<P> & { ref: ForwardedRef<any>; }' is assignable to the constraint of type 'P', but 'P' could be instantiated with a different subtype of constraint 'object'.
client/src/hooks/useUpdateBatching.ts(90,9): error TS2349: This expression is not callable.
  Not all constituents of type '((prev: TData | undefined) => TData) | (TData & Function)' are callable.
    Type 'TData & Function' has no call signatures.
client/src/hooks/useUpdateBatching.ts(228,13): error TS7030: Not all code paths return a value.
client/src/pages/OwnerDashboard.tsx(1123,7): error TS2779: The left-hand side of an assignment expression may not be an optional property access.
client/src/pages/OwnerDashboard.tsx(1135,7): error TS2779: The left-hand side of an assignment expression may not be an optional property access.
client/src/pages/OwnerDashboard.tsx(1144,7): error TS2779: The left-hand side of an assignment expression may not be an optional property access.
client/src/pages/OwnerDashboard.tsx(1160,7): error TS2779: The left-hand side of an assignment expression may not be an optional property access.
client/src/pages/OwnerDashboard.tsx(1316,22): error TS2551: Property 'realtime' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'REALTIME'?
client/src/pages/OwnerDashboard.tsx(1412,22): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(1445,22): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(1516,22): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2350,30): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2718,35): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2771,34): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2780,33): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2841,58): error TS2551: Property 'realtime' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'REALTIME'?
client/src/pages/OwnerDashboard.tsx(2852,59): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2853,46): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2862,59): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboard.tsx(2863,46): error TS2551: Property 'enhancements' does not exist on type '{ readonly CORE: { readonly DASHBOARD_STATS: true; readonly BASIC_BOOKING_MANAGEMENT: true; readonly PROPERTY_LISTING: true; readonly NOTIFICATIONS: true; }; readonly REALTIME: { readonly ENABLED: true; ... 4 more ...; readonly RECONNECT_INTERVAL: number; }; readonly ENHANCEMENTS: { ...; }; readonly EXPERIMENTAL: { ...'. Did you mean 'ENHANCEMENTS'?
client/src/pages/OwnerDashboardContextified.tsx(137,14): error TS2739: Type '{}' is missing the following properties from type 'PricingManagementTabProps': properties, isLoading
client/src/services/bandwidthOptimizer.ts(455,39): error TS2379: Argument of type '{ url: string; method: string; body: string; timestamp: number; expiresAt: number; etag: string | undefined; lastModified: string | undefined; }' is not assignable to parameter of type 'RequestCacheEntry' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'etag' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
client/src/services/bandwidthOptimizer.ts(476,9): error TS2375: Type '{ etag: string | undefined; lastModified: string | undefined; }' is not assignable to type '{ etag?: string; lastModified?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'etag' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
client/src/services/deltaStateManager.ts(111,42): error TS2379: Argument of type '{ entityType: "user" | "property" | "booking"; entityId: number; version: number; lastUpdated: number; checksum: string | undefined; }' is not assignable to parameter of type 'EntityVersionState' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'checksum' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
client/src/services/deltaStateManager.ts(444,35): error TS2484: Export declaration conflicts with exported declaration of 'BatchedDeltaMessage'.
client/src/services/deltaStateManager.ts(444,56): error TS2484: Export declaration conflicts with exported declaration of 'MissedUpdatesMessage'.
client/src/services/performanceMonitor.ts(82,23): error TS2379: Argument of type '{ name: string; value: number; unit: string; timestamp: number; metadata: Record<string, any> | undefined; }' is not assignable to parameter of type 'PerformanceMetric' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'metadata' are incompatible.
    Type 'Record<string, any> | undefined' is not assignable to type 'Record<string, any>'.
      Type 'undefined' is not assignable to type 'Record<string, any>'.
client/src/services/realTimeUpdates.ts(290,39): error TS2345: Argument of type 'BatchedDeltaMessage' is not assignable to parameter of type 'SSEEvent'.
  Type 'BatchedDeltaMessage' is missing the following properties from type 'ConnectedEvent': message, userId, connectionId, deltaSupport, and 2 more.
client/src/services/realTimeUpdates.ts(300,42): error TS2345: Argument of type 'MissedUpdatesMessage' is not assignable to parameter of type 'SSEEvent'.
  Property 'data' is missing in type 'MissedUpdatesMessage' but required in type 'MissedUpdatesEvent'.
client/src/services/realTimeUpdates.ts(309,41): error TS2345: Argument of type '{ trackedEntities: number; queuedUpdates: number; }' is not assignable to parameter of type 'SSEEvent'.
client/src/services/realTimeUpdates.ts(560,58): error TS2345: Argument of type '(event: BookingUpdateEvent) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'BookingUpdateEvent'.
      Type 'PropertyUpdateEvent' is missing the following properties from type 'BookingUpdateEvent': bookingId, userId
client/src/services/realTimeUpdates.ts(564,59): error TS2345: Argument of type '(event: PropertyUpdateEvent) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'PropertyUpdateEvent'.
      Property 'ownerId' is missing in type 'BookingUpdateEvent' but required in type 'PropertyUpdateEvent'.
client/src/services/realTimeUpdates.ts(569,55): error TS2345: Argument of type '(event: BatchedDeltaMessage) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'BatchedDeltaMessage'.
      Type 'BookingUpdateEvent' is missing the following properties from type 'BatchedDeltaMessage': messageId, count, messages, metadata
client/src/services/realTimeUpdates.ts(573,58): error TS2345: Argument of type '(event: MissedUpdatesMessage) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'MissedUpdatesMessage'.
      Type 'BookingUpdateEvent' is missing the following properties from type 'MissedUpdatesMessage': count, updates
client/src/services/realTimeUpdates.ts(577,55): error TS2345: Argument of type '(stats: { trackedEntities: number; queuedUpdates: number; }) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'stats' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type '{ trackedEntities: number; queuedUpdates: number; }'.
      Type 'BookingUpdateEvent' is missing the following properties from type '{ trackedEntities: number; queuedUpdates: number; }': trackedEntities, queuedUpdates
client/src/services/realTimeUpdates.ts(587,63): error TS2345: Argument of type '(event: BookingUpdateEvent) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'BookingUpdateEvent'.
      Type 'PropertyUpdateEvent' is missing the following properties from type 'BookingUpdateEvent': bookingId, userId
client/src/services/realTimeUpdates.ts(591,64): error TS2345: Argument of type '(event: PropertyUpdateEvent) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'PropertyUpdateEvent'.
      Property 'ownerId' is missing in type 'BookingUpdateEvent' but required in type 'PropertyUpdateEvent'.
client/src/services/realTimeUpdates.ts(595,60): error TS2345: Argument of type '(event: BatchedDeltaMessage) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'BatchedDeltaMessage'.
      Type 'BookingUpdateEvent' is missing the following properties from type 'BatchedDeltaMessage': messageId, count, messages, metadata
client/src/services/realTimeUpdates.ts(599,63): error TS2345: Argument of type '(event: MissedUpdatesMessage) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'event' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type 'MissedUpdatesMessage'.
      Type 'BookingUpdateEvent' is missing the following properties from type 'MissedUpdatesMessage': count, updates
client/src/services/realTimeUpdates.ts(603,60): error TS2345: Argument of type '(stats: { trackedEntities: number; queuedUpdates: number; }) => void' is not assignable to parameter of type '(event: SSEEvent) => void'.
  Types of parameters 'stats' and 'event' are incompatible.
    Type 'SSEEvent' is not assignable to type '{ trackedEntities: number; queuedUpdates: number; }'.
      Type 'BookingUpdateEvent' is missing the following properties from type '{ trackedEntities: number; queuedUpdates: number; }': trackedEntities, queuedUpdates
client/src/services/updateBatcher.ts(639,15): error TS2484: Export declaration conflicts with exported declaration of 'UpdateOperation'.
client/src/services/updateBatcher.ts(639,32): error TS2484: Export declaration conflicts with exported declaration of 'BatchedUpdate'.
client/src/services/updateBatcher.ts(639,47): error TS2484: Export declaration conflicts with exported declaration of 'DeduplicationRule'.
client/src/services/updateBatcher.ts(639,66): error TS2484: Export declaration conflicts with exported declaration of 'BatchingStats'.
server/errors/index.ts(127,3): error TS4114: This member must have an 'override' modifier because it overrides a member in the base class 'BaseError'.
server/errors/index.ts(213,5): error TS2412: Type 'number | undefined' is not assignable to type 'number' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
  Type 'undefined' is not assignable to type 'number'.
server/errors/index.ts(216,3): error TS4114: This member must have an 'override' modifier because it overrides a member in the base class 'BaseError'.
server/errors/index.ts(259,3): error TS4114: This member must have an 'override' modifier because it overrides a member in the base class 'BaseError'.
server/errors/index.ts(284,5): error TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
  Type 'undefined' is not assignable to type 'string'.
server/errors/index.ts(288,3): error TS4114: This member must have an 'override' modifier because it overrides a member in the base class 'BaseError'.
server/errors/index.ts(335,5): error TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
  Type 'undefined' is not assignable to type 'string'.
server/errors/index.ts(336,5): error TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
  Type 'undefined' is not assignable to type 'string'.
server/errors/index.ts(365,47): error TS2379: Argument of type '{ stack: string | undefined; userId?: number; requestId?: string; service?: string; operation?: string; metadata?: Record<string, any>; }' is not assignable to parameter of type 'ErrorContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'stack' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
server/middlewares/caseTransform.ts(177,38): error TS7030: Not all code paths return a value.
server/middlewares/caseTransform.ts(296,36): error TS2345: Argument of type 'readonly ["fullName", "phoneNumber"] | readonly ["halfDayPrice", "fullDayPrice"] | readonly ["bookingType", "specialRequests"]' is not assignable to parameter of type 'string[]'.
  The type 'readonly ["fullName", "phoneNumber"]' is 'readonly' and cannot be assigned to the mutable type 'string[]'.
server/middlewares/enhancedAuth.ts(96,15): error TS2345: Argument of type '"service_failure"' is not assignable to parameter of type '"unauthorized_access" | "invalid_token" | "role_mismatch" | "ownership_violation"'.
server/middlewares/enhancedAuth.ts(143,9): error TS2345: Argument of type '"critical_security_failure"' is not assignable to parameter of type '"unauthorized_access" | "invalid_token" | "role_mismatch" | "ownership_violation"'.
server/middlewares/enhancedAuth.ts(524,9): error TS2345: Argument of type '"critical_security_failure"' is not assignable to parameter of type '"unauthorized_access" | "invalid_token" | "role_mismatch" | "ownership_violation"'.
server/middlewares/enhancedErrorHandler.ts(28,3): error TS2305: Module '"../../shared/api-response-utils"' has no exported member 'sendStandardError'.
server/middlewares/enhancedErrorHandler.ts(82,16): error TS2339: Property 'fatal' does not exist on type 'typeof LoggerService'.
server/middlewares/enhancedErrorHandler.ts(90,69): error TS2554: Expected 1-3 arguments, but got 4.
server/middlewares/enhancedErrorHandler.ts(196,44): error TS2379: Argument of type '{ requestId: string | undefined; userId: any; }' is not assignable to parameter of type 'ErrorContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'requestId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
server/middlewares/enhancedErrorHandler.ts(199,49): error TS2379: Argument of type '{ requestId: string | undefined; userId: any; }' is not assignable to parameter of type 'ErrorContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'requestId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
server/middlewares/enhancedErrorHandler.ts(284,9): error TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
  Type 'undefined' is not assignable to type 'string'.
server/middlewares/enhancedErrorHandler.ts(346,12): error TS2339: Property 'fatal' does not exist on type 'typeof LoggerService'.
server/routes/auth.ts(41,10): error TS2323: Cannot redeclare exported variable 'authenticate'.
server/routes/auth.ts(41,10): error TS2484: Export declaration conflicts with exported declaration of 'authenticate'.
server/routes/auth.ts(41,24): error TS2323: Cannot redeclare exported variable 'authorize'.
server/routes/auth.ts(41,24): error TS2484: Export declaration conflicts with exported declaration of 'authorize'.
server/routes/auth.ts(74,23): error TS2323: Cannot redeclare exported variable 'authenticate'.
server/routes/auth.ts(113,17): error TS2323: Cannot redeclare exported variable 'authorize'.
server/routes/bookings.ts(220,11): error TS18048: 'newBooking' is possibly 'undefined'.
server/routes/bookings.ts(234,9): error TS18048: 'newBooking' is possibly 'undefined'.
server/routes/bookings.ts(275,11): error TS2451: Cannot redeclare block-scoped variable 'booking'.
server/routes/bookings.ts(285,11): error TS2451: Cannot redeclare block-scoped variable 'booking'.
server/routes/events.ts(6,24): error TS2307: Cannot find module '../../../shared/constants' or its corresponding type declarations.
server/routes/events.ts(200,14): error TS2323: Cannot redeclare exported variable 'sendEventToUser'.
server/routes/events.ts(245,14): error TS2323: Cannot redeclare exported variable 'broadcastEvent'.
server/routes/events.ts(355,18): error TS2551: Property 'logGeneralAction' does not exist on type 'typeof AuditService'. Did you mean 'logOwnerAction'?
server/routes/events.ts(433,16): error TS7030: Not all code paths return a value.
server/routes/events.ts(461,18): error TS7030: Not all code paths return a value.
server/routes/events.ts(485,10): error TS2323: Cannot redeclare exported variable 'sendEventToUser'.
server/routes/events.ts(485,10): error TS2484: Export declaration conflicts with exported declaration of 'sendEventToUser'.
server/routes/events.ts(485,27): error TS2323: Cannot redeclare exported variable 'broadcastEvent'.
server/routes/events.ts(485,27): error TS2484: Export declaration conflicts with exported declaration of 'broadcastEvent'.
server/routes/example-standardized.ts(15,3): error TS2305: Module '"../middlewares/errorHandler"' has no exported member 'BusinessLogicError'.
server/routes/example-standardized.ts(44,26): error TS2552: Cannot find name 'storage'. Did you mean 'Storage'?
server/routes/example-standardized.ts(47,28): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(47,47): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(72,37): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(72,66): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(73,26): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(83,28): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(89,31): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(91,16): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(108,28): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(114,30): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(114,49): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(131,27): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(146,28): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(152,9): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(152,59): error TS18048: 'req.user' is possibly 'undefined'.
server/routes/example-standardized.ts(157,38): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(162,11): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(202,28): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(205,28): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(208,19): error TS2304: Cannot find name 'storage'.
server/routes/example-standardized.ts(213,33): error TS2345: Argument of type '{ index: number; result: any; }' is not assignable to parameter of type 'never'.
server/routes/example-standardized.ts(216,29): error TS2345: Argument of type '{ index: number; operation: any; error: { code: any; message: any; }; }' is not assignable to parameter of type 'never'.
server/routes/example-standardized.ts(220,19): error TS18046: 'error' is of type 'unknown'.
server/routes/example-standardized.ts(221,22): error TS18046: 'error' is of type 'unknown'.
server/routes/example-standardized.ts(238,24): error TS2304: Cannot find name 'someAsyncOperation'.
server/routes/example-standardized.ts(251,24): error TS2304: Cannot find name 'someOperation'.
server/security/SecurityValidator.ts(273,15): error TS2339: Property 'database' does not exist on type 'typeof import("/mnt/c/Users/<USER>/Documents/Farmhouse/server/utils/database")'.
server/services/EnhancedPaymentService.ts(390,84): error TS7006: Parameter 'a' implicitly has an 'any' type.
server/services/EnhancedPaymentService.ts(390,87): error TS7006: Parameter 'b' implicitly has an 'any' type.
server/storage-example-enhanced.ts(147,11): error TS2353: Object literal may only specify known properties, and 'updatedAt' does not exist in type '{ bookingDate?: string | SQL<unknown> | PgColumn<ColumnBaseConfig<ColumnDataType, string>, {}, {}> | undefined; bookingType?: "morning" | "full_day" | SQL<...> | PgColumn<...> | undefined; ... 13 more ...; paymentExpiry?: Date | ... 3 more ... | undefined; }'.
server/storage-example-enhanced.ts(194,11): error TS18046: 'error' is of type 'unknown'.
server/storage-example-enhanced.ts(199,13): error TS2353: Object literal may only specify known properties, and 'propertyId' does not exist in type 'ErrorContext'.
server/storage-example-enhanced.ts(213,11): error TS2353: Object literal may only specify known properties, and 'propertyId' does not exist in type 'ErrorContext'.
server/storage-example-enhanced.ts(241,33): error TS2339: Property 'checkExists' does not exist on type 'EnhancedStorage'.
server/storage-example-enhanced.ts(253,37): error TS2339: Property 'users' does not exist on type '{}'.
server/storage-example-enhanced.ts(260,23): error TS18046: 'error' is of type 'unknown'.
server/utils/deltaUtils.ts(495,15): error TS2484: Export declaration conflicts with exported declaration of 'VersionedEntity'.
server/utils/secure-url-validation.ts(3,24): error TS2307: Cannot find module '../../../shared/constants' or its corresponding type declarations.
server/utils/secure-url-validation.ts(109,60): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Error'.
server/utils/secure-url-validation.ts(121,67): error TS2345: Argument of type 'string' is not assignable to parameter of type 'Error'.
server/utils/secure-url-validation.ts(132,11): error TS2769: No overload matches this call.
  Overload 1 of 2, '(input: string | URL | Request, init?: RequestInit | undefined): Promise<Response>', gave the following error.
    Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.
  Overload 2 of 2, '(input: URL | RequestInfo, init?: RequestInit | undefined): Promise<Response>', gave the following error.
    Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.
server/utils/secure-url-validation.ts(165,18): error TS2345: Argument of type 'Error | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
server/utils/storageErrorWrapper.ts(89,101): error TS2554: Expected 1-3 arguments, but got 4.
server/utils/storageErrorWrapper.ts(207,102): error TS2554: Expected 1-3 arguments, but got 4.
server/utils/storageErrorWrapper.ts(254,59): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.
