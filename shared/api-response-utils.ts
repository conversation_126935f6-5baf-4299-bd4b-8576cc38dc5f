/**
 * Standardized API Response Utilities
 * Provides consistent response creation and error handling across all endpoints
 */

import { Response } from 'express';
import { 
  ApiResponse, 
  SuccessResponse, 
  ErrorResponse, 
  PaginatedResponse, 
  ApiError, 
  ERROR_CODES, 
  ERROR_CODE_TO_STATUS,
  HTTP_STATUS,
  HttpStatus,
  ErrorCode
} from './api-response-types';

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message: string | null = null,
  requestId?: string
): SuccessResponse<T> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(
  code: ErrorCode | string,
  message: string,
  details?: any,
  requestId?: string,
  field?: string
): ErrorResponse {
  const error: ApiError = {
    code,
    message,
    details,
    requestId,
    field
  };

  return {
    success: false,
    data: null,
    message: null,
    error,
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Creates a standardized paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  message: string | null = null,
  requestId?: string
): PaginatedResponse<T> {
  const totalPages = Math.ceil(pagination.total / pagination.limit);
  
  return {
    success: true,
    data,
    message,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1
    },
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Sends a success response with consistent format
 */
export function sendSuccess<T>(
  res: Response,
  data: T,
  message: string | null = null,
  statusCode: HttpStatus = HTTP_STATUS.OK,
  requestId?: string
): Response {
  const response = createSuccessResponse(data, message, requestId);
  return res.status(statusCode).json(response);
}

/**
 * Sends an error response with consistent format
 */
export function sendError(
  res: Response,
  code: ErrorCode | string,
  message: string,
  statusCode?: HttpStatus,
  details?: any,
  requestId?: string,
  field?: string
): Response {
  const response = createErrorResponse(code, message, details, requestId, field);
  const status = statusCode || ERROR_CODE_TO_STATUS[code] || HTTP_STATUS.INTERNAL_SERVER_ERROR;
  
  return res.status(status).json(response);
}

/**
 * Sends a paginated response with consistent format
 */
export function sendPaginatedResponse<T>(
  res: Response,
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  message: string | null = null,
  statusCode: HttpStatus = HTTP_STATUS.OK,
  requestId?: string
): Response {
  const response = createPaginatedResponse(data, pagination, message, requestId);
  return res.status(statusCode).json(response);
}

/**
 * Sends a validation error response
 */
export function sendValidationError(
  res: Response,
  message: string = 'Validation failed',
  details?: any,
  requestId?: string,
  field?: string
): Response {
  return sendError(
    res,
    ERROR_CODES.VALIDATION_ERROR,
    message,
    HTTP_STATUS.BAD_REQUEST,
    details,
    requestId,
    field
  );
}

/**
 * Sends an authentication error response
 */
export function sendAuthenticationError(
  res: Response,
  message: string = 'Authentication required',
  requestId?: string
): Response {
  return sendError(
    res,
    ERROR_CODES.AUTHENTICATION_ERROR,
    message,
    HTTP_STATUS.UNAUTHORIZED,
    undefined,
    requestId
  );
}

/**
 * Sends an authorization error response
 */
export function sendAuthorizationError(
  res: Response,
  message: string = 'Insufficient permissions',
  requestId?: string
): Response {
  return sendError(
    res,
    ERROR_CODES.AUTHORIZATION_ERROR,
    message,
    HTTP_STATUS.FORBIDDEN,
    undefined,
    requestId
  );
}

/**
 * Sends a not found error response
 */
export function sendNotFoundError(
  res: Response,
  resource: string = 'Resource',
  requestId?: string
): Response {
  return sendError(
    res,
    ERROR_CODES.NOT_FOUND,
    `${resource} not found`,
    HTTP_STATUS.NOT_FOUND,
    undefined,
    requestId
  );
}

/**
 * Sends a conflict error response
 */
export function sendConflictError(
  res: Response,
  message: string,
  requestId?: string
): Response {
  return sendError(
    res,
    ERROR_CODES.CONFLICT,
    message,
    HTTP_STATUS.CONFLICT,
    undefined,
    requestId
  );
}

/**
 * Sends an internal server error response
 */
export function sendInternalError(
  res: Response,
  message: string = 'Internal server error',
  requestId?: string,
  details?: any
): Response {
  return sendError(
    res,
    ERROR_CODES.INTERNAL_ERROR,
    message,
    HTTP_STATUS.INTERNAL_SERVER_ERROR,
    details,
    requestId
  );
}

/**
 * Sends a rate limit error response
 */
export function sendRateLimitError(
  res: Response,
  message: string = 'Rate limit exceeded',
  requestId?: string,
  retryAfter?: number
): Response {
  const details = retryAfter ? { retryAfter } : undefined;
  
  if (retryAfter) {
    res.setHeader('Retry-After', retryAfter.toString());
  }
  
  return sendError(
    res,
    ERROR_CODES.RATE_LIMIT_ERROR,
    message,
    HTTP_STATUS.TOO_MANY_REQUESTS,
    details,
    requestId
  );
}

/**
 * Sends an OTP error response
 */
export function sendOtpError(
  res: Response,
  type: 'expired' | 'invalid' | 'limit_exceeded',
  message?: string,
  requestId?: string
): Response {
  const errorCodeMap = {
    expired: ERROR_CODES.OTP_EXPIRED,
    invalid: ERROR_CODES.OTP_INVALID,
    limit_exceeded: ERROR_CODES.OTP_LIMIT_EXCEEDED
  };

  const defaultMessageMap = {
    expired: 'OTP has expired',
    invalid: 'Invalid OTP',
    limit_exceeded: 'OTP request limit exceeded'
  };

  const code = errorCodeMap[type];
  const defaultMessage = defaultMessageMap[type];
  const status = type === 'limit_exceeded' ? HTTP_STATUS.TOO_MANY_REQUESTS : HTTP_STATUS.BAD_REQUEST;

  return sendError(
    res,
    code,
    message || defaultMessage,
    status,
    undefined,
    requestId
  );
}

/**
 * Sends a file upload error response
 */
export function sendFileUploadError(
  res: Response,
  type: 'invalid_type' | 'too_large' | 'upload_failed',
  message?: string,
  requestId?: string
): Response {
  const errorCodeMap = {
    invalid_type: ERROR_CODES.INVALID_FILE_TYPE,
    too_large: ERROR_CODES.FILE_TOO_LARGE,
    upload_failed: ERROR_CODES.UPLOAD_FAILED
  };

  const defaultMessageMap = {
    invalid_type: 'Invalid file type',
    too_large: 'File size exceeds limit',
    upload_failed: 'File upload failed'
  };

  const code = errorCodeMap[type];
  const defaultMessage = defaultMessageMap[type];
  const status = type === 'upload_failed' ? HTTP_STATUS.INTERNAL_SERVER_ERROR : HTTP_STATUS.BAD_REQUEST;

  return sendError(
    res,
    code,
    message || defaultMessage,
    status,
    undefined,
    requestId
  );
}

/**
 * Extracts request ID from Express request
 */
export function getRequestId(req: any): string | undefined {
  return req.headers['x-request-id'] || req.requestId;
}

/**
 * Utility to check if response is success
 */
export function isSuccessResponse<T>(response: ApiResponse<T>): response is SuccessResponse<T> {
  return response.success === true;
}

/**
 * Utility to check if response is error
 */
export function isErrorResponse(response: ApiResponse): response is ErrorResponse {
  return response.success === false;
}

/**
 * Utility to check if response is paginated
 */
export function isPaginatedResponse<T>(response: ApiResponse<T[]>): response is PaginatedResponse<T> {
  return isSuccessResponse(response) && 'pagination' in response;
}

/**
 * Creates a standardized error from various error types
 */
export function standardizeError(error: any): { code: ErrorCode | string; message: string; details?: any } {
  // Handle Zod validation errors
  if (error.name === 'ZodError') {
    const details = error.errors?.map((err: any) => ({
      field: err.path.join('.'),
      message: err.message
    }));
    
    return {
      code: ERROR_CODES.VALIDATION_ERROR,
      message: 'Validation failed',
      details
    };
  }

  // Handle custom app errors
  if (error.code && typeof error.code === 'string') {
    return {
      code: error.code,
      message: error.message || 'An error occurred',
      details: error.details
    };
  }

  // Handle database errors
  if (error.name === 'DatabaseError' || error.code === 'ER_DUP_ENTRY') {
    return {
      code: ERROR_CODES.DATABASE_ERROR,
      message: 'Database operation failed',
      details: error.message
    };
  }

  // Handle network/external service errors
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    return {
      code: ERROR_CODES.EXTERNAL_SERVICE_ERROR,
      message: 'External service unavailable'
    };
  }

  // Default to internal error
  return {
    code: ERROR_CODES.INTERNAL_ERROR,
    message: error.message || 'An unexpected error occurred'
  };
}

/**
 * Middleware to add consistent error handling to route handlers
 */
export function withErrorHandling(
  handler: (req: any, res: Response, next?: any) => Promise<any>
) {
  return async (req: any, res: Response, next: any) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      const requestId = getRequestId(req);
      const standardError = standardizeError(error);
      
      sendError(
        res,
        standardError.code,
        standardError.message,
        undefined,
        standardError.details,
        requestId
      );
    }
  };
}