/**
 * Consolidated Validation Utilities
 * Centralized validation patterns to reduce code duplication
 */

import { z } from 'zod';

// ============================================================================
// BASIC FIELD VALIDATORS
// ============================================================================

/**
 * Email validation with comprehensive rules
 */
export const emailValidator = z.string()
  .min(1, "Email is required")
  .email("Please enter a valid email address")
  .max(255, "Email must be less than 255 characters")
  .transform(val => val.trim().toLowerCase());

/**
 * Phone number validation (international format)
 */
export const phoneValidator = z.string()
  .min(1, "Phone number is required")
  .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number")
  .max(15, "Phone number must be less than 15 digits")
  .transform(val => val.trim());

/**
 * Indian phone number validation (10 digits starting with 6-9)
 */
export const indianPhoneValidator = z.string()
  .regex(/^[6-9]\d{9}$/, 'Invalid Indian phone number format')
  .transform(val => val.trim());

/**
 * Strong password validation
 */
export const strongPasswordValidator = z.string()
  .min(8, "Password must be at least 8 characters")
  .max(128, "Password must be less than 128 characters")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/\d/, "Password must contain at least one number")
  .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character");

/**
 * Basic password validation (for less strict requirements)
 */
export const basicPasswordValidator = z.string()
  .min(6, "Password must be at least 6 characters")
  .max(128, "Password must be less than 128 characters");

/**
 * URL validation
 */
export const urlValidator = z.string()
  .url("Please enter a valid URL")
  .max(2048, "URL must be less than 2048 characters");

/**
 * Positive integer ID validation
 */
export const idValidator = z.coerce.number()
  .int("ID must be an integer")
  .positive("ID must be a positive number");

/**
 * Non-empty string validation with trimming
 */
export const nonEmptyStringValidator = (fieldName: string, maxLength: number = 255) =>
  z.string()
    .min(1, `${fieldName} is required`)
    .max(maxLength, `${fieldName} must be less than ${maxLength} characters`)
    .transform(val => val.trim());

/**
 * Optional non-empty string validation
 */
export const optionalStringValidator = (maxLength: number = 255) =>
  z.string()
    .max(maxLength, `Field must be less than ${maxLength} characters`)
    .transform(val => val.trim())
    .optional();

// ============================================================================
// PRICE & NUMERIC VALIDATORS
// ============================================================================

/**
 * Enhanced price validation with currency formatting support
 */
export const priceValidator = z.coerce.number()
  .positive("Price must be greater than 0")
  .min(1, "Price must be at least ₹1")
  .max(999999, "Price must be less than ₹10,00,000")
  .multipleOf(0.01, "Price can have at most 2 decimal places")
  .refine(val => val >= 100, "Price must be at least ₹100 for valid bookings");

/**
 * Optional price validation with zero handling
 */
export const optionalPriceValidator = z.coerce.number()
  .min(0, "Price cannot be negative")
  .max(999999, "Price must be less than ₹10,00,000")
  .multipleOf(0.01, "Price can have at most 2 decimal places")
  .refine(val => val === 0 || val >= 100, "Price must be ₹0 (for fallback) or at least ₹100")
  .optional();

/**
 * Price range validation factory with currency support
 */
export const createPriceRangeValidator = () => z.object({
  minPrice: optionalPriceValidator,
  maxPrice: optionalPriceValidator,
}).refine((data) => {
  if (data.minPrice && data.maxPrice && data.minPrice > 0 && data.maxPrice > 0) {
    return data.maxPrice >= data.minPrice;
  }
  return true;
}, {
  message: "Maximum price must be greater than or equal to minimum price",
  path: ["maxPrice"]
}).refine((data) => {
  if (data.minPrice && data.minPrice > 0 && data.minPrice < 100) {
    return false;
  }
  return true;
}, {
  message: "Minimum price must be at least ₹100",
  path: ["minPrice"]
});

/**
 * Count/quantity validation
 */
export const countValidator = z.coerce.number()
  .int("Count must be an integer")
  .min(0, "Count must be non-negative")
  .max(100, "Count must be less than or equal to 100");

// ============================================================================
// DATE & TIME VALIDATORS
// ============================================================================

/**
 * Future date validation
 */
export const futureDateValidator = z.string()
  .min(1, "Date is required")
  .refine((date) => {
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return selectedDate >= today;
  }, "Date must be today or in the future");

/**
 * Date string validation (YYYY-MM-DD format)
 */
export const dateStringValidator = z.string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
  .refine((date) => {
    const parsed = new Date(date);
    return !isNaN(parsed.getTime());
  }, "Invalid date");

/**
 * Optional future date validation
 */
export const optionalFutureDateValidator = futureDateValidator.optional();

// ============================================================================
// FILE UPLOAD VALIDATORS
// ============================================================================

/**
 * Image file validation constants
 */
export const IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'] as const;
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const MAX_TOTAL_SIZE = 50 * 1024 * 1024; // 50MB

/**
 * Single file validation schema
 */
export const fileValidator = z.object({
  name: z.string().min(1, "File name is required"),
  size: z.number()
    .max(MAX_FILE_SIZE, `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`),
  type: z.string()
    .refine((type) => IMAGE_TYPES.includes(type as any), {
      message: `File type must be one of: ${IMAGE_TYPES.join(', ')}`
    })
});

/**
 * Multiple files validation
 */
export const filesValidator = z.array(fileValidator)
  .max(10, "Maximum 10 files allowed")
  .refine((files) => {
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    return totalSize <= MAX_TOTAL_SIZE;
  }, `Total file size must be less than ${MAX_TOTAL_SIZE / (1024 * 1024)}MB`);

/**
 * Client-side file validation helper
 */
export const validateFile = (file: File) => {
  const errors: string[] = [];
  
  if (!IMAGE_TYPES.includes(file.type as any)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${IMAGE_TYPES.join(', ')}`);
  }
  
  if (file.size > MAX_FILE_SIZE) {
    errors.push(`File size ${(file.size / (1024 * 1024)).toFixed(2)}MB exceeds maximum ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// ============================================================================
// TEXT CONTENT VALIDATORS
// ============================================================================

/**
 * HTML sanitization regex
 */
const HTML_REGEX = /<[^>]*>/g;
const SCRIPT_REGEX = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;

/**
 * Safe text validation (no HTML/scripts)
 */
export const safeTextValidator = (fieldName: string, maxLength: number = 1000) =>
  z.string()
    .max(maxLength, `${fieldName} must be less than ${maxLength} characters`)
    .refine(val => !SCRIPT_REGEX.test(val), `${fieldName} cannot contain script tags`)
    .transform(val => val.replace(HTML_REGEX, '').trim());

/**
 * Rich text validation (allows basic HTML)
 */
export const richTextValidator = (fieldName: string, maxLength: number = 5000) =>
  z.string()
    .max(maxLength, `${fieldName} must be less than ${maxLength} characters`)
    .refine(val => !SCRIPT_REGEX.test(val), `${fieldName} cannot contain script tags`)
    .transform(val => val.trim());

/**
 * Slug validation (URL-friendly string)
 */
export const slugValidator = z.string()
  .min(1, "Slug is required")
  .max(100, "Slug must be less than 100 characters")
  .regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens")
  .refine(val => !val.startsWith('-') && !val.endsWith('-'), "Slug cannot start or end with a hyphen");

// ============================================================================
// ARRAY & COLLECTION VALIDATORS
// ============================================================================

/**
 * String array validation with limits
 */
export const stringArrayValidator = (
  fieldName: string,
  maxItems: number = 20,
  maxItemLength: number = 50
) => z.array(
  z.string()
    .min(1, `${fieldName} item cannot be empty`)
    .max(maxItemLength, `${fieldName} item must be less than ${maxItemLength} characters`)
    .transform(val => val.trim())
)
  .max(maxItems, `Maximum ${maxItems} ${fieldName.toLowerCase()} allowed`)
  .refine(items => new Set(items).size === items.length, `${fieldName} must be unique`);

/**
 * Tags validation
 */
export const tagsValidator = stringArrayValidator("Tags", 10, 30);

/**
 * Amenities validation
 */
export const amenitiesValidator = stringArrayValidator("Amenities", 20, 50);

/**
 * Keywords validation
 */
export const keywordsValidator = stringArrayValidator("Keywords", 15, 25);

// ============================================================================
// COMPOSITE VALIDATORS
// ============================================================================

/**
 * Password confirmation validation
 */
export const createPasswordConfirmationValidator = (passwordField: string = 'password') =>
  z.object({
    [passwordField]: strongPasswordValidator,
    confirmPassword: z.string()
  }).refine((data) => data[passwordField] === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"]
  });

/**
 * Location validation
 */
export const locationValidator = z.object({
  address: nonEmptyStringValidator("Address", 500),
  city: nonEmptyStringValidator("City", 100),
  state: nonEmptyStringValidator("State", 100),
  country: nonEmptyStringValidator("Country", 100),
  zipCode: z.string()
    .regex(/^[A-Za-z0-9\s-]{3,10}$/, "Invalid postal code format")
    .transform(val => val.trim().toUpperCase())
});

/**
 * Contact information validation
 */
export const contactValidator = z.object({
  email: emailValidator,
  phone: phoneValidator,
  alternatePhone: phoneValidator.optional()
});

// ============================================================================
// PAGINATION & SEARCH VALIDATORS
// ============================================================================

/**
 * Pagination parameters validation
 */
export const paginationValidator = z.object({
  page: z.coerce.number().int().min(1, "Page must be at least 1").default(1),
  limit: z.coerce.number().int().min(1, "Limit must be at least 1").max(100, "Limit cannot exceed 100").default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

/**
 * Search query validation
 */
export const searchQueryValidator = z.object({
  q: z.string()
    .min(1, "Search query is required")
    .max(200, "Search query must be less than 200 characters")
    .transform(val => val.trim()),
  ...paginationValidator.shape
});

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create enum validator with custom error message
 */
export const createEnumValidator = <T extends string>(
  values: readonly T[],
  fieldName: string
) => z.enum(values as [T, ...T[]], {
  errorMap: () => ({ message: `${fieldName} must be one of: ${values.join(', ')}` })
});

/**
 * Create conditional validator based on another field
 */
export const createConditionalValidator = <T>(
  condition: (data: any) => boolean,
  validator: z.ZodType<T>,
  fallback: z.ZodType<T> = z.any()
) => z.any().superRefine((data, ctx) => {
  const targetValidator = condition(data) ? validator : fallback;
  const result = targetValidator.safeParse(data);
  
  if (!result.success) {
    result.error.issues.forEach(issue => {
      ctx.addIssue(issue);
    });
  }
});

/**
 * Validation error formatter
 */
export const formatValidationError = (error: z.ZodError) => {
  return error.issues.map(issue => ({
    field: issue.path.join('.'),
    message: issue.message
  }));
};

/**
 * Safe validation helper that returns result object
 */
export const safeValidate = <T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: Array<{ field: string; message: string }> } => {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { success: false, errors: formatValidationError(result.error) };
  }
};

// ============================================================================
// VALIDATION PRESETS
// ============================================================================

/**
 * Common validation presets for quick use
 */
export const ValidationPresets = {
  email: emailValidator,
  phone: phoneValidator,
  indianPhone: indianPhoneValidator,
  password: strongPasswordValidator,
  basicPassword: basicPasswordValidator,
  url: urlValidator,
  id: idValidator,
  price: priceValidator,
  futureDate: futureDateValidator,
  file: fileValidator,
  files: filesValidator,
  tags: tagsValidator,
  amenities: amenitiesValidator,
  location: locationValidator,
  contact: contactValidator,
  pagination: paginationValidator,
  search: searchQueryValidator
} as const;

export default ValidationPresets;