/**
 * CamelCase Schema Types
 * 
 * These types represent the camelCase versions of database entities
 * for use in API responses and frontend components.
 * 
 * ✅ ALL PROPERTIES USE CAMELCASE FOR CONSISTENCY
 */

import { z } from 'zod';

// ====================================
// USER TYPES (camelCase)
// ====================================

export interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;          // Transformed from full_name
  phone?: string;
  address?: string;
  bio?: string;
  role: 'user' | 'owner';
  isVerified: boolean;       // Transformed from is_verified
  termsAccepted: boolean;    // Transformed from terms_accepted
  privacyPolicyAccepted: boolean;    // Transformed from privacy_policy_accepted
  cookiePolicyAccepted: boolean;     // Transformed from cookie_policy_accepted
  dataProcessingConsent: boolean;    // Transformed from data_processing_consent
  marketingConsent: boolean;         // Transformed from marketing_consent
  consentTimestamp?: string;         // Transformed from consent_timestamp
  createdAt: string;         // Transformed from created_at
}

export const userValidationSchema = z.object({
  username: z.string().min(3).max(30),
  email: z.string().email(),
  fullName: z.string().min(2).max(100),
  phone: z.string().optional(),
  address: z.string().optional(),
  bio: z.string().optional(),
  role: z.enum(['user', 'owner']).default('user'),
  termsAccepted: z.boolean().default(false),
  privacyPolicyAccepted: z.boolean().default(false),
  cookiePolicyAccepted: z.boolean().default(false),
  dataProcessingConsent: z.boolean().default(false),
  marketingConsent: z.boolean().default(false)
});

// ====================================
// PROPERTY TYPES (camelCase)
// ====================================

export interface Property {
  id: number;
  ownerId: number;                    // Transformed from owner_id
  title: string;
  description: string;
  location: string;
  halfDayPrice: number;               // Transformed from half_day_price
  fullDayPrice: number;               // Transformed from full_day_price
  weekdayHalfDayPrice?: number;       // Transformed from weekday_half_day_price
  weekdayFullDayPrice?: number;       // Transformed from weekday_full_day_price
  weekendHalfDayPrice?: number;       // Transformed from weekend_half_day_price
  weekendFullDayPrice?: number;       // Transformed from weekend_full_day_price
  images: string[];
  videos?: string[];
  amenities: string[];
  maxGuests: number;                  // Transformed from max_guests
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  status: 'active' | 'inactive' | 'pending';
  featured: boolean;
  avgRating?: number;                 // Transformed from avg_rating
  totalReviews: number;              // Transformed from total_reviews
  createdAt: string;                 // Transformed from created_at
  updatedAt?: string;                // Transformed from updated_at
}

export const propertyValidationSchema = z.object({
  title: z.string().min(5).max(200),
  description: z.string().min(10).max(2000),
  location: z.string().min(5).max(200),
  halfDayPrice: z.number().min(100).max(100000),
  fullDayPrice: z.number().min(100).max(100000),
  weekdayHalfDayPrice: z.number().min(100).max(100000).optional(),
  weekdayFullDayPrice: z.number().min(100).max(100000).optional(),
  weekendHalfDayPrice: z.number().min(100).max(100000).optional(),
  weekendFullDayPrice: z.number().min(100).max(100000).optional(),
  images: z.array(z.string().url()),
  videos: z.array(z.string().url()).optional(),
  amenities: z.array(z.string()),
  maxGuests: z.number().min(1).max(20),
  bedrooms: z.number().min(1).max(10).optional(),
  bathrooms: z.number().min(1).max(10).optional(),
  area: z.number().min(100).max(10000).optional(),
  status: z.enum(['active', 'inactive', 'pending']).default('pending'),
  featured: z.boolean().default(false)
});

// ====================================
// BOOKING TYPES (camelCase)
// ====================================

export interface Booking {
  id: number;
  propertyId: number;        // Transformed from property_id
  userId: number;            // Transformed from user_id
  guestName: string;         // Transformed from guest_name
  guestEmail: string;        // Transformed from guest_email
  guestPhone: string;        // Transformed from guest_phone
  bookingDate: string;       // Transformed from booking_date
  bookingType: 'morning' | 'full_day';  // Transformed from booking_type
  guests: number;
  totalPrice: number;        // Transformed from total_price
  specialRequests?: string;  // Transformed from special_requests
  status: 'pending' | 'confirmed' | 'rejected' | 'cancelled';
  createdAt: string;         // Transformed from created_at
  updatedAt?: string;        // Transformed from updated_at
}

export interface BookingWithDetails extends Booking {
  property: Property;
  guest: {
    id: number;
    fullName: string;
    username: string;
    email: string;
    phone?: string;
  };
}

export const bookingValidationSchema = z.object({
  propertyId: z.number().int().positive(),
  guestName: z.string().min(2).max(100),
  guestEmail: z.string().email(),
  guestPhone: z.string().min(10).max(15),
  bookingDate: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: "Invalid date format" }
  ),
  bookingType: z.enum(['morning', 'full_day']),
  guests: z.number().int().min(1).max(20),
  specialRequests: z.string().max(500).optional()
});

// ====================================
// REVIEW TYPES (camelCase)
// ====================================

export interface Review {
  id: number;
  propertyId: number;        // Transformed from property_id
  userId: number;            // Transformed from user_id
  bookingId?: number;        // Transformed from booking_id
  rating: 1 | 2 | 3 | 4 | 5;
  title: string;
  comment: string;
  images?: string[];
  helpful: number;
  flagged: boolean;
  createdAt: string;         // Transformed from created_at
  updatedAt?: string;        // Transformed from updated_at
}

export interface ReviewWithDetails extends Review {
  property: {
    id: number;
    title: string;
    location: string;
  };
  user: {
    id: number;
    fullName: string;
    username: string;
  };
}

export const reviewValidationSchema = z.object({
  propertyId: z.number().int().positive(),
  bookingId: z.number().int().positive().optional(),
  rating: z.number().int().min(1).max(5),
  title: z.string().min(5).max(200),
  comment: z.string().min(10).max(2000),
  images: z.array(z.string().url()).optional()
});

// ====================================
// PAYMENT TYPES (camelCase)
// ====================================

export interface PaymentOrder {
  id: number;
  bookingId: number;         // Transformed from booking_id
  userId: number;            // Transformed from user_id
  amount: number;
  currency: string;
  status: 'created' | 'attempted' | 'paid' | 'failed' | 'cancelled';
  gatewayOrderId?: string;   // Transformed from gateway_order_id
  gatewayResponse?: any;     // Transformed from gateway_response
  createdAt: string;         // Transformed from created_at
  updatedAt?: string;        // Transformed from updated_at
}

export interface PaymentTransaction {
  id: number;
  orderId: number;           // Transformed from order_id
  gatewayTransactionId?: string;  // Transformed from gateway_transaction_id
  method: string;
  amount: number;
  currency: string;
  status: 'pending' | 'success' | 'failed' | 'cancelled';
  gatewayResponse?: any;     // Transformed from gateway_response
  failureReason?: string;    // Transformed from failure_reason
  createdAt: string;         // Transformed from created_at
  updatedAt?: string;        // Transformed from updated_at
}

export const paymentValidationSchema = z.object({
  bookingId: z.number().int().positive(),
  amount: z.number().positive(),
  currency: z.string().length(3).default('INR'),
  method: z.string().optional()
});

// ====================================
// COMMON TYPES
// ====================================

export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
    field?: string;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    timestamp: string;
    requestId?: string;
  };
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  bookingType?: 'morning' | 'full_day';
  minRating?: number;
  amenities?: string[];
  maxGuests?: number;
  dateFrom?: string;
  dateTo?: string;
}

// ====================================
// DASHBOARD TYPES (camelCase)
// ====================================

export interface DashboardStats {
  totalProperties: number;    // Transformed from total_properties
  activeProperties: number;   // Transformed from active_properties
  totalBookings: number;      // Transformed from total_bookings
  pendingBookings: number;    // Transformed from pending_bookings
  confirmedBookings: number;  // Transformed from confirmed_bookings
  totalRevenue: number;       // Transformed from total_revenue
  monthlyRevenue: number;     // Transformed from monthly_revenue
  averageRating: number;      // Transformed from average_rating
  totalReviews: number;       // Transformed from total_reviews
  occupancyRate: number;      // Transformed from occupancy_rate
}

export interface BookingTrend {
  date: string;
  bookings: number;
  revenue: number;
}

export interface PropertyPerformance {
  propertyId: number;         // Transformed from property_id
  title: string;
  totalBookings: number;      // Transformed from total_bookings
  totalRevenue: number;       // Transformed from total_revenue
  averageRating: number;      // Transformed from average_rating
  occupancyRate: number;      // Transformed from occupancy_rate
}

// ====================================
// FORM TYPES (camelCase)
// ====================================

export interface LoginFormData {
  username: string;
  password: string;
  rememberMe?: boolean;       // Transformed from remember_me
}

export interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;    // Transformed from confirm_password
  fullName: string;           // Transformed from full_name
  phone?: string;
  termsAccepted: boolean;     // Transformed from terms_accepted
  privacyPolicyAccepted: boolean;  // Transformed from privacy_policy_accepted
}

export interface BookingFormData {
  propertyId: number;         // Transformed from property_id
  guestName: string;          // Transformed from guest_name
  guestEmail: string;         // Transformed from guest_email
  guestPhone: string;         // Transformed from guest_phone
  bookingDate: string;        // Transformed from booking_date
  bookingType: 'morning' | 'full_day';  // Transformed from booking_type
  guests: number;
  specialRequests?: string;   // Transformed from special_requests
  agreedToTerms: boolean;     // Transformed from agreed_to_terms
}

export interface PropertyFormData {
  title: string;
  description: string;
  location: string;
  halfDayPrice: number;       // Transformed from half_day_price
  fullDayPrice: number;       // Transformed from full_day_price
  weekdayHalfDayPrice?: number;  // Transformed from weekday_half_day_price
  weekdayFullDayPrice?: number;  // Transformed from weekday_full_day_price
  weekendHalfDayPrice?: number;  // Transformed from weekend_half_day_price
  weekendFullDayPrice?: number;  // Transformed from weekend_full_day_price
  images: string[];
  videos?: string[];
  amenities: string[];
  maxGuests: number;          // Transformed from max_guests
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
}

// ====================================
// EXPORT ALL TYPES
// ====================================

// Types are already exported with their interface definitions above