/**
 * Standardized API Response Types for Type Safety
 * Provides consistent response structures across all endpoints
 * 
 * ✅ ALL TYPES USE CAMELCASE FOR CONSISTENCY
 */

import { z } from 'zod';

// Base API Response Interface
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  message: string | null;
  error?: ApiError;
  timestamp: string;
  requestId?: string | undefined;
}

// Error Response Interface
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  requestId?: string | undefined;
  field?: string | undefined; // For validation errors
}

// Standardized Error Codes
export const ERROR_CODES = {
  // Authentication & Authorization
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  
  // Validation & Input
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  
  // Resources
  NOT_FOUND: 'NOT_FOUND',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  PROPERTY_NOT_FOUND: 'PROPERTY_NOT_FOUND',
  BOOKING_NOT_FOUND: 'BOOKING_NOT_FOUND',
  
  // Business Logic
  CONFLICT: 'CONFLICT',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  PHONE_ALREADY_EXISTS: 'PHONE_ALREADY_EXISTS',
  BOOKING_CONFLICT: 'BOOKING_CONFLICT',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // System & External
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // OTP & Communication
  OTP_EXPIRED: 'OTP_EXPIRED',
  OTP_INVALID: 'OTP_INVALID',
  OTP_LIMIT_EXCEEDED: 'OTP_LIMIT_EXCEEDED',
  SMS_SEND_FAILED: 'SMS_SEND_FAILED',
  EMAIL_SEND_FAILED: 'EMAIL_SEND_FAILED',
  
  // Property & Booking Specific
  PROPERTY_UNAVAILABLE: 'PROPERTY_UNAVAILABLE',
  BOOKING_ALREADY_CONFIRMED: 'BOOKING_ALREADY_CONFIRMED',
  BOOKING_CANCELLED: 'BOOKING_CANCELLED',
  INVALID_BOOKING_DATE: 'INVALID_BOOKING_DATE',
  
  // Upload & Files
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  INVALID_IMAGE_FORMAT: 'INVALID_IMAGE_FORMAT',
  IMAGE_PROCESSING_FAILED: 'IMAGE_PROCESSING_FAILED'
} as const;

// Type for error codes
export type ErrorCode = keyof typeof ERROR_CODES;

// Success Response Type
export interface SuccessResponse<T = any> extends ApiResponse<T> {
  success: true;
  data: T;
  message: string | null;
  error?: never;
}

// Error Response Type
export interface ErrorResponse extends ApiResponse<null> {
  success: false;
  data: null;
  message: null;
  error: ApiError;
}

// Pagination Response Type
export interface PaginatedResponse<T = any> extends SuccessResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Common Response Types for specific endpoints
export interface AuthResponse {
  user: {
    id: number;
    email: string;
    fullName: string;
    role: string;
    profileImage?: string;
  };
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

export interface PropertyResponse {
  id: number;
  title: string;
  description: string;
  location: string;
  halfDayPrice: number;
  fullDayPrice: number;
  bedrooms: number;
  bathrooms: number;
  amenities: string[];
  images: string[];
  status: 'active' | 'inactive' | 'draft';
  featured: boolean;
  owner: {
    id: number;
    fullName: string;
    email: string;
    phone?: string;
  };
  availability?: {
    available: boolean;
    nextAvailableDate?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface BookingResponse {
  id: number;
  propertyId: number;
  userId: number;
  bookingDate: string;
  bookingType: 'morning' | 'full_day';
  guests: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  specialRequests?: string;
  totalAmount: number;
  property: {
    id: number;
    title: string;
    location: string;
    images: string[];
  };
  user: {
    id: number;
    fullName: string;
    email: string;
    phone?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ReviewResponse {
  id: number;
  propertyId: number;
  userId: number;
  rating: number;
  comment?: string;
  response?: string;
  property: {
    id: number;
    title: string;
  };
  user: {
    id: number;
    fullName: string;
    profileImage?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UploadResponse {
  fileName: string;
  originalName: string;
  url: string;
  size: number;
  mimeType: string;
  uploadedAt: string;
}

export interface OtpResponse {
  identifier: string;
  type: 'email' | 'sms';
  expiresIn: number;
  message: string;
}

// Zod schemas for runtime validation
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().nullable(),
  message: z.string().nullable(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
    requestId: z.string().optional(),
    field: z.string().optional()
  }).optional(),
  timestamp: z.string(),
  requestId: z.string().optional()
});

export const PaginatedResponseSchema = ApiResponseSchema.extend({
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  })
});

// Helper type for API endpoints
export type ApiEndpoint<TRequest = any, TResponse = any> = (
  req: TRequest
) => Promise<SuccessResponse<TResponse> | ErrorResponse>;

// Common status codes mapping
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

// Type for HTTP status codes
export type HttpStatus = typeof HTTP_STATUS[keyof typeof HTTP_STATUS];

// Error code to HTTP status mapping
export const ERROR_CODE_TO_STATUS: Record<string, HttpStatus> = {
  [ERROR_CODES.AUTHENTICATION_ERROR]: HTTP_STATUS.UNAUTHORIZED,
  [ERROR_CODES.AUTHORIZATION_ERROR]: HTTP_STATUS.FORBIDDEN,
  [ERROR_CODES.TOKEN_EXPIRED]: HTTP_STATUS.UNAUTHORIZED,
  [ERROR_CODES.TOKEN_INVALID]: HTTP_STATUS.UNAUTHORIZED,
  [ERROR_CODES.VALIDATION_ERROR]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.INVALID_INPUT]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.NOT_FOUND]: HTTP_STATUS.NOT_FOUND,
  [ERROR_CODES.RESOURCE_NOT_FOUND]: HTTP_STATUS.NOT_FOUND,
  [ERROR_CODES.USER_NOT_FOUND]: HTTP_STATUS.NOT_FOUND,
  [ERROR_CODES.PROPERTY_NOT_FOUND]: HTTP_STATUS.NOT_FOUND,
  [ERROR_CODES.BOOKING_NOT_FOUND]: HTTP_STATUS.NOT_FOUND,
  [ERROR_CODES.CONFLICT]: HTTP_STATUS.CONFLICT,
  [ERROR_CODES.RESOURCE_ALREADY_EXISTS]: HTTP_STATUS.CONFLICT,
  [ERROR_CODES.EMAIL_ALREADY_EXISTS]: HTTP_STATUS.CONFLICT,
  [ERROR_CODES.PHONE_ALREADY_EXISTS]: HTTP_STATUS.CONFLICT,
  [ERROR_CODES.BOOKING_CONFLICT]: HTTP_STATUS.CONFLICT,
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: HTTP_STATUS.FORBIDDEN,
  [ERROR_CODES.INTERNAL_ERROR]: HTTP_STATUS.INTERNAL_SERVER_ERROR,
  [ERROR_CODES.DATABASE_ERROR]: HTTP_STATUS.INTERNAL_SERVER_ERROR,
  [ERROR_CODES.EXTERNAL_SERVICE_ERROR]: HTTP_STATUS.SERVICE_UNAVAILABLE,
  [ERROR_CODES.RATE_LIMIT_ERROR]: HTTP_STATUS.TOO_MANY_REQUESTS,
  [ERROR_CODES.SERVICE_UNAVAILABLE]: HTTP_STATUS.SERVICE_UNAVAILABLE,
  [ERROR_CODES.OTP_EXPIRED]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.OTP_INVALID]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.OTP_LIMIT_EXCEEDED]: HTTP_STATUS.TOO_MANY_REQUESTS,
  [ERROR_CODES.INVALID_FILE_TYPE]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.FILE_TOO_LARGE]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODES.UPLOAD_FAILED]: HTTP_STATUS.INTERNAL_SERVER_ERROR
};