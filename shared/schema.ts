import { pgTable, text, serial, integer, boolean, timestamp, json, doublePrecision, pgEnum, index } from "drizzle-orm/pg-core";
import { z } from "zod";
import { relations } from 'drizzle-orm';

// Enums
export const userRoleEnum = pgEnum('user_role', ['user', 'owner']);
export const bookingTypeEnum = pgEnum('booking_type', ['morning', 'full_day']);
export const reviewStarsEnum = pgEnum('review_stars', ['1', '2', '3', '4', '5']);

// User Table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  fullName: text("full_name").notNull(),
  phone: text("phone"),
  whatsappNumber: text("whatsapp_number"), // For owner-specific WhatsApp booking flows
  address: text("address"),
  bio: text("bio"),
  role: userRoleEnum("role").notNull().default('user'),
  isVerified: boolean("is_verified").default(false), // Keep existing column
  termsAccepted: boolean("terms_accepted").default(false),
  privacyPolicyAccepted: boolean("privacy_policy_accepted").default(false),
  cookiePolicyAccepted: boolean("cookie_policy_accepted").default(false),
  dataProcessingConsent: boolean("data_processing_consent").default(false),
  marketingConsent: boolean("marketing_consent").default(false),
  consentTimestamp: timestamp("consent_timestamp"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  phoneIdx: index("users_phone_idx").on(table.phone),
  whatsappIdx: index("users_whatsapp_idx").on(table.whatsappNumber),
  roleIdx: index("users_role_idx").on(table.role),
  createdAtIdx: index("users_created_at_idx").on(table.createdAt),
}));

export const usersRelations = relations(users, ({ many }) => ({
  properties: many(properties),
  bookings: many(bookings),
}));

// Properties Table
export const properties = pgTable("properties", {
  id: serial("id").primaryKey(),
  ownerId: integer("owner_id").notNull().references(() => users.id),
  title: text("title").notNull(),
  description: text("description").notNull(),
  location: text("location").notNull(),
  propertyType: text("property_type").notNull().default('farmhouse'),
  halfDayPrice: doublePrecision("half_day_price").notNull(),
  fullDayPrice: doublePrecision("full_day_price").notNull(),
  weekdayHalfDayPrice: doublePrecision("weekday_half_day_price"),
  weekdayFullDayPrice: doublePrecision("weekday_full_day_price"),
  weekendHalfDayPrice: doublePrecision("weekend_half_day_price"),
  weekendFullDayPrice: doublePrecision("weekend_full_day_price"),
  bedrooms: integer("bedrooms").notNull(),
  bathrooms: integer("bathrooms").notNull(),
  amenities: json("amenities").$type<string[]>().notNull(),
  images: json("images").$type<string[]>().notNull(),
  videos: json("videos").$type<string[]>().notNull().default([]),
  status: text("status").notNull().default('active'),
  featured: boolean("featured").default(false),
  latitude: doublePrecision("latitude"),
  longitude: doublePrecision("longitude"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  ownerIdIdx: index("properties_owner_id_idx").on(table.ownerId),
  locationIdx: index("properties_location_idx").on(table.location),
  statusIdx: index("properties_status_idx").on(table.status),
  featuredIdx: index("properties_featured_idx").on(table.featured),
  priceRangeIdx: index("properties_price_range_idx").on(table.halfDayPrice, table.fullDayPrice),
  createdAtIdx: index("properties_created_at_idx").on(table.createdAt),
}));

export const propertiesRelations = relations(properties, ({ one, many }) => ({
  owner: one(users, {
    fields: [properties.ownerId],
    references: [users.id],
  }),
  bookings: many(bookings),
}));

// Bookings Table
export const bookings = pgTable("bookings", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").notNull().references(() => properties.id),
  userId: integer("user_id").notNull().references(() => users.id),
  bookingDate: text("booking_date").notNull(), // Store as ISO string format (YYYY-MM-DD)
  bookingType: bookingTypeEnum("booking_type").notNull(),
  guests: integer("guests").notNull(),
  totalPrice: doublePrecision("total_price").notNull(),
  status: text("status").notNull().default('confirmed'),
  specialRequests: text("special_requests"),
  // Payment-related columns
  paymentStatus: text("payment_status").default("pending"),
  advanceAmount: integer("advance_amount"), // in paise
  remainingAmount: integer("remaining_amount"), // in paise
  gstAmount: integer("gst_amount"), // in paise
  paymentDueDate: text("payment_due_date"),
  paymentExpiry: timestamp("payment_expiry"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  propertyIdIdx: index("bookings_property_id_idx").on(table.propertyId),
  userIdIdx: index("bookings_user_id_idx").on(table.userId),
  bookingDateIdx: index("bookings_booking_date_idx").on(table.bookingDate),
  statusIdx: index("bookings_status_idx").on(table.status),
  paymentStatusIdx: index("bookings_payment_status_idx").on(table.paymentStatus),
  availabilityIdx: index("bookings_availability_idx").on(table.propertyId, table.bookingDate),
  createdAtIdx: index("bookings_created_at_idx").on(table.createdAt),
}));

export const bookingsRelations = relations(bookings, ({ one }) => ({
  property: one(properties, {
    fields: [bookings.propertyId],
    references: [properties.id],
  }),
  user: one(users, {
    fields: [bookings.userId],
    references: [users.id],
  }),
}));

// Reviews Table
export const reviews = pgTable("reviews", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").notNull().references(() => properties.id),
  userId: integer("user_id").notNull().references(() => users.id),
  bookingId: integer("booking_id").references(() => bookings.id),
  rating: reviewStarsEnum("rating").notNull(),
  comment: text("comment"),
  response: text("owner_response"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  propertyIdIdx: index("reviews_property_id_idx").on(table.propertyId),
  userIdIdx: index("reviews_user_id_idx").on(table.userId),
  bookingIdIdx: index("reviews_booking_id_idx").on(table.bookingId),
  ratingIdx: index("reviews_rating_idx").on(table.rating),
  createdAtIdx: index("reviews_created_at_idx").on(table.createdAt),
}));

export const reviewsRelations = relations(reviews, ({ one }) => ({
  property: one(properties, {
    fields: [reviews.propertyId],
    references: [properties.id],
  }),
  user: one(users, {
    fields: [reviews.userId],
    references: [users.id],
  }),
  booking: one(bookings, {
    fields: [reviews.bookingId],
    references: [bookings.id],
  }),
}));

// OTP Table for secure token storage
export const otpTokens = pgTable("otp_tokens", {
  id: serial("id").primaryKey(),
  identifier: text("identifier").notNull(), // email or phone
  code: text("code").notNull(),
  type: text("type").notNull(), // 'email' or 'sms'
  attempts: integer("attempts").notNull().default(0),
  isUsed: boolean("is_used").default(false), // Keep existing column
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  identifierIdx: index("otp_tokens_identifier_idx").on(table.identifier),
  typeIdx: index("otp_tokens_type_idx").on(table.type),
  expiresAtIdx: index("otp_tokens_expires_at_idx").on(table.expiresAt),
  createdAtIdx: index("otp_tokens_created_at_idx").on(table.createdAt),
}));

// Owner Interest Requests Table
export const ownerInterestRequests = pgTable("owner_interest_requests", {
  id: serial("id").primaryKey(),
  fullName: text("full_name").notNull(),
  email: text("email").notNull(),
  phone: text("phone").notNull(),
  propertyLocation: text("property_location").notNull(),
  propertyType: text("property_type").notNull(),
  propertySize: text("property_size"),
  expectedRevenue: text("expected_revenue"),
  currentOccupancy: text("current_occupancy"),
  amenities: json("amenities").$type<string[]>().notNull().default([]),
  propertyDescription: text("property_description"),
  experience: text("experience"),
  availability: text("availability"),
  additionalInfo: text("additional_info"),
  status: text("status").notNull().default('pending'), // pending, contacted, approved, rejected
  contactedAt: timestamp("contacted_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  emailIdx: index("owner_interest_requests_email_idx").on(table.email),
  phoneIdx: index("owner_interest_requests_phone_idx").on(table.phone),
  statusIdx: index("owner_interest_requests_status_idx").on(table.status),
  createdAtIdx: index("owner_interest_requests_created_at_idx").on(table.createdAt),
}));

// Manual Zod Schemas for Validation (avoiding drizzle-zod compatibility issues)
export const insertUserSchema = z.object({
  username: z.string(),
  password: z.string(),
  email: z.string().email(),
  fullName: z.string(),
  phone: z.string().optional(),
  address: z.string().optional(),
  bio: z.string().optional(),
  role: z.enum(['user', 'owner']).optional(),
  termsAccepted: z.boolean().optional(),
  privacyPolicyAccepted: z.boolean().optional(),
  cookiePolicyAccepted: z.boolean().optional(),
  dataProcessingConsent: z.boolean().optional(),
  marketingConsent: z.boolean().optional()
});

export const insertPropertySchema = z.object({
  ownerId: z.number(),
  title: z.string(),
  description: z.string(),
  location: z.string(),
  halfDayPrice: z.number(),
  fullDayPrice: z.number(),
  weekdayHalfDayPrice: z.number().optional(),
  weekdayFullDayPrice: z.number().optional(),
  weekendHalfDayPrice: z.number().optional(),
  weekendFullDayPrice: z.number().optional(),
  bedrooms: z.number(),
  bathrooms: z.number(),
  amenities: z.array(z.string()),
  images: z.array(z.string()),
  videos: z.array(z.string()).default([]),
  status: z.string().default('active'),
  featured: z.boolean().default(false)
});

export const insertBookingSchema = z.object({
  propertyId: z.number(),
  userId: z.number(),
  bookingDate: z.string(),
  bookingType: z.enum(['morning', 'full_day']),
  guests: z.number(),
  totalPrice: z.number(),
  status: z.string().default('pending'),
  specialRequests: z.string().optional(),
  paymentStatus: z.string().default('pending'),
  advanceAmount: z.number().optional(),
  remainingAmount: z.number().optional(),
  gstAmount: z.number().optional(),
  paymentDueDate: z.string().optional(),
  paymentExpiry: z.date().optional()
});

export const insertReviewSchema = z.object({
  propertyId: z.number(),
  userId: z.number(),
  bookingId: z.number().optional(),
  rating: z.enum(['1', '2', '3', '4', '5']),
  comment: z.string().optional()
});

export const insertOtpTokenSchema = z.object({
  code: z.string(),
  type: z.string(),
  identifier: z.string(),
  attempts: z.number().default(0),
  expiresAt: z.date()
});

export const insertOwnerInterestSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  propertyLocation: z.string().min(2, "Property location is required"),
  propertyType: z.string().min(1, "Property type is required"),
  propertySize: z.string().optional(),
  expectedRevenue: z.string().optional(),
  currentOccupancy: z.string().optional(),
  amenities: z.array(z.string()).default([]),
  propertyDescription: z.string().optional(),
  experience: z.string().optional(),
  availability: z.string().optional(),
  additionalInfo: z.string().optional(),
  status: z.string().default('pending')
});

export const insertSmsTemplateSchema = z.object({
  key: z.string().min(1, "Template key is required"),
  name: z.string().min(1, "Template name is required"),
  content: z.string().min(1, "Template content is required"),
  dltTemplateId: z.string().min(1, "DLT template ID is required"),
  category: z.string().default('transactional'),
  variables: z.array(z.string()).default([]),
  status: z.string().default('active')
});

export const insertSmsLogSchema = z.object({
  templateId: z.number().optional(),
  recipientPhone: z.string().min(10, "Valid phone number is required"),
  messageContent: z.string().min(1, "Message content is required"),
  status: z.string().default('pending'),
  twilioMessageSid: z.string().optional(),
  errorMessage: z.string().optional()
});

export const smsTemplateFormSchema = insertSmsTemplateSchema.extend({
  content: z.string().min(10, "Template content must be at least 10 characters"),
  variables: z.array(z.string()).min(0, "Variables must be a valid array"),
  status: z.enum(['active', 'inactive', 'draft']).default('active')
});

// ===== CALENDAR TABLE VALIDATION SCHEMAS =====

export const insertCalendarBookingSchema = z.object({
  propertyId: z.number(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
  status: z.enum(['confirmed', 'tentative', 'blocked', 'cancelled']).default('confirmed'),
  bookingType: z.string().default('direct'),
  guestName: z.string().optional(),
  guestPhone: z.string().optional(),
  guestCount: z.number().int().positive().optional(),
  notes: z.string().optional(),
  source: z.string().default('website'),
  externalId: z.string().optional(),
  createdBy: z.number().optional()
}).refine(data => new Date(data.startDate) <= new Date(data.endDate), {
  message: "End date must be greater than or equal to start date",
  path: ["endDate"]
});

export const insertCalendarSyncStatusSchema = z.object({
  propertyId: z.number(),
  calendarType: z.string().min(1, "Calendar type is required"),
  lastSyncAt: z.date().optional(),
  syncToken: z.string().optional(),
  isActive: z.boolean().default(true),
  webhookUrl: z.string().url("Invalid webhook URL").optional(),
  syncSettings: z.record(z.any()).default({}),
  errorMessage: z.string().optional()
});

export const insertWhatsappConversationSchema = z.object({
  propertyId: z.number(),
  phoneNumber: z.string().min(10, "Valid phone number is required"),
  conversationState: z.record(z.any()).default({}),
  lastMessageAt: z.date().optional(),
  isActive: z.boolean().default(true),
  metadata: z.record(z.any()).default({})
});

export const calendarBookingFormSchema = z.object({
  propertyId: z.number(),
  startDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format")
    .refine(date => new Date(date) >= new Date(new Date().toISOString().split('T')[0]), {
      message: "Start date cannot be in the past"
    }),
  endDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
  status: z.enum(['confirmed', 'tentative', 'blocked', 'cancelled']).default('confirmed'),
  bookingType: z.string().default('direct'),
  guestName: z.string().optional(),
  guestPhone: z.string().optional(),
  guestCount: z.number().int().positive("Number of guests must be a positive integer").optional(),
  notes: z.string().optional(),
  source: z.string().default('website'),
  externalId: z.string().optional(),
  createdBy: z.number().optional()
}).refine(data => new Date(data.startDate) <= new Date(data.endDate), {
  message: "End date must be greater than or equal to start date",
  path: ["endDate"]
});

// Input Schemas with additional validations
export const userRegisterSchema = insertUserSchema.extend({
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character")
    .optional(), // Make password optional for OTP-based registration
  email: z.string().email("Invalid email address"),
  phone: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number")
    .optional(),
  consentData: z.object({
    termsAccepted: z.boolean().optional(),
    dataProcessingConsent: z.boolean().optional(),
    marketingConsent: z.boolean().optional(),
    consentTimestamp: z.string().optional(),
  }).optional(),
});

export const userLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});



export const propertyFormSchema = insertPropertySchema.extend({
  amenities: z.array(z.string()).min(1, "At least one amenity is required"),
  images: z.array(z.string()).min(1, "At least one image is required"),
  videos: z.array(z.string()).default([]),
  halfDayPrice: z.number().positive("Price must be a positive number"),
  fullDayPrice: z.number().positive("Price must be a positive number"),
  weekdayHalfDayPrice: z.number().positive("Price must be a positive number").optional(),
  weekdayFullDayPrice: z.number().positive("Price must be a positive number").optional(),
  weekendHalfDayPrice: z.number().positive("Price must be a positive number").optional(),
  weekendFullDayPrice: z.number().positive("Price must be a positive number").optional(),
  bedrooms: z.number().int().positive("Number of bedrooms must be a positive integer"),
  bathrooms: z.number().int().positive("Number of bathrooms must be a positive integer"),
});

export const bookingFormSchema = insertBookingSchema.extend({
  guests: z.number().int().positive("Number of guests must be a positive integer"),
  paymentMethod: z.enum(['advance', 'full_amount'], {
    required_error: "Please select a payment method",
  }),
});

export const reviewFormSchema = insertReviewSchema.extend({
  rating: z.enum(['1', '2', '3', '4', '5']),
  comment: z.string().min(5, "Review comment must be at least 5 characters").max(500, "Review comment cannot exceed 500 characters"),
});

export const reviewResponseSchema = z.object({
  response: z.string().min(5, "Response must be at least 5 characters").max(500, "Response cannot exceed 500 characters"),
});

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type UserRegister = z.infer<typeof userRegisterSchema>;
export type UserLogin = z.infer<typeof userLoginSchema>;

export type Property = typeof properties.$inferSelect;
export type InsertProperty = z.infer<typeof insertPropertySchema>;
export type PropertyForm = z.infer<typeof propertyFormSchema>;

export type Booking = typeof bookings.$inferSelect;
export type InsertBooking = z.infer<typeof insertBookingSchema>;
export type BookingForm = z.infer<typeof bookingFormSchema>;

export type Review = typeof reviews.$inferSelect;
export type InsertReview = z.infer<typeof insertReviewSchema>;
export type ReviewForm = z.infer<typeof reviewFormSchema>;
export type ReviewResponse = z.infer<typeof reviewResponseSchema>;

export type OtpToken = typeof otpTokens.$inferSelect;
export type InsertOtpToken = z.infer<typeof insertOtpTokenSchema>;

export type OwnerInterestRequest = typeof ownerInterestRequests.$inferSelect;
export type InsertOwnerInterest = z.infer<typeof insertOwnerInterestSchema>;

export type SmsTemplate = typeof smsTemplates.$inferSelect;
export type InsertSmsTemplate = z.infer<typeof insertSmsTemplateSchema>;
export type SmsTemplateForm = z.infer<typeof smsTemplateFormSchema>;

export type SmsLog = typeof smsLogs.$inferSelect;
export type InsertSmsLog = z.infer<typeof insertSmsLogSchema>;

export type PaymentOrder = typeof paymentOrders.$inferSelect;
export type InsertPaymentOrder = typeof paymentOrders.$inferInsert;

export type PaymentTransaction = typeof paymentTransactions.$inferSelect;
export type InsertPaymentTransaction = typeof paymentTransactions.$inferInsert;

export type GstRecord = typeof gstRecords.$inferSelect;
export type InsertGstRecord = typeof gstRecords.$inferInsert;

export type GstRateConfiguration = typeof gstRateConfigurations.$inferSelect;
export type InsertGstRateConfiguration = typeof gstRateConfigurations.$inferInsert;

export type PaymentAuditLog = typeof paymentAuditLogs.$inferSelect;
export type InsertPaymentAuditLog = typeof paymentAuditLogs.$inferInsert;

export type WebhookEvent = typeof webhookEvents.$inferSelect;
export type InsertWebhookEvent = typeof webhookEvents.$inferInsert;

export type IdempotencyKey = typeof idempotencyKeys.$inferSelect;
export type InsertIdempotencyKey = typeof idempotencyKeys.$inferInsert;

export type PaymentRole = typeof paymentRoles.$inferSelect;
export type InsertPaymentRole = typeof paymentRoles.$inferInsert;

export type UserPaymentRole = typeof userPaymentRoles.$inferSelect;
export type InsertUserPaymentRole = typeof userPaymentRoles.$inferInsert;

export type SecurityIncident = typeof securityIncidents.$inferSelect;
export type InsertSecurityIncident = typeof securityIncidents.$inferInsert;

export type EncryptionKey = typeof encryptionKeys.$inferSelect;
export type InsertEncryptionKey = typeof encryptionKeys.$inferInsert;

// Security table insert types
export type InsertSecuritySession = typeof securitySessions.$inferInsert;

export type InsertDeviceVerification = typeof deviceVerifications.$inferInsert;

// ===== CALENDAR TABLE TYPES =====

export type CalendarBooking = typeof calendarBookings.$inferSelect;
export type InsertCalendarBooking = z.infer<typeof insertCalendarBookingSchema>;
export type CalendarBookingForm = z.infer<typeof calendarBookingFormSchema>;

export type CalendarSyncStatus = typeof calendarSyncStatus.$inferSelect;
export type InsertCalendarSyncStatus = z.infer<typeof insertCalendarSyncStatusSchema>;

export type WhatsappConversation = typeof whatsappConversations.$inferSelect;
export type InsertWhatsappConversation = z.infer<typeof insertWhatsappConversationSchema>;

// SMS Templates Table
export const smsTemplates = pgTable("sms_templates", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  name: text("name").notNull(),
  content: text("content").notNull(),
  dltTemplateId: text("dlt_template_id").notNull(),
  category: text("category").notNull().default('transactional'),
  variables: json("variables").$type<string[]>().notNull().default([]),
  status: text("status").notNull().default('active'),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  keyIdx: index("sms_templates_key_idx").on(table.key),
  statusIdx: index("sms_templates_status_idx").on(table.status),
  categoryIdx: index("sms_templates_category_idx").on(table.category),
  createdAtIdx: index("sms_templates_created_at_idx").on(table.createdAt),
}));

// SMS Logs Table
export const smsLogs = pgTable("sms_logs", {
  id: serial("id").primaryKey(),
  templateId: integer("template_id").references(() => smsTemplates.id),
  recipientPhone: text("recipient_phone").notNull(),
  messageContent: text("message_content").notNull(),
  status: text("status").notNull().default('pending'),
  twilioMessageSid: text("twilio_message_sid"),
  errorMessage: text("error_message"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  templateIdIdx: index("sms_logs_template_id_idx").on(table.templateId),
  recipientPhoneIdx: index("sms_logs_recipient_phone_idx").on(table.recipientPhone),
  statusIdx: index("sms_logs_status_idx").on(table.status),
  createdAtIdx: index("sms_logs_created_at_idx").on(table.createdAt),
}));

export const smsTemplatesRelations = relations(smsTemplates, ({ many }) => ({
  logs: many(smsLogs),
}));

export const smsLogsRelations = relations(smsLogs, ({ one }) => ({
  template: one(smsTemplates, {
    fields: [smsLogs.templateId],
    references: [smsTemplates.id],
  }),
}));

// Payment Orders Table
export const paymentOrders = pgTable("payment_orders", {
  id: serial("id").primaryKey(),
  razorpayOrderId: text("razorpay_order_id").notNull().unique(),
  bookingId: integer("booking_id").references(() => bookings.id),
  idempotencyKey: text("idempotency_key").notNull().unique(),
  amount: integer("amount").notNull(), // Amount in paise (30% of total + GST)
  currency: text("currency").default("INR"),
  receipt: text("receipt"),
  status: text("status").default("created"), // created, attempted, paid, failed, expired
  attempts: integer("attempts").default(0),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  bookingIdIdx: index("payment_orders_booking_id_idx").on(table.bookingId),
  statusIdx: index("payment_orders_status_idx").on(table.status),
  idempotencyKeyIdx: index("payment_orders_idempotency_key_idx").on(table.idempotencyKey),
  createdAtIdx: index("payment_orders_created_at_idx").on(table.createdAt),
}));

// Payment Transactions Table (Enhanced Security)
export const paymentTransactions = pgTable("payment_transactions", {
  id: serial("id").primaryKey(),
  paymentOrderId: integer("payment_order_id").references(() => paymentOrders.id),
  razorpayPaymentId: text("razorpay_payment_id").unique(),
  razorpaySignatureHash: text("razorpay_signature_hash"), // Hashed signature for security
  signatureVerificationStatus: text("signature_verification_status").default("pending"), // pending, verified, failed
  amount: integer("amount").notNull(), // Amount in paise
  method: text("method"), // card, upi, netbanking, wallet
  bank: text("bank"),
  currency: text("currency").default("INR"), // Multi-currency support
  exchangeRate: doublePrecision("exchange_rate"),
  baseCurrency: text("base_currency").default("INR"),
  status: text("status").default("initiated"), // initiated, pending, authorized, captured, failed, refunded, partially_refunded
  failureCode: text("failure_code"),
  failureReason: text("failure_reason"),
  gatewayResponse: json("gateway_response"),
  // Security fields
  encryptionKeyId: text("encryption_key_id"), // For field-level encryption
  sensitiveDataHash: text("sensitive_data_hash"), // Hash of sensitive fields
  securityFlags: json("security_flags"), // Security-related flags and metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  paymentOrderIdIdx: index("payment_transactions_payment_order_id_idx").on(table.paymentOrderId),
  statusIdx: index("payment_transactions_status_idx").on(table.status),
  razorpayPaymentIdIdx: index("payment_transactions_razorpay_payment_id_idx").on(table.razorpayPaymentId),
  createdAtIdx: index("payment_transactions_created_at_idx").on(table.createdAt),
}));

// GST Records Table (Production-Ready Indian Tax System)
export const gstRecords = pgTable("gst_records", {
  id: serial("id").primaryKey(),
  bookingId: integer("booking_id").references(() => bookings.id),
  baseAmount: integer("base_amount").notNull(), // 30% of booking amount in paise
  transactionType: text("transaction_type").notNull(), // intrastate, interstate, export, import
  serviceType: text("service_type").notNull(), // accommodation, food, transport, etc.
  hsnSacCode: text("hsn_sac_code").notNull(), // HSN/SAC code for service classification
  // Business details
  supplierGstin: text("supplier_gstin"), // Property owner's GSTIN
  recipientGstin: text("recipient_gstin"), // Customer's GSTIN (if applicable)
  supplierState: text("supplier_state").notNull(), // State/UT code where service is provided
  recipientState: text("recipient_state").notNull(), // State/UT code of customer
  placeOfSupply: text("place_of_supply").notNull(), // Actual place of supply
  // GST Rate Configuration
  gstRateConfigId: integer("gst_rate_config_id").references(() => gstRateConfigurations.id),
  applicableDate: text("applicable_date").notNull(), // Date when these rates were applicable
  // Intrastate GST
  cgstRate: doublePrecision("cgst_rate").default(0.00),
  sgstRate: doublePrecision("sgst_rate").default(0.00),
  cgstAmount: integer("cgst_amount").default(0), // in paise
  sgstAmount: integer("sgst_amount").default(0), // in paise
  // Interstate GST
  igstRate: doublePrecision("igst_rate").default(0.00),
  igstAmount: integer("igst_amount").default(0), // in paise
  // Additional taxes (if applicable)
  cessRate: doublePrecision("cess_rate").default(0.00),
  cessAmount: integer("cess_amount").default(0), // in paise
  // Totals
  totalGst: integer("total_gst").notNull(), // in paise
  totalAmount: integer("total_amount").notNull(), // base + gst in paise
  // Compliance fields
  invoiceNumber: text("invoice_number"),
  invoiceDate: text("invoice_date"),
  irn: text("irn"), // Invoice Reference Number for e-invoicing
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  bookingIdIdx: index("gst_records_booking_id_idx").on(table.bookingId),
  transactionTypeIdx: index("gst_records_transaction_type_idx").on(table.transactionType),
  serviceTypeIdx: index("gst_records_service_type_idx").on(table.serviceType),
  supplierStateIdx: index("gst_records_supplier_state_idx").on(table.supplierState),
  applicableDateIdx: index("gst_records_applicable_date_idx").on(table.applicableDate),
  createdAtIdx: index("gst_records_created_at_idx").on(table.createdAt),
}));

// GST Rate Configuration Table (Dynamic Rate Management)
export const gstRateConfigurations = pgTable("gst_rate_configurations", {
  id: serial("id").primaryKey(),
  serviceType: text("service_type").notNull(),
  hsnSacCode: text("hsn_sac_code").notNull(),
  rateStructure: json("rate_structure").notNull(), // Flexible rate structure
  effectiveFrom: text("effective_from").notNull(),
  effectiveTo: text("effective_to"),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  serviceTypeIdx: index("gst_rate_config_service_type_idx").on(table.serviceType),
  hsnSacCodeIdx: index("gst_rate_config_hsn_sac_idx").on(table.hsnSacCode),
  effectiveDatesIdx: index("gst_rate_config_effective_dates_idx").on(table.effectiveFrom, table.effectiveTo),
  uniqueConfig: index("gst_rate_config_unique_idx").on(table.serviceType, table.hsnSacCode, table.effectiveFrom),
}));

// Payment Audit Logs Table
export const paymentAuditLogs = pgTable("payment_audit_logs", {
  id: serial("id").primaryKey(),
  paymentOrderId: integer("payment_order_id").references(() => paymentOrders.id),
  paymentTransactionId: integer("payment_transaction_id").references(() => paymentTransactions.id),
  action: text("action").notNull(),
  actorType: text("actor_type").notNull(), // user, system, admin, webhook
  actorId: integer("actor_id"),
  actorIp: text("actor_ip"),
  actorUserAgent: text("actor_user_agent"),
  beforeState: json("before_state"),
  afterState: json("after_state"),
  metadata: json("metadata"),
  securityContext: json("security_context"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  paymentOrderIdIdx: index("payment_audit_logs_payment_order_id_idx").on(table.paymentOrderId),
  actorIdIdx: index("payment_audit_logs_actor_id_idx").on(table.actorId),
  actionIdx: index("payment_audit_logs_action_idx").on(table.action),
  createdAtIdx: index("payment_audit_logs_created_at_idx").on(table.createdAt),
}));

// Webhook Events Table
export const webhookEvents = pgTable("webhook_events", {
  id: serial("id").primaryKey(),
  eventId: text("event_id").notNull().unique(),
  eventType: text("event_type").notNull(),
  payload: json("payload").notNull(),
  signatureHash: text("signature_hash").notNull(),
  status: text("status").default("received"), // received, processed, failed
  processedAt: timestamp("processed_at"),
  retryCount: integer("retry_count").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  eventIdIdx: index("webhook_events_event_id_idx").on(table.eventId),
  statusIdx: index("webhook_events_status_idx").on(table.status),
  createdAtIdx: index("webhook_events_created_at_idx").on(table.createdAt),
}));

// Idempotency Keys Table
export const idempotencyKeys = pgTable("idempotency_keys", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  resourceType: text("resource_type").notNull(),
  resourceId: integer("resource_id"),
  responseData: json("response_data"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at").notNull(),
}, (table) => ({
  keyIdx: index("idempotency_keys_key_idx").on(table.key),
  expiresAtIdx: index("idempotency_keys_expires_at_idx").on(table.expiresAt),
}));

// Role-Based Access Control Tables
export const paymentRoles = pgTable("payment_roles", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  description: text("description"),
  permissions: json("permissions").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const userPaymentRoles = pgTable("user_payment_roles", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  roleId: integer("role_id").references(() => paymentRoles.id),
  grantedBy: integer("granted_by").references(() => users.id),
  grantedAt: timestamp("granted_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"),
  isActive: boolean("is_active").default(true),
}, (table) => ({
  uniqueUserRole: index("user_payment_roles_unique_idx").on(table.userId, table.roleId),
}));

// Security Incidents Table
export const securityIncidents = pgTable("security_incidents", {
  id: serial("id").primaryKey(),
  incidentType: text("incident_type").notNull(),
  severity: text("severity").notNull(), // low, medium, high, critical
  description: text("description").notNull(),
  sourceIp: text("source_ip"),
  userId: integer("user_id").references(() => users.id),
  paymentOrderId: integer("payment_order_id").references(() => paymentOrders.id),
  incidentData: json("incident_data"),
  status: text("status").default("open"), // open, investigating, resolved, false_positive
  resolvedAt: timestamp("resolved_at"),
  resolvedBy: integer("resolved_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  incidentTypeIdx: index("security_incidents_type_idx").on(table.incidentType),
  severityIdx: index("security_incidents_severity_idx").on(table.severity),
  statusIdx: index("security_incidents_status_idx").on(table.status),
  createdAtIdx: index("security_incidents_created_at_idx").on(table.createdAt),
}));

// Field-Level Encryption Keys Table
export const encryptionKeys = pgTable("encryption_keys", {
  id: serial("id").primaryKey(),
  keyId: text("key_id").notNull().unique(),
  keyType: text("key_type").notNull(), // aes256, rsa2048, etc.
  encryptedKey: text("encrypted_key").notNull(),
  keyVersion: integer("key_version").notNull(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"),
}, (table) => ({
  keyIdIdx: index("encryption_keys_key_id_idx").on(table.keyId),
  isActiveIdx: index("encryption_keys_is_active_idx").on(table.isActive),
}));

// Security Sessions Table
export const securitySessions = pgTable("security_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  sessionType: text("session_type").notNull(), // payment_2fa, device_verification, etc.
  sessionData: json("session_data"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  isActive: boolean("is_active").default(true),
}, (table) => ({
  userIdIdx: index("security_sessions_user_id_idx").on(table.userId),
  sessionTypeIdx: index("security_sessions_session_type_idx").on(table.sessionType),
  expiresAtIdx: index("security_sessions_expires_at_idx").on(table.expiresAt),
}));

// Device Verifications Table  
export const deviceVerifications = pgTable("device_verifications", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  deviceId: text("device_id").notNull(),
  deviceFingerprint: text("device_fingerprint").notNull(),
  deviceInfo: json("device_info"), // browser, OS, etc.
  verificationMethod: text("verification_method").notNull(), // otp, email, etc.
  isVerified: boolean("is_verified").default(false),
  verifiedAt: timestamp("verified_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"),
}, (table) => ({
  userIdIdx: index("device_verifications_user_id_idx").on(table.userId),
  deviceIdIdx: index("device_verifications_device_id_idx").on(table.deviceId),
  isVerifiedIdx: index("device_verifications_is_verified_idx").on(table.isVerified),
}));

// ===== CALENDAR INTEGRATION TABLES =====

// Calendar Bookings Table - Enhanced bookings table with calendar integration
export const calendarBookings = pgTable("calendar_bookings", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").notNull().references(() => properties.id),
  startDate: text("start_date").notNull(), // Store as ISO date string (YYYY-MM-DD)
  endDate: text("end_date").notNull(), // Store as ISO date string (YYYY-MM-DD)
  status: text("status").notNull().default('confirmed'), // confirmed, tentative, blocked, cancelled
  bookingType: text("booking_type").default('direct'), // direct, external, blocked
  guestName: text("guest_name"),
  guestPhone: text("guest_phone"),
  guestCount: integer("guest_count"),
  notes: text("notes"),
  source: text("source").default('website'), // website, whatsapp, external_calendar, airbnb, etc.
  externalId: text("external_id"), // ID from external booking source
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  propertyIdIdx: index("calendar_bookings_property_id_idx").on(table.propertyId),
  dateRangeIdx: index("calendar_bookings_date_range_idx").on(table.propertyId, table.startDate, table.endDate),
  statusIdx: index("calendar_bookings_status_idx").on(table.status),
  sourceIdx: index("calendar_bookings_source_idx").on(table.source),
  createdByIdx: index("calendar_bookings_created_by_idx").on(table.createdBy),
  // Optimized index for overlap queries
  overlapIdx: index("calendar_bookings_overlap_idx").on(table.propertyId, table.startDate, table.endDate),
  createdAtIdx: index("calendar_bookings_created_at_idx").on(table.createdAt),
}));

// Calendar Sync Status Table - Track external calendar integrations
export const calendarSyncStatus = pgTable("calendar_sync_status", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").notNull().references(() => properties.id),
  calendarType: text("calendar_type").notNull(), // google, outlook, airbnb, vrbo, manual
  lastSyncAt: timestamp("last_sync_at"),
  syncToken: text("sync_token"), // For incremental sync
  isActive: boolean("is_active").default(true),
  webhookUrl: text("webhook_url"), // For webhook-based sync
  syncSettings: json("sync_settings").default({}), // Calendar-specific settings
  errorMessage: text("error_message"), // Last sync error if any
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  propertyIdIdx: index("calendar_sync_status_property_id_idx").on(table.propertyId),
  calendarTypeIdx: index("calendar_sync_status_calendar_type_idx").on(table.calendarType),
  isActiveIdx: index("calendar_sync_status_is_active_idx").on(table.isActive),
  uniquePropertyCalendar: index("calendar_sync_status_unique_idx").on(table.propertyId, table.calendarType),
}));

// WhatsApp Conversations Table - Track WhatsApp booking conversations
export const whatsappConversations = pgTable("whatsapp_conversations", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").notNull().references(() => properties.id),
  phoneNumber: text("phone_number").notNull(),
  conversationState: json("conversation_state").default({}), // Current conversation state
  lastMessageAt: timestamp("last_message_at"),
  isActive: boolean("is_active").default(true),
  metadata: json("metadata").default({}), // Additional conversation metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  propertyIdIdx: index("whatsapp_conversations_property_id_idx").on(table.propertyId),
  phoneNumberIdx: index("whatsapp_conversations_phone_number_idx").on(table.phoneNumber),
  isActiveIdx: index("whatsapp_conversations_is_active_idx").on(table.isActive),
  lastMessageAtIdx: index("whatsapp_conversations_last_message_at_idx").on(table.lastMessageAt),
  uniquePropertyPhone: index("whatsapp_conversations_unique_idx").on(table.propertyId, table.phoneNumber),
}));

// ===== CALENDAR TABLE RELATIONS =====

export const calendarBookingsRelations = relations(calendarBookings, ({ one }) => ({
  property: one(properties, {
    fields: [calendarBookings.propertyId],
    references: [properties.id],
  }),
  createdByUser: one(users, {
    fields: [calendarBookings.createdBy],
    references: [users.id],
  }),
}));

export const calendarSyncStatusRelations = relations(calendarSyncStatus, ({ one }) => ({
  property: one(properties, {
    fields: [calendarSyncStatus.propertyId],
    references: [properties.id],
  }),
}));

export const whatsappConversationsRelations = relations(whatsappConversations, ({ one }) => ({
  property: one(properties, {
    fields: [whatsappConversations.propertyId],
    references: [properties.id],
  }),
}));
