/**
 * Case Transformation Utilities
 * 
 * Provides automatic conversion between snake_case (database) and camelCase (API/Frontend)
 * to maintain consistent naming conventions across the application layers.
 */

/**
 * Convert string from snake_case to camelCase
 */
export const snakeToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * Convert string from camelCase to snake_case
 */
export const camelToSnake = (str: string): string => {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
};

/**
 * Recursively transform object keys from snake_case to camelCase
 * Handles nested objects, arrays, and primitive values
 */
export const toCamelCase = <T = any>(obj: any): T => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => toCamelCase(item)) as T;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const camelCaseObj: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = snakeToCamel(key);
      camelCaseObj[camelKey] = toCamelCase(value);
    }
    
    return camelCaseObj as T;
  }

  // Return primitive values as-is
  return obj;
};

/**
 * Recursively transform object keys from camelCase to snake_case
 * Handles nested objects, arrays, and primitive values
 */
export const toSnakeCase = <T = any>(obj: any): T => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => toSnakeCase(item)) as T;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const snakeCaseObj: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = camelToSnake(key);
      snakeCaseObj[snakeKey] = toSnakeCase(value);
    }
    
    return snakeCaseObj as T;
  }

  // Return primitive values as-is
  return obj;
};

/**
 * Transform database result to API-friendly camelCase format
 * This should be used when sending data from database to frontend
 */
export const transformDatabaseResult = <T = any>(data: any): T => {
  if (!data) return data;

  // Handle single object
  if (!Array.isArray(data)) {
    return toCamelCase<T>(data);
  }

  // Handle array of objects
  return data.map(item => toCamelCase(item)) as T;
};

/**
 * Transform API request data to database-friendly snake_case format
 * This should be used when receiving data from frontend to store in database
 */
export const transformApiRequest = <T = any>(data: any): T => {
  if (!data) return data;

  // Handle single object
  if (!Array.isArray(data)) {
    return toSnakeCase<T>(data);
  }

  // Handle array of objects
  return data.map(item => toSnakeCase(item)) as T;
};

/**
 * Express middleware to automatically transform request body from camelCase to snake_case
 * Apply this middleware to routes that need database interaction
 */
export const transformRequestMiddleware = (req: any, res: any, next: any) => {
  if (req.body && typeof req.body === 'object') {
    req.body = transformApiRequest(req.body);
  }
  
  // Also transform query parameters if needed
  if (req.query && typeof req.query === 'object') {
    req.query = transformApiRequest(req.query);
  }
  
  next();
};

/**
 * Express middleware to automatically transform response data from snake_case to camelCase
 * Apply this middleware to routes that return database data
 */
export const transformResponseMiddleware = (req: any, res: any, next: any) => {
  const originalJson = res.json;
  
  res.json = function(data: any) {
    if (data && typeof data === 'object') {
      // Transform the data portion of API responses
      if (data.success !== undefined && data.data !== undefined) {
        // Standard API response format
        data.data = transformDatabaseResult(data.data);
      } else {
        // Direct data response
        data = transformDatabaseResult(data);
      }
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Utility to transform specific field mappings
 * Useful for complex transformations that need custom handling
 */
export const transformWithMapping = <T = any>(
  obj: any, 
  fieldMapping: Record<string, string>
): T => {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => transformWithMapping(item, fieldMapping)) as T;
  }

  const transformed: any = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const mappedKey = fieldMapping[key] || key;
    transformed[mappedKey] = typeof value === 'object' && value !== null
      ? transformWithMapping(value, fieldMapping)
      : value;
  }
  
  return transformed as T;
};

/**
 * Common field mappings for database ↔ API transformations
 */
export const COMMON_FIELD_MAPPINGS = {
  // Database → API (snake_case → camelCase)
  DATABASE_TO_API: {
    'full_name': 'fullName',
    'created_at': 'createdAt',
    'updated_at': 'updatedAt',
    'owner_id': 'ownerId',
    'property_id': 'propertyId',
    'user_id': 'userId',
    'booking_id': 'bookingId',
    'booking_type': 'bookingType',
    'half_day_price': 'halfDayPrice',
    'full_day_price': 'fullDayPrice',
    'weekday_half_day_price': 'weekdayHalfDayPrice',
    'weekday_full_day_price': 'weekdayFullDayPrice',
    'weekend_half_day_price': 'weekendHalfDayPrice',
    'weekend_full_day_price': 'weekendFullDayPrice',
    'special_requests': 'specialRequests',
    'total_price': 'totalPrice',
    'booking_date': 'bookingDate',
    'is_verified': 'isVerified',
    'is_active': 'isActive',
    'phone_number': 'phoneNumber',
    'email_address': 'emailAddress',
  } as const,

  // API → Database (camelCase → snake_case)
  API_TO_DATABASE: {
    'fullName': 'full_name',
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'ownerId': 'owner_id',
    'propertyId': 'property_id',
    'userId': 'user_id',
    'bookingId': 'booking_id',
    'bookingType': 'booking_type',
    'halfDayPrice': 'half_day_price',
    'fullDayPrice': 'full_day_price',
    'weekdayHalfDayPrice': 'weekday_half_day_price',
    'weekdayFullDayPrice': 'weekday_full_day_price',
    'weekendHalfDayPrice': 'weekend_half_day_price',
    'weekendFullDayPrice': 'weekend_full_day_price',
    'specialRequests': 'special_requests',
    'totalPrice': 'total_price',
    'bookingDate': 'booking_date',
    'isVerified': 'is_verified',
    'isActive': 'is_active',
    'phoneNumber': 'phone_number',
    'emailAddress': 'email_address',
  } as const
};

/**
 * Transform database results using predefined field mappings
 */
export const transformDatabaseWithMapping = <T = any>(data: any): T => {
  return transformWithMapping<T>(data, COMMON_FIELD_MAPPINGS.DATABASE_TO_API);
};

/**
 * Transform API requests using predefined field mappings
 */
export const transformApiWithMapping = <T = any>(data: any): T => {
  return transformWithMapping<T>(data, COMMON_FIELD_MAPPINGS.API_TO_DATABASE);
};

/**
 * Type-safe transformation for specific entities
 * Note: These types are deprecated - use types from shared/schema-camelcase.ts instead
 */

/**
 * Validation helpers to ensure transformation integrity
 */
export const validateTransformation = (original: any, transformed: any): boolean => {
  if (typeof original !== typeof transformed) {
    return false;
  }

  if (Array.isArray(original)) {
    return Array.isArray(transformed) && original.length === transformed.length;
  }

  if (typeof original === 'object' && original !== null) {
    const originalKeys = Object.keys(original);
    const transformedKeys = Object.keys(transformed);
    
    // Should have same number of keys (though names may differ)
    return originalKeys.length === transformedKeys.length;
  }

  return true;
};

/**
 * Debug utility to log transformations
 */
export const debugTransformation = (
  original: any, 
  transformed: any, 
  label: string = 'Transformation'
) => {
  console.group(`🔄 ${label}`);
  console.log('Original:', original);
  console.log('Transformed:', transformed);
  console.log('Valid:', validateTransformation(original, transformed));
  console.groupEnd();
};

// Default export removed - prefer named exports for better tree shaking