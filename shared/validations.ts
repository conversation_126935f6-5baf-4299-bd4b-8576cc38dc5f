import { z } from 'zod';
import {
  emailValidator,
  phoneValidator,
  strongPasswordValidator,
  priceValidator,
  futureDateValidator,
  countValidator,
  nonEmptyStringValidator,
  safeTextValidator,
  amenitiesValidator,
  createEnumValidator,
  createPriceRangeValidator,
  createPasswordConfirmationValidator,
  ValidationPresets
} from './validation-utils';

// Backward compatibility - keep existing patterns
export const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
export const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Common field validations - refactored to use consolidated utilities
export const commonValidations = {
  email: emailValidator,
  phone: phoneValidator,
  password: strongPasswordValidator,
  name: z.string()
    .min(1, "Name is required")
    .max(100, "Name must be less than 100 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces")
    .transform(val => val.trim()),
  price: priceValidator.refine(val => val <= 100000, "Price cannot exceed ₹100,000"),
  rating: createEnumValidator(['1', '2', '3', '4', '5'], "Rating"),
  date: futureDateValidator,
  guests: countValidator
    .min(1, "At least 1 guest is required")
    .max(50, "Maximum 50 guests allowed"),
  location: nonEmptyStringValidator("Location", 200),
  description: safeTextValidator("Description", 2000).optional(),
  title: nonEmptyStringValidator("Title", 150),
  amenities: amenitiesValidator.optional(),
  images: z.array(z.string().url("Invalid image URL"))
    .max(10, "Maximum 10 images allowed")
    .optional()
};

// User Registration Validation
export const userRegistrationSchema = z.object({
  username: z.string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be less than 50 characters")
    .regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers, and underscores")
    .transform(val => val.trim()),
  email: commonValidations.email,
  password: commonValidations.password,
  fullName: commonValidations.name,
  phone: commonValidations.phone.optional(),
  address: safeTextValidator("Address", 500).optional(),
  bio: safeTextValidator("Bio", 1000).optional(),
  role: createEnumValidator(['user', 'owner'], "Role").default('user'),
  termsAccepted: z.boolean().refine(val => val === true, {
    message: "You must accept the terms and conditions"
  }),
  newsletterSubscribed: z.boolean().default(false),
  profileImage: z.string().url("Invalid profile image URL").optional(),
  dateOfBirth: z.string().optional(),
  emergencyContact: commonValidations.phone.optional()
});

// User Login Validation
export const userLoginSchema = z.object({
  identifier: nonEmptyStringValidator("Email or phone"),
  password: nonEmptyStringValidator("Password")
});

// OTP Request Validation
export const otpRequestSchema = z.object({
  identifier: nonEmptyStringValidator("Email or phone number")
    .refine((val) => {
      return emailRegex.test(val) || phoneRegex.test(val);
    }, "Please enter a valid email or phone number"),
  type: createEnumValidator(['email', 'sms'], "OTP type")
});

// OTP Verification Validation
export const otpVerificationSchema = z.object({
  identifier: nonEmptyStringValidator("Email or phone"),
  code: z.string()
    .min(4, "OTP must be at least 4 digits")
    .max(6, "OTP must be at most 6 digits")
    .regex(/^\d+$/, "OTP can only contain numbers"),
  type: createEnumValidator(['email', 'sms'], "OTP type")
});

// Property Form Validation with enhanced pricing logic
export const propertyFormSchema = z.object({
  title: commonValidations.title,
  description: commonValidations.description,
  location: commonValidations.location,
  halfDayPrice: commonValidations.price,
  fullDayPrice: commonValidations.price,
  weekdayHalfDayPrice: commonValidations.price.optional(),
  weekdayFullDayPrice: commonValidations.price.optional(),
  weekendHalfDayPrice: commonValidations.price.optional(),
  weekendFullDayPrice: commonValidations.price.optional(),
  bedrooms: countValidator
    .min(1, "At least 1 bedroom is required")
    .max(20, "Maximum 20 bedrooms allowed"),
  bathrooms: countValidator
    .min(1, "At least 1 bathroom is required")
    .max(10, "Maximum 10 bathrooms allowed"),
  amenities: commonValidations.amenities,
  images: commonValidations.images,
  featured: z.boolean().default(false),
  status: createEnumValidator(['active', 'draft'], "Status").default('active')
}).refine((data) => {
  // Base pricing validation
  return data.fullDayPrice >= data.halfDayPrice;
}, {
  message: "Full day price must be greater than or equal to half day price",
  path: ["fullDayPrice"]
}).refine((data) => {
  // Weekday pricing internal validation
  if (data.weekdayFullDayPrice && data.weekdayHalfDayPrice) {
    return data.weekdayFullDayPrice >= data.weekdayHalfDayPrice;
  }
  return true;
}, {
  message: "Weekday full day price must be greater than or equal to weekday half day price",
  path: ["weekdayFullDayPrice"]
}).refine((data) => {
  // Weekend pricing internal validation
  if (data.weekendFullDayPrice && data.weekendHalfDayPrice) {
    return data.weekendFullDayPrice >= data.weekendHalfDayPrice;
  }
  return true;
}, {
  message: "Weekend full day price must be greater than or equal to weekend half day price",
  path: ["weekendFullDayPrice"]
}).refine((data) => {
  // Enhanced weekend pricing validation with fallback logic
  const hasWeekendHalf = data.weekendHalfDayPrice !== undefined && data.weekendHalfDayPrice > 0;
  const hasWeekendFull = data.weekendFullDayPrice !== undefined && data.weekendFullDayPrice > 0;
  
  // Only validate weekend pricing consistency if both weekend prices are explicitly set
  if (hasWeekendHalf && hasWeekendFull) {
    return data.weekendFullDayPrice! >= data.weekendHalfDayPrice!;
  }
  
  // If only one weekend price is set, validate against fallback (weekday or base)
  if (hasWeekendHalf && data.weekendFullDayPrice === 0) {
    // Weekend half-day is set, full-day will fallback - ensure consistency
    const fallbackFullDay = data.weekdayFullDayPrice || data.fullDayPrice;
    return fallbackFullDay >= data.weekendHalfDayPrice!;
  }
  
  if (hasWeekendFull && data.weekendHalfDayPrice === 0) {
    // Weekend full-day is set, half-day will fallback - ensure consistency  
    const fallbackHalfDay = data.weekdayHalfDayPrice || data.halfDayPrice;
    return data.weekendFullDayPrice! >= fallbackHalfDay;
  }
  
  return true;
}, {
  message: "Weekend full day rate must be greater than or equal to weekend half day rate (including fallback rates)",
  path: ["weekendFullDayPrice"]
});

// Booking Form Validation
export const bookingFormSchema = z.object({
  propertyId: ValidationPresets.id,
  bookingDate: commonValidations.date,
  bookingType: createEnumValidator(['morning', 'full_day'], "Booking type"),
  guests: commonValidations.guests,
  specialRequests: safeTextValidator("Special requests", 500).optional()
});

// Review Form Validation
export const reviewFormSchema = z.object({
  propertyId: ValidationPresets.id,
  rating: commonValidations.rating,
  comment: safeTextValidator("Review comment", 1000).optional()
});

// Review Response Validation
export const reviewResponseSchema = z.object({
  response: safeTextValidator("Response", 500)
});

// Search/Filter Validation
export const propertySearchSchema = z.object({
  location: z.string().optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  date: z.string().optional(),
  amenities: z.array(z.string()).optional(),
  featured: z.boolean().optional()
}).refine((data) => {
  if (data.minPrice && data.maxPrice) {
    return data.maxPrice >= data.minPrice;
  }
  return true;
}, {
  message: "Maximum price must be greater than or equal to minimum price",
  path: ["maxPrice"]
});

// File Upload Validation - use consolidated file validators
export const fileUploadSchema = z.object({
  files: ValidationPresets.files
});

// Keep backward compatibility
export const imageUploadSchema = fileUploadSchema;

// Contact Form Validation
export const contactFormSchema = z.object({
  name: commonValidations.name,
  email: commonValidations.email,
  phone: commonValidations.phone.optional(),
  subject: nonEmptyStringValidator("Subject", 200),
  message: z.string()
    .min(10, "Message must be at least 10 characters")
    .max(2000, "Message must be less than 2000 characters")
    .refine(val => !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(val), "Message cannot contain script tags")
    .transform(val => val.replace(/<[^>]*>/g, '').trim())
});

// Profile Update Validation
export const profileUpdateSchema = z.object({
  fullName: commonValidations.name.optional(),
  email: commonValidations.email.optional(),
  phone: commonValidations.phone.optional(),
  address: safeTextValidator("Address", 500).optional(),
  bio: safeTextValidator("Bio", 1000).optional(),
  profileImage: z.string().url("Invalid profile image URL").optional(),
  dateOfBirth: z.string().optional(),
  emergencyContact: commonValidations.phone.optional(),
  newsletterSubscribed: z.boolean().optional()
});

// Password Change Validation 
export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: strongPasswordValidator,
  confirmPassword: z.string().min(1, "Password confirmation is required")
}).refine((data) => {
  return data.newPassword === data.confirmPassword;
}, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});

// Export all schemas for easy access
export const validationSchemas = {
  userRegistration: userRegistrationSchema,
  userLogin: userLoginSchema,
  otpRequest: otpRequestSchema,
  otpVerification: otpVerificationSchema,
  propertyForm: propertyFormSchema,
  bookingForm: bookingFormSchema,
  reviewForm: reviewFormSchema,
  reviewResponse: reviewResponseSchema,
  propertySearch: propertySearchSchema,
  imageUpload: imageUploadSchema,
  contactForm: contactFormSchema,
  profileUpdate: profileUpdateSchema,
  passwordChange: passwordChangeSchema
};

// Type exports for TypeScript
export type UserRegistrationData = z.infer<typeof userRegistrationSchema>;
export type UserLoginData = z.infer<typeof userLoginSchema>;
export type OTPRequestData = z.infer<typeof otpRequestSchema>;
export type OTPVerificationData = z.infer<typeof otpVerificationSchema>;
export type PropertyFormData = z.infer<typeof propertyFormSchema>;
export type BookingFormData = z.infer<typeof bookingFormSchema>;
export type ReviewFormData = z.infer<typeof reviewFormSchema>;
export type ReviewResponseData = z.infer<typeof reviewResponseSchema>;
export type PropertySearchData = z.infer<typeof propertySearchSchema>;
export type ImageUploadData = z.infer<typeof imageUploadSchema>;
export type ContactFormData = z.infer<typeof contactFormSchema>;
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;
export type PasswordChangeData = z.infer<typeof passwordChangeSchema>;