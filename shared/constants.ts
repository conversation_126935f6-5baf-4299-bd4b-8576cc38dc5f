/**
 * Shared Constants - Eliminating Magic Numbers for Better Maintainability
 * 
 * This file centralizes all hardcoded values used throughout the application,
 * organized by functional context for easy discovery and maintenance.
 */

// =============================================
// TIME CONSTANTS (all in milliseconds)
// =============================================

export const TIME_UNITS = {
  MILLISECOND: 1,
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
} as const;

export const TIMEOUTS = {
  // API Request Timeouts
  API_REQUEST_DEFAULT: 30 * TIME_UNITS.SECOND,
  API_REQUEST_LONG: 2 * TIME_UNITS.MINUTE,
  API_REQUEST_SHORT: 5 * TIME_UNITS.SECOND,
  
  // WebSocket Timeouts
  WEBSOCKET_CONNECT: 10 * TIME_UNITS.SECOND,
  WEBSOCKET_RECONNECT: 2 * TIME_UNITS.SECOND,
  WEBSOCKET_HEARTBEAT: 30 * TIME_UNITS.SECOND,
  
  // User Interface Timeouts
  TOAST_NOTIFICATION: 5 * TIME_UNITS.SECOND,
  LOADING_SPINNER: 500,
  DEBOUNCE_INPUT: 300,
  AUTO_SAVE: 3 * TIME_UNITS.SECOND,
  
  // Test Timeouts
  TEST_DEFAULT: 10 * TIME_UNITS.SECOND,
  TEST_INTEGRATION: 2 * TIME_UNITS.MINUTE,
  TEST_E2E: 5 * TIME_UNITS.MINUTE,
} as const;

export const INTERVALS = {
  // Polling Intervals
  POLLING_DEFAULT: 30 * TIME_UNITS.SECOND,
  POLLING_FREQUENT: 5 * TIME_UNITS.SECOND,
  POLLING_SLOW: 2 * TIME_UNITS.MINUTE,
  
  // Statistics and Monitoring
  STATS_UPDATE: 10 * TIME_UNITS.SECOND,
  PERFORMANCE_CHECK: 5 * TIME_UNITS.SECOND,
  BANDWIDTH_MONITOR: 5 * TIME_UNITS.SECOND,
  BATCH_UPDATE: 2 * TIME_UNITS.SECOND,
  
  // Health Checks
  HEALTH_CHECK: 30 * TIME_UNITS.SECOND,
  METRICS_COLLECTION: 1 * TIME_UNITS.MINUTE,
} as const;

export const DELAYS = {
  // User Experience Delays
  ANIMATION_SHORT: 150,
  ANIMATION_MEDIUM: 300,
  ANIMATION_LONG: 500,
  TRANSITION_SMOOTH: 200,
  
  // Network Retry Delays
  RETRY_INITIAL: 1 * TIME_UNITS.SECOND,
  RETRY_EXPONENTIAL_BASE: 2 * TIME_UNITS.SECOND,
  RETRY_MAX: 30 * TIME_UNITS.SECOND,
  
  // Audio Notification Delays
  SOUND_SEQUENCE_SHORT: 100,
  SOUND_SEQUENCE_MEDIUM: 150,
  SOUND_SEQUENCE_LONG: 300,
} as const;

// =============================================
// AUDIO CONSTANTS
// =============================================

export const AUDIO_FREQUENCIES = {
  // Musical Notes (Hz)
  C4: 261.63,
  C5: 523.25,
  E5: 659.25,
  G5: 783.99,
  A5: 880,
  C6: 1046.5,
} as const;

export const AUDIO_DURATIONS = {
  VERY_SHORT: 0.1,
  SHORT: 0.15,
  MEDIUM: 0.2,
  LONG: 0.25,
  VERY_LONG: 0.3,
} as const;

export const AUDIO_VOLUMES = {
  QUIET: 0.05,
  LOW: 0.1,
  MEDIUM: 0.12,
  HIGH: 0.15,
  LOUD: 0.2,
} as const;

export const NOTIFICATION_SOUND_DELAYS = {
  SUCCESS_E5: 150,
  SUCCESS_G5: 300,
  SUCCESS_CLEANUP: 500,
  
  ERROR_E5: 200,
  ERROR_CLEANUP: 500,
  
  WARNING_C5: 250,
  WARNING_CLEANUP: 500,
  
  INFO_E5: 100,
  INFO_CLEANUP: 300,
  
  BOOKING_E5: 150,
  BOOKING_G5: 300,
  BOOKING_C6: 450,
  BOOKING_CLEANUP: 700,
} as const;

// =============================================
// RATE LIMITING CONSTANTS
// =============================================

export const RATE_LIMITS = {
  // General API Limits
  GENERAL_API: {
    WINDOW_MS: 15 * TIME_UNITS.MINUTE,
    MAX_REQUESTS: 100,
    MAX_REQUESTS_DEV: 1000,
    RETRY_AFTER: 15 * TIME_UNITS.MINUTE,
  },
  
  // Authentication Limits
  AUTH: {
    WINDOW_MS: 15 * TIME_UNITS.MINUTE,
    MAX_REQUESTS: 20,
    MAX_REQUESTS_DEV: 100,
    RETRY_AFTER: 15 * TIME_UNITS.MINUTE,
  },
  
  // Booking Limits
  BOOKING: {
    WINDOW_MS: 1 * TIME_UNITS.MINUTE,
    MAX_REQUESTS: 5,
    RETRY_AFTER: 1 * TIME_UNITS.MINUTE,
  },
  
  // Search Limits
  SEARCH: {
    WINDOW_MS: 1 * TIME_UNITS.MINUTE,
    MAX_REQUESTS: 30,
    RETRY_AFTER: 1 * TIME_UNITS.MINUTE,
  },
  
  // Image/Media Limits
  MEDIA: {
    WINDOW_MS: 1 * TIME_UNITS.MINUTE,
    MAX_REQUESTS: 200,
    RETRY_AFTER: 1 * TIME_UNITS.MINUTE,
  },
  
  // OTP Limits
  OTP_SEND: {
    WINDOW_MS_DEV: 1 * TIME_UNITS.MINUTE,
    WINDOW_MS_PROD: 10 * TIME_UNITS.MINUTE,
    MAX_REQUESTS_DEV: 50,
    MAX_REQUESTS_PROD: 5,
  },
  
  OTP_VERIFY: {
    WINDOW_MS_DEV: 1 * TIME_UNITS.MINUTE,
    WINDOW_MS_PROD: 5 * TIME_UNITS.MINUTE,
    MAX_REQUESTS_DEV: 100,
    MAX_REQUESTS_PROD: 10,
  },
} as const;

// =============================================
// UI/UX CONSTANTS
// =============================================

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  SMALL_PAGE_SIZE: 10,
  LARGE_PAGE_SIZE: 50,
  MAX_PAGE_SIZE: 100,
} as const;

export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000,
} as const;

export const UI_DIMENSIONS = {
  // Loading Spinners
  SPINNER_SMALL: 16,
  SPINNER_MEDIUM: 32,
  SPINNER_LARGE: 64,
  
  // Image Sizes
  THUMBNAIL_SIZE: 150,
  PREVIEW_SIZE: 300,
  FULL_IMAGE_MAX: 1920,
  
  // Form Elements
  INPUT_HEIGHT: 40,
  BUTTON_HEIGHT: 36,
  TEXTAREA_MIN_HEIGHT: 80,
} as const;

export const BREAKPOINTS = {
  MOBILE: 640,
  TABLET: 768,
  DESKTOP: 1024,
  LARGE_DESKTOP: 1280,
} as const;

// =============================================
// BUSINESS LOGIC CONSTANTS
// =============================================

export const BOOKING_LIMITS = {
  MIN_GUESTS: 1,
  MAX_GUESTS: 20,
  MIN_ADVANCE_BOOKING_HOURS: 2,
  MAX_ADVANCE_BOOKING_DAYS: 365,
} as const;

export const FILE_UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES_PER_UPLOAD: 10,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/quicktime'],
} as const;

export const VALIDATION_LIMITS = {
  // Text Lengths
  USERNAME_MIN: 3,
  USERNAME_MAX: 30,
  PASSWORD_MIN: 8,
  PASSWORD_MAX: 128,
  NAME_MIN: 2,
  NAME_MAX: 100,
  DESCRIPTION_MIN: 10,
  DESCRIPTION_MAX: 2000,
  TITLE_MIN: 5,
  TITLE_MAX: 200,
  
  // Numeric Limits
  PRICE_MIN: 100,
  PRICE_MAX: 100000,
  RATING_MIN: 1,
  RATING_MAX: 5,
} as const;

// =============================================
// CACHE CONSTANTS
// =============================================

export const CACHE_DURATIONS = {
  // Query Cache Durations (React Query staleTime)
  VERY_SHORT: 1 * TIME_UNITS.MINUTE,
  SHORT: 5 * TIME_UNITS.MINUTE,
  MEDIUM: 15 * TIME_UNITS.MINUTE,
  LONG: 1 * TIME_UNITS.HOUR,
  VERY_LONG: 24 * TIME_UNITS.HOUR,
  
  // Browser Cache Durations
  STATIC_ASSETS: 30 * TIME_UNITS.DAY,
  API_RESPONSES: 5 * TIME_UNITS.MINUTE,
  USER_DATA: 10 * TIME_UNITS.MINUTE,
} as const;

// =============================================
// WEBSOCKET CONSTANTS
// =============================================

export const WEBSOCKET = {
  // Connection States
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
  
  // Reconnection Settings
  MAX_RECONNECT_ATTEMPTS: 5,
  RECONNECT_INTERVAL: 2 * TIME_UNITS.SECOND,
  INITIAL_RECONNECT_DELAY: 1 * TIME_UNITS.SECOND,
  MAX_RECONNECT_DELAY: 30 * TIME_UNITS.SECOND,
  
  // Close Codes
  NORMAL_CLOSURE: 1000,
  GOING_AWAY: 1001,
  PROTOCOL_ERROR: 1002,
  UNSUPPORTED_DATA: 1003,
  ABNORMAL_CLOSURE: 1006,
} as const;

// =============================================
// FEATURE FLAGS
// =============================================

export const FEATURE_FLAGS = {
  // Core MVP Features (Always Enabled)
  CORE: {
    DASHBOARD_STATS: true,
    BASIC_BOOKING_MANAGEMENT: true,
    PROPERTY_LISTING: true,
    NOTIFICATIONS: true,
  },
  
  // Real-Time Features
  REALTIME: {
    ENABLED: true,
    WEBSOCKET_ENABLED: true,
    POLLING_ENABLED: true,
    POLLING_INTERVAL: INTERVALS.POLLING_DEFAULT,
    RECONNECT_ATTEMPTS: WEBSOCKET.MAX_RECONNECT_ATTEMPTS,
    RECONNECT_INTERVAL: WEBSOCKET.RECONNECT_INTERVAL,
  },
  
  // Future Enhancements
  ENHANCEMENTS: {
    LISTING_MANAGEMENT: false,
    ADVANCED_ANALYTICS: false,
    GUEST_MESSAGING: false,
    AUTOMATED_PRICING: false,
    BULK_OPERATIONS: false,
    CALENDAR_INTEGRATION: false,
    REVENUE_OPTIMIZATION: false,
    MULTI_LANGUAGE: false,
  },
  
  // Experimental Features
  EXPERIMENTAL: {
    AI_INSIGHTS: false,
    DYNAMIC_PRICING: false,
    PREDICTIVE_ANALYTICS: false,
    VIRTUAL_TOURS: false,
  },
} as const;

// =============================================
// HTTP STATUS CODES
// =============================================

export const HTTP_STATUS = {
  // Success
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  
  // Client Errors
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  
  // Server Errors
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// =============================================
// ERROR CODES
// =============================================

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
} as const;

// =============================================
// ENVIRONMENT CONFIGURATION
// =============================================

export const ENVIRONMENT = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
  STAGING: 'staging',
} as const;

// Type exports for TypeScript usage
export type TimeUnit = typeof TIME_UNITS[keyof typeof TIME_UNITS];
export type Timeout = typeof TIMEOUTS[keyof typeof TIMEOUTS];
export type Interval = typeof INTERVALS[keyof typeof INTERVALS];
export type HttpStatus = typeof HTTP_STATUS[keyof typeof HTTP_STATUS];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type Environment = typeof ENVIRONMENT[keyof typeof ENVIRONMENT];