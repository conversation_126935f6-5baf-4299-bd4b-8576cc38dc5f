
import '@testing-library/jest-dom'
import { vi, beforeEach, afterEach } from 'vitest'

// Set test environment variables before any imports
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'sqlite://:memory:'
process.env.JWT_SECRET = 'test-jwt-secret-for-testing-only-32chars'
process.env.COOKIE_SECRET = 'test-cookie-secret-for-testing-only-32chars'
process.env.SESSION_SECRET = 'test-session-secret-for-testing-only-32chars'

// Setup TextEncoder/TextDecoder for all environments
import { TextEncoder, TextDecoder } from 'util'

if (typeof global.TextEncoder === 'undefined') {
  global.TextEncoder = TextEncoder
}

if (typeof global.TextDecoder === 'undefined') {
  global.TextDecoder = TextDecoder
}

// Only set up DOM-related mocks if window exists (jsdom environment)
if (typeof window !== 'undefined') {
  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    writable: true,
    value: vi.fn(),
  })
}

// Global mocks that work in both environments
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock fetch for tests to prevent URL errors in AuthContext
global.fetch = vi.fn().mockImplementation((url: string, options?: RequestInit) => {
  // Mock auth endpoints to prevent "Invalid URL" errors
  if (typeof url === 'string' && url.startsWith('/api/auth/')) {
    return Promise.resolve({
      ok: false,
      status: 401,
      json: () => Promise.resolve({ error: 'Not authenticated' })
    } as Response)
  }
  
  // Mock other API endpoints
  if (typeof url === 'string' && url.startsWith('/api/')) {
    return Promise.resolve({
      ok: false,
      status: 404,
      json: () => Promise.resolve({ error: 'Not found' })
    } as Response)
  }
  
  // For any other URLs, reject with network error
  return Promise.reject(new Error(`Network error: ${url}`))
})

// Razorpay mock is defined in individual test files where needed

// Clean up mocks between tests
beforeEach(() => {
  // Clear call history but keep the mock implementation
  vi.mocked(global.fetch).mockClear()
})
