/**
 * Owner Dashboard Integration Tests
 * 
 * End-to-end user flow tests that verify complete functionality
 * across multiple components and features working together.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'wouter/memory';
import userEvent from '@testing-library/user-event';

import OwnerDashboard from '@/pages/OwnerDashboard';
import { AuthContext } from '@/contexts/AuthContext';
import { 
  createTestQueryClient, 
  mockAuthContext, 
  mockProperties,
  mockBookings,
  simulateScenario,
  resetMockState 
} from '../setup';

function TestApp({ children, queryClient }: { children: React.ReactNode; queryClient: QueryClient }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={mockAuthContext}>
        <MemoryRouter initialEntries={['/owner/dashboard']}>
          {children}
        </MemoryRouter>
      </AuthContext.Provider>
    </QueryClientProvider>
  );
}

describe('Owner Dashboard Integration Flows', () => {
  let queryClient: QueryClient;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    user = userEvent.setup();
    resetMockState();
  });

  describe('Complete Property Management Flow', () => {
    it('should handle complete property lifecycle: create → edit → manage media → delete', async () => {
      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Step 1: Create new property
      const addPropertyButton = screen.getByTestId('add-property-button');
      await user.click(addPropertyButton);

      expect(screen.getByTestId('property-form-modal')).toBeInTheDocument();

      // Fill property form
      await user.type(screen.getByLabelText(/property name/i), 'New Beach House');
      await user.type(screen.getByLabelText(/location/i), 'Malibu Beach');
      await user.type(screen.getByLabelText(/description/i), 'Beautiful beachfront property');
      await user.type(screen.getByLabelText(/full day price/i), '800');
      await user.type(screen.getByLabelText(/half day price/i), '500');
      await user.type(screen.getByLabelText(/max guests/i), '8');

      // Submit form
      const submitButton = screen.getByTestId('submit-property');
      await user.click(submitButton);

      // Should show success message and close modal
      await waitFor(() => {
        expect(screen.getByText('Property created successfully')).toBeInTheDocument();
        expect(screen.queryByTestId('property-form-modal')).not.toBeInTheDocument();
      });

      // Should see new property in list
      await waitFor(() => {
        expect(screen.getByText('New Beach House')).toBeInTheDocument();
      });

      // Step 2: Edit the property
      const newPropertyCard = screen.getByTestId('property-card-3'); // Assuming it gets ID 3
      const editButton = within(newPropertyCard).getByTestId('edit-property-3');
      await user.click(editButton);

      // Should open edit modal with existing data
      expect(screen.getByDisplayValue('New Beach House')).toBeInTheDocument();

      // Update property details
      const nameInput = screen.getByDisplayValue('New Beach House');
      await user.clear(nameInput);
      await user.type(nameInput, 'Luxury Beach House');

      await user.click(screen.getByTestId('submit-property'));

      // Should see updated name
      await waitFor(() => {
        expect(screen.getByText('Luxury Beach House')).toBeInTheDocument();
      });

      // Step 3: Manage media for the property
      const mediaTab = within(newPropertyCard).getByTestId('media-tab');
      await user.click(mediaTab);

      // Should show media management section
      expect(screen.getByTestId('media-management-section')).toBeInTheDocument();

      // Upload an image
      const imageFile = new File([''], 'beach-house.jpg', { type: 'image/jpeg' });
      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, imageFile);

      // Should show file preview
      await waitFor(() => {
        expect(screen.getByText('beach-house.jpg')).toBeInTheDocument();
      });

      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should complete upload
      await waitFor(() => {
        expect(screen.getByText('Image uploaded successfully')).toBeInTheDocument();
      });

      // Step 4: Verify property appears in main dashboard with updated info
      const dashboardTab = screen.getByTestId('dashboard-tab');
      await user.click(dashboardTab);

      const updatedCard = screen.getByTestId('property-card-3');
      expect(within(updatedCard).getByText('Luxury Beach House')).toBeInTheDocument();
      expect(within(updatedCard).getByText('1 image')).toBeInTheDocument();

      // Step 5: Delete the property (after confirming no active bookings)
      const deleteButton = within(updatedCard).getByTestId('delete-property-3');
      await user.click(deleteButton);

      // Should show confirmation dialog
      expect(screen.getByText(/are you sure.*delete/i)).toBeInTheDocument();

      const confirmDeleteButton = screen.getByTestId('confirm-delete');
      await user.click(confirmDeleteButton);

      // Should remove property from list
      await waitFor(() => {
        expect(screen.queryByText('Luxury Beach House')).not.toBeInTheDocument();
      });

      // Should show success message
      expect(screen.getByText('Property deleted successfully')).toBeInTheDocument();
    });
  });

  describe('Booking Management Flow', () => {
    it('should handle complete booking workflow: view → update status → real-time updates', async () => {
      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Step 1: View bookings for a property
      const luxuryVillaCard = screen.getByTestId('property-card-1');
      const bookingsTab = within(luxuryVillaCard).getByTestId('bookings-tab');
      await user.click(bookingsTab);

      // Should show bookings list
      expect(screen.getByTestId('bookings-list')).toBeInTheDocument();
      expect(screen.getAllByTestId(/booking-\d+/)).toHaveLength(2); // 2 bookings for property 1

      // Step 2: View booking details
      const firstBooking = screen.getByTestId('booking-1');
      await user.click(firstBooking);

      // Should open booking details modal
      expect(screen.getByTestId('booking-details-modal')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument(); // Guest name
      expect(screen.getByText('6 guests')).toBeInTheDocument();
      expect(screen.getByText('₹500')).toBeInTheDocument(); // Total price

      // Step 3: Update booking status
      const statusSelect = screen.getByTestId('booking-status-select');
      await user.click(statusSelect);
      await user.click(screen.getByText('Completed'));

      const updateStatusButton = screen.getByTestId('update-booking-status');
      await user.click(updateStatusButton);

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText('Booking status updated successfully')).toBeInTheDocument();
      });

      // Should close modal and update UI
      await waitFor(() => {
        expect(screen.queryByTestId('booking-details-modal')).not.toBeInTheDocument();
      });

      // Should see updated status in booking list
      const updatedBooking = screen.getByTestId('booking-1');
      expect(within(updatedBooking).getByText('Completed')).toBeInTheDocument();

      // Step 4: Simulate real-time update from another booking
      const realtimeBookingUpdate = new CustomEvent('booking-updated', {
        detail: { 
          type: 'updated', 
          propertyId: 1, 
          bookingId: 2,
          newStatus: 'confirmed'
        }
      });
      
      fireEvent(window, realtimeBookingUpdate);

      // Should see real-time update reflected
      await waitFor(() => {
        const secondBooking = screen.getByTestId('booking-2');
        expect(within(secondBooking).getByText('Confirmed')).toBeInTheDocument();
      });

      // Step 5: Filter bookings by status
      const statusFilter = screen.getByTestId('booking-status-filter');
      await user.click(statusFilter);
      await user.click(screen.getByText('Confirmed'));

      // Should show only confirmed bookings
      await waitFor(() => {
        expect(screen.getByTestId('booking-2')).toBeInTheDocument();
        expect(screen.queryByTestId('booking-1')).not.toBeInTheDocument(); // Completed booking hidden
      });
    });

    it('should prevent conflicting booking operations', async () => {
      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Open booking details
      const booking = screen.getByTestId('booking-1');
      await user.click(booking);

      // Try to update to cancelled status
      const statusSelect = screen.getByTestId('booking-status-select');
      await user.click(statusSelect);
      await user.click(screen.getByText('Cancelled'));

      const updateButton = screen.getByTestId('update-booking-status');
      await user.click(updateButton);

      // Should show confirmation dialog for cancellation
      expect(screen.getByText(/are you sure.*cancel/i)).toBeInTheDocument();
      expect(screen.getByText(/this action cannot be undone/i)).toBeInTheDocument();

      // Cancel the cancellation
      const cancelButton = screen.getByTestId('cancel-cancellation');
      await user.click(cancelButton);

      // Should return to original state
      expect(screen.getByTestId('booking-details-modal')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Confirmed')).toBeInTheDocument();
    });
  });

  describe('Dashboard Analytics Flow', () => {
    it('should display comprehensive analytics and respond to data changes', async () => {
      simulateScenario('high-booking-volume');

      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      // Wait for analytics to load
      await waitFor(() => {
        expect(screen.getByTestId('dashboard-analytics')).toBeInTheDocument();
      });

      // Should show correct statistics
      expect(screen.getByTestId('total-properties-stat')).toHaveTextContent('2');
      expect(screen.getByTestId('total-bookings-stat')).toHaveTextContent('50'); // High volume scenario
      
      // Should show revenue information
      const revenueCard = screen.getByTestId('revenue-card');
      expect(within(revenueCard).getByText(/₹/)).toBeInTheDocument();

      // Should show occupancy rate
      const occupancyCard = screen.getByTestId('occupancy-card');
      expect(within(occupancyCard).getByText(/%/)).toBeInTheDocument();

      // Step 1: Change date range filter
      const dateRangeFilter = screen.getByTestId('date-range-filter');
      await user.click(dateRangeFilter);
      await user.click(screen.getByText('Last 30 days'));

      // Should update analytics
      await waitFor(() => {
        expect(screen.getByTestId('date-range-indicator')).toHaveTextContent('Last 30 days');
      });

      // Step 2: View detailed breakdown
      const detailedViewButton = screen.getByTestId('detailed-analytics-button');
      await user.click(detailedViewButton);

      // Should show expanded analytics
      expect(screen.getByTestId('detailed-analytics-panel')).toBeInTheDocument();
      expect(screen.getByTestId('booking-trend-chart')).toBeInTheDocument();
      expect(screen.getByTestId('revenue-breakdown-chart')).toBeInTheDocument();

      // Step 3: Export analytics data
      const exportButton = screen.getByTestId('export-analytics-button');
      await user.click(exportButton);

      // Should trigger download
      expect(screen.getByText('Analytics exported successfully')).toBeInTheDocument();
    });

    it('should handle real-time analytics updates', async () => {
      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-analytics')).toBeInTheDocument();
      });

      // Get initial booking count
      const initialBookings = screen.getByTestId('total-bookings-stat').textContent;

      // Simulate new booking creation via real-time update
      const newBookingEvent = new CustomEvent('booking-updated', {
        detail: { 
          type: 'created', 
          propertyId: 1, 
          bookingId: 999,
          totalPrice: 600
        }
      });
      
      fireEvent(window, newBookingEvent);

      // Should update analytics in real-time
      await waitFor(() => {
        const updatedBookings = screen.getByTestId('total-bookings-stat').textContent;
        expect(parseInt(updatedBookings!)).toBeGreaterThan(parseInt(initialBookings!));
      });

      // Should update revenue as well
      await waitFor(() => {
        const revenueCard = screen.getByTestId('revenue-card');
        expect(within(revenueCard).getByText(/updated/i)).toBeInTheDocument();
      });
    });
  });

  describe('Multi-Property Management Flow', () => {
    it('should efficiently handle owners with many properties', async () => {
      // Create scenario with many properties
      simulateScenario('high-property-volume');

      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      // Should use virtualization for large lists
      await waitFor(() => {
        expect(screen.getByTestId('virtualized-property-list')).toBeInTheDocument();
      });

      // Should show search/filter options
      expect(screen.getByTestId('property-search')).toBeInTheDocument();
      expect(screen.getByTestId('property-filters')).toBeInTheDocument();

      // Step 1: Search for specific property
      const searchInput = screen.getByTestId('property-search');
      await user.type(searchInput, 'Beach');

      // Should filter properties
      await waitFor(() => {
        const visibleProperties = screen.getAllByTestId(/property-card-\d+/);
        expect(visibleProperties.length).toBeLessThan(10); // Filtered results
      });

      // Step 2: Filter by availability
      const availabilityFilter = screen.getByTestId('availability-filter');
      await user.click(availabilityFilter);
      await user.click(screen.getByText('Available only'));

      // Should further filter results
      await waitFor(() => {
        const availableProperties = screen.getAllByTestId(/property-card-\d+/);
        availableProperties.forEach(card => {
          expect(within(card).getByText('Available')).toBeInTheDocument();
        });
      });

      // Step 3: Bulk operations
      const selectAllCheckbox = screen.getByTestId('select-all-properties');
      await user.click(selectAllCheckbox);

      // Should select all visible properties
      const bulkActionsPanel = screen.getByTestId('bulk-actions-panel');
      expect(bulkActionsPanel).toBeInTheDocument();

      // Step 4: Bulk status update
      const bulkStatusButton = screen.getByTestId('bulk-update-status');
      await user.click(bulkStatusButton);

      const statusSelect = screen.getByTestId('bulk-status-select');
      await user.click(statusSelect);
      await user.click(screen.getByText('Maintenance'));

      const confirmBulkUpdate = screen.getByTestId('confirm-bulk-update');
      await user.click(confirmBulkUpdate);

      // Should update all selected properties
      await waitFor(() => {
        expect(screen.getByText('Bulk update completed successfully')).toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery and Edge Cases', () => {
    it('should handle concurrent modifications gracefully', async () => {
      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Open property for editing
      const editButton = screen.getByTestId('edit-property-1');
      await user.click(editButton);

      // Simulate concurrent modification from another session
      const concurrentUpdateEvent = new CustomEvent('property-updated', {
        detail: { 
          propertyId: 1, 
          updatedAt: new Date().toISOString(),
          version: 2 // Newer version
        }
      });
      
      fireEvent(window, concurrentUpdateEvent);

      // Try to save changes
      const nameInput = screen.getByDisplayValue('Luxury Villa');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Villa');

      const submitButton = screen.getByTestId('submit-property');
      await user.click(submitButton);

      // Should detect conflict and show warning
      await waitFor(() => {
        expect(screen.getByText(/property was modified/i)).toBeInTheDocument();
        expect(screen.getByText(/refresh.*continue/i)).toBeInTheDocument();
      });

      // User can choose to refresh and lose changes
      const refreshButton = screen.getByTestId('refresh-property-data');
      await user.click(refreshButton);

      // Should reload with fresh data
      await waitFor(() => {
        expect(screen.getByDisplayValue('Luxury Villa')).toBeInTheDocument(); // Original name restored
      });
    });

    it('should handle partial network failures gracefully', async () => {
      // Mock partial failure - some requests succeed, others fail
      const fetchSpy = vi.spyOn(global, 'fetch');
      let callCount = 0;
      
      fetchSpy.mockImplementation((url) => {
        callCount++;
        if (callCount % 3 === 0) { // Every third request fails
          return Promise.reject(new Error('Network error'));
        }
        return Promise.resolve(new Response(JSON.stringify({ success: true, data: [] })));
      });

      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      // Should show partial loading states
      await waitFor(() => {
        expect(screen.getByTestId('partial-loading-indicator')).toBeInTheDocument();
      });

      // Should show retry options for failed requests
      await waitFor(() => {
        expect(screen.getByTestId('retry-failed-requests')).toBeInTheDocument();
      });

      // User can retry failed requests
      const retryButton = screen.getByTestId('retry-failed-requests');
      await user.click(retryButton);

      // Should attempt to refetch failed data
      await waitFor(() => {
        expect(fetchSpy).toHaveBeenCalledWith(
          expect.stringContaining('/api/'),
          expect.any(Object)
        );
      });

      fetchSpy.mockRestore();
    });

    it('should maintain UI state during real-time updates', async () => {
      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Open a modal
      const addPropertyButton = screen.getByTestId('add-property-button');
      await user.click(addPropertyButton);

      expect(screen.getByTestId('property-form-modal')).toBeInTheDocument();

      // Fill some form data
      await user.type(screen.getByLabelText(/property name/i), 'Test Property');

      // Simulate real-time update while modal is open
      const realtimeUpdate = new CustomEvent('booking-updated', {
        detail: { type: 'created', propertyId: 2, bookingId: 888 }
      });
      
      fireEvent(window, realtimeUpdate);

      // Modal should remain open with form data intact
      expect(screen.getByTestId('property-form-modal')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Property')).toBeInTheDocument();

      // Background data should still be updated
      await waitFor(() => {
        // Real-time update should be processed in background
        expect(queryClient.getQueryData(['batch-property-bookings'])).toBeTruthy();
      });
    });
  });

  describe('Performance and Optimization', () => {
    it('should demonstrate N+1 query optimization', async () => {
      const fetchSpy = vi.spyOn(global, 'fetch');

      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Should make exactly one request for all property bookings (not N+1)
      const bookingRequests = fetchSpy.mock.calls.filter(([url]) => 
        url?.includes('/api/bookings/by-property-ids')
      );
      expect(bookingRequests).toHaveLength(1);

      // Should not make individual booking requests for each property
      const individualRequests = fetchSpy.mock.calls.filter(([url]) => 
        url?.match(/\/api\/bookings\/property\/\d+$/)
      );
      expect(individualRequests).toHaveLength(0);

      fetchSpy.mockRestore();
    });

    it('should handle large datasets efficiently', async () => {
      simulateScenario('high-booking-volume'); // 50 bookings

      const renderStart = performance.now();

      render(
        <TestApp queryClient={queryClient}>
          <OwnerDashboard />
        </TestApp>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      const renderEnd = performance.now();
      const renderTime = renderEnd - renderStart;

      // Should render efficiently even with large datasets
      expect(renderTime).toBeLessThan(2000); // Less than 2 seconds

      // Should use virtualization for large lists
      expect(screen.getByTestId('virtualized-booking-list')).toBeInTheDocument();
    });
  });
});