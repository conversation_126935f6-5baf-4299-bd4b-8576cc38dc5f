/**
 * MSW (Mock Service Worker) Handlers for Owner Dashboard Tests
 * 
 * Provides realistic API mocking for comprehensive testing
 */

import { http, HttpResponse } from 'msw';
import { 
  mockOwner, 
  mockProperties, 
  mockBookings, 
  mockGuests 
} from '../setup';
import type { Property, Booking } from '@shared/schema';

// In-memory state for stateful testing
let properties = [...mockProperties];
let bookings = [...mockBookings];
let mediaUploadCount = 0;

// Helper to simulate network delays
const delay = (ms: number = 100) => new Promise(resolve => setTimeout(resolve, ms));

export const ownerDashboardHandlers = [
  // Authentication
  http.get('/api/auth/me', async () => {
    await delay();
    return HttpResponse.json({
      success: true,
      data: mockOwner,
    });
  }),

  // Properties
  http.get('/api/properties/owner', async () => {
    await delay();
    return HttpResponse.json({
      success: true,
      data: properties,
    });
  }),

  http.get('/api/properties/:id', async ({ params }) => {
    await delay();
    const id = parseInt(params.id as string);
    const property = properties.find(p => p.id === id);
    
    if (!property) {
      return HttpResponse.json(
        { success: false, error: { code: 'NOT_FOUND', message: 'Property not found' } },
        { status: 404 }
      );
    }
    
    return HttpResponse.json({
      success: true,
      data: property,
    });
  }),

  http.put('/api/properties/:id', async ({ params, request }) => {
    await delay();
    const id = parseInt(params.id as string);
    const updates = await request.json() as Partial<Property>;
    
    const propertyIndex = properties.findIndex(p => p.id === id);
    if (propertyIndex === -1) {
      return HttpResponse.json(
        { success: false, error: { code: 'NOT_FOUND', message: 'Property not found' } },
        { status: 404 }
      );
    }
    
    properties[propertyIndex] = {
      ...properties[propertyIndex],
      ...updates,
      updatedAt: new Date(),
    };
    
    return HttpResponse.json({
      success: true,
      data: properties[propertyIndex],
    });
  }),

  http.delete('/api/properties/:id', async ({ params }) => {
    await delay();
    const id = parseInt(params.id as string);
    const propertyIndex = properties.findIndex(p => p.id === id);
    
    if (propertyIndex === -1) {
      return HttpResponse.json(
        { success: false, error: { code: 'NOT_FOUND', message: 'Property not found' } },
        { status: 404 }
      );
    }
    
    // Check for active bookings
    const activeBookings = bookings.filter(
      b => b.propertyId === id && b.status === 'confirmed'
    );
    
    if (activeBookings.length > 0) {
      return HttpResponse.json(
        { 
          success: false, 
          error: { 
            code: 'CONFLICT_ERROR', 
            message: 'Cannot delete property with active bookings' 
          } 
        },
        { status: 409 }
      );
    }
    
    properties.splice(propertyIndex, 1);
    
    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Bookings - Optimized batch endpoint
  http.post('/api/bookings/by-property-ids', async ({ request }) => {
    await delay();
    const { propertyIds } = await request.json() as { propertyIds: number[] };
    
    const bookingsByProperty: Record<number, any[]> = {};
    
    for (const propertyId of propertyIds) {
      const propertyBookings = bookings
        .filter(b => b.propertyId === propertyId)
        .map(booking => ({
          ...booking,
          property: properties.find(p => p.id === booking.propertyId),
          guest: mockGuests.find(g => g.id === booking.userId),
        }));
      
      bookingsByProperty[propertyId] = propertyBookings;
    }
    
    return HttpResponse.json({
      success: true,
      data: {
        bookingsByProperty,
        propertyCount: propertyIds.length,
        totalBookings: Object.values(bookingsByProperty).flat().length,
      },
    });
  }),

  // Individual booking operations
  http.get('/api/bookings/:id', async ({ params }) => {
    await delay();
    const id = parseInt(params.id as string);
    const booking = bookings.find(b => b.id === id);
    
    if (!booking) {
      return HttpResponse.json(
        { success: false, error: { code: 'NOT_FOUND', message: 'Booking not found' } },
        { status: 404 }
      );
    }
    
    return HttpResponse.json({
      success: true,
      data: {
        ...booking,
        property: properties.find(p => p.id === booking.propertyId),
        guest: mockGuests.find(g => g.id === booking.userId),
      },
    });
  }),

  http.patch('/api/bookings/:id/status', async ({ params, request }) => {
    await delay();
    const id = parseInt(params.id as string);
    const { status } = await request.json() as { status: string };
    
    const bookingIndex = bookings.findIndex(b => b.id === id);
    if (bookingIndex === -1) {
      return HttpResponse.json(
        { success: false, error: { code: 'NOT_FOUND', message: 'Booking not found' } },
        { status: 404 }
      );
    }
    
    // Validate status transition
    const validStatuses = ['pending_payment', 'confirmed', 'cancelled', 'completed'];
    if (!validStatuses.includes(status)) {
      return HttpResponse.json(
        { 
          success: false, 
          error: { 
            code: 'VALIDATION_ERROR', 
            message: 'Invalid status' 
          } 
        },
        { status: 400 }
      );
    }
    
    bookings[bookingIndex] = {
      ...bookings[bookingIndex],
      status,
      updatedAt: new Date(),
    };
    
    return HttpResponse.json({
      success: true,
      data: bookings[bookingIndex],
    });
  }),

  // Dashboard analytics
  http.get('/api/owner/dashboard/summary', async () => {
    await delay();
    
    const totalBookings = bookings.length;
    const confirmedBookings = bookings.filter(b => b.status === 'confirmed').length;
    const pendingBookings = bookings.filter(b => b.status === 'pending_payment').length;
    const totalRevenue = bookings
      .filter(b => b.status === 'confirmed')
      .reduce((sum, b) => sum + b.totalPrice, 0);
    
    // Recent bookings (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    const recentBookings = bookings.filter(b => new Date(b.createdAt) >= weekAgo).length;
    
    return HttpResponse.json({
      success: true,
      data: {
        totalProperties: properties.length,
        totalBookings,
        confirmedBookings,
        pendingBookings,
        totalRevenue,
        recentBookings,
        averageBookingValue: totalBookings > 0 ? totalRevenue / confirmedBookings : 0,
        occupancyRate: properties.length > 0 ? (confirmedBookings / properties.length) * 100 : 0,
      },
    });
  }),

  // Media management
  http.post('/api/cloudinary/upload', async ({ request }) => {
    await delay(200); // Simulate upload time
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return HttpResponse.json(
        { success: false, error: { code: 'VALIDATION_ERROR', message: 'No file provided' } },
        { status: 400 }
      );
    }
    
    // Simulate file size validation
    if (file.size > 10 * 1024 * 1024) { // 10MB
      return HttpResponse.json(
        { 
          success: false, 
          error: { 
            code: 'VALIDATION_ERROR', 
            message: 'File size exceeds 10MB limit' 
          } 
        },
        { status: 400 }
      );
    }
    
    mediaUploadCount++;
    
    return HttpResponse.json({
      success: true,
      data: {
        public_id: `property_image_${mediaUploadCount}`,
        secure_url: `https://res.cloudinary.com/test/image/upload/v1/${file.name}`,
        width: 1200,
        height: 800,
        format: 'jpg',
        resource_type: 'image',
      },
    });
  }),

  http.delete('/api/cloudinary/:publicId', async ({ params }) => {
    await delay();
    return HttpResponse.json({
      success: true,
      data: { result: 'ok' },
    });
  }),

  // Video upload
  http.post('/api/cloudinary/upload-video', async ({ request }) => {
    await delay(1000); // Simulate longer video upload time
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return HttpResponse.json(
        { success: false, error: { code: 'VALIDATION_ERROR', message: 'No file provided' } },
        { status: 400 }
      );
    }
    
    // Simulate video size validation
    if (file.size > 100 * 1024 * 1024) { // 100MB
      return HttpResponse.json(
        { 
          success: false, 
          error: { 
            code: 'VALIDATION_ERROR', 
            message: 'Video file size exceeds 100MB limit' 
          } 
        },
        { status: 400 }
      );
    }
    
    mediaUploadCount++;
    
    return HttpResponse.json({
      success: true,
      data: {
        public_id: `property_video_${mediaUploadCount}`,
        secure_url: `https://res.cloudinary.com/test/video/upload/v1/${file.name}`,
        duration: 30.5,
        format: 'mp4',
        resource_type: 'video',
      },
    });
  }),

  // Real-time events (SSE)
  http.get('/api/events/stream', async ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    
    if (!userId) {
      return HttpResponse.json(
        { success: false, error: { code: 'AUTHENTICATION_ERROR', message: 'User ID required' } },
        { status: 401 }
      );
    }
    
    // Return SSE headers (actual SSE streaming would be handled by the real server)
    return new HttpResponse(null, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  }),

  // Error simulation handlers for testing error scenarios
  http.get('/api/test/server-error', () => {
    return HttpResponse.json(
      { success: false, error: { code: 'INTERNAL_ERROR', message: 'Server error' } },
      { status: 500 }
    );
  }),

  http.get('/api/test/network-error', () => {
    return HttpResponse.error();
  }),

  http.get('/api/test/timeout', async () => {
    await delay(10000); // Simulate timeout
    return HttpResponse.json({ success: true, data: {} });
  }),

  // Rate limiting simulation
  http.get('/api/test/rate-limit', () => {
    return HttpResponse.json(
      { 
        success: false, 
        error: { 
          code: 'RATE_LIMIT_ERROR', 
          message: 'Rate limit exceeded',
          retryAfter: 60 
        } 
      },
      { status: 429 }
    );
  }),
];

// Reset handlers for test isolation
export function resetMockState() {
  properties = [...mockProperties];
  bookings = [...mockBookings];
  mediaUploadCount = 0;
}

// Helper to simulate specific scenarios
export function simulateScenario(scenario: string) {
  switch (scenario) {
    case 'no-properties':
      properties = [];
      break;
    case 'no-bookings':
      bookings = [];
      break;
    case 'high-booking-volume':
      bookings = Array.from({ length: 50 }, (_, i) => ({
        ...mockBookings[0],
        id: i + 100,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      }));
      break;
    case 'mixed-booking-statuses':
      bookings = [
        ...mockBookings,
        ...Array.from({ length: 10 }, (_, i) => ({
          ...mockBookings[0],
          id: i + 200,
          status: ['confirmed', 'pending_payment', 'cancelled'][i % 3],
        })),
      ];
      break;
  }
}