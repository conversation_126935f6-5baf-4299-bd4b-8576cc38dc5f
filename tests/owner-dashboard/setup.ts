/**
 * Owner Dashboard Test Setup & Configuration
 * 
 * Provides common test utilities, mocks, and setup for Owner Dashboard tests
 */

import { vi, beforeEach, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import { QueryClient } from '@tanstack/react-query';
import { server } from './mocks/server';
import type { User, Property, Booking } from '@shared/schema';

// Extend vitest matchers
import '@testing-library/jest-dom/vitest';

// Mock WebSocket globally
global.WebSocket = vi.fn(() => ({
  close: vi.fn(),
  send: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: WebSocket.OPEN,
  CONNECTING: WebSocket.CONNECTING,
  OPEN: WebSocket.OPEN,
  CLOSING: WebSocket.CLOSING,
  CLOSED: WebSocket.CLOSED,
})) as any;

// Mock EventSource for SSE
global.EventSource = vi.fn(() => ({
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: EventSource.OPEN,
  CONNECTING: EventSource.CONNECTING,
  OPEN: EventSource.OPEN,
  CLOSED: EventSource.CLOSED,
})) as any;

// Mock window.matchMedia for responsive tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
})) as any;

// Test data fixtures
export const mockOwner: User = {
  id: 1,
  username: 'testowner',
  email: '<EMAIL>',
  fullName: 'Test Owner',
  role: 'owner',
  phone: '+1234567890',
  password: 'hashedpassword',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

export const mockProperties: Property[] = [
  {
    id: 1,
    name: 'Luxury Villa',
    description: 'Beautiful luxury villa with pool',
    location: 'Beverly Hills',
    fullDayPrice: 500,
    halfDayPrice: 300,
    maxGuests: 10,
    bedrooms: 4,
    bathrooms: 3,
    amenities: ['pool', 'wifi', 'parking'],
    imageUrls: ['https://example.com/villa1.jpg'],
    videos: null,
    ownerId: 1,
    isAvailable: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 2,
    name: 'Cozy Cottage',
    description: 'Charming cottage in the countryside',
    location: 'Napa Valley',
    fullDayPrice: 200,
    halfDayPrice: 120,
    maxGuests: 4,
    bedrooms: 2,
    bathrooms: 1,
    amenities: ['fireplace', 'garden'],
    imageUrls: ['https://example.com/cottage1.jpg'],
    videos: null,
    ownerId: 1,
    isAvailable: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

export const mockBookings: Booking[] = [
  {
    id: 1,
    propertyId: 1,
    userId: 2,
    bookingDate: new Date('2024-02-15'),
    bookingType: 'full_day',
    guests: 6,
    totalPrice: 500,
    status: 'confirmed',
    specialRequests: 'Need early check-in',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-12'),
  },
  {
    id: 2,
    propertyId: 1,
    userId: 3,
    bookingDate: new Date('2024-02-20'),
    bookingType: 'morning',
    guests: 4,
    totalPrice: 300,
    status: 'pending_payment',
    specialRequests: null,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 3,
    propertyId: 2,
    userId: 4,
    bookingDate: new Date('2024-02-25'),
    bookingType: 'full_day',
    guests: 2,
    totalPrice: 200,
    status: 'cancelled',
    specialRequests: 'Cancelled due to weather',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-22'),
  },
];

export const mockGuests = [
  {
    id: 2,
    fullName: 'John Doe',
    username: 'johndoe',
    email: '<EMAIL>',
    phone: '+1234567891',
  },
  {
    id: 3,
    fullName: 'Jane Smith',
    username: 'janesmith',
    email: '<EMAIL>',
    phone: '+1234567892',
  },
  {
    id: 4,
    fullName: 'Bob Wilson',
    username: 'bobwilson',
    email: '<EMAIL>',
    phone: '+1234567893',
  },
];

// Create a clean QueryClient for each test
export function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

// Mock authentication context
export const mockAuthContext = {
  user: mockOwner,
  token: 'mock-jwt-token',
  login: vi.fn(),
  logout: vi.fn(),
  isLoading: false,
  isAuthenticated: true,
};

// Mock WebSocket/SSE message generators
export function createMockSSEMessage(type: string, data: any) {
  return {
    type,
    data: JSON.stringify(data),
    lastEventId: Date.now().toString(),
    origin: 'http://localhost:3000',
    source: null,
    ports: [],
  };
}

export function createMockDeltaMessage(entityType: string, entityId: number, operations: any[]) {
  return {
    messageId: `delta-${Date.now()}`,
    version: 1,
    timestamp: Date.now(),
    entityType,
    entityId,
    operations,
    checksum: 'mock-checksum',
    metadata: {
      source: 'test',
      userId: mockOwner.id,
      priority: 'medium',
    },
  };
}

// Setup and teardown
beforeEach(() => {
  // Start MSW server
  server.listen({ onUnhandledRequest: 'error' });
  
  // Clear all mocks
  vi.clearAllMocks();
  
  // Reset DOM
  document.body.innerHTML = '';
});

afterEach(() => {
  // Clean up React components
  cleanup();
  
  // Reset MSW handlers
  server.resetHandlers();
  
  // Clear timers
  vi.clearAllTimers();
});

afterEach(() => {
  // Close MSW server
  server.close();
});

// Export all mocks for use in tests
export * from './mocks/handlers';
export * from './mocks/server';