/**
 * Media Management Operations Tests
 * 
 * Tests for image/video upload, update, and delete operations
 * with comprehensive error handling and validation.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import userEvent from '@testing-library/user-event';

import { MediaManagementSection } from '@/components/MediaManagementSection';
import { AuthContext } from '@/contexts/AuthContext';
import { 
  createTestQueryClient, 
  mockAuthContext, 
  mockProperties,
  resetMockState 
} from '../setup';

// Mock file objects for testing
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File([''], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

const mockImageFile = createMockFile('test-image.jpg', 1024 * 1024, 'image/jpeg'); // 1MB
const mockLargeImageFile = createMockFile('large-image.jpg', 15 * 1024 * 1024, 'image/jpeg'); // 15MB
const mockVideoFile = createMockFile('test-video.mp4', 50 * 1024 * 1024, 'video/mp4'); // 50MB
const mockLargeVideoFile = createMockFile('large-video.mp4', 150 * 1024 * 1024, 'video/mp4'); // 150MB
const mockInvalidFile = createMockFile('document.pdf', 1024, 'application/pdf');

function TestWrapper({ children, queryClient }: { children: React.ReactNode; queryClient: QueryClient }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={mockAuthContext}>
        {children}
      </AuthContext.Provider>
    </QueryClientProvider>
  );
}

describe('MediaManagement', () => {
  let queryClient: QueryClient;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    user = userEvent.setup();
    resetMockState();
    
    // Mock URL.createObjectURL for file previews
    global.URL.createObjectURL = vi.fn(() => 'blob:mock-url');
    global.URL.revokeObjectURL = vi.fn();
  });

  describe('Image Upload', () => {
    it('should allow uploading valid image files', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      const uploadButton = screen.getByTestId('upload-images-button');

      // Select file
      await user.upload(fileInput, mockImageFile);

      // Should show file preview
      await waitFor(() => {
        expect(screen.getByText('test-image.jpg')).toBeInTheDocument();
        expect(screen.getByTestId('image-preview')).toBeInTheDocument();
      });

      // Upload file
      await user.click(uploadButton);

      // Should show upload progress
      expect(screen.getByTestId('upload-progress')).toBeInTheDocument();

      // Should complete upload
      await waitFor(() => {
        expect(screen.getByText('Image uploaded successfully')).toBeInTheDocument();
        expect(screen.queryByTestId('upload-progress')).not.toBeInTheDocument();
      });
    });

    it('should reject files exceeding size limit', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');

      // Try to upload large file
      await user.upload(fileInput, mockLargeImageFile);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/file size exceeds.*limit/i)).toBeInTheDocument();
        expect(screen.getByText(/maximum.*10MB/i)).toBeInTheDocument();
      });

      // Upload button should be disabled
      const uploadButton = screen.getByTestId('upload-images-button');
      expect(uploadButton).toBeDisabled();
    });

    it('should reject invalid file types', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');

      // Try to upload invalid file type
      await user.upload(fileInput, mockInvalidFile);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/invalid file type/i)).toBeInTheDocument();
        expect(screen.getByText(/only.*images.*allowed/i)).toBeInTheDocument();
      });
    });

    it('should support multiple file upload', async () => {
      const mockImages = [
        createMockFile('image1.jpg', 1024 * 1024, 'image/jpeg'),
        createMockFile('image2.png', 1024 * 1024, 'image/png'),
        createMockFile('image3.gif', 1024 * 1024, 'image/gif'),
      ];

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');

      // Select multiple files
      await user.upload(fileInput, mockImages);

      // Should show all file previews
      await waitFor(() => {
        expect(screen.getByText('image1.jpg')).toBeInTheDocument();
        expect(screen.getByText('image2.png')).toBeInTheDocument();
        expect(screen.getByText('image3.gif')).toBeInTheDocument();
      });

      // Upload all files
      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should show batch upload progress
      expect(screen.getByText('Uploading 3 files...')).toBeInTheDocument();

      // Should complete all uploads
      await waitFor(() => {
        expect(screen.getByText('All images uploaded successfully')).toBeInTheDocument();
      });
    });

    it('should handle upload failures gracefully', async () => {
      // Mock failed upload
      vi.spyOn(global, 'fetch').mockResolvedValueOnce(
        new Response(JSON.stringify({ 
          success: false, 
          error: { message: 'Upload failed' } 
        }), { status: 500 })
      );

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, mockImageFile);

      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/upload failed/i)).toBeInTheDocument();
        expect(screen.getByTestId('retry-upload-button')).toBeInTheDocument();
      });
    });
  });

  describe('Video Upload', () => {
    it('should allow uploading valid video files', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      // Switch to video tab
      const videoTab = screen.getByTestId('video-tab');
      await user.click(videoTab);

      const fileInput = screen.getByTestId('video-file-input');
      const uploadButton = screen.getByTestId('upload-video-button');

      // Select video file
      await user.upload(fileInput, mockVideoFile);

      // Should show video preview
      await waitFor(() => {
        expect(screen.getByText('test-video.mp4')).toBeInTheDocument();
        expect(screen.getByTestId('video-preview')).toBeInTheDocument();
      });

      // Upload video
      await user.click(uploadButton);

      // Should show extended upload progress for video
      expect(screen.getByTestId('video-upload-progress')).toBeInTheDocument();
      expect(screen.getByText(/uploading video/i)).toBeInTheDocument();

      // Should complete upload
      await waitFor(() => {
        expect(screen.getByText('Video uploaded successfully')).toBeInTheDocument();
      }, { timeout: 5000 });
    });

    it('should reject video files exceeding size limit', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const videoTab = screen.getByTestId('video-tab');
      await user.click(videoTab);

      const fileInput = screen.getByTestId('video-file-input');

      // Try to upload large video
      await user.upload(fileInput, mockLargeVideoFile);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/video file size exceeds.*limit/i)).toBeInTheDocument();
        expect(screen.getByText(/maximum.*100MB/i)).toBeInTheDocument();
      });
    });

    it('should show video processing status', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const videoTab = screen.getByTestId('video-tab');
      await user.click(videoTab);

      const fileInput = screen.getByTestId('video-file-input');
      await user.upload(fileInput, mockVideoFile);

      const uploadButton = screen.getByTestId('upload-video-button');
      await user.click(uploadButton);

      // Should show processing steps
      await waitFor(() => {
        expect(screen.getByText(/uploading/i)).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/processing/i)).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/generating thumbnails/i)).toBeInTheDocument();
      });
    });
  });

  describe('Media Management', () => {
    it('should display existing media with controls', async () => {
      const propertyWithMedia = {
        ...mockProperties[0],
        imageUrls: [
          'https://res.cloudinary.com/test/image1.jpg',
          'https://res.cloudinary.com/test/image2.jpg'
        ],
        videos: [{
          url: 'https://res.cloudinary.com/test/video1.mp4',
          publicId: 'video1',
          duration: 30
        }]
      };

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={propertyWithMedia} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      // Should display existing images
      expect(screen.getByTestId('existing-images')).toBeInTheDocument();
      expect(screen.getAllByTestId(/image-item-/)).toHaveLength(2);

      // Should display existing videos
      const videoTab = screen.getByTestId('video-tab');
      await user.click(videoTab);
      
      expect(screen.getByTestId('existing-videos')).toBeInTheDocument();
      expect(screen.getByTestId('video-item-0')).toBeInTheDocument();
    });

    it('should allow deleting existing media', async () => {
      const propertyWithMedia = {
        ...mockProperties[0],
        imageUrls: ['https://res.cloudinary.com/test/image1.jpg']
      };

      const onPropertyUpdate = vi.fn();

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={propertyWithMedia} 
            onPropertyUpdate={onPropertyUpdate}
          />
        </TestWrapper>
      );

      // Click delete button
      const deleteButton = screen.getByTestId('delete-image-0');
      await user.click(deleteButton);

      // Should show confirmation dialog
      expect(screen.getByText(/are you sure.*delete/i)).toBeInTheDocument();

      // Confirm deletion
      const confirmButton = screen.getByTestId('confirm-delete-media');
      await user.click(confirmButton);

      // Should call property update
      await waitFor(() => {
        expect(onPropertyUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            imageUrls: []
          })
        );
      });
    });

    it('should allow reordering media items', async () => {
      const propertyWithMedia = {
        ...mockProperties[0],
        imageUrls: [
          'https://res.cloudinary.com/test/image1.jpg',
          'https://res.cloudinary.com/test/image2.jpg',
          'https://res.cloudinary.com/test/image3.jpg'
        ]
      };

      const onPropertyUpdate = vi.fn();

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={propertyWithMedia} 
            onPropertyUpdate={onPropertyUpdate}
          />
        </TestWrapper>
      );

      // Should show reorder controls
      expect(screen.getAllByTestId(/move-up-/)).toHaveLength(2); // First item can't move up
      expect(screen.getAllByTestId(/move-down-/)).toHaveLength(2); // Last item can't move down

      // Move second item up
      const moveUpButton = screen.getByTestId('move-up-1');
      await user.click(moveUpButton);

      // Should update property with new order
      await waitFor(() => {
        expect(onPropertyUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            imageUrls: [
              'https://res.cloudinary.com/test/image2.jpg',
              'https://res.cloudinary.com/test/image1.jpg',
              'https://res.cloudinary.com/test/image3.jpg'
            ]
          })
        );
      });
    });

    it('should set primary image', async () => {
      const propertyWithMedia = {
        ...mockProperties[0],
        imageUrls: [
          'https://res.cloudinary.com/test/image1.jpg',
          'https://res.cloudinary.com/test/image2.jpg'
        ]
      };

      const onPropertyUpdate = vi.fn();

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={propertyWithMedia} 
            onPropertyUpdate={onPropertyUpdate}
          />
        </TestWrapper>
      );

      // Click set as primary on second image
      const setPrimaryButton = screen.getByTestId('set-primary-1');
      await user.click(setPrimaryButton);

      // Should move image to front
      await waitFor(() => {
        expect(onPropertyUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            imageUrls: [
              'https://res.cloudinary.com/test/image2.jpg',
              'https://res.cloudinary.com/test/image1.jpg'
            ]
          })
        );
      });
    });
  });

  describe('Drag and Drop', () => {
    it('should support drag and drop file upload', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const dropZone = screen.getByTestId('media-drop-zone');

      // Simulate drag enter
      fireEvent.dragEnter(dropZone, {
        dataTransfer: {
          types: ['Files'],
          files: [mockImageFile]
        }
      });

      // Should show drop indicator
      expect(screen.getByText(/drop files here/i)).toBeInTheDocument();
      expect(dropZone).toHaveClass('drag-over');

      // Simulate drop
      fireEvent.drop(dropZone, {
        dataTransfer: {
          files: [mockImageFile]
        }
      });

      // Should process dropped files
      await waitFor(() => {
        expect(screen.getByText('test-image.jpg')).toBeInTheDocument();
      });
    });

    it('should reject invalid files on drop', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const dropZone = screen.getByTestId('media-drop-zone');

      // Drop invalid file
      fireEvent.drop(dropZone, {
        dataTransfer: {
          files: [mockInvalidFile]
        }
      });

      // Should show error
      await waitFor(() => {
        expect(screen.getByText(/invalid file type/i)).toBeInTheDocument();
      });
    });

    it('should handle drag and drop reordering', async () => {
      const propertyWithMedia = {
        ...mockProperties[0],
        imageUrls: [
          'https://res.cloudinary.com/test/image1.jpg',
          'https://res.cloudinary.com/test/image2.jpg'
        ]
      };

      const onPropertyUpdate = vi.fn();

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={propertyWithMedia} 
            onPropertyUpdate={onPropertyUpdate}
          />
        </TestWrapper>
      );

      const firstImage = screen.getByTestId('image-item-0');
      const secondImage = screen.getByTestId('image-item-1');

      // Drag first image over second
      fireEvent.dragStart(firstImage);
      fireEvent.dragEnter(secondImage);
      fireEvent.dragOver(secondImage);
      fireEvent.drop(secondImage);

      // Should reorder images
      await waitFor(() => {
        expect(onPropertyUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            imageUrls: [
              'https://res.cloudinary.com/test/image2.jpg',
              'https://res.cloudinary.com/test/image1.jpg'
            ]
          })
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors during upload', async () => {
      // Mock network error
      vi.spyOn(global, 'fetch').mockRejectedValueOnce(
        new Error('Network error')
      );

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, mockImageFile);

      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should show network error
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
        expect(screen.getByTestId('retry-upload-button')).toBeInTheDocument();
      });
    });

    it('should handle rate limiting', async () => {
      // Mock rate limit response
      vi.spyOn(global, 'fetch').mockResolvedValueOnce(
        new Response(JSON.stringify({ 
          success: false, 
          error: { 
            code: 'RATE_LIMIT_ERROR',
            message: 'Rate limit exceeded',
            retryAfter: 60
          } 
        }), { status: 429 })
      );

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, mockImageFile);

      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should show rate limit message with retry time
      await waitFor(() => {
        expect(screen.getByText(/rate limit exceeded/i)).toBeInTheDocument();
        expect(screen.getByText(/try again in.*60.*seconds/i)).toBeInTheDocument();
      });
    });

    it('should handle storage quota exceeded', async () => {
      // Mock quota exceeded response
      vi.spyOn(global, 'fetch').mockResolvedValueOnce(
        new Response(JSON.stringify({ 
          success: false, 
          error: { 
            code: 'QUOTA_EXCEEDED',
            message: 'Storage quota exceeded'
          } 
        }), { status:413 })
      );

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, mockImageFile);

      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should show quota exceeded message
      await waitFor(() => {
        expect(screen.getByText(/storage quota exceeded/i)).toBeInTheDocument();
        expect(screen.getByText(/upgrade.*plan/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    it('should debounce rapid file selections', async () => {
      const onPropertyUpdate = vi.fn();

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={onPropertyUpdate}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');

      // Rapidly select files
      await user.upload(fileInput, mockImageFile);
      await user.upload(fileInput, createMockFile('image2.jpg', 1024, 'image/jpeg'));
      await user.upload(fileInput, createMockFile('image3.jpg', 1024, 'image/jpeg'));

      // Should only process the final selection
      await waitFor(() => {
        expect(screen.getByText('image3.jpg')).toBeInTheDocument();
        expect(screen.queryByText('test-image.jpg')).not.toBeInTheDocument();
      });
    });

    it('should show upload progress for large files', async () => {
      const largeMockFile = createMockFile('large-image.jpg', 5 * 1024 * 1024, 'image/jpeg');

      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, largeMockFile);

      const uploadButton = screen.getByTestId('upload-images-button');
      await user.click(uploadButton);

      // Should show detailed progress
      expect(screen.getByTestId('upload-progress-bar')).toBeInTheDocument();
      expect(screen.getByText(/0%/)).toBeInTheDocument();
    });

    it('should optimize image previews', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <MediaManagementSection 
            property={mockProperties[0]} 
            onPropertyUpdate={vi.fn()}
          />
        </TestWrapper>
      );

      const fileInput = screen.getByTestId('image-file-input');
      await user.upload(fileInput, mockImageFile);

      // Should create optimized preview
      await waitFor(() => {
        const preview = screen.getByTestId('image-preview');
        expect(preview).toHaveAttribute('loading', 'lazy');
        expect(preview).toHaveStyle({ maxWidth: '200px', maxHeight: '200px' });
      });
    });
  });
});