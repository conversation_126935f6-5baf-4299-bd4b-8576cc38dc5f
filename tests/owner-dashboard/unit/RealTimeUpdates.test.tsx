/**
 * Real-Time Updates Tests (WebSocket/SSE)
 * 
 * Comprehensive tests for WebSocket and Server-Sent Events functionality
 * including connection management, delta updates, and error handling.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import userEvent from '@testing-library/user-event';

import { useRealTimeUpdates } from '@/services/realTimeUpdates';
import { useOptimizedBookings } from '@/hooks/useOptimizedBookings';
import { AuthContext } from '@/contexts/AuthContext';
import { 
  createTestQueryClient, 
  mockAuthContext,
  createMockSSEMessage,
  createMockDeltaMessage,
  resetMockState 
} from '../setup';

// Mock EventSource
class MockEventSource {
  public url: string;
  public readyState: number = EventSource.CONNECTING;
  private listeners: Map<string, Function[]> = new Map();
  
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSED = 2;

  constructor(url: string) {
    this.url = url;
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = EventSource.OPEN;
      this.dispatchEvent('open', new Event('open'));
    }, 10);
  }

  addEventListener(event: string, listener: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  removeEventListener(event: string, listener: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  dispatchEvent(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(data));
    }
  }

  close() {
    this.readyState = EventSource.CLOSED;
    this.dispatchEvent('close', new Event('close'));
  }

  // Test helpers
  simulateMessage(type: string, data: any) {
    this.dispatchEvent(type, createMockSSEMessage(type, data));
  }

  simulateError() {
    this.readyState = EventSource.CLOSED;
    this.dispatchEvent('error', new Event('error'));
  }

  simulateReconnect() {
    this.readyState = EventSource.CONNECTING;
    setTimeout(() => {
      this.readyState = EventSource.OPEN;
      this.dispatchEvent('open', new Event('open'));
    }, 100);
  }
}

// Test component that uses real-time updates
function TestRealTimeComponent({ userId, token }: { userId: number; token: string }) {
  const { connectionStatus, stats, deltaStats } = useRealTimeUpdates(userId, token, {
    onBookingUpdate: vi.fn(),
    onDeltaBatch: vi.fn(),
    onMissedUpdates: vi.fn(),
    onConnectionChange: vi.fn(),
  });

  return (
    <div data-testid="realtime-component">
      <div data-testid="connection-status">{connectionStatus}</div>
      <div data-testid="stats">{JSON.stringify(stats)}</div>
      <div data-testid="delta-stats">{JSON.stringify(deltaStats)}</div>
    </div>
  );
}

// Test component for optimized bookings with real-time updates
function TestOptimizedBookingsComponent() {
  const { bookingsByProperty, bookingsLoading, totalBookings } = useOptimizedBookings(
    [1, 2], 
    mockAuthContext.token
  );

  return (
    <div data-testid="optimized-bookings">
      <div data-testid="loading">{bookingsLoading.toString()}</div>
      <div data-testid="total-bookings">{totalBookings}</div>
      <div data-testid="bookings-data">{JSON.stringify(bookingsByProperty)}</div>
    </div>
  );
}

describe('RealTimeUpdates', () => {
  let queryClient: QueryClient;
  let mockEventSource: MockEventSource;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    resetMockState();
    
    // Mock EventSource globally
    global.EventSource = vi.fn((url: string) => {
      mockEventSource = new MockEventSource(url);
      return mockEventSource as any;
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Connection Management', () => {
    it('should establish SSE connection on mount', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      // Should create EventSource connection
      expect(global.EventSource).toHaveBeenCalledWith(
        expect.stringContaining('/api/events/stream')
      );

      // Should show connecting initially
      expect(screen.getByTestId('connection-status')).toHaveTextContent('connecting');

      // Should update to connected
      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });
    });

    it('should include authentication parameters in connection', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      expect(global.EventSource).toHaveBeenCalledWith(
        expect.stringMatching(/userId=1.*token=mock-jwt-token/)
      );
    });

    it('should handle connection errors gracefully', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Simulate connection error
      act(() => {
        mockEventSource.simulateError();
      });

      // Should update status to disconnected
      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('disconnected');
      });
    });

    it('should attempt reconnection after connection loss', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Simulate connection error
      act(() => {
        mockEventSource.simulateError();
      });

      // Should attempt reconnection
      await waitFor(() => {
        expect(global.EventSource).toHaveBeenCalledTimes(2); // Initial + reconnection
      });
    });

    it('should cleanup connection on unmount', async () => {
      const { unmount } = render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      const closeSpy = vi.spyOn(mockEventSource, 'close');

      // Unmount component
      unmount();

      // Should close connection
      expect(closeSpy).toHaveBeenCalled();
    });
  });

  describe('Delta Updates', () => {
    it('should handle single delta update messages', async () => {
      const onBookingUpdate = vi.fn();

      function TestComponent() {
        useRealTimeUpdates(mockAuthContext.user.id, mockAuthContext.token!, {
          onBookingUpdate,
        });
        return <div data-testid="test">Test</div>;
      }

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(mockEventSource.readyState).toBe(EventSource.OPEN);
      });

      // Send delta update
      const deltaMessage = createMockDeltaMessage('booking', 1, [
        { op: 'set', path: 'status', value: 'confirmed' }
      ]);

      act(() => {
        mockEventSource.simulateMessage('delta-update', deltaMessage);
      });

      // Should call booking update handler
      await waitFor(() => {
        expect(onBookingUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'booking_updated',
            bookingId: 1,
          })
        );
      });
    });

    it('should handle batched delta updates', async () => {
      const onDeltaBatch = vi.fn();

      function TestComponent() {
        useRealTimeUpdates(mockAuthContext.user.id, mockAuthContext.token!, {
          onDeltaBatch,
        });
        return <div data-testid="test">Test</div>;
      }

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(mockEventSource.readyState).toBe(EventSource.OPEN);
      });

      // Send batch delta update
      const batchMessage = {
        messageId: 'batch-123',
        type: 'batch',
        timestamp: Date.now(),
        count: 2,
        messages: [
          createMockDeltaMessage('booking', 1, [{ op: 'set', path: 'status', value: 'confirmed' }]),
          createMockDeltaMessage('booking', 2, [{ op: 'set', path: 'status', value: 'cancelled' }]),
        ],
      };

      act(() => {
        mockEventSource.simulateMessage('delta-batch', batchMessage);
      });

      // Should call batch handler
      await waitFor(() => {
        expect(onDeltaBatch).toHaveBeenCalledWith(batchMessage);
      });
    });

    it('should handle missed updates on reconnection', async () => {
      const onMissedUpdates = vi.fn();

      function TestComponent() {
        useRealTimeUpdates(mockAuthContext.user.id, mockAuthContext.token!, {
          onMissedUpdates,
        });
        return <div data-testid="test">Test</div>;
      }

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(mockEventSource.readyState).toBe(EventSource.OPEN);
      });

      // Send missed updates message
      const missedUpdatesMessage = {
        type: 'missed-updates',
        count: 3,
        updates: [
          createMockDeltaMessage('booking', 1, [{ op: 'set', path: 'status', value: 'confirmed' }]),
          createMockDeltaMessage('booking', 2, [{ op: 'set', path: 'status', value: 'confirmed' }]),
          createMockDeltaMessage('property', 1, [{ op: 'set', path: 'isAvailable', value: false }]),
        ],
        timestamp: Date.now(),
      };

      act(() => {
        mockEventSource.simulateMessage('missed-updates', missedUpdatesMessage);
      });

      // Should call missed updates handler
      await waitFor(() => {
        expect(onMissedUpdates).toHaveBeenCalledWith(missedUpdatesMessage);
      });
    });

    it('should handle malformed delta messages gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(mockEventSource.readyState).toBe(EventSource.OPEN);
      });

      // Send malformed message
      act(() => {
        mockEventSource.simulateMessage('delta-update', 'invalid-json');
      });

      // Should log error but not crash
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Failed to parse')
        );
      });

      // Component should still be functional
      expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');

      consoleSpy.mockRestore();
    });
  });

  describe('Integration with React Query', () => {
    it('should invalidate queries on relevant updates', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestOptimizedBookingsComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      // Wait for initial data load
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('false');
      });

      // Send booking update that should trigger invalidation
      const deltaMessage = createMockDeltaMessage('booking', 1, [
        { op: 'set', path: 'status', value: 'confirmed' }
      ]);

      // Simulate delta update event
      const customEvent = new CustomEvent('booking-updated', {
        detail: { type: 'updated', propertyId: 1, bookingId: 1 }
      });

      act(() => {
        window.dispatchEvent(customEvent);
      });

      // Should invalidate and refetch queries
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('true');
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('false');
      });
    });

    it('should update cached data with delta operations', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestOptimizedBookingsComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      // Wait for initial data
      await waitFor(() => {
        expect(screen.getByTestId('total-bookings')).toHaveTextContent('3');
      });

      // Get initial booking data
      const initialData = JSON.parse(screen.getByTestId('bookings-data').textContent || '{}');

      // Send delta update
      const deltaMessage = createMockDeltaMessage('booking', 1, [
        { op: 'set', path: 'status', value: 'confirmed' }
      ]);

      // Simulate delta state manager applying the update
      act(() => {
        // This would normally be handled by deltaStateManager
        const updatedData = { ...initialData };
        if (updatedData[1] && updatedData[1][0]) {
          updatedData[1][0].status = 'confirmed';
        }
        queryClient.setQueryData(['batch-property-bookings', [1, 2]], {
          bookingsByProperty: updatedData,
          totalBookings: 3,
        });
      });

      // Should reflect the update in UI
      await waitFor(() => {
        const bookingsData = JSON.parse(screen.getByTestId('bookings-data').textContent || '{}');
        expect(bookingsData[1]?.[0]?.status).toBe('confirmed');
      });
    });
  });

  describe('Heartbeat and Connection Monitoring', () => {
    it('should handle heartbeat messages', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Send heartbeat with delta stats
      const heartbeatMessage = {
        type: 'heartbeat',
        timestamp: new Date().toISOString(),
        connectionId: 'test-connection-123',
        deltaStats: {
          trackedEntities: 15,
          queuedUpdates: 3,
        },
      };

      act(() => {
        mockEventSource.simulateMessage('heartbeat', heartbeatMessage);
      });

      // Should update delta stats
      await waitFor(() => {
        const deltaStats = JSON.parse(screen.getByTestId('delta-stats').textContent || '{}');
        expect(deltaStats.trackedEntities).toBe(15);
      });
    });

    it('should detect connection timeout', async () => {
      vi.useFakeTimers();

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Fast-forward past heartbeat timeout (assuming 30 seconds)
      act(() => {
        vi.advanceTimersByTime(35000);
      });

      // Should detect timeout and attempt reconnection
      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('disconnected');
      });

      vi.useRealTimers();
    });
  });

  describe('Performance and Optimization', () => {
    it('should batch multiple rapid updates', async () => {
      const onBookingUpdate = vi.fn();

      function TestComponent() {
        useRealTimeUpdates(mockAuthContext.user.id, mockAuthContext.token!, {
          onBookingUpdate,
        });
        return <div data-testid="test">Test</div>;
      }

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(mockEventSource.readyState).toBe(EventSource.OPEN);
      });

      // Send multiple rapid updates
      act(() => {
        mockEventSource.simulateMessage('delta-update', createMockDeltaMessage('booking', 1, [
          { op: 'set', path: 'status', value: 'pending' }
        ]));
        mockEventSource.simulateMessage('delta-update', createMockDeltaMessage('booking', 1, [
          { op: 'set', path: 'status', value: 'confirmed' }
        ]));
        mockEventSource.simulateMessage('delta-update', createMockDeltaMessage('booking', 1, [
          { op: 'set', path: 'updatedAt', value: new Date().toISOString() }
        ]));
      });

      // Should handle all updates but may batch the notifications
      await waitFor(() => {
        expect(onBookingUpdate).toHaveBeenCalled();
      });
    });

    it('should filter updates for relevant properties only', async () => {
      const onBookingUpdate = vi.fn();

      function TestComponent() {
        useRealTimeUpdates(mockAuthContext.user.id, mockAuthContext.token!, {
          onBookingUpdate,
        });
        return <div data-testid="test">Test</div>;
      }

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestComponent />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(mockEventSource.readyState).toBe(EventSource.OPEN);
      });

      // Send update for property not owned by user
      act(() => {
        mockEventSource.simulateMessage('delta-update', createMockDeltaMessage('booking', 999, [
          { op: 'set', path: 'propertyId', value: 999 }, // Property not owned by user
          { op: 'set', path: 'status', value: 'confirmed' }
        ]));
      });

      // Should not trigger update handler for irrelevant properties
      await waitFor(() => {
        expect(onBookingUpdate).not.toHaveBeenCalled();
      }, { timeout: 500 });
    });

    it('should provide bandwidth usage statistics', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Send some delta updates to generate stats
      act(() => {
        mockEventSource.simulateMessage('delta-update', createMockDeltaMessage('booking', 1, [
          { op: 'set', path: 'status', value: 'confirmed' }
        ]));
      });

      // Should eventually show bandwidth savings
      await waitFor(() => {
        const deltaStats = JSON.parse(screen.getByTestId('delta-stats').textContent || '{}');
        expect(deltaStats.bandwidthSaved).toBeDefined();
      });
    });
  });

  describe('Error Recovery', () => {
    it('should recover from temporary network issues', async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Simulate network error
      act(() => {
        mockEventSource.simulateError();
      });

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('disconnected');
      });

      // Simulate network recovery
      act(() => {
        mockEventSource.simulateReconnect();
      });

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });
    });

    it('should handle exponential backoff for reconnection', async () => {
      vi.useFakeTimers();

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={mockAuthContext}>
            <TestRealTimeComponent 
              userId={mockAuthContext.user.id} 
              token={mockAuthContext.token!} 
            />
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toHaveTextContent('connected');
      });

      // Simulate repeated connection failures
      for (let i = 0; i < 3; i++) {
        act(() => {
          mockEventSource.simulateError();
        });

        // Each reconnection attempt should take longer
        act(() => {
          vi.advanceTimersByTime(1000 * Math.pow(2, i)); // Exponential backoff
        });
      }

      // Should eventually stop trying after max attempts
      expect(global.EventSource).toHaveBeenCalledTimes(4); // Initial + 3 retries

      vi.useRealTimers();
    });
  });
});