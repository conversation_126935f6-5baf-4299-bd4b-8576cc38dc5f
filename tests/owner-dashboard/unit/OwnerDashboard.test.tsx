/**
 * Unit Tests for OwnerDashboard Component
 * 
 * Comprehensive test coverage for the main Owner Dashboard component
 * including security, performance, and user interaction testing.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'wouter/memory';
import userEvent from '@testing-library/user-event';

import OwnerDashboard from '@/pages/OwnerDashboard';
import { AuthContext } from '@/contexts/AuthContext';
import { 
  createTestQueryClient, 
  mockAuthContext, 
  mockProperties, 
  mockBookings,
  simulateScenario,
  resetMockState 
} from '../setup';

// Test wrapper component
function TestWrapper({ children, queryClient }: { children: React.ReactNode; queryClient: QueryClient }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={mockAuthContext}>
        <MemoryRouter initialEntries={['/owner/dashboard']}>
          {children}
        </MemoryRouter>
      </AuthContext.Provider>
    </QueryClientProvider>
  );
}

describe('OwnerDashboard', () => {
  let queryClient: QueryClient;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    user = userEvent.setup();
    resetMockState();
  });

  describe('Initial Rendering', () => {
    it('should render loading state initially', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      // Should show loading skeletons
      expect(screen.getByTestId('dashboard-loading')).toBeInTheDocument();
      expect(screen.getAllByTestId('skeleton')).toHaveLength(4); // Properties skeleton cards
    });

    it('should render dashboard with properties after loading', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByTestId('dashboard-loading')).not.toBeInTheDocument();
      });

      // Should display properties
      expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      expect(screen.getByText('Cozy Cottage')).toBeInTheDocument();

      // Should display dashboard stats
      expect(screen.getByText('Total Properties')).toBeInTheDocument();
      expect(screen.getByText('Total Bookings')).toBeInTheDocument();
      expect(screen.getByText('Revenue')).toBeInTheDocument();
    });

    it('should handle empty state gracefully', async () => {
      simulateScenario('no-properties');

      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('No properties found')).toBeInTheDocument();
        expect(screen.getByText('Add your first property')).toBeInTheDocument();
      });
    });
  });

  describe('Security & Authorization', () => {
    it('should enforce ownership verification', async () => {
      // Mock unauthorized user
      const unauthorizedContext = {
        ...mockAuthContext,
        user: { ...mockAuthContext.user, role: 'user' }
      };

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={unauthorizedContext}>
            <MemoryRouter initialEntries={['/owner/dashboard']}>
              <OwnerDashboard />
            </MemoryRouter>
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      // Should show access denied or redirect
      await waitFor(() => {
        expect(screen.getByText(/access denied|unauthorized/i)).toBeInTheDocument();
      });
    });

    it('should handle token expiration gracefully', async () => {
      // Mock expired token
      const expiredTokenContext = {
        ...mockAuthContext,
        token: null,
        isAuthenticated: false
      };

      render(
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={expiredTokenContext}>
            <MemoryRouter initialEntries={['/owner/dashboard']}>
              <OwnerDashboard />
            </MemoryRouter>
          </AuthContext.Provider>
        </QueryClientProvider>
      );

      // Should redirect to login or show auth error
      await waitFor(() => {
        expect(mockAuthContext.login).toHaveBeenCalled();
      });
    });

    it('should validate property ownership before operations', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Try to edit property
      const editButton = screen.getByTestId('edit-property-1');
      await user.click(editButton);

      // Should verify ownership in the request
      await waitFor(() => {
        expect(screen.getByTestId('property-edit-modal')).toBeInTheDocument();
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should display connection status indicator', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('connection-status')).toBeInTheDocument();
      });

      // Should show connected status
      expect(screen.getByText(/connected|live/i)).toBeInTheDocument();
    });

    it('should reconnect on connection loss', async () => {
      const mockEventSource = vi.fn(() => ({
        close: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        readyState: EventSource.CLOSED,
      }));
      global.EventSource = mockEventSource as any;

      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/disconnected|reconnecting/i)).toBeInTheDocument();
      });

      // Should attempt reconnection
      expect(mockEventSource).toHaveBeenCalledWith(
        expect.stringContaining('/api/events/stream')
      );
    });

    it('should handle malformed messages gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      // Simulate malformed SSE message
      const mockEventSource = (global.EventSource as any).mock.results[0].value;
      const messageHandler = mockEventSource.addEventListener.mock.calls
        .find(([event]: any) => event === 'delta-update')?.[1];

      if (messageHandler) {
        messageHandler({ data: 'invalid-json' });
      }

      // Should not crash and should log error
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to parse')
      );
      
      consoleSpy.mockRestore();
    });

    it('should update UI when receiving booking updates', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Simulate receiving a booking update
      const customEvent = new CustomEvent('booking-updated', {
        detail: { type: 'created', propertyId: 1, bookingId: 999 }
      });
      window.dispatchEvent(customEvent);

      // Should invalidate and refetch data
      await waitFor(() => {
        expect(queryClient.getQueryData(['batch-property-bookings'])).toBeTruthy();
      });
    });
  });

  describe('Property Management', () => {
    it('should allow creating new properties', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('add-property-button')).toBeInTheDocument();
      });

      // Click add property button
      await user.click(screen.getByTestId('add-property-button'));

      // Should open property creation modal
      expect(screen.getByTestId('property-form-modal')).toBeInTheDocument();
      expect(screen.getByText('Add New Property')).toBeInTheDocument();
    });

    it('should allow editing existing properties', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Click edit button
      const editButton = screen.getByTestId('edit-property-1');
      await user.click(editButton);

      // Should open edit modal with existing data
      expect(screen.getByTestId('property-form-modal')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Luxury Villa')).toBeInTheDocument();
    });

    it('should handle property deletion with confirmation', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Click delete button
      const deleteButton = screen.getByTestId('delete-property-1');
      await user.click(deleteButton);

      // Should show confirmation dialog
      expect(screen.getByText(/are you sure/i)).toBeInTheDocument();
      expect(screen.getByText(/delete.*property/i)).toBeInTheDocument();

      // Confirm deletion
      const confirmButton = screen.getByTestId('confirm-delete');
      await user.click(confirmButton);

      // Should remove property from UI
      await waitFor(() => {
        expect(screen.queryByText('Luxury Villa')).not.toBeInTheDocument();
      });
    });

    it('should prevent deletion of properties with active bookings', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Click delete button for property with active bookings
      const deleteButton = screen.getByTestId('delete-property-1');
      await user.click(deleteButton);

      const confirmButton = screen.getByTestId('confirm-delete');
      await user.click(confirmButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/cannot delete.*active bookings/i)).toBeInTheDocument();
      });
    });
  });

  describe('Booking Management', () => {
    it('should display bookings grouped by property', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Should show booking counts
      const luxuryVillaCard = screen.getByTestId('property-card-1');
      expect(within(luxuryVillaCard).getByText(/2 bookings/i)).toBeInTheDocument();
    });

    it('should allow updating booking status', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('booking-1')).toBeInTheDocument();
      });

      // Click on booking to open details
      await user.click(screen.getByTestId('booking-1'));

      // Should show booking details modal
      expect(screen.getByTestId('booking-details-modal')).toBeInTheDocument();

      // Change status
      const statusSelect = screen.getByTestId('booking-status-select');
      await user.click(statusSelect);
      await user.click(screen.getByText('Confirmed'));

      // Should update status
      await waitFor(() => {
        expect(screen.getByText('Status updated successfully')).toBeInTheDocument();
      });
    });

    it('should handle booking filtering and sorting', async () => {
      simulateScenario('mixed-booking-statuses');

      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('booking-filters')).toBeInTheDocument();
      });

      // Filter by status
      const statusFilter = screen.getByTestId('status-filter');
      await user.click(statusFilter);
      await user.click(screen.getByText('Confirmed'));

      // Should show only confirmed bookings
      await waitFor(() => {
        const bookingItems = screen.getAllByTestId(/booking-\d+/);
        bookingItems.forEach(item => {
          expect(within(item).getByText('Confirmed')).toBeInTheDocument();
        });
      });
    });
  });

  describe('Performance', () => {
    it('should use optimized batch booking fetch', async () => {
      const fetchSpy = vi.spyOn(global, 'fetch');

      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      // Should make only one request for all property bookings
      const bookingRequests = fetchSpy.mock.calls.filter(([url]) => 
        url?.includes('/api/bookings/by-property-ids')
      );
      expect(bookingRequests).toHaveLength(1);

      fetchSpy.mockRestore();
    });

    it('should implement proper loading states', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      // Should show skeleton loading
      expect(screen.getByTestId('dashboard-loading')).toBeInTheDocument();

      // Should transition to loaded state
      await waitFor(() => {
        expect(screen.queryByTestId('dashboard-loading')).not.toBeInTheDocument();
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });
    });

    it('should handle high booking volume efficiently', async () => {
      simulateScenario('high-booking-volume');

      const renderStart = performance.now();
      
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
      });

      const renderEnd = performance.now();
      const renderTime = renderEnd - renderStart;

      // Should render efficiently even with many bookings
      expect(renderTime).toBeLessThan(1000); // Less than 1 second
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Mock network error
      vi.spyOn(global, 'fetch').mockRejectedValueOnce(
        new Error('Network error')
      );

      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/error loading/i)).toBeInTheDocument();
        expect(screen.getByTestId('retry-button')).toBeInTheDocument();
      });
    });

    it('should handle server errors with retry mechanism', async () => {
      // Mock server error
      vi.spyOn(global, 'fetch').mockResolvedValueOnce(
        new Response(JSON.stringify({ error: 'Server error' }), {
          status: 500,
          statusText: 'Internal Server Error'
        })
      );

      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/server error/i)).toBeInTheDocument();
      });

      // Click retry
      const retryButton = screen.getByTestId('retry-button');
      await user.click(retryButton);

      // Should attempt to refetch
      await waitFor(() => {
        expect(screen.getByTestId('dashboard-loading')).toBeInTheDocument();
      });
    });

    it('should validate user inputs properly', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('add-property-button')).toBeInTheDocument();
      });

      // Open property form
      await user.click(screen.getByTestId('add-property-button'));

      // Try to submit empty form
      const submitButton = screen.getByTestId('submit-property');
      await user.click(submitButton);

      // Should show validation errors
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/location is required/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Check for proper labeling
      expect(screen.getByLabelText(/dashboard navigation/i)).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('add-property-button')).toBeInTheDocument();
      });

      // Tab navigation should work
      const addButton = screen.getByTestId('add-property-button');
      addButton.focus();
      expect(document.activeElement).toBe(addButton);

      // Enter key should activate buttons
      fireEvent.keyDown(addButton, { key: 'Enter' });
      expect(screen.getByTestId('property-form-modal')).toBeInTheDocument();
    });

    it('should provide screen reader announcements for updates', async () => {
      render(
        <TestWrapper queryClient={queryClient}>
          <OwnerDashboard />
        </TestWrapper>
      );

      // Should have aria-live regions for dynamic updates
      expect(screen.getByRole('status')).toBeInTheDocument();
      expect(screen.getByLabelText(/live updates/i)).toBeInTheDocument();
    });
  });
});