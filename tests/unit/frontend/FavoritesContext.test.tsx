import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, act } from '../../../tests/test-utils'
import { FavoritesProvider, useFavorites } from '../../../client/src/contexts/FavoritesContext'

// Test component to test the useFavorites hook
function TestComponent() {
  const favoritesContext = useFavorites()

  if (!favoritesContext) {
    return <div>No context</div>
  }

  const { favorites, addToFavorites, removeFromFavorites, isFavorite } = favoritesContext

  return (
    <div>
      <div data-testid="favorites-count">{favorites.length}</div>
      <div>
        {favorites.map((id) => (
          <div key={id} data-testid={`favorite-${id}`}>Property {id}</div>
        ))}
      </div>
      <button onClick={() => addToFavorites(1)}>Add Property 1</button>
      <button onClick={() => addToFavorites(2)}>Add Property 2</button>
      <button onClick={() => removeFromFavorites(1)}>Remove Property 1</button>
      <div data-testid="is-favorite-1">{isFavorite(1) ? 'true' : 'false'}</div>
      <div data-testid="is-favorite-2">{isFavorite(2) ? 'true' : 'false'}</div>
    </div>
  )
}

describe('FavoritesContext', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
  })

  it('starts with empty favorites list', () => {
    render(
      <FavoritesProvider>
        <TestComponent />
      </FavoritesProvider>
    )

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('0')
    expect(screen.getByTestId('is-favorite-1')).toHaveTextContent('false')
    expect(screen.getByTestId('is-favorite-2')).toHaveTextContent('false')
  })

  it('adds properties to favorites', () => {
    render(
      <FavoritesProvider>
        <TestComponent />
      </FavoritesProvider>
    )

    const addButton1 = screen.getByText('Add Property 1')
    const addButton2 = screen.getByText('Add Property 2')

    act(() => {
      addButton1.click()
    })

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('1')
    expect(screen.getByTestId('is-favorite-1')).toHaveTextContent('true')
    expect(screen.getByTestId('favorite-1')).toBeInTheDocument()

    act(() => {
      addButton2.click()
    })

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('2')
    expect(screen.getByTestId('is-favorite-2')).toHaveTextContent('true')
    expect(screen.getByTestId('favorite-2')).toBeInTheDocument()
  })

  it('removes properties from favorites', () => {
    render(
      <FavoritesProvider>
        <TestComponent />
      </FavoritesProvider>
    )

    const addButton1 = screen.getByText('Add Property 1')
    const removeButton1 = screen.getByText('Remove Property 1')

    act(() => {
      addButton1.click()
    })

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('1')
    expect(screen.getByTestId('is-favorite-1')).toHaveTextContent('true')

    act(() => {
      removeButton1.click()
    })

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('0')
    expect(screen.getByTestId('is-favorite-1')).toHaveTextContent('false')
  })

  it('prevents duplicate favorites', () => {
    render(
      <FavoritesProvider>
        <TestComponent />
      </FavoritesProvider>
    )

    const addButton1 = screen.getByText('Add Property 1')

    act(() => {
      addButton1.click()
      addButton1.click()
      addButton1.click()
    })

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('1')
  })

  it('persists favorites in localStorage', () => {
    const { unmount } = render(
      <FavoritesProvider>
        <TestComponent />
      </FavoritesProvider>
    )

    const addButton1 = screen.getByText('Add Property 1')

    act(() => {
      addButton1.click()
    })

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('1')

    // Unmount and remount to test persistence
    unmount()

    render(
      <FavoritesProvider>
        <TestComponent />
      </FavoritesProvider>
    )

    expect(screen.getByTestId('favorites-count')).toHaveTextContent('1')
    expect(screen.getByTestId('is-favorite-1')).toHaveTextContent('true')
  })
})