import { describe, test, expect } from 'vitest'
import { getProxiedImageUrl, getProxiedImageUrls } from '../../../client/src/lib/imageProxy'

describe('imageProxy', () => {
  describe('getProxiedImageUrl', () => {
    test('returns local URLs unchanged', () => {
      const localUrl = '/uploads/image.jpg'
      expect(getProxiedImageUrl(localUrl)).toBe(localUrl)
    })

    test('returns data URLs unchanged', () => {
      const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='
      expect(getProxiedImageUrl(dataUrl)).toBe(dataUrl)
    })

    test('returns already proxied URLs unchanged', () => {
      const proxiedUrl = '/api/proxy-image?url=https%3A%2F%2Fexample.com%2Fimage.jpg'
      expect(getProxiedImageUrl(proxiedUrl)).toBe(proxiedUrl)
    })

    test('proxies Unsplash URLs', () => {
      const unsplashUrl = 'https://images.unsplash.com/photo-1234567890'
      const expected = '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890'
      expect(getProxiedImageUrl(unsplashUrl)).toBe(expected)
    })

    test('proxies unsplash.com URLs', () => {
      const unsplashUrl = 'https://unsplash.com/photo/1234567890'
      const expected = '/api/proxy-image?url=https%3A%2F%2Funsplash.com%2Fphoto%2F1234567890'
      expect(getProxiedImageUrl(unsplashUrl)).toBe(expected)
    })

    test('returns non-blocked domains unchanged', () => {
      const safeUrl = 'https://example.com/image.jpg'
      expect(getProxiedImageUrl(safeUrl)).toBe(safeUrl)
    })

    test('handles URLs with query parameters', () => {
      const unsplashUrlWithParams = 'https://images.unsplash.com/photo-1234567890?auto=format&fit=crop&w=800&q=80'
      const expected = '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890%3Fauto%3Dformat%26fit%3Dcrop%26w%3D800%26q%3D80'
      expect(getProxiedImageUrl(unsplashUrlWithParams)).toBe(expected)
    })

    test('handles URLs with fragments', () => {
      const urlWithFragment = 'https://images.unsplash.com/photo-1234567890#section'
      const expected = '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890%23section'
      expect(getProxiedImageUrl(urlWithFragment)).toBe(expected)
    })

    test('handles invalid URLs gracefully', () => {
      const invalidUrl = 'not-a-valid-url'
      expect(getProxiedImageUrl(invalidUrl)).toBe(invalidUrl)
    })

    test('handles empty string', () => {
      expect(getProxiedImageUrl('')).toBe('')
    })

    test('handles protocol-relative URLs', () => {
      const protocolRelativeUrl = '//images.unsplash.com/photo-1234567890'
      // Should return unchanged as URL parsing will fail
      expect(getProxiedImageUrl(protocolRelativeUrl)).toBe(protocolRelativeUrl)
    })

    test('properly encodes special characters in URLs', () => {
      const urlWithSpecialChars = 'https://images.unsplash.com/photo-1234567890?query=hello world&other=test+value'
      const expected = '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890%3Fquery%3Dhello%20world%26other%3Dtest%2Bvalue'
      expect(getProxiedImageUrl(urlWithSpecialChars)).toBe(expected)
    })
  })

  describe('getProxiedImageUrls', () => {
    test('processes array of URLs correctly', () => {
      const urls = [
        '/local/image.jpg',
        'https://images.unsplash.com/photo-1234567890',
        'https://example.com/safe-image.jpg',
        'data:image/png;base64,abc123'
      ]

      const expected = [
        '/local/image.jpg',
        '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890',
        'https://example.com/safe-image.jpg',
        'data:image/png;base64,abc123'
      ]

      expect(getProxiedImageUrls(urls)).toEqual(expected)
    })

    test('handles empty array', () => {
      expect(getProxiedImageUrls([])).toEqual([])
    })

    test('handles array with one URL', () => {
      const urls = ['https://images.unsplash.com/photo-1234567890']
      const expected = ['/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890']
      expect(getProxiedImageUrls(urls)).toEqual(expected)
    })

    test('handles mixed valid and invalid URLs', () => {
      const urls = [
        'https://images.unsplash.com/photo-1234567890',
        'invalid-url',
        '/local/image.jpg'
      ]

      const expected = [
        '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890',
        'invalid-url',
        '/local/image.jpg'
      ]

      expect(getProxiedImageUrls(urls)).toEqual(expected)
    })
  })

  describe('edge cases', () => {
    test('handles very long URLs', () => {
      const longUrl = 'https://images.unsplash.com/photo-1234567890' + 'a'.repeat(1000)
      const result = getProxiedImageUrl(longUrl)
      expect(result).toContain('/api/proxy-image?url=')
      expect(result).toContain(encodeURIComponent(longUrl))
    })

    test('handles URLs with non-ASCII characters', () => {
      const unicodeUrl = 'https://images.unsplash.com/photo-测试'
      const result = getProxiedImageUrl(unicodeUrl)
      expect(result).toContain('/api/proxy-image?url=')
      expect(result).toContain(encodeURIComponent(unicodeUrl))
    })

    test('handles URL with percent-encoded characters', () => {
      const encodedUrl = 'https://images.unsplash.com/photo-1234567890?query=hello%20world'
      const expected = '/api/proxy-image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1234567890%3Fquery%3Dhello%2520world'
      expect(getProxiedImageUrl(encodedUrl)).toBe(expected)
    })
  })
})