import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useKeyboardNavigation, useKeyboardNavigationLegacy } from '@/hooks/useKeyboardNavigation'

describe('useKeyboardNavigation (new API)', () => {
  let addEventListenerSpy: ReturnType<typeof vi.spyOn>
  let removeEventListenerSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    addEventListenerSpy = vi.spyOn(document, 'addEventListener')
    removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('adds keyboard event listener on mount when enabled', () => {
    const mockOnEnter = vi.fn()
    renderHook(() => useKeyboardNavigation({
      onEnter: mockOnEnter,
      onEscape: undefined,
      onArrowUp: undefined,
      onArrowDown: undefined,
      onArrowLeft: undefined,
      onArrowRight: undefined,
      onTab: undefined,
      enabled: true
    }))

    expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
  })

  it('does not add event listener when disabled', () => {
    const mockOnEnter = vi.fn()
    renderHook(() => useKeyboardNavigation({
      onEnter: mockOnEnter,
      onEscape: undefined,
      onArrowUp: undefined,
      onArrowDown: undefined,
      onArrowLeft: undefined,
      onArrowRight: undefined,
      onTab: undefined,
      enabled: false
    }))

    expect(addEventListenerSpy).not.toHaveBeenCalled()
  })

  it('removes keyboard event listener on unmount', () => {
    const mockOnEnter = vi.fn()
    const { unmount } = renderHook(() => useKeyboardNavigation({
      onEnter: mockOnEnter,
      onEscape: undefined,
      onArrowUp: undefined,
      onArrowDown: undefined,
      onArrowLeft: undefined,
      onArrowRight: undefined,
      onTab: undefined,
      enabled: true
    }))

    unmount()

    expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
  })

  it('calls onEnter when Enter key is pressed', () => {
    const mockOnEnter = vi.fn()
    renderHook(() => useKeyboardNavigation({
      onEnter: mockOnEnter,
      onEscape: undefined,
      onArrowUp: undefined,
      onArrowDown: undefined,
      onArrowLeft: undefined,
      onArrowRight: undefined,
      onTab: undefined,
      enabled: true
    }))

    const keydownEvent = new KeyboardEvent('keydown', { key: 'Enter' })

    act(() => {
      document.dispatchEvent(keydownEvent)
    })

    expect(mockOnEnter).toHaveBeenCalled()
  })

  it('calls onEscape when Escape key is pressed', () => {
    const mockOnEscape = vi.fn()
    renderHook(() => useKeyboardNavigation({
      onEnter: undefined,
      onEscape: mockOnEscape,
      onArrowUp: undefined,
      onArrowDown: undefined,
      onArrowLeft: undefined,
      onArrowRight: undefined,
      onTab: undefined,
      enabled: true
    }))

    const keydownEvent = new KeyboardEvent('keydown', { key: 'Escape' })

    act(() => {
      document.dispatchEvent(keydownEvent)
    })

    expect(mockOnEscape).toHaveBeenCalled()
  })

  it('calls arrow key handlers when arrow keys are pressed', () => {
    const mockOnArrowUp = vi.fn()
    const mockOnArrowDown = vi.fn()
    const mockOnArrowLeft = vi.fn()
    const mockOnArrowRight = vi.fn()

    renderHook(() => useKeyboardNavigation({
      onEnter: undefined,
      onEscape: undefined,
      onArrowUp: mockOnArrowUp,
      onArrowDown: mockOnArrowDown,
      onArrowLeft: mockOnArrowLeft,
      onArrowRight: mockOnArrowRight,
      onTab: undefined,
      enabled: true
    }))

    act(() => {
      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowUp' }))
    })
    expect(mockOnArrowUp).toHaveBeenCalled()

    act(() => {
      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowDown' }))
    })
    expect(mockOnArrowDown).toHaveBeenCalled()

    act(() => {
      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowLeft' }))
    })
    expect(mockOnArrowLeft).toHaveBeenCalled()

    act(() => {
      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowRight' }))
    })
    expect(mockOnArrowRight).toHaveBeenCalled()
  })

  it('handles null options gracefully', () => {
    expect(() => {
      renderHook(() => useKeyboardNavigation(null as any))
    }).not.toThrow()
  })
})

describe('useKeyboardNavigationLegacy (backward compatibility)', () => {
  let addEventListenerSpy: ReturnType<typeof vi.spyOn>
  let removeEventListenerSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    addEventListenerSpy = vi.spyOn(document, 'addEventListener')
    removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('adds keyboard event listener on mount', () => {
    const mockCallback = vi.fn()
    renderHook(() => useKeyboardNavigationLegacy(mockCallback))

    expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
  })

  it('removes keyboard event listener on unmount', () => {
    const mockCallback = vi.fn()
    const { unmount } = renderHook(() => useKeyboardNavigationLegacy(mockCallback))

    unmount()

    expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
  })

  it('calls callback when Enter key is pressed', () => {
    const mockCallback = vi.fn()
    renderHook(() => useKeyboardNavigationLegacy(mockCallback))

    const keydownEvent = new KeyboardEvent('keydown', { key: 'Enter' })

    act(() => {
      document.dispatchEvent(keydownEvent)
    })

    expect(mockCallback).toHaveBeenCalledWith(keydownEvent)
  })

  it('calls callback when Space key is pressed', () => {
    const mockCallback = vi.fn()
    renderHook(() => useKeyboardNavigationLegacy(mockCallback))

    const keydownEvent = new KeyboardEvent('keydown', { key: ' ' })

    act(() => {
      document.dispatchEvent(keydownEvent)
    })

    expect(mockCallback).toHaveBeenCalledWith(keydownEvent)
  })

  it('handles undefined callback gracefully', () => {
    expect(() => {
      renderHook(() => useKeyboardNavigationLegacy(undefined))
    }).not.toThrow()
  })
})