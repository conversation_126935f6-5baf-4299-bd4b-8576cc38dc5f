import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, act } from '../../../tests/test-utils'
import SearchForm from '../../../client/src/components/SearchForm'

const mockOnSearch = vi.fn()

describe('SearchForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders search form with all fields', () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    // Check for location dropdown (combobox)
    expect(screen.getByRole('combobox')).toBeInTheDocument()
    // Check for date input
    expect(screen.getByPlaceholderText('Select date')).toBeInTheDocument()
    // Check for search button
    expect(screen.getByRole('button', { name: /search properties/i })).toBeInTheDocument()
    // Check for filters button
    expect(screen.getByRole('button', { name: /more filters/i })).toBeInTheDocument()
  })

  it('allows selecting a location from dropdown', () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    // The location dropdown should be rendered - look for the combobox trigger
    const locationTrigger = screen.getByRole('combobox')
    expect(locationTrigger).toBeInTheDocument()
    
    // Check that the trigger shows the default selection
    expect(locationTrigger).toHaveTextContent('All Locations')
  })

  it('updates date input', () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    const dateInput = screen.getByPlaceholderText('Select date')

    act(() => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } })
    })

    expect(dateInput).toHaveValue('2024-01-01')
  })

  it('calls onSearch when form is submitted', async () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    const submitButton = screen.getByRole('button', { name: /search properties/i })
    
    await act(async () => {
      fireEvent.click(submitButton)
    })

    expect(mockOnSearch).toHaveBeenCalled()
  })

  it('handles empty form submission', async () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    const submitButton = screen.getByRole('button', { name: /search properties/i })
    
    await act(async () => {
      fireEvent.click(submitButton)
    })

    expect(mockOnSearch).toHaveBeenCalled()
  })

  it('renders filters button', () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    const filtersButton = screen.getByRole('button', { name: /more filters/i })
    expect(filtersButton).toBeInTheDocument()
  })

  it('submits form with date field', async () => {
    render(<SearchForm onSearch={mockOnSearch} />)

    const dateInput = screen.getByPlaceholderText('Select date')
    const submitButton = screen.getByRole('button', { name: /search properties/i })

    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-12-25' } })
      fireEvent.click(submitButton)
    })

    expect(mockOnSearch).toHaveBeenCalled()
  })
})