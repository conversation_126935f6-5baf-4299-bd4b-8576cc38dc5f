import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminConsentDashboard from '../../../client/src/pages/AdminConsentDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

// Mock dependencies
vi.mock('@/contexts/AuthContext');
vi.mock('@/hooks/use-toast');
vi.mock('@/lib/queryClient');
vi.mock('wouter', () => ({
  useLocation: () => ['/admin/consent', vi.fn()]
}));
vi.mock('@/components/PageTransition', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const mockUseAuth = useAuth as vi.MockedFunction<typeof useAuth>;
const mockUseToast = useToast as vi.MockedFunction<typeof useToast>;
const mockApiRequest = apiRequest as vi.MockedFunction<typeof apiRequest>;

const mockUserData = [
  {
    id: 1,
    username: 'user1',
    email: '<EMAIL>',
    termsAccepted: true,
    privacyPolicyAccepted: true,
    cookiePolicyAccepted: true,
    dataProcessingConsent: true,
    marketingConsent: false,
    consentTimestamp: '2023-01-15T10:30:00Z'
  },
  {
    id: 2,
    username: 'user2',
    email: '<EMAIL>',
    termsAccepted: false,
    privacyPolicyAccepted: false,
    cookiePolicyAccepted: false,
    dataProcessingConsent: false,
    marketingConsent: false,
    consentTimestamp: null
  }
];

describe('AdminConsentDashboard - Simple Tests', () => {
  let queryClient: QueryClient;
  let mockToast: vi.Mock;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, cacheTime: 0 },
        mutations: { retry: false }
      }
    });

    mockToast = vi.fn();
    mockUseToast.mockReturnValue({ toast: mockToast });
    
    mockApiRequest.mockResolvedValue({
      json: () => Promise.resolve(mockUserData)
    });
  });

  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

  describe('Authorization', () => {
    it('should show access denied for non-owner users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      renderWithProviders(<AdminConsentDashboard />);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Access Denied',
        description: 'You need administrator privileges to access this page.',
        variant: 'destructive'
      });
    });

    it('should render dashboard for owner users', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('User Consent Dashboard')).toBeInTheDocument();
      });
    });
  });

  describe('Data Loading', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });
    });

    it('should show loading state initially', () => {
      mockApiRequest.mockImplementation(() => new Promise(() => {})); // Never resolves

      renderWithProviders(<AdminConsentDashboard />);

      expect(screen.getByText('Loading user consent data...')).toBeInTheDocument();
    });

    it('should display user data when loaded', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('user1')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('user2')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('should handle API errors', async () => {
      mockApiRequest.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Error loading user consent data. Please try again.')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });
    });

    it('should filter users based on search input', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('user1')).toBeInTheDocument();
        expect(screen.getByText('user2')).toBeInTheDocument();
      });

      // Search for user1
      const searchInput = screen.getByPlaceholderText('Search by username or email...');
      fireEvent.change(searchInput, { target: { value: 'user1' } });

      await waitFor(() => {
        expect(screen.getByText('user1')).toBeInTheDocument();
        expect(screen.queryByText('user2')).not.toBeInTheDocument();
      });
    });

    it('should show no results message when search returns empty', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('user1')).toBeInTheDocument();
      });

      // Search for non-existent user
      const searchInput = screen.getByPlaceholderText('Search by username or email...');
      fireEvent.change(searchInput, { target: { value: 'nonexistent' } });

      await waitFor(() => {
        expect(screen.getByText('No users found.')).toBeInTheDocument();
      });
    });
  });

  describe('API Integration', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });
    });

    it('should call correct API endpoint', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(mockApiRequest).toHaveBeenCalledWith('GET', '/api/admin/users/consent');
      });
    });

    it('should handle empty data response', async () => {
      mockApiRequest.mockResolvedValue({
        json: () => Promise.resolve([])
      });

      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('No users found.')).toBeInTheDocument();
      });
    });

    it('should handle malformed API response', async () => {
      mockApiRequest.mockResolvedValue({
        json: () => Promise.resolve(null)
      });

      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('No users found.')).toBeInTheDocument();
      });
    });
  });

  describe('Component Rendering', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });
    });

    it('should render main dashboard elements', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('User Consent Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Monitor and manage user consent for legal compliance')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Search by username or email...')).toBeInTheDocument();
        expect(screen.getByText('Refresh Data')).toBeInTheDocument();
        expect(screen.getByText('Export to CSV')).toBeInTheDocument();
      });
    });

    it('should render tab navigation', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'All Users' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'Complete Consent' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'Partial Consent' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'No Consent' })).toBeInTheDocument();
      });
    });

    it('should render table structure when data is loaded', async () => {
      renderWithProviders(<AdminConsentDashboard />);

      await waitFor(() => {
        expect(screen.getByText('ID')).toBeInTheDocument();
        expect(screen.getByText('Username')).toBeInTheDocument();
        expect(screen.getByText('Email')).toBeInTheDocument();
        expect(screen.getByText('Terms')).toBeInTheDocument();
        expect(screen.getByText('Privacy')).toBeInTheDocument();
        expect(screen.getByText('Cookies')).toBeInTheDocument();
        expect(screen.getByText('Data Processing')).toBeInTheDocument();
        expect(screen.getByText('Marketing')).toBeInTheDocument();
        expect(screen.getByText('Consent Date')).toBeInTheDocument();
      });
    });
  });
});