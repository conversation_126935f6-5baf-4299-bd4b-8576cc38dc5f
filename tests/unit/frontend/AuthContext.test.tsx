import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, act, waitFor } from '@testing-library/react'
import { AuthProvider, useAuth } from '../../../client/src/contexts/AuthContext'

// Mock the API request module
vi.mock('../../../client/src/lib/queryClient', () => ({
  apiRequest: vi.fn(),
}))

// Mock global fetch
global.fetch = vi.fn()

import { apiRequest } from '../../../client/src/lib/queryClient'

const mockApiRequest = apiRequest as any
const mockFetch = global.fetch as any

// Test component to test the useAuth hook
function TestComponent() {
  const { user, loading, login, logout: authLogout } = useAuth()

  const handleLogin = () => {
    const testUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user'
    }
    login(testUser, 'test-token')
  }

  return (
    <div>
      <div data-testid="loading">{loading ? 'loading' : 'not-loading'}</div>
      <div data-testid="user-status">{user ? 'logged-in' : 'logged-out'}</div>
      {user && (
        <>
          <div data-testid="user-id">{user.id}</div>
          <div data-testid="username">{user.email}</div>
          <div data-testid="user-role">{user.role}</div>
        </>
      )}
      <button onClick={handleLogin}>
        Login
      </button>
      <button onClick={authLogout}>Logout</button>
    </div>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockFetch.mockResolvedValue({
      ok: false,
      status: 401,
      json: () => Promise.resolve({ message: 'Unauthorized' })
    })
    mockApiRequest.mockResolvedValue(undefined)
    // Clear localStorage before each test
    localStorage.clear()
  })

  it('starts with no user and not loading', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    // Wait for loading to finish
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })
    expect(screen.getByTestId('user-status')).toHaveTextContent('logged-out')
  })

  it('allows user to login', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    const loginButton = screen.getByRole('button', { name: /login/i })
    act(() => {
      loginButton.click()
    })

    // Since we're testing the context behavior, we need to check if the user state updates
    // The login function in the context should set the user state
    await waitFor(() => {
      expect(screen.getByTestId('user-status')).toHaveTextContent('logged-in')
    })
  })

  it('allows user to logout', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    // First login
    const loginButton = screen.getByRole('button', { name: /login/i })
    act(() => {
      loginButton.click()
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-status')).toHaveTextContent('logged-in')
    })

    // Then logout
    const logoutButton = screen.getByRole('button', { name: /logout/i })
    act(() => {
      logoutButton.click()
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-status')).toHaveTextContent('logged-out')
    })
    
    // Verify API was called
    expect(mockApiRequest).toHaveBeenCalledWith('POST', '/api/auth/logout')
  })
})