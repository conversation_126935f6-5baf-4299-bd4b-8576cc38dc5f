import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { apiRequest } from '../../../client/src/lib/queryClient'

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

describe('queryClient', () => {
  beforeEach(() => {
    mockFetch.mockReset()
    mockLocalStorage.getItem.mockReset()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('apiRequest', () => {
    test('makes successful GET request', async () => {
      const mockData = { id: 1, name: 'Test' }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockData),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve(mockData)
        })
      })

      const response = await apiRequest('GET', '/api/test')
      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'GET',
        headers: {},
        body: null,
        credentials: 'include'
      })
      expect(response.ok).toBe(true)
    })

    test('makes successful POST request with data', async () => {
      const requestData = { name: 'Test Item' }
      const responseData = { id: 1, ...requestData }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: () => Promise.resolve(responseData),
        clone: () => ({
          ok: true,
          status: 201,
          json: () => Promise.resolve(responseData)
        })
      })

      const response = await apiRequest('POST', '/api/test', requestData)

      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        credentials: 'include'
      })
      expect(response.ok).toBe(true)
    })

    test('handles JSON error response', async () => {
      const errorResponse = {
        message: 'Validation failed',
        errors: ['Name is required', 'Email is invalid']
      }

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: () => Promise.resolve(errorResponse),
        clone: () => ({
          ok: false,
          status: 400,
          statusText: 'Bad Request',
          json: () => Promise.resolve(errorResponse)
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Validation failed')
    })

    test('handles JSON error response with error field', async () => {
      const errorResponse = {
        error: 'Authentication failed'
      }

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: () => Promise.resolve(errorResponse),
        clone: () => ({
          ok: false,
          status: 401,
          statusText: 'Unauthorized',
          json: () => Promise.resolve(errorResponse)
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Authentication failed')
    })

    test('falls back to statusText when JSON has no message or error', async () => {
      const errorResponse = {
        data: 'some other field'
      }

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: () => Promise.resolve(errorResponse),
        clone: () => ({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          json: () => Promise.resolve(errorResponse)
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Internal Server Error')
    })

    test('handles text error response when JSON parsing fails', async () => {
      const errorText = 'Server temporarily unavailable'

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable',
        json: () => Promise.reject(new Error('Not JSON')),
        text: () => Promise.resolve(errorText),
        clone: () => ({
          ok: false,
          status: 503,
          statusText: 'Service Unavailable',
          json: () => Promise.reject(new Error('Not JSON')),
          text: () => Promise.resolve(errorText)
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow(errorText)
    })

    test('falls back to statusText when both JSON and text parsing fail', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 502,
        statusText: 'Bad Gateway',
        json: () => Promise.reject(new Error('Not JSON')),
        text: () => Promise.reject(new Error('Not text')),
        clone: () => ({
          ok: false,
          status: 502,
          statusText: 'Bad Gateway',
          json: () => Promise.reject(new Error('Not JSON')),
          text: () => Promise.reject(new Error('Not text'))
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Bad Gateway')
    })

    test('handles empty text response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: () => Promise.reject(new Error('Not JSON')),
        text: () => Promise.resolve(''),
        clone: () => ({
          ok: false,
          status: 404,
          statusText: 'Not Found',
          json: () => Promise.reject(new Error('Not JSON')),
          text: () => Promise.resolve('')
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Not Found')
    })

    test('adds Authorization header when token exists', async () => {
      const mockToken = 'test-jwt-token'
      mockLocalStorage.getItem.mockReturnValue(mockToken)

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve({})
        })
      })

      await apiRequest('GET', '/api/test')

      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-jwt-token'
        },
        body: null,
        credentials: 'include'
      })
    })

    test('automatically adds Content-Type for POST requests with body', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve({})
        })
      })

      await apiRequest('POST', '/api/test', { data: 'test' })

      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ data: 'test' }),
        credentials: 'include'
      })
    })

    test('does not add Content-Type for GET requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve({})
        })
      })

      await apiRequest('GET', '/api/test')

      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'GET',
        headers: {},
        body: null,
        credentials: 'include'
      })
    })

    test('handles PUT and DELETE methods', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve({})
        })
      })

      await apiRequest('DELETE', '/api/test/1')

      expect(mockFetch).toHaveBeenCalledWith('/api/test/1', {
        method: 'DELETE',
        headers: {},
        body: null,
        credentials: 'include'
      })
    })

    test('includes credentials in all requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve({})
        })
      })

      await apiRequest('GET', '/api/test')

      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'GET',
        headers: {},
        body: null,
        credentials: 'include'
      })
    })

    test('combines Authorization and Content-Type headers', async () => {
      const mockToken = 'test-jwt-token'
      mockLocalStorage.getItem.mockReturnValue(mockToken)

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        clone: () => ({
          ok: true,
          status: 200,
          json: () => Promise.resolve({})
        })
      })

      await apiRequest('POST', '/api/test', { data: 'test' })

      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-jwt-token'
        },
        body: JSON.stringify({ data: 'test' }),
        credentials: 'include'
      })
    })
  })

  describe('error handling edge cases', () => {
    test('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Network error')
    })

    test('handles response without clone method', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: () => Promise.resolve({ message: 'Test error' }),
        clone: function() { return this }
      }

      mockFetch.mockResolvedValueOnce(mockResponse)

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Test error')
    })

    test('handles malformed JSON response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: () => Promise.reject(new SyntaxError('Unexpected token')),
        text: () => Promise.resolve('Internal Server Error'),
        clone: () => ({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          json: () => Promise.reject(new SyntaxError('Unexpected token')),
          text: () => Promise.resolve('Internal Server Error')
        })
      })

      await expect(apiRequest('GET', '/api/test')).rejects.toThrow('Internal Server Error')
    })
  })
})