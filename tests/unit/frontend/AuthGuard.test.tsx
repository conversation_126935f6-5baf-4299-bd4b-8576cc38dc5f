import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { 
  AuthGuard, 
  RoleGuard, 
  PropertyGuard, 
  ActionGuard, 
  OwnerGuard, 
  AdminGuard 
} from '@/components/AuthGuard';
import { useAuth } from '@/contexts/AuthContext';

// Mock the auth context
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: vi.fn()
}));

const mockUseAuth = useAuth as vi.MockedFunction<typeof useAuth>;

describe('AuthGuard Components', () => {
  const mockChild = <div data-testid="protected-content">Protected Content</div>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('AuthGuard', () => {
    it('should render children when user is authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AuthGuard>{mockChild}</AuthGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should show loading state when auth is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: true,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AuthGuard>{mockChild}</AuthGuard>);
      
      expect(screen.getByText('Checking permissions...')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should show alert message when user is not authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AuthGuard>{mockChild}</AuthGuard>);
      
      expect(screen.getByText('You need to be logged in to access this feature.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should render fallback when provided and user not authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      const fallback = <div data-testid="fallback">Please login</div>;
      render(<AuthGuard fallback={fallback}>{mockChild}</AuthGuard>);
      
      expect(screen.getByTestId('fallback')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should render nothing when showMessage is false and user not authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      const { container } = render(<AuthGuard showMessage={false}>{mockChild}</AuthGuard>);
      
      expect(container.firstChild).toBeNull();
    });

    it('should render custom message when provided', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <AuthGuard customMessage="Custom auth message">
          {mockChild}
        </AuthGuard>
      );
      
      expect(screen.getByText('Custom auth message')).toBeInTheDocument();
    });

    it('should render different message types correctly', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      // Test text message type
      const { rerender } = render(
        <AuthGuard messageType="text">{mockChild}</AuthGuard>
      );
      expect(screen.getByText('You need to be logged in to access this feature.')).toBeInTheDocument();

      // Test button message type
      rerender(<AuthGuard messageType="button">{mockChild}</AuthGuard>);
      expect(screen.getByText('You need to be logged in to access this feature.')).toBeInTheDocument();
    });
  });

  describe('RoleGuard', () => {
    it('should render children when user has required role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<RoleGuard roles={['owner']}>{mockChild}</RoleGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should render children when user has one of multiple required roles', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'admin', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<RoleGuard roles={['owner', 'admin']}>{mockChild}</RoleGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should show error message when user lacks required role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<RoleGuard roles={['owner']}>{mockChild}</RoleGuard>);
      
      expect(screen.getByText('You need owner privileges to access this feature.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle requireAll option correctly', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 1, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      // This should fail because requireAll=true and user only has 'owner' role
      render(<RoleGuard roles={['owner', 'admin']} requireAll={true}>{mockChild}</RoleGuard>);
      
      expect(screen.getByText('You need owner or admin privileges to access this feature.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle no user gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<RoleGuard roles={['owner']}>{mockChild}</RoleGuard>);
      
      expect(screen.getByText('You need owner privileges to access this feature.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('PropertyGuard', () => {
    const mockProperty = {
      id: 1,
      ownerId: 123,
      title: 'Test Property',
      description: 'Test Description',
      location: 'Test Location',
      halfDayPrice: 1000,
      fullDayPrice: 1500,
      amenities: [],
      images: [],
      availability: []
    };

    it('should render children when user owns the property', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<PropertyGuard property={mockProperty}>{mockChild}</PropertyGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should render children when user is admin regardless of ownership', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'admin', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<PropertyGuard property={mockProperty}>{mockChild}</PropertyGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should show error when user does not own property', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<PropertyGuard property={mockProperty}>{mockChild}</PropertyGuard>);
      
      expect(screen.getByText('You can only manage properties that you own.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle null property gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      const { container } = render(<PropertyGuard property={null}>{mockChild}</PropertyGuard>);
      
      expect(container.firstChild).toBeNull();
    });

    it('should allow access when requireOwnership is false', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<PropertyGuard property={mockProperty} requireOwnership={false}>{mockChild}</PropertyGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });
  });

  describe('ActionGuard', () => {
    const mockResourceData = {
      ownerId: 123,
      property: { ownerId: 123 },
      userId: 456
    };

    it('should allow view action for authenticated users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="view" resource="property" resourceData={mockResourceData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should allow create property action for owners', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="create" resource="property" resourceData={mockResourceData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny create property action for regular users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="create" resource="property" resourceData={mockResourceData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByText('You are not authorized to create this property.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should allow edit action for property owner', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="edit" resource="property" resourceData={mockResourceData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny edit action for non-owner', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="edit" resource="property" resourceData={mockResourceData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByText('You are not authorized to edit this property.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should allow admins to perform any action', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 999, role: 'admin', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="delete" resource="property" resourceData={mockResourceData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should handle booking ownership correctly', () => {
      const bookingData = {
        userId: 456,
        property: { ownerId: 999 }
      };
      
      mockUseAuth.mockReturnValue({
        user: { id: 456, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="edit" resource="booking" resourceData={bookingData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should allow property owner to manage bookings on their property', () => {
      const bookingData = {
        userId: 456,
        property: { ownerId: 123 }
      };
      
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="approve" resource="booking" resourceData={bookingData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny approval action for non-property-owner', () => {
      const bookingData = {
        userId: 456,
        property: { ownerId: 999 }
      };
      
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(
        <ActionGuard action="approve" resource="booking" resourceData={bookingData}>
          {mockChild}
        </ActionGuard>
      );
      
      expect(screen.getByText('You are not authorized to approve this booking.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('OwnerGuard', () => {
    it('should render children for owner role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<OwnerGuard>{mockChild}</OwnerGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should render children for admin role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'admin', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<OwnerGuard>{mockChild}</OwnerGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny access for regular users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<OwnerGuard>{mockChild}</OwnerGuard>);
      
      expect(screen.getByText('This feature is only available to property owners.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('AdminGuard', () => {
    it('should render children for admin role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'admin', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AdminGuard>{mockChild}</AdminGuard>);
      
      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny access for owners', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AdminGuard>{mockChild}</AdminGuard>);
      
      expect(screen.getByText('This feature is only available to administrators.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should deny access for regular users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'user', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AdminGuard>{mockChild}</AdminGuard>);
      
      expect(screen.getByText('This feature is only available to administrators.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined user gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: undefined as any,
        loading: false,
        isAuthenticated: false,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<AuthGuard>{mockChild}</AuthGuard>);
      
      expect(screen.getByText('You need to be logged in to access this feature.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle user without role gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, email: '<EMAIL>' } as any,
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<RoleGuard roles={['owner']}>{mockChild}</RoleGuard>);
      
      expect(screen.getByText('You need owner privileges to access this feature.')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should handle empty roles array', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 123, role: 'owner', email: '<EMAIL>' },
        loading: false,
        isAuthenticated: true,
        login: vi.fn(),
        logout: vi.fn(),
        register: vi.fn()
      });

      render(<RoleGuard roles={[]}>{mockChild}</RoleGuard>);
      
      expect(screen.getByText(/You need.*privileges to access this feature/)).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });
});