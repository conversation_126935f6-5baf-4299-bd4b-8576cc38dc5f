import { describe, it, expect } from 'vitest';
import { 
  DEFAULT_BOOKING_CONFIG,
  calculateAdvanceAmountDisplay,
  formatCurrency,
  type BookingConfig 
} from '@/config/booking';

describe('Booking Configuration', () => {
  describe('DEFAULT_BOOKING_CONFIG', () => {
    it('should have correct default values', () => {
      expect(DEFAULT_BOOKING_CONFIG).toEqual({
        fees: {
          cleaning: 15,
        },
        payment: {
          advancePercentage: 30,
          gstRate: 18,
        },
        gst: {
          defaultSupplierState: "MH",
          defaultRecipientState: "MH",
          defaultServiceType: "accommodation",
        },
        ui: {
          maxGuests: 6,
          defaultBookingType: 'morning',
          defaultPaymentMethod: 'advance',
        },
      });
    });

    it('should have valid fee values', () => {
      expect(DEFAULT_BOOKING_CONFIG.fees.cleaning).toBeGreaterThan(0);
      expect(typeof DEFAULT_BOOKING_CONFIG.fees.cleaning).toBe('number');
    });

    it('should have valid payment configuration', () => {
      expect(DEFAULT_BOOKING_CONFIG.payment.advancePercentage).toBeGreaterThan(0);
      expect(DEFAULT_BOOKING_CONFIG.payment.advancePercentage).toBeLessThanOrEqual(100);
      expect(DEFAULT_BOOKING_CONFIG.payment.gstRate).toBeGreaterThanOrEqual(0);
      expect(DEFAULT_BOOKING_CONFIG.payment.gstRate).toBeLessThanOrEqual(100);
    });

    it('should have valid GST configuration', () => {
      expect(DEFAULT_BOOKING_CONFIG.gst.defaultSupplierState).toBe('MH');
      expect(DEFAULT_BOOKING_CONFIG.gst.defaultRecipientState).toBe('MH');
      expect(DEFAULT_BOOKING_CONFIG.gst.defaultServiceType).toBe('accommodation');
      
      // State codes should be 2 characters
      expect(DEFAULT_BOOKING_CONFIG.gst.defaultSupplierState).toHaveLength(2);
      expect(DEFAULT_BOOKING_CONFIG.gst.defaultRecipientState).toHaveLength(2);
    });

    it('should have valid UI configuration', () => {
      expect(DEFAULT_BOOKING_CONFIG.ui.maxGuests).toBeGreaterThan(0);
      expect(['morning', 'full_day']).toContain(DEFAULT_BOOKING_CONFIG.ui.defaultBookingType);
      expect(['advance', 'pay_at_venue']).toContain(DEFAULT_BOOKING_CONFIG.ui.defaultPaymentMethod);
    });

    it('should be immutable (not accidentally modified)', () => {
      const originalConfig = { ...DEFAULT_BOOKING_CONFIG };
      
      // Attempt to modify (this should not affect the original)
      const configCopy = DEFAULT_BOOKING_CONFIG;
      // Note: This is a shallow copy, so deep modifications could still affect it
      // In real usage, the config should be deep-frozen or handled carefully
      
      expect(DEFAULT_BOOKING_CONFIG).toEqual(originalConfig);
    });
  });

  describe('calculateAdvanceAmountDisplay', () => {
    it('should calculate advance amount with default percentage', () => {
      const totalPrice = 1000;
      const result = calculateAdvanceAmountDisplay(totalPrice);
      
      // 30% of 1000 = 300
      expect(result).toBe(300);
    });

    it('should calculate advance amount with custom percentage', () => {
      const totalPrice = 1000;
      const customPercentage = 50;
      const result = calculateAdvanceAmountDisplay(totalPrice, customPercentage);
      
      // 50% of 1000 = 500
      expect(result).toBe(500);
    });

    it('should handle zero total price', () => {
      const result = calculateAdvanceAmountDisplay(0);
      expect(result).toBe(0);
    });

    it('should handle zero percentage', () => {
      const totalPrice = 1000;
      const result = calculateAdvanceAmountDisplay(totalPrice, 0);
      expect(result).toBe(0);
    });

    it('should handle 100% advance', () => {
      const totalPrice = 1000;
      const result = calculateAdvanceAmountDisplay(totalPrice, 100);
      expect(result).toBe(1000);
    });

    it('should round to nearest integer', () => {
      const totalPrice = 333; // 30% of 333 = 99.9
      const result = calculateAdvanceAmountDisplay(totalPrice);
      expect(result).toBe(100); // Should round to 100
    });

    it('should handle decimal amounts correctly', () => {
      const totalPrice = 1234.56;
      const result = calculateAdvanceAmountDisplay(totalPrice, 25);
      
      // 25% of 1234.56 = 308.64, rounded = 309
      expect(result).toBe(309);
    });

    it('should handle negative amounts gracefully', () => {
      const totalPrice = -1000;
      const result = calculateAdvanceAmountDisplay(totalPrice);
      
      // Should handle negative input (though this might not be expected in real usage)
      expect(result).toBe(-300);
    });

    it('should handle very large numbers', () => {
      const totalPrice = 1000000; // 1 million
      const result = calculateAdvanceAmountDisplay(totalPrice, 30);
      
      expect(result).toBe(300000);
    });

    it('should handle fractional percentages', () => {
      const totalPrice = 1000;
      const result = calculateAdvanceAmountDisplay(totalPrice, 33.33);
      
      // 33.33% of 1000 = 333.3, rounded = 333
      expect(result).toBe(333);
    });

    it('should be consistent with multiple calls', () => {
      const totalPrice = 1500;
      const percentage = 25;
      
      const result1 = calculateAdvanceAmountDisplay(totalPrice, percentage);
      const result2 = calculateAdvanceAmountDisplay(totalPrice, percentage);
      
      expect(result1).toBe(result2);
      expect(result1).toBe(375); // 25% of 1500
    });

    describe('Edge Cases', () => {
      it('should handle very small amounts', () => {
        const totalPrice = 1;
        const result = calculateAdvanceAmountDisplay(totalPrice, 30);
        
        // 30% of 1 = 0.3, rounded = 0
        expect(result).toBe(0);
      });

      it('should handle infinity values', () => {
        const result = calculateAdvanceAmountDisplay(Infinity, 30);
        expect(result).toBe(Infinity);
      });

      it('should handle NaN values', () => {
        const result = calculateAdvanceAmountDisplay(NaN, 30);
        expect(Number.isNaN(result)).toBe(true);
      });

      it('should handle percentage over 100%', () => {
        const totalPrice = 1000;
        const result = calculateAdvanceAmountDisplay(totalPrice, 150);
        
        // 150% of 1000 = 1500
        expect(result).toBe(1500);
      });
    });
  });

  describe('formatCurrency', () => {
    it('should format currency in INR', () => {
      const amount = 1000;
      const result = formatCurrency(amount);
      
      expect(result).toBe('₹1,000');
    });

    it('should format zero amount', () => {
      const result = formatCurrency(0);
      expect(result).toBe('₹0');
    });

    it('should format large amounts with commas', () => {
      const amount = 1234567;
      const result = formatCurrency(amount);
      
      expect(result).toBe('₹12,34,567');
    });

    it('should format negative amounts', () => {
      const amount = -1000;
      const result = formatCurrency(amount);
      
      expect(result).toBe('-₹1,000');
    });

    it('should not show decimal places for whole numbers', () => {
      const amount = 1500;
      const result = formatCurrency(amount);
      
      expect(result).toBe('₹1,500');
      expect(result).not.toContain('.00');
    });

    it('should round decimal amounts to whole numbers', () => {
      const amount = 1500.67;
      const result = formatCurrency(amount);
      
      // Should round to nearest integer
      expect(result).toBe('₹1,501');
    });

    it('should handle very small decimal amounts', () => {
      const amount = 0.45;
      const result = formatCurrency(amount);
      
      // Should round to 0
      expect(result).toBe('₹0');
    });

    it('should handle very large amounts', () => {
      const amount = 1000000000; // 1 billion
      const result = formatCurrency(amount);
      
      expect(result).toBe('₹1,00,00,00,000');
    });

    it('should use Indian number formatting (lakh, crore style)', () => {
      const amount = 100000; // 1 lakh
      const result = formatCurrency(amount);
      
      expect(result).toBe('₹1,00,000');
    });

    it('should format crore amounts correctly', () => {
      const amount = 10000000; // 1 crore
      const result = formatCurrency(amount);
      
      expect(result).toBe('₹1,00,00,000');
    });

    describe('Edge Cases', () => {
      it('should handle infinity', () => {
        const result = formatCurrency(Infinity);
        expect(result).toContain('∞'); // Infinity symbol
      });

      it('should handle NaN', () => {
        const result = formatCurrency(NaN);
        expect(result).toContain('NaN');
      });

      it('should handle very precise decimal values', () => {
        const amount = 1234.5678901234;
        const result = formatCurrency(amount);
        
        // Should round to whole number
        expect(result).toBe('₹1,235');
      });

      it('should be consistent across multiple calls', () => {
        const amount = 1500.25;
        const result1 = formatCurrency(amount);
        const result2 = formatCurrency(amount);
        
        expect(result1).toBe(result2);
      });
    });
  });

  describe('Type Safety', () => {
    it('should enforce BookingConfig interface structure', () => {
      const customConfig: BookingConfig = {
        fees: {
          cleaning: 20,
        },
        payment: {
          advancePercentage: 40,
          gstRate: 18,
        },
        gst: {
          defaultSupplierState: "KA",
          defaultRecipientState: "MH",
          defaultServiceType: "accommodation",
        },
        ui: {
          maxGuests: 8,
          defaultBookingType: 'full_day',
          defaultPaymentMethod: 'pay_at_venue',
        },
      };

      // This test ensures the interface is correctly defined
      expect(customConfig.fees.cleaning).toBe(20);
      expect(customConfig.payment.advancePercentage).toBe(40);
      expect(customConfig.gst.defaultSupplierState).toBe('KA');
      expect(customConfig.ui.maxGuests).toBe(8);
    });

    it('should validate booking type values', () => {
      const validBookingTypes: Array<'morning' | 'full_day'> = ['morning', 'full_day'];
      
      validBookingTypes.forEach(type => {
        expect(['morning', 'full_day']).toContain(type);
      });
    });

    it('should validate payment method values', () => {
      const validPaymentMethods: Array<'advance' | 'pay_at_venue'> = ['advance', 'pay_at_venue'];
      
      validPaymentMethods.forEach(method => {
        expect(['advance', 'pay_at_venue']).toContain(method);
      });
    });
  });

  describe('Integration Tests', () => {
    it('should work together for complete booking calculation display', () => {
      const basePrice = 2000;
      const cleaningFee = DEFAULT_BOOKING_CONFIG.fees.cleaning;
      const totalPrice = basePrice + cleaningFee;
      
      const advanceAmount = calculateAdvanceAmountDisplay(totalPrice);
      const formattedAdvance = formatCurrency(advanceAmount);
      const formattedTotal = formatCurrency(totalPrice);
      
      expect(totalPrice).toBe(2015); // 2000 + 15
      expect(advanceAmount).toBe(605); // 30% of 2015, rounded
      expect(formattedAdvance).toBe('₹605');
      expect(formattedTotal).toBe('₹2,015');
    });

    it('should handle custom configuration override', () => {
      const customConfig: BookingConfig = {
        ...DEFAULT_BOOKING_CONFIG,
        fees: {
          cleaning: 25,
        },
        payment: {
          ...DEFAULT_BOOKING_CONFIG.payment,
          advancePercentage: 50,
        },
      };

      const basePrice = 1000;
      const totalPrice = basePrice + customConfig.fees.cleaning;
      const advanceAmount = calculateAdvanceAmountDisplay(
        totalPrice, 
        customConfig.payment.advancePercentage
      );

      expect(totalPrice).toBe(1025);
      expect(advanceAmount).toBe(513); // 50% of 1025, rounded
      expect(formatCurrency(advanceAmount)).toBe('₹513');
    });

    it('should maintain consistency between config values and calculations', () => {
      // Test that the default percentage used in calculateAdvanceAmountDisplay 
      // matches the config
      const amount = 1000;
      
      const resultWithDefault = calculateAdvanceAmountDisplay(amount);
      const resultWithConfigValue = calculateAdvanceAmountDisplay(
        amount, 
        DEFAULT_BOOKING_CONFIG.payment.advancePercentage
      );
      
      expect(resultWithDefault).toBe(resultWithConfigValue);
    });
  });

  describe('Performance Tests', () => {
    it('should handle calculations efficiently for large datasets', () => {
      const startTime = performance.now();
      
      // Simulate multiple booking calculations
      for (let i = 0; i < 1000; i++) {
        const price = Math.random() * 10000;
        const advance = calculateAdvanceAmountDisplay(price);
        formatCurrency(advance);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete 1000 operations in under 100ms (generous threshold)
      expect(duration).toBeLessThan(100);
    });

    it('should not have memory leaks with repeated calls', () => {
      const price = 1500;
      
      // Call functions many times to check for memory issues
      for (let i = 0; i < 10000; i++) {
        calculateAdvanceAmountDisplay(price);
        formatCurrency(price);
      }
      
      // If this test completes without throwing, there are no obvious memory issues
      expect(true).toBe(true);
    });
  });

  describe('Security Considerations', () => {
    it('should not expose sensitive configuration in client-side functions', () => {
      // These functions should only be for UI display, not payment processing
      const config = DEFAULT_BOOKING_CONFIG;
      
      // Ensure these are clearly marked as display-only
      expect(typeof calculateAdvanceAmountDisplay).toBe('function');
      
      // The config itself doesn't contain sensitive info, but the comment 
      // should warn about server-side validation
      expect(config.payment.advancePercentage).toBeGreaterThan(0);
    });

    it('should handle potentially malicious input safely', () => {
      // Test with potentially problematic values
      const maliciousInputs = [
        Number.MAX_SAFE_INTEGER,
        Number.MIN_SAFE_INTEGER,
        -Infinity,
        Infinity,
        NaN,
        1e100,
        -1e100
      ];

      maliciousInputs.forEach(input => {
        expect(() => {
          calculateAdvanceAmountDisplay(input);
          formatCurrency(input);
        }).not.toThrow();
      });
    });
  });
});