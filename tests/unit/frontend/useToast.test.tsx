import React from 'react'
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useToast, toast, reducer } from '../../../client/src/hooks/use-toast'

// Test component to test the useToast hook
function TestToastComponent() {
  const { toasts, toast: toastFn, dismiss } = useToast()

  return (
    <div>
      <div data-testid="toast-count">{toasts.length}</div>
      <div data-testid="toasts">
        {toasts.map((t, index) => (
          <div key={t.id} data-testid={`toast-${index}`}>
            <span data-testid={`toast-${index}-title`}>{t.title}</span>
            <span data-testid={`toast-${index}-description`}>{t.description}</span>
            <span data-testid={`toast-${index}-open`}>{t.open?.toString()}</span>
            <span data-testid={`toast-${index}-id`}>{t.id}</span>
          </div>
        ))}
      </div>
      <button 
        onClick={() => toastFn({ title: 'Test Toast', description: 'Test Description' })}
        data-testid="add-toast"
      >
        Add Toast
      </button>
      <button 
        onClick={() => toastFn({ title: 'Another Toast', description: 'Another Description' })}
        data-testid="add-another-toast"
      >
        Add Another Toast
      </button>
      <button 
        onClick={() => dismiss()}
        data-testid="dismiss-all"
      >
        Dismiss All
      </button>
      <button 
        onClick={() => toasts.length > 0 && dismiss(toasts[0].id)}
        data-testid="dismiss-first"
      >
        Dismiss First
      </button>
    </div>
  )
}

// Test component to test the standalone toast function
function TestStandaloneToast() {
  return (
    <div>
      <button 
        onClick={() => toast({ title: 'Standalone Toast', description: 'Standalone Description' })}
        data-testid="standalone-toast"
      >
        Standalone Toast
      </button>
      <button 
        onClick={() => {
          const t = toast({ title: 'Dismissible Toast' })
          setTimeout(() => t.dismiss(), 100)
        }}
        data-testid="auto-dismiss-toast"
      >
        Auto Dismiss Toast
      </button>
    </div>
  )
}

describe('useToast', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Note: Toast cleanup is handled within test components
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('Basic Toast Operations', () => {
    test('starts with empty toast list', () => {
      render(<TestToastComponent />)

      expect(screen.getByTestId('toast-count')).toHaveTextContent('0')
    })

    test('adds toast when toast function is called', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))

      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Test Toast')
      expect(screen.getByTestId('toast-0-description')).toHaveTextContent('Test Description')
      expect(screen.getByTestId('toast-0-open')).toHaveTextContent('true')
    })

    test('replaces toasts when limit is reached', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Test Toast')

      // Adding another toast should replace the first one due to TOAST_LIMIT = 1
      await user.click(screen.getByTestId('add-another-toast'))
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Another Toast')
    })

    test('limits toast count to TOAST_LIMIT', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      // Add multiple toasts (limit should be 1 based on the hook implementation)
      await user.click(screen.getByTestId('add-toast'))
      await user.click(screen.getByTestId('add-another-toast'))

      // Should only show 1 toast due to limit (TOAST_LIMIT = 1)
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      // The newer toast should be shown (most recent one)
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Another Toast')
    })

    test('assigns unique IDs to toasts', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))
      const firstId = screen.getByTestId('toast-0-id').textContent

      // Add another toast - due to TOAST_LIMIT=1, this will replace the first one
      await user.click(screen.getByTestId('add-another-toast'))
      const secondId = screen.getByTestId('toast-0-id').textContent

      // IDs should be different
      expect(firstId).not.toBe(secondId)
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Another Toast')
    })
  })

  describe('Toast Dismissal', () => {
    test('dismisses all toasts when dismiss() is called without ID', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')

      await user.click(screen.getByTestId('dismiss-all'))

      // Toast should still be present but marked as closed (open: false)
      // Due to TOAST_REMOVE_DELAY, it won't be removed immediately
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-open')).toHaveTextContent('false')
    })

    test('dismisses specific toast when ID is provided', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')

      await user.click(screen.getByTestId('dismiss-first'))

      // Toast should still be present but marked as closed (open: false)
      // Due to TOAST_REMOVE_DELAY, it won't be removed immediately
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-open')).toHaveTextContent('false')
    })

    test('sets toast open to false when dismissed', async () => {
      render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))
      expect(screen.getByTestId('toast-0-open')).toHaveTextContent('true')

      await user.click(screen.getByTestId('dismiss-first'))

      // Toast should be marked as closed but not immediately removed
      expect(screen.getByTestId('toast-0-open')).toHaveTextContent('false')
    })
  })

  describe('Standalone Toast Function', () => {
    test('standalone toast function creates toasts', async () => {
      render(
        <div>
          <TestStandaloneToast />
          <TestToastComponent />
        </div>
      )
      const user = userEvent.setup()

      await user.click(screen.getByTestId('standalone-toast'))

      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Standalone Toast')
    })

    test('toast function returns object with dismiss method', async () => {
      render(<TestStandaloneToast />)
      const user = userEvent.setup()

      // This test uses a toast that auto-dismisses itself
      await user.click(screen.getByTestId('auto-dismiss-toast'))

      // The toast should be created and then dismissed
      // We'll need to check this through the component
    })
  })

  describe('Toast State Management', () => {
    test('maintains toast state across re-renders', async () => {
      const { rerender } = render(<TestToastComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')

      rerender(<TestToastComponent />)
      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
    })
  })

  describe('Toast Properties', () => {
    test('creates toast with all properties', async () => {
      function TestToastWithProps() {
        const { toast: toastFn } = useToast()

        return (
          <button 
            onClick={() => toastFn({ 
              title: 'Complex Toast',
              description: 'With description',
              variant: 'destructive'
            })}
            data-testid="complex-toast"
          >
            Complex Toast
          </button>
        )
      }

      render(
        <div>
          <TestToastWithProps />
          <TestToastComponent />
        </div>
      )
      const user = userEvent.setup()

      await user.click(screen.getByTestId('complex-toast'))

      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Complex Toast')
      expect(screen.getByTestId('toast-0-description')).toHaveTextContent('With description')
    })

    test('handles toast without description', async () => {
      function TestToastTitleOnly() {
        const { toast: toastFn } = useToast()

        return (
          <button 
            onClick={() => toastFn({ title: 'Title Only' })}
            data-testid="title-only-toast"
          >
            Title Only Toast
          </button>
        )
      }

      render(
        <div>
          <TestToastTitleOnly />
          <TestToastComponent />
        </div>
      )
      const user = userEvent.setup()

      await user.click(screen.getByTestId('title-only-toast'))

      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
      expect(screen.getByTestId('toast-0-title')).toHaveTextContent('Title Only')
    })
  })

  describe('Error Handling', () => {
    test('handles empty toast creation', async () => {
      function TestEmptyToast() {
        const { toast: toastFn } = useToast()

        return (
          <button 
            onClick={() => toastFn({})}
            data-testid="empty-toast"
          >
            Empty Toast
          </button>
        )
      }

      render(
        <div>
          <TestEmptyToast />
          <TestToastComponent />
        </div>
      )
      const user = userEvent.setup()

      expect(async () => {
        await user.click(screen.getByTestId('empty-toast'))
      }).not.toThrow()

      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
    })

    test('handles dismiss with invalid ID gracefully', async () => {
      // Create a test component that can call dismiss with invalid ID
      function TestDismissComponent() {
        const { toast, dismiss } = useToast()

        return (
          <div>
            <TestToastComponent />
            <button 
              onClick={() => dismiss('invalid-id')}
              data-testid="dismiss-invalid"
            >
              Dismiss Invalid ID
            </button>
          </div>
        )
      }

      render(<TestDismissComponent />)
      const user = userEvent.setup()

      await user.click(screen.getByTestId('add-toast'))

      // Should not throw when dismissing with invalid ID
      expect(async () => {
        await user.click(screen.getByTestId('dismiss-invalid'))
      }).not.toThrow()

      expect(screen.getByTestId('toast-count')).toHaveTextContent('1')
    })
  })
})

describe('Toast Reducer', () => {
  const initialState = { toasts: [] }

  const mockToast = {
    id: '1',
    title: 'Test Toast',
    description: 'Test Description',
    open: true
  }

  describe('ADD_TOAST', () => {
    test('adds toast to empty state', () => {
      const action = { type: 'ADD_TOAST' as const, toast: mockToast }
      const newState = reducer(initialState, action)

      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0]).toEqual(mockToast)
    })

    test('adds toast to beginning of list (respects limit)', () => {
      const existingToast = { ...mockToast, id: '2', title: 'Existing' }
      const state = { toasts: [existingToast] }
      const newToast = { ...mockToast, id: '3', title: 'New' }

      const action = { type: 'ADD_TOAST' as const, toast: newToast }
      const newState = reducer(state, action)

      // With TOAST_LIMIT = 1, should only keep the new toast
      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0]).toEqual(newToast)
    })

    test('respects toast limit', () => {
      const existingToast = { ...mockToast, id: '2' }
      const state = { toasts: [existingToast] }
      const newToast = { ...mockToast, id: '3' }

      const action = { type: 'ADD_TOAST' as const, toast: newToast }
      const newState = reducer(state, action)

      // Should only keep 1 toast (TOAST_LIMIT = 1)
      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0]).toEqual(newToast)
    })
  })

  describe('UPDATE_TOAST', () => {
    test('updates existing toast', () => {
      const state = { toasts: [mockToast] }
      const updates = { title: 'Updated Title', description: 'Updated Description' }

      const action = { type: 'UPDATE_TOAST' as const, toast: { id: '1', ...updates } }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0].title).toBe('Updated Title')
      expect(newState.toasts[0].description).toBe('Updated Description')
      expect(newState.toasts[0].id).toBe('1')
    })

    test('does not update non-existent toast', () => {
      const state = { toasts: [mockToast] }

      const action = { type: 'UPDATE_TOAST' as const, toast: { id: 'non-existent', title: 'Updated' } }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0]).toEqual(mockToast)
    })

    test('partially updates toast properties', () => {
      const state = { toasts: [mockToast] }

      const action = { type: 'UPDATE_TOAST' as const, toast: { id: '1', title: 'New Title' } }
      const newState = reducer(state, action)

      expect(newState.toasts[0].title).toBe('New Title')
      expect(newState.toasts[0].description).toBe('Test Description') // Should remain unchanged
    })
  })

  describe('DISMISS_TOAST', () => {
    test('dismisses specific toast', () => {
      const toast1 = { ...mockToast, id: '1' }
      const toast2 = { ...mockToast, id: '2' }
      const state = { toasts: [toast1, toast2] }

      const action = { type: 'DISMISS_TOAST' as const, toastId: '1' }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(2)
      expect(newState.toasts[0].open).toBe(false) // toast1 should be dismissed
      expect(newState.toasts[1].open).toBe(true)  // toast2 should remain open
    })

    test('dismisses all toasts when no ID provided', () => {
      const toast1 = { ...mockToast, id: '1' }
      const toast2 = { ...mockToast, id: '2' }
      const state = { toasts: [toast1, toast2] }

      const action = { type: 'DISMISS_TOAST' as const }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(2)
      expect(newState.toasts[0].open).toBe(false)
      expect(newState.toasts[1].open).toBe(false)
    })
  })

  describe('REMOVE_TOAST', () => {
    test('removes specific toast', () => {
      const toast1 = { ...mockToast, id: '1' }
      const toast2 = { ...mockToast, id: '2' }
      const state = { toasts: [toast1, toast2] }

      const action = { type: 'REMOVE_TOAST' as const, toastId: '1' }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0].id).toBe('2')
    })

    test('removes all toasts when no ID provided', () => {
      const toast1 = { ...mockToast, id: '1' }
      const toast2 = { ...mockToast, id: '2' }
      const state = { toasts: [toast1, toast2] }

      const action = { type: 'REMOVE_TOAST' as const }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(0)
    })

    test('handles removal of non-existent toast', () => {
      const state = { toasts: [mockToast] }

      const action = { type: 'REMOVE_TOAST' as const, toastId: 'non-existent' }
      const newState = reducer(state, action)

      expect(newState.toasts).toHaveLength(1)
      expect(newState.toasts[0]).toEqual(mockToast)
    })
  })

  describe('Invalid Actions', () => {
    test('returns current state for unknown action types', () => {
      const state = { toasts: [mockToast] }

      // @ts-ignore - intentionally testing invalid action
      const action = { type: 'UNKNOWN_ACTION', data: 'test' }
      const newState = reducer(state, action)

      expect(newState).toBe(state)
    })
  })
})