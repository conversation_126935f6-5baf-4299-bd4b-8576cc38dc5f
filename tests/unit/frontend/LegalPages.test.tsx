
import React from 'react'
import { describe, it, expect } from 'vitest'
import { render, screen } from '../../../tests/test-utils'
import PrivacyPolicy from '@/pages/PrivacyPolicy'
import CookiePolicy from '@/pages/CookiePolicy'
import TermsOfService from '@/pages/TermsOfService'

describe('Legal Pages', () => {
  describe('PrivacyPolicy', () => {
    it('renders privacy policy content', () => {
      render(<PrivacyPolicy />)
      
      expect(screen.getByText('Privacy Policy')).toBeInTheDocument()
      expect(screen.getByText('1. Introduction')).toBeInTheDocument()
      expect(screen.getByText('2. Information We Collect')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    it('includes all required sections', () => {
      render(<PrivacyPolicy />)
      
      const sections = [
        '1. Introduction',
        '2. Information We Collect',
        '3. How We Use Your Information',
        '4. Data Sharing and Disclosure',
        '5. Data Security',
        '6. Your Data Protection Rights',
        '7. Cookies Policy',
        '8. Changes to This Privacy Policy',
        '9. Contact Us'
      ]

      sections.forEach(section => {
        expect(screen.getByText(section)).toBeInTheDocument()
      })
    })
  })

  describe('CookiePolicy', () => {
    it('renders cookie policy content', () => {
      render(<CookiePolicy />)
      
      expect(screen.getByText('Cookie Policy')).toBeInTheDocument()
      expect(screen.getByText('1. What Are Cookies')).toBeInTheDocument()
      expect(screen.getByText('2. Types of Cookies We Use')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    it('includes cookie management section', () => {
      render(<CookiePolicy />)
      
      expect(screen.getByText('3. How to Manage Cookies')).toBeInTheDocument()
      expect(screen.getByText('3.1 Browser Controls')).toBeInTheDocument()
      expect(screen.getByText('3.2 Our Cookie Consent Tool')).toBeInTheDocument()
    })

    it('includes different types of cookies', () => {
      render(<CookiePolicy />)
      
      expect(screen.getByText('2.1 Essential Cookies')).toBeInTheDocument()
      expect(screen.getByText('2.2 Analytical/Performance Cookies')).toBeInTheDocument()
      expect(screen.getByText('2.3 Functionality Cookies')).toBeInTheDocument()
      expect(screen.getByText('2.4 Targeting/Advertising Cookies')).toBeInTheDocument()
    })
  })

  describe('TermsOfService', () => {
    it('renders terms of service content', () => {
      render(<TermsOfService />)
      
      expect(screen.getByText('Terms of Service')).toBeInTheDocument()
      expect(screen.getByText('1. Introduction')).toBeInTheDocument()
    })

    it('displays last updated date', () => {
      render(<TermsOfService />)
      
      expect(screen.getByText(/Last Updated:/)).toBeInTheDocument()
    })
  })
})
