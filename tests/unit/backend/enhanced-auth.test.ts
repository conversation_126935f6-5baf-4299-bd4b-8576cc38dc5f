import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest'
import request from 'supertest'
import express from 'express'
import jwt from 'jsonwebtoken'
import { authenticate, authorize } from '../../../server/routes/auth'
import { TokenBlacklistService } from '../../../server/services/TokenBlacklistService'

// Mock dependencies
vi.mock('../../../server/services/TokenBlacklistService', () => ({
  TokenBlacklistService: {
    isTokenBlacklisted: vi.fn(),
    blacklistToken: vi.fn(),
    getBlacklistStats: vi.fn(),
    clearExpiredTokens: vi.fn(),
    blacklistUserTokens: vi.fn()
  }
}))

vi.mock('../../../server/services/CircuitBreaker', () => ({
  TokenBlacklistCircuitBreaker: {
    execute: vi.fn(async (fn, options) => {
      try {
        return await fn()
      } catch (error) {
        // When the circuit breaker catches an error, it should use the fallback
        if (options?.fallback) {
          return options.fallback()
        }
        throw error
      }
    })
  }
}))

vi.mock('../../../server/config', () => ({
  config: {
    jwt: {
      secret: 'test-secret-key'
    },
    app: {
      useSecureCookies: false
    },
    isDevelopment: () => true,
    isProduction: () => false
  }
}))

vi.mock('../../../server/middlewares/errorHandler', () => ({
  sendError: vi.fn((res, error) => {
    res.status(error.statusCode || 500).json({ error: error.message })
  }),
  sendSuccess: vi.fn((res, data, message, statusCode = 200) => {
    res.status(statusCode).json({ success: true, data, message })
  }),
  asyncHandler: vi.fn((fn) => fn),
  AuthenticationError: class AuthenticationError extends Error {
    statusCode = 401
    constructor(message: string) {
      super(message)
    }
  },
  ValidationError: class ValidationError extends Error {
    statusCode = 400
    constructor(message: string) {
      super(message)
    }
  },
  ConflictError: class ConflictError extends Error {
    statusCode = 409
    constructor(message: string) {
      super(message)
    }
  }
}))

describe('Enhanced Authentication Functionality', () => {
  let app: express.Application
  let mockToken: string
  let validPayload: any

  beforeEach(() => {
    app = express()
    app.use(express.json())
    // Mock cookie parser
    app.use((req: any, res, next) => {
      req.cookies = req.cookies || {}
      res.cookie = vi.fn()
      res.clearCookie = vi.fn()
      next()
    })

    validPayload = { userId: 123, role: 'user' }
    mockToken = jwt.sign(validPayload, 'test-secret-key', { expiresIn: '1h' })

    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('authenticate middleware', () => {
    test('should authenticate valid token from Authorization header', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`)

      expect(response.status).toBe(200)
      expect(response.body.user).toMatchObject(validPayload)
      expect(TokenBlacklistService.isTokenBlacklisted).toHaveBeenCalledWith(mockToken)
    })

    test('should authenticate valid token from cookie', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)

      app.get('/test', (req: any, res, next) => {
        req.cookies = { token: mockToken }
        next()
      }, authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(200)
      expect(response.body.user).toMatchObject(validPayload)
    })

    test('should reject request when no token provided', async () => {
      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Authentication required')
    })

    test('should reject blacklisted token', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(true)

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Token has been revoked')
      expect(TokenBlacklistService.isTokenBlacklisted).toHaveBeenCalledWith(mockToken)
    })

    test('should reject invalid token', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const invalidToken = 'invalid.jwt.token'

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${invalidToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Invalid or expired token')
    })

    test('should reject expired token', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const expiredToken = jwt.sign(validPayload, 'test-secret-key', { expiresIn: '-1h' })

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${expiredToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Invalid or expired token')
    })

    test('should handle TokenBlacklistService errors gracefully', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockRejectedValue(new Error('Cache error'))

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`)

      // SECURITY: When blacklist service fails, it should fail closed (deny access)
      // The circuit breaker's fallback treats the token as blacklisted for security
      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Token has been revoked')
    })

    test('should prefer Authorization header over cookie', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const cookieToken = jwt.sign({ userId: 999, role: 'admin' }, 'test-secret-key')

      app.get('/test', (req: any, res, next) => {
        req.cookies = { token: cookieToken }
        next()
      }, authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`)

      expect(response.status).toBe(200)
      // The Authorization header token should be used, not the cookie
      expect(response.body.user).toMatchObject({ userId: 123, role: 'user' })
      expect(TokenBlacklistService.isTokenBlacklisted).toHaveBeenCalledWith(mockToken)
    })
  })

  describe('authorize middleware', () => {
    beforeEach(() => {
      app.use((req: any, res, next) => {
        req.user = { userId: 123, role: 'user' }
        next()
      })
    })

    test('should allow access for authorized role', async () => {
      app.get('/test', authorize(['user', 'admin']), (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
    })

    test('should deny access for unauthorized role', async () => {
      app.get('/test', authorize(['admin']), (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(403)
      expect(response.body.error.message).toBe('Insufficient permissions')
    })

    test('should deny access when no user in request', async () => {
      app.use((req: any, res, next) => {
        req.user = undefined
        next()
      })

      app.get('/test', authorize(['user']), (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Authentication required')
    })

    test('should handle multiple allowed roles', async () => {
      const roles = ['user', 'moderator', 'admin']
      
      app.get('/test', authorize(roles), (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
    })

    test('should handle empty roles array', async () => {
      app.get('/test', authorize([]), (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(403)
      expect(response.body.error.message).toBe('Insufficient permissions')
    })

    test('should be case-sensitive for roles', async () => {
      app.use((req: any, res, next) => {
        req.user = { userId: 123, role: 'User' } // Capital U
        next()
      })

      app.get('/test', authorize(['user']), (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/test')

      expect(response.status).toBe(403)
      expect(response.body.error.message).toBe('Insufficient permissions')
    })
  })

  describe('auth middleware chain', () => {
    test('should work together in authentication chain', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const adminToken = jwt.sign({ userId: 456, role: 'admin' }, 'test-secret-key')

      app.get('/admin', authenticate, authorize(['admin']), (req: any, res) => {
        res.json({ user: req.user, message: 'Admin access granted' })
      })

      const response = await request(app)
        .get('/admin')
        .set('Authorization', `Bearer ${adminToken}`)

      expect(response.status).toBe(200)
      expect(response.body.user.role).toBe('admin')
      expect(response.body.message).toBe('Admin access granted')
    })

    test('should reject in authentication step before authorization', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(true)
      const adminToken = jwt.sign({ userId: 456, role: 'admin' }, 'test-secret-key')

      app.get('/admin', authenticate, authorize(['admin']), (req: any, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/admin')
        .set('Authorization', `Bearer ${adminToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Token has been revoked')
    })

    test('should reject in authorization step after successful authentication', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)

      app.get('/admin', authenticate, authorize(['admin']), (req: any, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .get('/admin')
        .set('Authorization', `Bearer ${mockToken}`) // user role token

      expect(response.status).toBe(403)
      expect(response.body.error.message).toBe('Insufficient permissions')
    })
  })

  describe('Token format handling', () => {
    test('should handle Bearer token without space after Bearer', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer${mockToken}`) // No space

      expect(response.status).toBe(401) // Should fail due to improper format
    })

    test('should handle malformed Authorization header', async () => {
      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', 'NotBearer token')

      expect(response.status).toBe(401)
      // Since 'NotBearer token' results in no valid token extraction, it should be 'Authentication required'
      // But our implementation might try to verify 'token' which would be invalid
      expect(response.body.error.message).toMatch(/Authentication required|Invalid or expired token/)
    })

    test('should handle empty Authorization header', async () => {
      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', '')

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Authentication required')
    })

    test('should handle Authorization header with only Bearer', async () => {
      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', 'Bearer ')

      expect(response.status).toBe(401)
      // Empty token after 'Bearer ' should be treated as no token
      expect(response.body.error.message).toBe('Authentication required')
    })
  })

  describe('Token payload validation', () => {
    test('should reject token with invalid role', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const customPayload = { userId: 999, role: 'superuser', extra: 'data' }
      const customToken = jwt.sign(customPayload, 'test-secret-key')

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${customToken}`)

      expect(response.status).toBe(403)
      expect(response.body.error.message).toBe('Invalid user role')
    })

    test('should handle token with missing userId', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const incompletePayload = { role: 'user' } // Missing userId
      const incompleteToken = jwt.sign(incompletePayload, 'test-secret-key')

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${incompleteToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Invalid token format')
    })

    test('should handle token with missing role', async () => {
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockResolvedValue(false)
      const incompletePayload = { userId: 123 } // Missing role
      const incompleteToken = jwt.sign(incompletePayload, 'test-secret-key')

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${incompleteToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Invalid token format')
    })
  })

  describe('Async error handling', () => {
    test('should handle async errors in blacklist check', async () => {
      // Mock the service to reject
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockRejectedValue(new Error('Cache service error'))

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`)

      // SECURITY: Should fail closed when blacklist check fails
      // The circuit breaker's fallback treats the token as blacklisted for security
      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Token has been revoked')
    })

    test('should handle promise rejection in authenticate', async () => {
      // Mock the service to reject with a different error
      vi.mocked(TokenBlacklistService.isTokenBlacklisted).mockRejectedValue(new Error('Database connection failed'))

      app.get('/test', authenticate, (req: any, res) => {
        res.json({ user: req.user })
      })

      const response = await request(app)
        .get('/test')
        .set('Authorization', `Bearer ${mockToken}`)

      // SECURITY: Should fail closed when blacklist check fails
      // The circuit breaker's fallback treats the token as blacklisted for security
      expect(response.status).toBe(401)
      expect(response.body.error.message).toBe('Token has been revoked')
    })
  })
})