import { describe, it, expect, vi, beforeEach, afterEach, MockedFunction } from 'vitest';

// Mock external dependencies
vi.mock('../../../server/db', () => {
  const mockChain = {
    select: vi.fn(),
    from: vi.fn(),
    where: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    set: vi.fn(),
    values: vi.fn(),
    returning: vi.fn(),
    onConflictDoNothing: vi.fn(),
    orderBy: vi.fn(),
    limit: vi.fn()
  };
  
  // Set up all chain methods to return mockChain for proper chaining
  mockChain.select.mockReturnValue(mockChain);
  mockChain.from.mockReturnValue(mockChain);
  mockChain.where.mockReturnValue(mockChain);
  mockChain.insert.mockReturnValue(mockChain);
  mockChain.update.mockReturnValue(mockChain);
  mockChain.set.mockReturnValue(mockChain);
  mockChain.values.mockReturnValue(mockChain);
  mockChain.returning.mockReturnValue(mockChain);
  mockChain.onConflictDoNothing.mockReturnValue(mockChain);
  mockChain.orderBy.mockReturnValue(mockChain);
  
  // Terminal operations return promises
  mockChain.limit.mockResolvedValue([]);
  
  return { db: mockChain };
});

vi.mock('../../../server/services/AuditLogger', () => ({
  auditLogger: {
    logPaymentAction: vi.fn().mockResolvedValue(undefined)
  }
}));

vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    keys: vi.fn().mockResolvedValue([])
  }
}));

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

vi.stubGlobal('console', mockConsole);

// Import after mocking
import { GSTCalculationService } from '../../../server/services/GSTCalculationService';
import { db } from '../../../server/db';
import { auditLogger } from '../../../server/services/AuditLogger';
import { cacheService } from '../../../server/services/CacheService';

describe('GSTCalculationService', () => {
  let service: GSTCalculationService;
  let mockDb: any;
  let mockAuditLogger: any;
  let mockCacheService: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset singleton instance
    (GSTCalculationService as any).instance = null;
    
    // Setup mocks
    mockDb = db as any;
    mockAuditLogger = auditLogger as any;
    mockCacheService = cacheService as any;
    
    // Restore chain methods after clearAllMocks
    mockDb.select.mockReturnValue(mockDb);
    mockDb.from.mockReturnValue(mockDb);
    mockDb.where.mockReturnValue(mockDb);
    mockDb.insert.mockReturnValue(mockDb);
    mockDb.update.mockReturnValue(mockDb);
    mockDb.set.mockReturnValue(mockDb);
    mockDb.values.mockReturnValue(mockDb);
    mockDb.returning.mockReturnValue(mockDb);
    mockDb.onConflictDoNothing.mockReturnValue(mockDb);
    mockDb.orderBy.mockReturnValue(mockDb);
    
    // Mock terminal operations - these end the chain and return promises  
    mockDb.limit.mockResolvedValue([]);
    
    // Mock methods that can be terminal operations depending on context
    mockDb.returning.mockImplementation(() => Promise.resolve([]));
    mockDb.values.mockImplementation(() => Promise.resolve([]));
    mockDb.onConflictDoNothing.mockImplementation(() => Promise.resolve([]));
    
    service = GSTCalculationService.getInstance();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = GSTCalculationService.getInstance();
      const instance2 = GSTCalculationService.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('calculateGST', () => {
    const validInput = {
      baseAmount: 10000,
      supplierState: 'KA',
      recipientState: 'TN',
      serviceType: 'accommodation'
    };

    beforeEach(() => {
      // Mock rate configuration (not found)
      mockDb.limit.mockResolvedValue([]);
    });

    it('should calculate GST for interstate transaction', async () => {
      const result = await service.calculateGST(validInput);
      
      expect(result.transactionType).toBe('interstate');
      expect(result.baseAmount).toBe(10000);
      expect(result.serviceType).toBe('accommodation');
      expect(result.hsnSacCode).toBe('996311');
      
      // Interstate transaction should have IGST only
      expect(result.igstRate).toBe(18);
      expect(result.igstAmount).toBe(1800);
      expect(result.cgstRate).toBe(0);
      expect(result.sgstRate).toBe(0);
      expect(result.cgstAmount).toBe(0);
      expect(result.sgstAmount).toBe(0);
      
      expect(result.totalGstRate).toBe(18);
      expect(result.totalGstAmount).toBe(1800);
      expect(result.totalAmount).toBe(11800);
      
      expect(result.supplierState).toBe('KA');
      expect(result.recipientState).toBe('TN');
      expect(result.placeOfSupply).toBe('TN');
    });

    it('should calculate GST for intrastate transaction', async () => {
      const intrastateInput = {
        ...validInput,
        recipientState: 'KA' // Same as supplier state
      };
      
      const result = await service.calculateGST(intrastateInput);
      
      expect(result.transactionType).toBe('intrastate');
      
      // Intrastate transaction should have CGST and SGST
      expect(result.cgstRate).toBe(9);
      expect(result.sgstRate).toBe(9);
      expect(result.cgstAmount).toBe(900);
      expect(result.sgstAmount).toBe(900);
      expect(result.igstRate).toBe(0);
      expect(result.igstAmount).toBe(0);
      
      expect(result.totalGstRate).toBe(18);
      expect(result.totalGstAmount).toBe(1800);
      expect(result.totalAmount).toBe(11800);
    });

    it('should use custom rate configuration when available', async () => {
      const customRateConfig = {
        id: 1,
        serviceType: 'accommodation',
        hsnSacCode: '996311',
        rateStructure: { standardRate: 12.0 },
        effectiveFrom: '2023-01-01',
        effectiveTo: null
      };
      
      // Mock rate configuration found
      mockDb.limit.mockResolvedValue([customRateConfig]);
      
      const result = await service.calculateGST(validInput);
      
      expect(result.totalGstRate).toBe(12);
      expect(result.igstRate).toBe(12);
      expect(result.igstAmount).toBe(1200);
      expect(result.totalGstAmount).toBe(1200);
      expect(result.totalAmount).toBe(11200);
      expect(result.rateConfigId).toBe(1);
    });

    it('should handle different service types', async () => {
      const foodBeverageInput = {
        ...validInput,
        serviceType: 'food_beverage'
      };
      
      const result = await service.calculateGST(foodBeverageInput);
      
      expect(result.serviceType).toBe('food_beverage');
      expect(result.hsnSacCode).toBe('996331');
      expect(result.totalGstRate).toBe(5);
      expect(result.igstRate).toBe(5);
      expect(result.igstAmount).toBe(500);
      expect(result.totalAmount).toBe(10500);
    });

    it('should validate input parameters', async () => {
      // Invalid base amount
      await expect(
        service.calculateGST({ ...validInput, baseAmount: 0 })
      ).rejects.toThrow('Base amount must be positive');

      await expect(
        service.calculateGST({ ...validInput, baseAmount: -1000 })
      ).rejects.toThrow('Base amount must be positive');

      // Invalid supplier state
      await expect(
        service.calculateGST({ ...validInput, supplierState: 'XX' })
      ).rejects.toThrow('Invalid supplier state');

      // Invalid recipient state
      await expect(
        service.calculateGST({ ...validInput, recipientState: 'YY' })
      ).rejects.toThrow('Invalid recipient state');

      // Invalid service type
      await expect(
        service.calculateGST({ ...validInput, serviceType: 'invalid_service' })
      ).rejects.toThrow('Invalid service type');
    });

    it('should validate GSTIN format when provided', async () => {
      await expect(
        service.calculateGST({ ...validInput, supplierGstin: 'invalid-gstin' })
      ).rejects.toThrow('Invalid supplier GSTIN');

      await expect(
        service.calculateGST({ ...validInput, recipientGstin: 'invalid-gstin' })
      ).rejects.toThrow('Invalid recipient GSTIN');
    });

    it('should accept valid GSTIN format', async () => {
      const validGstin = '29ABCDE1234F1Z5'; // Valid format
      const inputWithGstin = {
        ...validInput,
        supplierGstin: validGstin,
        recipientGstin: validGstin
      };
      
      // Should not throw error
      const result = await service.calculateGST(inputWithGstin);
      expect(result).toBeDefined();
    });

    it('should log audit events', async () => {
      await service.calculateGST(validInput);
      
      expect(mockAuditLogger.logPaymentAction).toHaveBeenCalledWith(
        'gst_calculated',
        expect.objectContaining({
          actorType: 'system',
          metadata: expect.objectContaining({
            baseAmount: 10000,
            transactionType: 'interstate',
            totalGstAmount: 1800,
            serviceType: 'accommodation'
          })
        })
      );
    });

    it('should handle custom transaction date', async () => {
      const customDate = new Date('2023-06-15');
      const inputWithDate = { ...validInput, transactionDate: customDate };
      
      const result = await service.calculateGST(inputWithDate);
      
      expect(result.applicableDate).toBe('2023-06-15');
    });

    it('should handle place of supply override', async () => {
      const inputWithPOS = { ...validInput, placeOfSupply: 'DL' };
      
      const result = await service.calculateGST(inputWithPOS);
      
      expect(result.placeOfSupply).toBe('DL');
    });

    it('should handle rounding correctly for GST amounts', async () => {
      const oddAmountInput = { ...validInput, baseAmount: 3333 }; // Will cause decimal GST
      
      const result = await service.calculateGST(oddAmountInput);
      
      // Check that all amounts are rounded to integers
      expect(Number.isInteger(result.igstAmount)).toBe(true);
      expect(Number.isInteger(result.cgstAmount)).toBe(true);
      expect(Number.isInteger(result.sgstAmount)).toBe(true);
      expect(Number.isInteger(result.totalGstAmount)).toBe(true);
      expect(Number.isInteger(result.totalAmount)).toBe(true);
    });
  });

  describe('storeGSTRecord', () => {
    const mockGSTResult = {
      baseAmount: 10000,
      transactionType: 'interstate' as const,
      serviceType: 'accommodation',
      hsnSacCode: '996311',
      supplierState: 'KA',
      recipientState: 'TN',
      placeOfSupply: 'TN',
      cgstRate: 0,
      sgstRate: 0,
      cgstAmount: 0,
      sgstAmount: 0,
      igstRate: 18,
      igstAmount: 1800,
      cessRate: 0,
      cessAmount: 0,
      totalGstRate: 18,
      totalGstAmount: 1800,
      totalAmount: 11800,
      rateConfigId: undefined,
      applicableDate: '2023-06-15'
    };

    beforeEach(() => {
      const mockRecord = {
        id: 1,
        bookingId: 123,
        ...mockGSTResult
      };
      // Setup default successful case
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([mockRecord]);
    });

    it('should store GST record successfully', async () => {
      const result = await service.storeGSTRecord(123, mockGSTResult, 'INV-2023-001');
      
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          bookingId: 123,
          baseAmount: 10000,
          transactionType: 'interstate',
          serviceType: 'accommodation',
          totalGst: 1800,
          totalAmount: 11800,
          invoiceNumber: 'INV-2023-001'
        })
      );
      
      expect(mockAuditLogger.logPaymentAction).toHaveBeenCalledWith(
        'gst_record_stored',
        expect.objectContaining({
          actorType: 'system',
          metadata: expect.objectContaining({
            bookingId: 123,
            gstRecordId: 1,
            totalGstAmount: 1800
          })
        })
      );
      
      expect(result.id).toBe(1);
    });

    it('should handle storage without invoice number', async () => {
      await service.storeGSTRecord(123, mockGSTResult);
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          invoiceNumber: undefined
        })
      );
    });

    it('should handle database errors', async () => {
      // Override the mock to reject
      mockDb.returning.mockRejectedValueOnce(new Error('Database connection failed'));
      
      await expect(
        service.storeGSTRecord(123, mockGSTResult)
      ).rejects.toThrow('Database connection failed');
    });
  });

  describe('getGSTRecord', () => {
    it('should retrieve GST record successfully', async () => {
      const mockRecord = {
        id: 1,
        bookingId: 123,
        totalAmount: 11800
      };
      
      mockDb.where.mockResolvedValue([mockRecord]);
      
      const result = await service.getGSTRecord(123);
      
      expect(result).toEqual(mockRecord);
      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.from).toHaveBeenCalled();
      expect(mockDb.where).toHaveBeenCalled();
    });

    it('should return null when record not found', async () => {
      mockDb.where.mockResolvedValue([]);
      
      const result = await service.getGSTRecord(999);
      
      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      mockDb.where.mockRejectedValue(new Error('Database error'));
      
      const result = await service.getGSTRecord(123);
      
      expect(result).toBeNull();
    });
  });

  describe('validateGSTIN', () => {
    it('should validate correct GSTIN format', () => {
      const validGSTINs = [
        '29ABCDE1234F1Z5',
        '07AABCU9603R1ZM',
        '33GSPTN2345F2ZI'
      ];
      
      validGSTINs.forEach(gstin => {
        expect(service.validateGSTIN(gstin)).toBe(true);
      });
    });

    it('should reject invalid GSTIN formats', () => {
      const invalidGSTINs = [
        '29ABCDE1234F1Z',    // Too short
        '29ABCDE1234F1Z55',  // Too long
        '29abcde1234f1z5',   // Lowercase letters
        '29ABCDE1234F0Z5',   // Invalid check digit (0)
        'XYZABCDE1234F1Z5',  // Invalid state code
        '29ABCDE1234F1@5'    // Invalid character (@)
      ];
      
      invalidGSTINs.forEach(gstin => {
        expect(service.validateGSTIN(gstin)).toBe(false);
      });
    });
  });

  describe('getStateFromGSTIN', () => {
    it('should extract correct state from valid GSTIN', () => {
      expect(service.getStateFromGSTIN('29ABCDE1234F1Z5')).toBe('KA'); // Karnataka
      expect(service.getStateFromGSTIN('33GSPTN2345F2ZI')).toBe('TN'); // Tamil Nadu
      expect(service.getStateFromGSTIN('07AABCU9603R1ZM')).toBe('DL'); // Delhi
      expect(service.getStateFromGSTIN('27GSPTN2345F2ZI')).toBe('MH'); // Maharashtra
    });

    it('should return null for invalid GSTIN', () => {
      expect(service.getStateFromGSTIN('invalid-gstin')).toBeNull();
      expect(service.getStateFromGSTIN('29ABCDE1234F0Z5')).toBeNull(); // Invalid check digit
    });

    it('should return null for unknown state codes', () => {
      // Using a valid format but unknown state code (99)
      expect(service.getStateFromGSTIN('99ABCDE1234F1Z5')).toBeNull();
    });
  });

  describe('generateInvoiceNumber', () => {
    it('should generate invoice number in correct format', () => {
      const mockDate = new Date('2023-06-15');
      vi.setSystemTime(mockDate);
      
      const invoiceNumber = service.generateInvoiceNumber(123, 456);
      
      expect(invoiceNumber).toBe('INV-202306-123-456');
      
      vi.useRealTimers();
    });

    it('should pad month with leading zero', () => {
      const mockDate = new Date('2023-03-15'); // March
      vi.setSystemTime(mockDate);
      
      const invoiceNumber = service.generateInvoiceNumber(123, 456);
      
      expect(invoiceNumber).toBe('INV-202303-123-456');
      
      vi.useRealTimers();
    });
  });

  describe('createRateConfiguration', () => {
    beforeEach(() => {
      const mockConfig = {
        id: 1,
        serviceType: 'accommodation',
        hsnSacCode: '996311',
        rateStructure: { standardRate: 15.0 }
      };
      // Setup default successful case
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([mockConfig]);
      mockCacheService.keys.mockResolvedValue(['gst_rate:accommodation_2023-01-01']);
    });

    it('should create rate configuration successfully', async () => {
      const effectiveFrom = new Date('2023-01-01');
      const effectiveTo = new Date('2023-12-31');
      const rateStructure = { standardRate: 15.0, description: 'Reduced rate' };
      
      const result = await service.createRateConfiguration(
        'accommodation',
        '996311',
        rateStructure,
        effectiveFrom,
        effectiveTo,
        1
      );
      
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          serviceType: 'accommodation',
          hsnSacCode: '996311',
          rateStructure,
          effectiveFrom: '2023-01-01',
          effectiveTo: '2023-12-31',
          createdBy: 1
        })
      );
      
      expect(mockCacheService.keys).toHaveBeenCalledWith('gst_rate:accommodation_*');
      expect(mockCacheService.delete).toHaveBeenCalled();
      
      expect(mockAuditLogger.logPaymentAction).toHaveBeenCalledWith(
        'gst_rate_config_created',
        expect.objectContaining({
          actorType: 'admin',
          actorId: 1,
          metadata: expect.objectContaining({
            serviceType: 'accommodation',
            hsnSacCode: '996311'
          })
        })
      );
      
      expect(result.id).toBe(1);
    });

    it('should create configuration without end date', async () => {
      const effectiveFrom = new Date('2023-01-01');
      const rateStructure = { standardRate: 15.0 };
      
      await service.createRateConfiguration(
        'accommodation',
        '996311',
        rateStructure,
        effectiveFrom
      );
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          effectiveTo: undefined
        })
      );
    });

    it('should handle configuration creation errors', async () => {
      // Override the mock to reject
      mockDb.returning.mockRejectedValueOnce(new Error('Constraint violation'));
      
      await expect(
        service.createRateConfiguration(
          'accommodation',
          '996311',
          { standardRate: 15.0 },
          new Date('2023-01-01')
        )
      ).rejects.toThrow('Constraint violation');
    });
  });

  describe('Rate Configuration Caching', () => {
    it('should use cached rate configuration when available', async () => {
      const cachedConfig = {
        id: 1,
        serviceType: 'accommodation',
        rateStructure: { standardRate: 15.0 }
      };
      
      // Clear any previous calls and setup cache to return config
      vi.clearAllMocks();
      mockCacheService.get.mockResolvedValueOnce(cachedConfig);
      
      // Reset DB chain methods to track calls properly
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);
      
      const input = {
        baseAmount: 10000,
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      };
      
      const result = await service.calculateGST(input);
      
      expect(mockCacheService.get).toHaveBeenCalled();
      expect(mockDb.select).not.toHaveBeenCalled(); // Should not query DB
      expect(result.totalGstRate).toBe(15);
      expect(result.rateConfigId).toBe(1);
    });

    it('should cache rate configuration after database query', async () => {
      const dbConfig = {
        id: 2,
        serviceType: 'accommodation',
        rateStructure: { standardRate: 12.0 }
      };
      
      mockCacheService.get.mockResolvedValue(null);
      mockDb.limit.mockResolvedValue([dbConfig]);
      
      const input = {
        baseAmount: 10000,
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      };
      
      await service.calculateGST(input);
      
      expect(mockCacheService.get).toHaveBeenCalled();
      expect(mockDb.select).toHaveBeenCalled();
      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('gst_rate:accommodation_'),
        dbConfig,
        3600 // 1 hour cache timeout
      );
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing rate configuration gracefully', async () => {
      mockCacheService.get.mockResolvedValue(null);
      mockDb.limit.mockResolvedValue([]); // No rate config found
      
      const input = {
        baseAmount: 10000,
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      };
      
      const result = await service.calculateGST(input);
      
      // Should use default rate (18% for accommodation)
      expect(result.totalGstRate).toBe(18);
      expect(result.rateConfigId).toBeUndefined();
    });

    it('should handle cache service errors gracefully', async () => {
      mockCacheService.get.mockRejectedValueOnce(new Error('Cache unavailable'));
      mockDb.limit.mockResolvedValue([]);
      
      const input = {
        baseAmount: 10000,
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      };
      
      // Should not throw error and fallback to database query
      const result = await service.calculateGST(input);
      expect(result).toBeDefined();
      expect(mockConsole.error).toHaveBeenCalledWith('Cache service error:', expect.any(Error));
    });

    it('should handle all Indian states correctly', async () => {
      const allStateCodes = [
        'AN', 'AP', 'AR', 'AS', 'BR', 'CG', 'CH', 'DN', 'DD', 'DL', 
        'GA', 'GJ', 'HR', 'HP', 'JK', 'JH', 'KA', 'KL', 'LA', 'LD',
        'MP', 'MH', 'MN', 'ML', 'MZ', 'NL', 'OD', 'PY', 'PB', 'RJ',
        'SK', 'TN', 'TS', 'TR', 'UP', 'UK', 'WB'
      ];
      
      const baseInput = {
        baseAmount: 10000,
        supplierState: 'KA',
        serviceType: 'accommodation'
      };
      
      // Test each state as recipient
      for (const stateCode of allStateCodes) {
        const input = { ...baseInput, recipientState: stateCode };
        const result = await service.calculateGST(input);
        expect(result).toBeDefined();
        expect(result.recipientState).toBe(stateCode);
      }
    });

    it('should handle all service types correctly', async () => {
      const serviceTypes = ['accommodation', 'food_beverage', 'transport', 'entertainment', 'conference'];
      const expectedRates = [18, 5, 18, 18, 18];
      
      const baseInput = {
        baseAmount: 10000,
        supplierState: 'KA',
        recipientState: 'TN'
      };
      
      for (let i = 0; i < serviceTypes.length; i++) {
        const input = { ...baseInput, serviceType: serviceTypes[i] };
        const result = await service.calculateGST(input);
        
        expect(result.serviceType).toBe(serviceTypes[i]);
        expect(result.totalGstRate).toBe(expectedRates[i]);
      }
    });

    it('should handle very large amounts correctly', async () => {
      const largeAmount = 999999999; // ~10 crore paise = 10 lakh rupees
      
      const input = {
        baseAmount: largeAmount,
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      };
      
      const result = await service.calculateGST(input);
      
      expect(result.baseAmount).toBe(largeAmount);
      expect(result.igstAmount).toBe(Math.round(largeAmount * 0.18));
      expect(result.totalAmount).toBe(largeAmount + result.totalGstAmount);
    });

    it('should handle very small amounts correctly', async () => {
      const smallAmount = 1; // 1 paisa
      
      const input = {
        baseAmount: smallAmount,
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      };
      
      const result = await service.calculateGST(input);
      
      expect(result.baseAmount).toBe(1);
      expect(result.igstAmount).toBe(0); // Will round to 0
      expect(result.totalAmount).toBe(1);
    });
  });
});