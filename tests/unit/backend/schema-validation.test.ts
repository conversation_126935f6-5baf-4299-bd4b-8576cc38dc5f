import { describe, test, expect } from 'vitest'
import { ZodError } from 'zod'
import {
  userRegisterSchema,
  userLoginSchema,
  propertyFormSchema,
  bookingFormSchema,
  reviewFormSchema,
  reviewResponseSchema
} from '../../../shared/schema'

describe('Schema Validation', () => {
  describe('userRegisterSchema', () => {
    const validUserData = {
      username: 'testuser',
      password: 'ValidPass123!',
      email: '<EMAIL>',
      fullName: 'Test User',
      phone: '+1234567890',
      address: '123 Test Street',
      bio: 'Test bio',
      role: 'user' as const,
      consentData: {
        termsAccepted: true,
        dataProcessingConsent: true,
        marketingConsent: false
      }
    }

    test('should validate correct user registration data', () => {
      const result = userRegisterSchema.parse(validUserData)
      expect(result).toEqual(validUserData)
    })

    test('should validate without optional fields', () => {
      const minimalData = {
        username: 'testuser',
        password: 'ValidPass123!',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'user' as const
      }

      const result = userRegisterSchema.parse(minimalData)
      expect(result.username).toBe('testuser')
      expect(result.email).toBe('<EMAIL>')
    })

    describe('password validation', () => {
      test('should reject password shorter than 8 characters', () => {
        const invalidData = { ...validUserData, password: 'Short1!' }
        
        expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          userRegisterSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Password must be at least 8 characters')
          }
        }
      })

      test('should reject password without uppercase letter', () => {
        const invalidData = { ...validUserData, password: 'lowercase123!' }
        
        expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          userRegisterSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Password must contain at least one uppercase letter')
          }
        }
      })

      test('should reject password without lowercase letter', () => {
        const invalidData = { ...validUserData, password: 'UPPERCASE123!' }
        
        expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          userRegisterSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Password must contain at least one lowercase letter')
          }
        }
      })

      test('should reject password without number', () => {
        const invalidData = { ...validUserData, password: 'NoNumber!' }
        
        expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          userRegisterSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Password must contain at least one number')
          }
        }
      })

      test('should reject password without special character', () => {
        const invalidData = { ...validUserData, password: 'NoSpecial123' }
        
        expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          userRegisterSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Password must contain at least one special character')
          }
        }
      })
    })

    describe('email validation', () => {
      test('should reject invalid email formats', () => {
        const invalidEmails = [
          'invalid-email',
          'test@',
          '@example.com',
          'test.example.com',
          'test@.com'
        ]

        invalidEmails.forEach(email => {
          const invalidData = { ...validUserData, email }
          expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
        })
      })

      test('should accept valid email formats', () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ]

        validEmails.forEach(email => {
          const validData = { ...validUserData, email }
          expect(() => userRegisterSchema.parse(validData)).not.toThrow()
        })
      })
    })

    test('should require username', () => {
      const invalidData = { ...validUserData }
      delete (invalidData as any).username
      
      expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
    })

    test('should require fullName', () => {
      const invalidData = { ...validUserData }
      delete (invalidData as any).fullName
      
      expect(() => userRegisterSchema.parse(invalidData)).toThrow(ZodError)
    })
  })

  describe('userLoginSchema', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'anypassword'
    }

    test('should validate correct login data', () => {
      const result = userLoginSchema.parse(validLoginData)
      expect(result).toEqual(validLoginData)
    })

    test('should require email', () => {
      const invalidData = { password: 'anypassword' }
      expect(() => userLoginSchema.parse(invalidData)).toThrow(ZodError)
    })

    test('should require password', () => {
      const invalidData = { email: '<EMAIL>' }
      expect(() => userLoginSchema.parse(invalidData)).toThrow(ZodError)
    })

    test('should reject empty password', () => {
      const invalidData = { email: '<EMAIL>', password: '' }
      
      expect(() => userLoginSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        userLoginSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('Password is required')
        }
      }
    })

    test('should validate email format', () => {
      const invalidData = { email: 'invalid-email', password: 'anypassword' }
      
      expect(() => userLoginSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        userLoginSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('Invalid email address')
        }
      }
    })
  })

  describe('propertyFormSchema', () => {
    const validPropertyData = {
      ownerId: 1,
      title: 'Beautiful Farmhouse',
      description: 'A stunning farmhouse with amazing views',
      location: 'California, USA',
      halfDayPrice: 150,
      fullDayPrice: 250,
      bedrooms: 3,
      bathrooms: 2,
      amenities: ['WiFi', 'Pool', 'Kitchen'],
      images: ['/uploads/farmhouse1.jpg', '/uploads/farmhouse2.jpg'],
      status: 'active',
      featured: false
    }

    test('should validate correct property data', () => {
      const result = propertyFormSchema.parse(validPropertyData)
      expect(result).toEqual({
        ...validPropertyData,
        videos: [] // Default empty array is added by schema
      })
    })

    test('should require at least one amenity', () => {
      const invalidData = { ...validPropertyData, amenities: [] }
      
      expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        propertyFormSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('At least one amenity is required')
        }
      }
    })

    test('should require at least one image', () => {
      const invalidData = { ...validPropertyData, images: [] }
      
      expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        propertyFormSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('At least one image is required')
        }
      }
    })

    describe('price validation', () => {
      test('should reject negative halfDayPrice', () => {
        const invalidData = { ...validPropertyData, halfDayPrice: -50 }
        
        expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          propertyFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Price must be a positive number')
          }
        }
      })

      test('should reject zero fullDayPrice', () => {
        const invalidData = { ...validPropertyData, fullDayPrice: 0 }
        
        expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          propertyFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Price must be a positive number')
          }
        }
      })
    })

    describe('room validation', () => {
      test('should reject non-integer bedrooms', () => {
        const invalidData = { ...validPropertyData, bedrooms: 2.5 }
        
        expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          propertyFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toContain('integer')
          }
        }
      })

      test('should reject zero bedrooms', () => {
        const invalidData = { ...validPropertyData, bedrooms: 0 }
        
        expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          propertyFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Number of bedrooms must be a positive integer')
          }
        }
      })

      test('should reject negative bathrooms', () => {
        const invalidData = { ...validPropertyData, bathrooms: -1 }
        
        expect(() => propertyFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          propertyFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Number of bathrooms must be a positive integer')
          }
        }
      })
    })
  })

  describe('bookingFormSchema', () => {
    const validBookingData = {
      propertyId: 1,
      userId: 1,
      bookingDate: '2024-12-01',
      bookingType: 'full_day' as const,
      guests: 4,
      totalPrice: 265,
      status: 'confirmed',
      paymentStatus: 'pending',
      paymentMethod: 'advance' as const
    }

    test('should validate correct booking data', () => {
      const result = bookingFormSchema.parse(validBookingData)
      expect(result).toEqual(validBookingData)
    })

    test('should require positive number of guests', () => {
      const invalidData = { ...validBookingData, guests: 0 }
      
      expect(() => bookingFormSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        bookingFormSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('Number of guests must be a positive integer')
        }
      }
    })

    test('should reject negative guests', () => {
      const invalidData = { ...validBookingData, guests: -2 }
      
      expect(() => bookingFormSchema.parse(invalidData)).toThrow(ZodError)
    })

    test('should reject non-integer guests', () => {
      const invalidData = { ...validBookingData, guests: 3.5 }
      
      expect(() => bookingFormSchema.parse(invalidData)).toThrow(ZodError)
    })
  })

  describe('reviewFormSchema', () => {
    const validReviewData = {
      propertyId: 1,
      userId: 1,
      bookingId: 1,
      rating: '5' as const,
      comment: 'Amazing farmhouse! Perfect for a weekend getaway.'
    }

    test('should validate correct review data', () => {
      const result = reviewFormSchema.parse(validReviewData)
      expect(result).toEqual(validReviewData)
    })

    describe('rating validation', () => {
      test('should accept valid ratings', () => {
        const validRatings = ['1', '2', '3', '4', '5']
        
        validRatings.forEach(rating => {
          const validData = { ...validReviewData, rating: rating as any }
          expect(() => reviewFormSchema.parse(validData)).not.toThrow()
        })
      })

      test('should reject invalid ratings', () => {
        const invalidRatings = ['0', '6', '10', 'excellent', 5] // Note: 5 as number should fail
        
        invalidRatings.forEach(rating => {
          const invalidData = { ...validReviewData, rating: rating as any }
          expect(() => reviewFormSchema.parse(invalidData)).toThrow(ZodError)
        })
      })
    })

    describe('comment validation', () => {
      test('should reject comments shorter than 5 characters', () => {
        const invalidData = { ...validReviewData, comment: 'Bad' }
        
        expect(() => reviewFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          reviewFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Review comment must be at least 5 characters')
          }
        }
      })

      test('should reject comments longer than 500 characters', () => {
        const longComment = 'a'.repeat(501)
        const invalidData = { ...validReviewData, comment: longComment }
        
        expect(() => reviewFormSchema.parse(invalidData)).toThrow(ZodError)
        
        try {
          reviewFormSchema.parse(invalidData)
        } catch (error) {
          if (error instanceof ZodError) {
            expect(error.errors[0].message).toBe('Review comment cannot exceed 500 characters')
          }
        }
      })

      test('should accept comments of exactly 5 characters', () => {
        const validData = { ...validReviewData, comment: 'Great' }
        expect(() => reviewFormSchema.parse(validData)).not.toThrow()
      })

      test('should accept comments of exactly 500 characters', () => {
        const maxComment = 'a'.repeat(500)
        const validData = { ...validReviewData, comment: maxComment }
        expect(() => reviewFormSchema.parse(validData)).not.toThrow()
      })
    })
  })

  describe('reviewResponseSchema', () => {
    const validResponseData = {
      response: 'Thank you for your wonderful review! We appreciate your feedback.'
    }

    test('should validate correct response data', () => {
      const result = reviewResponseSchema.parse(validResponseData)
      expect(result).toEqual(validResponseData)
    })

    test('should reject responses shorter than 5 characters', () => {
      const invalidData = { response: 'Thx' }
      
      expect(() => reviewResponseSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        reviewResponseSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('Response must be at least 5 characters')
        }
      }
    })

    test('should reject responses longer than 500 characters', () => {
      const longResponse = 'a'.repeat(501)
      const invalidData = { response: longResponse }
      
      expect(() => reviewResponseSchema.parse(invalidData)).toThrow(ZodError)
      
      try {
        reviewResponseSchema.parse(invalidData)
      } catch (error) {
        if (error instanceof ZodError) {
          expect(error.errors[0].message).toBe('Response cannot exceed 500 characters')
        }
      }
    })

    test('should accept responses of exactly 5 characters', () => {
      const validData = { response: 'Thank' }
      expect(() => reviewResponseSchema.parse(validData)).not.toThrow()
    })

    test('should accept responses of exactly 500 characters', () => {
      const maxResponse = 'a'.repeat(500)
      const validData = { response: maxResponse }
      expect(() => reviewResponseSchema.parse(validData)).not.toThrow()
    })

    test('should require response field', () => {
      const invalidData = {}
      expect(() => reviewResponseSchema.parse(invalidData)).toThrow(ZodError)
    })
  })
})