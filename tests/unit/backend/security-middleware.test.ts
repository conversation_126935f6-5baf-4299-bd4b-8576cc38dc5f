import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest'
import { Request, Response, NextFunction } from 'express'
import { 
  contentSecurityPolicy, 
  securityHeaders, 
  cspReportHandler, 
  enhancedSecurity,
  getAllowedOrigins 
} from '../../../server/middlewares/security'

// Mock the config module
vi.mock('../../../server/config', () => ({
  config: {
    isDevelopment: vi.fn().mockReturnValue(true),
    app: {
      nodeEnv: 'test'
    }
  }
}))

describe('Security Middleware', () => {
  let req: Partial<Request>
  let res: Partial<Response>
  let next: NextFunction

  beforeEach(() => {
    req = {
      path: '/api/test',
      ip: '127.0.0.1',
      get: vi.fn(),
      body: {},
      headers: {}
    }
    res = {
      setHeader: vi.fn(),
      status: vi.fn().mockReturnThis(),
      end: vi.fn(),
      json: vi.fn()
    }
    next = vi.fn()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('contentSecurityPolicy', () => {
    test('should be a function that returns middleware', () => {
      expect(typeof contentSecurityPolicy).toBe('function')
    })

    test('should call next() when applied', () => {
      const middleware = contentSecurityPolicy
      
      // CSP middleware is created by helmet, so we'll test it indirectly
      expect(middleware).toBeDefined()
      expect(typeof middleware).toBe('function')
    })
  })

  describe('securityHeaders', () => {
    test('should be a function that returns middleware', () => {
      expect(typeof securityHeaders).toBe('function')
    })

    test('should call next() when applied', () => {
      const middleware = securityHeaders
      
      // Security headers middleware is created by helmet
      expect(middleware).toBeDefined()
      expect(typeof middleware).toBe('function')
    })
  })

  describe('cspReportHandler', () => {
    test('should handle CSP violation reports', () => {
      req.path = '/api/security/csp-report'
      req.body = {
        'csp-report': {
          'document-uri': 'https://example.com/page',
          'violated-directive': 'script-src',
          'blocked-uri': 'https://evil.com/script.js'
        }
      }
      req.get = vi.fn().mockReturnValue('Mozilla/5.0...')

      cspReportHandler(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(204)
      expect(res.end).toHaveBeenCalled()
      expect(next).not.toHaveBeenCalled()
    })

    test('should call next() for non-CSP report paths', () => {
      req.path = '/api/other-endpoint'

      cspReportHandler(req as Request, res as Response, next)

      expect(next).toHaveBeenCalled()
      expect(res.status).not.toHaveBeenCalled()
      expect(res.end).not.toHaveBeenCalled()
    })

    test('should handle missing User-Agent gracefully', () => {
      req.path = '/api/security/csp-report'
      req.body = { 'csp-report': { 'violated-directive': 'script-src' } }
      req.get = vi.fn().mockReturnValue(undefined)

      cspReportHandler(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(204)
      expect(res.end).toHaveBeenCalled()
    })

    test('should handle empty CSP report body', () => {
      req.path = '/api/security/csp-report'
      req.body = {}

      cspReportHandler(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(204)
      expect(res.end).toHaveBeenCalled()
    })
  })

  describe('enhancedSecurity', () => {
    test('should set enhanced security headers', () => {
      enhancedSecurity(req as Request, res as Response, next)

      expect(res.setHeader).toHaveBeenCalledWith(
        'Cache-Control', 
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      )
      expect(res.setHeader).toHaveBeenCalledWith('Pragma', 'no-cache')
      expect(res.setHeader).toHaveBeenCalledWith('Expires', '0')
      expect(res.setHeader).toHaveBeenCalledWith('Surrogate-Control', 'no-store')
      expect(res.setHeader).toHaveBeenCalledWith('X-Security-Enhanced', 'true')
      expect(next).toHaveBeenCalled()
    })

    test('should use request ID from headers if available', () => {
      req.headers = { 'x-request-id': 'test-request-123' }

      enhancedSecurity(req as Request, res as Response, next)

      expect(res.setHeader).toHaveBeenCalledWith('X-Request-ID', 'test-request-123')
      expect(next).toHaveBeenCalled()
    })

    test('should use unknown request ID if not available', () => {
      req.headers = {}

      enhancedSecurity(req as Request, res as Response, next)

      expect(res.setHeader).toHaveBeenCalledWith('X-Request-ID', 'unknown')
      expect(next).toHaveBeenCalled()
    })

    test('should set all required security headers', () => {
      enhancedSecurity(req as Request, res as Response, next)

      const expectedHeaders = [
        'Cache-Control',
        'Pragma', 
        'Expires',
        'Surrogate-Control',
        'X-Security-Enhanced',
        'X-Request-ID'
      ]

      expectedHeaders.forEach(header => {
        expect(res.setHeader).toHaveBeenCalledWith(header, expect.any(String))
      })

      expect(next).toHaveBeenCalledTimes(1)
    })
  })

  describe('getAllowedOrigins', () => {
    beforeEach(() => {
      // Reset mocks
      vi.clearAllMocks()
    })

    test('should return array of allowed origins', () => {
      const origins = getAllowedOrigins()
      
      expect(Array.isArray(origins)).toBe(true)
      expect(origins.length).toBeGreaterThan(0)
      // Should contain some production domains by default
      expect(origins.some(origin => origin.includes('farmhouse-rental.com'))).toBe(true)
    })

    test('should return production origins when in production mode', async () => {
      // Mock config for this test
      vi.doMock('../../../server/config', () => ({
        config: {
          isDevelopment: () => false
        }
      }))
      
      const { getAllowedOrigins } = await import('../../../server/middlewares/security')
      const origins = getAllowedOrigins()

      expect(origins).toContain('https://farmhouse-rental.com')
      expect(origins).toContain('https://www.farmhouse-rental.com')
      expect(origins).not.toContain('http://localhost:3000')
      
      vi.doUnmock('../../../server/config')
    })

    test('should handle getAllowedOrigins function call', () => {
      const origins = getAllowedOrigins()
      
      expect(Array.isArray(origins)).toBe(true)
      expect(origins.length).toBeGreaterThan(0)
      // All origins should be valid URLs
      origins.forEach(origin => {
        expect(origin).toMatch(/^https?:\/\/[\w.-]+(:\d+)?$/)
      })
    })

    test('should only include HTTPS in production', async () => {
      // Mock config for this test
      vi.doMock('../../../server/config', () => ({
        config: {
          isDevelopment: () => false
        }
      }))
      
      const { getAllowedOrigins } = await import('../../../server/middlewares/security')
      const origins = getAllowedOrigins()

      const httpOrigins = origins.filter(origin => origin.startsWith('http://'))
      expect(httpOrigins).toHaveLength(0)

      const httpsOrigins = origins.filter(origin => origin.startsWith('https://'))
      expect(httpsOrigins.length).toBeGreaterThan(0)
      
      vi.doUnmock('../../../server/config')
    })

    test('should handle empty trusted domains list', async () => {
      // Mock config for this test
      vi.doMock('../../../server/config', () => ({
        config: {
          isDevelopment: () => false
        }
      }))
      
      const { getAllowedOrigins } = await import('../../../server/middlewares/security')
      
      // This tests the function's robustness
      const origins = getAllowedOrigins()
      expect(Array.isArray(origins)).toBe(true)
      
      vi.doUnmock('../../../server/config')
    })
  })

  describe('Security middleware integration', () => {
    test('should work together in a middleware chain', () => {
      const middlewares = [
        cspReportHandler,
        enhancedSecurity
      ]

      // Simulate middleware chain for non-CSP endpoint
      req.path = '/api/sensitive-data'

      middlewares.forEach(middleware => {
        middleware(req as Request, res as Response, next)
      })

      // cspReportHandler should call next
      expect(next).toHaveBeenCalledTimes(2)
      
      // enhancedSecurity should set headers
      expect(res.setHeader).toHaveBeenCalledWith('X-Security-Enhanced', 'true')
    })

    test('should handle CSP reports without interfering with other middleware', () => {
      req.path = '/api/security/csp-report'
      req.body = { 'csp-report': { 'violated-directive': 'script-src' } }

      // First middleware: CSP report handler
      cspReportHandler(req as Request, res as Response, next)

      // Should end the chain for CSP reports
      expect(res.status).toHaveBeenCalledWith(204)
      expect(res.end).toHaveBeenCalled()
      expect(next).not.toHaveBeenCalled()
    })
  })

  describe('Error handling', () => {
    test('should handle missing request properties gracefully', () => {
      req = { headers: {} } // Empty request object with headers

      expect(() => {
        cspReportHandler(req as Request, res as Response, next)
      }).not.toThrow()

      expect(() => {
        enhancedSecurity(req as Request, res as Response, next)
      }).not.toThrow()
    })

    test('should handle missing response methods gracefully', () => {
      res = {} // Empty response object

      expect(() => {
        // This might throw, but shouldn't crash the application
        enhancedSecurity(req as Request, res as Response, next)
      }).toThrow() // Expected since setHeader is missing
    })

    test('should handle CSP report parsing errors', () => {
      req.path = '/api/security/csp-report'
      req.body = 'invalid json string'

      expect(() => {
        cspReportHandler(req as Request, res as Response, next)
      }).not.toThrow()

      expect(res.status).toHaveBeenCalledWith(204)
      expect(res.end).toHaveBeenCalled()
    })
  })

  describe('Security header values', () => {
    test('should set correct cache control values for enhanced security', () => {
      enhancedSecurity(req as Request, res as Response, next)

      expect(res.setHeader).toHaveBeenCalledWith(
        'Cache-Control',
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      )
      expect(res.setHeader).toHaveBeenCalledWith('Pragma', 'no-cache')
      expect(res.setHeader).toHaveBeenCalledWith('Expires', '0')
    })

    test('should set security-specific headers', () => {
      enhancedSecurity(req as Request, res as Response, next)

      expect(res.setHeader).toHaveBeenCalledWith('X-Security-Enhanced', 'true')
      expect(res.setHeader).toHaveBeenCalledWith('Surrogate-Control', 'no-store')
    })

    test('should preserve existing X-Request-ID if present', () => {
      req.headers = { 'x-request-id': 'existing-id-123' }

      enhancedSecurity(req as Request, res as Response, next)

      expect(res.setHeader).toHaveBeenCalledWith('X-Request-ID', 'existing-id-123')
    })
  })
})