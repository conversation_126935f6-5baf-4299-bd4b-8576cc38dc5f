import { describe, it, expect, vi, beforeEach, afterEach, MockedFunction } from 'vitest';

// Mock external dependencies
vi.mock('../../../server/db', () => {
  const mockChain = {
    insert: vi.fn(),
    values: vi.fn(),
    select: vi.fn(),
    from: vi.fn(),
    where: vi.fn(),
    orderBy: vi.fn(),
    limit: vi.fn()
  };
  
  // Set up all chain methods to return mockChain for proper chaining
  mockChain.insert.mockReturnValue(mockChain);
  mockChain.select.mockReturnValue(mockChain);
  mockChain.from.mockReturnValue(mockChain);
  mockChain.where.mockReturnValue(mockChain);
  mockChain.orderBy.mockReturnValue(mockChain);
  
  // Terminal operations return promises
  mockChain.values.mockResolvedValue(undefined);
  mockChain.limit.mockResolvedValue([]);
  
  return { db: mockChain };
});

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

vi.stubGlobal('console', mockConsole);

// Import after mocking
import { AuditLogger } from '../../../server/services/AuditLogger';
import { db } from '../../../server/db';

describe('AuditLogger', () => {
  let auditLogger: AuditLogger;
  let mockDb: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset singleton instance
    (AuditLogger as any).instance = null;
    
    mockDb = db as any;
    
    // Restore chain methods after clearAllMocks
    mockDb.insert.mockReturnValue(mockDb);
    mockDb.select.mockReturnValue(mockDb);
    mockDb.from.mockReturnValue(mockDb);
    mockDb.where.mockReturnValue(mockDb);
    mockDb.orderBy.mockReturnValue(mockDb);
    
    // Mock terminal operations - these end the chain and return promises
    mockDb.values.mockResolvedValue(undefined);
    mockDb.limit.mockResolvedValue([]);
    
    auditLogger = AuditLogger.getInstance();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = AuditLogger.getInstance();
      const instance2 = AuditLogger.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBe(auditLogger);
    });
  });

  describe('logPaymentAction', () => {
    const mockContext = {
      actorType: 'user' as const,
      actorId: 123,
      actorIp: '***********',
      actorUserAgent: 'Mozilla/5.0',
      metadata: { key: 'value' }
    };

    it('should log payment action successfully', async () => {
      await auditLogger.logPaymentAction('payment_created', mockContext, 456, 789);
      
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith({
        paymentOrderId: 456,
        paymentTransactionId: 789,
        action: 'payment_created',
        actorType: 'user',
        actorId: 123,
        actorIp: '***********',
        actorUserAgent: 'Mozilla/5.0',
        beforeState: undefined,
        afterState: undefined,
        metadata: { key: 'value' },
        securityContext: undefined
      });
    });

    it('should log payment action with before and after states', async () => {
      const contextWithStates = {
        ...mockContext,
        beforeState: { status: 'pending' },
        afterState: { status: 'completed' },
        securityContext: { requestId: 'req_123' }
      };

      await auditLogger.logPaymentAction('payment_updated', contextWithStates);
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'payment_updated',
          beforeState: { status: 'pending' },
          afterState: { status: 'completed' },
          securityContext: { requestId: 'req_123' }
        })
      );
    });

    it('should log high-priority actions to console', async () => {
      await auditLogger.logPaymentAction('payment_order_created', mockContext, 456);
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        '[AUDIT] High-priority action: payment_order_created',
        expect.objectContaining({
          actorType: 'user',
          actorId: 123,
          paymentOrderId: 456,
          timestamp: expect.any(String)
        })
      );
    });

    it('should not log normal-priority actions to console', async () => {
      await auditLogger.logPaymentAction('payment_viewed', mockContext, 456);
      
      expect(mockConsole.log).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      mockDb.values.mockRejectedValue(new Error('Database connection failed'));
      
      // Should not throw error
      await expect(
        auditLogger.logPaymentAction('payment_created', mockContext)
      ).resolves.toBeUndefined();
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        'Failed to log audit entry:',
        expect.any(Error)
      );
    });

    it('should handle minimal context', async () => {
      const minimalContext = {
        actorType: 'system' as const
      };

      await auditLogger.logPaymentAction('system_action', minimalContext);
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'system_action',
          actorType: 'system',
          actorId: undefined,
          actorIp: undefined,
          actorUserAgent: undefined
        })
      );
    });
  });

  describe('logSecurityIncident', () => {
    const mockIncidentData = {
      incidentType: 'suspicious_login',
      severity: 'medium' as const,
      description: 'Multiple failed login attempts',
      sourceIp: '*************',
      userId: 456,
      paymentOrderId: 789,
      incidentData: { attempts: 5 }
    };

    it('should log security incident successfully', async () => {
      await auditLogger.logSecurityIncident(mockIncidentData);
      
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith({
        incidentType: 'suspicious_login',
        severity: 'medium',
        description: 'Multiple failed login attempts',
        sourceIp: '*************',
        userId: 456,
        paymentOrderId: 789,
        incidentData: { attempts: 5 }
      });
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        '[SECURITY] MEDIUM incident:',
        expect.objectContaining({
          type: 'suspicious_login',
          description: 'Multiple failed login attempts',
          sourceIp: '*************',
          timestamp: expect.any(String)
        })
      );
    });

    it('should handle critical incidents with alerting', async () => {
      const criticalIncident = {
        ...mockIncidentData,
        severity: 'critical' as const,
        incidentType: 'payment_fraud'
      };

      await auditLogger.logSecurityIncident(criticalIncident);
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        '[SECURITY] CRITICAL incident:',
        expect.objectContaining({
          type: 'payment_fraud'
        })
      );
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        '🚨 CRITICAL SECURITY INCIDENT 🚨',
        expect.objectContaining({
          type: 'payment_fraud',
          description: 'Multiple failed login attempts',
          sourceIp: '*************'
        })
      );
    });

    it('should handle database errors with critical logging', async () => {
      mockDb.values.mockRejectedValue(new Error('Database unavailable'));
      
      await auditLogger.logSecurityIncident(mockIncidentData);
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        'Failed to log security incident:',
        expect.any(Error)
      );
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[SECURITY] CRITICAL: Unable to log security incident to database:',
        mockIncidentData
      );
    });

    it('should handle minimal incident data', async () => {
      const minimalIncident = {
        incidentType: 'test_incident',
        severity: 'low' as const,
        description: 'Test description'
      };

      await auditLogger.logSecurityIncident(minimalIncident);
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          incidentType: 'test_incident',
          severity: 'low',
          description: 'Test description',
          sourceIp: undefined,
          userId: undefined,
          paymentOrderId: undefined,
          incidentData: undefined
        })
      );
    });
  });

  describe('extractAuditContext', () => {
    const mockRequest = {
      user: { userId: 123 },
      headers: { 'user-agent': 'TestAgent/1.0', 'x-forwarded-for': '********,***********' },
      get: vi.fn((header) => {
        if (header === 'User-Agent') return 'TestAgent/1.0';
        if (header === 'X-Forwarded-For') return '********,***********';
        return undefined;
      }),
      ip: '127.0.0.1',
      socket: { remoteAddress: '***********' },
      requestId: 'req_456',
      sessionId: 'session_789',
      path: '/api/payments',
      method: 'POST'
    } as any;

    it('should extract full audit context from request', () => {
      // Create a fresh mock for this specific test
      const testMockRequest = {
        user: { userId: 123 },
        headers: { 'user-agent': 'TestAgent/1.0', 'x-forwarded-for': '********,***********' },
        get: vi.fn().mockImplementation((header) => {
          if (header === 'User-Agent') return 'TestAgent/1.0';
          if (header === 'X-Forwarded-For') return '********,***********';
          return undefined;
        }),
        ip: '127.0.0.1',
        socket: { remoteAddress: '***********' },
        requestId: 'req_456',
        sessionId: 'session_789',
        path: '/api/payments',
        method: 'POST'
      } as any;

      const context = auditLogger.extractAuditContext(testMockRequest, 'user');
      
      expect(context).toEqual({
        actorType: 'user',
        actorId: 123,
        actorIp: '********', // First IP from X-Forwarded-For
        actorUserAgent: 'TestAgent/1.0',
        securityContext: {
          requestId: 'req_456',
          sessionId: 'session_789',
          timestamp: expect.any(String),
          requestPath: '/api/payments',
          requestMethod: 'POST'
        }
      });
    });

    it('should handle missing user in request', () => {
      const requestWithoutUser = { ...mockRequest, user: undefined };
      
      const context = auditLogger.extractAuditContext(requestWithoutUser, 'system');
      
      expect(context.actorType).toBe('system');
      expect(context.actorId).toBeUndefined();
    });

    it('should handle missing headers gracefully', () => {
      const requestWithoutHeaders = {
        ...mockRequest,
        headers: {},
        get: vi.fn(() => undefined)
      };
      
      const context = auditLogger.extractAuditContext(requestWithoutHeaders);
      
      expect(context.actorUserAgent).toBe('unknown');
      expect(context.actorIp).toBe('127.0.0.1'); // Falls back to req.ip
    });

    it('should extract IP from different sources', () => {
      // Test X-Forwarded-For
      let context = auditLogger.extractAuditContext(mockRequest);
      expect(context.actorIp).toBe('********');
      
      // Test fallback to req.ip when X-Forwarded-For missing
      const reqWithoutForwardedFor = {
        ...mockRequest,
        headers: {},
        get: vi.fn(() => undefined)
      };
      context = auditLogger.extractAuditContext(reqWithoutForwardedFor);
      expect(context.actorIp).toBe('127.0.0.1');
      
      // Test fallback to socket.remoteAddress
      const reqWithoutIp = {
        ...reqWithoutForwardedFor,
        ip: undefined
      };
      context = auditLogger.extractAuditContext(reqWithoutIp);
      expect(context.actorIp).toBe('***********');
      
      // Test fallback to 'unknown'
      const reqWithoutAnyIp = {
        ...reqWithoutIp,
        socket: {}
      };
      context = auditLogger.extractAuditContext(reqWithoutAnyIp);
      expect(context.actorIp).toBe('unknown');
    });
  });

  describe('logPaymentOrderAction', () => {
    it('should log payment order action with enhanced metadata', async () => {
      const context = {
        actorType: 'user' as const,
        actorId: 123,
        metadata: { originalKey: 'value' }
      };

      await auditLogger.logPaymentOrderAction(
        'order_created',
        456,
        context,
        { status: 'draft' },
        { status: 'created' }
      );
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'order_created',
          paymentOrderId: 456,
          beforeState: { status: 'draft' },
          afterState: { status: 'created' },
          metadata: {
            originalKey: 'value',
            paymentOrderId: 456
          }
        })
      );
    });
  });

  describe('logPaymentTransactionAction', () => {
    it('should log payment transaction action with enhanced metadata', async () => {
      const context = {
        actorType: 'system' as const,
        metadata: { systemAction: true }
      };

      await auditLogger.logPaymentTransactionAction(
        'transaction_captured',
        789,
        456,
        context,
        { status: 'authorized' },
        { status: 'captured' }
      );
      
      expect(mockDb.values).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'transaction_captured',
          paymentOrderId: 456,
          paymentTransactionId: 789,
          beforeState: { status: 'authorized' },
          afterState: { status: 'captured' },
          metadata: {
            systemAction: true,
            paymentTransactionId: 789,
            paymentOrderId: 456
          }
        })
      );
    });
  });

  describe('Security Incident Shortcuts', () => {
    describe('logSuspiciousActivity', () => {
      it('should log suspicious activity with correct parameters', async () => {
        await auditLogger.logSuspiciousActivity(
          'Multiple rapid payment attempts',
          '*************',
          123,
          { attemptCount: 10 }
        );
        
        expect(mockDb.values).toHaveBeenCalledWith(
          expect.objectContaining({
            incidentType: 'suspicious_activity',
            severity: 'medium',
            description: 'Multiple rapid payment attempts',
            sourceIp: '*************',
            userId: 123,
            incidentData: { attemptCount: 10 }
          })
        );
      });

      it('should handle suspicious activity without user ID', async () => {
        await auditLogger.logSuspiciousActivity(
          'Anonymous suspicious behavior',
          '********'
        );
        
        expect(mockDb.values).toHaveBeenCalledWith(
          expect.objectContaining({
            incidentType: 'suspicious_activity',
            severity: 'medium',
            description: 'Anonymous suspicious behavior',
            sourceIp: '********',
            incidentData: undefined
          })
        );
      });
    });

    describe('logAuthenticationFailure', () => {
      it('should log authentication failure with attempted user', async () => {
        await auditLogger.logAuthenticationFailure(
          'Invalid password',
          '************',
          '<EMAIL>',
          { method: 'password' }
        );
        
        expect(mockDb.values).toHaveBeenCalledWith(
          expect.objectContaining({
            incidentType: 'authentication_failure',
            severity: 'high',
            description: 'Invalid password',
            sourceIp: '************',
            incidentData: {
              attemptedUser: '<EMAIL>',
              method: 'password'
            }
          })
        );
      });
    });

    describe('logPaymentFraud', () => {
      it('should log payment fraud as critical incident', async () => {
        await auditLogger.logPaymentFraud(
          'Invalid signature detected',
          456,
          '***********00',
          123,
          { signatureHash: 'abc123' }
        );
        
        expect(mockDb.values).toHaveBeenCalledWith(
          expect.objectContaining({
            incidentType: 'payment_fraud',
            severity: 'critical',
            description: 'Invalid signature detected',
            sourceIp: '***********00',
            userId: 123,
            paymentOrderId: 456,
            incidentData: { signatureHash: 'abc123' }
          })
        );
        
        // Should trigger critical incident alerting
        expect(mockConsole.error).toHaveBeenCalledWith(
          '🚨 CRITICAL SECURITY INCIDENT 🚨',
          expect.objectContaining({
            type: 'payment_fraud'
          })
        );
      });
    });

    describe('logWebhookSecurityViolation', () => {
      it('should log webhook security violation', async () => {
        await auditLogger.logWebhookSecurityViolation(
          'Invalid webhook signature',
          '***********',
          { webhookId: 'wh_123', expectedSignature: 'sig_456' }
        );
        
        expect(mockDb.values).toHaveBeenCalledWith(
          expect.objectContaining({
            incidentType: 'webhook_security_violation',
            severity: 'high',
            description: 'Invalid webhook signature',
            sourceIp: '***********',
            incidentData: {
              webhookId: 'wh_123',
              expectedSignature: 'sig_456'
            }
          })
        );
      });
    });
  });

  describe('Audit Trail Retrieval', () => {
    describe('getPaymentAuditTrail', () => {
      it('should retrieve audit trail for payment order', async () => {
        const mockAuditLogs = [
          { id: 1, action: 'order_created', paymentOrderId: 456 },
          { id: 2, action: 'payment_captured', paymentOrderId: 456 }
        ];
        
        mockDb.orderBy.mockResolvedValue(mockAuditLogs);
        
        const result = await auditLogger.getPaymentAuditTrail(456);
        
        expect(mockDb.select).toHaveBeenCalled();
        expect(mockDb.where).toHaveBeenCalled();
        expect(mockDb.orderBy).toHaveBeenCalled();
        expect(result).toEqual(mockAuditLogs);
      });

      it('should handle database errors gracefully', async () => {
        mockDb.orderBy.mockRejectedValue(new Error('Database query failed'));
        
        const result = await auditLogger.getPaymentAuditTrail(456);
        
        expect(result).toEqual([]);
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to retrieve audit trail:',
          expect.any(Error)
        );
      });
    });

    describe('getUserSecurityIncidents', () => {
      it('should retrieve security incidents for user', async () => {
        const mockIncidents = [
          { id: 1, incidentType: 'login_failure', userId: 123 },
          { id: 2, incidentType: 'suspicious_activity', userId: 123 }
        ];
        
        mockDb.orderBy.mockResolvedValue(mockIncidents);
        
        const result = await auditLogger.getUserSecurityIncidents(123);
        
        expect(mockDb.select).toHaveBeenCalled();
        expect(mockDb.where).toHaveBeenCalled();
        expect(result).toEqual(mockIncidents);
      });

      it('should handle database errors gracefully', async () => {
        mockDb.orderBy.mockRejectedValue(new Error('Query timeout'));
        
        const result = await auditLogger.getUserSecurityIncidents(123);
        
        expect(result).toEqual([]);
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to retrieve security incidents:',
          expect.any(Error)
        );
      });
    });

    describe('getRecentSecurityIncidents', () => {
      it('should retrieve recent security incidents with default limit', async () => {
        const mockIncidents = Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          incidentType: 'test_incident',
          severity: 'low'
        }));
        
        mockDb.limit.mockResolvedValue(mockIncidents);
        
        const result = await auditLogger.getRecentSecurityIncidents();
        
        expect(mockDb.select).toHaveBeenCalled();
        expect(mockDb.orderBy).toHaveBeenCalled();
        expect(mockDb.limit).toHaveBeenCalledWith(50); // Default limit
        expect(result).toEqual(mockIncidents);
      });

      it('should retrieve recent security incidents with custom limit', async () => {
        const mockIncidents = Array.from({ length: 5 }, (_, i) => ({
          id: i + 1,
          incidentType: 'custom_incident'
        }));
        
        mockDb.limit.mockResolvedValue(mockIncidents);
        
        const result = await auditLogger.getRecentSecurityIncidents(10);
        
        expect(mockDb.limit).toHaveBeenCalledWith(10);
        expect(result).toEqual(mockIncidents);
      });

      it('should handle database errors gracefully', async () => {
        mockDb.limit.mockRejectedValue(new Error('Connection lost'));
        
        const result = await auditLogger.getRecentSecurityIncidents();
        
        expect(result).toEqual([]);
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to retrieve recent security incidents:',
          expect.any(Error)
        );
      });
    });
  });

  describe('High Priority Action Detection', () => {
    it('should identify high-priority actions correctly', () => {
      const highPriorityActions = [
        'payment_order_created',
        'payment_captured',
        'payment_failed',
        'payment_refunded',
        'signature_verification_failed',
        'webhook_signature_invalid',
        'rate_limit_exceeded',
        'suspicious_activity_detected'
      ];

      // Test that all high-priority actions are detected
      for (const action of highPriorityActions) {
        expect((auditLogger as any).isHighPriorityAction(action)).toBe(true);
      }
    });

    it('should identify normal-priority actions correctly', () => {
      const normalPriorityActions = [
        'payment_viewed',
        'user_login',
        'data_retrieved',
        'configuration_updated'
      ];

      // Test that normal actions are not flagged as high-priority
      for (const action of normalPriorityActions) {
        expect((auditLogger as any).isHighPriorityAction(action)).toBe(false);
      }
    });
  });

  describe('Error Resilience', () => {
    it('should not throw errors when audit logging fails', async () => {
      mockDb.values.mockRejectedValue(new Error('Database is down'));
      
      // None of these should throw
      await expect(
        auditLogger.logPaymentAction('test_action', { actorType: 'user' })
      ).resolves.toBeUndefined();
      
      await expect(
        auditLogger.logSecurityIncident({
          incidentType: 'test',
          severity: 'low',
          description: 'test incident'
        })
      ).resolves.toBeUndefined();
      
      await expect(
        auditLogger.logSuspiciousActivity('test', '127.0.0.1')
      ).resolves.toBeUndefined();
    });

    it('should continue logging high-priority actions even with errors', async () => {
      mockDb.values.mockRejectedValue(new Error('Database error'));
      
      await auditLogger.logPaymentAction('payment_order_created', { actorType: 'user' });
      
      // Should still attempt to log high-priority action to console
      expect(mockConsole.log).toHaveBeenCalledWith(
        '[AUDIT] High-priority action: payment_order_created',
        expect.any(Object)
      );
    });
  });

  describe('Timestamp Generation', () => {
    it('should generate ISO timestamps in audit context', () => {
      const mockRequest = {
        user: { userId: 123 },
        headers: {},
        get: vi.fn(() => undefined),
        ip: '127.0.0.1',
        socket: {},
        path: '/test',
        method: 'GET'
      } as any;
      
      const context = auditLogger.extractAuditContext(mockRequest);
      
      expect(context.securityContext?.timestamp).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/ // ISO 8601 format
      );
    });
  });
});