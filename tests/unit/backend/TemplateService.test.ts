import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TemplateService } from '../../../server/services/TemplateService';
import type { SmsTemplate, InsertSmsTemplate } from '../../../shared/schema';

// Mock the database module
vi.mock('../../../server/db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    execute: vi.fn(),
    count: vi.fn()
  }
}));

// Mock the schema module
vi.mock('../../../shared/schema', () => ({
  smsTemplates: {
    id: 'id',
    key: 'key',
    name: 'name',
    content: 'content',
    dltTemplateId: 'dltTemplateId',
    category: 'category',
    variables: 'variables',
    status: 'status',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  },
  smsLogs: {
    id: 'id',
    templateId: 'templateId',
    recipientPhone: 'recipientPhone',
    messageContent: 'messageContent',
    status: 'status',
    twilioMessageSid: 'twilioMessageSid',
    errorMessage: 'errorMessage',
    createdAt: 'createdAt'
  }
}));

import { db } from '../../../server/db';

describe('TemplateService', () => {
  let templateService: TemplateService;
  let mockDb: any;

  const mockTemplate: SmsTemplate = {
    id: 1,
    key: 'booking_confirmation',
    name: 'BKAFARM_BOOKINGCONFIRM',
    content: 'BookAFarm booking for {#var#} on {#var#} confirmed. Thank you for choosing us!',
    dltTemplateId: '1207175138826492810',
    category: 'transactional',
    variables: ['property_name', 'booking_date'],
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    templateService = new TemplateService();
    mockDb = db as any;
    
    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getActiveTemplateByKey', () => {
    it('should return template when found and active', async () => {
      // Arrange
      const mockQuery = {
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([mockTemplate])
      };
      mockDb.select.mockReturnValue(mockQuery);

      // Act
      const result = await templateService.getActiveTemplateByKey('booking_confirmation');

      // Assert
      expect(result).toEqual(mockTemplate);
      expect(mockDb.select).toHaveBeenCalled();
    });

    it('should return null when template not found', async () => {
      // Arrange
      const mockQuery = {
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      };
      mockDb.select.mockReturnValue(mockQuery);

      // Act
      const result = await templateService.getActiveTemplateByKey('nonexistent');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const mockQuery = {
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        limit: vi.fn().mockRejectedValue(new Error('Database error'))
      };
      mockDb.select.mockReturnValue(mockQuery);

      // Act & Assert
      await expect(templateService.getActiveTemplateByKey('test')).rejects.toThrow('Failed to fetch template');
    });
  });

  describe('createTemplate', () => {
    it('should create template successfully', async () => {
      // Arrange
      const newTemplate: InsertSmsTemplate = {
        key: 'test_template',
        name: 'Test Template',
        content: 'Test {#var#}',
        dltTemplateId: 'TEST123',
        category: 'transactional',
        variables: ['name'],
        status: 'active'
      };

      const mockQuery = {
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ ...mockTemplate, ...newTemplate }])
      };
      mockDb.insert.mockReturnValue(mockQuery);

      // Act
      const result = await templateService.createTemplate(newTemplate);

      // Assert
      expect(result).toBeDefined();
      expect(result.key).toBe('test_template');
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('should handle unique constraint errors', async () => {
      // Arrange
      const newTemplate: InsertSmsTemplate = {
        key: 'duplicate_key',
        name: 'Test',
        content: 'Test',
        dltTemplateId: 'TEST',
        category: 'transactional',
        variables: [],
        status: 'active'
      };

      const mockQuery = {
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockRejectedValue(new Error('unique constraint'))
      };
      mockDb.insert.mockReturnValue(mockQuery);

      // Act & Assert
      await expect(templateService.createTemplate(newTemplate)).rejects.toThrow('Template key already exists');
    });
  });

  describe('replaceTemplateVariables', () => {
    it('should replace variables correctly', async () => {
      // Arrange
      const variables = {
        property_name: 'Green Valley Farm',
        booking_date: '2024-12-15'
      };

      // Act
      const result = await templateService.replaceTemplateVariables(mockTemplate, variables);

      // Assert
      expect(result).toBe('BookAFarm booking for Green Valley Farm on 2024-12-15 confirmed. Thank you for choosing us!');
    });

    it('should throw error for missing variables', async () => {
      // Arrange
      const variables = {
        property_name: 'Green Valley Farm'
        // missing booking_date
      };

      // Act & Assert
      await expect(templateService.replaceTemplateVariables(mockTemplate, variables))
        .rejects.toThrow('Missing required variable: booking_date');
    });

    it('should handle invalid template variables format', async () => {
      // Arrange
      const invalidTemplate = { ...mockTemplate, variables: 'invalid' as any };
      const variables = { property_name: 'Test', booking_date: '2024-01-01' };

      // Act & Assert
      await expect(templateService.replaceTemplateVariables(invalidTemplate, variables))
        .rejects.toThrow('Invalid template variables format');
    });
  });

  describe('validateTemplate', () => {
    it('should validate template successfully', async () => {
      // Arrange
      const validTemplate: Partial<InsertSmsTemplate> = {
        key: 'test_key',
        name: 'Test Name',
        content: 'Hello {#var#}',
        dltTemplateId: 'DLT123',
        variables: ['name']
      };

      // Act
      const result = await templateService.validateTemplate(validTemplate);

      // Assert
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for missing required fields', async () => {
      // Arrange
      const invalidTemplate: Partial<InsertSmsTemplate> = {
        // missing required fields
      };

      // Act
      const result = await templateService.validateTemplate(invalidTemplate);

      // Assert
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Template key is required');
      expect(result.errors).toContain('Template name is required');
      expect(result.errors).toContain('Template content is required');
      expect(result.errors).toContain('DLT template ID is required');
    });

    it('should validate variable count matches placeholders', async () => {
      // Arrange
      const mismatchedTemplate: Partial<InsertSmsTemplate> = {
        key: 'test',
        name: 'Test',
        content: 'Hello {#var#} {#var#}', // 2 placeholders
        dltTemplateId: 'DLT123',
        variables: ['name'] // 1 variable
      };

      // Act
      const result = await templateService.validateTemplate(mismatchedTemplate);

      // Assert
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Template content has 2 variable placeholders but 1 variables defined');
    });
  });

  describe('logSmsMessage', () => {
    it('should log SMS message successfully', async () => {
      // Arrange
      const logData = {
        templateId: 1,
        recipientPhone: '+919876543210',
        messageContent: 'Test message',
        status: 'pending'
      };

      const mockQuery = {
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ id: 1, ...logData, createdAt: new Date() }])
      };
      mockDb.insert.mockReturnValue(mockQuery);

      // Act
      const result = await templateService.logSmsMessage(logData);

      // Assert
      expect(result).toBeDefined();
      expect(result.recipientPhone).toBe('+919876543210');
      expect(mockDb.insert).toHaveBeenCalled();
    });
  });

  describe('updateSmsLogStatus', () => {
    it('should update SMS log status successfully', async () => {
      // Arrange
      const mockQuery = {
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ id: 1, status: 'sent' }])
      };
      mockDb.update.mockReturnValue(mockQuery);

      // Act
      const result = await templateService.updateSmsLogStatus(1, 'sent', 'MSG123');

      // Assert
      expect(result).toBeDefined();
      expect(mockDb.update).toHaveBeenCalled();
    });
  });
});