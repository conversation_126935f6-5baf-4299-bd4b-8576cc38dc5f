import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { WhatsAppMessageParser } from '../../../server/services/WhatsAppMessageParser';

describe('WhatsApp Booking Integration', () => {
  let whatsAppMessageParser: WhatsAppMessageParser;

  beforeEach(() => {
    whatsAppMessageParser = new WhatsAppMessageParser();
  });

  describe('Message Parsing for Direct Booking Commands', () => {
    it('should parse "Book August 20 to August 22 for <PERSON>" correctly', () => {
      const message = "Book August 20 to August 22 for <PERSON>";
      const result = whatsAppMessageParser.parseMessage(message);

      expect(result.intent).toBe('booking');
      expect(result.confidence).toBeGreaterThan(0.7);
      expect(result.entities.guestName).toBe('John');
      expect(result.entities.dates?.checkIn).toBeInstanceOf(Date);
      expect(result.entities.dates?.checkOut).toBeInstanceOf(Date);
      expect(result.entities.dates?.rawDates).toContain('August 20 to August 22');
    });

    it('should parse "Book Aug 15-17 for <PERSON>" correctly', () => {
      const message = "Book Aug 15-17 for Sarah";
      const result = whatsAppMessageParser.parseMessage(message);

      expect(result.intent).toBe('booking');
      expect(result.entities.guestName).toBe('Sarah');
      expect(result.entities.dates?.checkIn).toBeInstanceOf(Date);
      expect(result.entities.dates?.checkOut).toBeInstanceOf(Date);
    });

    it('should parse "book December 25 to December 28 for Maria" correctly', () => {
      const message = "book December 25 to December 28 for Maria";
      const result = whatsAppMessageParser.parseMessage(message);

      expect(result.intent).toBe('booking');
      expect(result.entities.guestName).toBe('Maria');
      expect(result.entities.dates?.checkIn?.getMonth()).toBe(11); // December is month 11
      expect(result.entities.dates?.checkIn?.getDate()).toBe(25);
      expect(result.entities.dates?.checkOut?.getDate()).toBe(28);
    });

    it('should handle different name formats', () => {
      const testCases = [
        { message: "Book Jan 1 to Jan 3 for John Smith", expectedName: "John Smith" },
        { message: "Book Feb 15-17 for Mary Jane", expectedName: "Mary Jane" },
        { message: "booking March 10 to March 12 for Alex", expectedName: "Alex" }
      ];

      testCases.forEach(({ message, expectedName }) => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.entities.guestName).toBe(expectedName);
      });
    });

    it('should handle case insensitive booking commands', () => {
      const testCases = [
        "BOOK august 20 to august 22 for JOHN",
        "book August 20 To August 22 For John",
        "Book AUGUST 20 TO AUGUST 22 FOR john"
      ];

      testCases.forEach(message => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.intent).toBe('booking');
        expect(result.entities.guestName).toBe('JOHN' || 'John' || 'john');
      });
    });

    it('should not extract common words as guest names', () => {
      const message = "Book August 20 to August 22 for booking";
      const result = whatsAppMessageParser.parseMessage(message);

      expect(result.entities.guestName).toBeUndefined();
    });

    it('should handle different date formats', () => {
      const testCases = [
        "Book 20 August to 22 August for John",
        "Book August 20 to August 22 for John", 
        "Book Aug 20-22 for John",
        "Book 20-22 Aug for John"
      ];

      testCases.forEach(message => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.intent).toBe('booking');
        expect(result.entities.dates?.checkIn).toBeInstanceOf(Date);
        expect(result.entities.dates?.checkOut).toBeInstanceOf(Date);
      });
    });

    it('should handle year boundary correctly', () => {
      const message = "Book December 30 to January 2 for John";
      const result = whatsAppMessageParser.parseMessage(message);

      if (result.entities.dates?.checkIn && result.entities.dates?.checkOut) {
        // If end date is before start date in same year, it should be next year
        expect(result.entities.dates.checkOut.getFullYear()).toBeGreaterThanOrEqual(
          result.entities.dates.checkIn.getFullYear()
        );
      }
    });
  });

  describe('Message Intent Detection', () => {
    it('should detect booking intent with high confidence', () => {
      const bookingMessages = [
        "Book farmhouse for next week",
        "I want to book your property",
        "Reserve August 20-22 for John",
        "Looking for booking availability"
      ];

      bookingMessages.forEach(message => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.intent).toBe('booking');
        expect(result.confidence).toBeGreaterThan(0.5);
      });
    });

    it('should detect availability intent', () => {
      const availabilityMessages = [
        "Check availability for August",
        "Is your farmhouse available next week?",
        "Are there any free dates in September?"
      ];

      availabilityMessages.forEach(message => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.intent).toBe('availability');
      });
    });

    it('should detect help intent', () => {
      const helpMessages = [
        "help",
        "Hi, I need help",
        "What can you do?",
        "Hello"
      ];

      helpMessages.forEach(message => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.intent).toBe('help');
      });
    });
  });

  describe('Entity Extraction', () => {
    it('should extract guest count when present', () => {
      const testCases = [
        { message: "Book for 5 people August 20-22", expectedGuests: 5 },
        { message: "Reserve for 8 guests next week", expectedGuests: 8 },
        { message: "Need booking for 12 adults", expectedGuests: 12 }
      ];

      testCases.forEach(({ message, expectedGuests }) => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.entities.guests).toBe(expectedGuests);
      });
    });

    it('should extract location when mentioned', () => {
      const message = "Looking for farmhouse in Gurgaon for August";
      const result = whatsAppMessageParser.parseMessage(message);
      
      expect(result.entities.location).toBeDefined();
    });

    it('should handle messages without all entities gracefully', () => {
      const incompleteMessages = [
        "Book August 20",  // Missing end date and guest name
        "Book for John",   // Missing dates
        "Book August 20 to August 22"  // Missing guest name
      ];

      incompleteMessages.forEach(message => {
        const result = whatsAppMessageParser.parseMessage(message);
        expect(result.intent).toBe('booking');
        // Should not crash, should return partial results
        expect(result.confidence).toBeGreaterThan(0);
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty messages', () => {
      const result = whatsAppMessageParser.parseMessage("");
      expect(result.intent).toBe('unknown');
      expect(result.confidence).toBe(0);
    });

    it('should handle very long messages', () => {
      const longMessage = "Book " + "very ".repeat(100) + "August 20 to August 22 for John";
      const result = whatsAppMessageParser.parseMessage(longMessage);
      
      expect(result.intent).toBe('booking');
      expect(result.entities.guestName).toBe('John');
    });

    it('should handle special characters in guest names', () => {
      const message = "Book August 20 to August 22 for Jean-Pierre";
      const result = whatsAppMessageParser.parseMessage(message);
      
      // Should handle names with hyphens but might not extract perfectly
      expect(result.intent).toBe('booking');
    });

    it('should validate date ranges make sense', () => {
      const message = "Book August 22 to August 20 for John"; // End before start
      const result = whatsAppMessageParser.parseMessage(message);
      
      // Should still parse but might adjust dates
      expect(result.intent).toBe('booking');
      expect(result.entities.dates).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should parse messages quickly', () => {
      const message = "Book August 20 to August 22 for John";
      const startTime = performance.now();
      
      for (let i = 0; i < 100; i++) {
        whatsAppMessageParser.parseMessage(message);
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / 100;
      
      // Should parse each message in under 5ms on average
      expect(averageTime).toBeLessThan(5);
    });
  });
});

describe('WhatsApp Integration Flow', () => {
  describe('End-to-End Booking Flow', () => {
    it('should describe the complete booking flow', () => {
      // This is a documentation test that describes the expected flow
      const expectedFlow = {
        step1: "User sends: 'Book August 20 to August 22 for John'",
        step2: "System parses message and extracts: dates, guest name",
        step3: "System finds property owner by phone number mapping",
        step4: "If owner has one property: direct booking creation",
        step5: "If owner has multiple properties: property selection",
        step6: "System checks calendar availability",
        step7: "If available: creates booking in CalendarService",
        step8: "System sends confirmation via WhatsApp",
        step9: "Booking appears in BookAFarm calendar instantly"
      };

      expect(expectedFlow.step1).toContain('Book August 20 to August 22 for John');
      expect(expectedFlow.step9).toContain('BookAFarm calendar instantly');
    });
  });

  describe('Property Owner Mapping', () => {
    it('should describe phone number to property mapping requirement', () => {
      const mappingRequirement = {
        database: "Users table should have whatsappNumber field",
        lookup: "Find owner by WhatsApp number when message received",
        fallback: "Use phone field if whatsappNumber not set",
        properties: "Owner should have active properties for booking"
      };

      expect(mappingRequirement.database).toContain('whatsappNumber');
      expect(mappingRequirement.lookup).toContain('WhatsApp number');
    });
  });

  describe('Calendar Integration', () => {
    it('should describe calendar service integration', () => {
      const calendarIntegration = {
        availabilityCheck: "Use CalendarService.checkAvailability()",
        bookingCreation: "Use CalendarService.createCalendarBooking()",
        realTimeSync: "Booking appears immediately in web calendar",
        webhooks: "Triggers existing webhook system",
        iCalRegeneration: "iCal feed updates automatically"
      };

      expect(calendarIntegration.bookingCreation).toContain('createCalendarBooking');
      expect(calendarIntegration.realTimeSync).toContain('immediately');
    });
  });
});