import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest'
import { PerformanceMonitorService } from '../../../server/services/PerformanceMonitorService'
import { cacheService } from '../../../server/services/CacheService'
import { propertyCacheService } from '../../../server/services/PropertyCacheService'

// Mock dependencies
vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    getStats: vi.fn()
  }
}))

vi.mock('../../../server/services/PropertyCacheService', () => ({
  propertyCacheService: {
    getCacheMetrics: vi.fn()
  }
}))

vi.mock('../../../server/services/LoggerService', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }
}))

describe('PerformanceMonitorService', () => {
  let performanceMonitor: PerformanceMonitorService

  beforeEach(() => {
    performanceMonitor = PerformanceMonitorService.getInstance()
    // Reset analytics to ensure clean state
    performanceMonitor.resetAnalytics()
    vi.clearAllMocks()
    
    // Mock cache service stats
    vi.mocked(cacheService.getStats).mockReturnValue({
      size: 500,
      maxSize: 1000,
      hitRate: 0.8,
      memoryUsage: '2.5 KB'
    })
    
    // Mock property cache metrics
    vi.mocked(propertyCacheService.getCacheMetrics).mockReturnValue({
      hits: 80,
      misses: 20,
      invalidations: 5,
      lastReset: Date.now() - 3600000,
      hitRate: 80,
      totalRequests: 100,
      uptimeHours: 1
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Query Metrics Recording', () => {
    test('should record successful query metrics', () => {
      const operation = 'GET /api/properties'
      const duration = 150
      const success = true

      performanceMonitor.recordQuery(operation, duration, success)

      // Since metrics are private, we test through getMetrics
      expect(() => performanceMonitor.recordQuery(operation, duration, success)).not.toThrow()
    })

    test('should record failed query metrics', () => {
      const operation = 'POST /api/properties'
      const duration = 300
      const success = false
      const error = 'Validation error'

      performanceMonitor.recordQuery(operation, duration, success, false, error)

      expect(() => performanceMonitor.recordQuery(operation, duration, success, false, error)).not.toThrow()
    })

    test('should record cache hit information', () => {
      const operation = 'GET /api/properties/123'
      const duration = 50
      const success = true
      const cacheHit = true

      performanceMonitor.recordQuery(operation, duration, success, cacheHit)

      expect(() => performanceMonitor.recordQuery(operation, duration, success, cacheHit)).not.toThrow()
    })

    test('should handle multiple query recordings', () => {
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordQuery(`operation-${i}`, Math.random() * 1000, true)
      }

      expect(() => {
        for (let i = 0; i < 10; i++) {
          performanceMonitor.recordQuery(`operation-${i}`, Math.random() * 1000, true)
        }
      }).not.toThrow()
    })
  })

  describe('Search Analytics Recording', () => {
    test('should record location searches', () => {
      const searchParams = {
        location: 'Mumbai',
        priceRange: 'mid-range',
        searchTerm: 'luxury villa'
      }

      performanceMonitor.recordSearch(searchParams)

      expect(() => performanceMonitor.recordSearch(searchParams)).not.toThrow()
    })

    test('should record multiple search variations', () => {
      const searches = [
        { location: 'Delhi', priceRange: 'budget' },
        { location: 'Bangalore', priceRange: 'premium' },
        { location: 'Mumbai', priceRange: 'mid-range' },
        { searchTerm: 'beachfront property' }
      ]

      searches.forEach(search => {
        performanceMonitor.recordSearch(search)
      })

      expect(() => {
        searches.forEach(search => performanceMonitor.recordSearch(search))
      }).not.toThrow()
    })

    test('should handle empty search parameters', () => {
      performanceMonitor.recordSearch({})

      expect(() => performanceMonitor.recordSearch({})).not.toThrow()
    })
  })

  describe('Performance Metrics Calculation', () => {
    test('should calculate response time metrics correctly', async () => {
      // Record some test metrics
      const durations = [100, 200, 150, 300, 180, 120, 250, 90, 400, 160]
      durations.forEach((duration, index) => {
        performanceMonitor.recordQuery(`test-operation-${index}`, duration, true)
      })

      const metrics = await performanceMonitor.getMetrics()

      expect(metrics.averageResponseTime).toBeGreaterThan(0)
      expect(metrics.p95ResponseTime).toBeGreaterThan(0)
      expect(metrics.p99ResponseTime).toBeGreaterThan(0)
      expect(metrics.requestCount).toBeGreaterThan(0)
    })

    test('should calculate error rate correctly', async () => {
      // Reset first to ensure clean state
      performanceMonitor.resetAnalytics()
      
      // Record successful and failed operations
      for (let i = 0; i < 8; i++) {
        performanceMonitor.recordQuery(`success-${i}`, 100, true)
      }
      for (let i = 0; i < 2; i++) {
        performanceMonitor.recordQuery(`error-${i}`, 200, false)
      }

      const metrics = await performanceMonitor.getMetrics()

      expect(metrics.errorRate).toBeCloseTo(20, 1) // 2/10 = 20%
    })

    test('should calculate cache hit rate correctly', async () => {
      // Reset first to ensure clean state
      performanceMonitor.resetAnalytics()
      
      // Record cache hits and misses
      for (let i = 0; i < 7; i++) {
        performanceMonitor.recordQuery(`cache-hit-${i}`, 50, true, true)
      }
      for (let i = 0; i < 3; i++) {
        performanceMonitor.recordQuery(`cache-miss-${i}`, 150, true, false)
      }

      const metrics = await performanceMonitor.getMetrics()

      expect(metrics.cacheHitRate).toBeCloseTo(70, 1) // 7/10 = 70%
    })

    test('should handle no metrics gracefully', async () => {
      // Create a fresh instance with no recorded metrics
      const freshMonitor = new (PerformanceMonitorService as any)()

      const metrics = await freshMonitor.getMetrics()

      expect(metrics.averageResponseTime).toBe(0)
      expect(metrics.requestCount).toBe(0)
      expect(metrics.errorRate).toBe(0)
    })
  })

  describe('Health Check', () => {
    test('should return healthy status with good metrics', async () => {
      // Reset first to ensure clean state
      performanceMonitor.resetAnalytics()
      
      // Record good performance metrics (low error rate, good response times, good cache hit rate)
      for (let i = 0; i < 100; i++) {
        performanceMonitor.recordQuery(`good-${i}`, 100, true, true) // All successful with cache hits
      }

      const healthCheck = await performanceMonitor.performHealthCheck()

      expect(healthCheck.status).toBe('healthy')
      expect(healthCheck.issues).toHaveLength(0)
      expect(healthCheck.metrics).toBeDefined()
    })

    test('should return degraded status with high error rate', async () => {
      // Record high error rate
      for (let i = 0; i < 50; i++) {
        performanceMonitor.recordQuery(`good-${i}`, 100, true)
      }
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordQuery(`error-${i}`, 200, false)
      }

      const healthCheck = await performanceMonitor.performHealthCheck()

      expect(healthCheck.status).toBe('degraded')
      expect(healthCheck.issues.some(issue => issue.includes('error rate'))).toBe(true)
    })

    test('should return degraded status with low cache hit rate', async () => {
      // Record low cache hit rate
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordQuery(`cache-hit-${i}`, 50, true, true)
      }
      for (let i = 0; i < 40; i++) {
        performanceMonitor.recordQuery(`cache-miss-${i}`, 150, true, false)
      }

      const healthCheck = await performanceMonitor.performHealthCheck()

      expect(healthCheck.status).toBe('degraded')
      expect(healthCheck.issues.some(issue => issue.includes('cache hit rate'))).toBe(true)
    })

    test('should return degraded status with slow queries', async () => {
      // Record many slow queries
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordQuery(`slow-${i}`, 1500, true) // Slow queries > 500ms
      }

      const healthCheck = await performanceMonitor.performHealthCheck()

      expect(healthCheck.status).toBe('degraded')
      expect(healthCheck.issues.some(issue => issue.includes('slow queries'))).toBe(true)
    })

    test('should return unhealthy status with very slow response times', async () => {
      // Reset first to ensure clean state
      performanceMonitor.resetAnalytics()
      
      // Record very slow queries for P99 (need multiple slow queries to push P99 over threshold)
      for (let i = 0; i < 95; i++) {
        performanceMonitor.recordQuery(`fast-${i}`, 100, true)
      }
      for (let i = 0; i < 5; i++) {
        performanceMonitor.recordQuery(`slow-${i}`, 3000, true) // Multiple very slow requests
      }

      const healthCheck = await performanceMonitor.performHealthCheck()

      expect(healthCheck.status).toBe('unhealthy')
      expect(healthCheck.issues.some(issue => issue.includes('P99 response time'))).toBe(true)
    })
  })

  describe('Performance Summary', () => {
    test('should generate performance summary', async () => {
      // Record some test data
      for (let i = 0; i < 20; i++) {
        performanceMonitor.recordQuery(`summary-test-${i}`, 150, true, true)
      }

      performanceMonitor.recordSearch({ location: 'Mumbai' })
      performanceMonitor.recordSearch({ location: 'Delhi' })
      performanceMonitor.recordSearch({ location: 'Mumbai' })

      const summary = await performanceMonitor.getPerformanceSummary()

      expect(summary).toHaveProperty('status')
      expect(summary).toHaveProperty('responseTime')
      expect(summary).toHaveProperty('cacheHitRate')
      expect(summary).toHaveProperty('errorRate')
      expect(summary).toHaveProperty('uptime')
      expect(summary).toHaveProperty('topLocations')
      expect(Array.isArray(summary.topLocations)).toBe(true)
    })

    test('should format uptime correctly', async () => {
      const summary = await performanceMonitor.getPerformanceSummary()

      expect(summary.uptime).toMatch(/^\d+d \d+h$/)
    })
  })

  describe('Analytics Reset', () => {
    test('should reset analytics data', () => {
      // Record some data first
      performanceMonitor.recordSearch({ location: 'Mumbai' })
      performanceMonitor.recordQuery('test', 100, true)

      performanceMonitor.resetAnalytics()

      expect(() => performanceMonitor.resetAnalytics()).not.toThrow()
    })
  })

  describe('Metrics Export', () => {
    test('should export metrics in Prometheus format', async () => {
      // Record some test metrics
      for (let i = 0; i < 5; i++) {
        performanceMonitor.recordQuery(`export-test-${i}`, 200, true)
      }

      const exportedMetrics = await performanceMonitor.exportMetrics()

      expect(typeof exportedMetrics).toBe('string')
      expect(exportedMetrics).toContain('property_api_response_time_ms')
      expect(exportedMetrics).toContain('property_api_requests_total')
      expect(exportedMetrics).toContain('property_api_error_rate')
      expect(exportedMetrics).toContain('property_cache_hit_rate')
      expect(exportedMetrics).toContain('property_uptime_seconds')
    })

    test('should include proper Prometheus formatting', async () => {
      const exportedMetrics = await performanceMonitor.exportMetrics()

      // Check for proper Prometheus format
      expect(exportedMetrics).toContain('# HELP')
      expect(exportedMetrics).toContain('# TYPE')
      expect(exportedMetrics).toMatch(/property_\w+ \d+/)
    })
  })

  describe('Edge Cases and Error Handling', () => {
    test('should handle very large numbers of metrics', () => {
      // Record a large number of metrics
      for (let i = 0; i < 2000; i++) {
        performanceMonitor.recordQuery(`bulk-${i}`, Math.random() * 1000, Math.random() > 0.1)
      }

      expect(() => {
        for (let i = 0; i < 2000; i++) {
          performanceMonitor.recordQuery(`bulk-${i}`, Math.random() * 1000, Math.random() > 0.1)
        }
      }).not.toThrow()
    })

    test('should handle negative durations gracefully', () => {
      performanceMonitor.recordQuery('negative-test', -100, true)

      expect(() => performanceMonitor.recordQuery('negative-test', -100, true)).not.toThrow()
    })

    test('should handle undefined search parameters', () => {
      performanceMonitor.recordSearch({
        location: undefined,
        priceRange: undefined,
        searchTerm: undefined
      })

      expect(() => performanceMonitor.recordSearch({
        location: undefined,
        priceRange: undefined,
        searchTerm: undefined
      })).not.toThrow()
    })

    test('should handle empty strings in search parameters', () => {
      performanceMonitor.recordSearch({
        location: '',
        priceRange: '',
        searchTerm: ''
      })

      expect(() => performanceMonitor.recordSearch({
        location: '',
        priceRange: '',
        searchTerm: ''
      })).not.toThrow()
    })
  })

  describe('Singleton Pattern', () => {
    test('should return the same instance', () => {
      const instance1 = PerformanceMonitorService.getInstance()
      const instance2 = PerformanceMonitorService.getInstance()

      expect(instance1).toBe(instance2)
    })
  })

  describe('Slow Query Detection', () => {
    test('should identify slow queries in metrics', async () => {
      // Record some slow queries
      performanceMonitor.recordQuery('slow-query-1', 800, true)
      performanceMonitor.recordQuery('slow-query-2', 1200, true)
      performanceMonitor.recordQuery('fast-query', 100, true)

      const metrics = await performanceMonitor.getMetrics()

      expect(metrics.slowQueries).toBeDefined()
      expect(Array.isArray(metrics.slowQueries)).toBe(true)
      expect(metrics.slowQueries.length).toBeGreaterThan(0)
      
      // Check that slow queries are actually slow
      metrics.slowQueries.forEach(query => {
        expect(query.duration).toBeGreaterThan(500)
      })
    })

    test('should limit slow queries in results', async () => {
      // Record many slow queries
      for (let i = 0; i < 20; i++) {
        performanceMonitor.recordQuery(`slow-query-${i}`, 600 + i * 10, true)
      }

      const metrics = await performanceMonitor.getMetrics()

      // Should be limited to 10 slow queries
      expect(metrics.slowQueries.length).toBeLessThanOrEqual(10)
    })
  })

  describe('Analytics Aggregation', () => {
    test('should aggregate location searches correctly', async () => {
      // Record searches for different locations
      const locations = ['Mumbai', 'Delhi', 'Mumbai', 'Bangalore', 'Mumbai']
      locations.forEach(location => {
        performanceMonitor.recordSearch({ location })
      })

      const metrics = await performanceMonitor.getMetrics()

      expect(metrics.topLocations).toBeDefined()
      expect(Array.isArray(metrics.topLocations)).toBe(true)
      
      // Mumbai should be the top location (3 searches)
      if (metrics.topLocations.length > 0) {
        expect(metrics.topLocations[0].location).toBe('Mumbai')
        expect(metrics.topLocations[0].searches).toBe(3)
      }
    })

    test('should aggregate price range searches', async () => {
      const priceRanges = ['budget', 'mid-range', 'budget', 'premium', 'budget']
      priceRanges.forEach(priceRange => {
        performanceMonitor.recordSearch({ priceRange })
      })

      const metrics = await performanceMonitor.getMetrics()

      expect(metrics.priceRangePopularity).toBeDefined()
      expect(Array.isArray(metrics.priceRangePopularity)).toBe(true)
      
      // Budget should be the most popular (3 searches)
      if (metrics.priceRangePopularity.length > 0) {
        expect(metrics.priceRangePopularity[0].range).toBe('budget')
        expect(metrics.priceRangePopularity[0].count).toBe(3)
      }
    })
  })
})