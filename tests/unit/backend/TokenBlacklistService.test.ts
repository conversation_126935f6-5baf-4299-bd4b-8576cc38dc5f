import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest'
import { TokenBlacklistService } from '../../../server/services/TokenBlacklistService'
import { cacheService } from '../../../server/services/CacheService'

// Mock the cache service
vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    set: vi.fn(),
    exists: vi.fn(),
    keys: vi.fn(),
    getStats: vi.fn()
  }
}))

// Mock the logger service
vi.mock('../../../server/services/LoggerService', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }
}))

describe('TokenBlacklistService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('blacklistToken', () => {
    test('should blacklist a token with correct key and TTL', async () => {
      const token = 'sample.jwt.token'
      const mockSet = vi.mocked(cacheService.set)

      await TokenBlacklistService.blacklistToken(token)

      expect(mockSet).toHaveBeenCalledWith(
        'blacklisted_token:sample.jwt.token',
        true,
        7 * 24 * 60 * 60 // 7 days in seconds
      )
      expect(mockSet).toHaveBeenCalledTimes(1)
    })

    test('should handle special characters in tokens', async () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      const mockSet = vi.mocked(cacheService.set)

      await TokenBlacklistService.blacklistToken(token)

      expect(mockSet).toHaveBeenCalledWith(
        `blacklisted_token:${token}`,
        true,
        7 * 24 * 60 * 60
      )
    })

    test('should handle empty token gracefully', async () => {
      const token = ''
      const mockSet = vi.mocked(cacheService.set)

      await TokenBlacklistService.blacklistToken(token)

      // Should not call cache service for empty token
      expect(mockSet).not.toHaveBeenCalled()
    })
  })

  describe('isTokenBlacklisted', () => {
    test('should return true for blacklisted token', async () => {
      const token = 'blacklisted.jwt.token'
      const mockExists = vi.mocked(cacheService.exists).mockResolvedValue(true)

      const result = await TokenBlacklistService.isTokenBlacklisted(token)

      expect(result).toBe(true)
      expect(mockExists).toHaveBeenCalledWith('blacklisted_token:blacklisted.jwt.token')
      expect(mockExists).toHaveBeenCalledTimes(1)
    })

    test('should return false for non-blacklisted token', async () => {
      const token = 'valid.jwt.token'
      const mockExists = vi.mocked(cacheService.exists).mockResolvedValue(false)

      const result = await TokenBlacklistService.isTokenBlacklisted(token)

      expect(result).toBe(false)
      expect(mockExists).toHaveBeenCalledWith('blacklisted_token:valid.jwt.token')
      expect(mockExists).toHaveBeenCalledTimes(1)
    })

    test('should handle cache service errors gracefully', async () => {
      const token = 'error.jwt.token'
      const mockExists = vi.mocked(cacheService.exists).mockRejectedValue(new Error('Cache error'))

      await expect(TokenBlacklistService.isTokenBlacklisted(token)).rejects.toThrow('Cache error')
      expect(mockExists).toHaveBeenCalledTimes(1)
    })

    test('should use correct cache key format', async () => {
      const token = 'test.token.123'
      const mockExists = vi.mocked(cacheService.exists).mockResolvedValue(false)

      await TokenBlacklistService.isTokenBlacklisted(token)

      expect(mockExists).toHaveBeenCalledWith('blacklisted_token:test.token.123')
    })
  })

  describe('blacklistUserTokens', () => {
    test('should log user token blacklisting action', async () => {
      const userId = 'user123'

      // This method currently just logs - in production would blacklist all user tokens
      await TokenBlacklistService.blacklistUserTokens(userId)

      // Verify it doesn't throw and completes successfully
      expect(true).toBe(true) // Placeholder assertion since method currently only logs
    })

    test('should handle different user ID formats', async () => {
      const userIds = ['123', 'uuid-string', '', '<EMAIL>']

      for (const userId of userIds) {
        await expect(TokenBlacklistService.blacklistUserTokens(userId)).resolves.toBeUndefined()
      }
    })
  })

  describe('getBlacklistStats', () => {
    test('should return correct statistics', async () => {
      const mockKeys = ['blacklisted_token:token1', 'blacklisted_token:token2', 'blacklisted_token:token3']
      const mockStats = {
        size: 100,
        maxSize: 1000,
        hitRate: 0.85,
        memoryUsage: '2.5 KB'
      }

      vi.mocked(cacheService.keys).mockResolvedValue(mockKeys)
      vi.mocked(cacheService.getStats).mockReturnValue(mockStats)

      const result = await TokenBlacklistService.getBlacklistStats()

      expect(result).toEqual({
        count: 3,
        memoryUsage: '2.5 KB'
      })
      expect(cacheService.keys).toHaveBeenCalledWith('blacklisted_token:*')
      expect(cacheService.getStats).toHaveBeenCalledTimes(1)
    })

    test('should handle empty blacklist', async () => {
      const mockKeys: string[] = []
      const mockStats = {
        size: 0,
        maxSize: 1000,
        hitRate: 0,
        memoryUsage: '0 KB'
      }

      vi.mocked(cacheService.keys).mockResolvedValue(mockKeys)
      vi.mocked(cacheService.getStats).mockReturnValue(mockStats)

      const result = await TokenBlacklistService.getBlacklistStats()

      expect(result).toEqual({
        count: 0,
        memoryUsage: '0 KB'
      })
    })

    test('should handle cache service errors in getBlacklistStats', async () => {
      vi.mocked(cacheService.keys).mockRejectedValue(new Error('Keys error'))

      await expect(TokenBlacklistService.getBlacklistStats()).rejects.toThrow('Keys error')
    })
  })

  describe('clearExpiredTokens', () => {
    test('should query blacklist keys and log status', async () => {
      const mockKeys = ['blacklisted_token:token1', 'blacklisted_token:token2']
      vi.mocked(cacheService.keys).mockResolvedValue(mockKeys)

      await TokenBlacklistService.clearExpiredTokens()

      expect(cacheService.keys).toHaveBeenCalledWith('blacklisted_token:*')
      expect(cacheService.keys).toHaveBeenCalledTimes(1)
    })

    test('should handle errors in clearExpiredTokens gracefully', async () => {
      vi.mocked(cacheService.keys).mockRejectedValue(new Error('Clear error'))

      await expect(TokenBlacklistService.clearExpiredTokens()).rejects.toThrow('Clear error')
    })
  })

  describe('Integration scenarios', () => {
    test('should handle token lifecycle correctly', async () => {
      const token = 'lifecycle.test.token'
      
      // Mock cache responses for the lifecycle
      vi.mocked(cacheService.exists)
        .mockResolvedValueOnce(false) // Initially not blacklisted
        .mockResolvedValueOnce(true)  // After blacklisting

      // 1. Token should not be blacklisted initially
      let isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token)
      expect(isBlacklisted).toBe(false)

      // 2. Blacklist the token
      await TokenBlacklistService.blacklistToken(token)
      expect(cacheService.set).toHaveBeenCalledWith(
        `blacklisted_token:${token}`,
        true,
        7 * 24 * 60 * 60
      )

      // 3. Token should now be blacklisted
      isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token)
      expect(isBlacklisted).toBe(true)
    })

    test('should handle multiple tokens correctly', async () => {
      const tokens = ['token1', 'token2', 'token3']
      
      // Blacklist multiple tokens
      for (const token of tokens) {
        await TokenBlacklistService.blacklistToken(token)
      }

      expect(cacheService.set).toHaveBeenCalledTimes(tokens.length)
      
      // Verify each token was blacklisted with correct parameters
      tokens.forEach((token, index) => {
        expect(cacheService.set).toHaveBeenNthCalledWith(
          index + 1,
          `blacklisted_token:${token}`,
          true,
          7 * 24 * 60 * 60
        )
      })
    })

    test('should handle concurrent blacklisting operations', async () => {
      const tokens = Array.from({ length: 10 }, (_, i) => `concurrent.token.${i}`)
      
      // Simulate concurrent blacklisting
      const promises = tokens.map(token => TokenBlacklistService.blacklistToken(token))
      await Promise.all(promises)

      expect(cacheService.set).toHaveBeenCalledTimes(tokens.length)
    })
  })

  describe('Edge cases', () => {
    test('should handle extremely long tokens', async () => {
      const longToken = 'a'.repeat(10000) // 10KB token
      const mockSet = vi.mocked(cacheService.set)

      await TokenBlacklistService.blacklistToken(longToken)

      expect(mockSet).toHaveBeenCalledWith(
        `blacklisted_token:${longToken}`,
        true,
        7 * 24 * 60 * 60
      )
    })

    test('should handle tokens with special characters', async () => {
      const specialToken = 'token.with-special_chars+and=symbols/and\\backslashes'
      const mockSet = vi.mocked(cacheService.set)

      await TokenBlacklistService.blacklistToken(specialToken)

      expect(mockSet).toHaveBeenCalledWith(
        `blacklisted_token:${specialToken}`,
        true,
        7 * 24 * 60 * 60
      )
    })

    test('should handle null/undefined tokens gracefully', async () => {
      const mockSet = vi.mocked(cacheService.set)

      // These should not throw errors and should not call cache service
      await TokenBlacklistService.blacklistToken(null as any)
      await TokenBlacklistService.blacklistToken(undefined as any)

      expect(mockSet).not.toHaveBeenCalled()
    })
  })
})