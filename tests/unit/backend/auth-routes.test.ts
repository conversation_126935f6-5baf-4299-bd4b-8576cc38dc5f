import { describe, test, expect, vi, beforeEach, beforeAll } from 'vitest'
import request from 'supertest'
import express from 'express'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'
import { ZodError } from 'zod'

// Mock the storage module
const mockStorage = {
  getUserByEmail: vi.fn(),
  createUser: vi.fn(),
  getUser: vi.fn(),
}

// Mock the email service
const mockEmailService = {
  sendBookingEmails: vi.fn(),
}

// Mock bcrypt
vi.mock('bcrypt', () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  }
}))

// Mock jsonwebtoken
vi.mock('jsonwebtoken', () => ({
  default: {
    sign: vi.fn(),
    verify: vi.fn(),
    decode: vi.fn(),
  }
}))

const TEST_JWT_SECRET = 'test-jwt-secret-key-12345'

// Create a test app with auth routes
function createTestApp() {
  const app = express()
  app.use(express.json())
  app.use(express.urlencoded({ extended: true }))

  // Mock rate limiting middleware
  const mockRateLimit = (req: any, res: any, next: any) => next()

  // Mock validation middleware
  const mockValidate = (schema: any) => (req: any, res: any, next: any) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({ 
          message: "Validation error", 
          errors: error.errors 
        })
      }
      next(error)
    }
  }

  // Simple validation schemas for testing
  const userRegisterSchema = {
    parse: (data: any) => {
      if (!data.email || !data.password || !data.username || !data.fullName) {
        throw new ZodError([])
      }
      return data
    }
  }

  const userLoginSchema = {
    parse: (data: any) => {
      if (!data.email || !data.password) {
        throw new ZodError([])
      }
      return data
    }
  }

  // Register route
  app.post('/api/auth/register', mockRateLimit, mockValidate(userRegisterSchema), async (req, res) => {
    try {
      const { email, consentData } = req.body

      // Check if user already exists
      const existingUser = await mockStorage.getUserByEmail(email)
      if (existingUser) {
        return res.status(400).json({ message: "User with this email already exists" })
      }

      // Prepare user data with consent information
      const userData = {
        ...req.body,
        termsAccepted: consentData?.termsAccepted || false,
        privacyPolicyAccepted: consentData?.termsAccepted || false,
        cookiePolicyAccepted: consentData?.termsAccepted || false,
        dataProcessingConsent: consentData?.dataProcessingConsent || false,
        marketingConsent: consentData?.marketingConsent || false,
        consentTimestamp: new Date()
      }

      delete userData.consentData

      const user = await mockStorage.createUser(userData)

      // Generate token
      const token = jwt.sign(
        { userId: user.id, role: user.role },
        TEST_JWT_SECRET,
        { expiresIn: "7d" }
      )

      res.status(201).json({ 
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          role: user.role
        },
        token 
      })
    } catch (error) {
      console.error("Registration error:", error)
      res.status(500).json({ message: "Server error during registration" })
    }
  })

  // Login route
  app.post('/api/auth/login', mockRateLimit, mockValidate(userLoginSchema), async (req, res) => {
    try {
      const { email, password } = req.body

      const user = await mockStorage.getUserByEmail(email)
      if (!user) {
        return res.status(401).json({ message: "Invalid email or password" })
      }

      const isPasswordValid = await bcrypt.compare(password, user.password)
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid email or password" })
      }

      const token = jwt.sign(
        { userId: user.id, role: user.role },
        TEST_JWT_SECRET,
        { expiresIn: "1d" }
      )

      res.json({ 
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          role: user.role
        },
        token 
      })
    } catch (error) {
      console.error("Login error:", error)
      res.status(500).json({ message: "Server error during login" })
    }
  })

  return app
}

describe('Authentication Routes', () => {
  let app: express.Application

  beforeAll(() => {
    process.env.JWT_SECRET = TEST_JWT_SECRET
  })

  beforeEach(() => {
    app = createTestApp()
    vi.clearAllMocks()
  })

  describe('POST /api/auth/register', () => {
    const validRegistrationData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123!',
      fullName: 'Test User',
      consentData: {
        termsAccepted: true,
        dataProcessingConsent: true,
        marketingConsent: false
      }
    }

    test('should register a new user successfully', async () => {
      // Mock storage methods
      mockStorage.getUserByEmail.mockResolvedValue(null) // No existing user
      mockStorage.createUser.mockResolvedValue({
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'user'
      })

      // Mock JWT signing
      ;(jwt.sign as any).mockReturnValue('mock-jwt-token')

      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty('user')
      expect(response.body).toHaveProperty('token')
      expect(response.body.user).toEqual({
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'user'
      })
      expect(response.body.token).toBe('mock-jwt-token')
      
      expect(mockStorage.getUserByEmail).toHaveBeenCalledWith('<EMAIL>')
      expect(mockStorage.createUser).toHaveBeenCalledWith(expect.objectContaining({
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        termsAccepted: true,
        dataProcessingConsent: true,
        marketingConsent: false
      }))
    })

    test('should reject registration if user already exists', async () => {
      mockStorage.getUserByEmail.mockResolvedValue({
        id: 1,
        email: '<EMAIL>'
      })

      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)

      expect(response.status).toBe(400)
      expect(response.body).toEqual({
        message: "User with this email already exists"
      })
      expect(mockStorage.createUser).not.toHaveBeenCalled()
    })

    test('should reject registration with missing required fields', async () => {
      const incompleteData = {
        email: '<EMAIL>'
        // Missing username, password, fullName
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(incompleteData)

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('message', 'Validation error')
      expect(mockStorage.getUserByEmail).not.toHaveBeenCalled()
    })

    test('should handle database errors during registration', async () => {
      mockStorage.getUserByEmail.mockResolvedValue(null)
      mockStorage.createUser.mockRejectedValue(new Error('Database error'))

      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({
        message: "Server error during registration"
      })
    })
  })

  describe('POST /api/auth/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'password123!'
    }

    test('should login user with valid credentials', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'user',
        password: 'hashed-password'
      }

      mockStorage.getUserByEmail.mockResolvedValue(mockUser)
      ;(bcrypt.compare as any).mockResolvedValue(true)
      ;(jwt.sign as any).mockReturnValue('mock-jwt-token')

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData)

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('user')
      expect(response.body).toHaveProperty('token')
      expect(response.body.user).toEqual({
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'user'
      })
      expect(response.body.token).toBe('mock-jwt-token')

      expect(mockStorage.getUserByEmail).toHaveBeenCalledWith('<EMAIL>')
      expect(bcrypt.compare).toHaveBeenCalledWith('password123!', 'hashed-password')
    })

    test('should reject login with non-existent user', async () => {
      mockStorage.getUserByEmail.mockResolvedValue(null)

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData)

      expect(response.status).toBe(401)
      expect(response.body).toEqual({
        message: "Invalid email or password"
      })
      expect(bcrypt.compare).not.toHaveBeenCalled()
    })

    test('should reject login with invalid password', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        password: 'hashed-password'
      }

      mockStorage.getUserByEmail.mockResolvedValue(mockUser)
      ;(bcrypt.compare as any).mockResolvedValue(false)

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData)

      expect(response.status).toBe(401)
      expect(response.body).toEqual({
        message: "Invalid email or password"
      })
      expect(bcrypt.compare).toHaveBeenCalledWith('password123!', 'hashed-password')
    })

    test('should reject login with missing credentials', async () => {
      const incompleteData = {
        email: '<EMAIL>'
        // Missing password
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(incompleteData)

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('message', 'Validation error')
      expect(mockStorage.getUserByEmail).not.toHaveBeenCalled()
    })

    test('should handle database errors during login', async () => {
      mockStorage.getUserByEmail.mockRejectedValue(new Error('Database error'))

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({
        message: "Server error during login"
      })
    })
  })
})