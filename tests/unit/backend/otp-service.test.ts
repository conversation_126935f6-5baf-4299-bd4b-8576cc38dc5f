import { describe, test, expect, beforeEach, vi } from 'vitest'

// Mock events module first
vi.mock('events', () => {
  class MockEventEmitter {
    private listeners: Map<string, Function[]> = new Map();
    
    on(event: string, listener: Function) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, []);
      }
      this.listeners.get(event)!.push(listener);
    }
    
    emit(event: string, ...args: any[]) {
      const eventListeners = this.listeners.get(event) || [];
      eventListeners.forEach(listener => listener(...args));
    }
    
    removeAllListeners() {
      this.listeners.clear();
    }
    
    listenerCount(event: string) {
      return this.listeners.get(event)?.length || 0;
    }
  }

  return {
    default: { EventEmitter: MockEventEmitter },
    EventEmitter: MockEventEmitter
  };
});

import { SMSOTPProvider } from '../../../server/services/SMSOTPProvider'

// Mock the config module
vi.mock('../../../server/config', () => ({
  config: {
    isDevelopment: vi.fn().mockReturnValue(true),
    twilio: { available: false, accountSid: '', authToken: '' },
    sendgrid: { available: false },
    app: { nodeEnv: 'test' }
  }
}))

vi.mock('twilio', () => ({
  default: vi.fn()
}))

describe('OTPService Core Features', () => {
  let smsProvider: SMSOTPProvider
  let mockConfig: any
  
  beforeEach(async () => {
    vi.clearAllMocks()
    
    // Get the mocked config
    const configModule = await import('../../../server/config')
    mockConfig = configModule.config as any
    
    smsProvider = new SMSOTPProvider()
  })

  describe('Test Phone Numbers Recognition', () => {
    test('recognizes test phone numbers', () => {
      const testNumbers = [
        '+************',
        '+************'
      ]

      testNumbers.forEach(number => {
        const isTest = smsProvider.isTestPhoneNumber(number)
        expect(isTest).toBe(true)
      })
    })

    test('does not recognize regular phone numbers as test numbers', () => {
      const regularNumbers = [
        '+************',
        '+91**********',
        '+447700900123',
        '+**********1'
      ]

      regularNumbers.forEach(number => {
        const isTest = smsProvider.isTestPhoneNumber(number)
        expect(isTest).toBe(false)
      })
    })

    test('handles invalid phone number formats', () => {
      const invalidNumbers = [
        '************', // Missing +
        '+91999999999', // Too short
        'invalid-phone',
        '',
        null,
        undefined
      ]

      invalidNumbers.forEach(number => {
        const isTest = smsProvider.isTestPhoneNumber(number as string)
        expect(isTest).toBe(false)
      })
    })
  })

  describe('Master Code Logic', () => {
    test('recognizes master codes in development', () => {
      mockConfig.isDevelopment.mockReturnValue(true)

      const masterCodes = ['123456', '999999']
      
      masterCodes.forEach(code => {
        const isMaster = smsProvider.isMasterCode(code)
        expect(isMaster).toBe(true)
      })
    })

    test('does not recognize master codes in production', () => {
      mockConfig.isDevelopment.mockReturnValue(false)

      const masterCodes = ['123456', '999999']
      
      masterCodes.forEach(code => {
        const isMaster = smsProvider.isMasterCode(code)
        expect(isMaster).toBe(false)
      })
    })

    test('does not recognize non-master codes', () => {
      mockConfig.isDevelopment.mockReturnValue(true)

      const regularCodes = ['111111', '555555', '777777']
      
      regularCodes.forEach(code => {
        const isMaster = smsProvider.isMasterCode(code)
        expect(isMaster).toBe(false)
      })
    })
  })

  describe('OTP Generation', () => {
    test('generates 6-digit OTP for regular users', () => {
      const otp = smsProvider.generateOTP()
      
      expect(otp).toMatch(/^\d{6}$/)
      expect(parseInt(otp)).toBeGreaterThanOrEqual(100000)
      expect(parseInt(otp)).toBeLessThanOrEqual(999999)
    })

    test('generates different OTPs on multiple calls', () => {
      const otps = new Set()
      
      // Generate 100 OTPs and check they're different
      for (let i = 0; i < 100; i++) {
        otps.add(smsProvider.generateOTP())
      }
      
      // Should have generated mostly unique values
      expect(otps.size).toBeGreaterThan(90) // Allow some duplicates due to randomness
    })
  })

  describe('Phone Number Validation', () => {
    test('validates correct Indian phone numbers', () => {
      const validNumbers = [
        '+91**********',
        '+************',
        '+91**********',
        '**********', // Without +91
        '**********'  // Without +91
      ]

      validNumbers.forEach(number => {
        const isValid = smsProvider.validateIdentifier(number)
        expect(isValid).toBe(true)
      })
    })

    test('rejects invalid phone numbers', () => {
      const invalidNumbers = [
        '+911234',     // Too short
        '+91**********1', // Too long
        '+************',  // Starts with 5 (invalid)
        '+************',  // Starts with 4 (invalid)
        'not-a-number',
        '',
        '+**********'     // US number
      ]

      invalidNumbers.forEach(number => {
        const isValid = smsProvider.validateIdentifier(number)
        expect(isValid).toBe(false)
      })
    })
  })

  describe('Phone Number Sanitization', () => {
    test('sanitizes phone numbers correctly', () => {
      const testCases = [
        { input: '**********', expected: '+91**********' },
        { input: '+91**********', expected: '+91**********' },
        { input: '91**********', expected: '+91**********' },
        { input: ' 98 76 54 32 10 ', expected: '+91**********' }
      ]

      testCases.forEach(({ input, expected }) => {
        const sanitized = smsProvider.sanitizeIdentifier(input)
        expect(sanitized).toBe(expected)
      })
    })
  })

  describe('Service Availability', () => {
    test('reports unavailable when not configured', () => {
      const isAvailable = smsProvider.isAvailable()
      expect(isAvailable).toBe(false)
    })
  })

  describe('SMS Sending - Test Mode', () => {
    test('handles test phone numbers without actual SMS', async () => {
      const result = await smsProvider.sendOTP('+************', '123456') // Use valid test number
      
      expect(result.success).toBe(true)
      expect(result.message).toContain('test mode')
    })

    test('handles development fallback when service unavailable', async () => {
      mockConfig.isDevelopment.mockReturnValue(true)
      
      const result = await smsProvider.sendOTP('+************', '123456')
      
      expect(result.success).toBe(true)
      expect(result.message).toContain('OTP sent successfully')
    })

    test('logs OTP code in development mode but does not expose in response', async () => {
      mockConfig.isDevelopment.mockReturnValue(true)
      
      const result = await smsProvider.sendOTP('+************', '123456')
      
      expect(result.success).toBe(true)
      expect(result.message).toContain('OTP sent successfully')
      // OTP code should NOT be in response for security reasons
      expect(result.code).toBeUndefined()
      expect(result.otp).toBeUndefined()
    })

    test('does not include OTP code in production responses', async () => {
      mockConfig.isDevelopment.mockReturnValue(false)
      
      const result = await smsProvider.sendOTP('+************', '123456') // Use valid test number
      
      expect(result.success).toBe(true)
      expect(result.code).toBeUndefined()
    })
    
    test('rejects invalid phone number format', async () => {
      const result = await smsProvider.sendOTP('invalid-phone', '123456')
      
      expect(result.success).toBe(false)
      expect(result.message).toContain('Invalid phone number format')
    })
  })
})