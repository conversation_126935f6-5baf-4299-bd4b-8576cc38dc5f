import { describe, it, expect, vi, beforeEach, afterEach, MockedFunction } from 'vitest';
import crypto from 'crypto';

// Mock external dependencies
vi.mock('../../../server/db', () => {
  const mockChain = {
    insert: vi.fn(),
    values: vi.fn(),
    select: vi.fn(),
    from: vi.fn(),
    where: vi.fn(),
    update: vi.fn(),
    set: vi.fn(),
    orderBy: vi.fn(),
    limit: vi.fn()
  };
  
  // Set up complete chain returns - every method should return the chain
  mockChain.insert.mockReturnValue(mockChain);
  mockChain.select.mockReturnValue(mockChain);
  mockChain.from.mockReturnValue(mockChain);
  mockChain.where.mockReturnValue(mockChain);
  mockChain.update.mockReturnValue(mockChain);
  mockChain.set.mockReturnValue(mockChain);
  mockChain.orderBy.mockReturnValue(mockChain);
  mockChain.limit.mockReturnValue(mockChain);
  
  // Return successful results by default
  mockChain.values.mockResolvedValue({ insertId: 1 }); // Simulate successful insert
  mockChain.set.mockResolvedValue({ affectedRows: 1 }); // Simulate successful update
  mockChain.limit.mockResolvedValue([]);
  
  return { db: mockChain };
});

// Mock drizzle-orm functions partially
vi.mock('drizzle-orm', async (importOriginal) => {
  const actual = await importOriginal() as any;
  return {
    ...actual,
    eq: vi.fn(() => 'mock_eq_condition'),
    and: vi.fn(() => 'mock_and_condition'),
    lt: vi.fn(() => 'mock_lt_condition')
  };
});

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

vi.stubGlobal('console', mockConsole);

// Import after mocking
import { FieldEncryptionService } from '../../../server/services/FieldEncryptionService';
import { db } from '../../../server/db';

describe('FieldEncryptionService', () => {
  let service: FieldEncryptionService;
  let mockDb: any;
  let originalEnv: string | undefined;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks(); // Add this for complete reset
    
    // Reset singleton instance
    (FieldEncryptionService as any).instance = null;
    
    mockDb = db as any;
    
    // Reset all chain methods to return mockDb for chaining
    mockDb.insert.mockReturnValue(mockDb);
    mockDb.select.mockReturnValue(mockDb);
    mockDb.from.mockReturnValue(mockDb);
    mockDb.update.mockReturnValue(mockDb);
    mockDb.orderBy.mockReturnValue(mockDb);
    
    // Reset mock implementations to successful defaults
    // where needs to be both thenable (for await) and chainable (has methods)
    const createChainablePromise = (resolveValue) => {
      const promise = Promise.resolve(resolveValue);
      // Copy all mockDb methods to the promise so it can be chained further
      Object.setPrototypeOf(promise, mockDb);
      return promise;
    };
    
    mockDb.where.mockImplementation(() => createChainablePromise([]));
    mockDb.values.mockResolvedValue({ insertId: 1 });
    mockDb.set.mockResolvedValue({ affectedRows: 1 });
    mockDb.limit.mockResolvedValue([]);
    
    // Re-establish crypto mocks after reset
    let randomCounter = 0;
    vi.spyOn(crypto, 'randomBytes').mockImplementation((size) => {
      const buffer = Buffer.alloc(size);
      for (let i = 0; i < size; i++) {
        buffer[i] = (randomCounter + i) % 256;
      }
      randomCounter++;
      return buffer;
    });
    
    vi.spyOn(crypto, 'createCipheriv').mockImplementation(() => ({
      update: vi.fn().mockReturnValue('a1b2c3d4e5f6'),
      final: vi.fn().mockReturnValue('789012'),
      getAuthTag: vi.fn().mockReturnValue(Buffer.alloc(16, 0xab)),
      setAAD: vi.fn()
    } as any));
    
    vi.spyOn(crypto, 'createDecipheriv').mockImplementation(() => ({
      update: vi.fn().mockReturnValue(Buffer.from('decryptedpart1')),
      final: vi.fn().mockReturnValue(Buffer.from('part2')),
      setAuthTag: vi.fn(),
      setAAD: vi.fn()
    } as any));
    
    // Set up environment
    originalEnv = process.env.ENCRYPTION_MASTER_KEY;
    // Use a proper 32-byte hex key for AES-256
    process.env.ENCRYPTION_MASTER_KEY = '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef';
    
    service = FieldEncryptionService.getInstance();
  });

  afterEach(() => {
    vi.resetAllMocks();
    
    // Restore environment
    if (originalEnv !== undefined) {
      process.env.ENCRYPTION_MASTER_KEY = originalEnv;
    } else {
      delete process.env.ENCRYPTION_MASTER_KEY;
    }
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = FieldEncryptionService.getInstance();
      const instance2 = FieldEncryptionService.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBe(service);
    });

    it('should warn when no master key is provided in environment', () => {
      delete process.env.ENCRYPTION_MASTER_KEY;
      (FieldEncryptionService as any).instance = null;
      
      FieldEncryptionService.getInstance();
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        'WARNING: Using generated master key. Set ENCRYPTION_MASTER_KEY in production.'
      );
    });
  });

  describe('Key Generation and Management', () => {
    beforeEach(() => {
      mockDb.where.mockResolvedValue([]);
      mockDb.values.mockResolvedValue([{ id: 1 }]);
      mockDb.set.mockResolvedValue([{ id: 1 }]);
    });

    describe('generateKeyId', () => {
      it('should generate unique key IDs with purpose', () => {
        const keyId1 = (service as any).generateKeyId('payment');
        const keyId2 = (service as any).generateKeyId('payment');
        
        expect(keyId1).toMatch(/^payment_\d+_[a-f0-9]{8}$/);
        expect(keyId2).toMatch(/^payment_\d+_[a-f0-9]{8}$/);
        expect(keyId1).not.toBe(keyId2);
      });
    });

    describe('createEncryptionKey', () => {
      it('should create new encryption key successfully', async () => {
        // Ensure database operations resolve successfully
        mockDb.values.mockResolvedValueOnce({ insertId: 1 });
        
        // Global crypto mocks will handle the encryption
        
        const keyId = await service.createEncryptionKey('payment', 'aes256');
        
        expect(keyId).toMatch(/^payment_\d+_[a-f0-9]{8}$/);
        expect(mockDb.insert).toHaveBeenCalled();
        expect(mockDb.values).toHaveBeenCalledWith(
          expect.objectContaining({
            keyId: expect.stringMatching(/^payment_\d+_[a-f0-9]{8}$/),
            keyType: 'aes256',
            encryptedKey: expect.any(String),
            keyVersion: 1,
            isActive: true,
            expiresAt: expect.any(Date)
          })
        );
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          `Created new encryption key: ${keyId}`
        );
      });

      it('should handle database errors during key creation', async () => {
        mockDb.values.mockRejectedValue(new Error('Database connection failed'));
        
        await expect(
          service.createEncryptionKey('payment')
        ).rejects.toThrow('Key creation failed');
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to create encryption key:',
          expect.any(Error)
        );
      });

      it('should set expiration date to 1 year from creation', async () => {
        const beforeCreation = Date.now();
        
        // Ensure database operations resolve successfully
        mockDb.values.mockResolvedValue({ insertId: 1 });
        
        await service.createEncryptionKey('test');
        
        const callArgs = mockDb.values.mock.calls[0][0];
        const expiresAt = callArgs.expiresAt.getTime();
        const expectedExpiry = beforeCreation + 365 * 24 * 60 * 60 * 1000;
        
        // Allow 1 second tolerance for test execution time
        expect(expiresAt).toBeCloseTo(expectedExpiry, -3);
      });
    });

    describe('getOrCreateEncryptionKey', () => {
      it('should return existing active key', async () => {
        const mockKey = {
          id: 1,
          keyId: 'payment_123_abc',
          keyType: 'aes256',
          isActive: true,
          expiresAt: new Date(Date.now() + 1000000),
          encryptedKey: 'encrypted_key_data',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        mockDb.where.mockResolvedValue([mockKey]);
        
        const keyId = await service.getOrCreateEncryptionKey('payment');
        
        expect(keyId).toMatch(/^payment_\d+_[a-f0-9]{8}$/);
        expect(mockDb.insert).not.toHaveBeenCalled(); // Should not create new key
      });

      it('should create new key when none exists', async () => {
        mockDb.where.mockResolvedValue([]); // No existing key
        
        const keyId = await service.getOrCreateEncryptionKey('payment');
        
        expect(keyId).toMatch(/^payment_\d+_[a-f0-9]{8}$/);
        expect(mockDb.insert).toHaveBeenCalled(); // Should create new key
      });

      it('should create new key when existing key is inactive', async () => {
        const mockInactiveKey = {
          id: 1,
          keyId: 'payment_123_abc',
          keyType: 'aes256',
          isActive: false,
          expiresAt: new Date(Date.now() + 1000000),
          encryptedKey: 'encrypted_key_data',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        mockDb.where.mockResolvedValue([mockInactiveKey]);
        
        const keyId = await service.getOrCreateEncryptionKey('payment');
        
        expect(mockDb.insert).toHaveBeenCalled(); // Should create new key
      });
    });

    describe('getEncryptionKey', () => {
      it('should return cached key when valid', async () => {
        const mockKey = {
          id: 1,
          keyId: 'payment_123_abc',
          keyType: 'aes256',
          isActive: true,
          expiresAt: new Date(Date.now() + 1000000),
          encryptedKey: 'encrypted_key_data',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        // Add to cache
        (service as any).keyCache.set('payment_123_abc', mockKey);
        
        const result = await service.getEncryptionKey('payment_123_abc');
        
        expect(result).toEqual(mockKey);
        expect(mockDb.select).not.toHaveBeenCalled(); // Should use cache
      });

      it('should query database when key not in cache', async () => {
        const mockKey = {
          id: 1,
          keyId: 'payment_456_def',
          keyType: 'aes256',
          isActive: true,
          expiresAt: new Date(Date.now() + 1000000),
          encryptedKey: 'iv_hex_data:encrypted_key_data:auth_tag',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        // Mock the specific query chain for this test
        mockDb.where.mockResolvedValueOnce([mockKey]);
        
        // Clear cache to force database query
        (service as any).keyCache.clear();
        
        const result = await service.getEncryptionKey('payment_456_def');
        
        expect(result).toEqual(mockKey);
        expect(mockDb.select).toHaveBeenCalled();
        
        // Should update cache
        expect((service as any).keyCache.get('payment_456_def')).toEqual(mockKey);
      });

      it('should return null when key not found', async () => {
        mockDb.where.mockResolvedValue([]);
        
        const result = await service.getEncryptionKey('nonexistent_key');
        
        expect(result).toBeNull();
      });

      it('should handle database errors gracefully', async () => {
        mockDb.where.mockRejectedValue(new Error('Database query failed'));
        
        const result = await service.getEncryptionKey('payment_123_abc');
        
        expect(result).toBeNull();
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to get encryption key:',
          expect.any(Error)
        );
      });

      it('should invalidate cached key if expired', async () => {
        const expiredKey = {
          id: 1,
          keyId: 'payment_expired_key',
          keyType: 'aes256',
          isActive: true,
          expiresAt: new Date(Date.now() - 1000), // Expired
          encryptedKey: 'encrypted_key_data',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        // Add expired key to cache
        (service as any).keyCache.set('payment_expired_key', expiredKey);
        
        // Mock database returning no results for expired key
        mockDb.where.mockResolvedValue([]);
        
        const result = await service.getEncryptionKey('payment_expired_key');
        
        expect(result).toBeNull();
        expect(mockDb.select).toHaveBeenCalled(); // Should query database
      });
    });

    describe('rotateEncryptionKey', () => {
      it('should deactivate old key and create new one', async () => {
        const oldKeyId = 'payment_123_abc';
        
        // Mock key in cache
        (service as any).keyCache.set(oldKeyId, { id: 1, isActive: true });
        
        // Mock the update chain
        mockDb.set.mockReturnValueOnce(mockDb);
        mockDb.where.mockResolvedValueOnce({ affectedRows: 1 });
        
        // Mock the insert chain for new key creation
        mockDb.values.mockResolvedValueOnce({ insertId: 2 });
        
        const newKeyId = await service.rotateEncryptionKey(oldKeyId);
        
        expect(mockDb.update).toHaveBeenCalled();
        expect(mockDb.set).toHaveBeenCalledWith({ isActive: false });
        expect(mockDb.insert).toHaveBeenCalled(); // Create new key
        expect(newKeyId).toMatch(/^payment_\d+_[a-f0-9]{8}$/);
        
        // Should remove old key from cache
        expect((service as any).keyCache.has(oldKeyId)).toBe(false);
      });

      it('should handle rotation errors', async () => {
        // Mock the chain - set returns mockDb, but where rejects
        mockDb.set.mockReturnValueOnce(mockDb);
        mockDb.where.mockRejectedValueOnce(new Error('Update failed'));
        
        await expect(
          service.rotateEncryptionKey('payment_123_abc')
        ).rejects.toThrow('Key rotation failed');
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to rotate encryption key:',
          expect.any(Error)
        );
      });
    });
  });

  describe('Encryption and Decryption', () => {
    let mockKey: any;

    beforeEach(() => {
      mockKey = {
        id: 1,
        keyId: 'payment_test_key',
        keyType: 'aes256',
        isActive: true,
        expiresAt: new Date(Date.now() + 1000000),
        encryptedKey: 'iv_hex_data:encrypted_dek_data:auth_tag',
        keyVersion: 1,
        createdAt: new Date()
      };
      
      // Mock generateKeyId to return predictable keyId that matches mockKey
      vi.spyOn(service as any, 'generateKeyId').mockReturnValue('payment_test_key');
      
      // Mock database responses
      mockDb.where.mockResolvedValue([mockKey]);
      
      // Mock crypto operations for consistent testing
      vi.spyOn(crypto, 'createCipheriv').mockImplementation(() => ({
        update: vi.fn().mockReturnValue('encrypted_part1'),
        final: vi.fn().mockReturnValue('encrypted_part2'),
        getAuthTag: vi.fn().mockReturnValue(Buffer.from('auth_tag_12345678', 'hex')),
        setAAD: vi.fn()
      } as any));
      
      vi.spyOn(crypto, 'createDecipheriv').mockImplementation(() => ({
        update: vi.fn().mockReturnValue(Buffer.from('decrypted_part1')),
        final: vi.fn().mockReturnValue(Buffer.from('decrypted_part2')),
        setAuthTag: vi.fn(),
        setAAD: vi.fn()
      } as any));
      
      // Use global randomBytes mock
    });

    describe('encryptSensitiveData', () => {
      it('should encrypt data successfully', async () => {
        const sensitiveData = 'credit_card_1234567890123456';
        
        try {
          const result = await service.encryptSensitiveData(sensitiveData, 'payment');
          
          expect(result).toEqual({
            encryptedValue: 'encrypted_part1encrypted_part2',
            keyId: 'payment_test_key',
            algorithm: 'aes-256-gcm',
            iv: expect.any(String),
            tag: expect.any(String)
          });
          
          expect(crypto.createCipheriv).toHaveBeenCalledWith('aes-256-gcm', expect.any(Buffer), expect.any(Buffer));
        } catch (error) {
          // Log what the actual error was for debugging
          console.log('Actual error:', error);
          console.log('Console.error calls:', mockConsole.error.mock.calls);
          throw error;
        }
      });

      it('should handle encryption errors', async () => {
        vi.mocked(crypto.createCipheriv).mockImplementation(() => {
          throw new Error('Cipher creation failed');
        });
        
        await expect(
          service.encryptSensitiveData('test_data', 'payment')
        ).rejects.toThrow('Encryption failed');
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to encrypt sensitive data:',
          expect.any(Error)
        );
      });

      it('should handle missing encryption key', async () => {
        mockDb.where.mockResolvedValue([]);
        mockDb.values.mockRejectedValue(new Error('Database insert failed'));
        
        await expect(
          service.encryptSensitiveData('test_data', 'payment')
        ).rejects.toThrow('Encryption failed');
      });
    });

    describe('decryptSensitiveData', () => {
      it('should decrypt data successfully', async () => {
        const encryptedData = {
          encryptedValue: 'encrypted_data_hex',
          keyId: 'payment_test_key',
          algorithm: 'aes-256-gcm',
          iv: 'iv_hex_data',
          tag: 'auth_tag_hex'
        };
        
        const result = await service.decryptSensitiveData(encryptedData);
        
        expect(result).toBe('decrypted_part1decrypted_part2');
        expect(crypto.createDecipheriv).toHaveBeenCalledWith('aes-256-gcm', expect.any(Buffer), expect.any(Buffer));
      });

      it('should handle decryption with missing tag', async () => {
        const encryptedDataWithoutTag = {
          encryptedValue: 'encrypted_data_hex',
          keyId: 'payment_test_key',
          algorithm: 'aes-256-gcm',
          iv: 'iv_hex_data'
        };
        
        const result = await service.decryptSensitiveData(encryptedDataWithoutTag);
        
        expect(result).toBe('decrypted_part1decrypted_part2');
        // Should not call setAuthTag when tag is missing
      });

      it('should handle inactive encryption key', async () => {
        const inactiveKey = { ...mockKey, isActive: false };
        mockDb.where.mockResolvedValue([inactiveKey]);
        
        const encryptedData = {
          encryptedValue: 'encrypted_data',
          keyId: 'inactive_key',
          algorithm: 'aes-256-gcm',
          iv: 'iv_data',
          tag: 'tag_data'
        };
        
        await expect(
          service.decryptSensitiveData(encryptedData)
        ).rejects.toThrow('Decryption failed');
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to decrypt sensitive data:',
          expect.any(Error)
        );
      });

      it('should handle decryption errors', async () => {
        vi.mocked(crypto.createDecipheriv).mockImplementation(() => {
          throw new Error('Decipher creation failed');
        });
        
        const encryptedData = {
          encryptedValue: 'encrypted_data',
          keyId: 'payment_test_key',
          algorithm: 'aes-256-gcm',
          iv: 'iv_data',
          tag: 'tag_data'
        };
        
        await expect(
          service.decryptSensitiveData(encryptedData)
        ).rejects.toThrow('Decryption failed');
      });
    });
  });

  describe('Hashing and Verification', () => {
    beforeEach(() => {
      // Mock pbkdf2Sync for consistent testing
      vi.spyOn(crypto, 'pbkdf2Sync').mockReturnValue(
        Buffer.from('mocked_hash_output_64_bytes_long_for_sha512_algorithm_testing_purpose', 'ascii')
      );
      
      // Use global randomBytes mock
    });

    describe('hashSensitiveData', () => {
      it('should hash data with generated salt', () => {
        const data = 'sensitive_password_123';
        
        const hashedData = service.hashSensitiveData(data);
        
        expect(hashedData).toMatch(/^[a-f0-9]{32}:[a-f0-9]+$/);
        expect(crypto.pbkdf2Sync).toHaveBeenCalledWith(
          data,
          expect.any(String),
          10000,
          64,
          'sha512'
        );
      });

      it('should hash data with provided salt', () => {
        const data = 'sensitive_password_123';
        const salt = 'predefined_salt_value';
        
        const hashedData = service.hashSensitiveData(data, salt);
        
        expect(hashedData).toMatch(/^predefined_salt_value:[a-f0-9]+$/);
        expect(crypto.pbkdf2Sync).toHaveBeenCalledWith(
          data,
          salt,
          10000,
          64,
          'sha512'
        );
      });
    });

    describe('verifySensitiveData', () => {
      it('should verify correct data', () => {
        const data = 'original_password';
        const mockHashBuffer = Buffer.from('mocked_hash_output_64_bytes_long_for_sha512_algorithm_testing_purpose', 'ascii');
        const hashedData = `salt_value:${mockHashBuffer.toString('hex')}`;
        
        const isValid = service.verifySensitiveData(data, hashedData);
        
        expect(isValid).toBe(true);
        expect(crypto.pbkdf2Sync).toHaveBeenCalledWith(
          data,
          'salt_value',
          10000,
          64,
          'sha512'
        );
      });

      it('should reject incorrect data', () => {
        // Set up different mock return value for verification
        vi.mocked(crypto.pbkdf2Sync).mockReturnValue(
          Buffer.from('different_hash_output', 'ascii')
        );
        
        const data = 'wrong_password';
        const hashedData = 'salt_value:original_hash_output';
        
        const isValid = service.verifySensitiveData(data, hashedData);
        
        expect(isValid).toBe(false);
      });

      it('should handle malformed hash gracefully', () => {
        const data = 'password';
        const malformedHash = 'invalid_hash_format';
        
        // Mock pbkdf2Sync to throw an error for malformed input
        vi.mocked(crypto.pbkdf2Sync).mockImplementationOnce(() => {
          throw new Error('Invalid hash format');
        });
        
        const isValid = service.verifySensitiveData(data, malformedHash);
        
        expect(isValid).toBe(false);
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to verify sensitive data:',
          expect.any(Error)
        );
      });

      it('should handle crypto errors gracefully', () => {
        vi.mocked(crypto.pbkdf2Sync).mockImplementation(() => {
          throw new Error('PBKDF2 failed');
        });
        
        const data = 'password';
        const hashedData = 'salt:hash';
        
        const isValid = service.verifySensitiveData(data, hashedData);
        
        expect(isValid).toBe(false);
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to verify sensitive data:',
          expect.any(Error)
        );
      });
    });
  });

  describe('Specialized Encryption Methods', () => {
    beforeEach(() => {
      // Mock the generic encryption methods
      vi.spyOn(service, 'encryptSensitiveData').mockResolvedValue({
        encryptedValue: 'mocked_encrypted_value',
        keyId: 'mocked_key_id',
        algorithm: 'aes-256-gcm',
        iv: 'mocked_iv',
        tag: 'mocked_tag'
      });
      
      vi.spyOn(service, 'decryptSensitiveData').mockResolvedValue('mocked_decrypted_value');
    });

    describe('Razorpay Signature Methods', () => {
      it('should encrypt Razorpay signature', async () => {
        const signature = 'razorpay_signature_abc123';
        
        const result = await service.encryptRazorpaySignature(signature);
        
        expect(service.encryptSensitiveData).toHaveBeenCalledWith(
          signature,
          'razorpay_signature'
        );
        expect(result.encryptedValue).toBe('mocked_encrypted_value');
      });

      it('should decrypt Razorpay signature', async () => {
        const encryptedData = {
          encryptedValue: 'encrypted_signature',
          keyId: 'razorpay_key_id',
          algorithm: 'aes-256-gcm',
          iv: 'iv_data',
          tag: 'tag_data'
        };
        
        const result = await service.decryptRazorpaySignature(encryptedData);
        
        expect(service.decryptSensitiveData).toHaveBeenCalledWith(encryptedData);
        expect(result).toBe('mocked_decrypted_value');
      });
    });

    describe('Payment Method Methods', () => {
      it('should encrypt payment method data', async () => {
        const paymentMethodData = {
          cardNumber: '****************',
          expiryDate: '12/25',
          cvv: '123'
        };
        
        const result = await service.encryptPaymentMethod(paymentMethodData);
        
        expect(service.encryptSensitiveData).toHaveBeenCalledWith(
          JSON.stringify(paymentMethodData),
          'payment_method'
        );
        expect(result.encryptedValue).toBe('mocked_encrypted_value');
      });

      it('should decrypt payment method data', async () => {
        const paymentMethodData = {
          cardNumber: '****************',
          expiryDate: '12/25'
        };
        
        vi.mocked(service.decryptSensitiveData).mockResolvedValue(
          JSON.stringify(paymentMethodData)
        );
        
        const encryptedData = {
          encryptedValue: 'encrypted_payment_method',
          keyId: 'payment_method_key',
          algorithm: 'aes-256-gcm',
          iv: 'iv_data',
          tag: 'tag_data'
        };
        
        const result = await service.decryptPaymentMethod(encryptedData);
        
        expect(service.decryptSensitiveData).toHaveBeenCalledWith(encryptedData);
        expect(result).toEqual(paymentMethodData);
      });

      it('should handle invalid JSON during payment method decryption', async () => {
        vi.mocked(service.decryptSensitiveData).mockResolvedValue('invalid_json{');
        
        const encryptedData = {
          encryptedValue: 'encrypted_invalid_json',
          keyId: 'payment_method_key',
          algorithm: 'aes-256-gcm',
          iv: 'iv_data',
          tag: 'tag_data'
        };
        
        await expect(
          service.decryptPaymentMethod(encryptedData)
        ).rejects.toThrow();
      });
    });
  });

  describe('Key Cleanup Operations', () => {
    describe('cleanupExpiredKeys', () => {
      it('should deactivate expired keys in database', async () => {
        // Mock the chain to return mockDb for chaining, then resolve at where()
        mockDb.set.mockReturnValueOnce(mockDb);
        mockDb.where.mockResolvedValueOnce({ affectedRows: 1 });
        
        await service.cleanupExpiredKeys();
        
        expect(mockDb.update).toHaveBeenCalled();
        expect(mockDb.set).toHaveBeenCalledWith({ isActive: false });
        expect(mockConsole.log).toHaveBeenCalledWith('Cleaned up expired encryption keys');
      });

      it('should clear expired keys from cache', async () => {
        const expiredKey = {
          id: 1,
          keyId: 'expired_key',
          isActive: true,
          expiresAt: new Date(Date.now() - 1000), // Expired
          encryptedKey: 'encrypted_data',
          keyType: 'aes256',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        const validKey = {
          id: 2,
          keyId: 'valid_key',
          isActive: true,
          expiresAt: new Date(Date.now() + 1000000), // Not expired
          encryptedKey: 'encrypted_data',
          keyType: 'aes256',
          keyVersion: 1,
          createdAt: new Date()
        };
        
        // Add keys to cache
        (service as any).keyCache.set('expired_key', expiredKey);
        (service as any).keyCache.set('valid_key', validKey);
        
        // Mock the database update chain
        mockDb.set.mockReturnValueOnce(mockDb);
        mockDb.where.mockResolvedValueOnce({ affectedRows: 1 });
        
        await service.cleanupExpiredKeys();
        
        // Expired key should be removed from cache
        expect((service as any).keyCache.has('expired_key')).toBe(false);
        // Valid key should remain in cache
        expect((service as any).keyCache.has('valid_key')).toBe(true);
      });

      it('should handle cleanup errors gracefully', async () => {
        // Mock the chain - set returns mockDb, but where rejects
        mockDb.set.mockReturnValueOnce(mockDb);
        mockDb.where.mockRejectedValueOnce(new Error('Database update failed'));
        
        await service.cleanupExpiredKeys();
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          'Failed to cleanup expired keys:',
          expect.any(Error)
        );
      });
    });
  });

  describe('Master Key Operations', () => {
    describe('encryptKey and decryptKey', () => {
      it('should encrypt and decrypt keys with master key', () => {
        const originalKey = Buffer.from('test_data_encryption_key_32_bytes');
        
        // Mock cipher operations
        const mockCipher = {
          update: vi.fn().mockReturnValue('encrypted_key_data'),
          final: vi.fn().mockReturnValue(''),
          getAuthTag: vi.fn().mockReturnValue(Buffer.from('617574685f7461675f3132333435363738', 'hex'))
        };
        
        const mockDecipher = {
          update: vi.fn().mockReturnValue(originalKey.subarray(0, 16)),
          final: vi.fn().mockReturnValue(originalKey.subarray(16)),
          setAuthTag: vi.fn()
        };
        
        vi.mocked(crypto.createCipheriv).mockReturnValue(mockCipher as any);
        vi.mocked(crypto.createDecipheriv).mockReturnValue(mockDecipher as any);
        
        // Test encryption
        const encryptedKey = (service as any).encryptKey(originalKey);
        // With IV (16 bytes = 32 hex chars), encrypted data, and tag
        expect(encryptedKey).toBe('000102030405060708090a0b0c0d0e0f:encrypted_key_data:617574685f7461675f3132333435363738');
        
        // Test decryption
        const decryptedKey = (service as any).decryptKey(encryptedKey);
        expect(decryptedKey).toEqual(originalKey);
        
        expect(mockDecipher.setAuthTag).toHaveBeenCalledWith(
          Buffer.from('617574685f7461675f3132333435363738', 'hex')
        );
      });
    });

    describe('generateMasterKey', () => {
      it('should generate master key of correct length', () => {
        // Mock crypto.randomBytes to return a predictable result
        vi.spyOn(crypto, 'randomBytes').mockReturnValue(
          Buffer.from('0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef', 'hex')
        );
        
        const masterKey = (service as any).generateMasterKey();
        
        expect(masterKey).toMatch(/^[a-f0-9]{64}$/); // 32 bytes = 64 hex chars
        expect(masterKey).toBe('0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef');
      });
    });
  });

  describe('Error Edge Cases', () => {
    it('should handle crypto module failures gracefully', async () => {
      vi.spyOn(crypto, 'randomBytes').mockImplementation(() => {
        throw new Error('System entropy unavailable');
      });
      
      await expect(
        service.encryptSensitiveData('test_data', 'payment')
      ).rejects.toThrow('Encryption failed');
    });

    it('should handle corrupted key data', async () => {
      const corruptedKey = {
        ...mockDb.where.mockResolvedValue([{
          id: 1,
          keyId: 'corrupted_key',
          isActive: true,
          expiresAt: new Date(Date.now() + 1000000),
          encryptedKey: 'corrupted_data_without_proper_format',
          keyType: 'aes256',
          keyVersion: 1,
          createdAt: new Date()
        }])
      };
      
      await expect(
        service.encryptSensitiveData('test_data', 'payment')
      ).rejects.toThrow('Encryption failed');
    });

    it('should handle cache corruption gracefully', async () => {
      // Corrupt the cache with invalid data
      (service as any).keyCache.set('test_key', null);
      
      mockDb.where.mockResolvedValue([]);
      
      const result = await service.getEncryptionKey('test_key');
      
      expect(result).toBeNull();
      expect(mockDb.select).toHaveBeenCalled();
    });
  });

  describe('Configuration Validation', () => {
    it('should use correct encryption algorithm configuration', () => {
      const config = (service as any).config;
      
      expect(config.algorithm).toBe('aes-256-gcm');
      expect(config.keyLength).toBe(32); // 256 bits
      expect(config.ivLength).toBe(16);  // 128 bits
    });
  });
});