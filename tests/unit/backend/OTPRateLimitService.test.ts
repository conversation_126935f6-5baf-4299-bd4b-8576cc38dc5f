import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the cache service using factory function
vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    keys: vi.fn()
  }
}));

vi.mock('../../../server/utils/logger', () => ({
  log: vi.fn()
}));

// Import after mocking
import { OTPRateLimitService } from '../../../server/services/OTPRateLimitService';
import { cacheService } from '../../../server/services/CacheService';

describe('OTPRateLimitService', () => {
  let otpRateLimitService: OTPRateLimitService;
  const testIdentifier = '<EMAIL>';

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(cacheService.get).mockReset();
    vi.mocked(cacheService.set).mockReset();
    vi.mocked(cacheService.delete).mockReset();
    vi.mocked(cacheService.keys).mockReset();
    otpRateLimitService = new OTPRateLimitService();
    
    // Mock Date.now() for consistent testing
    vi.useFakeTimers();
    // Don't set a default system time here - let individual tests set their own
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('canRequestOTP', () => {
    it('should allow first OTP request', async () => {
      const now = 1000000;
      vi.setSystemTime(now);
      vi.mocked(cacheService.get).mockResolvedValue(null);

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(3);
      expect(result.reason).toBeUndefined();
      expect(result.waitTime).toBeUndefined();
    });

    it('should reject when daily limit exceeded', async () => {
      const now = 1000000;
      vi.setSystemTime(now);
      vi.mocked(cacheService).get
        .mockResolvedValueOnce(100) // Daily count at limit
        .mockResolvedValueOnce(null); // No attempts data

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Daily OTP limit exceeded');
      expect(result.waitTime).toBeGreaterThan(0);
    });

    it('should respect blocking when user is temporarily blocked', async () => {
      const now = 1000000; // Set a fixed time
      vi.setSystemTime(now);
      const blockUntil = now + 10000; // Blocked for 10 seconds

      // The service checks daily limit first, then attempts data
      vi.mocked(cacheService.get)
        .mockResolvedValueOnce(50) // Daily count (under limit)
        .mockResolvedValueOnce({   // Attempts data - blocked
          count: 3,
          lastAttempt: now - 1000,
          blocked: true,
          blockUntil
        });

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Temporarily blocked due to too many attempts');
      expect(result.waitTime).toBe(10);
    });

    it('should enforce cooldown period between requests', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const lastSent = now - 30000; // 30 seconds ago (less than 60 second cooldown)

      // Mock the cache calls in the correct order:
      // 1. Daily limit check, 2. Attempts check, 3. Last sent check
      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50)       // Daily count (under limit of 100)
        .mockResolvedValueOnce(null)     // No attempts data (not blocked)
        .mockResolvedValueOnce(lastSent); // Last sent time (should trigger cooldown)

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Please wait before requesting another OTP');
      expect(result.waitTime).toBe(30); // 60 - 30 = 30 seconds left
    });

    it('should allow request when cooldown period has passed', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const lastSent = now - 70000; // 70 seconds ago (more than 60 second cooldown)

      // Mock the cache calls in order: daily, attempts, lastSent
      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50)       // Daily count (under limit)
        .mockResolvedValueOnce(null)     // No attempts data 
        .mockResolvedValueOnce(lastSent); // Last sent time (cooldown passed)

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(3); // Should be 3 when no attempts data
    });

    it('should block user when max attempts reached in window', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      
      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50) // Daily count (under limit)
        .mockResolvedValueOnce({   // Attempts data - at max attempts (3)
          count: 3, // At max attempts (default limit is 3)
          lastAttempt: now - 100000, // Within 5-minute window (300000ms)
          blocked: false
        })
        .mockResolvedValueOnce(null); // No recent OTP sent (passed cooldown)

      // Also mock the set call that will block the user
      vi.mocked(cacheService).set.mockResolvedValue(undefined);

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Too many OTP requests');
      expect(result.waitTime).toBe(900); // 15 minutes block duration (default)
      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:attempts'),
        expect.objectContaining({
          blocked: true,
          blockUntil: expect.any(Number)
        }),
        900
      );
    });

    it('should reset attempts when window has expired', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const oldAttempt = now - 400000; // 6+ minutes ago (outside 5-minute window of 300000ms)

      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50) // Daily count (under limit)
        .mockResolvedValueOnce({   // Attempts data but outside window
          count: 3,
          lastAttempt: oldAttempt, // Outside 5-minute window, so should be reset
          blocked: false
        })
        .mockResolvedValueOnce(null); // No recent OTP sent

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(3); // Should be 3 because attempts are reset (outside window)
    });

    it('should calculate attempts left correctly', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);

      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50) // Daily count (under limit)
        .mockResolvedValueOnce({   // Attempts data with 1 attempt used
          count: 1, // 1 attempt used
          lastAttempt: now - 60000, // Within 5-minute window (300000ms)
          blocked: false
        })
        .mockResolvedValueOnce(null); // No recent OTP sent (passed cooldown)

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(2); // 3 (max) - 1 (used) = 2 attempts left
    });

    it('should fail open on cache errors', async () => {
      vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      // SECURITY: Service fails closed (denies request) on cache errors
      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Rate limiting service temporarily unavailable');
      expect(result.waitTime).toBe(60);
    });

    it('should respect custom rate limit configuration', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      
      const customConfig = {
        maxAttempts: 5,
        windowSeconds: 600,
        blockDurationSeconds: 1800,
        cooldownSeconds: 120
      };

      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50) // Daily count
        .mockResolvedValueOnce({
          count: 2,
          lastAttempt: now - 60000,
          blocked: false
        })
        .mockResolvedValueOnce(null); // No recent OTP sent

      const result = await otpRateLimitService.canRequestOTP(testIdentifier, customConfig);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(3); // 5 - 2 = 3 attempts left
    });
  });

  describe('recordOTPRequest', () => {
    it('should record first OTP request', async () => {
      vi.mocked(cacheService).get
        .mockResolvedValueOnce(null) // No existing attempts
        .mockResolvedValueOnce(5); // Current daily count

      await otpRateLimitService.recordOTPRequest(testIdentifier);

      // Should update attempts counter
      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:attempts'),
        {
          count: 1,
          lastAttempt: expect.any(Number),
          blocked: false
        },
        300 // 5 minutes
      );

      // Should record last sent time
      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:sent'),
        expect.any(Number),
        60 // 1 minute cooldown
      );

      // Should increment daily counter
      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:daily'),
        6, // 5 + 1
        86400 // 24 hours
      );
    });

    it('should increment existing attempts within window', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      vi.mocked(cacheService).get
        .mockResolvedValueOnce({
          count: 2,
          lastAttempt: now - 60000, // Within 5-minute window
          blocked: false
        })
        .mockResolvedValueOnce(10); // Current daily count

      await otpRateLimitService.recordOTPRequest(testIdentifier);

      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:attempts'),
        {
          count: 3, // 2 + 1
          lastAttempt: expect.any(Number),
          blocked: false
        },
        300
      );
    });

    it('should reset attempts when window has expired', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      vi.mocked(cacheService).get
        .mockResolvedValueOnce({
          count: 3,
          lastAttempt: now - 400000, // Outside 5-minute window
          blocked: false
        })
        .mockResolvedValueOnce(10); // Current daily count

      await otpRateLimitService.recordOTPRequest(testIdentifier);

      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:attempts'),
        {
          count: 1, // Reset to 1
          lastAttempt: expect.any(Number),
          blocked: false
        },
        300
      );
    });

    it('should handle cache errors gracefully', async () => {
      vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

      await expect(otpRateLimitService.recordOTPRequest(testIdentifier)).resolves.not.toThrow();
    });
  });

  describe('canVerifyOTP', () => {
    it('should allow first verification attempt', async () => {
      vi.mocked(cacheService).get.mockResolvedValue(null);

      const result = await otpRateLimitService.canVerifyOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(5);
    });

    it('should block when verification attempts exceeded', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const blockUntil = now + 5000;

      vi.mocked(cacheService).get.mockResolvedValue({
        count: 6,
        lastAttempt: now - 1000,
        blocked: true,
        blockUntil
      });

      const result = await otpRateLimitService.canVerifyOTP(testIdentifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Too many verification attempts');
      expect(result.waitTime).toBe(5);
    });

    it('should block user when max verification attempts reached', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);

      vi.mocked(cacheService).get.mockResolvedValue({
        count: 5, // At max attempts
        lastAttempt: now - 60000, // Within 5-minute window
        blocked: false
      });

      const result = await otpRateLimitService.canVerifyOTP(testIdentifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Too many verification attempts');
      expect(result.waitTime).toBe(600); // 10 minutes block

      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify'),
        expect.objectContaining({
          blocked: true,
          blockUntil: expect.any(Number)
        }),
        600
      );
    });

    it('should calculate verification attempts left correctly', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);

      vi.mocked(cacheService).get.mockResolvedValue({
        count: 2,
        lastAttempt: now - 60000, // Within window
        blocked: false
      });

      const result = await otpRateLimitService.canVerifyOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(3); // 5 - 2 = 3
    });

    it('should reset verification attempts when window expired', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);

      vi.mocked(cacheService).get.mockResolvedValue({
        count: 5,
        lastAttempt: now - 400000, // Outside 5-minute window
        blocked: false
      });

      const result = await otpRateLimitService.canVerifyOTP(testIdentifier);

      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(5); // Reset to full attempts
    });

    it('should fail open on cache errors', async () => {
      vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

      const result = await otpRateLimitService.canVerifyOTP(testIdentifier);

      // SECURITY: Service fails closed (denies verification) on cache errors
      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Verification rate limiting service temporarily unavailable');
      expect(result.waitTime).toBe(60);
    });
  });

  describe('recordOTPVerificationAttempt', () => {
    it('should clear attempts on successful verification', async () => {
      await otpRateLimitService.recordOTPVerificationAttempt(testIdentifier, true);

      expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify')
      );
    });

    it('should increment failed verification attempts', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      vi.mocked(cacheService).get.mockResolvedValue({
        count: 2,
        lastAttempt: now - 60000, // Within window
        blocked: false
      });

      await otpRateLimitService.recordOTPVerificationAttempt(testIdentifier, false);

      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify'),
        {
          count: 3, // 2 + 1
          lastAttempt: expect.any(Number),
          blocked: false
        },
        300 // 5 minutes
      );
    });

    it('should reset verification attempts on new window', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      vi.mocked(cacheService).get.mockResolvedValue({
        count: 3,
        lastAttempt: now - 400000, // Outside window
        blocked: false
      });

      await otpRateLimitService.recordOTPVerificationAttempt(testIdentifier, false);

      expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify'),
        {
          count: 1, // Reset to 1
          lastAttempt: expect.any(Number),
          blocked: false
        },
        300
      );
    });

    it('should handle cache errors gracefully', async () => {
      vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

      await expect(
        otpRateLimitService.recordOTPVerificationAttempt(testIdentifier, false)
      ).resolves.not.toThrow();
    });
  });

  describe('clearRateLimits', () => {
    it('should clear all rate limit data for identifier', async () => {
      vi.mocked(cacheService).delete.mockResolvedValue(true);

      await otpRateLimitService.clearRateLimits(testIdentifier);

      expect(vi.mocked(cacheService).delete).toHaveBeenCalledTimes(3);
      expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith(
        expect.stringContaining('otp:attempts')
      );
      expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith(
        expect.stringContaining('otp:sent')
      );
      expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify')
      );
    });

    it('should handle cache errors gracefully', async () => {
      vi.mocked(cacheService).delete.mockRejectedValue(new Error('Cache error'));

      await expect(otpRateLimitService.clearRateLimits(testIdentifier)).resolves.not.toThrow();
    });
  });

  describe('getRateLimitStatus', () => {
    it('should return complete rate limit status', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const lastSent = now - 30000;

      vi.mocked(cacheService).get
        .mockResolvedValueOnce({
          count: 2,
          lastAttempt: now - 60000,
          blocked: false
        }) // OTP attempts
        .mockResolvedValueOnce({
          count: 1,
          lastAttempt: now - 60000,
          blocked: false
        }) // Verify attempts
        .mockResolvedValueOnce(25) // Daily count
        .mockResolvedValueOnce(lastSent); // Last sent

      const status = await otpRateLimitService.getRateLimitStatus(testIdentifier);

      expect(status).toEqual({
        otpRequests: {
          count: 2,
          maxAttempts: 3,
          blocked: false,
          waitTime: undefined
        },
        verifyAttempts: {
          count: 1,
          maxAttempts: 5,
          blocked: false,
          waitTime: undefined
        },
        dailyCount: 25,
        lastOTPSent: new Date(lastSent)
      });
    });

    it('should handle blocked status correctly', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const blockUntil = now + 5000;

      vi.mocked(cacheService).get
        .mockResolvedValueOnce({
          count: 3,
          lastAttempt: now - 1000,
          blocked: true,
          blockUntil
        }) // OTP attempts - blocked
        .mockResolvedValueOnce(null) // Verify attempts
        .mockResolvedValueOnce(null) // Daily count
        .mockResolvedValueOnce(null); // Last sent

      const status = await otpRateLimitService.getRateLimitStatus(testIdentifier);

      expect(status.otpRequests.blocked).toBe(true);
      expect(status.otpRequests.waitTime).toBe(5);
      expect(status.verifyAttempts.blocked).toBe(false);
    });

    it('should handle expired attempts correctly', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);

      vi.mocked(cacheService).get
        .mockResolvedValueOnce({
          count: 3,
          lastAttempt: now - 400000, // Outside window
          blocked: false
        }) // OTP attempts
        .mockResolvedValueOnce({
          count: 2,
          lastAttempt: now - 400000, // Outside window
          blocked: false
        }) // Verify attempts
        .mockResolvedValueOnce(null) // Daily count
        .mockResolvedValueOnce(null); // Last sent

      const status = await otpRateLimitService.getRateLimitStatus(testIdentifier);

      expect(status.otpRequests.count).toBe(0); // Reset due to expired window
      expect(status.verifyAttempts.count).toBe(0); // Reset due to expired window
    });

    it('should return default values on cache errors', async () => {
      vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

      const status = await otpRateLimitService.getRateLimitStatus(testIdentifier);

      // SECURITY: When status check fails, service indicates system is unavailable
      expect(status).toEqual({
        otpRequests: { count: 999, maxAttempts: 3, blocked: true, waitTime: 60 },
        verifyAttempts: { count: 999, maxAttempts: 5, blocked: true, waitTime: 60 },
        dailyCount: 999
      });
    });
  });

  describe('cleanup', () => {
    it('should clean up old daily entries', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const oldDate = new Date(now - 8 * 24 * 60 * 60 * 1000); // 8 days ago
      const recentDate = new Date(now - 2 * 24 * 60 * 60 * 1000); // 2 days ago

      const oldKey = `otp:daily:user1:${oldDate.toISOString().split('T')[0]}`;
      const recentKey = `otp:daily:user2:${recentDate.toISOString().split('T')[0]}`;

      vi.mocked(cacheService).keys.mockResolvedValue([
        oldKey,
        recentKey,
        'otp:attempts:user1',
        'otp:sent:user2'
      ]);

      vi.mocked(cacheService).delete.mockResolvedValue(true);

      await otpRateLimitService.cleanup();

      // Should only delete the old daily entry
      expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith(oldKey);
      expect(vi.mocked(cacheService).delete).not.toHaveBeenCalledWith(recentKey);
    });

    it('should handle cleanup errors gracefully', async () => {
      vi.mocked(cacheService).keys.mockRejectedValue(new Error('Cache error'));

      await expect(otpRateLimitService.cleanup()).resolves.not.toThrow();
    });

    it('should not delete recent daily entries', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const recentDate = new Date(now - 3 * 24 * 60 * 60 * 1000); // 3 days ago

      const recentKey = `otp:daily:user1:${recentDate.toISOString().split('T')[0]}`;

      vi.mocked(cacheService).keys.mockResolvedValue([recentKey]);

      await otpRateLimitService.cleanup();

      expect(vi.mocked(cacheService).delete).not.toHaveBeenCalled();
    });
  });

  describe('edge cases', () => {
    it('should handle malformed cache data', async () => {
      vi.mocked(cacheService).get.mockResolvedValue({ invalidData: true });

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(true);
    });

    it('should handle very large timestamps', async () => {
      const now = 1000000; // Use fixed time
      vi.setSystemTime(now);
      const futureTime = now + (365 * 24 * 60 * 60 * 1000); // 1 year from now

      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50) // Daily count
        .mockResolvedValueOnce({
          count: 1,
          lastAttempt: futureTime,
          blocked: false
        });

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      expect(result.allowed).toBe(true);
    });

    it('should handle missing blockUntil in blocked attempts', async () => {
      const now = 1000000; // Set a fixed time
      vi.setSystemTime(now);
      
      vi.mocked(cacheService).get
        .mockResolvedValueOnce(50) // Daily count (under limit)
        .mockResolvedValueOnce({   // Attempts data with blocked=true but no blockUntil
          count: 2, // Less than max (3) so won't trigger new blocking
          lastAttempt: now - 1000, // Recent attempt within window
          blocked: true
          // Missing blockUntil - blocking condition should fail
        })
        .mockResolvedValueOnce(null); // No last sent time (cooldown passed)

      const result = await otpRateLimitService.canRequestOTP(testIdentifier);

      // Should allow request when blockUntil is missing (blocking condition fails)
      expect(result.allowed).toBe(true);
      expect(result.attemptsLeft).toBe(1); // 3 (max) - 2 (current) = 1 left
    });
  });
});