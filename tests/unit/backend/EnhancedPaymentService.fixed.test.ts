import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock Razorpay constructor
vi.mock('razorpay', () => ({
  default: vi.fn(() => ({
    orders: {
      create: vi.fn()
    },
    payments: {
      capture: vi.fn(),
      refund: vi.fn()
    }
  }))
}));

// Simple db mock (moved inside mock factory)

vi.mock('../../../server/db', () => ({ 
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(), 
    where: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
    onConflictDoNothing: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockResolvedValue([])
  }
}));

// Mock other services
vi.mock('../../../server/services/AuditLogger', () => ({
  auditLogger: {
    logPaymentAction: vi.fn(),
    logPaymentOrderAction: vi.fn(),
    logPaymentTransactionAction: vi.fn(),
    logPaymentFraud: vi.fn()
  }
}));

vi.mock('../../../server/services/FieldEncryptionService', () => ({
  fieldEncryptionService: {
    encryptRazorpaySignature: vi.fn(),
    hashSensitiveData: vi.fn()
  }
}));

// Mock crypto
vi.mock('crypto', () => ({
  createHmac: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  digest: vi.fn(),
  timingSafeEqual: vi.fn(),
  randomBytes: vi.fn().mockReturnValue({ toString: vi.fn() })
}));

// Import after mocking
import { EnhancedPaymentService, PaymentStatus } from '../../../server/services/EnhancedPaymentService';
import { db } from '../../../server/db';
import Razorpay from 'razorpay';

describe('EnhancedPaymentService - Fixed Tests', () => {
  let service: EnhancedPaymentService;
  let mockDb: any;
  let mockRazorpay: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset singleton
    (EnhancedPaymentService as any).instance = null;
    
    // Get mocked instances
    mockDb = db as any;
    
    // Ensure chaining works properly
    mockDb.select.mockReturnValue(mockDb);
    mockDb.from.mockReturnValue(mockDb);
    mockDb.where.mockReturnValue(mockDb);
    mockDb.insert.mockReturnValue(mockDb);
    mockDb.update.mockReturnValue(mockDb);
    mockDb.set.mockReturnValue(mockDb);
    mockDb.values.mockReturnValue(mockDb);
    mockDb.returning.mockReturnValue(mockDb);
    mockDb.onConflictDoNothing.mockReturnValue(mockDb);
    mockDb.orderBy.mockReturnValue(mockDb);
    mockDb.limit.mockResolvedValue([]);
    
    mockRazorpay = {
      orders: {
        create: vi.fn()
      },
      payments: {
        capture: vi.fn(),
        refund: vi.fn()
      }
    };
    
    // Set environment variables
    process.env.RAZORPAY_KEY_ID = 'test_key_id';
    process.env.RAZORPAY_KEY_SECRET = 'test_key_secret';
    
    service = EnhancedPaymentService.getInstance();
    
    // Directly inject mock
    (service as any).razorpay = mockRazorpay;
  });

  afterEach(() => {
    delete process.env.RAZORPAY_KEY_ID;
    delete process.env.RAZORPAY_KEY_SECRET;
  });

  describe('createPaymentOrder', () => {
    it('should create a new payment order successfully', async () => {
      // Setup mocks
      mockRazorpay.orders.create.mockResolvedValue({
        id: 'order_test123',
        receipt: 'booking_1_123456789',
        amount: 10000,
        currency: 'INR'
      });

      mockDb.where.mockResolvedValueOnce([]); // No existing order
      mockDb.returning.mockResolvedValueOnce([{
        id: 1,
        razorpayOrderId: 'order_test123',
        bookingId: 1,
        amount: 10000,
        currency: 'INR',
        status: 'created'
      }]);

      const mockBookingDetails = {
        bookingId: 1,
        amount: 10000,
        currency: 'INR',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        customerPhone: '+919876543210',
        propertyDetails: { propertyId: 1 }
      };

      const result = await service.createPaymentOrder(mockBookingDetails, 'test-key');

      expect(mockRazorpay.orders.create).toHaveBeenCalledWith({
        amount: 10000,
        currency: 'INR',
        receipt: expect.stringContaining('booking_1_'),
        notes: {
          booking_id: '1',
          customer_name: 'John Doe',
          customer_email: '<EMAIL>',
          customer_phone: '+919876543210'
        }
      });

      expect(result.id).toBe(1);
      expect(result.razorpayOrderId).toBe('order_test123');
    });
  });

  describe('capturePayment', () => {
    it('should capture payment successfully', async () => {
      mockRazorpay.payments.capture.mockResolvedValue({
        id: 'pay_test123',
        amount: 10000,
        status: 'captured'
      });

      mockDb.where.mockResolvedValueOnce([]); // No existing capture
      mockDb.where.mockResolvedValueOnce(); // Update operation
      mockDb.where.mockResolvedValueOnce([{
        id: 1,
        razorpayPaymentId: 'pay_test123',
        status: PaymentStatus.CAPTURED
      }]);

      const result = await service.capturePayment('pay_test123', 10000, 'capture_key');

      expect(mockRazorpay.payments.capture).toHaveBeenCalledWith('pay_test123', 10000, 'INR');
      expect(result.status).toBe(PaymentStatus.CAPTURED);
    });
  });

  describe('processRefund', () => {
    it('should process refund successfully', async () => {
      mockRazorpay.payments.refund.mockResolvedValue({
        id: 'rfnd_test123',
        payment_id: 'pay_test123',
        amount: 5000,
        status: 'processed'
      });

      mockDb.where.mockResolvedValueOnce([]); // No existing refund

      const result = await service.processRefund('pay_test123', 5000, 'Customer cancellation', 'refund_key');

      expect(mockRazorpay.payments.refund).toHaveBeenCalledWith('pay_test123', {
        amount: 5000,
        notes: {
          reason: 'Customer cancellation',
          processed_at: expect.any(String)
        }
      });

      expect(result.id).toBe('rfnd_test123');
    });
  });
});