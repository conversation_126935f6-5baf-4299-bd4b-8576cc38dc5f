import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { Pool, PoolClient } from 'pg';

// Mock pg module using factory function
vi.mock('pg', () => ({
  default: {
    Pool: vi.fn(() => ({
      connect: vi.fn(),
      end: vi.fn(),
      on: vi.fn(),
      query: vi.fn(),
      totalCount: 5,
      idleCount: 3,
      waitingCount: 1
    }))
  }
}));

// Mock drizzle using factory function
vi.mock('drizzle-orm/node-postgres', () => ({
  drizzle: vi.fn(() => ({
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn()
  }))
}));

// Mock schema
vi.mock('../../../shared/schema', () => ({
  users: {},
  properties: {},
  bookings: {}
}));

// Import after mocking
import DatabaseManager from '../../../server/utils/database';
import pg from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';

describe('DatabaseManager', () => {
  let databaseManager: any;
  let mockPool: any;
  let mockPoolClient: any;
  
  // Get references to mocked functions
  const mockPg = vi.mocked(pg);
  const mockDrizzle = vi.mocked(drizzle);

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Setup mock client
    mockPoolClient = {
      query: vi.fn().mockResolvedValue({ rows: [{ health: 1 }] }),
      release: vi.fn().mockResolvedValue(undefined),
      on: vi.fn(),
      off: vi.fn()
    };

    // Setup mock pool with methods
    mockPool = {
      connect: vi.fn().mockResolvedValue(mockPoolClient),
      end: vi.fn().mockResolvedValue(undefined),
      on: vi.fn(),
      query: vi.fn(),
      totalCount: 5,
      idleCount: 3,
      waitingCount: 1
    };
    
    // Setup test connection to work properly
    mockPoolClient.query.mockImplementation((sql: string) => {
      if (sql.includes('NOW()')) {
        return Promise.resolve({ rows: [{ current_time: new Date() }] });
      }
      return Promise.resolve({ rows: [{ health: 1 }] });
    });

    // Mock pg.Pool constructor to return our mock
    vi.mocked(pg.Pool).mockImplementation(() => mockPool as any);

    // Create a new instance for each test
    databaseManager = new (DatabaseManager.constructor as any)();
    
    // Mock environment
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
    process.env.NODE_ENV = 'test';
  });

  afterEach(async () => {
    try {
      await databaseManager.shutdown();
    } catch (error) {
      // Ignore shutdown errors in tests
    }
    vi.useRealTimers();
    delete process.env.DATABASE_URL;
  });

  describe('constructor', () => {
    it('should initialize with default configuration', () => {
      expect(databaseManager).toBeDefined();
      
      const metrics = databaseManager.getMetrics();
      expect(metrics.totalConnections).toBe(0);
      expect(metrics.connectionErrors).toBe(0);
      expect(metrics.lastHealthCheck).toBeNull();
    });

    it('should respect environment configuration', () => {
      process.env.DB_MAX_CONNECTIONS = '10';
      process.env.NODE_ENV = 'production';
      
      const prodManager = new (DatabaseManager.constructor as any)();
      
      // Can't directly test private config, but can test it doesn't throw
      expect(prodManager).toBeDefined();
    });
  });

  describe('database URL validation', () => {
    it('should throw error when DATABASE_URL is not set', () => {
      delete process.env.DATABASE_URL;
      
      expect(() => {
        databaseManager.getDrizzleInstance();
      }).toThrow('DATABASE_URL must be set');
    });

    it('should use DATABASE_URL when provided', () => {
      process.env.DATABASE_URL = 'postgresql://custom:<EMAIL>:5432/db';
      
      databaseManager.getDrizzleInstance();
      
      expect(mockPg.Pool).toHaveBeenCalledWith(
        expect.objectContaining({
          connectionString: 'postgresql://custom:<EMAIL>:5432/db'
        })
      );
    });
  });

  describe('pool creation and management', () => {
    it('should create pool with correct configuration', () => {
      databaseManager.getDrizzleInstance();
      
      expect(mockPg.Pool).toHaveBeenCalledWith(
        expect.objectContaining({
          connectionString: expect.any(String),
          max: expect.any(Number),
          min: 1,
          idleTimeoutMillis: expect.any(Number),
          connectionTimeoutMillis: expect.any(Number),
          keepAlive: false,
          keepAliveInitialDelayMillis: 10000,
          allowExitOnIdle: false,
          ssl: expect.objectContaining({
            rejectUnauthorized: false
          }),
          application_name: 'farmhouse-app',
          statement_timeout: expect.any(Number),
          query_timeout: expect.any(Number),
          idle_in_transaction_session_timeout: 30000,
          log: expect.any(Function)
        })
      );
    });

    it('should set up pool event handlers', () => {
      databaseManager.getDrizzleInstance();
      
      expect(mockPool.on).toHaveBeenCalledWith('connect', expect.any(Function));
      expect(mockPool.on).toHaveBeenCalledWith('acquire', expect.any(Function));
      expect(mockPool.on).toHaveBeenCalledWith('release', expect.any(Function));
      expect(mockPool.on).toHaveBeenCalledWith('remove', expect.any(Function));
      expect(mockPool.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should reuse existing pool', () => {
      databaseManager.getDrizzleInstance();
      databaseManager.getDrizzleInstance();
      
      expect(mockPg.Pool).toHaveBeenCalledTimes(1);
    });
  });

  describe('health checks', () => {
    it('should perform health check successfully', async () => {
      databaseManager.getDrizzleInstance();
      
      // Trigger health check manually
      const healthCheckResult = await databaseManager.performHealthCheck();
      
      expect(healthCheckResult).toBe(true);
      expect(mockPool.connect).toHaveBeenCalled();
      expect(mockPoolClient.query).toHaveBeenCalledWith('SELECT 1 as health');
      expect(mockPoolClient.release).toHaveBeenCalled();
    });

    it('should handle health check failures', async () => {
      // Initialize the pool first to complete prewarming
      databaseManager.getDrizzleInstance();
      
      // Clear any previous mocks and set up the failure
      mockPool.connect.mockClear();
      mockPool.connect.mockRejectedValueOnce(new Error('Connection failed'));
      
      const healthCheckResult = await databaseManager.performHealthCheck();
      
      expect(healthCheckResult).toBe(false);
      
      const metrics = databaseManager.getMetrics();
      expect(metrics.connectionErrors).toBeGreaterThan(0);
      expect(metrics.lastError?.message).toBe('Connection failed');
    });

    it('should start health check interval', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      
      databaseManager.getDrizzleInstance();
      
      expect(setIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        expect.any(Number)
      );
    });

    it('should clear existing health check interval when creating new one', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      // Manually set an interval to simulate existing one
      (databaseManager as any).healthCheckInterval = setInterval(() => {}, 1000);
      
      // Call getDrizzleInstance which calls startHealthChecks
      databaseManager.getDrizzleInstance();
      
      // Should have cleared the existing interval
      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });

  describe('withConnection', () => {
    it('should execute operation with connection', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      
      const result = await databaseManager.withConnection(mockOperation, 'test operation');
      
      expect(result).toBe('success');
      expect(mockPool.connect).toHaveBeenCalled();
      expect(mockOperation).toHaveBeenCalledWith(mockPoolClient);
      expect(mockPoolClient.release).toHaveBeenCalled();
    });

    it.skip('should retry on connection errors', async () => {
      const connectionError = new Error('terminating connection due to administrator command');
      const mockOperation = vi.fn()
        .mockRejectedValueOnce(connectionError)
        .mockResolvedValueOnce('success after retry');
      
      const resultPromise = databaseManager.withConnection(mockOperation, 'retry test');
      
      // Advance timers to skip retry delays
      vi.advanceTimersByTime(5000);
      
      const result = await resultPromise;
      
      expect(result).toBe('success after retry');
      expect(mockOperation).toHaveBeenCalledTimes(2);
    }, 5000);

    it('should not retry on non-connection errors', async () => {
      const validationError = new Error('invalid input syntax');
      const mockOperation = vi.fn().mockRejectedValue(validationError);
      
      await expect(
        databaseManager.withConnection(mockOperation, 'validation test')
      ).rejects.toThrow('invalid input syntax');
      
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it.skip('should fail after max retries', async () => {
      const connectionError = new Error('Connection terminated');
      const mockOperation = vi.fn().mockRejectedValue(connectionError);
      
      const operationPromise = databaseManager.withConnection(mockOperation, 'max retry test').catch((error) => {
        // Expected error from max retries
        expect(error.message).toBe('Connection terminated');
        return error;
      });
      
      // Advance timers to skip all retry delays  
      vi.advanceTimersByTime(20000);
      
      const result = await operationPromise;
      expect(result).toBeInstanceOf(Error);
      expect(mockOperation).toHaveBeenCalledTimes(4); // Initial + 3 retries
    }, 5000);

    it('should reject when shutting down', async () => {
      databaseManager.isShuttingDown = true;
      const mockOperation = vi.fn();
      
      await expect(
        databaseManager.withConnection(mockOperation, 'shutdown test')
      ).rejects.toThrow('Database is shutting down');
      
      expect(mockOperation).not.toHaveBeenCalled();
    });

    it('should release client even if operation throws', async () => {
      const mockOperation = vi.fn().mockRejectedValue(new Error('Operation failed'));
      
      await expect(
        databaseManager.withConnection(mockOperation, 'error test')
      ).rejects.toThrow('Operation failed');
      
      expect(mockPoolClient.release).toHaveBeenCalled();
    });

    it('should handle client release errors gracefully', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const releaseError = new Error('Release failed');
      mockPoolClient.release.mockImplementation(() => {
        // Throw synchronously to simulate release error
        throw releaseError;
      });
      
      const result = await databaseManager.withConnection(mockOperation, 'release error test');
      
      expect(result).toBe('success');
      // Should not throw even if release fails
    });
  });

  describe('withTransaction', () => {
    it('should execute transaction successfully', async () => {
      const mockOperation = vi.fn().mockResolvedValue('transaction success');
      
      // Reset and setup the query mock for transaction
      mockPoolClient.query.mockReset();
      mockPoolClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // COMMIT
        .mockResolvedValue({ rows: [{ health: 1 }] }); // Default for other queries
      
      const result = await databaseManager.withTransaction(mockOperation, 'test transaction');
      
      expect(result).toBe('transaction success');
      expect(mockPoolClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockPoolClient.query).toHaveBeenCalledWith('COMMIT');
      expect(mockOperation).toHaveBeenCalledWith(mockPoolClient);
    });

    it('should rollback on operation failure', async () => {
      const mockOperation = vi.fn().mockRejectedValue(new Error('Transaction failed'));
      
      // Reset and setup the query mock for rollback
      mockPoolClient.query.mockReset();
      mockPoolClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // ROLLBACK
        .mockResolvedValue({ rows: [{ health: 1 }] }); // Default for other queries
      
      await expect(
        databaseManager.withTransaction(mockOperation, 'failing transaction')
      ).rejects.toThrow('Transaction failed');
      
      expect(mockPoolClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockPoolClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(mockPoolClient.query).not.toHaveBeenCalledWith('COMMIT');
    });

    it('should handle rollback errors gracefully', async () => {
      const mockOperation = vi.fn().mockRejectedValue(new Error('Transaction failed'));
      
      // Reset and setup the query mock for rollback error
      mockPoolClient.query.mockReset();
      mockPoolClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockRejectedValueOnce(new Error('Rollback failed')) // ROLLBACK
        .mockResolvedValue({ rows: [{ health: 1 }] }); // Default for other queries
      
      await expect(
        databaseManager.withTransaction(mockOperation, 'rollback error transaction')
      ).rejects.toThrow('Transaction failed'); // Should still throw original error
      
      expect(mockPoolClient.query).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('metrics and monitoring', () => {
    it('should track connection metrics', () => {
      const initialMetrics = databaseManager.getMetrics();
      
      expect(initialMetrics).toEqual(
        expect.objectContaining({
          totalConnections: 0,
          idleConnections: 0,
          activeConnections: 0,
          connectionErrors: 0,
          lastHealthCheck: null,
          lastError: null
        })
      );
    });

    it('should update metrics on connection events', () => {
      // Initialize the drizzle instance to set up event handlers
      databaseManager.getDrizzleInstance();
      
      // Manually trigger the metrics tracking for this test
      databaseManager.trackConnectionMetric('connect');
      databaseManager.trackConnectionMetric('error', new Error('Test error'));
      
      const metrics = databaseManager.getMetrics();
      expect(metrics.totalConnections).toBe(1);
      expect(metrics.connectionErrors).toBe(1);
      expect(metrics.lastError?.message).toBe('Test error');
    });

    it('should return pool statistics', () => {
      databaseManager.getDrizzleInstance();
      
      const stats = databaseManager.getPoolStats();
      
      expect(stats).toEqual({
        total: 5,
        idle: 3,
        waiting: 1
      });
    });

    it('should return empty stats when no pool', () => {
      const stats = databaseManager.getPoolStats();
      
      expect(stats).toEqual({
        total: 0,
        idle: 0,
        waiting: 0
      });
    });
  });

  describe('connection testing', () => {
    it('should test connection successfully', async () => {
      const result = await databaseManager.testConnection();
      
      expect(result).toBe(true);
      expect(mockPoolClient.query).toHaveBeenCalledWith('SELECT NOW() as current_time');
    });

    it('should handle connection test failures', async () => {
      // Initialize the pool first to complete prewarming
      databaseManager.getDrizzleInstance();
      
      // Clear any previous mocks and set up the failure
      mockPool.connect.mockClear();
      mockPool.connect.mockRejectedValueOnce(new Error('Connection test failed'));
      
      const result = await databaseManager.testConnection();
      
      expect(result).toBe(false);
    });
  });

  describe('shutdown', () => {
    it('should shutdown gracefully', async () => {
      databaseManager.getDrizzleInstance();
      
      await databaseManager.shutdown();
      
      expect(mockPool.end).toHaveBeenCalled();
      expect(databaseManager.isShuttingDown).toBe(true);
    });

    it('should clear health check interval on shutdown', async () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      databaseManager.getDrizzleInstance();
      await databaseManager.shutdown();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
    });

    it('should handle pool end errors gracefully', async () => {
      databaseManager.getDrizzleInstance();
      mockPool.end.mockRejectedValueOnce(new Error('Pool end failed'));
      
      await expect(databaseManager.shutdown()).resolves.not.toThrow();
    });

    it('should handle shutdown when no pool exists', async () => {
      await expect(databaseManager.shutdown()).resolves.not.toThrow();
    });
  });

  describe('drizzle integration', () => {
    it('should return drizzle instance', () => {
      const drizzleInstance = databaseManager.getDrizzleInstance();
      
      expect(drizzleInstance).toBeDefined();
      expect(mockDrizzle).toHaveBeenCalled();
    });

    it('should reuse drizzle instance', () => {
      const instance1 = databaseManager.getDrizzleInstance();
      const instance2 = databaseManager.getDrizzleInstance();
      
      expect(instance1).toBe(instance2);
    });

    it('should reset drizzle instance on shutdown', async () => {
      const instance1 = databaseManager.getDrizzleInstance();
      
      await databaseManager.shutdown();
      
      const instance2 = databaseManager.getDrizzleInstance();
      expect(instance2).not.toBe(instance1);
    });
  });

  describe('error handling', () => {
    it.skip('should identify connection errors correctly', async () => {
      const connectionErrors = [
        new Error('terminating connection due to administrator command'),
        new Error('Connection terminated'),
        new Error('connect ECONNREFUSED'),
        new Error('Test error with code') // Mock code property
      ];
      
      (connectionErrors[3] as any).code = '57P01';
      
      for (const error of connectionErrors) {
        const mockOperation = vi.fn().mockRejectedValue(error);
        
        try {
          const operationPromise = databaseManager.withConnection(mockOperation).catch((err) => {
            // Expected error from connection test
            expect(err).toBeInstanceOf(Error);
            return err;
          });
          
          // Advance timers to skip retry delays
          vi.advanceTimersByTime(15000);
          
          const result = await operationPromise;
          expect(result).toBeInstanceOf(Error);
          
          // Should attempt retries for connection errors
          expect(mockOperation).toHaveBeenCalledTimes(4); // Initial + 3 retries
          mockOperation.mockClear();
        } catch (err) {
          // Handle any unhandled rejections from mock errors
        }
      }
    }, 15000);

    it('should handle statement timeout correctly', async () => {
      databaseManager.getDrizzleInstance();
      
      // Simulate connect event
      const connectHandler = mockPool.on.mock.calls.find(call => call[0] === 'connect')?.[1];
      const mockClientWithTimeout = {
        ...mockPoolClient,
        query: vi.fn().mockResolvedValue({ rows: [] })
      };
      
      connectHandler?.(mockClientWithTimeout);
      
      expect(mockClientWithTimeout.query).toHaveBeenCalledWith(
        expect.stringContaining('SET statement_timeout')
      );
    });

    it('should handle statement timeout setting errors', async () => {
      databaseManager.getDrizzleInstance();
      
      const connectHandler = mockPool.on.mock.calls.find(call => call[0] === 'connect')?.[1];
      const mockClientWithFailingTimeout = {
        ...mockPoolClient,
        query: vi.fn().mockRejectedValue(new Error('Timeout setting failed'))
      };
      
      // Should not throw error when setting timeout fails
      expect(() => connectHandler?.(mockClientWithFailingTimeout)).not.toThrow();
    });
  });

  describe('environment-specific behavior', () => {
    it('should use different max connections in production', () => {
      process.env.NODE_ENV = 'production';
      const prodManager = new (DatabaseManager.constructor as any)();
      
      prodManager.getDrizzleInstance();
      
      expect(mockPg.Pool).toHaveBeenCalledWith(
        expect.objectContaining({
          max: expect.any(Number)
        })
      );
    });

    it('should respect DB_MAX_CONNECTIONS environment variable', () => {
      process.env.DB_MAX_CONNECTIONS = '15';
      const customManager = new (DatabaseManager.constructor as any)();
      
      customManager.getDrizzleInstance();
      
      expect(mockPg.Pool).toHaveBeenCalledWith(
        expect.objectContaining({
          max: 15
        })
      );
    });

    it('should handle invalid DB_MAX_CONNECTIONS gracefully', () => {
      process.env.DB_MAX_CONNECTIONS = 'invalid';
      
      expect(() => {
        new (DatabaseManager.constructor as any)();
      }).not.toThrow();
    });
  });

  describe('concurrent operations', () => {
    it('should handle concurrent connection requests', async () => {
      const operations = Array(5).fill(null).map((_, i) =>
        databaseManager.withConnection(
          async () => `result ${i}`,
          `operation ${i}`
        )
      );
      
      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(5);
      results.forEach((result, i) => {
        expect(result).toBe(`result ${i}`);
      });
    });

    it('should handle concurrent transactions', async () => {
      mockPoolClient.query
        .mockResolvedValue({ rows: [] }); // For BEGIN/COMMIT/ROLLBACK
      
      const transactions = Array(3).fill(null).map((_, i) =>
        databaseManager.withTransaction(
          async () => `transaction ${i}`,
          `transaction ${i}`
        )
      );
      
      const results = await Promise.all(transactions);
      
      expect(results).toHaveLength(3);
      results.forEach((result, i) => {
        expect(result).toBe(`transaction ${i}`);
      });
    });
  });
});