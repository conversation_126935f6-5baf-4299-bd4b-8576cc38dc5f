import { describe, test, expect, vi, beforeEach, beforeAll } from 'vitest'
import jwt from 'jsonwebtoken'
import { Request, Response, NextFunction } from 'express'

// Import the middleware functions - we'll need to extract them first
// For now, we'll mock the implementation to test the behavior

const TEST_JWT_SECRET = 'test-jwt-secret-key-12345'

// Mock the blacklisted tokens set
const blacklistedTokens = new Set<string>()

// Recreate the authenticate middleware for testing
const authenticate = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization
  const cookieToken = req.cookies?.auth_token

  if (!authHeader && !cookieToken) {
    return res.status(401).json({ message: "Authentication required" })
  }

  const token = authHeader ? authHeader.split(" ")[1] : cookieToken

  try {
    if (blacklistedTokens.has(token)) {
      return res.status(401).json({ message: "Token has been revoked" })
    }

    const decoded = jwt.verify(token, TEST_JWT_SECRET, {
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api',
      algorithms: ['HS256']
    }) as { userId: number; role: string; jti?: string }

    req.user = {
      userId: decoded.userId,
      role: decoded.role
    }

    next()
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({ message: "Session expired, please log in again" })
    } else if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({ message: "Invalid token" })
    }

    return res.status(401).json({ message: "Authentication failed" })
  }
}

// Recreate the authorize middleware for testing
const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" })
    }

    if (roles.includes(req.user.role)) {
      next()
    } else {
      res.status(403).json({ message: "Insufficient permissions" })
    }
  }
}

// Helper function to create a valid JWT token
function createValidToken(userId: number = 1, role: string = 'user') {
  return jwt.sign(
    { userId, role, jti: 'test-token' },
    TEST_JWT_SECRET,
    { 
      expiresIn: '1h',
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api'
    }
  )
}

// Helper function to create an expired token
function createExpiredToken(userId: number = 1, role: string = 'user') {
  return jwt.sign(
    { userId, role, jti: 'test-token' },
    TEST_JWT_SECRET,
    { 
      expiresIn: '-1h', // Already expired
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api'
    }
  )
}

// Mock request and response objects
function mockRequest(overrides: any = {}): Partial<Request> {
  return {
    headers: {},
    cookies: {},
    user: undefined,
    ...overrides
  }
}

function mockResponse(): Partial<Response> {
  const res: any = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn().mockReturnThis(),
  }
  return res
}

function mockNext(): NextFunction {
  return vi.fn()
}

describe('Authentication Middleware', () => {
  beforeAll(() => {
    process.env.JWT_SECRET = TEST_JWT_SECRET
  })

  beforeEach(() => {
    blacklistedTokens.clear()
    vi.clearAllMocks()
  })

  describe('authenticate middleware', () => {
    test('should authenticate with valid token in Authorization header', () => {
      const token = createValidToken(1, 'user')
      const req = mockRequest({
        headers: { authorization: `Bearer ${token}` }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(req.user).toEqual({
        userId: 1,
        role: 'user'
      })
      expect(next).toHaveBeenCalledOnce()
      expect(res.status).not.toHaveBeenCalled()
    })

    test('should authenticate with valid token in cookie', () => {
      const token = createValidToken(2, 'owner')
      const req = mockRequest({
        cookies: { auth_token: token }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(req.user).toEqual({
        userId: 2,
        role: 'owner'
      })
      expect(next).toHaveBeenCalledOnce()
      expect(res.status).not.toHaveBeenCalled()
    })

    test('should prioritize Authorization header over cookie', () => {
      const headerToken = createValidToken(1, 'user')
      const cookieToken = createValidToken(2, 'owner')
      
      const req = mockRequest({
        headers: { authorization: `Bearer ${headerToken}` },
        cookies: { auth_token: cookieToken }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(req.user).toEqual({
        userId: 1,
        role: 'user'
      })
      expect(next).toHaveBeenCalledOnce()
    })

    test('should reject request with no token', () => {
      const req = mockRequest()
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Authentication required" })
      expect(next).not.toHaveBeenCalled()
      expect(req.user).toBeUndefined()
    })

    test('should reject blacklisted token', () => {
      const token = createValidToken(1, 'user')
      blacklistedTokens.add(token)
      
      const req = mockRequest({
        headers: { authorization: `Bearer ${token}` }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Token has been revoked" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should reject expired token', () => {
      const expiredToken = createExpiredToken(1, 'user')
      
      const req = mockRequest({
        headers: { authorization: `Bearer ${expiredToken}` }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Session expired, please log in again" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should reject invalid token', () => {
      const req = mockRequest({
        headers: { authorization: 'Bearer invalid-token' }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Invalid token" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should reject token with wrong secret', () => {
      const tokenWithWrongSecret = jwt.sign(
        { userId: 1, role: 'user' },
        'wrong-secret',
        { expiresIn: '1h' }
      )
      
      const req = mockRequest({
        headers: { authorization: `Bearer ${tokenWithWrongSecret}` }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Invalid token" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should reject token with wrong audience', () => {
      const tokenWithWrongAudience = jwt.sign(
        { userId: 1, role: 'user' },
        TEST_JWT_SECRET,
        { 
          expiresIn: '1h',
          audience: 'wrong-audience',
          issuer: 'farmhouse-rental-api'
        }
      )
      
      const req = mockRequest({
        headers: { authorization: `Bearer ${tokenWithWrongAudience}` }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Invalid token" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should handle malformed Authorization header', () => {
      const req = mockRequest({
        headers: { authorization: 'InvalidFormat' }
      })
      const res = mockResponse()
      const next = mockNext()

      authenticate(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Invalid token" })
      expect(next).not.toHaveBeenCalled()
    })
  })

  describe('authorize middleware', () => {
    test('should allow access for user with correct role', () => {
      const req = mockRequest({
        user: { userId: 1, role: 'owner' }
      })
      const res = mockResponse()
      const next = mockNext()

      const ownerOnly = authorize(['owner'])
      ownerOnly(req as Request, res as Response, next)

      expect(next).toHaveBeenCalledOnce()
      expect(res.status).not.toHaveBeenCalled()
    })

    test('should allow access for user with any of multiple allowed roles', () => {
      const req = mockRequest({
        user: { userId: 1, role: 'user' }
      })
      const res = mockResponse()
      const next = mockNext()

      const userOrOwner = authorize(['user', 'owner'])
      userOrOwner(req as Request, res as Response, next)

      expect(next).toHaveBeenCalledOnce()
      expect(res.status).not.toHaveBeenCalled()
    })

    test('should deny access for user with wrong role', () => {
      const req = mockRequest({
        user: { userId: 1, role: 'user' }
      })
      const res = mockResponse()
      const next = mockNext()

      const ownerOnly = authorize(['owner'])
      ownerOnly(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(403)
      expect(res.json).toHaveBeenCalledWith({ message: "Insufficient permissions" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should deny access when no user in request', () => {
      const req = mockRequest() // No user property
      const res = mockResponse()
      const next = mockNext()

      const ownerOnly = authorize(['owner'])
      ownerOnly(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith({ message: "Authentication required" })
      expect(next).not.toHaveBeenCalled()
    })

    test('should be case sensitive for roles', () => {
      const req = mockRequest({
        user: { userId: 1, role: 'Owner' } // Capital O
      })
      const res = mockResponse()
      const next = mockNext()

      const ownerOnly = authorize(['owner']) // lowercase o
      ownerOnly(req as Request, res as Response, next)

      expect(res.status).toHaveBeenCalledWith(403)
      expect(res.json).toHaveBeenCalledWith({ message: "Insufficient permissions" })
      expect(next).not.toHaveBeenCalled()
    })
  })
})