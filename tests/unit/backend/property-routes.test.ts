import { describe, test, expect, vi, beforeEach, beforeAll } from 'vitest'
import request from 'supertest'
import express from 'express'

// Mock the storage module
const mockStorage = {
  getProperties: vi.fn(),
  getProperty: vi.fn(),
  createProperty: vi.fn(),
  updateProperty: vi.fn(),
  deleteProperty: vi.fn(),
  getPropertiesByOwner: vi.fn(),
}

// Create a test app with property routes
function createTestApp() {
  const app = express()
  app.use(express.json())

  // Mock authentication middleware
  const mockAuthenticate = (req: any, res: any, next: any) => {
    // Set a default user for testing
    req.user = { userId: 1, role: 'user' }
    next()
  }

  // Mock authorization middleware
  const mockAuthorize = (roles: string[]) => (req: any, res: any, next: any) => {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" })
    }
    if (roles.includes(req.user.role)) {
      next()
    } else {
      res.status(403).json({ message: "Insufficient permissions" })
    }
  }

  // Mock rate limiting
  const mockRateLimit = (req: any, res: any, next: any) => next()

  // Mock validation middleware
  const mockValidate = (schema: any) => (req: any, res: any, next: any) => next()

  // Property routes
  app.get('/api/properties', mockRateLimit, async (req, res) => {
    try {
      const featured = req.query.featured === "true"
      const location = req.query.location as string | undefined
      const date = req.query.date ? new Date(req.query.date as string) : undefined
      const minPrice = req.query.minPrice ? parseFloat(req.query.minPrice as string) : undefined
      const maxPrice = req.query.maxPrice ? parseFloat(req.query.maxPrice as string) : undefined
      const amenities = req.query.amenities ? (req.query.amenities as string).split(',') : undefined

      const properties = await mockStorage.getProperties(
        featured, 
        location, 
        date, 
        minPrice, 
        maxPrice, 
        amenities
      )

      res.json(properties)
    } catch (error) {
      console.error("Get properties error:", error)
      res.status(500).json({ message: "Server error" })
    }
  })

  app.get('/api/properties/:id', async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id)
      const property = await mockStorage.getProperty(propertyId)

      if (!property) {
        return res.status(404).json({ message: "Property not found" })
      }

      res.json(property)
    } catch (error) {
      console.error("Get property error:", error)
      res.status(500).json({ message: "Server error" })
    }
  })

  app.post('/api/properties', mockAuthenticate, mockAuthorize(['owner']), mockValidate({}), async (req, res) => {
    try {
      const property = {
        ...req.body,
        ownerId: req.user.userId
      }

      const newProperty = await mockStorage.createProperty(property)
      res.status(201).json(newProperty)
    } catch (error) {
      console.error("Create property error:", error)
      res.status(500).json({ message: "Server error" })
    }
  })

  app.put('/api/properties/:id', mockAuthenticate, mockAuthorize(['owner']), async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id)

      // Verify ownership
      const property = await mockStorage.getProperty(propertyId)
      if (!property) {
        return res.status(404).json({ message: "Property not found" })
      }

      if (property.ownerId !== req.user.userId) {
        return res.status(403).json({ message: "You don't have permission to update this property" })
      }

      const updatedProperty = await mockStorage.updateProperty(propertyId, req.body)
      res.json(updatedProperty)
    } catch (error) {
      console.error("Update property error:", error)
      res.status(500).json({ message: "Server error" })
    }
  })

  app.delete('/api/properties/:id', mockAuthenticate, mockAuthorize(['owner']), async (req, res) => {
    try {
      const propertyId = parseInt(req.params.id)

      // Verify ownership
      const property = await mockStorage.getProperty(propertyId)
      if (!property) {
        return res.status(404).json({ message: "Property not found" })
      }

      if (property.ownerId !== req.user.userId) {
        return res.status(403).json({ message: "You don't have permission to delete this property" })
      }

      await mockStorage.deleteProperty(propertyId)
      res.status(204).end()
    } catch (error) {
      console.error("Delete property error:", error)
      res.status(500).json({ message: "Server error" })
    }
  })

  app.get('/api/properties/owner/me', mockAuthenticate, mockAuthorize(['owner']), async (req, res) => {
    try {
      const properties = await mockStorage.getPropertiesByOwner(req.user.userId)
      res.json(properties)
    } catch (error) {
      console.error("Get owner properties error:", error)
      res.status(500).json({ message: "Server error" })
    }
  })

  return app
}

const mockProperty = {
  id: 1,
  ownerId: 1,
  title: 'Beautiful Farmhouse',
  description: 'A stunning farmhouse with amazing views',
  location: 'California, USA',
  halfDayPrice: 150,
  fullDayPrice: 250,
  bedrooms: 3,
  bathrooms: 2,
  amenities: ['WiFi', 'Pool', 'Kitchen'],
  images: ['/uploads/farmhouse1.jpg'],
  status: 'active',
  featured: true,
  createdAt: '2025-05-31T14:41:36.687Z',
}

describe('Property Routes', () => {
  let app: express.Application

  beforeEach(() => {
    app = createTestApp()
    vi.clearAllMocks()
  })

  describe('GET /api/properties', () => {
    test('should get all properties', async () => {
      const mockProperties = [mockProperty]
      mockStorage.getProperties.mockResolvedValue(mockProperties)

      const response = await request(app)
        .get('/api/properties')

      expect(response.status).toBe(200)
      expect(response.body).toEqual(mockProperties)
      expect(mockStorage.getProperties).toHaveBeenCalledWith(
        false, // featured
        undefined, // location
        undefined, // date
        undefined, // minPrice
        undefined, // maxPrice
        undefined  // amenities
      )
    })

    test('should filter properties by featured status', async () => {
      const featuredProperties = [{ ...mockProperty, featured: true }]
      mockStorage.getProperties.mockResolvedValue(featuredProperties)

      const response = await request(app)
        .get('/api/properties?featured=true')

      expect(response.status).toBe(200)
      expect(response.body).toEqual(featuredProperties)
      expect(mockStorage.getProperties).toHaveBeenCalledWith(
        true, // featured
        undefined, // location
        undefined, // date
        undefined, // minPrice
        undefined, // maxPrice
        undefined  // amenities
      )
    })

    test('should filter properties by location', async () => {
      const locationProperties = [mockProperty]
      mockStorage.getProperties.mockResolvedValue(locationProperties)

      const response = await request(app)
        .get('/api/properties?location=California')

      expect(response.status).toBe(200)
      expect(mockStorage.getProperties).toHaveBeenCalledWith(
        false, // featured
        'California', // location
        undefined, // date
        undefined, // minPrice
        undefined, // maxPrice
        undefined  // amenities
      )
    })

    test('should filter properties by price range', async () => {
      const priceFilteredProperties = [mockProperty]
      mockStorage.getProperties.mockResolvedValue(priceFilteredProperties)

      const response = await request(app)
        .get('/api/properties?minPrice=100&maxPrice=300')

      expect(response.status).toBe(200)
      expect(mockStorage.getProperties).toHaveBeenCalledWith(
        false, // featured
        undefined, // location
        undefined, // date
        100, // minPrice
        300, // maxPrice
        undefined  // amenities
      )
    })

    test('should filter properties by amenities', async () => {
      const amenityFilteredProperties = [mockProperty]
      mockStorage.getProperties.mockResolvedValue(amenityFilteredProperties)

      const response = await request(app)
        .get('/api/properties?amenities=WiFi,Pool')

      expect(response.status).toBe(200)
      expect(mockStorage.getProperties).toHaveBeenCalledWith(
        false, // featured
        undefined, // location
        undefined, // date
        undefined, // minPrice
        undefined, // maxPrice
        ['WiFi', 'Pool']  // amenities
      )
    })

    test('should handle database errors', async () => {
      mockStorage.getProperties.mockRejectedValue(new Error('Database error'))

      const response = await request(app)
        .get('/api/properties')

      expect(response.status).toBe(500)
      expect(response.body).toEqual({ message: "Server error" })
    })
  })

  describe('GET /api/properties/:id', () => {
    test('should get property by id', async () => {
      mockStorage.getProperty.mockResolvedValue(mockProperty)

      const response = await request(app)
        .get('/api/properties/1')

      expect(response.status).toBe(200)
      expect(response.body).toEqual(mockProperty)
      expect(mockStorage.getProperty).toHaveBeenCalledWith(1)
    })

    test('should return 404 for non-existent property', async () => {
      mockStorage.getProperty.mockResolvedValue(null)

      const response = await request(app)
        .get('/api/properties/999')

      expect(response.status).toBe(404)
      expect(response.body).toEqual({ message: "Property not found" })
    })

    test('should handle database errors', async () => {
      mockStorage.getProperty.mockRejectedValue(new Error('Database error'))

      const response = await request(app)
        .get('/api/properties/1')

      expect(response.status).toBe(500)
      expect(response.body).toEqual({ message: "Server error" })
    })
  })

  describe('POST /api/properties', () => {
    const newPropertyData = {
      title: 'New Farmhouse',
      description: 'A beautiful new farmhouse',
      location: 'Texas, USA',
      halfDayPrice: 120,
      fullDayPrice: 200,
      bedrooms: 2,
      bathrooms: 1,
      amenities: ['WiFi', 'Kitchen'],
      images: ['/uploads/new-farmhouse.jpg']
    }

    test('should create property for owner', async () => {
      // Mock the app to use owner role
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 2, role: 'owner' }
        next()
      })
      ownerApp.post('/api/properties', async (req, res) => {
        const property = { ...req.body, ownerId: req.user.userId }
        const newProperty = await mockStorage.createProperty(property)
        res.status(201).json(newProperty)
      })

      const expectedProperty = { ...newPropertyData, ownerId: 2, id: 1 }
      mockStorage.createProperty.mockResolvedValue(expectedProperty)

      const response = await request(ownerApp)
        .post('/api/properties')
        .send(newPropertyData)

      expect(response.status).toBe(201)
      expect(response.body).toEqual(expectedProperty)
      expect(mockStorage.createProperty).toHaveBeenCalledWith({
        ...newPropertyData,
        ownerId: 2
      })
    })

    test('should handle database errors during creation', async () => {
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 2, role: 'owner' }
        next()
      })
      ownerApp.post('/api/properties', async (req, res) => {
        try {
          const property = { ...req.body, ownerId: req.user.userId }
          await mockStorage.createProperty(property)
        } catch (error) {
          res.status(500).json({ message: "Server error" })
        }
      })

      mockStorage.createProperty.mockRejectedValue(new Error('Database error'))

      const response = await request(ownerApp)
        .post('/api/properties')
        .send(newPropertyData)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({ message: "Server error" })
    })
  })

  describe('PUT /api/properties/:id', () => {
    const updateData = {
      title: 'Updated Farmhouse',
      description: 'Updated description'
    }

    test('should update property for owner', async () => {
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 1, role: 'owner' }
        next()
      })
      ownerApp.put('/api/properties/:id', async (req, res) => {
        const propertyId = parseInt(req.params.id)
        const property = await mockStorage.getProperty(propertyId)
        
        if (!property) {
          return res.status(404).json({ message: "Property not found" })
        }
        
        if (property.ownerId !== req.user.userId) {
          return res.status(403).json({ message: "You don't have permission to update this property" })
        }
        
        const updatedProperty = await mockStorage.updateProperty(propertyId, req.body)
        res.json(updatedProperty)
      })

      mockStorage.getProperty.mockResolvedValue({ ...mockProperty, ownerId: 1 })
      const updatedProperty = { ...mockProperty, ...updateData, ownerId: 1 }
      mockStorage.updateProperty.mockResolvedValue(updatedProperty)

      const response = await request(ownerApp)
        .put('/api/properties/1')
        .send(updateData)

      expect(response.status).toBe(200)
      expect(response.body).toEqual(updatedProperty)
      expect(mockStorage.updateProperty).toHaveBeenCalledWith(1, updateData)
    })

    test('should return 404 for non-existent property', async () => {
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 1, role: 'owner' }
        next()
      })
      ownerApp.put('/api/properties/:id', async (req, res) => {
        const propertyId = parseInt(req.params.id)
        const property = await mockStorage.getProperty(propertyId)
        
        if (!property) {
          return res.status(404).json({ message: "Property not found" })
        }
      })

      mockStorage.getProperty.mockResolvedValue(null)

      const response = await request(ownerApp)
        .put('/api/properties/999')
        .send(updateData)

      expect(response.status).toBe(404)
      expect(response.body).toEqual({ message: "Property not found" })
    })

    test('should return 403 for non-owner trying to update', async () => {
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 2, role: 'owner' } // Different owner
        next()
      })
      ownerApp.put('/api/properties/:id', async (req, res) => {
        const propertyId = parseInt(req.params.id)
        const property = await mockStorage.getProperty(propertyId)
        
        if (!property) {
          return res.status(404).json({ message: "Property not found" })
        }
        
        if (property.ownerId !== req.user.userId) {
          return res.status(403).json({ message: "You don't have permission to update this property" })
        }
      })

      mockStorage.getProperty.mockResolvedValue({ ...mockProperty, ownerId: 1 })

      const response = await request(ownerApp)
        .put('/api/properties/1')
        .send(updateData)

      expect(response.status).toBe(403)
      expect(response.body).toEqual({ message: "You don't have permission to update this property" })
    })
  })

  describe('DELETE /api/properties/:id', () => {
    test('should delete property for owner', async () => {
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 1, role: 'owner' }
        next()
      })
      ownerApp.delete('/api/properties/:id', async (req, res) => {
        const propertyId = parseInt(req.params.id)
        const property = await mockStorage.getProperty(propertyId)
        
        if (!property) {
          return res.status(404).json({ message: "Property not found" })
        }
        
        if (property.ownerId !== req.user.userId) {
          return res.status(403).json({ message: "You don't have permission to delete this property" })
        }
        
        await mockStorage.deleteProperty(propertyId)
        res.status(204).end()
      })

      mockStorage.getProperty.mockResolvedValue({ ...mockProperty, ownerId: 1 })
      mockStorage.deleteProperty.mockResolvedValue(undefined)

      const response = await request(ownerApp)
        .delete('/api/properties/1')

      expect(response.status).toBe(204)
      expect(mockStorage.deleteProperty).toHaveBeenCalledWith(1)
    })

    test('should return 404 for non-existent property', async () => {
      const ownerApp = express()
      ownerApp.use(express.json())
      ownerApp.use((req: any, res, next) => {
        req.user = { userId: 1, role: 'owner' }
        next()
      })
      ownerApp.delete('/api/properties/:id', async (req, res) => {
        const propertyId = parseInt(req.params.id)
        const property = await mockStorage.getProperty(propertyId)
        
        if (!property) {
          return res.status(404).json({ message: "Property not found" })
        }
      })

      mockStorage.getProperty.mockResolvedValue(null)

      const response = await request(ownerApp)
        .delete('/api/properties/999')

      expect(response.status).toBe(404)
      expect(response.body).toEqual({ message: "Property not found" })
    })
  })
})