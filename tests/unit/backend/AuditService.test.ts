import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AuditService } from '../../../server/services/AuditService';
import { logger } from '../../../server/services/LoggerService';

// Mock the logger service
vi.mock('../../../server/services/LoggerService', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
}));

describe('AuditService', () => {
  const mockLogger = logger as {
    info: vi.Mock;
    warn: vi.Mock;
    error: vi.<PERSON>ck;
  };

  const mockRequest = {
    ip: '***********',
    headers: {
      'user-agent': 'Mozilla/5.0 Test Browser'
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock environment variable
    process.env.NODE_ENV = 'test';
  });

  afterEach(() => {
    delete process.env.NODE_ENV;
  });

  describe('logOwnerAction', () => {
    it('should log a basic owner action successfully', () => {
      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_create',
        resource: 'property',
        resourceId: 456,
        success: true,
        details: { name: 'Test Property' }
      };

      AuditService.logOwnerAction(auditData);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: property_create',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'owner',
          action: 'property_create',
          resource: 'property',
          resourceId: 456,
          success: true,
          details: { name: 'Test Property' },
          metadata: expect.objectContaining({
            environment: 'test'
          })
        })
      );
    });

    it('should log critical actions with warning level', () => {
      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_delete',
        resource: 'property',
        resourceId: 456,
        success: true
      };

      AuditService.logOwnerAction(auditData);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: property_delete',
        'audit',
        expect.objectContaining({
          action: 'property_delete'
        })
      );
    });

    it('should log security-relevant actions with appropriate level', () => {
      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'security_unauthorized_access',
        resource: 'security',
        success: false
      };

      AuditService.logOwnerAction(auditData);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: security_unauthorized_access',
        'audit',
        expect.objectContaining({
          action: 'security_unauthorized_access'
        })
      );
    });

    it('should log failed actions with error level', () => {
      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_update',
        resource: 'property',
        resourceId: 456,
        success: false,
        details: { error: 'Validation failed' }
      };

      AuditService.logOwnerAction(auditData);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'FAILED OWNER ACTION: property_update',
        undefined,
        'security',
        expect.objectContaining({
          success: false,
          alertLevel: 'high'
        })
      );
    });

    it('should include request metadata when provided', () => {
      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_create',
        resource: 'property',
        success: true,
        ip: mockRequest.ip,
        userAgent: mockRequest.headers['user-agent']
      };

      AuditService.logOwnerAction(auditData);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: property_create',
        'audit',
        expect.objectContaining({
          metadata: expect.objectContaining({
            ip: '***********',
            userAgent: 'Mozilla/5.0 Test Browser',
            environment: 'test'
          })
        })
      );
    });

    it('should use current timestamp when not provided', () => {
      const beforeCall = new Date();
      
      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_create',
        resource: 'property',
        success: true
      };

      AuditService.logOwnerAction(auditData);

      const afterCall = new Date();
      
      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: property_create',
        'audit',
        expect.objectContaining({
          timestamp: expect.any(String)
        })
      );

      // Verify timestamp is recent
      const loggedTimestamp = new Date(mockLogger.info.mock.calls[0][2].timestamp);
      expect(loggedTimestamp.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(loggedTimestamp.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    });

    it('should handle logging errors gracefully', () => {
      // Make logger.info throw an error
      mockLogger.info.mockImplementationOnce(() => {
        throw new Error('Logger service unavailable');
      });

      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_create',
        resource: 'property',
        success: true
      };

      // Should not throw
      expect(() => AuditService.logOwnerAction(auditData)).not.toThrow();

      // Should log the error
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to log audit entry',
        expect.any(Error),
        'system',
        expect.objectContaining({
          error: 'Logger service unavailable',
          originalData: auditData
        })
      );
    });

    it('should handle non-Error exceptions in logging', () => {
      mockLogger.info.mockImplementationOnce(() => {
        throw 'String error';
      });

      const auditData = {
        userId: 123,
        userRole: 'owner',
        action: 'property_create',
        resource: 'property',
        success: true
      };

      expect(() => AuditService.logOwnerAction(auditData)).not.toThrow();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to log audit entry',
        undefined,
        'system',
        expect.objectContaining({
          error: 'Unknown error',
          originalData: auditData
        })
      );
    });
  });

  describe('logPropertyAction', () => {
    it('should log property creation action', () => {
      AuditService.logPropertyAction(123, 'create', 456, { name: 'New Property' }, mockRequest);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: property_create',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'owner',
          action: 'property_create',
          resource: 'property',
          resourceId: 456,
          details: { name: 'New Property' },
          success: true,
          metadata: expect.objectContaining({
            ip: '***********',
            userAgent: 'Mozilla/5.0 Test Browser'
          })
        })
      );
    });

    it('should handle all CRUD operations', () => {
      const operations = ['create', 'read', 'update', 'delete'] as const;
      
      operations.forEach((op, index) => {
        vi.clearAllMocks(); // Clear previous calls
        AuditService.logPropertyAction(123, op, 456);
        
        if (op === 'delete') {
          expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining(`property_${op}`),
            'audit',
            expect.objectContaining({
              action: `property_${op}`
            })
          );
        } else {
          expect(mockLogger.info).toHaveBeenCalledWith(
            expect.stringContaining(`property_${op}`),
            'audit',
            expect.objectContaining({
              action: `property_${op}`
            })
          );
        }
      });
    });

    it('should default to empty details when not provided', () => {
      AuditService.logPropertyAction(123, 'create', 456);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: property_create',
        'audit',
        expect.objectContaining({
          details: {}
        })
      );
    });
  });

  describe('logBookingAction', () => {
    it('should log booking approval action', () => {
      AuditService.logBookingAction(123, 'approve', 789, { reason: 'All requirements met' }, mockRequest);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: booking_approve',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'owner',
          action: 'booking_approve',
          resource: 'booking',
          resourceId: 789,
          details: { reason: 'All requirements met' },
          success: true
        })
      );
    });

    it('should handle all booking actions', () => {
      const actions = ['approve', 'reject', 'complete', 'view'] as const;
      
      actions.forEach(action => {
        vi.clearAllMocks(); // Clear previous calls
        AuditService.logBookingAction(123, action, 789);
        
        if (action === 'reject') {
          expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining(`booking_${action}`),
            'audit',
            expect.objectContaining({
              action: `booking_${action}`
            })
          );
        } else {
          expect(mockLogger.info).toHaveBeenCalledWith(
            expect.stringContaining(`booking_${action}`),
            'audit',
            expect.objectContaining({
              action: `booking_${action}`
            })
          );
        }
      });
    });

    it('should log booking rejection as critical action', () => {
      AuditService.logBookingAction(123, 'reject', 789, { reason: 'Policy violation' });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: booking_reject',
        'audit',
        expect.objectContaining({
          action: 'booking_reject'
        })
      );
    });
  });

  describe('logPricingAction', () => {
    const oldPricing = {
      halfDayPrice: 1000,
      fullDayPrice: 1500,
      weekendHalfDayPrice: 1200
    };

    const newPricing = {
      halfDayPrice: 1100,
      fullDayPrice: 1600,
      weekendHalfDayPrice: 1200
    };

    it('should log pricing updates with calculated changes', () => {
      AuditService.logPricingAction(123, 456, oldPricing, newPricing, mockRequest);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: pricing_update',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'owner',
          action: 'pricing_update',
          resource: 'property',
          resourceId: 456,
          details: expect.objectContaining({
            oldPricing,
            newPricing,
            changes: expect.objectContaining({
              halfDayPrice: expect.objectContaining({
                from: 1000,
                to: 1100,
                change: 100,
                percentChange: '10.00%'
              }),
              fullDayPrice: expect.objectContaining({
                from: 1500,
                to: 1600,
                change: 100,
                percentChange: '6.67%'
              })
            })
          })
        })
      );
    });

    it('should handle missing pricing data gracefully', () => {
      AuditService.logPricingAction(123, 456, null, newPricing);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: pricing_update',
        'audit',
        expect.objectContaining({
          details: expect.objectContaining({
            changes: { error: 'Missing pricing data for comparison' }
          })
        })
      );
    });

    it('should not log changes for unchanged fields', () => {
      const samePricing = { ...oldPricing };
      
      AuditService.logPricingAction(123, 456, oldPricing, samePricing);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: pricing_update',
        'audit',
        expect.objectContaining({
          details: expect.objectContaining({
            changes: {}
          })
        })
      );
    });

    it('should handle zero old price correctly', () => {
      const zeroPricing = { halfDayPrice: 0 };
      const updatedPricing = { halfDayPrice: 1000 };
      
      AuditService.logPricingAction(123, 456, zeroPricing, updatedPricing);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: pricing_update',
        'audit',
        expect.objectContaining({
          details: expect.objectContaining({
            changes: expect.objectContaining({
              halfDayPrice: expect.objectContaining({
                from: 0,
                to: 1000,
                change: 1000,
                percentChange: 'N/A'
              })
            })
          })
        })
      );
    });
  });

  describe('logMediaAction', () => {
    it('should log media upload action', () => {
      AuditService.logMediaAction(123, 'upload', 456, { fileName: 'image.jpg', size: '2MB' }, mockRequest);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: media_upload',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'owner',
          action: 'media_upload',
          resource: 'property',
          resourceId: 456,
          details: { fileName: 'image.jpg', size: '2MB' }
        })
      );
    });

    it('should log media deletion as critical action', () => {
      AuditService.logMediaAction(123, 'delete', 456, { fileName: 'old-image.jpg' });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'CRITICAL OWNER ACTION: media_delete',
        'audit',
        expect.objectContaining({
          action: 'media_delete'
        })
      );
    });

    it('should handle all media actions', () => {
      const actions = ['upload', 'delete', 'reorder'] as const;
      
      actions.forEach(action => {
        vi.clearAllMocks(); // Clear previous calls
        AuditService.logMediaAction(123, action, 456);
        
        if (action === 'delete') {
          expect(mockLogger.warn).toHaveBeenCalledTimes(1);
        } else {
          expect(mockLogger.info).toHaveBeenCalledTimes(1);
        }
      });
    });
  });

  describe('logAuthAction', () => {
    it('should log successful login action', () => {
      AuditService.logAuthAction(123, 'login', { userRole: 'owner' }, mockRequest, true);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: auth_login',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'owner',
          action: 'auth_login',
          resource: 'authentication',
          success: true
        })
      );
    });

    it('should log failed access attempts', () => {
      AuditService.logAuthAction(null, 'access_denied', { userRole: 'unknown', reason: 'Invalid token' }, mockRequest, false);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'FAILED OWNER ACTION: auth_access_denied',
        undefined,
        'security',
        expect.objectContaining({
          userId: 0,
          userRole: 'unknown',
          action: 'auth_access_denied',
          success: false,
          alertLevel: 'high'
        })
      );
    });

    it('should handle null userId by defaulting to 0', () => {
      AuditService.logAuthAction(null, 'login', { userRole: 'user' });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: auth_login',
        'audit',
        expect.objectContaining({
          userId: 0
        })
      );
    });

    it('should default success to true when not provided', () => {
      AuditService.logAuthAction(123, 'logout');

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: auth_logout',
        'audit',
        expect.objectContaining({
          success: true
        })
      );
    });

    it('should handle missing userRole gracefully', () => {
      AuditService.logAuthAction(123, 'login', {});

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: auth_login',
        'audit',
        expect.objectContaining({
          userRole: 'unknown'
        })
      );
    });
  });

  describe('logSecurityEvent', () => {
    it('should log unauthorized access event', () => {
      const details = {
        userRole: 'user',
        resource: 'admin_panel',
        attemptedAction: 'access'
      };

      AuditService.logSecurityEvent('unauthorized_access', 123, details, mockRequest);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: security_unauthorized_access',
        'audit',
        expect.objectContaining({
          userId: 123,
          userRole: 'user',
          action: 'security_unauthorized_access',
          resource: 'security',
          success: false,
          details: expect.objectContaining({
            ...details,
            severity: 'high',
            requiresInvestigation: true
          })
        })
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'SECURITY ALERT: unauthorized_access',
        undefined,
        'security',
        expect.objectContaining({
          userId: 123,
          event: 'unauthorized_access',
          details,
          ip: '***********',
          alertLevel: 'critical'
        })
      );
    });

    it('should handle invalid_token security event', () => {
      AuditService.logSecurityEvent('invalid_token', 123, { userRole: 'user' });
      
      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: security_invalid_token',
        'audit',
        expect.any(Object)
      );
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'SECURITY ALERT: invalid_token',
        undefined,
        'security',
        expect.any(Object)
      );
    });

    it('should handle role_mismatch security event', () => {
      AuditService.logSecurityEvent('role_mismatch', 123, { userRole: 'user' });
      
      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: security_role_mismatch',
        'audit',
        expect.any(Object)
      );
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'SECURITY ALERT: role_mismatch',
        undefined,
        'security',
        expect.any(Object)
      );
    });


    it('should handle null userId by defaulting to 0', () => {
      AuditService.logSecurityEvent('invalid_token', null, { userRole: 'unknown' });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: security_invalid_token',
        'audit',
        expect.objectContaining({
          userId: 0
        })
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'SECURITY ALERT: invalid_token',
        undefined,
        'security',
        expect.objectContaining({
          userId: null
        })
      );
    });

    it('should default userRole when not provided in details', () => {
      AuditService.logSecurityEvent('role_mismatch', 123, {});

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER SECURITY ACTION: security_role_mismatch',
        'audit',
        expect.objectContaining({
          userRole: 'unknown'
        })
      );
    });
  });

  describe('generateAuditSummary', () => {
    it('should generate audit summary with default 30 days', () => {
      AuditService.generateAuditSummary(123);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Generating audit summary for user 123 (last 30 days)',
        'audit',
        expect.objectContaining({
          userId: 123,
          reportType: 'summary',
          periodDays: 30
        })
      );
    });

    it('should generate audit summary with custom days', () => {
      AuditService.generateAuditSummary(123, 7);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Generating audit summary for user 123 (last 7 days)',
        'audit',
        expect.objectContaining({
          userId: 123,
          reportType: 'summary',
          periodDays: 7
        })
      );
    });
  });

  describe('Private Methods', () => {
    // Testing private methods by examining their behavior through public methods
    
    it('should identify critical actions correctly', () => {
      const criticalActions = [
        'property_delete',
        'booking_reject',
        'media_delete',
        'pricing_update'
      ];

      criticalActions.forEach(action => {
        AuditService.logOwnerAction({
          userId: 123,
          userRole: 'owner',
          action,
          resource: 'test',
          success: true
        });

        expect(mockLogger.warn).toHaveBeenCalledWith(
          `CRITICAL OWNER ACTION: ${action}`,
          'audit',
          expect.any(Object)
        );
      });
    });

    it('should identify security-relevant actions correctly', () => {
      const securityActions = [
        'security_unauthorized_access',
        'auth_login',
        'property_delete',
        'booking_reject'
      ];

      securityActions.forEach(action => {
        vi.clearAllMocks();
        AuditService.logOwnerAction({
          userId: 123,
          userRole: 'owner',
          action,
          resource: 'test',
          success: true
        });

        // Check that the action was logged with security or critical prefix
        const infoCall = mockLogger.info.mock.calls[0];
        const warnCall = mockLogger.warn.mock.calls[0];
        
        if (warnCall) {
          expect(warnCall[0]).toMatch(/CRITICAL OWNER ACTION:|OWNER SECURITY ACTION:/);
        } else if (infoCall) {
          expect(infoCall[0]).toMatch(/OWNER SECURITY ACTION:/);
        } else {
          throw new Error(`No log call found for action: ${action}`);
        }
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty details object', () => {
      AuditService.logOwnerAction({
        userId: 123,
        userRole: 'owner',
        action: 'test_action',
        resource: 'test',
        success: true,
        details: {}
      });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: test_action',
        'audit',
        expect.objectContaining({
          details: {}
        })
      );
    });

    it('should handle undefined details', () => {
      AuditService.logOwnerAction({
        userId: 123,
        userRole: 'owner',
        action: 'test_action',
        resource: 'test',
        success: true
      });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: test_action',
        'audit',
        expect.objectContaining({
          details: {}
        })
      );
    });

    it('should handle complex nested details objects', () => {
      const complexDetails = {
        nested: {
          deeply: {
            data: 'value'
          }
        },
        array: [1, 2, 3],
        null_value: null,
        undefined_value: undefined
      };

      AuditService.logOwnerAction({
        userId: 123,
        userRole: 'owner',
        action: 'test_action',
        resource: 'test',
        success: true,
        details: complexDetails
      });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'OWNER ACTION: test_action',
        'audit',
        expect.objectContaining({
          details: complexDetails
        })
      );
    });
  });
});