import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Request, Response } from 'express';

// Mock the cache service using factory function
vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    keys: vi.fn()
  }
}));

vi.mock('../../../server/utils/logger', () => ({
  log: vi.fn()
}));

// Import after mocking
import {
  AdvancedRateLimiter,
  rateLimiters,
  createRateLimitMiddleware,
  ipRateLimit,
  userRateLimit,
  endpointRateLimit,
  rateLimiter
} from '../../../server/utils/rate-limiting';
import { cacheService } from '../../../server/services/CacheService';

describe('Rate Limiting', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockReq = {
      ip: '127.0.0.1',
      method: 'POST',
      path: '/test',
      body: {},
      headers: {},
      user: undefined
    };
    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
      set: vi.fn()
    };
    mockNext = vi.fn();
  });

  describe('AdvancedRateLimiter', () => {
    let rateLimiter: AdvancedRateLimiter;

    beforeEach(() => {
      rateLimiter = AdvancedRateLimiter.getInstance();
    });

    describe('checkRateLimit', () => {
      it('should allow first request', async () => {
        vi.mocked(cacheService).get.mockResolvedValue(null);
        vi.mocked(cacheService).set.mockResolvedValue(undefined);

        const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000
        });

        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(4);
        expect(vi.mocked(cacheService).set).toHaveBeenCalled();
      });

      it('should track requests within window', async () => {
        const now = Date.now();
        vi.mocked(cacheService).get.mockResolvedValue({
          count: 2,
          resetTime: now + 30000,
          blocked: false
        });
        vi.mocked(cacheService).set.mockResolvedValue(undefined);

        const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000
        });

        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(2);
      });

      it('should block when limit exceeded', async () => {
        const now = Date.now();
        vi.mocked(cacheService).get.mockResolvedValue({
          count: 5,
          resetTime: now + 30000,
          blocked: false
        });
        vi.mocked(cacheService).set.mockResolvedValue(undefined);

        const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000
        });

        expect(result.allowed).toBe(false);
        expect(result.remaining).toBe(0);
        expect(result.retryAfter).toBeGreaterThan(0);
      });

      it('should reset counter after window expires', async () => {
        const now = Date.now();
        vi.mocked(cacheService).get.mockResolvedValue({
          count: 5,
          resetTime: now - 1000, // Expired window
          blocked: false
        });
        vi.mocked(cacheService).set.mockResolvedValue(undefined);

        const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000
        });

        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(4);
      });

      it('should respect block duration', async () => {
        const now = Date.now();
        vi.mocked(cacheService).get.mockResolvedValue({
          count: 6,
          resetTime: now + 30000,
          blocked: true,
          blockUntil: now + 10000
        });

        const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000,
          blockDurationMs: 15000
        });

        expect(result.allowed).toBe(false);
        expect(result.retryAfter).toBeCloseTo(10, 1);
      });

      it('should fail open on cache errors', async () => {
        vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

        const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000
        });

        expect(result.allowed).toBe(true);
      });
    });

    describe('checkProgressiveRateLimit', () => {
      it('should apply progressive delays', async () => {
        // Mock violation count
        vi.mocked(cacheService).get
          .mockResolvedValueOnce(null) // First call for rate limit check
          .mockResolvedValueOnce(2);   // Second call for violation count

        vi.mocked(cacheService).set.mockResolvedValue(undefined);

        const result = await rateLimiter.checkProgressiveRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          multiplier: 2
        });

        expect(result.allowed).toBe(true);
      });

      it('should increment violation count on block', async () => {
        const now = Date.now();
        vi.mocked(cacheService).get
          .mockImplementation(async (key: string) => {
            if (key.includes('violations:')) {
              return 1; // Current violation count
            }
            if (key.includes('rate_limit:')) {
              return {
                count: 6, // Exceeds maxRequests of 5
                resetTime: now + 30000,
                blocked: false
              };
            }
            return null;
          });

        vi.mocked(cacheService).set.mockResolvedValue(undefined);

        const result = await rateLimiter.checkProgressiveRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          multiplier: 2
        });

        expect(result.allowed).toBe(false);
        expect(vi.mocked(cacheService).set).toHaveBeenCalledWith(
          expect.stringContaining('violations'),
          2,
          3600
        );
      });
    });

    describe('clearRateLimit', () => {
      it('should clear rate limit and violation data', async () => {
        vi.mocked(cacheService).delete.mockResolvedValue(true);

        await rateLimiter.clearRateLimit('test-user', 'test-operation');

        expect(vi.mocked(cacheService).delete).toHaveBeenCalledTimes(2);
        expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith('rate_limit:test-operation:test-user');
        expect(vi.mocked(cacheService).delete).toHaveBeenCalledWith('violations:test-operation:test-user');
      });
    });

    describe('getRateLimitStatus', () => {
      it('should return current rate limit status', async () => {
        const now = Date.now();
        vi.mocked(cacheService).get
          .mockImplementation(async (key: string) => {
            if (key.includes('rate_limit:')) {
              return {
                count: 3,
                resetTime: now + 30000,
                blocked: false
              };
            }
            if (key.includes('violations:')) {
              return 1;
            }
            return null;
          });

        const status = await rateLimiter.getRateLimitStatus('test-user', 'test-operation');

        expect(status.current).toBe(3);
        expect(status.blocked).toBe(false);
        expect(status.violations).toBe(1);
      });

      it('should handle missing rate limit data', async () => {
        vi.mocked(cacheService).get.mockImplementation(async () => null);

        const status = await rateLimiter.getRateLimitStatus('test-user', 'test-operation');

        expect(status.current).toBe(0);
        expect(status.blocked).toBe(false);
        expect(status.violations).toBe(0);
      });
    });
  });

  describe('Pre-configured Rate Limiters', () => {
    describe('API Rate Limiter', () => {
      it('should create API rate limiter with default settings', () => {
        const limiter = rateLimiters.api();
        expect(limiter).toBeDefined();
      });

      it('should accept custom settings', () => {
        const limiter = rateLimiters.api(200, 120000);
        expect(limiter).toBeDefined();
      });
    });

    describe('Auth Rate Limiter', () => {
      it('should create auth rate limiter with stricter limits', () => {
        const limiter = rateLimiters.auth();
        expect(limiter).toBeDefined();
      });

      it('should use IP-based limiting', () => {
        const limiter = rateLimiters.auth(3, 600000);
        expect(limiter).toBeDefined();
      });
    });

    describe('OTP Rate Limiter', () => {
      it('should create OTP rate limiter', () => {
        const limiter = rateLimiters.otp();
        expect(limiter).toBeDefined();
      });

      it('should use identifier-based limiting', () => {
        mockReq.body = { identifier: '<EMAIL>' };
        const limiter = rateLimiters.otp(5, 300000);
        expect(limiter).toBeDefined();
      });
    });

    describe('Registration Rate Limiter', () => {
      it('should create registration rate limiter', () => {
        const limiter = rateLimiters.registration();
        expect(limiter).toBeDefined();
      });
    });

    describe('Password Reset Rate Limiter', () => {
      it('should create password reset rate limiter', () => {
        const limiter = rateLimiters.passwordReset();
        expect(limiter).toBeDefined();
      });
    });

    describe('File Upload Rate Limiter', () => {
      it('should create file upload rate limiter', () => {
        const limiter = rateLimiters.fileUpload();
        expect(limiter).toBeDefined();
      });
    });

    describe('Search Rate Limiter', () => {
      it('should create search rate limiter', () => {
        const limiter = rateLimiters.search();
        expect(limiter).toBeDefined();
      });
    });

    describe('Booking Rate Limiter', () => {
      it('should create booking rate limiter', () => {
        const limiter = rateLimiters.booking();
        expect(limiter).toBeDefined();
      });
    });
  });

  describe('Rate Limit Middleware Factory', () => {
    beforeEach(() => {
      // Mock successful rate limit check
      vi.mocked(cacheService).get.mockResolvedValue(null);
      vi.mocked(cacheService).set.mockResolvedValue(undefined);
    });

    it('should create middleware that allows requests within limit', async () => {
      const middleware = createRateLimitMiddleware('test-operation', {
        maxRequests: 5,
        windowMs: 60000
      });

      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.set).toHaveBeenCalledWith({
        'X-RateLimit-Limit': '5',
        'X-RateLimit-Remaining': expect.any(String),
        'X-RateLimit-Reset': expect.any(String)
      });
    });

    it('should block requests that exceed limit', async () => {
      const now = Date.now();
      vi.mocked(cacheService).get.mockResolvedValue({
        count: 6,
        resetTime: now + 30000,
        blocked: true,
        blockUntil: now + 10000
      });

      const middleware = createRateLimitMiddleware('test-operation', {
        maxRequests: 5,
        windowMs: 60000
      });

      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(429);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Rate limit exceeded',
        retryAfter: expect.any(Number),
        resetTime: expect.any(Number),
        timestamp: expect.any(String)
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should use custom key generator', async () => {
      const customKeyGenerator = vi.fn().mockReturnValue('custom-key');
      
      const middleware = createRateLimitMiddleware('test-operation', {
        maxRequests: 5,
        windowMs: 60000,
        keyGenerator: customKeyGenerator
      });

      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(customKeyGenerator).toHaveBeenCalledWith(mockReq);
    });

    it('should handle progressive rate limiting', async () => {
      const middleware = createRateLimitMiddleware('test-operation', {
        maxRequests: 5,
        windowMs: 60000,
        progressive: true
      });

      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should fail open on rate limiter errors', async () => {
      vi.mocked(cacheService).get.mockRejectedValue(new Error('Cache error'));

      const middleware = createRateLimitMiddleware('test-operation', {
        maxRequests: 5,
        windowMs: 60000
      });

      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('Specific Rate Limiters', () => {
    describe('IP Rate Limit', () => {
      it('should create IP-based rate limiter', () => {
        const middleware = ipRateLimit(100, 60000);
        expect(middleware).toBeDefined();
      });

      it('should use IP address as identifier', async () => {
        mockReq.ip = '***********';
        
        const middleware = ipRateLimit(100, 60000);
        await middleware(mockReq as Request, mockRes as Response, mockNext);

        expect(mockNext).toHaveBeenCalled();
      });
    });

    describe('User Rate Limit', () => {
      it('should use user ID when available', async () => {
        (mockReq as any).user = { id: 123 };
        
        const middleware = userRateLimit(1000, 60000);
        await middleware(mockReq as Request, mockRes as Response, mockNext);

        expect(mockNext).toHaveBeenCalled();
      });

      it('should fall back to IP when user not available', async () => {
        mockReq.ip = '***********';
        
        const middleware = userRateLimit(1000, 60000);
        await middleware(mockReq as Request, mockRes as Response, mockNext);

        expect(mockNext).toHaveBeenCalled();
      });
    });

    describe('Endpoint Rate Limit', () => {
      it('should create endpoint-specific rate limiter', () => {
        const middleware = endpointRateLimit('auth-login', 10, 300000);
        expect(middleware).toBeDefined();
      });

      it('should use endpoint and IP combination', async () => {
        mockReq.ip = '***********';
        
        const middleware = endpointRateLimit('auth-login', 10, 300000);
        await middleware(mockReq as Request, mockRes as Response, mockNext);

        expect(mockNext).toHaveBeenCalled();
      });
    });
  });

  describe('Rate Limiter Singleton', () => {
    it('should return same instance', () => {
      const instance1 = AdvancedRateLimiter.getInstance();
      const instance2 = AdvancedRateLimiter.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should expose main rate limiter instance', () => {
      expect(rateLimiter).toBeDefined();
      expect(rateLimiter).toBeInstanceOf(AdvancedRateLimiter);
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined IP address', async () => {
      mockReq.ip = undefined;
      
      const middleware = createRateLimitMiddleware('test-operation', {
        maxRequests: 5,
        windowMs: 60000
      });

      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle malformed cache data', async () => {
      vi.mocked(cacheService).get.mockResolvedValue({
        invalidData: true
      });

      const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
        maxRequests: 5,
        windowMs: 60000
      });

      expect(result.allowed).toBe(true);
    });

    it('should handle very large time values', async () => {
      const futureTime = Date.now() + (365 * 24 * 60 * 60 * 1000); // 1 year from now
      vi.mocked(cacheService).get.mockResolvedValue({
        count: 1,
        resetTime: futureTime,
        blocked: false
      });

      const result = await rateLimiter.checkRateLimit('test-user', 'test-operation', {
        maxRequests: 5,
        windowMs: 60000
      });

      expect(result.allowed).toBe(true);
    });

    it('should handle concurrent requests properly', async () => {
      vi.mocked(cacheService).get.mockResolvedValue(null);
      vi.mocked(cacheService).set.mockResolvedValue(undefined);

      const promises = Array(10).fill(null).map(() =>
        rateLimiter.checkRateLimit('test-user', 'test-operation', {
          maxRequests: 5,
          windowMs: 60000
        })
      );

      const results = await Promise.all(promises);
      
      // All should be allowed since cache is mocked to return null (first request)
      results.forEach(result => {
        expect(result.allowed).toBe(true);
      });
    });
  });
});