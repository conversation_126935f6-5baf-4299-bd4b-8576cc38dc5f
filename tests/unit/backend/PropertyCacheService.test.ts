import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest'
import { PropertyCacheService } from '../../../server/services/PropertyCacheService'
import { cacheService } from '../../../server/services/CacheService'

// Mock the cache service
vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    set: vi.fn(),
    get: vi.fn(),
    delete: vi.fn(),
    keys: vi.fn(),
    getStats: vi.fn()
  }
}))

// Mock the logger service
vi.mock('../../../server/services/LoggerService', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }
}))

describe('PropertyCacheService', () => {
  let propertyCacheService: PropertyCacheService

  beforeEach(() => {
    propertyCacheService = PropertyCacheService.getInstance()
    // Reset metrics to ensure clean state
    propertyCacheService.resetMetrics()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Property Search Caching', () => {
    test('should cache property search results with correct key generation', async () => {
      const searchParams = {
        location: 'Mumbai',
        date: '2024-01-15',
        minPrice: 1000,
        maxPrice: 5000,
        amenities: ['wifi', 'pool'],
        featured: true
      }
      const mockData = [{ id: 1, title: 'Test Property' }]

      await propertyCacheService.cachePropertySearch(searchParams, mockData)

      expect(cacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('search:'),
        mockData,
        600 // 10 minutes TTL
      )
    })

    test('should retrieve cached property search results', async () => {
      const searchParams = {
        location: 'Delhi',
        featured: false
      }
      const mockData = [{ id: 2, title: 'Cached Property' }]

      vi.mocked(cacheService.get).mockResolvedValue(mockData)

      const result = await propertyCacheService.getCachedPropertySearch(searchParams)

      expect(result).toEqual(mockData)
      expect(cacheService.get).toHaveBeenCalledWith(expect.stringContaining('search:'))
    })

    test('should handle cache miss for property search', async () => {
      const searchParams = { location: 'Chennai' }

      vi.mocked(cacheService.get).mockResolvedValue(null)

      const result = await propertyCacheService.getCachedPropertySearch(searchParams)

      expect(result).toBeNull()
    })

    test('should normalize search parameters for consistent caching', async () => {
      const params1 = { location: 'Mumbai', amenities: ['wifi', 'pool'] }
      const params2 = { location: 'mumbai', amenities: ['pool', 'wifi'] }
      
      await propertyCacheService.cachePropertySearch(params1, [])
      await propertyCacheService.getCachedPropertySearch(params2)

      // Should generate the same key for similar parameters
      expect(cacheService.set).toHaveBeenCalled()
      expect(cacheService.get).toHaveBeenCalled()
    })
  })

  describe('Property Details Caching', () => {
    test('should cache individual property details', async () => {
      const propertyId = 123
      const mockProperty = { id: propertyId, title: 'Test Property', description: 'A nice place' }

      await propertyCacheService.cachePropertyDetails(propertyId, mockProperty)

      expect(cacheService.set).toHaveBeenCalledWith(
        'property:123',
        mockProperty,
        1800 // 30 minutes TTL
      )
    })

    test('should retrieve cached property details', async () => {
      const propertyId = 456
      const mockProperty = { id: propertyId, title: 'Cached Property' }

      vi.mocked(cacheService.get).mockResolvedValue(mockProperty)

      const result = await propertyCacheService.getCachedPropertyDetails(propertyId)

      expect(result).toEqual(mockProperty)
      expect(cacheService.get).toHaveBeenCalledWith('property:456')
    })
  })

  describe('Featured Properties Caching', () => {
    test('should cache featured properties list', async () => {
      const mockFeatured = [
        { id: 1, title: 'Featured 1', featured: true },
        { id: 2, title: 'Featured 2', featured: true }
      ]

      await propertyCacheService.cacheFeaturedProperties(mockFeatured)

      expect(cacheService.set).toHaveBeenCalledWith(
        'featured:list',
        mockFeatured,
        3600 // 1 hour TTL
      )
    })

    test('should retrieve cached featured properties', async () => {
      const mockFeatured = [{ id: 1, title: 'Featured Property' }]

      vi.mocked(cacheService.get).mockResolvedValue(mockFeatured)

      const result = await propertyCacheService.getCachedFeaturedProperties()

      expect(result).toEqual(mockFeatured)
      expect(cacheService.get).toHaveBeenCalledWith('featured:list')
    })
  })

  describe('Availability Caching', () => {
    test('should cache availability data', async () => {
      const propertyId = 789
      const date = '2024-01-15'
      const bookingType = 'full_day'
      const isAvailable = true

      await propertyCacheService.cacheAvailability(propertyId, date, bookingType, isAvailable)

      expect(cacheService.set).toHaveBeenCalledWith(
        'availability:789:2024-01-15:full_day',
        { isAvailable: true, cachedAt: expect.any(Number) },
        300 // 5 minutes TTL
      )
    })

    test('should retrieve cached availability', async () => {
      const propertyId = 101
      const date = '2024-01-20'
      const bookingType = 'half_day'

      vi.mocked(cacheService.get).mockResolvedValue({ isAvailable: false, cachedAt: Date.now() })

      const result = await propertyCacheService.getCachedAvailability(propertyId, date, bookingType)

      expect(result).toBe(false)
      expect(cacheService.get).toHaveBeenCalledWith('availability:101:2024-01-20:half_day')
    })

    test('should return null for cache miss on availability', async () => {
      vi.mocked(cacheService.get).mockResolvedValue(null)

      const result = await propertyCacheService.getCachedAvailability(123, '2024-01-15', 'full_day')

      expect(result).toBeNull()
    })
  })

  describe('Cache Invalidation', () => {
    test('should invalidate property cache on update', async () => {
      const propertyId = 123
      const mockKeys = [
        'property:123',
        'property:123:reviews',
        'availability:123:2024-01-15:full_day'
      ]

      // Mock keys to return the mockKeys for each pattern call
      vi.mocked(cacheService.keys)
        .mockResolvedValueOnce(mockKeys)  // property:123*
        .mockResolvedValueOnce(mockKeys)  // availability:123*
        .mockResolvedValueOnce(mockKeys)  // reviews:123*
        .mockResolvedValueOnce(mockKeys)  // search:*
        .mockResolvedValueOnce(mockKeys)  // featured:*
        .mockResolvedValueOnce(mockKeys)  // prices:*
        .mockResolvedValueOnce(mockKeys)  // locations:*
      vi.mocked(cacheService.delete).mockResolvedValue(true)

      await propertyCacheService.invalidatePropertyCache(propertyId, 'update')

      expect(cacheService.keys).toHaveBeenCalledWith('property:123*')
      expect(cacheService.keys).toHaveBeenCalledWith('search:*')
      // Should call delete for all patterns that return keys
      expect(cacheService.delete).toHaveBeenCalled()
    })

    test('should invalidate owner cache', async () => {
      const ownerId = 456
      const mockKeys = ['owner:456:properties']

      vi.mocked(cacheService.keys).mockResolvedValue(mockKeys)
      vi.mocked(cacheService.delete).mockResolvedValue(true)

      await propertyCacheService.invalidateOwnerCache(ownerId)

      expect(cacheService.keys).toHaveBeenCalledWith('owner:456*')
      expect(cacheService.delete).toHaveBeenCalledTimes(mockKeys.length)
    })

    test('should invalidate all property cache', async () => {
      const mockKeys = ['search:test', 'property:123', 'featured:list']

      vi.mocked(cacheService.keys).mockResolvedValue(mockKeys)
      vi.mocked(cacheService.delete).mockResolvedValue(true)

      await propertyCacheService.invalidateAllPropertyCache()

      // Should call keys for each pattern (there are 10 patterns)
      expect(cacheService.keys).toHaveBeenCalledTimes(10)
      // Should call delete for each key in each pattern call
      expect(cacheService.delete).toHaveBeenCalled()
    })

    test('should handle different invalidation reasons', async () => {
      const propertyId = 789

      vi.mocked(cacheService.keys).mockResolvedValue([])

      // Test booking invalidation (should only invalidate availability)
      await propertyCacheService.invalidatePropertyCache(propertyId, 'booking')

      expect(cacheService.keys).toHaveBeenCalledWith('availability:789*')

      vi.clearAllMocks()

      // Test delete invalidation (should invalidate search results too)
      await propertyCacheService.invalidatePropertyCache(propertyId, 'delete')

      expect(cacheService.keys).toHaveBeenCalledWith('search:*')
    })
  })

  describe('Cache Metrics', () => {
    test('should track cache hits and misses', async () => {
      // Reset metrics first
      propertyCacheService.resetMetrics()
      
      // Simulate cache hit
      vi.mocked(cacheService.get).mockResolvedValueOnce({ id: 1 })
      await propertyCacheService.getCachedPropertyDetails(123)

      // Simulate cache miss
      vi.mocked(cacheService.get).mockResolvedValueOnce(null)
      await propertyCacheService.getCachedPropertyDetails(456)

      const metrics = propertyCacheService.getCacheMetrics()

      expect(metrics.hits).toBe(1)
      expect(metrics.misses).toBe(1)
      expect(metrics.totalRequests).toBe(2)
      expect(metrics.hitRate).toBe(50)
    })

    test('should calculate hit rate correctly', async () => {
      // Reset metrics first
      propertyCacheService.resetMetrics()

      // Simulate multiple hits
      vi.mocked(cacheService.get).mockResolvedValue({ id: 1 })
      await propertyCacheService.getCachedPropertyDetails(1)
      await propertyCacheService.getCachedPropertyDetails(2)
      await propertyCacheService.getCachedPropertyDetails(3)

      // Simulate one miss
      vi.mocked(cacheService.get).mockResolvedValue(null)
      await propertyCacheService.getCachedPropertyDetails(4)

      const metrics = propertyCacheService.getCacheMetrics()

      expect(metrics.hits).toBe(3)
      expect(metrics.misses).toBe(1)
      expect(metrics.hitRate).toBe(75) // 3/4 = 75%
    })
  })

  describe('Health Check', () => {
    test('should return healthy status with good metrics', async () => {
      // Mock good cache stats
      vi.mocked(cacheService.getStats).mockReturnValue({
        size: 500,
        maxSize: 1000,
        hitRate: 0.8,
        memoryUsage: '2.5 KB'
      })

      // Reset and simulate good hit rate
      propertyCacheService.resetMetrics()
      vi.mocked(cacheService.get).mockResolvedValue({ id: 1 })
      
      // Generate some cache hits
      for (let i = 0; i < 100; i++) {
        await propertyCacheService.getCachedPropertyDetails(i)
      }

      const health = await propertyCacheService.healthCheck()

      expect(health.status).toBe('healthy')
      expect(health.metrics.hitRate).toBeGreaterThan(90)
    })

    test('should return degraded status with low hit rate', async () => {
      // Mock cache stats
      vi.mocked(cacheService.getStats).mockReturnValue({
        size: 800,
        maxSize: 1000,
        hitRate: 0.4,
        memoryUsage: '5.2 KB'
      })

      // Reset and simulate low hit rate
      propertyCacheService.resetMetrics()
      
      // Generate mostly cache misses (10 hits, 100 misses)
      for (let i = 0; i < 10; i++) {
        vi.mocked(cacheService.get).mockResolvedValueOnce({ id: i })
        await propertyCacheService.getCachedPropertyDetails(i)
      }
      for (let i = 10; i < 110; i++) {
        vi.mocked(cacheService.get).mockResolvedValueOnce(null)
        await propertyCacheService.getCachedPropertyDetails(i)
      }

      const health = await propertyCacheService.healthCheck()

      expect(health.status).toBe('degraded')
      expect(health.metrics.hitRate).toBeLessThan(60)
    })

    test('should return unhealthy status when cache is nearly full', async () => {
      // Mock nearly full cache
      vi.mocked(cacheService.getStats).mockReturnValue({
        size: 950,
        maxSize: 1000,
        hitRate: 0.7,
        memoryUsage: '15.8 KB'
      })

      const health = await propertyCacheService.healthCheck()

      expect(health.status).toBe('unhealthy')
    })
  })

  describe('Location and Amenity Caching', () => {
    test('should cache location suggestions', async () => {
      const locations = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai']

      await propertyCacheService.cacheLocationSuggestions(locations)

      expect(cacheService.set).toHaveBeenCalledWith(
        'locations:all',
        locations,
        7200 // 2 hours TTL
      )
    })

    test('should cache amenity filters', async () => {
      const amenities = ['wifi', 'pool', 'parking', 'gym', 'kitchen']

      await propertyCacheService.cacheAmenityFilters(amenities)

      expect(cacheService.set).toHaveBeenCalledWith(
        'amenities:all',
        amenities,
        7200 // 2 hours TTL
      )
    })

    test('should retrieve cached location suggestions', async () => {
      const mockLocations = ['Goa', 'Kerala', 'Rajasthan']

      vi.mocked(cacheService.get).mockResolvedValue(mockLocations)

      const result = await propertyCacheService.getCachedLocationSuggestions()

      expect(result).toEqual(mockLocations)
      expect(cacheService.get).toHaveBeenCalledWith('locations:all')
    })
  })

  describe('Price Range Caching', () => {
    test('should cache price range statistics', async () => {
      const priceStats = {
        min: 500,
        max: 10000,
        avg: 3500,
        median: 3000
      }

      await propertyCacheService.cachePriceRanges(priceStats)

      expect(cacheService.set).toHaveBeenCalledWith(
        'prices:stats',
        priceStats,
        3600 // 1 hour TTL
      )
    })

    test('should retrieve cached price ranges', async () => {
      const mockPriceStats = {
        min: 1000,
        max: 8000,
        avg: 4000,
        median: 3500
      }

      vi.mocked(cacheService.get).mockResolvedValue(mockPriceStats)

      const result = await propertyCacheService.getCachedPriceRanges()

      expect(result).toEqual(mockPriceStats)
      expect(cacheService.get).toHaveBeenCalledWith('prices:stats')
    })
  })

  describe('Review Summary Caching', () => {
    test('should cache review summary for property', async () => {
      const propertyId = 123
      const reviewSummary = {
        averageRating: 4.5,
        totalReviews: 25,
        ratingDistribution: { 5: 15, 4: 8, 3: 2, 2: 0, 1: 0 }
      }

      await propertyCacheService.cacheReviewSummary(propertyId, reviewSummary)

      expect(cacheService.set).toHaveBeenCalledWith(
        'reviews:123',
        reviewSummary,
        1800 // 30 minutes TTL
      )
    })

    test('should retrieve cached review summary', async () => {
      const propertyId = 456
      const mockReviewSummary = {
        averageRating: 4.2,
        totalReviews: 18
      }

      vi.mocked(cacheService.get).mockResolvedValue(mockReviewSummary)

      const result = await propertyCacheService.getCachedReviewSummary(propertyId)

      expect(result).toEqual(mockReviewSummary)
      expect(cacheService.get).toHaveBeenCalledWith('reviews:456')
    })
  })

  describe('Edge Cases and Error Handling', () => {
    test('should handle cache service errors gracefully', async () => {
      vi.mocked(cacheService.get).mockRejectedValue(new Error('Cache service error'))

      // The current implementation doesn't handle errors, so this will throw
      await expect(propertyCacheService.getCachedPropertyDetails(123)).rejects.toThrow('Cache service error')
    })

    test('should handle empty search parameters', async () => {
      const emptyParams = {}

      await propertyCacheService.cachePropertySearch(emptyParams, [])

      expect(cacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('search:'),
        [],
        600
      )
    })

    test('should handle null cache values', async () => {
      vi.mocked(cacheService.get).mockResolvedValue(null)

      const result = await propertyCacheService.getCachedFeaturedProperties()

      expect(result).toBeNull()
    })
  })
})