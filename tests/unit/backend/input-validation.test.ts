import { describe, it, expect, vi, beforeEach } from 'vitest';
import { z } from 'zod';
import {
  phoneNumberSchema,
  emailSchema,
  otpCodeSchema,
  userInputSchema,
  smsTemplateSchema,
  propertyInputSchema,
  bookingInputSchema,
  fileUploadSchema,
  InputSanitizer,
  validateInput,
  validatePhoneNumber,
  validateEmail,
  validateOtpCode
} from '../../../server/utils/input-validation';

describe('Input Validation', () => {
  describe('Phone Number Validation', () => {
    it('should validate and format Indian phone numbers', () => {
      expect(phoneNumberSchema.parse('9876543210')).toBe('+************');
      expect(phoneNumberSchema.parse('************')).toBe('+************');
      expect(phoneNumberSchema.parse('+************')).toBe('+************');
    });

    it('should handle phone numbers with spaces and special characters', () => {
      expect(phoneNumberSchema.parse('98765 43210')).toBe('+************');
      expect(phoneNumberSchema.parse('(98765) 43210')).toBe('+************');
      expect(phoneNumberSchema.parse('************')).toBe('+************');
    });

    it('should reject invalid phone numbers', () => {
      expect(() => phoneNumberSchema.parse('123')).toThrow();
      expect(() => phoneNumberSchema.parse('12345678901234567890')).toThrow();
      expect(() => phoneNumberSchema.parse('abcdefghij')).toThrow();
      expect(() => phoneNumberSchema.parse('')).toThrow();
    });

    it('should handle international numbers', () => {
      expect(phoneNumberSchema.parse('+1234567890')).toBe('+1234567890');
      expect(phoneNumberSchema.parse('1234567890')).toBe('+1234567890');
    });
  });

  describe('Email Validation', () => {
    it('should validate and normalize valid emails', () => {
      expect(emailSchema.parse('<EMAIL>')).toBe('<EMAIL>');
      expect(emailSchema.parse('  <EMAIL>  ')).toBe('<EMAIL>');
      expect(emailSchema.parse('<EMAIL>')).toBe('<EMAIL>');
    });

    it('should reject invalid emails', () => {
      expect(() => emailSchema.parse('invalid-email')).toThrow();
      expect(() => emailSchema.parse('test@')).toThrow();
      expect(() => emailSchema.parse('@domain.com')).toThrow();
      expect(() => emailSchema.parse('<EMAIL>')).toThrow();
      expect(() => emailSchema.parse('')).toThrow();
    });

    it('should enforce email length limits', () => {
      expect(() => emailSchema.parse('a@b')).toThrow(); // Too short
      const longEmail = 'a'.repeat(250) + '@domain.com';
      expect(() => emailSchema.parse(longEmail)).toThrow(); // Too long
    });
  });

  describe('OTP Code Validation', () => {
    it('should validate numeric OTP codes', () => {
      expect(otpCodeSchema.parse('1234')).toBe('1234');
      expect(otpCodeSchema.parse('123456')).toBe('123456');
      expect(otpCodeSchema.parse('12345678')).toBe('12345678');
    });

    it('should trim whitespace from OTP codes', () => {
      expect(otpCodeSchema.parse('  1234  ')).toBe('1234');
    });

    it('should reject invalid OTP codes', () => {
      expect(() => otpCodeSchema.parse('123')).toThrow(); // Too short
      expect(() => otpCodeSchema.parse('123456789')).toThrow(); // Too long
      expect(() => otpCodeSchema.parse('12ab')).toThrow(); // Non-numeric
      expect(() => otpCodeSchema.parse('')).toThrow(); // Empty
    });
  });

  describe('User Input Validation', () => {
    it('should validate complete user data', () => {
      const validUser = {
        fullName: 'John Doe',
        username: 'johndoe123',
        password: 'SecurePass123!'
      };

      const result = userInputSchema.parse(validUser);
      expect(result.fullName).toBe('John Doe');
      expect(result.username).toBe('johndoe123');
      expect(result.password).toBe('SecurePass123!');
    });

    it('should normalize full names', () => {
      const user = {
        fullName: '  John   Doe  ',
        username: 'johndoe',
        password: 'SecurePass123!'
      };

      const result = userInputSchema.parse(user);
      expect(result.fullName).toBe('John Doe');
    });

    it('should reject invalid usernames', () => {
      const invalidUsers = [
        { fullName: 'John Doe', username: 'ab', password: 'SecurePass123!' }, // Too short
        { fullName: 'John Doe', username: 'user@name', password: 'SecurePass123!' }, // Invalid chars
        { fullName: 'John Doe', username: 'User Name', password: 'SecurePass123!' } // Spaces
      ];

      invalidUsers.forEach(user => {
        expect(() => userInputSchema.parse(user)).toThrow();
      });
    });

    it('should enforce password complexity', () => {
      const weakPasswords = [
        'short',        // Too short
        'nouppercase1!', // No uppercase
        'NOLOWERCASE1!', // No lowercase
        'NoNumbers!',    // No numbers
        'NoSpecialChar1' // No special characters
      ];

      weakPasswords.forEach(password => {
        const user = { fullName: 'John Doe', username: 'johndoe', password };
        expect(() => userInputSchema.parse(user)).toThrow();
      });
    });
  });

  describe('SMS Template Validation', () => {
    it('should validate complete SMS template data', () => {
      const validTemplate = {
        key: 'booking_confirmation',
        name: 'Booking Confirmation',
        content: 'Your booking for {#var#} on {#var#} is confirmed.',
        variables: ['property_name', 'booking_date'],
        dltTemplateId: '1234567890123456789'
      };

      const result = smsTemplateSchema.parse(validTemplate);
      expect(result.key).toBe('booking_confirmation');
      expect(result.variables).toEqual(['property_name', 'booking_date']);
    });

    it('should normalize template keys to lowercase', () => {
      const template = {
        key: 'BOOKING_CONFIRMATION',
        name: 'Test',
        content: 'Test content {#var#}',
        variables: ['test'],
        dltTemplateId: '1234567890123456789'
      };

      const result = smsTemplateSchema.parse(template);
      expect(result.key).toBe('booking_confirmation');
    });

    it('should validate DLT template ID format', () => {
      const invalidDltIds = [
        '123456789012345678',   // Too short
        '12345678901234567890', // Too long
        '123456789012345678a',  // Non-numeric
        ''                      // Empty
      ];

      invalidDltIds.forEach(dltTemplateId => {
        const template = {
          key: 'test',
          name: 'Test',
          content: 'Test {#var#}',
          variables: ['test'],
          dltTemplateId
        };
        expect(() => smsTemplateSchema.parse(template)).toThrow();
      });
    });

    it('should limit number of variables', () => {
      const template = {
        key: 'test',
        name: 'Test',
        content: 'Test content',
        variables: Array(15).fill('var'), // Too many variables
        dltTemplateId: '1234567890123456789'
      };

      expect(() => smsTemplateSchema.parse(template)).toThrow();
    });
  });

  describe('Property Input Validation', () => {
    it('should validate complete property data', () => {
      const validProperty = {
        title: 'Beautiful Beach House',
        description: 'A stunning beachfront property with amazing ocean views and modern amenities.',
        location: 'Goa, India',
        pricePerNight: 5000,
        coordinates: { latitude: 15.2993, longitude: 74.1240 },
        amenities: ['WiFi', 'Pool', 'AC'],
        maxGuests: 6
      };

      const result = propertyInputSchema.parse(validProperty);
      expect(result.title).toBe('Beautiful Beach House');
      expect(result.maxGuests).toBe(6);
    });

    it('should enforce price limits', () => {
      const properties = [
        { pricePerNight: 50 },   // Too low
        { pricePerNight: 150000 } // Too high
      ];

      properties.forEach(priceData => {
        const property = {
          title: 'Test Property Title Here',
          description: 'A detailed description of the property with sufficient length to meet requirements.',
          location: 'Test Location',
          maxGuests: 4,
          amenities: [],
          ...priceData
        };
        expect(() => propertyInputSchema.parse(property)).toThrow();
      });
    });

    it('should validate coordinate ranges', () => {
      const invalidCoordinates = [
        { latitude: 100, longitude: 74 },   // Invalid latitude
        { latitude: 15, longitude: 200 },   // Invalid longitude
        { latitude: -100, longitude: 74 },  // Invalid latitude
        { latitude: 15, longitude: -200 }   // Invalid longitude
      ];

      invalidCoordinates.forEach(coordinates => {
        const property = {
          title: 'Test Property Title Here',
          description: 'A detailed description of the property with sufficient length to meet requirements.',
          location: 'Test Location',
          pricePerNight: 5000,
          maxGuests: 4,
          amenities: [],
          coordinates
        };
        expect(() => propertyInputSchema.parse(property)).toThrow();
      });
    });
  });

  describe('Booking Input Validation', () => {
    it('should validate complete booking data', () => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      const validBooking = {
        propertyId: 1,
        checkIn: tomorrow.toISOString().split('T')[0],
        checkOut: dayAfter.toISOString().split('T')[0],
        guests: 2,
        totalAmount: 10000
      };

      const result = bookingInputSchema.parse(validBooking);
      expect(result.propertyId).toBe(1);
      expect(result.guests).toBe(2);
    });

    it('should reject past check-in dates', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const today = new Date();

      const booking = {
        propertyId: 1,
        checkIn: yesterday.toISOString().split('T')[0],
        checkOut: today.toISOString().split('T')[0],
        guests: 2,
        totalAmount: 10000
      };

      expect(() => bookingInputSchema.parse(booking)).toThrow();
    });

    it('should enforce guest and amount limits', () => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      const invalidBookings = [
        { guests: 0, totalAmount: 10000 },      // No guests
        { guests: 60, totalAmount: 10000 },     // Too many guests
        { guests: 2, totalAmount: 50 },         // Amount too low
        { guests: 2, totalAmount: 2000000 }     // Amount too high
      ];

      invalidBookings.forEach(bookingData => {
        const booking = {
          propertyId: 1,
          checkIn: tomorrow.toISOString().split('T')[0],
          checkOut: dayAfter.toISOString().split('T')[0],
          ...bookingData
        };
        expect(() => bookingInputSchema.parse(booking)).toThrow();
      });
    });
  });

  describe('File Upload Validation', () => {
    it('should validate allowed file types', () => {
      const validFiles = [
        { fieldname: 'image', originalname: 'test.jpg', encoding: '7bit', mimetype: 'image/jpeg', size: 1024 },
        { fieldname: 'image', originalname: 'test.png', encoding: '7bit', mimetype: 'image/png', size: 1024 },
        { fieldname: 'doc', originalname: 'test.pdf', encoding: '7bit', mimetype: 'application/pdf', size: 1024 }
      ];

      validFiles.forEach(file => {
        expect(() => fileUploadSchema.parse(file)).not.toThrow();
      });
    });

    it('should reject disallowed file types', () => {
      const invalidFiles = [
        { fieldname: 'file', originalname: 'test.exe', encoding: '7bit', mimetype: 'application/x-executable', size: 1024 },
        { fieldname: 'file', originalname: 'test.mp4', encoding: '7bit', mimetype: 'video/mp4', size: 1024 }
      ];

      invalidFiles.forEach(file => {
        expect(() => fileUploadSchema.parse(file)).toThrow();
      });
    });

    it('should enforce file size limits', () => {
      const largeFile = {
        fieldname: 'image',
        originalname: 'large.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 15 * 1024 * 1024 // 15MB
      };

      expect(() => fileUploadSchema.parse(largeFile)).toThrow();
    });
  });

  describe('Input Sanitizer', () => {
    describe('sanitizeString', () => {
      it('should remove dangerous characters', () => {
        expect(InputSanitizer.sanitizeString('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script');
        expect(InputSanitizer.sanitizeString('javascript:alert("xss")')).toBe('alert("xss")');
        expect(InputSanitizer.sanitizeString('onclick=alert("xss")')).toBe('alert("xss")');
      });

      it('should handle null bytes', () => {
        expect(InputSanitizer.sanitizeString('test\0null')).toBe('testnull');
      });

      it('should trim whitespace', () => {
        expect(InputSanitizer.sanitizeString('  test  ')).toBe('test');
      });
    });

    describe('sanitizeHtml', () => {
      it('should escape HTML entities', () => {
        expect(InputSanitizer.sanitizeHtml('<div>test</div>')).toBe('&lt;div&gt;test&lt;&#x2F;div&gt;');
        expect(InputSanitizer.sanitizeHtml('test & "quotes"')).toBe('test &amp; &quot;quotes&quot;');
      });
    });

    describe('sanitizeSql', () => {
      it('should remove SQL injection characters', () => {
        expect(InputSanitizer.sanitizeSql("test'; DROP TABLE users; --")).toBe('test DROP TABLE users');
        expect(InputSanitizer.sanitizeSql('SELECT * FROM users')).toBe(' * FROM users');
      });
    });

    describe('sanitizePhoneNumber', () => {
      it('should clean phone numbers', () => {
        expect(InputSanitizer.sanitizePhoneNumber('(*************')).toBe('9876543210');
        expect(InputSanitizer.sanitizePhoneNumber('+91 98765 43210')).toBe('+************');
      });

      it('should throw on invalid phone numbers', () => {
        expect(() => InputSanitizer.sanitizePhoneNumber('abc')).toThrow();
        expect(() => InputSanitizer.sanitizePhoneNumber('123')).toThrow();
      });
    });

    describe('sanitizeEmail', () => {
      it('should normalize emails', () => {
        expect(InputSanitizer.sanitizeEmail('<EMAIL>')).toBe('<EMAIL>');
        expect(InputSanitizer.sanitizeEmail('  <EMAIL>  ')).toBe('<EMAIL>');
      });

      it('should throw on invalid emails', () => {
        expect(() => InputSanitizer.sanitizeEmail('invalid-email')).toThrow();
      });
    });

    describe('sanitizeFilePath', () => {
      it('should clean file paths', () => {
        expect(InputSanitizer.sanitizeFilePath('../../../etc/passwd')).toBe('etcpasswd');
        expect(InputSanitizer.sanitizeFilePath('file...name')).toBe('file.name');
        expect(InputSanitizer.sanitizeFilePath('valid-file_name.txt')).toBe('valid-file_name.txt');
      });
    });

    describe('sanitizeUrl', () => {
      it('should validate and clean URLs', () => {
        expect(InputSanitizer.sanitizeUrl('https://example.com')).toBe('https://example.com/');
        expect(InputSanitizer.sanitizeUrl('http://test.org/path')).toBe('http://test.org/path');
      });

      it('should reject dangerous protocols', () => {
        expect(() => InputSanitizer.sanitizeUrl('javascript:alert("xss")')).toThrow();
        expect(() => InputSanitizer.sanitizeUrl('data:text/html,<script>')).toThrow();
      });
    });
  });

  describe('Validation Middleware', () => {
    let mockReq: any;
    let mockRes: any;
    let mockNext: any;

    beforeEach(() => {
      mockReq = { body: {}, method: 'POST', path: '/test' };
      mockRes = { status: vi.fn().mockReturnThis(), json: vi.fn() };
      mockNext = vi.fn();
    });

    it('should validate request body and call next on success', () => {
      mockReq.body = {
        fullName: 'John Doe',
        username: 'johndoe',
        password: 'SecurePass123!'
      };

      const middleware = validateInput(userInputSchema);
      middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should return 400 on validation error', () => {
      mockReq.body = {
        fullName: 'John',
        username: 'jo', // Too short
        password: 'weak'
      };

      const middleware = validateInput(userInputSchema);
      middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Helper Functions', () => {
    it('should validate phone numbers', () => {
      expect(validatePhoneNumber('9876543210')).toBe(true);
      expect(validatePhoneNumber('invalid')).toBe(false);
    });

    it('should validate emails', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('invalid-email')).toBe(false);
    });

    it('should validate OTP codes', () => {
      expect(validateOtpCode('1234')).toBe(true);
      expect(validateOtpCode('abc')).toBe(false);
    });
  });
});