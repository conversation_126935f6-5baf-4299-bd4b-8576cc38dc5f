import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import {
  ErrorBoundary,
  withDatabaseErrorBoundary,
  withAPIErrorBoundary,
  withExternalServiceErrorBoundary,
  errorBoundaryMiddleware,
  getErrorBoundaryHealth
} from '../../../server/utils/error-boundary';

describe('ErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    ErrorBoundary.clearStats();
  });

  afterEach(() => {
    ErrorBoundary.clearStats();
  });

  describe('withErrorBoundary', () => {
    it('should execute operation successfully', async () => {
      const operation = vi.fn().mockResolvedValue('success');
      
      const result = await ErrorBoundary.withErrorBoundary(operation, {
        context: { operation: 'test-operation' }
      });

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should catch and log errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' }
        })
      ).rejects.toThrow('Test error');

      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleSpy.mock.calls[0][0]).toContain('❌ ERROR:');
    });

    it('should return fallback response on error', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      const result = await ErrorBoundary.withErrorBoundary(operation, {
        context: { operation: 'test-operation' },
        fallbackResponse: 'fallback'
      });

      expect(result).toBe('fallback');
    });

    it('should call custom error handler', async () => {
      const onError = vi.fn();
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' },
          onError
        })
      ).rejects.toThrow('Test error');

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Test error' }),
        expect.objectContaining({ operation: 'test-operation' })
      );
    });

    it('should handle custom error handler failures', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const onError = vi.fn().mockImplementation(() => {
        throw new Error('Handler error');
      });
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' },
          onError
        })
      ).rejects.toThrow('Test error');

      expect(consoleSpy).toHaveBeenCalledWith('Error in custom error handler:', expect.any(Error));
    });

    it('should respect shouldReportError function', async () => {
      const shouldReportError = vi.fn().mockReturnValue(false);
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' },
          shouldReportError
        })
      ).rejects.toThrow('Test error');

      expect(shouldReportError).toHaveBeenCalledWith(expect.objectContaining({ message: 'Test error' }));
    });

    it('should track error statistics', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' }
        })
      ).rejects.toThrow();

      const stats = ErrorBoundary.getErrorStats();
      expect(stats['test-operation']).toBeDefined();
      expect(stats['test-operation'].count).toBe(1);
      expect(stats['test-operation'].lastOccurrence).toBeInstanceOf(Date);
    });

    it('should log critical errors differently', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const criticalError = new Error('ECONNREFUSED connection failed');
      const operation = vi.fn().mockRejectedValue(criticalError);
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' }
        })
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleSpy.mock.calls[0][0]).toContain('🔥 CRITICAL ERROR:');
    });

    it('should log warning errors differently', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const warningError = new Error('VALIDATION_ERROR invalid input');
      const operation = vi.fn().mockRejectedValue(warningError);
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: { operation: 'test-operation' }
        })
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleSpy.mock.calls[0][0]).toContain('⚠️ WARNING ERROR:');
    });

    it('should include context in error logs', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(
        ErrorBoundary.withErrorBoundary(operation, {
          context: {
            operation: 'test-operation',
            requestId: 'req-123',
            userId: 'user-456',
            metadata: { test: 'data' }
          }
        })
      ).rejects.toThrow();

      const logCall = consoleSpy.mock.calls[0][1];
      const logData = JSON.parse(logCall);
      
      expect(logData.operation).toBe('test-operation');
      expect(logData.context.requestId).toBe('req-123');
      expect(logData.context.userId).toBe('user-456');
      expect(logData.context.metadata.test).toBe('data');
    });
  });

  describe('withDatabaseErrorBoundary', () => {
    it('should wrap database operations', async () => {
      const operation = vi.fn().mockResolvedValue('db result');
      
      const result = await withDatabaseErrorBoundary(operation, 'select-users', 'req-123');

      expect(result).toBe('db result');
    });

    it('should not report expected database errors', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const duplicateKeyError = new Error('duplicate key value violates unique constraint');
      const operation = vi.fn().mockRejectedValue(duplicateKeyError);
      
      await expect(
        withDatabaseErrorBoundary(operation, 'insert-user')
      ).rejects.toThrow();

      // Should not report this error (no error report log)
      const reportLogs = consoleSpy.mock.calls.filter(call => 
        call[0]?.includes?.('📊 ERROR REPORT:')
      );
      expect(reportLogs).toHaveLength(0);
    });

    it('should handle connection termination gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const connectionError = new Error('terminating connection due to administrator command');
      const operation = vi.fn().mockRejectedValue(connectionError);
      
      await expect(
        withDatabaseErrorBoundary(operation, 'select-data')
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith(
        '🔄 Database connection terminated, will retry automatically'
      );
    });

    it('should handle connection timeouts', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const timeoutError = new Error('connection timeout exceeded');
      const operation = vi.fn().mockRejectedValue(timeoutError);
      
      await expect(
        withDatabaseErrorBoundary(operation, 'slow-query')
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith('⏱️ Database operation timed out');
    });
  });

  describe('withAPIErrorBoundary', () => {
    const mockRequest = {
      method: 'POST',
      originalUrl: '/api/test',
      get: vi.fn().mockReturnValue('test-agent'),
      ip: '127.0.0.1',
      id: 'req-123',
      user: { id: 'user-456' }
    } as unknown as Request;

    it('should wrap API operations with request context', async () => {
      const operation = vi.fn().mockResolvedValue('api result');
      
      const result = await withAPIErrorBoundary(operation, 'test-endpoint', mockRequest);

      expect(result).toBe('api result');
    });

    it('should return fallback response on API errors', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('API error'));
      
      const result = await withAPIErrorBoundary(
        operation,
        'test-endpoint',
        mockRequest,
        'fallback response'
      );

      expect(result).toBe('fallback response');
    });

    it('should handle validation errors', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const validationError = new Error('Invalid data');
      validationError.name = 'ValidationError';
      const operation = vi.fn().mockRejectedValue(validationError);
      
      await expect(
        withAPIErrorBoundary(operation, 'validate-input', mockRequest)
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ Validation error in api:validate-input')
      );
    });

    it('should handle rate limit errors', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const rateLimitError = new Error('rate limit exceeded');
      const operation = vi.fn().mockRejectedValue(rateLimitError);
      
      await expect(
        withAPIErrorBoundary(operation, 'rate-limited-endpoint', mockRequest)
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('🚦 Rate limit exceeded for api:rate-limited-endpoint')
      );
    });

    it('should include request metadata in context', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const operation = vi.fn().mockRejectedValue(new Error('API error'));
      
      await expect(
        withAPIErrorBoundary(operation, 'test-endpoint', mockRequest)
      ).rejects.toThrow();

      const logCall = consoleSpy.mock.calls[0][1];
      const logData = JSON.parse(logCall);
      
      expect(logData.context.metadata.method).toBe('POST');
      expect(logData.context.metadata.url).toBe('/api/test');
      expect(logData.context.metadata.ip).toBe('127.0.0.1');
      expect(logData.context.requestId).toBe('req-123');
      expect(logData.context.userId).toBe('user-456');
    });
  });

  describe('withExternalServiceErrorBoundary', () => {
    it('should wrap external service calls', async () => {
      const operation = vi.fn().mockResolvedValue('service result');
      
      const result = await withExternalServiceErrorBoundary(operation, 'payment-service');

      expect(result).toBe('service result');
    });

    it('should return fallback value on service errors', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Service unavailable'));
      
      const result = await withExternalServiceErrorBoundary(
        operation,
        'payment-service',
        'fallback value'
      );

      expect(result).toBe('fallback value');
    });

    it('should always report external service errors', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Service error'));
      
      await expect(
        withExternalServiceErrorBoundary(operation, 'sms-service')
      ).rejects.toThrow();

      // Error should be tracked in stats
      const stats = ErrorBoundary.getErrorStats();
      expect(stats['external:sms-service']).toBeDefined();
    });

    it('should handle service timeouts', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const timeoutError = new Error('request timeout after 5000ms');
      const operation = vi.fn().mockRejectedValue(timeoutError);
      
      await expect(
        withExternalServiceErrorBoundary(operation, 'slow-service')
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⏱️ slow-service timeout - consider implementing circuit breaker')
      );
    });

    it('should log external service errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const serviceError = new Error('External API failed');
      const operation = vi.fn().mockRejectedValue(serviceError);
      
      await expect(
        withExternalServiceErrorBoundary(operation, 'external-api')
      ).rejects.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith(
        '🌐 External service external-api error:',
        'External API failed'
      );
    });
  });

  describe('errorBoundaryMiddleware', () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let mockNext: NextFunction;

    beforeEach(() => {
      mockRequest = {
        method: 'GET',
        originalUrl: '/test',
        get: vi.fn().mockReturnValue('test-agent'),
        ip: '127.0.0.1',
        id: 'req-123',
        user: { id: 'user-456' }
      };

      mockResponse = {
        headersSent: false,
        status: vi.fn().mockReturnThis(),
        json: vi.fn()
      };

      mockNext = vi.fn();
    });

    it('should handle validation errors with 400 status', async () => {
      const zodError = new z.ZodError([]);
      
      errorBoundaryMiddleware(
        zodError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Validation Error',
          requestId: 'req-123'
        })
      );
    });

    it('should handle not found errors with 404 status', async () => {
      const notFoundError = new Error('User not found');
      
      errorBoundaryMiddleware(
        notFoundError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Not Found'
        })
      );
    });

    it('should handle unauthorized errors with 401 status', async () => {
      const unauthorizedError = new Error('unauthorized access');
      
      errorBoundaryMiddleware(
        unauthorizedError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Unauthorized'
        })
      );
    });

    it('should handle forbidden errors with 403 status', async () => {
      const forbiddenError = new Error('forbidden operation');
      
      errorBoundaryMiddleware(
        forbiddenError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Forbidden'
        })
      );
    });

    it('should handle rate limit errors with 429 status', async () => {
      const rateLimitError = new Error('rate limit exceeded');
      
      errorBoundaryMiddleware(
        rateLimitError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(429);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Too Many Requests'
        })
      );
    });

    it('should handle generic errors with 500 status', async () => {
      const genericError = new Error('Something went wrong');
      
      errorBoundaryMiddleware(
        genericError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Internal Server Error'
        })
      );
    });

    it('should not send response if headers already sent', async () => {
      mockResponse.headersSent = true;
      const error = new Error('Test error');
      
      errorBoundaryMiddleware(
        error,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockNext).toHaveBeenCalledWith(error);
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should include timestamp in response', async () => {
      const error = new Error('Test error');
      
      errorBoundaryMiddleware(
        error,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: expect.any(String)
        })
      );
    });

    it('should handle validation errors with custom name', async () => {
      const customValidationError = new Error('Custom validation failed');
      customValidationError.name = 'ValidationError';
      
      errorBoundaryMiddleware(
        customValidationError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Custom validation failed'
        })
      );
    });
  });

  describe('getErrorBoundaryHealth', () => {
    it('should return healthy status with no errors', () => {
      const health = getErrorBoundaryHealth();
      
      expect(health.status).toBe('healthy');
      expect(health.stats).toEqual({});
      expect(health.recommendations).toContain('Error rates are within normal limits');
    });

    it('should return warning status for elevated error rates', async () => {
      // Generate some errors
      for (let i = 0; i < 60; i++) {
        try {
          await ErrorBoundary.withErrorBoundary(
            () => Promise.reject(new Error('Test error')),
            { context: { operation: 'test-operation' } }
          );
        } catch {
          // Ignore
        }
      }

      const health = getErrorBoundaryHealth();
      
      expect(health.status).toBe('warning');
      expect(health.recommendations[0]).toContain('Elevated error rate in test-operation');
    });

    it('should return critical status for high error rates', async () => {
      // Generate many errors
      for (let i = 0; i < 120; i++) {
        try {
          await ErrorBoundary.withErrorBoundary(
            () => Promise.reject(new Error('Critical error')),
            { context: { operation: 'critical-operation' } }
          );
        } catch {
          // Ignore
        }
      }

      const health = getErrorBoundaryHealth();
      
      expect(health.status).toBe('critical');
      expect(health.recommendations[0]).toContain('High error rate in critical-operation');
    });

    it('should include error statistics', async () => {
      try {
        await ErrorBoundary.withErrorBoundary(
          () => Promise.reject(new Error('Stat test error')),
          { context: { operation: 'stats-test' } }
        );
      } catch {
        // Ignore
      }

      const health = getErrorBoundaryHealth();
      
      expect(health.stats['stats-test']).toBeDefined();
      expect(health.stats['stats-test'].count).toBe(1);
      expect(health.stats['stats-test'].lastOccurrence).toBeInstanceOf(Date);
    });
  });

  describe('error classification', () => {
    it('should identify critical errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const criticalErrors = [
        'ECONNREFUSED',
        'ENOTFOUND', 
        'ETIMEDOUT',
        'DATABASE_CONNECTION_FAILED',
        'AUTHENTICATION_FAILED'
      ];

      for (const errorType of criticalErrors) {
        const error = new Error(`Test ${errorType} error`);
        try {
          await ErrorBoundary.withErrorBoundary(
            () => Promise.reject(error),
            { context: { operation: 'critical-test' } }
          );
        } catch {
          // Ignore
        }
      }

      const criticalLogs = consoleSpy.mock.calls.filter(call => 
        call[0]?.includes?.('🔥 CRITICAL ERROR:')
      );
      
      expect(criticalLogs.length).toBe(criticalErrors.length);
    });

    it('should identify warning errors', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const warningErrors = [
        'VALIDATION_ERROR',
        'NOT_FOUND',
        'UNAUTHORIZED',
        'RATE_LIMITED'
      ];

      for (const errorType of warningErrors) {
        const error = new Error(`Test ${errorType} error`);
        try {
          await ErrorBoundary.withErrorBoundary(
            () => Promise.reject(error),
            { context: { operation: 'warning-test' } }
          );
        } catch {
          // Ignore
        }
      }

      const warningLogs = consoleSpy.mock.calls.filter(call => 
        call[0]?.includes?.('⚠️ WARNING ERROR:')
      );
      
      expect(warningLogs.length).toBe(warningErrors.length);
    });

    it('should handle errors with code properties', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const errorWithCode = new Error('Test error');
      (errorWithCode as any).code = 'ECONNREFUSED';
      
      try {
        await ErrorBoundary.withErrorBoundary(
          () => Promise.reject(errorWithCode),
          { context: { operation: 'code-test' } }
        );
      } catch {
        // Ignore
      }

      const criticalLogs = consoleSpy.mock.calls.filter(call => 
        call[0]?.includes?.('🔥 CRITICAL ERROR:')
      );
      
      expect(criticalLogs.length).toBe(1);
    });
  });

  describe('error tracking and statistics', () => {
    it('should track multiple errors for same operation', async () => {
      for (let i = 0; i < 3; i++) {
        try {
          await ErrorBoundary.withErrorBoundary(
            () => Promise.reject(new Error(`Error ${i}`)),
            { context: { operation: 'repeated-operation' } }
          );
        } catch {
          // Ignore
        }
      }

      const stats = ErrorBoundary.getErrorStats();
      expect(stats['repeated-operation'].count).toBe(3);
    });

    it('should track errors for different operations separately', async () => {
      try {
        await ErrorBoundary.withErrorBoundary(
          () => Promise.reject(new Error('Error 1')),
          { context: { operation: 'operation-1' } }
        );
      } catch {
        // Ignore
      }

      try {
        await ErrorBoundary.withErrorBoundary(
          () => Promise.reject(new Error('Error 2')),
          { context: { operation: 'operation-2' } }
        );
      } catch {
        // Ignore
      }

      const stats = ErrorBoundary.getErrorStats();
      expect(stats['operation-1'].count).toBe(1);
      expect(stats['operation-2'].count).toBe(1);
    });

    it('should clear statistics', async () => {
      try {
        await ErrorBoundary.withErrorBoundary(
          () => Promise.reject(new Error('Test error')),
          { context: { operation: 'clear-test' } }
        );
      } catch {
        // Ignore
      }

      expect(Object.keys(ErrorBoundary.getErrorStats())).toHaveLength(1);
      
      ErrorBoundary.clearStats();
      
      expect(Object.keys(ErrorBoundary.getErrorStats())).toHaveLength(0);
    });

    it('should update last occurrence timestamp', async () => {
      const startTime = Date.now();
      
      try {
        await ErrorBoundary.withErrorBoundary(
          () => Promise.reject(new Error('Timestamp test')),
          { context: { operation: 'timestamp-test' } }
        );
      } catch {
        // Ignore
      }

      const stats = ErrorBoundary.getErrorStats();
      const lastOccurrence = stats['timestamp-test'].lastOccurrence.getTime();
      
      expect(lastOccurrence).toBeGreaterThanOrEqual(startTime);
      expect(lastOccurrence).toBeLessThanOrEqual(Date.now());
    });
  });
});