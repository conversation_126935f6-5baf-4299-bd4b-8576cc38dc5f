import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import performanceRouter from '../../../server/routes/performance';


// Mock all dependencies using factory functions
vi.mock('../../../server/services/CacheService', () => ({
  cacheService: {
    getStats: vi.fn(),
    exists: vi.fn(),
    set: vi.fn(),
    get: vi.fn(),
    delete: vi.fn()
  }
}));

vi.mock('../../../server/services/CachedTemplateService', () => ({
  cachedTemplateService: {
    getCacheMetrics: vi.fn(),
    warmCache: vi.fn(),
    getAllTemplates: vi.fn()
  }
}));

vi.mock('../../../server/services/AsyncJobQueue', () => ({
  jobQueue: {
    getMetrics: vi.fn(),
    cleanup: vi.fn(),
    addJob: vi.fn()
  }
}));

vi.mock('../../../server/services/OTPRateLimitService', () => ({
  otpRateLimitService: {
    cleanup: vi.fn()
  }
}));

vi.mock('../../../server/services/CachedQueryService', () => ({
  cachedQueryService: {
    getCacheMetrics: vi.fn(),
    healthCheck: vi.fn(),
    warmCache: vi.fn(),
    cleanup: vi.fn()
  }
}));

vi.mock('../../../server/services/DLTSMSService', () => ({
  dltSMSService: {
    getServiceHealth: vi.fn()
  }
}));

vi.mock('../../../server/utils/asyncHandler', () => ({
  asyncHandler: (fn: any) => fn
}));

vi.mock('../../../server/utils/logger', () => ({
  log: vi.fn()
}));

// Import after mocking
import { cacheService } from '../../../server/services/CacheService';
import { cachedTemplateService } from '../../../server/services/CachedTemplateService';
import { jobQueue } from '../../../server/services/AsyncJobQueue';
import { otpRateLimitService } from '../../../server/services/OTPRateLimitService';
import { cachedQueryService } from '../../../server/services/CachedQueryService';
import { dltSMSService } from '../../../server/services/DLTSMSService';

describe('Performance Endpoints', () => {
  let app: express.Application;

  beforeEach(() => {
    vi.clearAllMocks();
    
    app = express();
    app.use(express.json());
    app.use('/performance', performanceRouter);

    // Setup default mock responses
    vi.mocked(cacheService).getStats.mockResolvedValue({
      hits: 100,
      misses: 20,
      size: 50,
      memoryUsage: 1024
    });

    vi.mocked(cachedTemplateService).getCacheMetrics.mockReturnValue({
      hitRate: 85,
      templateAccess: [],
      cacheStats: {}
    });

    vi.mocked(cachedQueryService).getCacheMetrics.mockReturnValue({
      queryPerformance: [
        { hitRate: 80 },
        { hitRate: 90 }
      ]
    });

    vi.mocked(jobQueue).getMetrics.mockReturnValue({
      totalJobs: 100,
      completedJobs: 95,
      failedJobs: 5,
      queueLength: 2,
      processingJobs: 1,
      averageProcessingTime: 250
    });

    vi.mocked(dltSMSService).getServiceHealth.mockResolvedValue({
      twilioConnected: true,
      dltConfigured: true,
      templatesLoaded: 5,
      availableTemplates: ['booking_confirmation'],
      jobQueueMetrics: { queueLength: 2 },
      cacheMetrics: { hitRate: 75 }
    });

    vi.mocked(cachedQueryService).healthCheck.mockResolvedValue({
      cacheAvailable: true,
      avgCacheHitRate: 85
    });
  });

  describe('GET /performance/cache', () => {
    it('should return cache performance metrics', async () => {
      const response = await request(app)
        .get('/performance/cache')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        cacheService: {
          hits: 100,
          misses: 20,
          size: 50,
          memoryUsage: 1024
        },
        templateCache: {
          hitRate: 85
        },
        summary: {
          totalCacheSize: 50,
          memoryUsage: 1024,
          templateHitRate: 85,
          queryAvgHitRate: 85 // (80 + 90) / 2
        }
      });

      expect(vi.mocked(cacheService).getStats).toHaveBeenCalled();
      expect(vi.mocked(cachedTemplateService).getCacheMetrics).toHaveBeenCalled();
      expect(vi.mocked(cachedQueryService).getCacheMetrics).toHaveBeenCalled();
    });

    it('should handle cache metrics errors', async () => {
      vi.mocked(cacheService).getStats.mockRejectedValue(new Error('Cache error'));

      const response = await request(app)
        .get('/performance/cache')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to get cache metrics'
      });
    });

    it('should handle empty query performance array', async () => {
      vi.mocked(cachedQueryService).getCacheMetrics.mockReturnValue({
        queryPerformance: []
      });

      const response = await request(app)
        .get('/performance/cache')
        .expect(200);

      expect(response.body.summary.queryAvgHitRate).toBe(0);
    });
  });

  describe('GET /performance/jobs', () => {
    it('should return job queue performance metrics', async () => {
      const response = await request(app)
        .get('/performance/jobs')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        jobQueue: {
          totalJobs: 100,
          completedJobs: 95,
          failedJobs: 5,
          queueLength: 2,
          processingJobs: 1,
          averageProcessingTime: 250
        },
        performance: {
          throughput: expect.any(Number),
          successRate: 95,
          failureRate: 5,
          avgProcessingTime: 250
        }
      });

      expect(vi.mocked(jobQueue).getMetrics).toHaveBeenCalled();
    });

    it('should handle zero total jobs', async () => {
      vi.mocked(jobQueue).getMetrics.mockReturnValue({
        totalJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        queueLength: 0,
        processingJobs: 0,
        averageProcessingTime: 0
      });

      const response = await request(app)
        .get('/performance/jobs')
        .expect(200);

      expect(response.body.performance).toMatchObject({
        throughput: 0,
        successRate: 0,
        failureRate: 0,
        avgProcessingTime: 0
      });
    });

    it('should handle job metrics errors', async () => {
      vi.mocked(jobQueue).getMetrics.mockImplementation(() => {
        throw new Error('Job queue error');
      });

      const response = await request(app)
        .get('/performance/jobs')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to get job metrics'
      });
    });
  });

  describe('GET /performance/sms', () => {
    it('should return SMS service performance metrics', async () => {
      const response = await request(app)
        .get('/performance/sms')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        smsService: {
          twilioConnected: true,
          dltConfigured: true,
          templatesLoaded: 5,
          availableTemplates: ['booking_confirmation']
        },
        performance: {
          templatesLoaded: 5,
          twilioConnected: true,
          dltConfigured: true,
          jobQueueHealthy: true, // queueLength < 100
          cacheHealthy: true // hitRate > 50
        }
      });

      expect(vi.mocked(dltSMSService).getServiceHealth).toHaveBeenCalled();
    });

    it('should identify unhealthy job queue', async () => {
      vi.mocked(dltSMSService).getServiceHealth.mockResolvedValue({
        twilioConnected: true,
        dltConfigured: true,
        templatesLoaded: 5,
        availableTemplates: ['booking_confirmation'],
        jobQueueMetrics: { queueLength: 150 }, // > 100
        cacheMetrics: { hitRate: 75 }
      });

      const response = await request(app)
        .get('/performance/sms')
        .expect(200);

      expect(response.body.performance.jobQueueHealthy).toBe(false);
    });

    it('should identify unhealthy cache', async () => {
      vi.mocked(dltSMSService).getServiceHealth.mockResolvedValue({
        twilioConnected: true,
        dltConfigured: true,
        templatesLoaded: 5,
        availableTemplates: ['booking_confirmation'],
        jobQueueMetrics: { queueLength: 2 },
        cacheMetrics: { hitRate: 30 } // < 50
      });

      const response = await request(app)
        .get('/performance/sms')
        .expect(200);

      expect(response.body.performance.cacheHealthy).toBe(false);
    });

    it('should handle SMS metrics errors', async () => {
      vi.mocked(dltSMSService).getServiceHealth.mockRejectedValue(new Error('SMS error'));

      const response = await request(app)
        .get('/performance/sms')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to get SMS metrics'
      });
    });
  });

  describe('GET /performance/rate-limits', () => {
    it('should return rate limiting metrics', async () => {
      vi.mocked(cacheService).exists.mockResolvedValue(true);

      const response = await request(app)
        .get('/performance/rate-limits')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        rateLimiting: {
          service: 'active',
          cacheHealthy: true,
          features: {
            otpRequestLimiting: true,
            otpVerificationLimiting: true,
            dailyLimits: true,
            cooldownPeriods: true
          }
        }
      });

      expect(vi.mocked(cacheService).exists).toHaveBeenCalledWith('rate_limit_test');
    });

    it('should handle unhealthy cache', async () => {
      vi.mocked(cacheService).exists.mockResolvedValue(false);

      const response = await request(app)
        .get('/performance/rate-limits')
        .expect(200);

      expect(response.body.rateLimiting.cacheHealthy).toBe(false);
    });

    it('should handle rate limit metrics errors', async () => {
      vi.mocked(cacheService).exists.mockRejectedValue(new Error('Cache error'));

      const response = await request(app)
        .get('/performance/rate-limits')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to get rate limit metrics'
      });
    });
  });

  describe('GET /performance/summary', () => {
    it('should return overall performance summary with excellent score', async () => {
      const response = await request(app)
        .get('/performance/summary')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        overallPerformance: {
          score: expect.any(Number),
          status: expect.stringMatching(/excellent|good|fair|poor/),
          components: {
            cache: expect.any(Number),
            queries: expect.any(Number),
            jobs: expect.any(Number),
            sms: expect.any(Number),
            templates: expect.any(Number)
          }
        },
        caching: {
          enabled: true,
          healthy: true,
          hitRate: 85,
          size: 50,
          memoryUsage: 1024
        },
        asyncProcessing: {
          enabled: true,
          queueLength: 2,
          processing: 1,
          avgTime: 250,
          throughput: 95
        },
        smsService: {
          operational: true,
          templatesReady: true,
          dltCompliant: true,
          asyncEnabled: true
        },
        recommendations: expect.any(Array)
      });

      // Verify all services were called
      expect(vi.mocked(cacheService).getStats).toHaveBeenCalled();
      expect(vi.mocked(cachedQueryService).healthCheck).toHaveBeenCalled();
      expect(vi.mocked(jobQueue).getMetrics).toHaveBeenCalled();
      expect(vi.mocked(dltSMSService).getServiceHealth).toHaveBeenCalled();
    });

    it('should calculate poor performance score', async () => {
      // Setup for poor performance
      vi.mocked(cacheService).getStats.mockResolvedValue({
        hits: 0,
        misses: 100,
        size: 0, // Empty cache
        memoryUsage: 0
      });

      vi.mocked(cachedQueryService).healthCheck.mockResolvedValue({
        cacheAvailable: false, // Cache not available
        avgCacheHitRate: 20 // Low hit rate
      });

      vi.mocked(jobQueue).getMetrics.mockReturnValue({
        totalJobs: 100,
        completedJobs: 30,
        failedJobs: 70, // High failure rate
        queueLength: 200, // Large queue
        processingJobs: 10,
        averageProcessingTime: 8000 // Slow processing
      });

      vi.mocked(dltSMSService).getServiceHealth.mockResolvedValue({
        twilioConnected: false, // Not connected
        dltConfigured: false,
        templatesLoaded: 0, // No templates
        availableTemplates: [],
        jobQueueMetrics: { queueLength: 200 },
        cacheMetrics: { hitRate: 20 }
      });

      const response = await request(app)
        .get('/performance/summary')
        .expect(200);

      expect(response.body.overallPerformance.status).toBe('poor');
      expect(response.body.overallPerformance.score).toBeLessThan(50);
      expect(response.body.recommendations).toContain(
        'Overall performance is below optimal. Consider reviewing system resources.'
      );
    });

    it('should generate appropriate recommendations', async () => {
      // Setup conditions that trigger specific recommendations
      vi.mocked(cacheService).getStats.mockResolvedValue({
        hits: 100,
        misses: 20,
        size: 0, // Empty cache
        memoryUsage: 0
      });

      vi.mocked(jobQueue).getMetrics.mockReturnValue({
        totalJobs: 100,
        completedJobs: 95,
        failedJobs: 5,
        queueLength: 60, // Large queue
        processingJobs: 5,
        averageProcessingTime: 6000 // Slow processing
      });

      vi.mocked(dltSMSService).getServiceHealth.mockResolvedValue({
        twilioConnected: false, // Not connected
        dltConfigured: true,
        templatesLoaded: 5,
        availableTemplates: ['template1'],
        jobQueueMetrics: { queueLength: 60 },
        cacheMetrics: { hitRate: 30 }
      });

      vi.mocked(cachedQueryService).healthCheck.mockResolvedValue({
        cacheAvailable: true,
        avgCacheHitRate: 30 // Low hit rate
      });

      const response = await request(app)
        .get('/performance/summary')
        .expect(200);

      const recommendations = response.body.recommendations;
      expect(recommendations).toContain(
        'Cache is empty. Run cache warming to improve performance.'
      );
      expect(recommendations).toContain(
        'Job queue is growing. Consider increasing processing capacity.'
      );
      expect(recommendations).toContain(
        'Job processing time is high. Optimize job handlers.'
      );
      expect(recommendations).toContain(
        'SMS service is not connected. Check Twilio configuration.'
      );
      expect(recommendations).toContain(
        'Query cache hit rate is low. Review caching strategy.'
      );
    });

    it('should handle performance summary errors', async () => {
      vi.mocked(cacheService).getStats.mockRejectedValue(new Error('Summary error'));

      const response = await request(app)
        .get('/performance/summary')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to get performance summary'
      });
    });
  });

  describe('POST /performance/cache/warm', () => {
    it('should warm cache successfully', async () => {
      vi.mocked(cachedTemplateService).warmCache.mockResolvedValue(undefined);
      vi.mocked(cachedQueryService).warmCache.mockResolvedValue(undefined);

      const response = await request(app)
        .post('/performance/cache/warm')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Cache warming completed',
        timestamp: expect.any(String)
      });

      expect(vi.mocked(cachedTemplateService).warmCache).toHaveBeenCalled();
      expect(vi.mocked(cachedQueryService).warmCache).toHaveBeenCalled();
    });

    it('should handle cache warming errors', async () => {
      vi.mocked(cachedTemplateService).warmCache.mockRejectedValue(new Error('Warming failed'));

      const response = await request(app)
        .post('/performance/cache/warm')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to warm cache'
      });
    });
  });

  describe('POST /performance/cache/cleanup', () => {
    it('should cleanup cache successfully', async () => {
      vi.mocked(otpRateLimitService).cleanup.mockResolvedValue(undefined);
      vi.mocked(cachedQueryService).cleanup.mockResolvedValue(undefined);
      vi.mocked(jobQueue).cleanup.mockResolvedValue(undefined);

      const response = await request(app)
        .post('/performance/cache/cleanup')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Cache cleanup completed',
        timestamp: expect.any(String)
      });

      expect(vi.mocked(otpRateLimitService).cleanup).toHaveBeenCalled();
      expect(vi.mocked(cachedQueryService).cleanup).toHaveBeenCalled();
      expect(vi.mocked(jobQueue).cleanup).toHaveBeenCalled();
    });

    it('should handle cache cleanup errors', async () => {
      vi.mocked(otpRateLimitService).cleanup.mockRejectedValue(new Error('Cleanup failed'));

      const response = await request(app)
        .post('/performance/cache/cleanup')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Failed to cleanup cache'
      });
    });
  });

  describe('POST /performance/test', () => {
    beforeEach(() => {
      vi.mocked(cacheService).set.mockResolvedValue(undefined);
      vi.mocked(cacheService).get.mockResolvedValue({ data: 'test_data_0' });
      vi.mocked(cacheService).delete.mockResolvedValue(true);
      vi.mocked(cachedTemplateService).getAllTemplates.mockResolvedValue([]);
      vi.mocked(jobQueue).addJob.mockResolvedValue('job_123');
    });

    it('should run basic cache performance test', async () => {
      const response = await request(app)
        .post('/performance/test')
        .send({ testType: 'cache', iterations: 3 })
        .expect(200);

      expect(response.body).toMatchObject({
        testType: 'cache',
        iterations: 3,
        results: {
          testType: 'cache',
          iterations: 3,
          startTime: expect.any(String),
          endTime: expect.any(String),
          totalDuration: expect.any(Number),
          avgOperationTime: expect.any(Number),
          successRate: 100,
          operations: expect.arrayContaining([
            expect.objectContaining({
              operation: expect.any(Number),
              duration: expect.any(Number),
              success: true
            })
          ])
        }
      });

      expect(vi.mocked(cacheService).set).toHaveBeenCalledTimes(3);
      expect(vi.mocked(cacheService).get).toHaveBeenCalledTimes(3);
      expect(vi.mocked(cacheService).delete).toHaveBeenCalledTimes(3);
    });

    it('should run template performance test', async () => {
      const response = await request(app)
        .post('/performance/test')
        .send({ testType: 'template', iterations: 2 })
        .expect(200);

      expect(response.body.results.operations).toHaveLength(2);
      expect(vi.mocked(cachedTemplateService).getAllTemplates).toHaveBeenCalledTimes(2);
    });

    it('should run jobs performance test with limited iterations', async () => {
      const response = await request(app)
        .post('/performance/test')
        .send({ testType: 'jobs', iterations: 10 }) // Should be limited to 5
        .expect(200);

      expect(response.body.results.operations).toHaveLength(5);
      expect(vi.mocked(jobQueue).addJob).toHaveBeenCalledTimes(5);
    });

    it('should use default parameters', async () => {
      const response = await request(app)
        .post('/performance/test')
        .send({})
        .expect(200);

      expect(response.body.testType).toBe('basic');
      expect(response.body.iterations).toBe(10);
    });

    it('should handle unknown test type', async () => {
      const response = await request(app)
        .post('/performance/test')
        .send({ testType: 'unknown' })
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Performance test failed'
      });
    });

    it('should handle test execution errors', async () => {
      vi.mocked(cacheService).set.mockRejectedValue(new Error('Cache set failed'));

      const response = await request(app)
        .post('/performance/test')
        .send({ testType: 'cache', iterations: 1 })
        .expect(200);

      expect(response.body.results.error).toBe('Cache set failed');
      expect(response.body.results.endTime).toBeDefined();
      expect(response.body.results.totalDuration).toBeGreaterThan(0);
    });

    it('should handle cache get returning null', async () => {
      vi.mocked(cacheService).get.mockResolvedValue(null);

      const response = await request(app)
        .post('/performance/test')
        .send({ testType: 'cache', iterations: 1 })
        .expect(200);

      expect(response.body.results.operations[0].success).toBe(false);
      expect(response.body.results.successRate).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should handle async handler errors gracefully', async () => {
      // Mock a service to throw an error
      vi.mocked(cacheService).getStats.mockImplementation(() => {
        throw new Error('Synchronous error');
      });

      const response = await request(app)
        .get('/performance/cache')
        .expect(500);

      expect(response.body.error).toBe('Failed to get cache metrics');
    });

    it('should handle Promise rejections in parallel operations', async () => {
      vi.mocked(cacheService).getStats.mockResolvedValue({
        size: 0,
        memoryUsage: 0
      });
      vi.mocked(cachedTemplateService).getCacheMetrics.mockImplementation(() => {
        throw new Error('Template cache error');
      });

      const response = await request(app)
        .get('/performance/cache')
        .expect(500);

      expect(response.body.error).toBe('Failed to get cache metrics');
    });
  });

  describe('edge cases', () => {
    it('should handle very high job queue metrics', async () => {
      vi.mocked(jobQueue).getMetrics.mockReturnValue({
        totalJobs: 1000000,
        completedJobs: 999999,
        failedJobs: 1,
        queueLength: 50000,
        processingJobs: 100,
        averageProcessingTime: 50
      });

      const response = await request(app)
        .get('/performance/jobs')
        .expect(200);

      expect(response.body.performance.successRate).toBeCloseTo(99.9999, 4);
      expect(response.body.performance.failureRate).toBeCloseTo(0.0001, 4);
    });

    it('should handle empty arrays in query performance', async () => {
      vi.mocked(cachedQueryService).getCacheMetrics.mockReturnValue({
        queryPerformance: []
      });

      const response = await request(app)
        .get('/performance/cache')
        .expect(200);

      expect(response.body.summary.queryAvgHitRate).toBe(0);
    });

    it('should handle missing optional properties', async () => {
      vi.mocked(dltSMSService).getServiceHealth.mockResolvedValue({
        twilioConnected: true,
        dltConfigured: true,
        templatesLoaded: 0,
        // Missing optional properties
      });

      const response = await request(app)
        .get('/performance/sms')
        .expect(200);

      expect(response.body.smsService.templatesLoaded).toBe(0);
    });
  });
});