import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MetricsService, MetricType, METRICS } from '../../../server/services/MetricsService';

describe('MetricsService', () => {
  let metricsService: MetricsService;

  beforeEach(() => {
    metricsService = new MetricsService();
  });

  afterEach(() => {
    metricsService.stop();
    metricsService.reset();
  });

  describe('counter metrics', () => {
    it('should increment counter correctly', () => {
      metricsService.incrementCounter('test_counter', 5, { service: 'test' });
      metricsService.incrementCounter('test_counter', 3, { service: 'test' });

      const metrics = metricsService.getAllMetrics();
      const counter = metrics.counters.find(c => c.name === 'test_counter');

      expect(counter).toBeDefined();
      expect(counter!.value).toBe(8);
      expect(counter!.type).toBe(MetricType.COUNTER);
      expect(counter!.labels).toEqual({ service: 'test' });
    });

    it('should handle counters with different labels separately', () => {
      metricsService.incrementCounter('http_requests', 1, { method: 'GET' });
      metricsService.incrementCounter('http_requests', 1, { method: 'POST' });
      metricsService.incrementCounter('http_requests', 2, { method: 'GET' });

      const metrics = metricsService.getAllMetrics();
      const counters = metrics.counters.filter(c => c.name === 'http_requests');

      expect(counters).toHaveLength(2);
      
      const getCounter = counters.find(c => c.labels?.method === 'GET');
      const postCounter = counters.find(c => c.labels?.method === 'POST');
      
      expect(getCounter!.value).toBe(3);
      expect(postCounter!.value).toBe(1);
    });
  });

  describe('gauge metrics', () => {
    it('should set gauge value correctly', () => {
      metricsService.setGauge('memory_usage', 1024, { type: 'heap' });
      metricsService.setGauge('memory_usage', 2048, { type: 'heap' }); // Override

      const metrics = metricsService.getAllMetrics();
      const gauge = metrics.gauges.find(g => g.name === 'memory_usage');

      expect(gauge).toBeDefined();
      expect(gauge!.value).toBe(2048);
      expect(gauge!.type).toBe(MetricType.GAUGE);
      expect(gauge!.labels).toEqual({ type: 'heap' });
      expect(gauge!.timestamp).toBeInstanceOf(Date);
    });
  });

  describe('histogram metrics', () => {
    it('should record histogram values correctly', () => {
      metricsService.recordHistogram('request_duration', 0.1);
      metricsService.recordHistogram('request_duration', 0.5);
      metricsService.recordHistogram('request_duration', 1.2);

      const metrics = metricsService.getAllMetrics();
      const histogram = metrics.histograms.find(h => h.name === 'request_duration');

      expect(histogram).toBeDefined();
      expect(histogram!.count).toBe(3);
      expect(histogram!.sum).toBeCloseTo(1.8, 5);
      expect(histogram!.buckets.length).toBeGreaterThan(0);
      
      // Check that values are distributed in correct buckets
      const bucket_1 = histogram!.buckets.find(b => b.le === 1);
      const bucket_2_5 = histogram!.buckets.find(b => b.le === 2.5);
      
      expect(bucket_1!.count).toBe(2); // 0.1 and 0.5
      expect(bucket_2_5!.count).toBe(3); // All values
    });

    it('should handle custom histogram configuration', () => {
      const customBuckets = [0.1, 0.5, 1.0, 2.0];
      
      // Record multiple values to test all buckets
      metricsService.recordHistogram('custom_duration', 0.05, undefined, {
        buckets: customBuckets
      });
      metricsService.recordHistogram('custom_duration', 0.3, undefined, {
        buckets: customBuckets
      });
      metricsService.recordHistogram('custom_duration', 1.5, undefined, {
        buckets: customBuckets
      });

      const metrics = metricsService.getAllMetrics();
      const histogram = metrics.histograms.find(h => h.name === 'custom_duration');

      expect(histogram).toBeDefined();
      expect(histogram!.count).toBe(3);
      
      // All buckets should exist now
      const bucketValues = histogram!.buckets.map(b => b.le).sort((a, b) => a - b);
      expect(bucketValues).toEqual(customBuckets.sort((a, b) => a - b));
    });
  });

  describe('timer functionality', () => {
    it('should time function execution', async () => {
      const mockFn = vi.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });

      const result = await metricsService.timeFunction('test_operation', mockFn, {
        service: 'test'
      });

      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledOnce();

      const allMetrics = metricsService.getAllMetrics();
      const histogram = allMetrics.histograms.find(h => h.name === 'test_operation');
      
      expect(histogram).toBeDefined();
      expect(histogram!.count).toBe(1);
      expect(histogram!.sum).toBeGreaterThan(0.09); // Should be around 100ms
    });

    it('should handle function errors and record metrics', async () => {
      const mockError = new Error('Test error');
      const mockFn = vi.fn().mockRejectedValue(mockError);

      await expect(
        metricsService.timeFunction('failing_operation', mockFn)
      ).rejects.toThrow('Test error');

      // Should still record timing
      const allMetrics = metricsService.getAllMetrics();
      const histogram = allMetrics.histograms.find(h => h.name === 'failing_operation');
      expect(histogram).toBeDefined();
      expect(histogram!.count).toBe(1);
      
      // Should record error counter
      const errorCounter = allMetrics.counters.find(c => c.name === 'failing_operation_error_total');
      expect(errorCounter).toBeDefined();
      expect(errorCounter!.value).toBe(1);
    });

    it('should start and stop timers manually', async () => {
      const timerId = metricsService.startTimer('manual_timer', { op: 'test' });
      
      // Simulate some work
      const start = Date.now();
      while (Date.now() - start < 50) {
        // Wait 50ms
      }
      
      const result = metricsService.stopTimer(timerId);
      
      expect(result).toBeDefined();
      expect(result!.duration).toBeGreaterThan(0.04); // Should be around 50ms
      expect(result!.labels).toEqual({ op: 'test' });

      const metrics = metricsService.getAllMetrics();
      const allMetrics = metricsService.getAllMetrics();
      const histogram = allMetrics.histograms.find(h => h.name === 'manual_timer');
      
      expect(histogram).toBeDefined();
      expect(histogram!.count).toBe(1);
    });
  });

  describe('Prometheus format', () => {
    it('should generate Prometheus format correctly', () => {
      metricsService.incrementCounter('http_requests_total', 10, { method: 'GET', status: '200' });
      metricsService.setGauge('memory_usage_bytes', 1024000, { type: 'heap' });
      metricsService.recordHistogram('request_duration_seconds', 0.15);

      const prometheusMetrics = metricsService.getPrometheusMetrics();

      expect(prometheusMetrics).toContain('# TYPE http_requests_total counter');
      expect(prometheusMetrics).toContain('http_requests_total{method="GET",status="200"} 10');
      
      expect(prometheusMetrics).toContain('# TYPE memory_usage_bytes gauge');
      expect(prometheusMetrics).toContain('memory_usage_bytes{type="heap"} 1024000');
      
      expect(prometheusMetrics).toContain('# TYPE request_duration_seconds histogram');
      expect(prometheusMetrics).toContain('request_duration_seconds_bucket');
      expect(prometheusMetrics).toContain('request_duration_seconds_sum');
      expect(prometheusMetrics).toContain('request_duration_seconds_count');
    });
  });

  describe('application metrics constants', () => {
    it('should have all required metric names defined', () => {
      expect(METRICS.HTTP_REQUESTS_TOTAL).toBe('http_requests_total');
      expect(METRICS.PAYMENTS_PROCESSED_TOTAL).toBe('payments_processed_total');
      expect(METRICS.BOOKINGS_CREATED_TOTAL).toBe('bookings_created_total');
      expect(METRICS.DB_CONNECTIONS_ACTIVE).toBe('db_connections_active');
      expect(METRICS.MEMORY_USAGE).toBe('memory_usage_bytes');
    });
  });

  describe('system metrics collection', () => {
    it('should collect system metrics automatically', async () => {
      // Trigger system metrics collection manually for testing
      (metricsService as any).collectSystemMetrics();
      const metrics = metricsService.getAllMetrics();
      
      // Check if memory metrics are being collected
      const memoryMetrics = metrics.gauges.filter(g => g.name === METRICS.MEMORY_USAGE);
      expect(memoryMetrics.length).toBeGreaterThan(0);
      
      // Should have different types of memory metrics
      const memoryTypes = memoryMetrics.map(m => m.labels?.type);
      expect(memoryTypes).toContain('heap_used');
      expect(memoryTypes).toContain('heap_total');
      expect(memoryTypes).toContain('rss');
    });
  });

  describe('metric key handling', () => {
    it('should handle metrics without labels', () => {
      metricsService.incrementCounter('simple_counter');
      
      const metrics = metricsService.getAllMetrics();
      const counter = metrics.counters.find(c => c.name === 'simple_counter');
      
      expect(counter).toBeDefined();
      expect(counter!.labels).toBeUndefined();
    });

    it('should handle empty labels', () => {
      metricsService.incrementCounter('empty_labels_counter', 1, {});
      
      const metrics = metricsService.getAllMetrics();
      const counter = metrics.counters.find(c => c.name === 'empty_labels_counter');
      
      expect(counter).toBeDefined();
    });
  });

  describe('reset functionality', () => {
    it('should reset all metrics', () => {
      metricsService.incrementCounter('test_counter', 5);
      metricsService.setGauge('test_gauge', 100);
      metricsService.recordHistogram('test_histogram', 0.5);

      let metrics = metricsService.getAllMetrics();
      expect(metrics.counters.length).toBeGreaterThan(0);
      expect(metrics.gauges.length).toBeGreaterThan(0);
      expect(metrics.histograms.length).toBeGreaterThan(0);

      metricsService.reset();

      metrics = metricsService.getAllMetrics();
      expect(metrics.counters.length).toBe(0);
      expect(metrics.gauges.length).toBe(0);
      expect(metrics.histograms.length).toBe(0);
    });
  });
});