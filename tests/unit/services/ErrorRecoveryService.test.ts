import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ErrorRecoveryService, OperationType } from '../../../server/services/ErrorRecoveryService';

describe('ErrorRecoveryService', () => {
  let errorRecoveryService: ErrorRecoveryService;

  beforeEach(() => {
    errorRecoveryService = new ErrorRecoveryService();
  });

  afterEach(() => {
    errorRecoveryService.stop();
  });

  describe('executeWithRecovery', () => {
    it('should execute primary operation successfully', async () => {
      const mockResult = { success: true, data: 'test' };
      const primaryOperation = vi.fn().mockResolvedValue(mockResult);
      
      const strategy = {
        operationType: OperationType.PAYMENT_PROCESSING,
        primaryOperation,
        fallbackOperations: []
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result.success).toBe(true);
      expect(result.result).toBe(mockResult);
      expect(result.usedFallback).toBe(false);
      expect(result.errors).toHaveLength(0);
      expect(primaryOperation).toHaveBeenCalledOnce();
    });

    it('should use fallback when primary operation fails', async () => {
      const primaryError = new Error('Primary operation failed');
      const fallbackResult = { success: true, data: 'fallback' };
      
      const primaryOperation = vi.fn().mockRejectedValue(primaryError);
      const fallbackOperation = vi.fn().mockResolvedValue(fallbackResult);
      
      const strategy = {
        operationType: OperationType.PAYMENT_PROCESSING,
        primaryOperation,
        fallbackOperations: [fallbackOperation],
        onFallbackUsed: vi.fn()
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result.success).toBe(true);
      expect(result.result).toBe(fallbackResult);
      expect(result.usedFallback).toBe(true);
      expect(result.fallbackIndex).toBe(0);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Primary operation failed');
      
      expect(primaryOperation).toHaveBeenCalledOnce();
      expect(fallbackOperation).toHaveBeenCalledOnce();
      expect(strategy.onFallbackUsed).toHaveBeenCalledWith(0, primaryError);
    });

    it('should try all fallbacks when operations fail', async () => {
      const primaryError = new Error('Primary failed');
      const fallback1Error = new Error('Fallback 1 failed');
      const fallback2Result = { success: true, data: 'fallback2' };
      
      const primaryOperation = vi.fn().mockRejectedValue(primaryError);
      const fallbackOperation1 = vi.fn().mockRejectedValue(fallback1Error);
      const fallbackOperation2 = vi.fn().mockResolvedValue(fallback2Result);
      
      const strategy = {
        operationType: OperationType.BOOKING_CREATION,
        primaryOperation,
        fallbackOperations: [fallbackOperation1, fallbackOperation2],
        onFallbackUsed: vi.fn()
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result.success).toBe(true);
      expect(result.result).toBe(fallback2Result);
      expect(result.usedFallback).toBe(true);
      expect(result.fallbackIndex).toBe(1);
      expect(result.errors).toHaveLength(2);
      
      expect(primaryOperation).toHaveBeenCalledOnce();
      expect(fallbackOperation1).toHaveBeenCalledOnce();
      expect(fallbackOperation2).toHaveBeenCalledOnce();
      expect(strategy.onFallbackUsed).toHaveBeenCalledWith(1, fallback1Error);
    });

    it('should fail when all operations fail', async () => {
      const primaryError = new Error('Primary failed');
      const fallbackError = new Error('Fallback failed');
      
      const primaryOperation = vi.fn().mockRejectedValue(primaryError);
      const fallbackOperation = vi.fn().mockRejectedValue(fallbackError);
      
      const strategy = {
        operationType: OperationType.EMAIL_NOTIFICATION,
        primaryOperation,
        fallbackOperations: [fallbackOperation],
        onAllFailed: vi.fn()
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result.success).toBe(false);
      expect(result.usedFallback).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.totalAttempts).toBe(2);
      
      expect(strategy.onAllFailed).toHaveBeenCalledWith([primaryError, fallbackError]);
    });
  });

  describe('queueFailedOperation', () => {
    it('should queue failed operation for retry', async () => {
      const operationId = 'test-operation-123';
      const mockOperation = vi.fn().mockResolvedValue('success');
      const metadata = { bookingId: 123, userId: 456 };

      await errorRecoveryService.queueFailedOperation(
        operationId,
        OperationType.PAYMENT_PROCESSING,
        mockOperation,
        metadata
      );

      const status = errorRecoveryService.getQueueStatus();
      
      expect(status.queueSize).toBe(1);
      expect(status.operations).toHaveLength(1);
      expect(status.operations[0].id).toBe(operationId);
      expect(status.operations[0].operationType).toBe(OperationType.PAYMENT_PROCESSING);
      expect(status.operations[0].retryCount).toBe(0);
    });
  });

  describe('getQueueStatus', () => {
    it('should return current queue status', () => {
      const status = errorRecoveryService.getQueueStatus();
      
      expect(status).toHaveProperty('queueSize');
      expect(status).toHaveProperty('isProcessing');
      expect(status).toHaveProperty('operations');
      expect(Array.isArray(status.operations)).toBe(true);
    });
  });

  describe('static factory methods', () => {
    it('should create payment strategy', () => {
      const primaryProcessor = vi.fn();
      const fallbackProcessor = vi.fn();
      
      const strategy = ErrorRecoveryService.createPaymentStrategy(
        primaryProcessor,
        fallbackProcessor
      );

      expect(strategy.operationType).toBe(OperationType.PAYMENT_PROCESSING);
      expect(strategy.primaryOperation).toBe(primaryProcessor);
      expect(strategy.fallbackOperations).toHaveLength(2); // fallback + emergency
      expect(strategy.retryConfig).toBeDefined();
    });

    it('should create booking strategy', () => {
      const primaryCreator = vi.fn();
      const fallbackCreator = vi.fn();
      
      const strategy = ErrorRecoveryService.createBookingStrategy(
        primaryCreator,
        fallbackCreator
      );

      expect(strategy.operationType).toBe(OperationType.BOOKING_CREATION);
      expect(strategy.primaryOperation).toBe(primaryCreator);
      expect(strategy.fallbackOperations).toHaveLength(1);
      expect(strategy.retryConfig).toBeDefined();
    });

    it('should create notification strategy', () => {
      const primaryNotification = vi.fn();
      const fallbackNotifications = [vi.fn(), vi.fn()];
      
      const strategy = ErrorRecoveryService.createNotificationStrategy(
        primaryNotification,
        fallbackNotifications
      );

      expect(strategy.operationType).toBe(OperationType.EMAIL_NOTIFICATION);
      expect(strategy.primaryOperation).toBe(primaryNotification);
      expect(strategy.fallbackOperations).toHaveLength(2);
      expect(strategy.retryConfig).toBeDefined();
    });
  });

  describe('retry delay calculation', () => {
    it('should calculate exponential backoff correctly', () => {
      // Access private method through reflection for testing
      const calculateRetryDelay = (errorRecoveryService as any).calculateRetryDelay.bind(errorRecoveryService);
      
      const delay0 = calculateRetryDelay(0);
      const delay1 = calculateRetryDelay(1);
      const delay2 = calculateRetryDelay(2);
      const delay10 = calculateRetryDelay(10);

      expect(delay0).toBe(60000); // 1 minute base
      expect(delay1).toBe(120000); // 2 minutes
      expect(delay2).toBe(240000); // 4 minutes
      expect(delay10).toBe(3600000); // Max 1 hour
    });
  });
});