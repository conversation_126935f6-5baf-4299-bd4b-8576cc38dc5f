import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  StandardizedErrorService,
  AppError,
  ErrorCode,
  ErrorSeverity,
  ErrorCategory,
  ValidationError,
  BookingConflictError,
  PaymentError,
  DatabaseError,
  ExternalServiceError
} from '../../../server/services/StandardizedErrorService';

// Mock the logger
vi.mock('../../../server/services/LoggerService', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
  }
}));

describe('StandardizedErrorService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('AppError class', () => {
    it('should create AppError with all properties', () => {
      const error = new AppError({
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Test validation error',
        severity: ErrorSeverity.LOW,
        category: ErrorCategory.USER_ERROR,
        details: { field: 'email' },
        userMessage: 'Please check your email',
        retryable: false,
        requestId: 'req-123',
        userId: 456,
        context: { action: 'user_registration' }
      });

      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.message).toBe('Test validation error');
      expect(error.severity).toBe(ErrorSeverity.LOW);
      expect(error.category).toBe(ErrorCategory.USER_ERROR);
      expect(error.details).toEqual({ field: 'email' });
      expect(error.userMessage).toBe('Please check your email');
      expect(error.retryable).toBe(false);
      expect(error.requestId).toBe('req-123');
      expect(error.userId).toBe(456);
      expect(error.context).toEqual({ action: 'user_registration' });
      expect(error.timestamp).toBeInstanceOf(Date);
    });

    it('should use default user message when not provided', () => {
      const error = new AppError({
        code: ErrorCode.UNAUTHORIZED,
        message: 'Invalid token',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SECURITY_ERROR
      });

      expect(error.userMessage).toBe('Authentication required. Please log in.');
    });

    it('should serialize to JSON correctly', () => {
      const error = new AppError({
        code: ErrorCode.PAYMENT_FAILED,
        message: 'Payment processing failed',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.EXTERNAL_ERROR,
        details: { paymentId: 'pay_123' },
        retryable: true,
        requestId: 'req-456'
      });

      const json = error.toJSON();

      expect(json).toEqual({
        code: ErrorCode.PAYMENT_FAILED,
        message: 'Payment processing failed',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.EXTERNAL_ERROR,
        details: { paymentId: 'pay_123' },
        userMessage: 'Payment processing failed. Please try again.',
        retryable: true,
        timestamp: error.timestamp,
        requestId: 'req-456',
        userId: undefined,
        context: {}
      });
    });
  });

  describe('specific error classes', () => {
    it('should create ValidationError correctly', () => {
      const error = new ValidationError('Invalid email format', { field: 'email' }, 'req-123');

      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.severity).toBe(ErrorSeverity.LOW);
      expect(error.category).toBe(ErrorCategory.USER_ERROR);
      expect(error.retryable).toBe(false);
      expect(error.details).toEqual({ field: 'email' });
      expect(error.requestId).toBe('req-123');
    });

    it('should create BookingConflictError correctly', () => {
      const conflictingBookings = [{ id: 1, date: '2024-01-01' }];
      const suggestedAlternatives = [new Date('2024-01-02')];
      
      const error = new BookingConflictError(
        'Booking conflict detected',
        conflictingBookings,
        suggestedAlternatives,
        'req-456'
      );

      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toBe(ErrorCode.BOOKING_CONFLICT);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.category).toBe(ErrorCategory.BUSINESS_LOGIC);
      expect(error.retryable).toBe(false);
      expect(error.details).toEqual({
        conflictingBookings,
        suggestedAlternatives
      });
    });

    it('should create PaymentError correctly', () => {
      const paymentDetails = { gatewayId: 'gw_123', amount: 1000 };
      const error = new PaymentError(
        'Payment gateway timeout',
        paymentDetails,
        true,
        'req-789'
      );

      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toBe(ErrorCode.PAYMENT_FAILED);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.category).toBe(ErrorCategory.EXTERNAL_ERROR);
      expect(error.retryable).toBe(true);
      expect(error.details).toEqual(paymentDetails);
      expect(error.requestId).toBe('req-789');
    });

    it('should create DatabaseError correctly', () => {
      const error = new DatabaseError(
        'Connection pool exhausted',
        'getUserBookings',
        { poolSize: 10 },
        'req-101'
      );

      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toBe(ErrorCode.DATABASE_CONNECTION_ERROR);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.category).toBe(ErrorCategory.SYSTEM_ERROR);
      expect(error.retryable).toBe(true);
      expect(error.details).toEqual({ operation: 'getUserBookings', poolSize: 10 });
    });

    it('should create ExternalServiceError correctly', () => {
      const error = new ExternalServiceError(
        'SMS service unavailable',
        'twilio',
        503,
        true,
        'req-202'
      );

      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toBe(ErrorCode.EXTERNAL_SERVICE_ERROR);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.category).toBe(ErrorCategory.EXTERNAL_ERROR);
      expect(error.retryable).toBe(true);
      expect(error.details).toEqual({ service: 'twilio', statusCode: 503 });
    });
  });

  describe('standardizeError method', () => {
    it('should return AppError as-is', () => {
      const originalError = new AppError({
        code: ErrorCode.UNAUTHORIZED,
        message: 'Access denied',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SECURITY_ERROR
      });

      const result = StandardizedErrorService.standardizeError(originalError);
      expect(result).toBe(originalError);
    });

    it('should categorize database errors', () => {
      const dbError = new Error('Connection timeout to database');
      const result = StandardizedErrorService.standardizeError(dbError, { action: 'test' }, 'req-123');

      expect(result).toBeInstanceOf(DatabaseError);
      expect(result.code).toBe(ErrorCode.DATABASE_CONNECTION_ERROR);
      expect(result.details.originalError).toBe('Error');
      expect(result.requestId).toBe('req-123');
    });

    it('should categorize validation errors', () => {
      const validationError = new Error('Validation failed for field email');
      const result = StandardizedErrorService.standardizeError(validationError);

      expect(result).toBeInstanceOf(ValidationError);
      expect(result.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(result.details.originalError).toBe('Error');
    });

    it('should categorize payment errors', () => {
      const paymentError = new Error('Razorpay payment failed');
      const result = StandardizedErrorService.standardizeError(paymentError);

      expect(result).toBeInstanceOf(PaymentError);
      expect(result.code).toBe(ErrorCode.PAYMENT_FAILED);
      expect(result.retryable).toBe(true);
    });

    it('should categorize timeout errors', () => {
      const timeoutError = new Error('Request timeout after 30s');
      const result = StandardizedErrorService.standardizeError(timeoutError);

      expect(result).toBeInstanceOf(ExternalServiceError);
      expect(result.code).toBe(ErrorCode.EXTERNAL_SERVICE_ERROR);
      expect(result.retryable).toBe(true);
    });

    it('should handle non-Error objects', () => {
      const result1 = StandardizedErrorService.standardizeError('String error');
      expect(result1.message).toBe('String error');
      expect(result1.code).toBe(ErrorCode.INTERNAL_SERVER_ERROR);

      const result2 = StandardizedErrorService.standardizeError({ custom: 'object' });
      expect(result2.message).toBe('Unknown error occurred');
      expect(result2.code).toBe(ErrorCode.INTERNAL_SERVER_ERROR);
    });
  });

  describe('logError method', () => {
    it('should log critical errors with error level', async () => {
      const { logger } = await import('../../../server/services/LoggerService');
      
      const error = new AppError({
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        message: 'Critical system failure',
        severity: ErrorSeverity.CRITICAL,
        category: ErrorCategory.SYSTEM_ERROR
      });

      StandardizedErrorService.logError(error);

      expect(logger.error).toHaveBeenCalledWith(
        'Critical error occurred', 
        expect.any(Error), 
        'error-handling', 
        {
          code: error.code,
          message: error.message,
          severity: error.severity,
          category: error.category,
          details: error.details,
          context: error.context,
          requestId: error.requestId,
          userId: error.userId,
          timestamp: error.timestamp
        }
      );
    });

    it('should log medium severity errors with warn level', async () => {
      const { logger } = await import('../../../server/services/LoggerService');
      
      const error = new AppError({
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        message: 'External service temporary unavailable',
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.EXTERNAL_ERROR
      });

      StandardizedErrorService.logError(error);

      expect(logger.warn).toHaveBeenCalledWith('Medium severity error', 'error-handling', expect.any(Object));
    });

    it('should log low severity errors with info level', async () => {
      const { logger } = await import('../../../server/services/LoggerService');
      
      const error = new ValidationError('Invalid input');

      StandardizedErrorService.logError(error);

      expect(logger.info).toHaveBeenCalledWith('Low severity error', 'error-handling', expect.any(Object));
    });
  });

  describe('formatForResponse method', () => {
    it('should format error for API response', () => {
      const error = new AppError({
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Internal validation message',
        severity: ErrorSeverity.LOW,
        category: ErrorCategory.USER_ERROR,
        userMessage: 'Please check your input',
        retryable: false,
        requestId: 'req-123'
      });

      const formatted = StandardizedErrorService.formatForResponse(error);

      expect(formatted).toEqual({
        error: {
          code: ErrorCode.VALIDATION_ERROR,
          message: 'Please check your input',
          requestId: 'req-123'
        }
      });
    });

    it('should include retryable info for retryable errors', () => {
      const error = new PaymentError('Payment failed', {}, true, 'req-456');
      const formatted = StandardizedErrorService.formatForResponse(error);

      expect(formatted.error.details).toEqual({ retryable: true });
    });
  });

  describe('shouldAlert method', () => {
    it('should alert for critical errors', () => {
      const error = new AppError({
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        message: 'Critical failure',
        severity: ErrorSeverity.CRITICAL,
        category: ErrorCategory.SYSTEM_ERROR
      });

      expect(StandardizedErrorService.shouldAlert(error)).toBe(true);
    });

    it('should alert for high severity system errors', () => {
      const error = new DatabaseError('Database crashed', 'critical_operation');

      expect(StandardizedErrorService.shouldAlert(error)).toBe(true);
    });

    it('should not alert for high severity business logic errors', () => {
      const error = new BookingConflictError('Booking conflict', [], []);

      expect(StandardizedErrorService.shouldAlert(error)).toBe(false);
    });

    it('should not alert for low/medium severity errors', () => {
      const validationError = new ValidationError('Invalid email');
      const serviceError = new ExternalServiceError('Service timeout', 'sms', 503);

      expect(StandardizedErrorService.shouldAlert(validationError)).toBe(false);
      expect(StandardizedErrorService.shouldAlert(serviceError)).toBe(false);
    });
  });

  describe('getMetricsData method', () => {
    it('should extract metrics data from error', () => {
      const error = new AppError({
        code: ErrorCode.PAYMENT_FAILED,
        message: 'Payment processing failed',
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.EXTERNAL_ERROR,
        retryable: true,
        userId: 123
      });

      const metricsData = StandardizedErrorService.getMetricsData(error);

      expect(metricsData).toEqual({
        error_code: ErrorCode.PAYMENT_FAILED,
        error_severity: ErrorSeverity.HIGH,
        error_category: ErrorCategory.EXTERNAL_ERROR,
        retryable: true,
        user_id: '123'
      });
    });

    it('should handle anonymous users', () => {
      const error = new ValidationError('Invalid input');
      const metricsData = StandardizedErrorService.getMetricsData(error);

      expect(metricsData.user_id).toBe('anonymous');
    });
  });
});