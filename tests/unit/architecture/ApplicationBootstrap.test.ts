/**
 * Application Bootstrap Tests
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { ApplicationBootstrap } from '../../../server/core/ApplicationBootstrap';
import { TOKENS } from '../../../server/core/Container';
import { ILogger } from '../../../server/core/interfaces/ILogger';
import { shutdownContainer } from '../../../server/core/ContainerConfiguration';

describe('Application Bootstrap', () => {
  let app: ApplicationBootstrap;

  beforeEach(async () => {
    // Clear any existing container
    try {
      await shutdownContainer();
    } catch (error) {
      // Ignore errors if container wasn't initialized
    }
    
    app = new ApplicationBootstrap();
    await app.initialize();
  });

  afterEach(async () => {
    if (app) {
      await app.shutdown();
    }
    
    // Clear the container after each test
    try {
      await shutdownContainer();
    } catch (error) {
      // Ignore errors if container wasn't initialized
    }
  });

  describe('Initialization', () => {
    test('should initialize container with core services', () => {
      const container = app.getContainer();
      
      expect(container.isRegistered(TOKENS.LOGGER)).toBe(true);
      expect(container.isRegistered(TOKENS.CACHE)).toBe(true);
      expect(container.isRegistered(TOKENS.CONFIG)).toBe(true);
    });

    test('should resolve logger service', () => {
      const container = app.getContainer();
      const logger = container.resolve<ILogger>(TOKENS.LOGGER);
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
    });

    test('should create request-scoped containers', () => {
      const scope1 = app.createRequestScope();
      const scope2 = app.createRequestScope();
      
      expect(scope1).not.toBe(scope2);
      expect(scope1.isRegistered(TOKENS.LOGGER)).toBe(true);
      expect(scope2.isRegistered(TOKENS.LOGGER)).toBe(true);
    });
  });

  describe('Service Resolution', () => {
    test('should resolve services from container', () => {
      const container = app.getContainer();
      
      // Test core services
      expect(() => container.resolve(TOKENS.LOGGER)).not.toThrow();
      expect(() => container.resolve(TOKENS.CACHE)).not.toThrow();
    });

    test('should handle service resolution errors gracefully', () => {
      const container = app.getContainer();
      
      expect(() => {
        container.resolve('NonExistentService');
      }).toThrow();
    });
  });

  describe('Configuration', () => {
    test('should have default configuration', () => {
      const container = app.getContainer();
      const config = container.resolve(TOKENS.CONFIG);
      
      expect(config).toBeDefined();
      expect(config.environment).toBeDefined();
      expect(config.cache).toBeDefined();
      expect(config.logging).toBeDefined();
    });

    test('should configure services based on environment', () => {
      const container = app.getContainer();
      const config = container.resolve(TOKENS.CONFIG);
      
      // Test environment-specific configuration
      if (config.environment === 'development' || config.environment === 'test') {
        expect(config.logging.level).toBe('debug');
        expect(config.logging.format).toBe('console');
      } else {
        expect(config.logging.level).toBe('info');
        expect(config.logging.format).toBe('json');
      }
    });
  });

  describe('Migration Helper', () => {
    test('should provide migration helper', () => {
      const migrationHelper = app.getMigrationHelper();
      expect(migrationHelper).toBeDefined();
      expect(typeof migrationHelper.createRouteHandlerWrapper).toBe('function');
    });

    test('should create route handler wrapper', () => {
      const migrationHelper = app.getMigrationHelper();
      const mockHandler = vi.fn();
      
      const wrapper = migrationHelper.createRouteHandlerWrapper(mockHandler);
      expect(typeof wrapper).toBe('function');
    });
  });

  describe('Lifecycle Management', () => {
    test('should handle graceful shutdown', async () => {
      const container = app.getContainer();
      const logger = container.resolve<ILogger>(TOKENS.LOGGER);
      
      const infoSpy = vi.spyOn(logger, 'info');
      
      await app.shutdown();
      
      // Should not throw and should log shutdown message
      expect(infoSpy).toHaveBeenCalledWith(
        expect.stringContaining('shutdown'),
        'ApplicationBootstrap'
      );
    });

    test('should cleanup resources on shutdown', async () => {
      let disposed = false;
      
      class TestService {
        async dispose() {
          disposed = true;
        }
      }
      
      const container = app.getContainer();
      container.register('TestService', TestService, 'singleton');
      container.resolve('TestService'); // Create instance
      
      await app.shutdown();
      expect(disposed).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle initialization errors', async () => {
      // Create a new app instance that might fail
      const failingApp = new ApplicationBootstrap();
      
      // Mock a service that throws during registration
      const container = failingApp.getContainer();
      const logger = container.resolve<ILogger>(TOKENS.LOGGER);
      const errorSpy = vi.spyOn(logger, 'error');
      
      // Try to register a service that throws
      container.registerFactory('FailingService', () => {
        throw new Error('Service registration failed');
      });
      
      try {
        container.resolve('FailingService');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
      
      await failingApp.shutdown();
    });

    test('should handle shutdown errors gracefully', async () => {
      const container = app.getContainer();
      const logger = container.resolve<ILogger>(TOKENS.LOGGER);
      
      // Register a service that throws during disposal
      class FailingService {
        async dispose() {
          throw new Error('Disposal failed');
        }
      }
      
      container.register('FailingService', FailingService, 'singleton');
      container.resolve('FailingService');
      
      // Should handle disposal error gracefully (not throw)
      await expect(app.shutdown()).resolves.not.toThrow();
    });
  });
});