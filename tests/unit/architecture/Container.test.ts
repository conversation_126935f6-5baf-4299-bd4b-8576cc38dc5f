/**
 * Dependency Injection Container Tests
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { Container, TOKENS } from '../../../server/core/Container';
import { ILogger } from '../../../server/core/interfaces/ILogger';
import { ICache } from '../../../server/core/interfaces/ICache';

describe('Dependency Injection Container', () => {
  let container: Container;

  beforeEach(() => {
    container = new Container();
  });

  afterEach(async () => {
    await container.dispose();
  });

  describe('Service Registration', () => {
    test('should register and resolve transient services', () => {
      class TestService {
        getValue() { return 'test'; }
      }

      container.register('TestService', TestService, 'transient');

      const instance1 = container.resolve<TestService>('TestService');
      const instance2 = container.resolve<TestService>('TestService');

      expect(instance1).toBeInstanceOf(TestService);
      expect(instance2).toBeInstanceOf(TestService);
      expect(instance1).not.toBe(instance2); // Different instances
      expect(instance1.getValue()).toBe('test');
    });

    test('should register and resolve singleton services', () => {
      class TestService {
        getValue() { return 'singleton'; }
      }

      container.register('TestService', TestService, 'singleton');

      const instance1 = container.resolve<TestService>('TestService');
      const instance2 = container.resolve<TestService>('TestService');

      expect(instance1).toBeInstanceOf(TestService);
      expect(instance2).toBeInstanceOf(TestService);
      expect(instance1).toBe(instance2); // Same instance
    });

    test('should register and resolve scoped services', () => {
      class TestService {
        getValue() { return 'scoped'; }
      }

      container.register('TestService', TestService, 'scoped');

      const instance1 = container.resolve<TestService>('TestService');
      const instance2 = container.resolve<TestService>('TestService');

      expect(instance1).toBeInstanceOf(TestService);
      expect(instance2).toBeInstanceOf(TestService);
      expect(instance1).toBe(instance2); // Same instance within scope
    });

    test('should register services with factory functions', () => {
      interface ITestService {
        getValue(): string;
      }

      container.registerFactory<ITestService>(
        'ITestService',
        (container) => ({
          getValue: () => 'factory'
        }),
        'singleton'
      );

      const instance = container.resolve<ITestService>('ITestService');
      expect(instance.getValue()).toBe('factory');
    });

    test('should register service instances', () => {
      const testInstance = { getValue: () => 'instance' };
      container.registerInstance('TestInstance', testInstance);

      const resolved = container.resolve('TestInstance');
      expect(resolved).toBe(testInstance);
    });
  });

  describe('Dependency Injection', () => {
    test('should inject dependencies into services', () => {
      class Logger {
        log(message: string) { return `LOG: ${message}`; }
      }

      class UserService {
        constructor(private logger: Logger) {}
        createUser() { return this.logger.log('User created'); }
      }

      container.register('Logger', Logger, 'singleton');
      container.register('UserService', UserService, 'transient', ['Logger']);

      const userService = container.resolve<UserService>('UserService');
      expect(userService.createUser()).toBe('LOG: User created');
    });

    test('should handle nested dependencies', () => {
      class Database {
        query() { return 'DB_RESULT'; }
      }

      class Logger {
        log(message: string) { return `LOG: ${message}`; }
      }

      class UserRepository {
        constructor(private db: Database, private logger: Logger) {}
        
        getUser() {
          const result = this.db.query();
          this.logger.log('User fetched');
          return result;
        }
      }

      container.register('Database', Database, 'singleton');
      container.register('Logger', Logger, 'singleton');
      container.register('UserRepository', UserRepository, 'scoped', ['Database', 'Logger']);

      const userRepo = container.resolve<UserRepository>('UserRepository');
      expect(userRepo.getUser()).toBe('DB_RESULT');
    });
  });

  describe('Scoped Containers', () => {
    test('should create independent scoped containers', () => {
      class TestService {
        private static counter = 0;
        public id: number;
        
        constructor() {
          this.id = ++TestService.counter;
        }
      }

      container.register('TestService', TestService, 'scoped');

      const scope1 = container.createScope();
      const scope2 = container.createScope();

      const instance1a = scope1.resolve<TestService>('TestService');
      const instance1b = scope1.resolve<TestService>('TestService');
      const instance2a = scope2.resolve<TestService>('TestService');

      expect(instance1a.id).toBe(instance1b.id); // Same within scope
      expect(instance1a.id).not.toBe(instance2a.id); // Different across scopes
    });

    test('should inherit registrations from parent container', () => {
      class TestService {
        getValue() { return 'inherited'; }
      }

      container.register('TestService', TestService, 'singleton');
      const scope = container.createScope();

      const instance = scope.resolve<TestService>('TestService');
      expect(instance.getValue()).toBe('inherited');
    });
  });

  describe('Error Handling', () => {
    test('should throw error for unregistered services', () => {
      expect(() => {
        container.resolve('NonExistentService');
      }).toThrow("Service 'NonExistentService' is not registered");
    });

    test('should check if service is registered', () => {
      class TestService {}
      
      expect(container.isRegistered('TestService')).toBe(false);
      
      container.register('TestService', TestService);
      expect(container.isRegistered('TestService')).toBe(true);
    });
  });

  describe('Service Tokens', () => {
    test('should work with symbol tokens', () => {
      class TestService {
        getValue() { return 'symbol'; }
      }

      const TOKEN = Symbol('TestService');
      container.register(TOKEN, TestService, 'singleton');

      const instance = container.resolve<TestService>(TOKEN);
      expect(instance.getValue()).toBe('symbol');
    });

    test('should use predefined tokens', () => {
      class MockLogger implements ILogger {
        info() {}
        warn() {}
        error() {}
        debug() {}
        httpRequest() {}
        performance() {}
        audit() {}
      }

      container.register(TOKENS.LOGGER, MockLogger, 'singleton');
      
      const logger = container.resolve<ILogger>(TOKENS.LOGGER);
      expect(logger).toBeInstanceOf(MockLogger);
    });
  });

  describe('Disposal', () => {
    test('should dispose of disposable services', async () => {
      let disposed = false;
      
      class DisposableService {
        async dispose() {
          disposed = true;
        }
      }

      container.register('DisposableService', DisposableService, 'singleton');
      container.resolve('DisposableService'); // Create instance

      await container.dispose();
      expect(disposed).toBe(true);
    });
  });
});