import { test, expect } from '@playwright/test'

test.describe('Property Search and Booking Flow', () => {
  const testUser = {
    username: 'booking_user_' + Date.now(),
    email: `booking_${Date.now()}@example.com`,
    password: 'BookingUser123!',
    fullName: 'Booking Test User'
  }

  // Setup: Create a user account before booking tests
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    
    // Register user if not already done
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await page.click('text="Sign up" || text="Register"')
    
    await page.fill('[name="username"]', testUser.username)
    await page.fill('[name="email"]', testUser.email)
    await page.fill('[name="password"]', testUser.password)
    await page.fill('[name="fullName"]', testUser.fullName)
    await page.check('[name="termsAccepted"]')
    await page.check('[name="dataProcessingConsent"]')
    await page.click('button[type="submit"]')
    
    // Wait for registration/login to complete
    await page.waitForURL(/\/dashboard|\/profile|\/home|\//)
  })

  test('complete property search and booking workflow', async ({ page }) => {
    // Navigate to properties page
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('text="Properties"')
    } catch {
      try {
        await page.click('text="Browse"')
      } catch {
        await page.click('[data-testid="properties-link"]')
      }
    }
    
    // Verify properties are loaded
    await expect(page.locator('[data-testid="property-card"], .property-card')).toHaveCount.greaterThan(0)
    
    // Search for properties
    const searchInput = page.locator('[data-testid="search-input"], [placeholder*="Search"], [name="search"]')
    if (await searchInput.isVisible()) {
      await searchInput.fill('farmhouse')
      await page.keyboard.press('Enter')
      await page.waitForTimeout(1000) // Wait for search results
    }
    
    // Filter properties by location
    const locationFilter = page.locator('[data-testid="location-filter"], select[name="location"]')
    if (await locationFilter.isVisible()) {
      await locationFilter.selectOption({ index: 1 }) // Select first non-empty option
      await page.waitForTimeout(1000)
    }
    
    // Filter by price range
    const minPriceInput = page.locator('[data-testid="min-price"], [name="minPrice"]')
    const maxPriceInput = page.locator('[data-testid="max-price"], [name="maxPrice"]')
    if (await minPriceInput.isVisible()) {
      await minPriceInput.fill('100')
      await maxPriceInput.fill('500')
      await page.waitForTimeout(1000)
    }
    
    // Click on the first property
    await page.click('[data-testid="property-card"], .property-card')
    
    // Verify property detail page
    await expect(page.locator('[data-testid="property-title"], h1')).toBeVisible()
    await expect(page.locator('[data-testid="property-description"], .description')).toBeVisible()
    await expect(page.locator('[data-testid="property-price"], .price')).toBeVisible()
    
    // View property images
    const imageGallery = page.locator('[data-testid="image-gallery"], .image-gallery')
    if (await imageGallery.isVisible()) {
      await page.click('[data-testid="gallery-next"], .gallery-next')
      await page.waitForTimeout(500)
      await page.click('[data-testid="gallery-prev"], .gallery-prev')
    }
    
    // Start booking process
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="book-now"]')
    } catch {
      try {
        await page.click('text="Book Now"')
      } catch {
        await page.click('text="Make Booking"')
      }
    }
    
    // Fill booking form
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const bookingDate = tomorrow.toISOString().split('T')[0]
    
    await page.fill('[data-testid="booking-date"], [name="bookingDate"]', bookingDate)
    await page.selectOption('[data-testid="booking-type"], [name="bookingType"]', 'full_day')
    await page.fill('[data-testid="guests"], [name="guests"]', '4')
    
    // Add special requests if available
    const specialRequests = page.locator('[data-testid="special-requests"], [name="specialRequests"]')
    if (await specialRequests.isVisible()) {
      await specialRequests.fill('Family celebration, need early check-in')
    }
    
    // Submit booking
    await page.click('[data-testid="confirm-booking"], button[type="submit"]')
    
    // Verify booking confirmation
    await expect(page.locator('text="Booking confirmed" || text="Booking successful"')).toBeVisible()
    await expect(page.locator('[data-testid="booking-id"], .booking-reference')).toBeVisible()
    
    // Verify booking appears in user's bookings
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="my-bookings"]')
    } catch {
      try {
        await page.click('text="My Bookings"')
      } catch {
        await page.click('text="Bookings"')
      }
    }
    await expect(page.locator('[data-testid="booking-item"], .booking-item')).toHaveCount.greaterThan(0)
  })

  test('property search filters and results', async ({ page }) => {
    await page.goto('/properties')
    
    // Test featured properties filter
    const featuredFilter = page.locator('[data-testid="featured-filter"], input[name="featured"]')
    if (await featuredFilter.isVisible()) {
      await featuredFilter.check()
      await page.waitForTimeout(1000)
      
      // Verify featured properties are shown
      const featuredBadges = page.locator('[data-testid="featured-badge"], .featured')
      if (await featuredBadges.first().isVisible()) {
        await expect(featuredBadges).toHaveCount.greaterThan(0)
      }
    }
    
    // Test amenities filter
    const amenitiesFilter = page.locator('[data-testid="amenities-filter"], select[name="amenities"]')
    if (await amenitiesFilter.isVisible()) {
      await amenitiesFilter.selectOption('WiFi')
      await page.waitForTimeout(1000)
    }
    
    // Test sorting
    const sortSelect = page.locator('[data-testid="sort-select"], select[name="sort"]')
    if (await sortSelect.isVisible()) {
      await sortSelect.selectOption('price_low_high')
      await page.waitForTimeout(1000)
    }
    
    // Verify results are displayed
    await expect(page.locator('[data-testid="property-card"], .property-card')).toHaveCount.greaterThan(0)
  })

  test('property detail page functionality', async ({ page }) => {
    await page.goto('/properties')
    await page.click('[data-testid="property-card"], .property-card')
    
    // Test image gallery navigation
    const galleryImages = page.locator('[data-testid="gallery-image"], .gallery img')
    if (await galleryImages.first().isVisible()) {
      await page.click('[data-testid="gallery-next"], .gallery-next')
      await page.waitForTimeout(500)
      
      // Test image modal
      await page.click('[data-testid="gallery-image"], .gallery img')
      await expect(page.locator('[data-testid="image-modal"], .modal')).toBeVisible()
      await page.keyboard.press('Escape')
      await expect(page.locator('[data-testid="image-modal"], .modal')).not.toBeVisible()
    }
    
    // Test amenities display
    await expect(page.locator('[data-testid="amenities"], .amenities')).toBeVisible()
    
    // Test location map
    const mapContainer = page.locator('[data-testid="location-map"], .map')
    if (await mapContainer.isVisible()) {
      await expect(mapContainer).toBeVisible()
    }
    
    // Test reviews section
    const reviewsSection = page.locator('[data-testid="reviews"], .reviews')
    if (await reviewsSection.isVisible()) {
      await expect(reviewsSection).toBeVisible()
    }
    
    // Test sharing functionality
    const shareButton = page.locator('[data-testid="share-property"], button[aria-label*="Share"]')
    if (await shareButton.isVisible()) {
      await shareButton.click()
      await expect(page.locator('[data-testid="share-modal"], .share-modal')).toBeVisible()
      await page.keyboard.press('Escape')
    }
  })

  test('booking form validation', async ({ page }) => {
    await page.goto('/properties')
    await page.click('[data-testid="property-card"], .property-card')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="book-now"]')
    } catch {
      await page.click('text="Book Now"')
    }
    
    // Try to submit empty form
    await page.click('[data-testid="confirm-booking"], button[type="submit"]')
    
    // Should show validation errors
    await expect(page.locator('text="required" || text="This field is required"')).toBeVisible()
    
    // Test past date validation
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    const pastDate = yesterday.toISOString().split('T')[0]
    
    await page.fill('[data-testid="booking-date"], [name="bookingDate"]', pastDate)
    await page.selectOption('[data-testid="booking-type"], [name="bookingType"]', 'full_day')
    await page.fill('[data-testid="guests"], [name="guests"]', '2')
    await page.click('[data-testid="confirm-booking"], button[type="submit"]')
    
    // Should show past date error
    await expect(page.locator('text="past" || text="future date"')).toBeVisible()
    
    // Test invalid guest count
    await page.fill('[data-testid="guests"], [name="guests"]', '0')
    await page.click('[data-testid="confirm-booking"], button[type="submit"]')
    
    await expect(page.locator('text="least 1" || text="minimum"')).toBeVisible()
  })

  test('booking availability check', async ({ page }) => {
    await page.goto('/properties')
    await page.click('[data-testid="property-card"], .property-card')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="book-now"]')
    } catch {
      await page.click('text="Book Now"')
    }
    
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const bookingDate = tomorrow.toISOString().split('T')[0]
    
    // Fill form with valid data
    await page.fill('[data-testid="booking-date"], [name="bookingDate"]', bookingDate)
    await page.selectOption('[data-testid="booking-type"], [name="bookingType"]', 'full_day')
    await page.fill('[data-testid="guests"], [name="guests"]', '4')
    
    // Check availability before booking
    const checkAvailabilityButton = page.locator('[data-testid="check-availability"], text="Check Availability"')
    if (await checkAvailabilityButton.isVisible()) {
      await checkAvailabilityButton.click()
      await page.waitForTimeout(1000)
      
      // Should show availability status
      await expect(page.locator('[data-testid="availability-status"], .availability')).toBeVisible()
    }
  })

  test('property favorites functionality', async ({ page }) => {
    await page.goto('/properties')
    
    // Add property to favorites
    const favoriteButton = page.locator('[data-testid="favorite-button"], button[aria-label*="favorite"]').first()
    if (await favoriteButton.isVisible()) {
      await favoriteButton.click()
      
      // Should show added to favorites
      await expect(page.locator('text="Added to favorites" || text="Favorited"')).toBeVisible()
      
      // Go to favorites page
      // Try multiple selectors in order
      try {
        await page.click('[data-testid="favorites-link"]')
      } catch {
        await page.click('text="Favorites"')
      }
      await expect(page.locator('[data-testid="property-card"], .property-card')).toHaveCount.greaterThan(0)
      
      // Remove from favorites
      await favoriteButton.click()
      await page.waitForTimeout(1000)
    }
  })

  test('responsive design - mobile booking flow', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/properties')
    
    // Verify mobile property grid
    await expect(page.locator('[data-testid="property-card"], .property-card')).toBeVisible()
    
    // Test mobile filters
    const mobileFilterButton = page.locator('[data-testid="mobile-filter"], button[aria-label*="filter"]')
    if (await mobileFilterButton.isVisible()) {
      await mobileFilterButton.click()
      await expect(page.locator('[data-testid="filter-drawer"], .filter-modal')).toBeVisible()
      await page.keyboard.press('Escape')
    }
    
    // Test mobile property detail
    await page.click('[data-testid="property-card"], .property-card')
    await expect(page.locator('[data-testid="property-title"], h1')).toBeVisible()
    
    // Test mobile booking form
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="book-now"]')
    } catch {
      await page.click('text="Book Now"')
    }
    await expect(page.locator('[data-testid="booking-date"], [name="bookingDate"]')).toBeVisible()
  })

  test('keyboard navigation in property browsing', async ({ page }) => {
    await page.goto('/properties')
    
    // Test keyboard navigation through property cards
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // Should be able to navigate to and select a property
    await page.keyboard.press('Enter')
    
    // Should navigate to property detail
    await expect(page.locator('[data-testid="property-title"], h1')).toBeVisible()
    
    // Test escape to go back
    await page.keyboard.press('Escape')
    // Note: This depends on browser back functionality or app implementation
  })

  test('property search with no results', async ({ page }) => {
    await page.goto('/properties')
    
    // Search for something that likely won't exist
    const searchInput = page.locator('[data-testid="search-input"], [placeholder*="Search"]')
    if (await searchInput.isVisible()) {
      await searchInput.fill('nonexistentproperty12345')
      await page.keyboard.press('Enter')
      await page.waitForTimeout(1000)
      
      // Should show no results message
      await expect(page.locator('text="No properties found" || text="No results"')).toBeVisible()
      
      // Should show suggestion to clear filters or try different search
      const clearFiltersButton = page.locator('[data-testid="clear-filters"], text="Clear filters"')
      if (await clearFiltersButton.isVisible()) {
        await clearFiltersButton.click()
        await page.waitForTimeout(1000)
        await expect(page.locator('[data-testid="property-card"], .property-card')).toHaveCount.greaterThan(0)
      }
    }
  })

  test('booking confirmation and details', async ({ page }) => {
    await page.goto('/properties')
    await page.click('[data-testid="property-card"], .property-card')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="book-now"]')
    } catch {
      await page.click('text="Book Now"')
    }
    
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 2) // Use day after tomorrow to avoid conflicts
    const bookingDate = tomorrow.toISOString().split('T')[0]
    
    await page.fill('[data-testid="booking-date"], [name="bookingDate"]', bookingDate)
    await page.selectOption('[data-testid="booking-type"], [name="bookingType"]', 'full_day')
    await page.fill('[data-testid="guests"], [name="guests"]', '3')
    
    // Submit booking
    await page.click('[data-testid="confirm-booking"], button[type="submit"]')
    
    // Wait for confirmation
    await expect(page.locator('text="Booking confirmed" || text="successful"')).toBeVisible({ timeout: 10000 })
    
    // Should show booking details
    await expect(page.locator('[data-testid="booking-id"], .booking-reference')).toBeVisible()
    await expect(page.locator(`text="${bookingDate}"`)).toBeVisible()
    await expect(page.locator('text="3" || text="3 guests"')).toBeVisible()
    
    // Should provide options to view booking or continue browsing
    const viewBookingButton = page.locator('[data-testid="view-booking"], text="View Booking"')
    const continueButton = page.locator('[data-testid="continue-browsing"], text="Browse More"')
    
    if (await viewBookingButton.isVisible()) {
      await expect(viewBookingButton).toBeVisible()
    }
    if (await continueButton.isVisible()) {
      await expect(continueButton).toBeVisible()
    }
  })
})