import { test, expect } from '@playwright/test'

test.describe('Owner Property Management Flow', () => {
  const testOwner = {
    username: 'owner_' + Date.now(),
    email: `owner_${Date.now()}@example.com`,
    password: 'OwnerPassword123!',
    fullName: 'Property Owner Test',
    role: 'owner'
  }

  const testProperty = {
    title: 'E2E Test Farmhouse',
    description: 'A beautiful farmhouse for testing purposes',
    location: 'Test County, Test State',
    halfDayPrice: 150,
    fullDayPrice: 250,
    bedrooms: 3,
    bathrooms: 2,
    maxGuests: 8,
    amenities: ['WiFi', 'Kitchen', 'Pool', 'Parking']
  }

  // Setup: Create owner account
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    
    // Register as owner
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await page.click('text="Sign up" || text="Register"')
    
    await page.fill('[name="username"]', testOwner.username)
    await page.fill('[name="email"]', testOwner.email)
    await page.fill('[name="password"]', testOwner.password)
    await page.fill('[name="fullName"]', testOwner.fullName)
    
    // Select owner role if available
    const roleSelect = page.locator('[data-testid="role-select"], [name="role"]')
    if (await roleSelect.isVisible()) {
      await roleSelect.selectOption('owner')
    }
    
    await page.check('[name="termsAccepted"]')
    await page.check('[name="dataProcessingConsent"]')
    await page.click('button[type="submit"]')
    
    // Wait for registration
    await page.waitForURL(/\/dashboard|\/profile|\/home|\/owner/)
  })

  test('complete property creation workflow', async ({ page }) => {
    // Navigate to create property page
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="create-property"]')
    } catch {
      try {
        await page.click('text="Add Property"')
      } catch {
        await page.click('text="Create Property"')
      }
    }
    
    // Fill basic property information
    await page.fill('[data-testid="property-title"], [name="title"]', testProperty.title)
    await page.fill('[data-testid="property-description"], [name="description"]', testProperty.description)
    await page.fill('[data-testid="property-location"], [name="location"]', testProperty.location)
    
    // Fill pricing information
    await page.fill('[data-testid="half-day-price"], [name="halfDayPrice"]', testProperty.halfDayPrice.toString())
    await page.fill('[data-testid="full-day-price"], [name="fullDayPrice"]', testProperty.fullDayPrice.toString())
    
    // Fill property details
    await page.fill('[data-testid="bedrooms"], [name="bedrooms"]', testProperty.bedrooms.toString())
    await page.fill('[data-testid="bathrooms"], [name="bathrooms"]', testProperty.bathrooms.toString())
    await page.fill('[data-testid="max-guests"], [name="maxGuests"]', testProperty.maxGuests.toString())
    
    // Select amenities
    for (const amenity of testProperty.amenities) {
      const amenityCheckbox = page.locator(`[data-testid="amenity-${amenity}"], input[value="${amenity}"]`)
      if (await amenityCheckbox.isVisible()) {
        await amenityCheckbox.check()
      }
    }
    
    // Upload images (simulate file upload)
    const fileInput = page.locator('[data-testid="image-upload"], input[type="file"]')
    if (await fileInput.isVisible()) {
      // Note: In real tests, you'd upload actual test images
      // For now, we'll just verify the upload component exists
      await expect(fileInput).toBeVisible()
    }
    
    // Submit property creation
    await page.click('[data-testid="create-property-submit"], button[type="submit"]')
    
    // Verify property was created
    await expect(page.locator('text="Property created" || text="success"')).toBeVisible({ timeout: 10000 })
    
    // Should redirect to property list or property detail
    await expect(page).toHaveURL(/\/properties|\/owner|\/dashboard/)
    
    // Verify property appears in owner's property list
    await page.click('[data-testid="my-properties"], text="My Properties"')
    await expect(page.locator(`text="${testProperty.title}"`)).toBeVisible()
  })

  test('property management dashboard functionality', async ({ page }) => {
    // Create a property first (simplified)
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="create-property"]')
    } catch {
      await page.click('text="Add Property"')
    }
    await page.fill('[name="title"]', 'Dashboard Test Property')
    await page.fill('[name="description"]', 'Test description')
    await page.fill('[name="location"]', 'Test Location')
    await page.fill('[name="halfDayPrice"]', '100')
    await page.fill('[name="fullDayPrice"]', '180')
    await page.fill('[name="bedrooms"]', '2')
    await page.fill('[name="bathrooms"]', '1')
    await page.click('button[type="submit"]')
    
    // Navigate to owner dashboard
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="owner-dashboard"]')
    } catch {
      await page.click('text="Dashboard"')
    }
    
    // Verify dashboard components
    await expect(page.locator('[data-testid="properties-summary"], .properties-stats')).toBeVisible()
    await expect(page.locator('[data-testid="bookings-summary"], .bookings-stats')).toBeVisible()
    await expect(page.locator('[data-testid="revenue-summary"], .revenue-stats')).toBeVisible()
    
    // Test analytics/charts if present
    const analyticsChart = page.locator('[data-testid="analytics-chart"], .chart')
    if (await analyticsChart.isVisible()) {
      await expect(analyticsChart).toBeVisible()
    }
    
    // Test recent bookings section
    const recentBookings = page.locator('[data-testid="recent-bookings"], .recent-bookings')
    if (await recentBookings.isVisible()) {
      await expect(recentBookings).toBeVisible()
    }
  })

  test('property editing and updates', async ({ page }) => {
    // Create property first
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="create-property"]')
    } catch {
      await page.click('text="Add Property"')
    }
    await page.fill('[name="title"]', 'Editable Property')
    await page.fill('[name="description"]', 'Original description')
    await page.fill('[name="location"]', 'Original Location')
    await page.fill('[name="halfDayPrice"]', '120')
    await page.fill('[name="fullDayPrice"]', '200')
    await page.fill('[name="bedrooms"]', '2')
    await page.fill('[name="bathrooms"]', '1')
    await page.click('button[type="submit"]')
    
    // Go to property list
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="my-properties"]')
    } catch {
      await page.click('text="My Properties"')
    }
    
    // Click edit on the property
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="edit-property"]')
    } catch {
      try {
        await page.click('text="Edit"')
      } catch {
        await page.click('button[aria-label*="Edit"]')
      }
    }
    
    // Update property details
    await page.fill('[name="title"]', 'Updated Property Title')
    await page.fill('[name="description"]', 'Updated description with more details')
    await page.fill('[name="halfDayPrice"]', '140')
    await page.fill('[name="fullDayPrice"]', '220')
    
    // Toggle featured status if available
    const featuredToggle = page.locator('[data-testid="featured-toggle"], [name="featured"]')
    if (await featuredToggle.isVisible()) {
      await featuredToggle.check()
    }
    
    // Save changes
    await page.click('[data-testid="save-property"], button[type="submit"]')
    
    // Verify updates were saved
    await expect(page.locator('text="Property updated" || text="Changes saved"')).toBeVisible()
    await expect(page.locator('text="Updated Property Title"')).toBeVisible()
  })

  test('booking management for owners', async ({ page }) => {
    // Navigate to bookings management
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="manage-bookings"]')
    } catch {
      try {
        await page.click('text="Bookings"')
      } catch {
        await page.click('text="Manage Bookings"')
      }
    }
    
    // Verify bookings list interface
    await expect(page.locator('[data-testid="bookings-list"], .bookings-container')).toBeVisible()
    
    // Test booking filters
    const statusFilter = page.locator('[data-testid="status-filter"], select[name="status"]')
    if (await statusFilter.isVisible()) {
      await statusFilter.selectOption('pending')
      await page.waitForTimeout(1000)
      
      await statusFilter.selectOption('confirmed')
      await page.waitForTimeout(1000)
    }
    
    // Test date range filter
    const dateFromInput = page.locator('[data-testid="date-from"], [name="dateFrom"]')
    const dateToInput = page.locator('[data-testid="date-to"], [name="dateTo"]')
    if (await dateFromInput.isVisible()) {
      const today = new Date().toISOString().split('T')[0]
      const nextMonth = new Date()
      nextMonth.setMonth(nextMonth.getMonth() + 1)
      const nextMonthStr = nextMonth.toISOString().split('T')[0]
      
      await dateFromInput.fill(today)
      await dateToInput.fill(nextMonthStr)
      await page.waitForTimeout(1000)
    }
    
    // Test booking actions (if bookings exist)
    const bookingItem = page.locator('[data-testid="booking-item"], .booking-row').first()
    if (await bookingItem.isVisible()) {
      // Test view booking details
      await bookingItem.click()
      await expect(page.locator('[data-testid="booking-details"], .booking-modal')).toBeVisible()
      await page.keyboard.press('Escape')
      
      // Test booking actions
      const approveButton = page.locator('[data-testid="approve-booking"], button[data-action="approve"]')
      const rejectButton = page.locator('[data-testid="reject-booking"], button[data-action="reject"]')
      
      if (await approveButton.isVisible()) {
        await expect(approveButton).toBeVisible()
      }
      if (await rejectButton.isVisible()) {
        await expect(rejectButton).toBeVisible()
      }
    }
  })

  test('property analytics and insights', async ({ page }) => {
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="analytics"]')
    } catch {
      try {
        await page.click('text="Analytics"')
      } catch {
        await page.click('text="Insights"')
      }
    }
    
    // Verify analytics page components
    await expect(page.locator('[data-testid="revenue-chart"], .revenue-analytics')).toBeVisible()
    
    // Test date range selector for analytics
    const dateRangeSelect = page.locator('[data-testid="date-range"], select[name="dateRange"]')
    if (await dateRangeSelect.isVisible()) {
      await dateRangeSelect.selectOption('last-month')
      await page.waitForTimeout(1000)
      
      await dateRangeSelect.selectOption('last-quarter')
      await page.waitForTimeout(1000)
    }
    
    // Test analytics filters
    const propertyFilter = page.locator('[data-testid="property-filter"], select[name="property"]')
    if (await propertyFilter.isVisible()) {
      await propertyFilter.selectOption({ index: 1 }) // Select first property
      await page.waitForTimeout(1000)
    }
    
    // Verify key metrics are displayed
    const metrics = [
      '[data-testid="total-revenue"], .revenue-metric',
      '[data-testid="total-bookings"], .bookings-metric',
      '[data-testid="occupancy-rate"], .occupancy-metric',
      '[data-testid="average-rating"], .rating-metric'
    ]
    
    for (const metric of metrics) {
      const element = page.locator(metric)
      if (await element.isVisible()) {
        await expect(element).toBeVisible()
      }
    }
  })

  test('property deletion workflow', async ({ page }) => {
    // Create a property to delete
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="create-property"]')
    } catch {
      await page.click('text="Add Property"')
    }
    await page.fill('[name="title"]', 'Property To Delete')
    await page.fill('[name="description"]', 'This property will be deleted')
    await page.fill('[name="location"]', 'Delete Location')
    await page.fill('[name="halfDayPrice"]', '100')
    await page.fill('[name="fullDayPrice"]', '150')
    await page.fill('[name="bedrooms"]', '1')
    await page.fill('[name="bathrooms"]', '1')
    await page.click('button[type="submit"]')
    
    // Navigate to property list
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="my-properties"]')
    } catch {
      await page.click('text="My Properties"')
    }
    
    // Find and delete the property
    const deleteButton = page.locator('[data-testid="delete-property"], button[aria-label*="Delete"]')
    if (await deleteButton.isVisible()) {
      await deleteButton.click()
      
      // Confirm deletion in modal
      await expect(page.locator('[data-testid="delete-modal"], .delete-confirmation')).toBeVisible()
      await page.click('[data-testid="confirm-delete"], button[data-action="confirm"]')
      
      // Verify deletion success
      await expect(page.locator('text="Property deleted" || text="Deleted successfully"')).toBeVisible()
      await expect(page.locator('text="Property To Delete"')).not.toBeVisible()
    }
  })

  test('owner profile and settings management', async ({ page }) => {
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="profile-settings"]')
    } catch {
      try {
        await page.click('text="Profile"')
      } catch {
        await page.click('text="Settings"')
      }
    }
    
    // Update profile information
    await page.fill('[data-testid="full-name"], [name="fullName"]', 'Updated Owner Name')
    await page.fill('[data-testid="phone"], [name="phone"]', '+1234567890')
    
    // Update bio/description if available
    const bioTextarea = page.locator('[data-testid="bio"], [name="bio"]')
    if (await bioTextarea.isVisible()) {
      await bioTextarea.fill('Experienced property owner with multiple farmhouse rentals.')
    }
    
    // Test notification preferences
    const emailNotifications = page.locator('[data-testid="email-notifications"], [name="emailNotifications"]')
    const smsNotifications = page.locator('[data-testid="sms-notifications"], [name="smsNotifications"]')
    
    if (await emailNotifications.isVisible()) {
      await emailNotifications.check()
    }
    if (await smsNotifications.isVisible()) {
      await smsNotifications.check()
    }
    
    // Save profile changes
    await page.click('[data-testid="save-profile"], button[type="submit"]')
    await expect(page.locator('text="Profile updated" || text="Changes saved"')).toBeVisible()
  })

  test('responsive design - mobile owner dashboard', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to dashboard
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="owner-dashboard"]')
    } catch {
      await page.click('text="Dashboard"')
    }
    
    // Verify mobile layout
    await expect(page.locator('[data-testid="mobile-menu"], .mobile-nav')).toBeVisible()
    
    // Test mobile navigation
    const mobileMenuButton = page.locator('[data-testid="mobile-menu-toggle"], button[aria-label*="menu"]')
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click()
      await expect(page.locator('[data-testid="mobile-nav-menu"], .mobile-menu')).toBeVisible()
      await page.keyboard.press('Escape')
    }
    
    // Test mobile property management
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="my-properties"]')
    } catch {
      await page.click('text="Properties"')
    }
    await expect(page.locator('[data-testid="property-card"], .property-item')).toBeVisible()
  })

  test('owner booking notifications and messaging', async ({ page }) => {
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="notifications"]')
    } catch {
      await page.click('text="Notifications"')
    }
    
    // Verify notifications interface
    await expect(page.locator('[data-testid="notifications-list"], .notifications')).toBeVisible()
    
    // Test notification filters
    const notificationFilter = page.locator('[data-testid="notification-filter"], select[name="type"]')
    if (await notificationFilter.isVisible()) {
      await notificationFilter.selectOption('booking')
      await page.waitForTimeout(1000)
      
      await notificationFilter.selectOption('review')
      await page.waitForTimeout(1000)
    }
    
    // Test mark as read functionality
    const markAllReadButton = page.locator('[data-testid="mark-all-read"], button[data-action="mark-read"]')
    if (await markAllReadButton.isVisible()) {
      await markAllReadButton.click()
      await expect(page.locator('text="Marked as read" || text="Updated"')).toBeVisible()
    }
    
    // Test messaging if available
    const messagesSection = page.locator('[data-testid="messages"], text="Messages"')
    if (await messagesSection.isVisible()) {
      await messagesSection.click()
      await expect(page.locator('[data-testid="messages-list"], .messages')).toBeVisible()
    }
  })

  test('property availability calendar management', async ({ page }) => {
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="my-properties"]')
    } catch {
      await page.click('text="My Properties"')
    }
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="manage-availability"]')
    } catch {
      try {
        await page.click('text="Availability"')
      } catch {
        await page.click('text="Calendar"')
      }
    }
    
    // Verify calendar interface
    await expect(page.locator('[data-testid="availability-calendar"], .calendar')).toBeVisible()
    
    // Test calendar navigation
    const nextMonthButton = page.locator('[data-testid="next-month"], button[aria-label*="next"]')
    const prevMonthButton = page.locator('[data-testid="prev-month"], button[aria-label*="previous"]')
    
    if (await nextMonthButton.isVisible()) {
      await nextMonthButton.click()
      await page.waitForTimeout(500)
      await prevMonthButton.click()
    }
    
    // Test blocking dates
    const calendarDate = page.locator('[data-testid="calendar-date"], .calendar-day').first()
    if (await calendarDate.isVisible()) {
      await calendarDate.click()
      
      // Should open availability modal
      const availabilityModal = page.locator('[data-testid="availability-modal"], .availability-dialog')
      if (await availabilityModal.isVisible()) {
        await expect(availabilityModal).toBeVisible()
        
        // Test blocking the date
        const blockDateButton = page.locator('[data-testid="block-date"], button[data-action="block"]')
        if (await blockDateButton.isVisible()) {
          await blockDateButton.click()
          await expect(page.locator('text="Date blocked" || text="Updated"')).toBeVisible()
        }
        
        await page.keyboard.press('Escape')
      }
    }
  })
})