import { test, expect } from '@playwright/test'

test.describe('Accessibility and Responsive Design', () => {
  test.describe('Accessibility Testing', () => {
    test('keyboard navigation throughout the application', async ({ page }) => {
      await page.goto('/')
      
      // Test keyboard navigation on homepage
      await page.keyboard.press('Tab')
      let focusedElement = await page.evaluate(() => document.activeElement?.tagName)
      expect(['BUTTON', 'A', 'INPUT']).toContain(focusedElement)
      
      // Navigate through multiple elements
      for (let i = 0; i < 5; i++) {
        await page.keyboard.press('Tab')
      }
      
      // Test Enter key on focused element
      await page.keyboard.press('Enter')
      
      // Test Escape key functionality
      await page.keyboard.press('Escape')
      
      // Navigate to properties page via keyboard
      await page.goto('/properties')
      await page.keyboard.press('Tab')
      
      // Should be able to navigate to property cards
      let tabCount = 0
      while (tabCount < 10) {
        await page.keyboard.press('Tab')
        const focusedEl = await page.evaluate(() => {
          const el = document.activeElement
          return el ? {
            tag: el.tagName,
            role: el.getAttribute('role'),
            ariaLabel: el.getAttribute('aria-label')
          } : null
        })
        
        if (focusedEl && (focusedEl.role === 'button' || focusedEl.tag === 'BUTTON')) {
          break
        }
        tabCount++
      }
      
      // Test Enter on property card
      await page.keyboard.press('Enter')
    })

    test('ARIA labels and semantic HTML structure', async ({ page }) => {
      await page.goto('/')
      
      // Check for proper heading hierarchy
      const h1Count = await page.locator('h1').count()
      expect(h1Count).toBeGreaterThanOrEqual(1)
      
      // Verify main navigation has proper ARIA labels
      const nav = page.locator('nav, [role="navigation"]')
      if (await nav.isVisible()) {
        await expect(nav).toBeVisible()
        
        // Check for aria-label or aria-labelledby
        const hasAriaLabel = await nav.evaluate((el) => 
          el.hasAttribute('aria-label') || el.hasAttribute('aria-labelledby')
        )
        // This is a soft expectation as not all implementations may have this
      }
      
      // Check for proper button labels
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i)
        if (await button.isVisible()) {
          const hasLabel = await button.evaluate((el) => {
            const text = el.textContent?.trim()
            const ariaLabel = el.getAttribute('aria-label')
            const ariaLabelledby = el.getAttribute('aria-labelledby')
            return !!(text || ariaLabel || ariaLabelledby)
          })
          expect(hasLabel).toBe(true)
        }
      }
      
      // Check for proper form labels
      await page.goto('/')
      // Try multiple selectors in order
      try {
        await page.click('[data-testid="auth-button"]')
      } catch {
        await page.click('text="Login"')
      }
      
      const formInputs = page.locator('input[type="email"], input[type="password"], input[type="text"]')
      const inputCount = await formInputs.count()
      
      for (let i = 0; i < inputCount; i++) {
        const input = formInputs.nth(i)
        if (await input.isVisible()) {
          const hasLabel = await input.evaluate((el) => {
            const id = el.id
            const ariaLabel = el.getAttribute('aria-label')
            const ariaLabelledby = el.getAttribute('aria-labelledby')
            const associatedLabel = id ? document.querySelector(`label[for="${id}"]`) : null
            return !!(ariaLabel || ariaLabelledby || associatedLabel)
          })
          // This is a soft check as implementation may vary
        }
      }
    })

    test('color contrast and visual accessibility', async ({ page }) => {
      await page.goto('/')
      
      // Check for focus indicators
      await page.keyboard.press('Tab')
      const focusedElement = page.locator(':focus')
      if (await focusedElement.isVisible()) {
        const focusStyles = await focusedElement.evaluate((el) => {
          const styles = window.getComputedStyle(el)
          return {
            outline: styles.outline,
            outlineColor: styles.outlineColor,
            boxShadow: styles.boxShadow,
            border: styles.border
          }
        })
        
        // Should have some form of focus indicator
        const hasFocusIndicator = Object.values(focusStyles).some(style => 
          style && style !== 'none' && style !== 'rgb(0, 0, 0)' && style !== '0px'
        )
        expect(hasFocusIndicator).toBe(true)
      }
      
      // Check for sufficient text contrast (basic check)
      const textElements = page.locator('p, span, div, h1, h2, h3, h4, h5, h6')
      const textElement = textElements.first()
      
      if (await textElement.isVisible()) {
        const contrast = await textElement.evaluate((el) => {
          const styles = window.getComputedStyle(el)
          return {
            color: styles.color,
            backgroundColor: styles.backgroundColor,
            fontSize: styles.fontSize
          }
        })
        
        // Basic check that text is not transparent or same as background
        expect(contrast.color).not.toBe('rgba(0, 0, 0, 0)')
        expect(contrast.color).not.toBe('transparent')
      }
    })

    test('screen reader compatibility with alt texts', async ({ page }) => {
      await page.goto('/')
      
      // Navigate to a page with images
      await page.goto('/properties')
      
      // Check images have alt text
      const images = page.locator('img')
      const imageCount = await images.count()
      
      for (let i = 0; i < Math.min(imageCount, 5); i++) {
        const img = images.nth(i)
        if (await img.isVisible()) {
          const altText = await img.getAttribute('alt')
          // Images should have alt text (empty alt="" is acceptable for decorative images)
          expect(altText).not.toBeNull()
        }
      }
      
      // Check for proper heading structure
      const headings = page.locator('h1, h2, h3, h4, h5, h6')
      const headingLevels = await headings.evaluateAll((elements) => 
        elements.map(el => parseInt(el.tagName.charAt(1)))
      )
      
      if (headingLevels.length > 1) {
        // Check that heading levels don't skip (e.g., h1 to h3 without h2)
        for (let i = 1; i < headingLevels.length; i++) {
          const diff = headingLevels[i] - headingLevels[i - 1]
          expect(diff).toBeLessThanOrEqual(1)
        }
      }
    })

    test('form accessibility and error handling', async ({ page }) => {
      await page.goto('/')
      // Try multiple selectors in order
      try {
        await page.click('[data-testid="auth-button"]')
      } catch {
        await page.click('text="Login"')
      }
      await page.click('text="Sign up" || text="Register"')
      
      // Submit form with errors to test error accessibility
      await page.click('button[type="submit"]')
      
      // Check that errors are accessible
      const errorMessages = page.locator('[role="alert"], .error-message, .field-error')
      if (await errorMessages.first().isVisible()) {
        const errorCount = await errorMessages.count()
        expect(errorCount).toBeGreaterThan(0)
        
        // Check that errors are associated with form fields
        for (let i = 0; i < Math.min(errorCount, 3); i++) {
          const error = errorMessages.nth(i)
          const errorId = await error.getAttribute('id')
          
          if (errorId) {
            // Look for input with aria-describedby pointing to this error
            const associatedInput = page.locator(`[aria-describedby*="${errorId}"]`)
            const hasAssociation = await associatedInput.count() > 0
            // This is a best practice but not always implemented
          }
        }
      }
    })
  })

  test.describe('Responsive Design Testing', () => {
    const viewports = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1920, height: 1080 }
    ]

    viewports.forEach(({ name, width, height }) => {
      test(`${name} viewport (${width}x${height}) - homepage layout`, async ({ page }) => {
        await page.setViewportSize({ width, height })
        await page.goto('/')
        
        // Check that content is visible and properly laid out
        await expect(page.locator('header, [role="banner"]')).toBeVisible()
        await expect(page.locator('main, [role="main"]')).toBeVisible()
        
        // Check for mobile menu on smaller screens
        if (width < 768) {
          const mobileMenu = page.locator('[data-testid="mobile-menu"], .mobile-nav-toggle')
          if (await mobileMenu.isVisible()) {
            await mobileMenu.click()
            await expect(page.locator('[data-testid="mobile-nav"], .mobile-menu')).toBeVisible()
            await page.keyboard.press('Escape')
          }
        }
        
        // Check that text is readable (not too small)
        const mainHeading = page.locator('h1').first()
        if (await mainHeading.isVisible()) {
          const fontSize = await mainHeading.evaluate((el) => 
            window.getComputedStyle(el).fontSize
          )
          const fontSizeNum = parseInt(fontSize)
          expect(fontSizeNum).toBeGreaterThan(14) // Minimum readable size
        }
      })

      test(`${name} viewport - property listing responsive layout`, async ({ page }) => {
        await page.setViewportSize({ width, height })
        await page.goto('/properties')
        
        // Verify property cards are properly displayed
        await expect(page.locator('[data-testid="property-card"], .property-item')).toBeVisible()
        
        // Check grid layout adapts to screen size
        const propertyGrid = page.locator('[data-testid="property-grid"], .properties-grid')
        if (await propertyGrid.isVisible()) {
          const gridColumns = await propertyGrid.evaluate((el) => 
            window.getComputedStyle(el).gridTemplateColumns
          )
          
          // Grid should adapt to screen size
          if (width < 768) {
            // Mobile: should be single column or minimal columns
            expect(gridColumns).toMatch(/1fr|repeat\(1,|^auto$/)
          } else if (width < 1200) {
            // Tablet: should be 2-3 columns
            expect(gridColumns).toMatch(/repeat\([2-3],/)
          }
          // Desktop can have more columns
        }
        
        // Test filters on different screen sizes
        const filterSection = page.locator('[data-testid="filters"], .filters')
        if (await filterSection.isVisible()) {
          if (width < 768) {
            // Mobile: filters might be in a collapsible section
            const mobileFilters = page.locator('[data-testid="mobile-filters"], .mobile-filter-toggle')
            if (await mobileFilters.isVisible()) {
              await mobileFilters.click()
              await expect(page.locator('[data-testid="filter-panel"], .filter-drawer')).toBeVisible()
              await page.keyboard.press('Escape')
            }
          } else {
            // Desktop/Tablet: filters should be visible
            await expect(filterSection).toBeVisible()
          }
        }
      })

      test(`${name} viewport - property detail responsive layout`, async ({ page }) => {
        await page.setViewportSize({ width, height })
        await page.goto('/properties')
        await page.click('[data-testid="property-card"], .property-item')
        
        // Verify property detail elements are properly displayed
        await expect(page.locator('[data-testid="property-title"], h1')).toBeVisible()
        await expect(page.locator('[data-testid="property-images"], .property-gallery')).toBeVisible()
        
        // Test image gallery responsiveness
        const gallery = page.locator('[data-testid="image-gallery"], .gallery')
        if (await gallery.isVisible()) {
          // Gallery should be usable on all screen sizes
          const galleryNext = page.locator('[data-testid="gallery-next"], .gallery-next')
          if (await galleryNext.isVisible()) {
            await galleryNext.click()
            await page.waitForTimeout(500)
          }
        }
        
        // Test booking form layout
        const bookingForm = page.locator('[data-testid="booking-form"], .booking-section')
        if (await bookingForm.isVisible()) {
          await expect(bookingForm).toBeVisible()
          
          // Form should be properly sized for the viewport
          const formWidth = await bookingForm.evaluate((el) => el.offsetWidth)
          expect(formWidth).toBeLessThanOrEqual(width)
        }
      })
    })

    test('Touch interactions on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto('/properties')
      
      // Test touch interactions on property cards
      const propertyCard = page.locator('[data-testid="property-card"], .property-item').first()
      if (await propertyCard.isVisible()) {
        // Simulate touch tap
        await propertyCard.tap()
        
        // Should navigate to property detail
        await expect(page.locator('[data-testid="property-title"], h1')).toBeVisible()
      }
      
      // Test swipe gestures in image gallery
      const gallery = page.locator('[data-testid="image-gallery"], .gallery')
      if (await gallery.isVisible()) {
        // Simulate swipe left (if gallery supports touch gestures)
        await gallery.swipeRight()
        await page.waitForTimeout(500)
        await gallery.swipeLeft()
      }
    })

    test('Content overflow and text wrapping', async ({ page }) => {
      await page.setViewportSize({ width: 320, height: 568 }) // Very small mobile
      await page.goto('/properties')
      
      // Check that content doesn't overflow horizontally
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
      const viewportWidth = await page.evaluate(() => window.innerWidth)
      
      // Allow small tolerance for scrollbars
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 20)
      
      // Check that text wraps properly
      const longTexts = page.locator('p, .description')
      const textCount = await longTexts.count()
      
      for (let i = 0; i < Math.min(textCount, 3); i++) {
        const text = longTexts.nth(i)
        if (await text.isVisible()) {
          const textWidth = await text.evaluate((el) => el.scrollWidth)
          const containerWidth = await text.evaluate((el) => el.offsetWidth)
          
          // Text should not overflow its container significantly
          expect(textWidth).toBeLessThanOrEqual(containerWidth + 10)
        }
      }
    })

    test('Form usability across devices', async ({ page }) => {
      const devices = [
        { width: 375, height: 667 }, // Mobile
        { width: 768, height: 1024 }  // Tablet
      ]

      for (const device of devices) {
        await page.setViewportSize(device)
        await page.goto('/')
        // Try multiple selectors in order
        try {
          await page.click('[data-testid="auth-button"]')
        } catch {
          await page.click('text="Login"')
        }
        
        // Test form field sizes and spacing
        const emailInput = page.locator('[name="email"]')
        if (await emailInput.isVisible()) {
          const inputHeight = await emailInput.evaluate((el) => el.offsetHeight)
          
          // Form fields should be large enough for touch interaction
          if (device.width < 768) {
            expect(inputHeight).toBeGreaterThanOrEqual(44) // iOS recommendation
          }
          
          // Test that form fields are properly spaced
          await emailInput.click()
          await page.keyboard.type('<EMAIL>')
          
          const passwordInput = page.locator('[name="password"]')
          if (await passwordInput.isVisible()) {
            await passwordInput.click()
            await page.keyboard.type('password123')
            
            // Fields should not overlap or be too close
            const emailRect = await emailInput.boundingBox()
            const passwordRect = await passwordInput.boundingBox()
            
            if (emailRect && passwordRect) {
              const spacing = passwordRect.y - (emailRect.y + emailRect.height)
              expect(spacing).toBeGreaterThanOrEqual(0) // No overlap
            }
          }
        }
      }
    })
  })

  test.describe('Cross-browser Compatibility', () => {
    test('CSS Grid and Flexbox support', async ({ page }) => {
      await page.goto('/properties')
      
      // Check that CSS Grid is properly supported and working
      const gridSupport = await page.evaluate(() => {
        const testEl = document.createElement('div')
        testEl.style.display = 'grid'
        return testEl.style.display === 'grid'
      })
      expect(gridSupport).toBe(true)
      
      // Check that Flexbox is properly supported
      const flexSupport = await page.evaluate(() => {
        const testEl = document.createElement('div')
        testEl.style.display = 'flex'
        return testEl.style.display === 'flex'
      })
      expect(flexSupport).toBe(true)
    })

    test('JavaScript ES6+ features support', async ({ page }) => {
      await page.goto('/')
      
      // Test that modern JavaScript features are working
      const jsSupport = await page.evaluate(() => {
        try {
          // Test arrow functions, const/let, template literals
          const test = (x) => `Hello ${x}`
          const result = test('world')
          
          // Test async/await support
          const asyncTest = async () => Promise.resolve('success')
          
          return {
            arrowFunctions: typeof test === 'function',
            templateLiterals: result === 'Hello world',
            asyncAwait: typeof asyncTest === 'function'
          }
        } catch (e) {
          return { error: e.message }
        }
      })
      
      expect(jsSupport.arrowFunctions).toBe(true)
      expect(jsSupport.templateLiterals).toBe(true)
      expect(jsSupport.asyncAwait).toBe(true)
    })
  })

  test.describe('Performance on Different Devices', () => {
    test('Page load performance on slow connections', async ({ page }) => {
      // Simulate slow 3G connection
      await page.route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 100)) // Add delay
        await route.continue()
      })
      
      const startTime = Date.now()
      await page.goto('/')
      const loadTime = Date.now() - startTime
      
      // Page should load within reasonable time even on slow connection
      expect(loadTime).toBeLessThan(10000) // 10 seconds max
      
      // Critical content should be visible
      await expect(page.locator('header, [role="banner"]')).toBeVisible()
      await expect(page.locator('main, [role="main"]')).toBeVisible()
    })

    test('Image loading and optimization', async ({ page }) => {
      await page.goto('/properties')
      
      // Check that images are properly loaded
      const images = page.locator('img')
      const imageCount = await images.count()
      
      if (imageCount > 0) {
        // Check first few images for proper loading
        for (let i = 0; i < Math.min(imageCount, 3); i++) {
          const img = images.nth(i)
          if (await img.isVisible()) {
            // Image should have loaded successfully
            const naturalWidth = await img.evaluate((el: HTMLImageElement) => el.naturalWidth)
            expect(naturalWidth).toBeGreaterThan(0)
            
            // Check for lazy loading attributes
            const loading = await img.getAttribute('loading')
            const srcset = await img.getAttribute('srcset')
            
            // These are optimization features that might be present
            // but not required for functionality
          }
        }
      }
    })
  })
})