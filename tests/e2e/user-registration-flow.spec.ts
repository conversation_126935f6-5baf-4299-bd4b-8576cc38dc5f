import { test, expect } from '@playwright/test'

test.describe('User Registration and Authentication Flow', () => {
  const testUser = {
    username: 'e2e_user_' + Date.now(),
    email: `e2e_test_${Date.now()}@example.com`,
    password: 'SecurePassword123!',
    fullName: 'E2E Test User'
  }

  test('complete user registration workflow', async ({ page }) => {
    // Navigate to homepage
    await page.goto('/')
    
    // Click on register/login button
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    
    // Switch to register mode
    await page.click('text="Sign up" || text="Register" || text="Create account"')
    
    // Fill registration form
    await page.fill('[data-testid="username-input"], [name="username"]', testUser.username)
    await page.fill('[data-testid="email-input"], [name="email"]', testUser.email)
    await page.fill('[data-testid="password-input"], [name="password"]', testUser.password)
    await page.fill('[data-testid="fullname-input"], [name="fullName"]', testUser.fullName)
    
    // Accept terms and conditions
    await page.check('[data-testid="terms-checkbox"], [name="termsAccepted"]')
    await page.check('[data-testid="privacy-checkbox"], [name="dataProcessingConsent"]')
    
    // Submit registration
    await page.click('[data-testid="register-submit"], button[type="submit"]')
    
    // Wait for successful registration
    await expect(page).toHaveURL(/\/dashboard|\/profile|\/home/<USER>
    
    // Verify user is logged in
    await expect(page.locator('[data-testid="user-menu"], [data-testid="profile-menu"]')).toBeVisible()
    
    // Verify welcome message or user info
    await expect(page.locator(`text="${testUser.username}" || text="${testUser.fullName}"`)).toBeVisible()
  })

  test('user login and logout workflow', async ({ page }) => {
    // First register the user (reuse from previous test)
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await page.click('text="Sign up" || text="Register"')
    
    await page.fill('[name="username"]', testUser.username)
    await page.fill('[name="email"]', testUser.email)
    await page.fill('[name="password"]', testUser.password)
    await page.fill('[name="fullName"]', testUser.fullName)
    await page.check('[name="termsAccepted"]')
    await page.check('[name="dataProcessingConsent"]')
    await page.click('button[type="submit"]')
    
    // Wait for registration to complete
    await page.waitForURL(/\/dashboard|\/profile|\/home/<USER>
    
    // Logout
    await page.click('[data-testid="user-menu"], [data-testid="profile-menu"]')
    await page.click('text="Logout" || text="Sign out"')
    
    // Verify logout
    await expect(page.locator('[data-testid="auth-button"], [data-testid="login-button"]')).toBeVisible()
    
    // Now test login
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    
    // Ensure we're in login mode
    if (await page.locator('text="Sign up" || text="Register"').isVisible()) {
      await page.click('text="Sign in" || text="Login" || text="Already have an account"')
    }
    
    // Fill login form
    await page.fill('[name="email"]', testUser.email)
    await page.fill('[name="password"]', testUser.password)
    await page.click('button[type="submit"]')
    
    // Verify successful login
    await expect(page).toHaveURL(/\/dashboard|\/profile|\/home/<USER>
    await expect(page.locator('[data-testid="user-menu"], [data-testid="profile-menu"]')).toBeVisible()
  })

  test('registration validation errors', async ({ page }) => {
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await page.click('text="Sign up" || text="Register"')
    
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    // Should show validation errors
    await expect(page.locator('text="required" || text="This field is required"')).toBeVisible()
    
    // Test invalid email
    await page.fill('[name="email"]', 'invalid-email')
    await page.fill('[name="password"]', '123') // Weak password
    await page.click('button[type="submit"]')
    
    // Should show email and password validation errors
    await expect(page.locator('text="valid email" || text="Invalid email"')).toBeVisible()
    await expect(page.locator('text="password" || text="8 characters"')).toBeVisible()
  })

  test('login validation and error handling', async ({ page }) => {
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    
    // Try login with non-existent user
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'wrongpassword')
    await page.click('button[type="submit"]')
    
    // Should show error message
    await expect(page.locator('text="Invalid email or password" || text="Login failed"')).toBeVisible()
  })

  test('password visibility toggle', async ({ page }) => {
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    
    const passwordInput = page.locator('[name="password"]')
    const toggleButton = page.locator('[data-testid="password-toggle"], button[aria-label*="password"]')
    
    // Password should be hidden initially
    await expect(passwordInput).toHaveAttribute('type', 'password')
    
    // Click toggle to show password
    if (await toggleButton.isVisible()) {
      await toggleButton.click()
      await expect(passwordInput).toHaveAttribute('type', 'text')
      
      // Click again to hide password
      await toggleButton.click()
      await expect(passwordInput).toHaveAttribute('type', 'password')
    }
  })

  test('responsive design - mobile registration', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await page.click('text="Sign up" || text="Register"')
    
    // Verify form is properly displayed on mobile
    await expect(page.locator('[name="username"]')).toBeVisible()
    await expect(page.locator('[name="email"]')).toBeVisible()
    await expect(page.locator('[name="password"]')).toBeVisible()
    
    // Form should be usable on mobile
    await page.fill('[name="username"]', 'mobile_user')
    await page.fill('[name="email"]', '<EMAIL>')
    await expect(page.locator('[name="username"]')).toHaveValue('mobile_user')
  })

  test('keyboard navigation in auth forms', async ({ page }) => {
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    
    // Test tab navigation through login form
    await page.keyboard.press('Tab') // Should focus email field
    await page.keyboard.type(testUser.email)
    
    await page.keyboard.press('Tab') // Should focus password field
    await page.keyboard.type(testUser.password)
    
    await page.keyboard.press('Tab') // Should focus submit button
    await page.keyboard.press('Enter') // Should submit form
    
    // Form should attempt to submit
    await expect(page.locator('text="Invalid email or password" || text="required"')).toBeVisible()
  })

  test('auth persistence across page reload', async ({ page }) => {
    // Login first
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await page.click('text="Sign up" || text="Register"')
    
    await page.fill('[name="username"]', testUser.username + '_persist')
    await page.fill('[name="email"]', `persist_${testUser.email}`)
    await page.fill('[name="password"]', testUser.password)
    await page.fill('[name="fullName"]', testUser.fullName)
    await page.check('[name="termsAccepted"]')
    await page.check('[name="dataProcessingConsent"]')
    await page.click('button[type="submit"]')
    
    await expect(page).toHaveURL(/\/dashboard|\/profile|\/home/<USER>
    
    // Reload page and verify still logged in
    await page.reload()
    await expect(page.locator('[data-testid="user-menu"], [data-testid="profile-menu"]')).toBeVisible()
  })

  test('cookie consent interaction with auth', async ({ page }) => {
    await page.goto('/')
    
    // Handle cookie consent if present
    const cookieConsent = page.locator('[data-testid="cookie-consent"], text="cookies"')
    if (await cookieConsent.isVisible()) {
      await page.click('text="Accept" || button[data-testid="accept-cookies"]')
    }
    
    // Proceed with authentication
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    await expect(page.locator('[name="email"]')).toBeVisible()
  })

  test('social login integration (if available)', async ({ page }) => {
    await page.goto('/')
    // Try multiple selectors in order
    try {
      await page.click('[data-testid="auth-button"]')
    } catch {
      try {
        await page.click('[data-testid="login-button"]')
      } catch {
        await page.click('text="Login"')
      }
    }
    
    // Check if social login buttons are present
    const googleLogin = page.locator('text="Google" || [data-testid="google-login"]')
    const facebookLogin = page.locator('text="Facebook" || [data-testid="facebook-login"]')
    
    if (await googleLogin.isVisible()) {
      // Verify Google login button is clickable (don't actually click in test)
      await expect(googleLogin).toBeVisible()
      await expect(googleLogin).toBeEnabled()
    }
    
    if (await facebookLogin.isVisible()) {
      // Verify Facebook login button is clickable
      await expect(facebookLogin).toBeVisible()
      await expect(facebookLogin).toBeEnabled()
    }
  })
})