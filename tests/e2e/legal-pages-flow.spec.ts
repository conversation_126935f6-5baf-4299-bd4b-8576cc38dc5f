import { test, expect } from '@playwright/test'

test.describe('Legal Pages Flow', () => {
  test('can navigate to privacy policy from footer', async ({ page }) => {
    await page.goto('/')

    // Wait for page to load
    await expect(page.locator('text=BookAFarm')).toBeVisible()

    // Look for privacy policy link in footer
    const privacyLink = page.locator('footer').locator('text=Privacy Policy').first()
    if (await privacyLink.isVisible()) {
      await privacyLink.click()
      await expect(page.locator('h1').filter({ hasText: 'Privacy Policy' })).toBeVisible()
    } else {
      // Navigate directly if footer link not found
      await page.goto('/privacy-policy')
      await expect(page.locator('h1').filter({ hasText: 'Privacy Policy' })).toBeVisible()
    }
  })

  test('can navigate to terms of service', async ({ page }) => {
    await page.goto('/terms-of-service')

    await expect(page.locator('h1').filter({ hasText: 'Terms of Service' })).toBeVisible()
    await expect(page.locator('text=Last Updated:')).toBeVisible()
  })

  test('can navigate to cookie policy', async ({ page }) => {
    await page.goto('/cookie-policy')

    await expect(page.locator('h1').filter({ hasText: 'Cookie Policy' })).toBeVisible()
    await expect(page.locator('text=Last Updated:')).toBeVisible()
  })

  test('legal pages have proper page transitions', async ({ page }) => {
    await page.goto('/privacy-policy')

    // Wait for content to load
    await expect(page.locator('h1').filter({ hasText: 'Privacy Policy' })).toBeVisible()
    await expect(page.locator('text=Last Updated:')).toBeVisible()

    // Navigate to another legal page
    await page.goto('/terms-of-service')
    await expect(page.locator('h1').filter({ hasText: 'Terms of Service' })).toBeVisible()
  })

  test('legal pages are responsive', async ({ page }) => {
    // Test mobile responsiveness
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/privacy-policy')

    await expect(page.locator('h1').filter({ hasText: 'Privacy Policy' })).toBeVisible()

    // Test desktop responsiveness
    await page.setViewportSize({ width: 1920, height: 1080 })
    await expect(page.locator('h1').filter({ hasText: 'Privacy Policy' })).toBeVisible()
  })
})