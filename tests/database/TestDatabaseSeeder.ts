/**
 * Test Database Seeding Utilities
 * Provides utilities for seeding and managing test database state
 */

import { UserFactory, PropertyFactory, BookingFactory, TestDataSeeder } from '../factories';
import { storage } from '../../server/storage';

export interface SeedOptions {
  users?: number;
  owners?: number;
  admins?: number;
  propertiesPerOwner?: number;
  bookingsPerProperty?: number;
  includePastBookings?: boolean;
  includeReviews?: boolean;
  seedAnalyticsData?: boolean;
}

export interface SeedResult {
  users: any[];
  properties: any[];
  bookings: any[];
  reviews?: any[];
  credentials: {
    users: Array<{ email: string; password: string; role: string }>;
  };
}

export class TestDatabaseSeeder {
  private static instance: TestDatabaseSeeder;
  private isSeeded = false;
  private seedData: SeedResult | null = null;

  private constructor() {}

  static getInstance(): TestDatabaseSeeder {
    if (!this.instance) {
      this.instance = new TestDatabaseSeeder();
    }
    return this.instance;
  }

  /**
   * Seed database with comprehensive test data
   */
  async seedDatabase(options: SeedOptions = {}): Promise<SeedResult> {
    if (this.isSeeded && this.seedData) {
      console.log('Database already seeded, returning existing data');
      return this.seedData;
    }

    console.log('Starting database seeding...');
    
    const {
      users = 10,
      owners = 3,
      admins = 1,
      propertiesPerOwner = 3,
      bookingsPerProperty = 2,
      includePastBookings = true,
      includeReviews = true,
      seedAnalyticsData = false
    } = options;

    try {
      // Clear existing test data
      await this.clearTestData();

      // Reset factory counters for consistent IDs
      UserFactory.resetCounter();
      PropertyFactory.resetCounter();
      BookingFactory.resetCounter();

      // Create users
      const createdUsers = await this.createUsers(users, owners, admins);
      
      // Create properties
      const createdProperties = await this.createProperties(createdUsers.owners, propertiesPerOwner);
      
      // Create bookings
      const createdBookings = await this.createBookings(
        createdUsers.all,
        createdProperties,
        bookingsPerProperty,
        includePastBookings
      );

      // Create reviews if requested
      const createdReviews = includeReviews 
        ? await this.createReviews(createdBookings.filter(b => b.status === 'completed'))
        : [];

      // Seed analytics data if requested
      if (seedAnalyticsData) {
        await this.seedAnalyticsData();
      }

      this.seedData = {
        users: createdUsers.all,
        properties: createdProperties,
        bookings: createdBookings,
        reviews: createdReviews,
        credentials: {
          users: createdUsers.all.map(user => ({
            email: user.email,
            password: 'TestPassword123!', // Standard test password
            role: user.role
          }))
        }
      };

      this.isSeeded = true;
      console.log(`Database seeding completed:
        - ${createdUsers.all.length} users created
        - ${createdProperties.length} properties created
        - ${createdBookings.length} bookings created
        - ${createdReviews.length} reviews created`);

      return this.seedData;
    } catch (error) {
      console.error('Database seeding failed:', error);
      throw error;
    }
  }

  /**
   * Create test users
   */
  private async createUsers(userCount: number, ownerCount: number, adminCount: number) {
    console.log('Creating test users...');
    
    const users = [];
    const owners = [];
    const admins = [];

    // Create admin users
    for (let i = 0; i < adminCount; i++) {
      const adminData = UserFactory.createAdmin({
        email: `admin${i + 1}@test.com`,
        username: `admin${i + 1}`
      });
      
      const admin = await this.createUserInDatabase(adminData);
      admins.push(admin);
      users.push(admin);
    }

    // Create owner users
    for (let i = 0; i < ownerCount; i++) {
      const ownerData = UserFactory.createOwner({
        email: `owner${i + 1}@test.com`,
        username: `owner${i + 1}`
      });
      
      const owner = await this.createUserInDatabase(ownerData);
      owners.push(owner);
      users.push(owner);
    }

    // Create regular users
    for (let i = 0; i < userCount; i++) {
      const userData = UserFactory.create({
        email: `user${i + 1}@test.com`,
        username: `user${i + 1}`,
        role: 'user'
      });
      
      const user = await this.createUserInDatabase(userData);
      users.push(user);
    }

    return { all: users, owners, admins };
  }

  /**
   * Create user in database with proper password hashing
   */
  private async createUserInDatabase(userData: any) {
    try {
      // Use storage layer to create user (handles password hashing)
      const user = await storage.createUser({
        ...userData,
        password: 'TestPassword123!' // Standard test password
      });
      
      return user;
    } catch (error) {
      console.error('Failed to create user:', userData.email, error);
      throw error;
    }
  }

  /**
   * Create test properties
   */
  private async createProperties(owners: any[], propertiesPerOwner: number) {
    console.log('Creating test properties...');
    
    const properties = [];

    for (const owner of owners) {
      // Create mix of property types for each owner
      const ownerProperties = [
        ...PropertyFactory.createMany(Math.floor(propertiesPerOwner * 0.6), { 
          ownerId: owner.id, 
          status: 'active' 
        }),
        ...PropertyFactory.createMany(Math.floor(propertiesPerOwner * 0.3), { 
          ownerId: owner.id, 
          status: 'active',
          featured: true 
        }),
        ...PropertyFactory.createMany(Math.ceil(propertiesPerOwner * 0.1), { 
          ownerId: owner.id, 
          status: 'draft' 
        })
      ];

      for (const propertyData of ownerProperties) {
        try {
          const property = await storage.createProperty({
            ...propertyData,
            owner // Include owner information
          });
          properties.push(property);
        } catch (error) {
          console.error('Failed to create property:', error);
        }
      }
    }

    return properties;
  }

  /**
   * Create test bookings
   */
  private async createBookings(
    users: any[], 
    properties: any[], 
    bookingsPerProperty: number, 
    includePastBookings: boolean
  ) {
    console.log('Creating test bookings...');
    
    const bookings = [];

    for (const property of properties) {
      const propertyBookings = [];
      
      // Create future bookings
      for (let i = 0; i < Math.floor(bookingsPerProperty * 0.7); i++) {
        const user = users[Math.floor(Math.random() * users.length)];
        const bookingData = BookingFactory.createFuture({
          propertyId: property.id,
          userId: user.id,
          status: Math.random() > 0.8 ? 'pending' : 'confirmed'
        });
        
        propertyBookings.push(bookingData);
      }

      // Create past bookings if requested
      if (includePastBookings) {
        for (let i = 0; i < Math.ceil(bookingsPerProperty * 0.3); i++) {
          const user = users[Math.floor(Math.random() * users.length)];
          const bookingData = BookingFactory.createPast({
            propertyId: property.id,
            userId: user.id,
            status: Math.random() > 0.2 ? 'completed' : 'cancelled'
          });
          
          propertyBookings.push(bookingData);
        }
      }

      // Create bookings in database
      for (const bookingData of propertyBookings) {
        try {
          const booking = await storage.createBooking({
            ...bookingData,
            property,
            user: users.find(u => u.id === bookingData.userId)
          });
          bookings.push(booking);
        } catch (error) {
          console.error('Failed to create booking:', error);
        }
      }
    }

    return bookings;
  }

  /**
   * Create test reviews
   */
  private async createReviews(completedBookings: any[]) {
    console.log('Creating test reviews...');
    
    const reviews = [];

    for (const booking of completedBookings) {
      // 70% chance of having a review for completed bookings
      if (Math.random() > 0.3) {
        try {
          const reviewData = {
            propertyId: booking.propertyId,
            userId: booking.userId,
            bookingId: booking.id,
            rating: Math.floor(Math.random() * 2) + 4, // 4-5 star ratings mostly
            comment: this.generateReviewComment(),
            createdAt: new Date(booking.updatedAt.getTime() + 24 * 60 * 60 * 1000) // Next day
          };

          const review = await storage.createReview?.(reviewData);
          if (review) {
            reviews.push(review);
          }
        } catch (error) {
          console.error('Failed to create review:', error);
        }
      }
    }

    return reviews;
  }

  /**
   * Seed analytics data for testing dashboards and reports
   */
  private async seedAnalyticsData() {
    console.log('Seeding analytics data...');
    
    // Create historical bookings for analytics
    const analyticsBookings = BookingFactory.createAnalyticsData(100);
    
    for (const bookingData of analyticsBookings) {
      try {
        await storage.createBooking?.(bookingData);
      } catch (error) {
        console.error('Failed to create analytics booking:', error);
      }
    }
  }

  /**
   * Get seeded data
   */
  getSeededData(): SeedResult | null {
    return this.seedData;
  }

  /**
   * Check if database is seeded
   */
  isSeeded(): boolean {
    return this.isSeeded;
  }

  /**
   * Clear all test data
   */
  async clearTestData(): Promise<void> {
    console.log('Clearing test data...');
    
    try {
      // Clear in reverse dependency order
      await storage.clearReviews?.();
      await storage.clearBookings?.();
      await storage.clearProperties?.();
      await storage.clearUsers?.();
      
      this.isSeeded = false;
      this.seedData = null;
      
      console.log('Test data cleared successfully');
    } catch (error) {
      console.error('Failed to clear test data:', error);
      throw error;
    }
  }

  /**
   * Seed specific test scenario
   */
  async seedTestScenario(scenarioName: string): Promise<SeedResult> {
    console.log(`Seeding test scenario: ${scenarioName}`);
    
    switch (scenarioName) {
      case 'minimal':
        return this.seedDatabase({
          users: 2,
          owners: 1,
          admins: 1,
          propertiesPerOwner: 1,
          bookingsPerProperty: 1,
          includeReviews: false
        });

      case 'booking-flow':
        return this.seedDatabase({
          users: 5,
          owners: 2,
          admins: 1,
          propertiesPerOwner: 2,
          bookingsPerProperty: 3,
          includePastBookings: true,
          includeReviews: true
        });

      case 'owner-dashboard':
        return this.seedDatabase({
          users: 8,
          owners: 1,
          admins: 1,
          propertiesPerOwner: 5,
          bookingsPerProperty: 4,
          includePastBookings: true,
          includeReviews: true,
          seedAnalyticsData: true
        });

      case 'search-performance':
        return this.seedDatabase({
          users: 20,
          owners: 5,
          admins: 1,
          propertiesPerOwner: 4,
          bookingsPerProperty: 2,
          includePastBookings: false,
          includeReviews: false
        });

      case 'full-system':
        return this.seedDatabase({
          users: 50,
          owners: 10,
          admins: 2,
          propertiesPerOwner: 3,
          bookingsPerProperty: 5,
          includePastBookings: true,
          includeReviews: true,
          seedAnalyticsData: true
        });

      default:
        throw new Error(`Unknown test scenario: ${scenarioName}`);
    }
  }

  /**
   * Get test user credentials by role
   */
  getTestCredentials(role?: string) {
    if (!this.seedData) {
      throw new Error('Database not seeded yet');
    }

    if (role) {
      return this.seedData.credentials.users.filter(u => u.role === role);
    }

    return this.seedData.credentials.users;
  }

  /**
   * Get test user by email
   */
  getTestUser(email: string) {
    if (!this.seedData) {
      throw new Error('Database not seeded yet');
    }

    return this.seedData.users.find(u => u.email === email);
  }

  /**
   * Get test properties by owner
   */
  getTestPropertiesByOwner(ownerId: number) {
    if (!this.seedData) {
      throw new Error('Database not seeded yet');
    }

    return this.seedData.properties.filter(p => p.ownerId === ownerId);
  }

  /**
   * Get test bookings by user
   */
  getTestBookingsByUser(userId: number) {
    if (!this.seedData) {
      throw new Error('Database not seeded yet');
    }

    return this.seedData.bookings.filter(b => b.userId === userId);
  }

  /**
   * Generate realistic review comments
   */
  private generateReviewComment(): string {
    const comments = [
      'Amazing property with great amenities. Highly recommended!',
      'Perfect getaway spot. Clean, comfortable, and well-maintained.',
      'Beautiful location with stunning views. Will definitely return.',
      'Excellent hospitality and service. Everything was perfect.',
      'Great value for money. The property exceeded our expectations.',
      'Peaceful and serene environment. Exactly what we needed.',
      'Well-equipped kitchen and spacious rooms. Very comfortable stay.',
      'The host was very responsive and helpful throughout our stay.',
      'Beautiful property but could use some maintenance updates.',
      'Good location but the amenities could be improved.',
      'Outstanding experience! The property photos don\'t do it justice.',
      'Perfect for a family vacation. Kids loved the pool and garden.',
      'Very clean and modern. Great attention to detail.',
      'Fantastic views from every room. Truly breathtaking.',
      'The perfect blend of luxury and comfort. Highly recommended.'
    ];

    return comments[Math.floor(Math.random() * comments.length)];
  }

  /**
   * Create test data for specific user types
   */
  async createTestUser(type: 'admin' | 'owner' | 'user', overrides: any = {}) {
    let userData;
    
    switch (type) {
      case 'admin':
        userData = UserFactory.createAdmin(overrides);
        break;
      case 'owner':
        userData = UserFactory.createOwner(overrides);
        break;
      case 'user':
      default:
        userData = UserFactory.create({ role: 'user', ...overrides });
        break;
    }

    return this.createUserInDatabase(userData);
  }

  /**
   * Create test property for specific owner
   */
  async createTestProperty(ownerId: number, overrides: any = {}) {
    const propertyData = PropertyFactory.create({ ownerId, ...overrides });
    
    return await storage.createProperty(propertyData);
  }

  /**
   * Create test booking for specific user and property
   */
  async createTestBooking(userId: number, propertyId: number, overrides: any = {}) {
    const bookingData = BookingFactory.create({ userId, propertyId, ...overrides });
    
    return await storage.createBooking(bookingData);
  }
}

// Export singleton instance
export const testDbSeeder = TestDatabaseSeeder.getInstance();

// Utility functions for common test scenarios
export const TestScenarios = {
  async minimal() {
    return testDbSeeder.seedTestScenario('minimal');
  },

  async bookingFlow() {
    return testDbSeeder.seedTestScenario('booking-flow');
  },

  async ownerDashboard() {
    return testDbSeeder.seedTestScenario('owner-dashboard');
  },

  async searchPerformance() {
    return testDbSeeder.seedTestScenario('search-performance');
  },

  async fullSystem() {
    return testDbSeeder.seedTestScenario('full-system');
  }
};