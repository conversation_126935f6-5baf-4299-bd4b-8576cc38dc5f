/**
 * Performance Tests for Critical Endpoints
 * Tests load, stress, and endurance performance for key API endpoints
 */

import { describe, test, beforeAll, afterAll } from 'vitest';
import { Express } from 'express';
import PerformanceTestRunner, { LoadTestConfig, PerformanceMetrics } from './PerformanceTestFramework';
import { UserFactory, PropertyFactory, BookingFactory } from '../factories';
import { storage } from '../../server/storage';

// Import your app
let app: Express;

describe('Critical Endpoints Performance Tests', () => {
  let performanceRunner: PerformanceTestRunner;
  let authToken: string;
  let ownerId: number;
  let propertyId: number;

  beforeAll(async () => {
    // Import app dynamically to avoid circular dependencies
    const { app: testApp } = await import('../../server/index');
    app = testApp;
    
    performanceRunner = new PerformanceTestRunner(app);

    // Set up test data
    await setupTestData();
  }, 30000); // 30 second timeout for setup

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
    
    // Export performance results
    performanceRunner.exportResults(`performance-results-${Date.now()}.json`);
  });

  async function setupTestData() {
    console.log('Setting up performance test data...');
    
    // Create test users and properties for performance testing
    const ownerData = UserFactory.createOwner({
      email: '<EMAIL>',
      password: 'PerfTestPassword123!'
    });

    // Register owner
    const request = require('supertest')(app);
    const ownerResponse = await request
      .post('/api/auth/register')
      .send(ownerData);

    authToken = ownerResponse.body.data.token;
    ownerId = ownerResponse.body.data.user.id;

    // Create multiple properties for testing
    const properties = PropertyFactory.createMany(20, { ownerId });
    
    for (const property of properties) {
      const response = await request
        .post('/api/properties')
        .set('Authorization', `Bearer ${authToken}`)
        .send(PropertyFactory.createFormData(property));
      
      if (!propertyId) {
        propertyId = response.body.data.id;
      }
    }

    // Create test users
    const users = UserFactory.createMany(10);
    for (const user of users) {
      await request
        .post('/api/auth/register')
        .send(UserFactory.createRegistrationData(user));
    }

    console.log('Performance test data setup completed');
  }

  async function cleanupTestData() {
    try {
      await storage.clearTestData?.();
    } catch (error) {
      console.warn('Cleanup warning:', error);
    }
  }

  describe('Authentication Endpoints Performance', () => {
    test('POST /api/auth/login - Load Test', async () => {
      const config: LoadTestConfig = {
        endpoint: '/api/auth/login',
        method: 'POST',
        concurrency: 10,
        totalRequests: 100,
        payload: {
          email: '<EMAIL>',
          password: 'PerfTestPassword123!'
        },
        warmupRequests: 5,
        thresholds: {
          averageResponseTime: 500, // 500ms
          p95ResponseTime: 1000,    // 1s
          errorRate: 0.02,          // 2%
          requestsPerSecond: 20     // 20 RPS
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      // Assertions
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
      expect(metrics.p95ResponseTime).toBeLessThan(config.thresholds.p95ResponseTime);
      expect(metrics.requestsPerSecond).toBeGreaterThan(config.thresholds.requestsPerSecond);
    }, 60000);

    test('GET /api/auth/me - Concurrent Access Test', async () => {
      const config: LoadTestConfig = {
        endpoint: '/api/auth/me',
        method: 'GET',
        concurrency: 20,
        totalRequests: 200,
        authentication: {
          type: 'bearer',
          token: authToken
        },
        thresholds: {
          averageResponseTime: 200,
          p95ResponseTime: 500,
          errorRate: 0.01,
          requestsPerSecond: 50
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
    }, 45000);
  });

  describe('Property Endpoints Performance', () => {
    test('GET /api/properties - High Load Test', async () => {
      const config: LoadTestConfig = {
        endpoint: '/api/properties',
        method: 'GET',
        concurrency: 25,
        totalRequests: 500,
        warmupRequests: 10,
        thresholds: {
          averageResponseTime: 300,
          p95ResponseTime: 800,
          errorRate: 0.01,
          requestsPerSecond: 50
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
      expect(metrics.requestsPerSecond).toBeGreaterThan(config.thresholds.requestsPerSecond);
    }, 90000);

    test('GET /api/properties/:id - Cache Performance Test', async () => {
      const config: LoadTestConfig = {
        endpoint: `/api/properties/${propertyId}`,
        method: 'GET',
        concurrency: 30,
        totalRequests: 300,
        thresholds: {
          averageResponseTime: 150, // Should be fast due to caching
          p95ResponseTime: 400,
          errorRate: 0.005,
          requestsPerSecond: 75
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
    }, 60000);

    test('GET /api/properties - Search Query Performance', async () => {
      const config: LoadTestConfig = {
        endpoint: '/api/properties?location=Goa&minPrice=2000&maxPrice=10000&featured=true',
        method: 'GET',
        concurrency: 15,
        totalRequests: 150,
        thresholds: {
          averageResponseTime: 400,
          p95ResponseTime: 1000,
          errorRate: 0.01,
          requestsPerSecond: 30
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
    }, 60000);

    test('POST /api/properties - Property Creation Load', async () => {
      const propertyData = PropertyFactory.createFormData();
      
      const config: LoadTestConfig = {
        endpoint: '/api/properties',
        method: 'POST',
        concurrency: 5, // Lower concurrency for write operations
        totalRequests: 25,
        payload: propertyData,
        authentication: {
          type: 'bearer',
          token: authToken
        },
        thresholds: {
          averageResponseTime: 800,
          p95ResponseTime: 1500,
          errorRate: 0.02,
          requestsPerSecond: 10
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
    }, 45000);
  });

  describe('Booking Endpoints Performance', () => {
    test('POST /api/bookings - Booking Creation Under Load', async () => {
      const bookingData = BookingFactory.createFormData({
        propertyId: propertyId
      });

      const config: LoadTestConfig = {
        endpoint: '/api/bookings',
        method: 'POST',
        concurrency: 8,
        totalRequests: 40,
        payload: bookingData,
        authentication: {
          type: 'bearer',
          token: authToken
        },
        thresholds: {
          averageResponseTime: 600,
          p95ResponseTime: 1200,
          errorRate: 0.15, // Higher tolerance due to date conflicts
          requestsPerSecond: 15
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      // Note: Some bookings may conflict, so we allow higher error rate
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
    }, 60000);

    test('GET /api/bookings - User Bookings Performance', async () => {
      const config: LoadTestConfig = {
        endpoint: '/api/bookings',
        method: 'GET',
        concurrency: 12,
        totalRequests: 120,
        authentication: {
          type: 'bearer',
          token: authToken
        },
        thresholds: {
          averageResponseTime: 250,
          p95ResponseTime: 600,
          errorRate: 0.01,
          requestsPerSecond: 40
        }
      };

      const metrics = await performanceRunner.runLoadTest(config);
      
      expect(metrics.errorRate).toBeLessThan(config.thresholds.errorRate);
      expect(metrics.averageResponseTime).toBeLessThan(config.thresholds.averageResponseTime);
    }, 45000);
  });

  describe('Stress Testing', () => {
    test('Properties Endpoint Stress Test', async () => {
      const stressConfig = {
        endpoint: '/api/properties',
        method: 'GET' as const,
        concurrency: 5,
        maxConcurrency: 50,
        concurrencyStep: 5,
        stepDuration: 10, // 10 seconds per step
        totalRequests: 100,
        thresholds: {
          averageResponseTime: 500,
          p95ResponseTime: 1500,
          errorRate: 0.05,
          requestsPerSecond: 20
        }
      };

      const results = await performanceRunner.runStressTest(stressConfig);
      
      // Find the breaking point
      const breakingPoint = results.find(result => 
        result.errorRate > 0.1 || result.averageResponseTime > 2000
      );

      if (breakingPoint) {
        console.log(`System breaking point found at concurrency level with error rate: ${(breakingPoint.errorRate * 100).toFixed(2)}%`);
      }

      // Ensure system can handle at least baseline load
      const baselineResult = results[0];
      expect(baselineResult.errorRate).toBeLessThan(0.05);
    }, 300000); // 5 minute timeout for stress test
  });

  describe('Endurance Testing', () => {
    test('Properties Endpoint Endurance Test', async () => {
      const enduranceConfig: LoadTestConfig = {
        endpoint: '/api/properties',
        method: 'GET',
        concurrency: 10,
        totalRequests: 600, // Will be overridden by duration
        thresholds: {
          averageResponseTime: 400,
          p95ResponseTime: 1000,
          errorRate: 0.02,
          requestsPerSecond: 25
        }
      };

      const results = await performanceRunner.runEnduranceTest(enduranceConfig, 15); // 15 minutes
      
      // Check for performance degradation over time
      const firstInterval = results[0];
      const lastInterval = results[results.length - 1];
      
      const responseTimeDegradation = 
        (lastInterval.averageResponseTime - firstInterval.averageResponseTime) / firstInterval.averageResponseTime;
      
      // Allow up to 20% degradation over time
      expect(responseTimeDegradation).toBeLessThan(0.2);
      
      // Check for memory leaks
      expect(lastInterval.memoryUsage.heapUsed).toBeLessThan(200 * 1024 * 1024); // 200MB
    }, 1200000); // 20 minute timeout for endurance test
  });

  describe('Spike Testing', () => {
    test('Authentication Endpoint Spike Test', async () => {
      const spikeConfig: LoadTestConfig = {
        endpoint: '/api/auth/login',
        method: 'POST',
        concurrency: 5,
        totalRequests: 50,
        payload: {
          email: '<EMAIL>',
          password: 'PerfTestPassword123!'
        },
        thresholds: {
          averageResponseTime: 600,
          p95ResponseTime: 1200,
          errorRate: 0.05,
          requestsPerSecond: 15
        }
      };

      const results = await performanceRunner.runSpikeTest(spikeConfig, 5); // 5x spike
      
      // Baseline should be stable
      expect(results.baseline.errorRate).toBeLessThan(0.02);
      
      // System should recover after spike
      const recoveryDegradation = 
        (results.recovery.averageResponseTime - results.baseline.averageResponseTime) / 
        results.baseline.averageResponseTime;
      
      expect(recoveryDegradation).toBeLessThan(0.1); // Allow 10% degradation post-spike
    }, 120000);
  });

  describe('Performance Benchmarks', () => {
    test('Complete Benchmark Suite', async () => {
      // Set up authentication token for protected endpoints
      const benchmarkResults = await performanceRunner.runBenchmarkSuite();
      
      // Validate all critical endpoints meet performance requirements
      Object.entries(benchmarkResults).forEach(([endpoint, metrics]) => {
        console.log(`\nBenchmark Results for ${endpoint}:`);
        console.log(`- Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
        console.log(`- P95 Response Time: ${metrics.p95ResponseTime.toFixed(2)}ms`);
        console.log(`- Requests per Second: ${metrics.requestsPerSecond.toFixed(2)}`);
        console.log(`- Error Rate: ${(metrics.errorRate * 100).toFixed(2)}%`);
        
        // Basic performance assertions
        expect(metrics.averageResponseTime).toBeLessThan(1000); // 1 second max
        expect(metrics.errorRate).toBeLessThan(0.1); // 10% max error rate
      });
    }, 180000);
  });

  describe('Memory and Resource Monitoring', () => {
    test('Memory Usage During High Load', async () => {
      const { PerformanceTestUtils } = await import('./PerformanceTestFramework');
      
      const { result, resourceUsage } = await PerformanceTestUtils.monitorResources(
        async () => {
          const config: LoadTestConfig = {
            endpoint: '/api/properties',
            method: 'GET',
            concurrency: 20,
            totalRequests: 200,
            thresholds: {
              averageResponseTime: 400,
              p95ResponseTime: 1000,
              errorRate: 0.02,
              requestsPerSecond: 40
            }
          };
          
          return await performanceRunner.runLoadTest(config);
        },
        500 // Monitor every 500ms
      );
      
      // Check for memory leaks
      const initialMemory = resourceUsage[0]?.memory.heapUsed || 0;
      const finalMemory = resourceUsage[resourceUsage.length - 1]?.memory.heapUsed || 0;
      const memoryIncrease = (finalMemory - initialMemory) / initialMemory;
      
      // Allow up to 50% memory increase during test
      expect(memoryIncrease).toBeLessThan(0.5);
      
      // Check that memory doesn't exceed reasonable limits
      expect(finalMemory).toBeLessThan(500 * 1024 * 1024); // 500MB
    }, 90000);
  });
});