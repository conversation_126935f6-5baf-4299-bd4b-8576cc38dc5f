/**
 * Performance Testing Framework
 * Provides tools for load testing, stress testing, and performance monitoring
 */

import { performance } from 'perf_hooks';
import request from 'supertest';
import { Express } from 'express';

export interface PerformanceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  medianResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  memoryUsage: {
    rss: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  duration: number;
}

export interface LoadTestConfig {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  concurrency: number;
  totalRequests: number;
  duration?: number; // in seconds
  rampUpTime?: number; // in seconds
  payload?: any;
  headers?: Record<string, string>;
  authentication?: {
    type: 'bearer' | 'basic';
    token?: string;
    username?: string;
    password?: string;
  };
  warmupRequests?: number;
  thresholds: {
    averageResponseTime: number; // ms
    p95ResponseTime: number; // ms
    errorRate: number; // percentage
    requestsPerSecond: number;
  };
}

export interface StressTestConfig extends LoadTestConfig {
  maxConcurrency: number;
  concurrencyStep: number;
  stepDuration: number; // seconds per step
}

export class PerformanceTestRunner {
  private app: Express;
  private results: PerformanceMetrics[] = [];

  constructor(app: Express) {
    this.app = app;
  }

  /**
   * Run a load test with specified configuration
   */
  async runLoadTest(config: LoadTestConfig): Promise<PerformanceMetrics> {
    console.log(`Starting load test: ${config.method} ${config.endpoint}`);
    console.log(`Configuration: ${config.concurrency} concurrent users, ${config.totalRequests} total requests`);

    // Warmup phase
    if (config.warmupRequests) {
      await this.runWarmup(config);
    }

    const startTime = performance.now();
    const responseTimes: number[] = [];
    const errors: Error[] = [];
    let completedRequests = 0;

    // Track memory usage
    const initialMemory = process.memoryUsage();

    // Create request promises
    const requestPromises: Promise<void>[] = [];
    const requestsPerWorker = Math.floor(config.totalRequests / config.concurrency);
    const extraRequests = config.totalRequests % config.concurrency;

    for (let i = 0; i < config.concurrency; i++) {
      const workerRequests = requestsPerWorker + (i < extraRequests ? 1 : 0);
      const workerPromise = this.runWorker(config, workerRequests, responseTimes, errors);
      requestPromises.push(workerPromise);
    }

    // Wait for all workers to complete
    await Promise.all(requestPromises);

    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // seconds

    const finalMemory = process.memoryUsage();
    const memoryDelta = {
      rss: finalMemory.rss - initialMemory.rss,
      heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
      heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
      external: finalMemory.external - initialMemory.external
    };

    const metrics = this.calculateMetrics(responseTimes, errors, duration, memoryDelta);
    this.results.push(metrics);

    console.log('Load test completed:');
    console.log(`- Total requests: ${metrics.totalRequests}`);
    console.log(`- Success rate: ${((1 - metrics.errorRate) * 100).toFixed(2)}%`);
    console.log(`- Average response time: ${metrics.averageResponseTime.toFixed(2)}ms`);
    console.log(`- P95 response time: ${metrics.p95ResponseTime.toFixed(2)}ms`);
    console.log(`- Requests per second: ${metrics.requestsPerSecond.toFixed(2)}`);

    return metrics;
  }

  /**
   * Run a stress test with increasing load
   */
  async runStressTest(config: StressTestConfig): Promise<PerformanceMetrics[]> {
    console.log(`Starting stress test: ${config.method} ${config.endpoint}`);
    
    const results: PerformanceMetrics[] = [];
    let currentConcurrency = config.concurrency;

    while (currentConcurrency <= config.maxConcurrency) {
      console.log(`\nStress test step: ${currentConcurrency} concurrent users`);
      
      const stepConfig: LoadTestConfig = {
        ...config,
        concurrency: currentConcurrency,
        totalRequests: currentConcurrency * config.stepDuration * 2, // 2 requests per second per user
        duration: config.stepDuration
      };

      const stepResult = await this.runLoadTest(stepConfig);
      results.push(stepResult);

      // Check if system is breaking down
      if (stepResult.errorRate > 0.5 || stepResult.averageResponseTime > config.thresholds.averageResponseTime * 10) {
        console.log('System breakdown detected, stopping stress test');
        break;
      }

      currentConcurrency += config.concurrencyStep;
    }

    return results;
  }

  /**
   * Run endurance test (sustained load over time)
   */
  async runEnduranceTest(config: LoadTestConfig, durationMinutes: number): Promise<PerformanceMetrics[]> {
    console.log(`Starting endurance test: ${durationMinutes} minutes`);
    
    const results: PerformanceMetrics[] = [];
    const intervalMinutes = 5; // Report every 5 minutes
    const totalIntervals = Math.ceil(durationMinutes / intervalMinutes);

    for (let i = 0; i < totalIntervals; i++) {
      const intervalDuration = Math.min(intervalMinutes, durationMinutes - (i * intervalMinutes));
      console.log(`\nEndurance test interval ${i + 1}/${totalIntervals} (${intervalDuration} minutes)`);
      
      const intervalConfig: LoadTestConfig = {
        ...config,
        duration: intervalDuration * 60, // convert to seconds
        totalRequests: config.concurrency * intervalDuration * 60 // 1 request per second per user
      };

      const intervalResult = await this.runLoadTest(intervalConfig);
      results.push(intervalResult);

      // Monitor for memory leaks
      if (intervalResult.memoryUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
        console.warn('High memory usage detected:', intervalResult.memoryUsage);
      }
    }

    return results;
  }

  /**
   * Run spike test (sudden load increase)
   */
  async runSpikeTest(config: LoadTestConfig, spikeFactor: number = 5): Promise<{
    baseline: PerformanceMetrics;
    spike: PerformanceMetrics;
    recovery: PerformanceMetrics;
  }> {
    console.log(`Starting spike test with ${spikeFactor}x load increase`);

    // Baseline phase
    console.log('\nRunning baseline load...');
    const baseline = await this.runLoadTest(config);

    // Spike phase
    console.log('\nRunning spike load...');
    const spikeConfig: LoadTestConfig = {
      ...config,
      concurrency: config.concurrency * spikeFactor,
      totalRequests: config.totalRequests * spikeFactor
    };
    const spike = await this.runLoadTest(spikeConfig);

    // Recovery phase
    console.log('\nRunning recovery load...');
    const recovery = await this.runLoadTest(config);

    return { baseline, spike, recovery };
  }

  /**
   * Performance benchmark for critical endpoints
   */
  async runBenchmarkSuite(baseUrl: string = ''): Promise<Record<string, PerformanceMetrics>> {
    const benchmarks: Record<string, LoadTestConfig> = {
      'GET /api/properties': {
        endpoint: `${baseUrl}/api/properties`,
        method: 'GET',
        concurrency: 10,
        totalRequests: 100,
        thresholds: {
          averageResponseTime: 200,
          p95ResponseTime: 500,
          errorRate: 0.01,
          requestsPerSecond: 50
        }
      },
      'GET /api/properties/:id': {
        endpoint: `${baseUrl}/api/properties/1`,
        method: 'GET',
        concurrency: 20,
        totalRequests: 200,
        thresholds: {
          averageResponseTime: 100,
          p95ResponseTime: 300,
          errorRate: 0.01,
          requestsPerSecond: 100
        }
      },
      'POST /api/auth/login': {
        endpoint: `${baseUrl}/api/auth/login`,
        method: 'POST',
        concurrency: 5,
        totalRequests: 50,
        payload: {
          email: '<EMAIL>',
          password: 'TestPassword123!'
        },
        thresholds: {
          averageResponseTime: 300,
          p95ResponseTime: 800,
          errorRate: 0.02,
          requestsPerSecond: 20
        }
      },
      'GET /api/auth/me': {
        endpoint: `${baseUrl}/api/auth/me`,
        method: 'GET',
        concurrency: 15,
        totalRequests: 150,
        authentication: {
          type: 'bearer',
          token: 'test-jwt-token'
        },
        thresholds: {
          averageResponseTime: 150,
          p95ResponseTime: 400,
          errorRate: 0.01,
          requestsPerSecond: 75
        }
      }
    };

    const results: Record<string, PerformanceMetrics> = {};

    for (const [name, config] of Object.entries(benchmarks)) {
      console.log(`\nRunning benchmark: ${name}`);
      try {
        const result = await this.runLoadTest(config);
        results[name] = result;
        
        // Check thresholds
        this.validateThresholds(result, config.thresholds, name);
      } catch (error) {
        console.error(`Benchmark failed for ${name}:`, error);
      }
    }

    return results;
  }

  /**
   * Validate performance thresholds
   */
  private validateThresholds(
    metrics: PerformanceMetrics, 
    thresholds: LoadTestConfig['thresholds'], 
    testName: string
  ): void {
    const violations: string[] = [];

    if (metrics.averageResponseTime > thresholds.averageResponseTime) {
      violations.push(`Average response time: ${metrics.averageResponseTime.toFixed(2)}ms > ${thresholds.averageResponseTime}ms`);
    }

    if (metrics.p95ResponseTime > thresholds.p95ResponseTime) {
      violations.push(`P95 response time: ${metrics.p95ResponseTime.toFixed(2)}ms > ${thresholds.p95ResponseTime}ms`);
    }

    if (metrics.errorRate > thresholds.errorRate) {
      violations.push(`Error rate: ${(metrics.errorRate * 100).toFixed(2)}% > ${(thresholds.errorRate * 100).toFixed(2)}%`);
    }

    if (metrics.requestsPerSecond < thresholds.requestsPerSecond) {
      violations.push(`Requests per second: ${metrics.requestsPerSecond.toFixed(2)} < ${thresholds.requestsPerSecond}`);
    }

    if (violations.length > 0) {
      console.warn(`⚠️  Performance threshold violations for ${testName}:`);
      violations.forEach(violation => console.warn(`   - ${violation}`));
    } else {
      console.log(`✅ All performance thresholds passed for ${testName}`);
    }
  }

  /**
   * Run warmup requests
   */
  private async runWarmup(config: LoadTestConfig): Promise<void> {
    console.log(`Running ${config.warmupRequests} warmup requests...`);
    
    const warmupPromises: Promise<void>[] = [];
    for (let i = 0; i < config.warmupRequests!; i++) {
      warmupPromises.push(this.makeSingleRequest(config).catch(() => {})); // Ignore warmup errors
    }
    
    await Promise.all(warmupPromises);
    console.log('Warmup completed');
  }

  /**
   * Run worker for concurrent requests
   */
  private async runWorker(
    config: LoadTestConfig,
    requestCount: number,
    responseTimes: number[],
    errors: Error[]
  ): Promise<void> {
    for (let i = 0; i < requestCount; i++) {
      try {
        const startTime = performance.now();
        await this.makeSingleRequest(config);
        const endTime = performance.now();
        
        responseTimes.push(endTime - startTime);
      } catch (error) {
        errors.push(error as Error);
      }
    }
  }

  /**
   * Make a single HTTP request
   */
  private async makeSingleRequest(config: LoadTestConfig): Promise<any> {
    let req = request(this.app)[config.method.toLowerCase() as 'get'](config.endpoint);

    // Add headers
    if (config.headers) {
      Object.entries(config.headers).forEach(([key, value]) => {
        req = req.set(key, value);
      });
    }

    // Add authentication
    if (config.authentication) {
      if (config.authentication.type === 'bearer' && config.authentication.token) {
        req = req.set('Authorization', `Bearer ${config.authentication.token}`);
      } else if (config.authentication.type === 'basic' && config.authentication.username && config.authentication.password) {
        req = req.auth(config.authentication.username, config.authentication.password);
      }
    }

    // Add payload for non-GET requests
    if (config.payload && config.method !== 'GET') {
      req = req.send(config.payload);
    }

    return req;
  }

  /**
   * Calculate performance metrics
   */
  private calculateMetrics(
    responseTimes: number[], 
    errors: Error[], 
    duration: number,
    memoryUsage: any
  ): PerformanceMetrics {
    const totalRequests = responseTimes.length + errors.length;
    const successfulRequests = responseTimes.length;
    const failedRequests = errors.length;

    if (responseTimes.length === 0) {
      throw new Error('No successful requests to calculate metrics');
    }

    const sortedTimes = responseTimes.sort((a, b) => a - b);
    const sum = responseTimes.reduce((acc, time) => acc + time, 0);

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: sum / responseTimes.length,
      minResponseTime: sortedTimes[0],
      maxResponseTime: sortedTimes[sortedTimes.length - 1],
      medianResponseTime: this.getPercentile(sortedTimes, 50),
      p95ResponseTime: this.getPercentile(sortedTimes, 95),
      p99ResponseTime: this.getPercentile(sortedTimes, 99),
      requestsPerSecond: totalRequests / duration,
      errorRate: failedRequests / totalRequests,
      memoryUsage,
      duration
    };
  }

  /**
   * Calculate percentile from sorted array
   */
  private getPercentile(sortedArray: number[], percentile: number): number {
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, index)];
  }

  /**
   * Get all test results
   */
  getResults(): PerformanceMetrics[] {
    return this.results;
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.results = [];
  }

  /**
   * Export results to JSON
   */
  exportResults(filename?: string): string {
    const results = {
      timestamp: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: process.memoryUsage()
      },
      results: this.results
    };

    const json = JSON.stringify(results, null, 2);
    
    if (filename) {
      const fs = require('fs');
      fs.writeFileSync(filename, json);
      console.log(`Results exported to ${filename}`);
    }

    return json;
  }
}

/**
 * Performance Test Utilities
 */
export class PerformanceTestUtils {
  /**
   * Monitor system resources during test
   */
  static async monitorResources(
    testFunction: () => Promise<any>,
    interval: number = 1000
  ): Promise<{
    result: any;
    resourceUsage: Array<{
      timestamp: number;
      cpu: NodeJS.CpuUsage;
      memory: NodeJS.MemoryUsage;
    }>;
  }> {
    const resourceUsage: Array<{
      timestamp: number;
      cpu: NodeJS.CpuUsage;
      memory: NodeJS.MemoryUsage;
    }> = [];

    let monitoring = true;
    const startCpuUsage = process.cpuUsage();

    // Start monitoring
    const monitor = setInterval(() => {
      if (monitoring) {
        resourceUsage.push({
          timestamp: Date.now(),
          cpu: process.cpuUsage(startCpuUsage),
          memory: process.memoryUsage()
        });
      }
    }, interval);

    try {
      const result = await testFunction();
      monitoring = false;
      clearInterval(monitor);
      return { result, resourceUsage };
    } catch (error) {
      monitoring = false;
      clearInterval(monitor);
      throw error;
    }
  }

  /**
   * Compare performance metrics
   */
  static compareMetrics(baseline: PerformanceMetrics, current: PerformanceMetrics): {
    improvements: string[];
    regressions: string[];
    summary: string;
  } {
    const improvements: string[] = [];
    const regressions: string[] = [];

    // Response time comparison
    const responseTimeDiff = ((current.averageResponseTime - baseline.averageResponseTime) / baseline.averageResponseTime) * 100;
    if (responseTimeDiff < -5) {
      improvements.push(`Average response time improved by ${Math.abs(responseTimeDiff).toFixed(1)}%`);
    } else if (responseTimeDiff > 5) {
      regressions.push(`Average response time regressed by ${responseTimeDiff.toFixed(1)}%`);
    }

    // Throughput comparison
    const throughputDiff = ((current.requestsPerSecond - baseline.requestsPerSecond) / baseline.requestsPerSecond) * 100;
    if (throughputDiff > 5) {
      improvements.push(`Throughput improved by ${throughputDiff.toFixed(1)}%`);
    } else if (throughputDiff < -5) {
      regressions.push(`Throughput regressed by ${Math.abs(throughputDiff).toFixed(1)}%`);
    }

    // Error rate comparison
    const errorRateDiff = current.errorRate - baseline.errorRate;
    if (errorRateDiff < -0.01) {
      improvements.push(`Error rate improved by ${Math.abs(errorRateDiff * 100).toFixed(2)}%`);
    } else if (errorRateDiff > 0.01) {
      regressions.push(`Error rate regressed by ${(errorRateDiff * 100).toFixed(2)}%`);
    }

    const summary = regressions.length > 0 
      ? `Performance regression detected (${regressions.length} issues)`
      : improvements.length > 0 
        ? `Performance improvements detected (${improvements.length} improvements)`
        : 'Performance is stable';

    return { improvements, regressions, summary };
  }
}

export { PerformanceTestRunner as default };