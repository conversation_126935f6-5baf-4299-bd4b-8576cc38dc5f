/**
 * Test Data Factories - Central Export
 * Provides easy access to all test data factories
 */

// Import factories for internal use
import { UserFactory, TEST_USERS, TEST_CREDENTIALS } from './UserFactory';
import { PropertyFactory, TEST_PROPERTIES, TEST_SEARCH_FILTERS } from './PropertyFactory';
import { BookingFactory, TEST_BOOKINGS } from './BookingFactory';

// Export all factories
export { UserFactory, TEST_USERS, TEST_CREDENTIALS } from './UserFactory';
export { PropertyFactory, TEST_PROPERTIES, TEST_SEARCH_FILTERS } from './PropertyFactory';
export { BookingFactory, TEST_BOOKINGS } from './BookingFactory';

// Export all types
export type { UserTestData, UserCredentials } from './UserFactory';
export type { PropertyTestData, PropertySearchFilters } from './PropertyFactory';
export type { BookingTestData } from './BookingFactory';

/**
 * Database Seeding Utility
 * Provides methods to seed test database with consistent data
 */
export class TestDataSeeder {
  /**
   * Seed database with a complete dataset for testing
   */
  static createFullDataset() {
    // Reset all counters for consistent IDs
    UserFactory.resetCounter();
    PropertyFactory.resetCounter();
    BookingFactory.resetCounter();

    // Create users
    const admin = UserFactory.createAdmin({ id: 1 });
    const owners = UserFactory.createMany(3, { role: 'owner' }).map((owner, index) => ({
      ...owner,
      id: index + 2
    }));
    const users = UserFactory.createMany(5, { role: 'user' }).map((user, index) => ({
      ...user,
      id: index + 5
    }));

    // Create properties for each owner
    const properties = [];
    let propertyId = 1;
    for (const owner of owners) {
      const ownerProperties = PropertyFactory.createMany(2, { ownerId: owner.id }).map(property => ({
        ...property,
        id: propertyId++,
        owner
      }));
      properties.push(...ownerProperties);
    }

    // Create bookings
    const bookings = [];
    let bookingId = 1;
    for (let i = 0; i < 10; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const property = properties[Math.floor(Math.random() * properties.length)];
      
      const booking = BookingFactory.createWithUserAndProperty(user, property, {
        id: bookingId++
      });
      bookings.push(booking);
    }

    return {
      users: [admin, ...owners, ...users],
      properties,
      bookings
    };
  }

  /**
   * Create minimal dataset for unit tests
   */
  static createMinimalDataset() {
    UserFactory.resetCounter();
    PropertyFactory.resetCounter();
    BookingFactory.resetCounter();

    const user = UserFactory.create({ id: 1 });
    const owner = UserFactory.createOwner({ id: 2 });
    const property = PropertyFactory.createWithOwner(owner, { id: 1 });
    const booking = BookingFactory.createWithUserAndProperty(user, property, { id: 1 });

    return { user, owner, property, booking };
  }

  /**
   * Create performance test dataset
   */
  static createPerformanceDataset(scale: 'small' | 'medium' | 'large' = 'medium') {
    const scales = {
      small: { users: 10, properties: 20, bookings: 50 },
      medium: { users: 100, properties: 200, bookings: 500 },
      large: { users: 1000, properties: 2000, bookings: 5000 }
    };

    const { users: userCount, properties: propertyCount, bookings: bookingCount } = scales[scale];

    UserFactory.resetCounter();
    PropertyFactory.resetCounter();
    BookingFactory.resetCounter();

    // Create users with mixed roles
    const users = UserFactory.createMixedRoles({
      users: Math.floor(userCount * 0.7),
      owners: Math.floor(userCount * 0.25),
      admins: Math.floor(userCount * 0.05)
    });

    // Create properties with mixed statuses
    const properties = PropertyFactory.createMixedStatuses({
      active: Math.floor(propertyCount * 0.8),
      inactive: Math.floor(propertyCount * 0.1),
      draft: Math.floor(propertyCount * 0.1)
    });

    // Create bookings with mixed statuses
    const bookings = BookingFactory.createMixedStatuses({
      completed: Math.floor(bookingCount * 0.6),
      confirmed: Math.floor(bookingCount * 0.25),
      cancelled: Math.floor(bookingCount * 0.1),
      pending: Math.floor(bookingCount * 0.05)
    });

    return { users, properties, bookings };
  }

  /**
   * Create analytics test dataset
   */
  static createAnalyticsDataset() {
    UserFactory.resetCounter();
    PropertyFactory.resetCounter();
    BookingFactory.resetCounter();

    // Create time-series data for the last 12 months
    const bookings = BookingFactory.createAnalyticsData(200);
    
    // Create users and properties to match bookings
    const userIds = Array.from(new Set(bookings.map((b: any) => b.userId)));
    const propertyIds = Array.from(new Set(bookings.map((b: any) => b.propertyId)));
    
    const users = userIds.map((id: any) => UserFactory.createWithId(id!));
    const properties = propertyIds.map((id: any) => PropertyFactory.create({ id }));

    return { users, properties, bookings };
  }
}

/**
 * Test Scenario Builder
 * Helps create specific test scenarios with predefined data
 */
export class TestScenarioBuilder {
  /**
   * Create authentication test scenario
   */
  static authenticationScenario() {
    return {
      validUser: UserFactory.createForAuth(),
      invalidCredentials: TEST_CREDENTIALS.INVALID_PASSWORD,
      expiredUser: UserFactory.createWithExpiredConsent(),
      adminUser: UserFactory.createAdmin()
    };
  }

  /**
   * Create property booking scenario
   */
  static propertyBookingScenario() {
    const owner = UserFactory.createOwner();
    const user = UserFactory.create();
    const property = PropertyFactory.createWithOwner(owner);
    const booking = BookingFactory.createWithUserAndProperty(user, property);
    const conflictingBooking = BookingFactory.createConflicting(booking);

    return { owner, user, property, booking, conflictingBooking };
  }

  /**
   * Create search and filter scenario
   */
  static searchScenario() {
    const goaProperties = PropertyFactory.createMany(3, { location: 'Goa' });
    const luxuryProperties = PropertyFactory.createMany(2, { 
      halfDayPrice: 8000, 
      featured: true 
    });
    const budgetProperties = PropertyFactory.createMany(4, { 
      halfDayPrice: 2000 
    });

    return {
      allProperties: [...goaProperties, ...luxuryProperties, ...budgetProperties],
      searchFilters: TEST_SEARCH_FILTERS
    };
  }

  /**
   * Create owner dashboard scenario
   */
  static ownerDashboardScenario() {
    const owner = UserFactory.createOwner();
    const properties = PropertyFactory.createMany(3, { ownerId: owner.id! });
    const bookings = properties.flatMap((property: any) => 
      BookingFactory.createMany(2, { propertyId: property.id })
    );

    return { owner, properties, bookings };
  }

  /**
   * Create admin analytics scenario
   */
  static adminAnalyticsScenario() {
    const admin = UserFactory.createAdmin();
    const dataset = TestDataSeeder.createAnalyticsDataset();

    return { admin, ...dataset };
  }
}

/**
 * Mock Data Generator
 * Generates mock data for API responses and external services
 */
export class MockDataGenerator {
  /**
   * Generate mock payment response
   */
  static paymentResponse(success: boolean = true) {
    return {
      success,
      transactionId: `TXN${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
      amount: Math.floor(Math.random() * 10000) + 1000,
      currency: 'INR',
      paymentMethod: 'card',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Generate mock SMS/OTP response
   */
  static otpResponse(success: boolean = true) {
    return {
      success,
      messageId: `MSG${Math.random().toString(36).substr(2, 9)}`,
      code: success ? Math.floor(100000 + Math.random() * 900000).toString() : undefined,
      expiresIn: 300,
      message: success ? 'OTP sent successfully' : 'Failed to send OTP'
    };
  }

  /**
   * Generate mock file upload response
   */
  static uploadResponse(success: boolean = true) {
    return {
      success,
      url: success ? `https://cdn.farmhouse.com/uploads/${Date.now()}.jpg` : undefined,
      filename: success ? `property_${Date.now()}.jpg` : undefined,
      size: Math.floor(Math.random() * 5000000) + 100000,
      error: success ? undefined : 'Upload failed'
    };
  }
}