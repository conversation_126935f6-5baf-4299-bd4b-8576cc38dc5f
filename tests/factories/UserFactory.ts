/**
 * User Test Data Factory
 * Provides consistent test data generation for User entities
 */

import { faker } from '@faker-js/faker';

export interface UserTestData {
  id?: number;
  username?: string;
  email?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  phone?: string;
  role?: 'user' | 'owner' | 'admin';
  profileImage?: string;
  address?: string;
  bio?: string;
  dateOfBirth?: string;
  emergencyContact?: string;
  termsAccepted?: boolean;
  privacyPolicyAccepted?: boolean;
  cookiePolicyAccepted?: boolean;
  dataProcessingConsent?: boolean;
  marketingConsent?: boolean;
  newsletterSubscribed?: boolean;
  consentTimestamp?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UserCredentials {
  email: string;
  password: string;
}

export class UserFactory {
  private static counter = 1;

  /**
   * Generate a basic user with minimal required fields
   */
  static create(overrides: Partial<UserTestData> = {}): UserTestData {
    const id = this.counter++;
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const username = faker.internet.username({ firstName, lastName }).toLowerCase();
    
    return {
      id,
      username,
      email: faker.internet.email({ firstName, lastName }).toLowerCase(),
      password: 'TestPassword123!',
      firstName,
      lastName,
      fullName: `${firstName} ${lastName}`,
      phone: this.generateIndianPhone(),
      role: 'user',
      profileImage: faker.image.avatar(),
      address: faker.location.streetAddress({ useFullAddress: true }),
      bio: faker.lorem.sentences(2),
      dateOfBirth: faker.date.birthdate({ min: 18, max: 80, mode: 'age' }).toISOString().split('T')[0],
      emergencyContact: this.generateIndianPhone(),
      termsAccepted: true,
      privacyPolicyAccepted: true,
      cookiePolicyAccepted: true,
      dataProcessingConsent: true,
      marketingConsent: faker.datatype.boolean(),
      newsletterSubscribed: faker.datatype.boolean(),
      consentTimestamp: faker.date.recent(),
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      ...overrides
    };
  }

  /**
   * Generate a property owner user
   */
  static createOwner(overrides: Partial<UserTestData> = {}): UserTestData {
    return this.create({
      role: 'owner',
      bio: 'Experienced property owner with multiple vacation rentals',
      ...overrides
    });
  }

  /**
   * Generate an admin user
   */
  static createAdmin(overrides: Partial<UserTestData> = {}): UserTestData {
    return this.create({
      role: 'admin',
      email: '<EMAIL>',
      username: 'admin',
      fullName: 'System Administrator',
      ...overrides
    });
  }

  /**
   * Generate user registration data
   */
  static createRegistrationData(overrides: Partial<UserTestData> = {}): UserTestData {
    const user = this.create(overrides);
    return {
      username: user.username!,
      email: user.email!,
      password: user.password!,
      firstName: user.firstName!,
      lastName: user.lastName!,
      fullName: user.fullName!,
      phone: user.phone!,
      role: user.role!,
      termsAccepted: user.termsAccepted!,
      privacyPolicyAccepted: user.privacyPolicyAccepted!,
      cookiePolicyAccepted: user.cookiePolicyAccepted!,
      dataProcessingConsent: user.dataProcessingConsent!,
      marketingConsent: user.marketingConsent!,
      newsletterSubscribed: user.newsletterSubscribed!
    };
  }

  /**
   * Generate user login credentials
   */
  static createCredentials(overrides: Partial<UserCredentials> = {}): UserCredentials {
    const user = this.create();
    return {
      email: user.email!,
      password: user.password!,
      ...overrides
    };
  }

  /**
   * Generate user profile update data
   */
  static createProfileUpdateData(overrides: Partial<UserTestData> = {}): Partial<UserTestData> {
    return {
      fullName: faker.person.fullName(),
      phone: this.generateIndianPhone(),
      address: faker.location.streetAddress({ useFullAddress: true }),
      bio: faker.lorem.sentences(3),
      profileImage: faker.image.avatar(),
      emergencyContact: this.generateIndianPhone(),
      newsletterSubscribed: faker.datatype.boolean(),
      ...overrides
    };
  }

  /**
   * Generate multiple users
   */
  static createMany(count: number, overrides: Partial<UserTestData> = {}): UserTestData[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  /**
   * Generate a mix of users with different roles
   */
  static createMixedRoles(counts: { users?: number; owners?: number; admins?: number } = {}): UserTestData[] {
    const { users = 5, owners = 3, admins = 1 } = counts;
    
    return [
      ...this.createMany(users, { role: 'user' }),
      ...this.createMany(owners, { role: 'owner' }),
      ...this.createMany(admins, { role: 'admin' })
    ];
  }

  /**
   * Generate users with specific consent preferences
   */
  static createWithConsent(consents: {
    termsAccepted?: boolean;
    dataProcessingConsent?: boolean;
    marketingConsent?: boolean;
  }): UserTestData {
    return this.create({
      termsAccepted: consents.termsAccepted ?? true,
      dataProcessingConsent: consents.dataProcessingConsent ?? true,
      marketingConsent: consents.marketingConsent ?? false,
      consentTimestamp: new Date()
    });
  }

  /**
   * Generate user with invalid data for negative testing
   */
  static createInvalid(invalidField: 'email' | 'password' | 'phone' | 'username'): UserTestData {
    const user = this.create();
    
    switch (invalidField) {
      case 'email':
        user.email = 'invalid-email';
        break;
      case 'password':
        user.password = '123'; // Too short
        break;
      case 'phone':
        user.phone = '123'; // Invalid format
        break;
      case 'username':
        user.username = 'ab'; // Too short
        break;
    }
    
    return user;
  }

  /**
   * Generate test-specific users with predictable data
   */
  static createTestUser(testName: string): UserTestData {
    const id = testName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 1000;
    
    return this.create({
      id,
      username: `test_user_${testName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`,
      email: `test_${testName.toLowerCase().replace(/[^a-z0-9]/g, '_')}@test.com`,
      fullName: `Test User ${testName}`,
      password: 'TestPassword123!'
    });
  }

  /**
   * Reset factory counter for predictable IDs in tests
   */
  static resetCounter(): void {
    this.counter = 1;
  }

  /**
   * Generate a valid Indian phone number
   */
  private static generateIndianPhone(): string {
    // Generate Indian mobile number format +91XXXXXXXXXX
    const number = faker.string.numeric(10);
    return `+91${number}`;
  }

  /**
   * Generate user with specific ID for deterministic testing
   */
  static createWithId(id: number, overrides: Partial<UserTestData> = {}): UserTestData {
    return this.create({ id, ...overrides });
  }

  /**
   * Generate user for authentication testing
   */
  static createForAuth(overrides: Partial<UserTestData> = {}): UserTestData {
    return this.create({
      email: '<EMAIL>',
      password: 'AuthTestPassword123!',
      username: 'auth_test_user',
      fullName: 'Auth Test User',
      role: 'user',
      ...overrides
    });
  }

  /**
   * Generate user with expired consent for GDPR testing
   */
  static createWithExpiredConsent(): UserTestData {
    const oldDate = new Date();
    oldDate.setFullYear(oldDate.getFullYear() - 2); // 2 years ago
    
    return this.create({
      consentTimestamp: oldDate,
      termsAccepted: true,
      dataProcessingConsent: true,
      marketingConsent: false
    });
  }

  /**
   * Generate realistic name combinations
   */
  static generateRealisticName(): { firstName: string; lastName: string } {
    return {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName()
    };
  }

  /**
   * Create user with realistic profile data
   */
  static createWithRealisticProfile(overrides: Partial<UserTestData> = {}): UserTestData {
    const name = this.generateRealisticName();
    return this.create({
      fullName: `${name.firstName} ${name.lastName}`,
      username: faker.internet.username({ firstName: name.firstName, lastName: name.lastName }).toLowerCase(),
      email: faker.internet.email({ firstName: name.firstName, lastName: name.lastName }).toLowerCase(),
      bio: faker.person.bio(),
      address: `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()}`,
      ...overrides
    });
  }
}

// Export commonly used test data
export const TEST_USERS = {
  BASIC_USER: UserFactory.create({ id: 1, email: '<EMAIL>' }),
  PROPERTY_OWNER: UserFactory.createOwner({ id: 2, email: '<EMAIL>' }),
  ADMIN_USER: UserFactory.createAdmin({ id: 3, email: '<EMAIL>' }),
  AUTH_USER: UserFactory.createForAuth({ id: 4 })
};

export const TEST_CREDENTIALS = {
  VALID: UserFactory.createCredentials({ email: '<EMAIL>', password: 'ValidPassword123!' }),
  INVALID_EMAIL: UserFactory.createCredentials({ email: '<EMAIL>', password: 'ValidPassword123!' }),
  INVALID_PASSWORD: UserFactory.createCredentials({ email: '<EMAIL>', password: 'wrongpassword' })
};