/**
 * Booking Test Data Factory
 * Provides consistent test data generation for Booking entities
 */

import { faker } from '@faker-js/faker';
import { UserTestData } from './UserFactory';
import { PropertyTestData } from './PropertyFactory';

export interface BookingTestData {
  id?: number;
  propertyId?: number;
  userId?: number;
  bookingDate?: string;
  bookingType?: 'morning' | 'full_day';
  guests?: number;
  status?: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  specialRequests?: string;
  totalAmount?: number;
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: 'card' | 'upi' | 'netbanking' | 'wallet';
  transactionId?: string;
  checkInTime?: string;
  checkOutTime?: string;
  actualCheckIn?: Date;
  actualCheckOut?: Date;
  guestDetails?: {
    primaryGuest: {
      name: string;
      phone: string;
      email: string;
    };
    additionalGuests?: Array<{
      name: string;
      age?: number;
    }>;
  };
  cancellationReason?: string;
  cancellationDate?: Date;
  refundAmount?: number;
  review?: {
    rating?: number;
    comment?: string;
    createdAt?: Date;
  };
  property?: PropertyTestData;
  user?: UserTestData;
  createdAt?: Date;
  updatedAt?: Date;
}

export class BookingFactory {
  private static counter = 1;

  /**
   * Generate a basic booking with minimal required fields
   */
  static create(overrides: Partial<BookingTestData> = {}): BookingTestData {
    const id = this.counter++;
    const bookingType = faker.helpers.arrayElement(['morning', 'full_day']);
    const guests = faker.number.int({ min: 1, max: 8 });
    const basePrice = faker.number.int({ min: 2000, max: 10000 });
    const totalAmount = bookingType === 'full_day' ? Math.round(basePrice * 1.6) : basePrice;
    
    return {
      id,
      propertyId: faker.number.int({ min: 1, max: 100 }),
      userId: faker.number.int({ min: 1, max: 100 }),
      bookingDate: faker.date.future().toISOString().split('T')[0],
      bookingType,
      guests,
      status: 'confirmed',
      specialRequests: this.generateSpecialRequests(),
      totalAmount,
      paymentStatus: 'paid',
      paymentMethod: faker.helpers.arrayElement(['card', 'upi', 'netbanking', 'wallet']),
      transactionId: `TXN${faker.string.alphanumeric(10).toUpperCase()}`,
      checkInTime: bookingType === 'morning' ? '10:00' : '14:00',
      checkOutTime: bookingType === 'morning' ? '14:00' : '11:00',
      guestDetails: this.generateGuestDetails(guests)!,
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      ...overrides
    };
  }

  /**
   * Generate booking with specific user and property
   */
  static createWithUserAndProperty(
    user: UserTestData, 
    property: PropertyTestData, 
    overrides: Partial<BookingTestData> = {}
  ): BookingTestData {
    const bookingType = faker.helpers.arrayElement(['morning', 'full_day']);
    const totalAmount = bookingType === 'full_day' ? property.fullDayPrice! : property.halfDayPrice!;
    
    return this.create({
      userId: user.id!,
      propertyId: property.id!,
      totalAmount,
      user,
      property,
      guestDetails: {
        primaryGuest: {
          name: user.fullName!,
          phone: user.phone!,
          email: user.email!
        }
      },
      ...overrides
    });
  }

  /**
   * Generate pending booking
   */
  static createPending(overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({
      status: 'pending',
      paymentStatus: 'pending',
      transactionId: '',
      ...overrides
    });
  }

  /**
   * Generate confirmed booking
   */
  static createConfirmed(overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({
      status: 'confirmed',
      paymentStatus: 'paid',
      transactionId: `TXN${faker.string.alphanumeric(10).toUpperCase()}`,
      ...overrides
    });
  }

  /**
   * Generate cancelled booking
   */
  static createCancelled(overrides: Partial<BookingTestData> = {}): BookingTestData {
    const cancellationReasons = [
      'Change in plans',
      'Weather conditions',
      'Family emergency',
      'Found better alternative',
      'Budget constraints',
      'Travel restrictions'
    ];

    return this.create({
      status: 'cancelled',
      paymentStatus: 'refunded',
      cancellationReason: faker.helpers.arrayElement(cancellationReasons),
      cancellationDate: faker.date.recent(),
      refundAmount: faker.number.int({ min: 500, max: 5000 }),
      ...overrides
    });
  }

  /**
   * Generate completed booking with review
   */
  static createCompleted(overrides: Partial<BookingTestData> = {}): BookingTestData {
    const checkInDate = faker.date.recent();
    const checkOutDate = new Date(checkInDate);
    checkOutDate.setHours(checkOutDate.getHours() + 8); // 8 hours later

    return this.create({
      status: 'completed',
      paymentStatus: 'paid',
      actualCheckIn: checkInDate,
      actualCheckOut: checkOutDate,
      review: {
        rating: faker.number.int({ min: 3, max: 5 }),
        comment: this.generateReviewComment(),
        createdAt: faker.date.recent()
      },
      ...overrides
    });
  }

  /**
   * Generate booking for specific date
   */
  static createForDate(date: string, overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({
      bookingDate: date,
      ...overrides
    });
  }

  /**
   * Generate booking form data
   */
  static createFormData(overrides: Partial<BookingTestData> = {}): Partial<BookingTestData> {
    const booking = this.create(overrides);
    return {
      propertyId: booking.propertyId!,
      bookingDate: booking.bookingDate!,
      bookingType: booking.bookingType!,
      guests: booking.guests!,
      specialRequests: booking.specialRequests!
    };
  }

  /**
   * Generate multiple bookings
   */
  static createMany(count: number, overrides: Partial<BookingTestData> = {}): BookingTestData[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  /**
   * Generate bookings with mixed statuses
   */
  static createMixedStatuses(counts: { 
    pending?: number; 
    confirmed?: number; 
    cancelled?: number; 
    completed?: number; 
  } = {}): BookingTestData[] {
    const { pending = 2, confirmed = 5, cancelled = 1, completed = 3 } = counts;
    
    return [
      ...this.createMany(pending, { status: 'pending' }),
      ...this.createMany(confirmed, { status: 'confirmed' }),
      ...this.createMany(cancelled, { status: 'cancelled' }),
      ...this.createMany(completed, { status: 'completed' })
    ];
  }

  /**
   * Generate future booking
   */
  static createFuture(overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({
      bookingDate: faker.date.future().toISOString().split('T')[0],
      status: 'confirmed',
      ...overrides
    });
  }

  /**
   * Generate past booking
   */
  static createPast(overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({
      bookingDate: faker.date.past().toISOString().split('T')[0],
      status: 'completed',
      ...overrides
    });
  }

  /**
   * Generate booking with invalid data for negative testing
   */
  static createInvalid(invalidField: 'date' | 'guests' | 'propertyId'): BookingTestData {
    const booking = this.create();
    
    switch (invalidField) {
      case 'date':
        booking.bookingDate = faker.date.past().toISOString().split('T')[0]; // Past date
        break;
      case 'guests':
        booking.guests = 0; // Invalid guest count
        break;
      case 'propertyId':
        booking.propertyId = -1; // Invalid property ID
        break;
    }
    
    return booking;
  }

  /**
   * Generate booking for specific user
   */
  static createForUser(userId: number, overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({ userId, ...overrides });
  }

  /**
   * Generate booking for specific property
   */
  static createForProperty(propertyId: number, overrides: Partial<BookingTestData> = {}): BookingTestData {
    return this.create({ propertyId, ...overrides });
  }

  /**
   * Generate conflicting booking (same property, same date)
   */
  static createConflicting(existingBooking: BookingTestData): BookingTestData {
    return this.create({
      propertyId: existingBooking.propertyId!,
      bookingDate: existingBooking.bookingDate!,
      bookingType: existingBooking.bookingType!,
      status: 'pending'
    });
  }

  /**
   * Reset factory counter for predictable IDs in tests
   */
  static resetCounter(): void {
    this.counter = 1;
  }

  /**
   * Generate special requests
   */
  private static generateSpecialRequests(): string {
    const requests = [
      'Early check-in requested',
      'Late check-out needed',
      'Vegetarian meals preferred',
      'Anniversary celebration setup',
      'Extra towels and bedding',
      'Ground floor room required',
      'Quiet room away from common areas',
      'Baby crib needed',
      'Wheelchair accessible facilities',
      'Pet accommodation required'
    ];
    
    return faker.datatype.boolean({ probability: 0.3 }) 
      ? faker.helpers.arrayElement(requests)
      : '';
  }

  /**
   * Generate guest details
   */
  private static generateGuestDetails(guestCount: number): BookingTestData['guestDetails'] {
    const primaryGuest = {
      name: faker.person.fullName(),
      phone: `+91${faker.string.numeric(10)}`,
      email: faker.internet.email()
    };

    const additionalGuests = guestCount > 1 
      ? Array.from({ length: guestCount - 1 }, () => ({
          name: faker.person.fullName(),
          age: faker.number.int({ min: 5, max: 65 })
        }))
      : [];

    return {
      primaryGuest,
      additionalGuests
    };
  }

  /**
   * Generate review comment
   */
  private static generateReviewComment(): string {
    const comments = [
      'Amazing property with great amenities. Highly recommended!',
      'Perfect getaway spot. Clean, comfortable, and well-maintained.',
      'Beautiful location with stunning views. Will definitely return.',
      'Excellent hospitality and service. Everything was perfect.',
      'Great value for money. The property exceeded our expectations.',
      'Peaceful and serene environment. Exactly what we needed.',
      'Well-equipped kitchen and spacious rooms. Very comfortable stay.',
      'The host was very responsive and helpful throughout our stay.',
      'Beautiful property but could use some maintenance updates.',
      'Good location but the amenities could be improved.'
    ];
    
    return faker.helpers.arrayElement(comments);
  }

  /**
   * Generate booking analytics data
   */
  static createAnalyticsData(count: number = 50): BookingTestData[] {
    const bookings: BookingTestData[] = [];
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6); // 6 months back
    
    for (let i = 0; i < count; i++) {
      const bookingDate = faker.date.between({ from: startDate, to: new Date() });
      const status = faker.helpers.weightedArrayElement([
        { weight: 60, value: 'completed' },
        { weight: 25, value: 'confirmed' },
        { weight: 10, value: 'cancelled' },
        { weight: 5, value: 'pending' }
      ]);
      
      bookings.push(this.create({
        bookingDate: bookingDate.toISOString().split('T')[0],
        status,
        createdAt: bookingDate
      }));
    }
    
    return bookings;
  }
}

// Export commonly used test data
export const TEST_BOOKINGS = {
  PENDING_BOOKING: BookingFactory.createPending({ id: 1, userId: 1, propertyId: 1 }),
  CONFIRMED_BOOKING: BookingFactory.createConfirmed({ id: 2, userId: 2, propertyId: 2 }),
  CANCELLED_BOOKING: BookingFactory.createCancelled({ id: 3, userId: 3, propertyId: 3 }),
  COMPLETED_BOOKING: BookingFactory.createCompleted({ id: 4, userId: 1, propertyId: 2 }),
  FUTURE_BOOKING: BookingFactory.createFuture({ id: 5, userId: 2, propertyId: 1 })
};