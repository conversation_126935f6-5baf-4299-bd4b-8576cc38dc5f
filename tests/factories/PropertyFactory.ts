/**
 * Property Test Data Factory
 * Provides consistent test data generation for Property entities
 */

import { faker } from '@faker-js/faker';
import { UserFactory, UserTestData } from './UserFactory';

export interface PropertyTestData {
  id?: number;
  ownerId?: number;
  title?: string;
  description?: string;
  location?: string;
  halfDayPrice?: number;
  fullDayPrice?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  images?: string[];
  status?: 'active' | 'inactive' | 'draft';
  featured?: boolean;
  category?: 'luxury' | 'standard' | 'budget';
  owner?: UserTestData;
  availability?: {
    available: boolean;
    nextAvailableDate?: string;
  };
  createdAt?: Date;
  updatedAt?: Date;
  coordinates?: {
    lat: number;
    lng: number;
  };
  maxGuests?: number;
  checkInTime?: string;
  checkOutTime?: string;
  rules?: string[];
  cancellationPolicy?: string;
  instantBook?: boolean;
  minBookingHours?: number;
  securityDeposit?: number;
}

export interface PropertySearchFilters {
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  date?: string;
  amenities?: string[];
  featured?: boolean;
  bedrooms?: number;
  bathrooms?: number;
  maxGuests?: number;
}

export class PropertyFactory {
  private static counter = 1;

  // Common amenities for Indian farmhouses
  private static readonly AMENITIES = [
    'wifi', 'pool', 'parking', 'kitchen', 'ac', 'tv', 'garden', 'bbq',
    'fireplace', 'gym', 'spa', 'games_room', 'library', 'terrace',
    'balcony', 'lake_view', 'mountain_view', 'pet_friendly', 'playground',
    'security', 'housekeeping', 'caretaker', 'generator', 'solar_power'
  ];

  // Popular locations in India for farmhouses
  private static readonly LOCATIONS = [
    'Goa', 'Lonavala', 'Mahabaleshwar', 'Alibaug', 'Karjat', 'Igatpuri',
    'Khandala', 'Matheran', 'Panchgani', 'Dehradun', 'Mussoorie', 'Rishikesh',
    'Manali', 'Shimla', 'Coorg', 'Ooty', 'Munnar', 'Wayanad', 'Bangalore',
    'Pune', 'Delhi NCR', 'Jaipur', 'Udaipur', 'Jodhpur'
  ];

  /**
   * Generate a basic property with minimal required fields
   */
  static create(overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    const id = this.counter++;
    const location = faker.helpers.arrayElement(this.LOCATIONS);
    const amenities = faker.helpers.arrayElements(this.AMENITIES, { min: 3, max: 8 });
    const bedrooms = faker.number.int({ min: 1, max: 6 });
    const bathrooms = faker.number.int({ min: 1, max: bedrooms });
    const halfDayPrice = faker.number.int({ min: 2000, max: 8000 });
    
    return {
      id,
      ownerId: faker.number.int({ min: 1, max: 100 }),
      title: this.generatePropertyTitle(location),
      description: this.generatePropertyDescription(location, amenities),
      location,
      halfDayPrice,
      fullDayPrice: Math.round(halfDayPrice * 1.6), // 60% more for full day
      bedrooms,
      bathrooms,
      amenities,
      images: this.generateImageUrls(faker.number.int({ min: 3, max: 8 })),
      status: 'active',
      featured: faker.datatype.boolean({ probability: 0.2 }), // 20% featured
      maxGuests: bedrooms * 2 + faker.number.int({ min: 0, max: 4 }),
      checkInTime: '14:00',
      checkOutTime: '11:00',
      rules: this.generatePropertyRules(),
      cancellationPolicy: faker.helpers.arrayElement([
        'Free cancellation up to 24 hours before check-in',
        'Free cancellation up to 48 hours before check-in',
        'Moderate: Free cancellation up to 5 days before check-in',
        'Strict: 50% refund up to 7 days before check-in'
      ]),
      instantBook: faker.datatype.boolean({ probability: 0.3 }),
      minBookingHours: faker.helpers.arrayElement([4, 6, 8, 12]),
      securityDeposit: faker.number.int({ min: 1000, max: 5000 }),
      coordinates: this.generateCoordinates(location),
      availability: {
        available: true,
        nextAvailableDate: faker.date.future().toISOString().split('T')[0]
      },
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      ...overrides
    };
  }

  /**
   * Generate property with specific owner
   */
  static createWithOwner(owner: UserTestData, overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({
      ownerId: owner.id!,
      owner,
      ...overrides
    });
  }

  /**
   * Generate a luxury property
   */
  static createLuxury(overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({
      title: 'Luxury Villa with Private Pool',
      halfDayPrice: faker.number.int({ min: 8000, max: 15000 }),
      bedrooms: faker.number.int({ min: 3, max: 8 }),
      amenities: [...this.AMENITIES.slice(0, 15)], // Include many amenities
      featured: true,
      status: 'active',
      securityDeposit: faker.number.int({ min: 5000, max: 15000 }),
      instantBook: false, // Luxury properties usually require approval
      ...overrides
    });
  }

  /**
   * Generate a budget-friendly property
   */
  static createBudget(overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({
      title: 'Cozy Farmhouse Getaway',
      halfDayPrice: faker.number.int({ min: 1500, max: 3500 }),
      bedrooms: faker.number.int({ min: 1, max: 3 }),
      bathrooms: faker.number.int({ min: 1, max: 2 }),
      amenities: faker.helpers.arrayElements(this.AMENITIES, { min: 2, max: 5 }),
      featured: false,
      securityDeposit: faker.number.int({ min: 500, max: 2000 }),
      instantBook: true,
      ...overrides
    });
  }

  /**
   * Generate property in specific location
   */
  static createInLocation(location: string, overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({
      location,
      title: this.generatePropertyTitle(location),
      description: this.generatePropertyDescription(location, this.AMENITIES.slice(0, 5)),
      coordinates: this.generateCoordinates(location),
      ...overrides
    });
  }

  /**
   * Generate featured property
   */
  static createFeatured(overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({
      featured: true,
      status: 'active',
      halfDayPrice: faker.number.int({ min: 5000, max: 12000 }),
      amenities: faker.helpers.arrayElements(this.AMENITIES, { min: 6, max: 12 }),
      images: this.generateImageUrls(faker.number.int({ min: 5, max: 10 })),
      ...overrides
    });
  }

  /**
   * Generate property form data for creation/updates
   */
  static createFormData(overrides: Partial<PropertyTestData> = {}): Partial<PropertyTestData> {
    const property = this.create(overrides);
    return {
      title: property.title!,
      description: property.description!,
      location: property.location!,
      halfDayPrice: property.halfDayPrice!,
      fullDayPrice: property.fullDayPrice!,
      bedrooms: property.bedrooms!,
      bathrooms: property.bathrooms!,
      amenities: property.amenities!,
      images: property.images!,
      status: property.status!,
      featured: property.featured!
    };
  }

  /**
   * Generate multiple properties
   */
  static createMany(count: number, overrides: Partial<PropertyTestData> = {}): PropertyTestData[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  /**
   * Generate properties with mixed statuses
   */
  static createMixedStatuses(counts: { active?: number; inactive?: number; draft?: number } = {}): PropertyTestData[] {
    const { active = 5, inactive = 2, draft = 3 } = counts;
    
    return [
      ...this.createMany(active, { status: 'active' }),
      ...this.createMany(inactive, { status: 'inactive' }),
      ...this.createMany(draft, { status: 'draft' })
    ];
  }

  /**
   * Generate property search filters
   */
  static createSearchFilters(overrides: Partial<PropertySearchFilters> = {}): PropertySearchFilters {
    return {
      location: faker.helpers.arrayElement(this.LOCATIONS),
      minPrice: faker.number.int({ min: 1000, max: 3000 }),
      maxPrice: faker.number.int({ min: 5000, max: 15000 }),
      date: faker.date.future().toISOString().split('T')[0],
      amenities: faker.helpers.arrayElements(this.AMENITIES, { min: 1, max: 3 }),
      featured: faker.datatype.boolean(),
      bedrooms: faker.number.int({ min: 1, max: 4 }),
      maxGuests: faker.number.int({ min: 2, max: 10 }),
      ...overrides
    };
  }

  /**
   * Generate property with invalid data for negative testing
   */
  static createInvalid(invalidField: 'title' | 'price' | 'bedrooms' | 'location'): PropertyTestData {
    const property = this.create();
    
    switch (invalidField) {
      case 'title':
        property.title = 'ab'; // Too short
        break;
      case 'price':
        property.halfDayPrice = -100; // Negative price
        break;
      case 'bedrooms':
        property.bedrooms = 0; // Invalid bedroom count
        break;
      case 'location':
        property.location = ''; // Empty location
        break;
    }
    
    return property;
  }

  /**
   * Generate property for specific owner ID
   */
  static createForOwner(ownerId: number, overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({ ownerId, ...overrides });
  }

  /**
   * Generate unavailable property
   */
  static createUnavailable(overrides: Partial<PropertyTestData> = {}): PropertyTestData {
    return this.create({
      availability: {
        available: false,
        nextAvailableDate: faker.date.future().toISOString().split('T')[0]
      },
      ...overrides
    });
  }

  /**
   * Reset factory counter for predictable IDs in tests
   */
  static resetCounter(): void {
    this.counter = 1;
  }

  /**
   * Generate property title based on location
   */
  private static generatePropertyTitle(location: string): string {
    const adjectives = ['Beautiful', 'Stunning', 'Luxury', 'Cozy', 'Spacious', 'Modern', 'Traditional', 'Rustic'];
    const types = ['Villa', 'Farmhouse', 'Cottage', 'Retreat', 'Estate', 'Bungalow', 'Resort'];
    
    const adjective = faker.helpers.arrayElement(adjectives);
    const type = faker.helpers.arrayElement(types);
    
    return `${adjective} ${type} in ${location}`;
  }

  /**
   * Generate property description
   */
  private static generatePropertyDescription(location: string, amenities: string[]): string {
    const descriptions = [
      `Experience the perfect getaway at this charming property in ${location}.`,
      `Escape to this beautiful retreat nestled in the heart of ${location}.`,
      `Discover tranquility at this stunning property located in ${location}.`,
      `Unwind at this exceptional vacation rental in the scenic ${location}.`
    ];
    
    const intro = faker.helpers.arrayElement(descriptions);
    const amenityText = `Features include ${amenities.slice(0, 3).join(', ')} and much more.`;
    const closing = 'Perfect for families, couples, or groups looking for a memorable experience.';
    
    return `${intro} ${amenityText} ${closing}`;
  }

  /**
   * Generate property rules
   */
  private static generatePropertyRules(): string[] {
    const allRules = [
      'No smoking inside the property',
      'No pets allowed',
      'No loud music after 10 PM',
      'Maximum occupancy strictly enforced',
      'No outside guests without prior approval',
      'Shoes to be removed before entering',
      'Pool use at own risk',
      'No damage to property or furnishings',
      'Respect neighbors and local community',
      'Clean up after use'
    ];
    
    return faker.helpers.arrayElements(allRules, { min: 3, max: 6 });
  }

  /**
   * Generate image URLs
   */
  private static generateImageUrls(count: number): string[] {
    return Array.from({ length: count }, (_, index) => 
      `https://images.farmhouse.com/property_${this.counter}_${index + 1}.jpg`
    );
  }

  /**
   * Generate coordinates based on location
   */
  private static generateCoordinates(location: string): { lat: number; lng: number } {
    // Approximate coordinates for popular Indian destinations
    const locationCoords: Record<string, { lat: number; lng: number }> = {
      'Goa': { lat: 15.2993, lng: 74.1240 },
      'Lonavala': { lat: 18.7537, lng: 73.4068 },
      'Mahabaleshwar': { lat: 17.9242, lng: 73.6588 },
      'Alibaug': { lat: 18.6414, lng: 72.8722 },
      'Manali': { lat: 32.2396, lng: 77.1887 },
      'Shimla': { lat: 31.1048, lng: 77.1734 },
      'Ooty': { lat: 11.4064, lng: 76.6932 },
      'Coorg': { lat: 12.3375, lng: 75.8069 }
    };
    
    const baseCoords = locationCoords[location] || { lat: 20.5937, lng: 78.9629 }; // India center
    
    // Add small random offset for variety
    return {
      lat: baseCoords.lat + faker.number.float({ min: -0.1, max: 0.1 }),
      lng: baseCoords.lng + faker.number.float({ min: -0.1, max: 0.1 })
    };
  }
}

// Export commonly used test data
export const TEST_PROPERTIES = {
  BASIC_PROPERTY: PropertyFactory.create({ id: 1, ownerId: 1 }),
  LUXURY_VILLA: PropertyFactory.createLuxury({ id: 2, ownerId: 2 }),
  BUDGET_COTTAGE: PropertyFactory.createBudget({ id: 3, ownerId: 3 }),
  FEATURED_PROPERTY: PropertyFactory.createFeatured({ id: 4, ownerId: 2 }),
  GOA_VILLA: PropertyFactory.createInLocation('Goa', { id: 5, ownerId: 1 })
};

export const TEST_SEARCH_FILTERS = {
  BASIC: PropertyFactory.createSearchFilters(),
  LUXURY: PropertyFactory.createSearchFilters({ minPrice: 8000, maxPrice: 20000 }),
  BUDGET: PropertyFactory.createSearchFilters({ minPrice: 1000, maxPrice: 4000 }),
  GOA_ONLY: PropertyFactory.createSearchFilters({ location: 'Goa' }),
  FEATURED_ONLY: PropertyFactory.createSearchFilters({ featured: true })
};