/**
 * Properties Endpoints Contract Tests
 * Validates API contract compliance for property endpoints
 */

import { describe, test, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '../../../server/index';
import { ApiContractValidator } from '../ApiContractTesting';
import { UserFactory, PropertyFactory, TEST_PROPERTIES } from '../../factories';
import { storage } from '../../../server/storage';

describe('Properties API Contract Tests', () => {
  let testApp: any;
  let ownerToken: string;
  let ownerId: number;
  let userToken: string;
  let userId: number;

  beforeEach(async () => {
    testApp = request(app);
    UserFactory.resetCounter();
    PropertyFactory.resetCounter();

    // Create owner for property operations
    const ownerData = UserFactory.createOwner({
      email: '<EMAIL>'
    });

    const ownerResponse = await testApp
      .post('/api/auth/register')
      .send(ownerData);

    ownerToken = ownerResponse.body.data.token;
    ownerId = ownerResponse.body.data.user.id;

    // Create regular user for testing access controls
    const userData = UserFactory.create({
      email: '<EMAIL>'
    });

    const userResponse = await testApp
      .post('/api/auth/register')
      .send(userData);

    userToken = userResponse.body.data.token;
    userId = userResponse.body.data.user.id;
  });

  afterEach(async () => {
    try {
      await storage.clearTestData?.();
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('GET /api/properties', () => {
    beforeEach(async () => {
      // Create some test properties
      const properties = PropertyFactory.createMany(3, { ownerId });
      
      for (const property of properties) {
        await testApp
          .post('/api/properties')
          .set('Authorization', `Bearer ${ownerToken}`)
          .send(PropertyFactory.createFormData(property));
      }
    });

    test('should validate successful properties list response contract', async () => {
      const response = await testApp
        .get('/api/properties');

      ApiContractValidator.validatePropertiesListResponse(response);
    });

    test('should validate properties list with search filters', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({
          location: 'Goa',
          minPrice: 2000,
          maxPrice: 10000,
          featured: true
        });

      ApiContractValidator.validatePropertiesListResponse(response);
    });

    test('should validate pagination parameters', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({
          page: 1,
          limit: 5
        });

      ApiContractValidator.validatePropertiesListResponse(response);
      
      // Validate pagination structure
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(5);
    });

    test('should validate invalid pagination parameters', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({
          page: -1,
          limit: 0
        });

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate price range filter validation', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({
          minPrice: 10000,
          maxPrice: 5000 // Invalid: max < min
        });

      ApiContractValidator.validateValidationErrorResponse(response);
    });
  });

  describe('GET /api/properties/:id', () => {
    let propertyId: number;

    beforeEach(async () => {
      const propertyData = PropertyFactory.createFormData({ ownerId });
      
      const createResponse = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      propertyId = createResponse.body.data.id;
    });

    test('should validate successful property detail response contract', async () => {
      const response = await testApp
        .get(`/api/properties/${propertyId}`);

      ApiContractValidator.validatePropertyResponse(response);
    });

    test('should validate not found error for non-existent property', async () => {
      const response = await testApp
        .get('/api/properties/99999');

      ApiContractValidator.validateNotFoundErrorResponse(response);
    });

    test('should validate invalid property ID format', async () => {
      const response = await testApp
        .get('/api/properties/invalid-id');

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate property business rules', async () => {
      const response = await testApp
        .get(`/api/properties/${propertyId}`);

      ApiContractValidator.validatePropertyResponse(response);
      
      const property = response.body.data;
      
      // Business rule validations
      expect(property.fullDayPrice).toBeGreaterThanOrEqual(property.halfDayPrice);
      expect(property.bedrooms).toBeGreaterThan(0);
      expect(property.bathrooms).toBeGreaterThan(0);
      expect(property.images).toHaveLength.greaterThan(0);
      expect(['active', 'inactive', 'draft']).toContain(property.status);
    });
  });

  describe('POST /api/properties', () => {
    test('should validate successful property creation response contract', async () => {
      const propertyData = PropertyFactory.createFormData();

      const response = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      ApiContractValidator.validatePropertyResponse(response, 201);
    });

    test('should validate authentication required for property creation', async () => {
      const propertyData = PropertyFactory.createFormData();

      const response = await testApp
        .post('/api/properties')
        .send(propertyData);

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate authorization required for non-owners', async () => {
      const propertyData = PropertyFactory.createFormData();

      const response = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${userToken}`)
        .send(propertyData);

      ApiContractValidator.validateAuthorizationErrorResponse(response);
    });

    test('should validate property data validation', async () => {
      const invalidPropertyData = PropertyFactory.createInvalid('title');

      const response = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(invalidPropertyData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate required fields', async () => {
      const response = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send({});

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate price validation rules', async () => {
      const propertyData = PropertyFactory.createFormData({
        halfDayPrice: -100, // Invalid negative price
        fullDayPrice: 5000
      });

      const response = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate bedroom/bathroom count rules', async () => {
      const propertyData = PropertyFactory.createFormData({
        bedrooms: 0, // Invalid
        bathrooms: 0 // Invalid
      });

      const response = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });
  });

  describe('PUT /api/properties/:id', () => {
    let propertyId: number;

    beforeEach(async () => {
      const propertyData = PropertyFactory.createFormData({ ownerId });
      
      const createResponse = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      propertyId = createResponse.body.data.id;
    });

    test('should validate successful property update response contract', async () => {
      const updateData = PropertyFactory.createFormData({
        title: 'Updated Property Title'
      });

      const response = await testApp
        .put(`/api/properties/${propertyId}`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(updateData);

      ApiContractValidator.validatePropertyResponse(response);
    });

    test('should validate authentication required for updates', async () => {
      const updateData = PropertyFactory.createFormData();

      const response = await testApp
        .put(`/api/properties/${propertyId}`)
        .send(updateData);

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate owner authorization for updates', async () => {
      const updateData = PropertyFactory.createFormData();

      const response = await testApp
        .put(`/api/properties/${propertyId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);

      ApiContractValidator.validateAuthorizationErrorResponse(response);
    });

    test('should validate not found error for non-existent property', async () => {
      const updateData = PropertyFactory.createFormData();

      const response = await testApp
        .put('/api/properties/99999')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(updateData);

      ApiContractValidator.validateNotFoundErrorResponse(response);
    });

    test('should validate business rule enforcement on update', async () => {
      const updateData = PropertyFactory.createFormData({
        fullDayPrice: 2000,
        halfDayPrice: 5000 // Invalid: half day more than full day
      });

      const response = await testApp
        .put(`/api/properties/${propertyId}`)
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(updateData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });
  });

  describe('DELETE /api/properties/:id', () => {
    let propertyId: number;

    beforeEach(async () => {
      const propertyData = PropertyFactory.createFormData({ ownerId });
      
      const createResponse = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      propertyId = createResponse.body.data.id;
    });

    test('should validate successful property deletion response contract', async () => {
      const response = await testApp
        .delete(`/api/properties/${propertyId}`)
        .set('Authorization', `Bearer ${ownerToken}`);

      ApiContractValidator.validateSuccessResponse(response, undefined, 204);
    });

    test('should validate authentication required for deletion', async () => {
      const response = await testApp
        .delete(`/api/properties/${propertyId}`);

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate owner authorization for deletion', async () => {
      const response = await testApp
        .delete(`/api/properties/${propertyId}`)
        .set('Authorization', `Bearer ${userToken}`);

      ApiContractValidator.validateAuthorizationErrorResponse(response);
    });

    test('should validate not found error for non-existent property', async () => {
      const response = await testApp
        .delete('/api/properties/99999')
        .set('Authorization', `Bearer ${ownerToken}`);

      ApiContractValidator.validateNotFoundErrorResponse(response);
    });
  });

  describe('GET /api/properties/owner/me', () => {
    beforeEach(async () => {
      // Create properties for the owner
      const properties = PropertyFactory.createMany(2, { ownerId });
      
      for (const property of properties) {
        await testApp
          .post('/api/properties')
          .set('Authorization', `Bearer ${ownerToken}`)
          .send(PropertyFactory.createFormData(property));
      }
    });

    test('should validate owner properties list response contract', async () => {
      const response = await testApp
        .get('/api/properties/owner/me')
        .set('Authorization', `Bearer ${ownerToken}`);

      ApiContractValidator.validatePropertiesListResponse(response);
      ApiContractValidator.validateBusinessRules.validatePropertyOwnership(response, ownerId);
    });

    test('should validate authentication required for owner properties', async () => {
      const response = await testApp
        .get('/api/properties/owner/me');

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate owner role authorization', async () => {
      const response = await testApp
        .get('/api/properties/owner/me')
        .set('Authorization', `Bearer ${userToken}`);

      ApiContractValidator.validateAuthorizationErrorResponse(response);
    });
  });

  describe('Performance and Caching Contract', () => {
    test('should validate property list endpoint performance', async () => {
      const startTime = Date.now();
      const response = await testApp
        .get('/api/properties');
      const endTime = Date.now();

      ApiContractValidator.validateResponseTiming(response, 1500);
      expect(endTime - startTime).toBeLessThan(1500);
    });

    test('should validate cache headers for property details', async () => {
      // Create a property first
      const propertyData = PropertyFactory.createFormData({ ownerId });
      const createResponse = await testApp
        .post('/api/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send(propertyData);

      const propertyId = createResponse.body.data.id;

      const response = await testApp
        .get(`/api/properties/${propertyId}`);

      // Property details should have cache headers
      expect(response.headers['cache-control']).toBeDefined();
      if (response.headers['etag']) {
        expect(response.headers['etag']).toMatch(/^".*"$/);
      }
    });
  });

  describe('Search Functionality Contract', () => {
    beforeEach(async () => {
      // Create diverse properties for search testing
      const properties = [
        PropertyFactory.createInLocation('Goa', { ownerId, halfDayPrice: 3000 }),
        PropertyFactory.createInLocation('Lonavala', { ownerId, halfDayPrice: 5000 }),
        PropertyFactory.createLuxury({ ownerId, location: 'Goa' }),
        PropertyFactory.createBudget({ ownerId, location: 'Pune' })
      ];

      for (const property of properties) {
        await testApp
          .post('/api/properties')
          .set('Authorization', `Bearer ${ownerToken}`)
          .send(PropertyFactory.createFormData(property));
      }
    });

    test('should validate location-based search contract', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({ location: 'Goa' });

      ApiContractValidator.validatePropertiesListResponse(response);
      
      // All returned properties should match location
      response.body.data.forEach((property: any) => {
        expect(property.location).toBe('Goa');
      });
    });

    test('should validate price range search contract', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({ 
          minPrice: 4000,
          maxPrice: 8000
        });

      ApiContractValidator.validatePropertiesListResponse(response);
      
      // All returned properties should be in price range
      response.body.data.forEach((property: any) => {
        expect(property.halfDayPrice).toBeGreaterThanOrEqual(4000);
        expect(property.halfDayPrice).toBeLessThanOrEqual(8000);
      });
    });

    test('should validate featured properties filter contract', async () => {
      const response = await testApp
        .get('/api/properties')
        .query({ featured: true });

      ApiContractValidator.validatePropertiesListResponse(response);
      
      // All returned properties should be featured
      response.body.data.forEach((property: any) => {
        expect(property.featured).toBe(true);
      });
    });
  });
});