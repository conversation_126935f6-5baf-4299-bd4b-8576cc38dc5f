/**
 * Authentication Endpoints Contract Tests
 * Validates API contract compliance for auth endpoints
 */

import { describe, test, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '../../../server/index';
import { ApiContractValidator } from '../ApiContractTesting';
import { UserFactory, TEST_CREDENTIALS } from '../../factories';
import { storage } from '../../../server/storage';

describe('Authentication API Contract Tests', () => {
  let testApp: any;

  beforeEach(() => {
    testApp = request(app);
    UserFactory.resetCounter();
  });

  afterEach(async () => {
    // Clean up test data
    try {
      await storage.clearTestData?.();
    } catch (error) {
      // Ignore cleanup errors in contract tests
    }
  });

  describe('POST /api/auth/register', () => {
    test('should validate successful registration response contract', async () => {
      const userData = UserFactory.createRegistrationData({
        email: '<EMAIL>',
        username: 'contractuser'
      });

      const response = await testApp
        .post('/api/auth/register')
        .send(userData);

      ApiContractValidator.validateAuthResponse(response, 201);
    });

    test('should validate validation error response contract', async () => {
      const invalidUserData = UserFactory.createInvalid('email');

      const response = await testApp
        .post('/api/auth/register')
        .send(invalidUserData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate conflict error response contract', async () => {
      const userData = UserFactory.createRegistrationData({
        email: '<EMAIL>'
      });

      // Create user first
      await testApp.post('/api/auth/register').send(userData);

      // Try to create duplicate
      const response = await testApp
        .post('/api/auth/register')
        .send(userData);

      ApiContractValidator.validateConflictErrorResponse(response);
    });

    test('should validate required fields contract', async () => {
      const response = await testApp
        .post('/api/auth/register')
        .send({});

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate password strength requirements', async () => {
      const userData = UserFactory.createRegistrationData({
        password: '123' // Too weak
      });

      const response = await testApp
        .post('/api/auth/register')
        .send(userData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate email format requirements', async () => {
      const userData = UserFactory.createRegistrationData({
        email: 'invalid-email-format'
      });

      const response = await testApp
        .post('/api/auth/register')
        .send(userData);

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate security headers in response', async () => {
      const userData = UserFactory.createRegistrationData();

      const response = await testApp
        .post('/api/auth/register')
        .send(userData);

      ApiContractValidator.validateSecurityHeaders(response);
    });
  });

  describe('POST /api/auth/login', () => {
    let existingUser: any;

    beforeEach(async () => {
      // Create a user for login tests
      const userData = UserFactory.createRegistrationData({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });

      const registerResponse = await testApp
        .post('/api/auth/register')
        .send(userData);

      existingUser = registerResponse.body.data.user;
    });

    test('should validate successful login response contract', async () => {
      const credentials = {
        email: existingUser.email,
        password: 'TestPassword123!'
      };

      const response = await testApp
        .post('/api/auth/login')
        .send(credentials);

      ApiContractValidator.validateAuthResponse(response);
    });

    test('should validate authentication error for invalid credentials', async () => {
      const credentials = {
        email: existingUser.email,
        password: 'WrongPassword123!'
      };

      const response = await testApp
        .post('/api/auth/login')
        .send(credentials);

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate authentication error for non-existent user', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'TestPassword123!'
      };

      const response = await testApp
        .post('/api/auth/login')
        .send(credentials);

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate validation error for missing fields', async () => {
      const response = await testApp
        .post('/api/auth/login')
        .send({});

      ApiContractValidator.validateValidationErrorResponse(response);
    });

    test('should validate rate limiting after multiple failed attempts', async () => {
      const credentials = {
        email: existingUser.email,
        password: 'WrongPassword'
      };

      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        await testApp
          .post('/api/auth/login')
          .send(credentials);
      }

      const response = await testApp
        .post('/api/auth/login')
        .send(credentials);

      ApiContractValidator.validateRateLimitErrorResponse(response);
    });
  });

  describe('GET /api/auth/me', () => {
    let authToken: string;
    let userId: number;

    beforeEach(async () => {
      // Register and login to get auth token
      const userData = UserFactory.createRegistrationData({
        email: '<EMAIL>'
      });

      const registerResponse = await testApp
        .post('/api/auth/register')
        .send(userData);

      authToken = registerResponse.body.data.token;
      userId = registerResponse.body.data.user.id;
    });

    test('should validate successful user profile response contract', async () => {
      const response = await testApp
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);

      ApiContractValidator.validateUserResponse(response);
      ApiContractValidator.validateBusinessRules.validateUserOwnership(response, userId);
    });

    test('should validate authentication error without token', async () => {
      const response = await testApp
        .get('/api/auth/me');

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate authentication error with invalid token', async () => {
      const response = await testApp
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid.jwt.token');

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate authentication error with expired token', async () => {
      // This would require creating an expired token
      // For now, we'll test with a malformed token that fails verification
      const response = await testApp
        .get('/api/auth/me')
        .set('Authorization', 'Bearer expired.token.here');

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate security headers for authenticated request', async () => {
      const response = await testApp
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);

      ApiContractValidator.validateSecurityHeaders(response);
    });
  });

  describe('POST /api/auth/logout', () => {
    let authToken: string;

    beforeEach(async () => {
      // Register and login to get auth token
      const userData = UserFactory.createRegistrationData({
        email: '<EMAIL>'
      });

      const registerResponse = await testApp
        .post('/api/auth/register')
        .send(userData);

      authToken = registerResponse.body.data.token;
    });

    test('should validate successful logout response contract', async () => {
      const response = await testApp
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`);

      ApiContractValidator.validateSuccessResponse(response, undefined, 200);
    });

    test('should validate authentication error without token', async () => {
      const response = await testApp
        .post('/api/auth/logout');

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });

    test('should validate that logged out token cannot be used', async () => {
      // First logout
      await testApp
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`);

      // Try to use the same token
      const response = await testApp
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`);

      ApiContractValidator.validateAuthenticationErrorResponse(response);
    });
  });

  describe('Error Response Consistency', () => {
    test('should validate all error responses have consistent structure', async () => {
      const errorEndpoints = [
        { method: 'post', path: '/api/auth/register', data: {}, expectedStatus: 400 },
        { method: 'post', path: '/api/auth/login', data: {}, expectedStatus: 400 },
        { method: 'get', path: '/api/auth/me', expectedStatus: 401 },
        { method: 'post', path: '/api/auth/logout', expectedStatus: 401 }
      ];

      for (const endpoint of errorEndpoints) {
        const response = await testApp[endpoint.method](endpoint.path)
          .send(endpoint.data || {});

        ApiContractValidator.validateBaseResponse(response);
        
        // All error responses should have consistent structure
        if (response.body.success === false) {
          ApiContractValidator.validateErrorResponse(
            response, 
            endpoint.expectedStatus
          );
        }
      }
    });
  });

  describe('Response Performance Contract', () => {
    test('should validate authentication endpoints respond within SLA', async () => {
      const userData = UserFactory.createRegistrationData();

      const startTime = Date.now();
      const response = await testApp
        .post('/api/auth/register')
        .send(userData);
      const endTime = Date.now();

      // Authentication should respond within 2 seconds
      ApiContractValidator.validateResponseTiming(response, 2000);
      
      // Manual timing check
      expect(endTime - startTime).toBeLessThan(2000);
    });
  });

  describe('API Versioning Contract', () => {
    test('should validate API version headers', async () => {
      const userData = UserFactory.createRegistrationData();

      const response = await testApp
        .post('/api/auth/register')
        .send(userData);

      // API version should be consistent
      if (response.headers['api-version']) {
        expect(response.headers['api-version']).toMatch(/^v\d+(\.\d+)*$/);
      }
    });
  });
});