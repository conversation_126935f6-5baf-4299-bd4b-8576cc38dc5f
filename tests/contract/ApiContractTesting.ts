/**
 * API Contract Testing Framework
 * Validates API responses against predefined schemas and contracts
 */

import { z } from 'zod';
import { expect } from 'vitest';
import { Response } from 'supertest';
import { ApiResponse, ErrorResponse, SuccessResponse } from '../../shared/api-response-types';

/**
 * Base API Response Schema for contract validation
 */
export const BaseApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().nullable(),
  message: z.string().nullable(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
    requestId: z.string().optional(),
    field: z.string().optional()
  }).optional(),
  timestamp: z.string().datetime(),
  requestId: z.string().optional()
});

/**
 * Success Response Schema
 */
export const SuccessResponseSchema = BaseApiResponseSchema.extend({
  success: z.literal(true),
  data: z.any(),
  error: z.undefined()
});

/**
 * Error Response Schema
 */
export const ErrorResponseSchema = BaseApiResponseSchema.extend({
  success: z.literal(false),
  data: z.literal(null),
  message: z.literal(null),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
    requestId: z.string().optional(),
    field: z.string().optional()
  })
});

/**
 * Paginated Response Schema
 */
export const PaginatedResponseSchema = SuccessResponseSchema.extend({
  data: z.array(z.any()),
  pagination: z.object({
    page: z.number().int().positive(),
    limit: z.number().int().positive(),
    total: z.number().int().min(0),
    totalPages: z.number().int().min(0),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  })
});

/**
 * User Response Schemas
 */
export const UserResponseSchema = z.object({
  id: z.number().int().positive(),
  username: z.string().min(3),
  email: z.string().email(),
  fullName: z.string().min(1),
  role: z.enum(['user', 'owner', 'admin']),
  profileImage: z.string().url().optional(),
  phone: z.string().optional(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional()
});

export const AuthResponseSchema = z.object({
  user: UserResponseSchema,
  token: z.string().min(1),
  expiresIn: z.number().int().positive()
});

/**
 * Property Response Schemas
 */
export const PropertyResponseSchema = z.object({
  id: z.number().int().positive(),
  title: z.string().min(5),
  description: z.string().min(10),
  location: z.string().min(1),
  halfDayPrice: z.number().int().positive(),
  fullDayPrice: z.number().int().positive(),
  bedrooms: z.number().int().positive(),
  bathrooms: z.number().int().positive(),
  amenities: z.array(z.string()),
  images: z.array(z.string().url()),
  status: z.enum(['active', 'inactive', 'draft']),
  featured: z.boolean(),
  owner: z.object({
    id: z.number().int().positive(),
    fullName: z.string(),
    email: z.string().email(),
    phone: z.string().optional()
  }),
  availability: z.object({
    available: z.boolean(),
    nextAvailableDate: z.string().optional()
  }).optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
});

/**
 * Booking Response Schemas
 */
export const BookingResponseSchema = z.object({
  id: z.number().int().positive(),
  propertyId: z.number().int().positive(),
  userId: z.number().int().positive(),
  bookingDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  bookingType: z.enum(['morning', 'full_day']),
  guests: z.number().int().positive(),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed']),
  specialRequests: z.string().optional(),
  totalAmount: z.number().int().positive(),
  property: z.object({
    id: z.number().int().positive(),
    title: z.string(),
    location: z.string(),
    images: z.array(z.string())
  }),
  user: z.object({
    id: z.number().int().positive(),
    fullName: z.string(),
    email: z.string().email(),
    phone: z.string().optional()
  }),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
});

/**
 * Contract Testing Utilities
 */
export class ApiContractValidator {
  /**
   * Validate that response matches base API response structure
   */
  static validateBaseResponse(response: Response): void {
    expect(response.body).toBeDefined();
    
    const result = BaseApiResponseSchema.safeParse(response.body);
    if (!result.success) {
      console.error('Base response validation failed:', result.error.format());
      throw new Error(`Base API response validation failed: ${result.error.issues.map(i => i.message).join(', ')}`);
    }
  }

  /**
   * Validate success response structure and data schema
   */
  static validateSuccessResponse<T>(
    response: Response, 
    dataSchema?: z.ZodSchema<T>, 
    statusCode: number = 200
  ): void {
    expect(response.status).toBe(statusCode);
    this.validateBaseResponse(response);
    
    const result = SuccessResponseSchema.safeParse(response.body);
    if (!result.success) {
      throw new Error(`Success response validation failed: ${result.error.issues.map(i => i.message).join(', ')}`);
    }

    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
    expect(response.body.error).toBeUndefined();

    // Validate data schema if provided
    if (dataSchema && response.body.data) {
      const dataResult = dataSchema.safeParse(response.body.data);
      if (!dataResult.success) {
        console.error('Data schema validation failed:', dataResult.error.format());
        throw new Error(`Data schema validation failed: ${dataResult.error.issues.map(i => i.message).join(', ')}`);
      }
    }
  }

  /**
   * Validate error response structure
   */
  static validateErrorResponse(
    response: Response, 
    expectedStatusCode: number, 
    expectedErrorCode?: string
  ): void {
    expect(response.status).toBe(expectedStatusCode);
    this.validateBaseResponse(response);
    
    const result = ErrorResponseSchema.safeParse(response.body);
    if (!result.success) {
      throw new Error(`Error response validation failed: ${result.error.issues.map(i => i.message).join(', ')}`);
    }

    expect(response.body.success).toBe(false);
    expect(response.body.data).toBe(null);
    expect(response.body.error).toBeDefined();
    expect(response.body.error.code).toBeDefined();
    expect(response.body.error.message).toBeDefined();

    if (expectedErrorCode) {
      expect(response.body.error.code).toBe(expectedErrorCode);
    }
  }

  /**
   * Validate paginated response structure
   */
  static validatePaginatedResponse<T>(
    response: Response, 
    itemSchema?: z.ZodSchema<T>, 
    statusCode: number = 200
  ): void {
    expect(response.status).toBe(statusCode);
    this.validateBaseResponse(response);
    
    const result = PaginatedResponseSchema.safeParse(response.body);
    if (!result.success) {
      throw new Error(`Paginated response validation failed: ${result.error.issues.map(i => i.message).join(', ')}`);
    }

    expect(response.body.success).toBe(true);
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.pagination).toBeDefined();

    const pagination = response.body.pagination;
    expect(pagination.page).toBeGreaterThan(0);
    expect(pagination.limit).toBeGreaterThan(0);
    expect(pagination.total).toBeGreaterThanOrEqual(0);
    expect(pagination.totalPages).toBeGreaterThanOrEqual(0);
    expect(typeof pagination.hasNext).toBe('boolean');
    expect(typeof pagination.hasPrev).toBe('boolean');

    // Validate pagination logic
    if (pagination.total > 0) {
      expect(pagination.totalPages).toBeGreaterThan(0);
      if (pagination.page > 1) {
        expect(pagination.hasPrev).toBe(true);
      }
      if (pagination.page < pagination.totalPages) {
        expect(pagination.hasNext).toBe(true);
      }
    }

    // Validate each item if schema provided
    if (itemSchema && response.body.data.length > 0) {
      response.body.data.forEach((item: any, index: number) => {
        const itemResult = itemSchema.safeParse(item);
        if (!itemResult.success) {
          throw new Error(`Item ${index} validation failed: ${itemResult.error.issues.map(i => i.message).join(', ')}`);
        }
      });
    }
  }

  /**
   * Validate user response
   */
  static validateUserResponse(response: Response, statusCode: number = 200): void {
    this.validateSuccessResponse(response, UserResponseSchema, statusCode);
  }

  /**
   * Validate authentication response
   */
  static validateAuthResponse(response: Response, statusCode: number = 200): void {
    this.validateSuccessResponse(response, AuthResponseSchema, statusCode);
    
    // Additional auth-specific validations
    const { token, expiresIn } = response.body.data;
    expect(token).toMatch(/^[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+$/); // JWT format
    expect(expiresIn).toBeGreaterThan(0);
  }

  /**
   * Validate property response
   */
  static validatePropertyResponse(response: Response, statusCode: number = 200): void {
    this.validateSuccessResponse(response, PropertyResponseSchema, statusCode);
    
    // Additional property-specific validations
    const property = response.body.data;
    expect(property.fullDayPrice).toBeGreaterThanOrEqual(property.halfDayPrice);
    expect(property.bedrooms).toBeGreaterThanOrEqual(1);
    expect(property.bathrooms).toBeGreaterThanOrEqual(1);
    expect(property.images.length).toBeGreaterThan(0);
  }

  /**
   * Validate booking response
   */
  static validateBookingResponse(response: Response, statusCode: number = 200): void {
    this.validateSuccessResponse(response, BookingResponseSchema, statusCode);
    
    // Additional booking-specific validations
    const booking = response.body.data;
    expect(booking.guests).toBeGreaterThan(0);
    expect(booking.totalAmount).toBeGreaterThan(0);
    
    // Validate date format
    expect(booking.bookingDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    
    // Validate that booking date is not in the past (for new bookings)
    if (booking.status === 'pending' || booking.status === 'confirmed') {
      const bookingDate = new Date(booking.bookingDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      expect(bookingDate.getTime()).toBeGreaterThanOrEqual(today.getTime());
    }
  }

  /**
   * Validate properties list response
   */
  static validatePropertiesListResponse(response: Response): void {
    this.validatePaginatedResponse(response, PropertyResponseSchema);
  }

  /**
   * Validate bookings list response
   */
  static validateBookingsListResponse(response: Response): void {
    this.validatePaginatedResponse(response, BookingResponseSchema);
  }

  /**
   * Validate common validation error response
   */
  static validateValidationErrorResponse(response: Response): void {
    this.validateErrorResponse(response, 400, 'VALIDATION_ERROR');
    
    // Validation errors should have details
    expect(response.body.error.details).toBeDefined();
    expect(Array.isArray(response.body.error.details) || typeof response.body.error.details === 'object').toBe(true);
  }

  /**
   * Validate authentication error response
   */
  static validateAuthenticationErrorResponse(response: Response): void {
    this.validateErrorResponse(response, 401, 'AUTHENTICATION_ERROR');
  }

  /**
   * Validate authorization error response
   */
  static validateAuthorizationErrorResponse(response: Response): void {
    this.validateErrorResponse(response, 403, 'AUTHORIZATION_ERROR');
  }

  /**
   * Validate not found error response
   */
  static validateNotFoundErrorResponse(response: Response): void {
    this.validateErrorResponse(response, 404, 'NOT_FOUND');
  }

  /**
   * Validate conflict error response
   */
  static validateConflictErrorResponse(response: Response): void {
    this.validateErrorResponse(response, 409, 'CONFLICT');
  }

  /**
   * Validate rate limit error response
   */
  static validateRateLimitErrorResponse(response: Response): void {
    this.validateErrorResponse(response, 429, 'RATE_LIMIT_ERROR');
    
    // Rate limit responses should include retry information
    if (response.headers['retry-after']) {
      expect(parseInt(response.headers['retry-after'])).toBeGreaterThan(0);
    }
  }

  /**
   * Validate response headers for security and caching
   */
  static validateSecurityHeaders(response: Response): void {
    // Check for security headers
    expect(response.headers['x-request-id']).toBeDefined();
    
    // For authenticated endpoints
    if (response.request.header && response.request.header.Authorization) {
      expect(response.headers['cache-control']).toMatch(/private|no-cache|no-store/);
    }
  }

  /**
   * Validate response timing for performance
   */
  static validateResponseTiming(response: Response, maxTimeMs: number = 1000): void {
    // Note: This requires response time tracking to be set up in tests
    if (response.responseTime) {
      expect(response.responseTime).toBeLessThan(maxTimeMs);
    }
  }

  /**
   * Custom validation for business rules
   */
  static validateBusinessRules = {
    /**
     * Validate that user can only access their own resources
     */
    validateUserOwnership(response: Response, expectedUserId: number): void {
      ApiContractValidator.validateSuccessResponse(response);
      const data = response.body.data;
      
      if (data.userId) {
        expect(data.userId).toBe(expectedUserId);
      } else if (data.user && data.user.id) {
        expect(data.user.id).toBe(expectedUserId);
      }
    },

    /**
     * Validate that property owner can access their properties
     */
    validatePropertyOwnership(response: Response, expectedOwnerId: number): void {
      ApiContractValidator.validateSuccessResponse(response);
      const data = response.body.data;
      
      if (Array.isArray(data)) {
        data.forEach(property => {
          expect(property.ownerId || property.owner.id).toBe(expectedOwnerId);
        });
      } else {
        expect(data.ownerId || data.owner.id).toBe(expectedOwnerId);
      }
    },

    /**
     * Validate booking availability rules
     */
    validateBookingAvailability(response: Response): void {
      ApiContractValidator.validateSuccessResponse(response);
      const booking = response.body.data;
      
      // Booking date should be in the future
      const bookingDate = new Date(booking.bookingDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      expect(bookingDate.getTime()).toBeGreaterThanOrEqual(today.getTime());
      
      // If property availability is included, it should be available
      if (booking.property && booking.property.availability) {
        expect(booking.property.availability.available).toBe(true);
      }
    }
  };
}

/**
 * Contract Test Suite Builder
 * Helps create comprehensive contract test suites
 */
export class ContractTestSuiteBuilder {
  /**
   * Create standard CRUD contract tests for an endpoint
   */
  static createCrudContractTests(
    endpoint: string, 
    schema: z.ZodSchema, 
    sampleData: any
  ) {
    return {
      [`GET ${endpoint} should return paginated list`]: (request: any) => {
        return request.get(endpoint)
          .expect((res: Response) => {
            ApiContractValidator.validatePaginatedResponse(res, schema);
          });
      },

      [`GET ${endpoint}/:id should return single item`]: (request: any, id: number) => {
        return request.get(`${endpoint}/${id}`)
          .expect((res: Response) => {
            ApiContractValidator.validateSuccessResponse(res, schema);
          });
      },

      [`POST ${endpoint} should create new item`]: (request: any) => {
        return request.post(endpoint)
          .send(sampleData)
          .expect((res: Response) => {
            ApiContractValidator.validateSuccessResponse(res, schema, 201);
          });
      },

      [`PUT ${endpoint}/:id should update item`]: (request: any, id: number) => {
        return request.put(`${endpoint}/${id}`)
          .send(sampleData)
          .expect((res: Response) => {
            ApiContractValidator.validateSuccessResponse(res, schema);
          });
      },

      [`DELETE ${endpoint}/:id should delete item`]: (request: any, id: number) => {
        return request.delete(`${endpoint}/${id}`)
          .expect((res: Response) => {
            ApiContractValidator.validateSuccessResponse(res, undefined, 204);
          });
      }
    };
  }

  /**
   * Create authentication contract tests
   */
  static createAuthContractTests() {
    return {
      'POST /api/auth/register should validate registration': (request: any, userData: any) => {
        return request.post('/api/auth/register')
          .send(userData)
          .expect((res: Response) => {
            ApiContractValidator.validateAuthResponse(res, 201);
          });
      },

      'POST /api/auth/login should validate login': (request: any, credentials: any) => {
        return request.post('/api/auth/login')
          .send(credentials)
          .expect((res: Response) => {
            ApiContractValidator.validateAuthResponse(res);
          });
      },

      'GET /api/auth/me should validate user profile': (request: any, token: string) => {
        return request.get('/api/auth/me')
          .set('Authorization', `Bearer ${token}`)
          .expect((res: Response) => {
            ApiContractValidator.validateUserResponse(res);
          });
      }
    };
  }
}

export { ApiContractValidator as default };