import { RetryService, RetryableErrorType, RetryExhaustedError } from '../../server/services/RetryService';

describe('RetryService', () => {
  beforeAll(() => {
    // Mock console methods to avoid test output noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('executeWithRetry', () => {
    it('should succeed on first attempt without retry', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await RetryService.executeWithRetry(mockOperation);
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry on network errors and eventually succeed', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('network timeout'))
        .mockRejectedValueOnce(new Error('connection refused'))
        .mockResolvedValue('success');
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxAttempts: 3,
        baseDelayMs: 10, // Fast retry for testing
        maxDelayMs: 100
      });
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('validation failed'));
      
      await expect(
        RetryService.executeWithRetry(mockOperation, {
          maxAttempts: 3,
          baseDelayMs: 10
        })
      ).rejects.toThrow('validation failed');
      
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should exhaust retries and throw RetryExhaustedError', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('timeout'));
      
      await expect(
        RetryService.executeWithRetry(mockOperation, {
          maxAttempts: 2,
          baseDelayMs: 10
        })
      ).rejects.toThrow(RetryExhaustedError);
      
      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should handle database connection errors with proper retry', async () => {
      const dbError = new Error('database connection failed');
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(dbError)
        .mockResolvedValue('database query result');
      
      const result = await RetryService.executeWithRetry(
        mockOperation,
        RetryService.createDatabaseRetryConfig()
      );
      
      expect(result).toBe('database query result');
      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should handle rate limiting with exponential backoff', async () => {
      const rateLimitError = Object.assign(new Error('too many requests'), { status: 429 });
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValue('api response');
      
      const startTime = Date.now();
      const result = await RetryService.executeWithRetry(
        mockOperation,
        {
          maxAttempts: 2,
          baseDelayMs: 50,
          backoffMultiplier: 2
        }
      );
      const endTime = Date.now();
      
      expect(result).toBe('api response');
      expect(mockOperation).toHaveBeenCalledTimes(2);
      // Should have waited at least the base delay
      expect(endTime - startTime).toBeGreaterThanOrEqual(40);
    });

    it('should call retry callback on each retry attempt', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('timeout'))
        .mockRejectedValueOnce(new Error('network error'))
        .mockResolvedValue('success');
      
      const onRetrySpy = jest.fn();
      
      await RetryService.executeWithRetry(mockOperation, {
        maxAttempts: 3,
        baseDelayMs: 10,
        onRetry: onRetrySpy
      });
      
      expect(onRetrySpy).toHaveBeenCalledTimes(2);
      expect(onRetrySpy).toHaveBeenNthCalledWith(1, 1, expect.any(Error));
      expect(onRetrySpy).toHaveBeenNthCalledWith(2, 2, expect.any(Error));
    });
  });

  describe('executeWithRetryResult', () => {
    it('should return detailed result for successful operation', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await RetryService.executeWithRetryResult(mockOperation);
      
      expect(result).toEqual({
        success: true,
        result: 'success',
        attempts: 1,
        totalDuration: expect.any(Number)
      });
    });

    it('should return detailed result when retries are exhausted', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('timeout'));
      
      const result = await RetryService.executeWithRetryResult(mockOperation, {
        maxAttempts: 2,
        baseDelayMs: 10
      });
      
      expect(result).toEqual({
        success: false,
        error: expect.any(Error),
        attempts: 2,
        totalDuration: expect.any(Number)
      });
    });
  });

  describe('timeFunction', () => {
    it('should time function execution and record metrics', async () => {
      const mockFn = jest.fn().mockResolvedValue('result');
      
      const result = await RetryService.timeFunction('test_operation', mockFn, { user: 'test' });
      
      expect(result).toBe('result');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should record error metrics when function fails', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('function failed'));
      
      await expect(
        RetryService.timeFunction('test_operation', mockFn)
      ).rejects.toThrow('function failed');
      
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('error categorization', () => {
    const testCases = [
      { error: new Error('timeout occurred'), shouldRetry: true },
      { error: new Error('connection failed'), shouldRetry: true },
      { error: new Error('network unavailable'), shouldRetry: true },
      { error: new Error('database connection lost'), shouldRetry: true },
      { error: Object.assign(new Error('rate limited'), { status: 429 }), shouldRetry: true },
      { error: Object.assign(new Error('server error'), { status: 500 }), shouldRetry: true },
      { error: Object.assign(new Error('service unavailable'), { status: 503 }), shouldRetry: true },
      { error: Object.assign(new Error('bad request'), { status: 400 }), shouldRetry: false },
      { error: Object.assign(new Error('unauthorized'), { status: 401 }), shouldRetry: false },
      { error: new Error('validation error'), shouldRetry: false }
    ];

    testCases.forEach(({ error, shouldRetry }) => {
      it(`should ${shouldRetry ? 'retry' : 'not retry'} for error: ${error.message}`, async () => {
        const mockOperation = jest.fn().mockRejectedValue(error);
        
        const promise = RetryService.executeWithRetry(mockOperation, {
          maxAttempts: 2,
          baseDelayMs: 10
        });
        
        if (shouldRetry) {
          await expect(promise).rejects.toThrow(RetryExhaustedError);
          expect(mockOperation).toHaveBeenCalledTimes(2);
        } else {
          await expect(promise).rejects.toThrow(error.message);
          expect(mockOperation).toHaveBeenCalledTimes(1);
        }
      });
    });
  });

  describe('configuration presets', () => {
    it('should provide appropriate database retry configuration', () => {
      const config = RetryService.createDatabaseRetryConfig();
      
      expect(config.maxAttempts).toBe(5);
      expect(config.baseDelayMs).toBe(500);
      expect(config.maxDelayMs).toBe(10000);
      expect(config.backoffMultiplier).toBe(2);
      expect(config.onRetry).toBeDefined();
    });

    it('should provide appropriate API retry configuration', () => {
      const config = RetryService.createAPIRetryConfig();
      
      expect(config.maxAttempts).toBe(3);
      expect(config.baseDelayMs).toBe(1000);
      expect(config.maxDelayMs).toBe(15000);
      expect(config.backoffMultiplier).toBe(2);
      expect(config.onRetry).toBeDefined();
    });

    it('should provide conservative payment retry configuration', () => {
      const config = RetryService.createPaymentRetryConfig();
      
      expect(config.maxAttempts).toBe(2); // Limited for payment safety
      expect(config.baseDelayMs).toBe(2000);
      expect(config.maxDelayMs).toBe(5000);
      expect(config.backoffMultiplier).toBe(1.5);
      expect(config.onRetry).toBeDefined();
    });
  });

  describe('concurrent operations', () => {
    it('should handle multiple concurrent retry operations independently', async () => {
      const operation1 = jest.fn()
        .mockRejectedValueOnce(new Error('timeout'))
        .mockResolvedValue('result1');
      
      const operation2 = jest.fn()
        .mockRejectedValueOnce(new Error('connection'))
        .mockRejectedValueOnce(new Error('network'))
        .mockResolvedValue('result2');
      
      const [result1, result2] = await Promise.all([
        RetryService.executeWithRetry(operation1, { maxAttempts: 2, baseDelayMs: 10 }),
        RetryService.executeWithRetry(operation2, { maxAttempts: 3, baseDelayMs: 10 })
      ]);
      
      expect(result1).toBe('result1');
      expect(result2).toBe('result2');
      expect(operation1).toHaveBeenCalledTimes(2);
      expect(operation2).toHaveBeenCalledTimes(3);
    });
  });

  describe('jitter and backoff calculation', () => {
    it('should apply exponential backoff with jitter', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('timeout'))
        .mockRejectedValueOnce(new Error('timeout'))
        .mockResolvedValue('success');
      
      const delays: number[] = [];
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = jest.fn((callback, delay) => {
        delays.push(delay);
        return originalSetTimeout(callback, 1); // Speed up for testing
      }) as any;
      
      await RetryService.executeWithRetry(mockOperation, {
        maxAttempts: 3,
        baseDelayMs: 100,
        backoffMultiplier: 2,
        jitterMs: 50
      });
      
      // Should have two delays (for two retries)
      expect(delays).toHaveLength(2);
      // First delay should be around 100ms + jitter
      expect(delays[0]).toBeGreaterThanOrEqual(100);
      expect(delays[0]).toBeLessThanOrEqual(150);
      // Second delay should be around 200ms + jitter
      expect(delays[1]).toBeGreaterThanOrEqual(200);
      expect(delays[1]).toBeLessThanOrEqual(250);
      
      global.setTimeout = originalSetTimeout;
    });
  });
});