import { ErrorRecoveryService, OperationType } from '../../server/services/ErrorRecoveryService';

describe('ErrorRecoveryService', () => {
  let errorRecoveryService: ErrorRecoveryService;

  beforeEach(() => {
    errorRecoveryService = new ErrorRecoveryService();
    
    // Mock console methods to avoid test output noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    errorRecoveryService.stop();
    jest.restoreAllMocks();
    jest.clearAllTimers();
  });

  describe('executeWithRecovery', () => {
    it('should succeed with primary operation on first attempt', async () => {
      const primaryOperation = jest.fn().mockResolvedValue('primary success');
      const fallbackOperation = jest.fn().mockResolvedValue('fallback success');

      const strategy = {
        operationType: OperationType.PAYMENT_PROCESSING,
        primaryOperation,
        fallbackOperations: [fallbackOperation]
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result).toEqual({
        success: true,
        result: 'primary success',
        usedFallback: false,
        errors: [],
        totalAttempts: 1,
        duration: expect.any(Number)
      });
      expect(primaryOperation).toHaveBeenCalledTimes(1);
      expect(fallbackOperation).not.toHaveBeenCalled();
    });

    it('should use fallback when primary operation fails', async () => {
      const primaryError = new Error('primary failed');
      const primaryOperation = jest.fn().mockRejectedValue(primaryError);
      const fallbackOperation = jest.fn().mockResolvedValue('fallback success');
      const onFallbackUsed = jest.fn();

      const strategy = {
        operationType: OperationType.BOOKING_CREATION,
        primaryOperation,
        fallbackOperations: [fallbackOperation],
        onFallbackUsed
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result).toEqual({
        success: true,
        result: 'fallback success',
        usedFallback: true,
        fallbackIndex: 0,
        errors: [primaryError],
        totalAttempts: 2,
        duration: expect.any(Number)
      });
      expect(onFallbackUsed).toHaveBeenCalledWith(0, primaryError);
    });

    it('should try multiple fallbacks until one succeeds', async () => {
      const primaryError = new Error('primary failed');
      const fallback1Error = new Error('fallback1 failed');
      
      const primaryOperation = jest.fn().mockRejectedValue(primaryError);
      const fallbackOperation1 = jest.fn().mockRejectedValue(fallback1Error);
      const fallbackOperation2 = jest.fn().mockResolvedValue('fallback2 success');

      const strategy = {
        operationType: OperationType.EMAIL_NOTIFICATION,
        primaryOperation,
        fallbackOperations: [fallbackOperation1, fallbackOperation2]
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result).toEqual({
        success: true,
        result: 'fallback2 success',
        usedFallback: true,
        fallbackIndex: 1,
        errors: [primaryError, fallback1Error],
        totalAttempts: 3,
        duration: expect.any(Number)
      });
    });

    it('should fail when all operations including fallbacks fail', async () => {
      const primaryError = new Error('primary failed');
      const fallbackError = new Error('fallback failed');
      const onAllFailed = jest.fn();
      
      const primaryOperation = jest.fn().mockRejectedValue(primaryError);
      const fallbackOperation = jest.fn().mockRejectedValue(fallbackError);

      const strategy = {
        operationType: OperationType.PAYMENT_PROCESSING,
        primaryOperation,
        fallbackOperations: [fallbackOperation],
        onAllFailed
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result).toEqual({
        success: false,
        usedFallback: false,
        errors: [primaryError, fallbackError],
        totalAttempts: 2,
        duration: expect.any(Number)
      });
      expect(onAllFailed).toHaveBeenCalledWith([primaryError, fallbackError]);
    });

    it('should work with retry configuration', async () => {
      const networkError = new Error('network timeout');
      const primaryOperation = jest.fn()
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success after retries');

      const strategy = {
        operationType: OperationType.EXTERNAL_API_CALL,
        primaryOperation,
        fallbackOperations: [],
        retryConfig: {
          maxAttempts: 3,
          baseDelayMs: 10,
          backoffMultiplier: 1
        }
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);

      expect(result.success).toBe(true);
      expect(result.result).toBe('success after retries');
      expect(primaryOperation).toHaveBeenCalledTimes(3);
    });
  });

  describe('queueFailedOperation', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should queue failed operation for retry', async () => {
      const failedOperation = jest.fn().mockRejectedValue(new Error('operation failed'));
      
      await errorRecoveryService.queueFailedOperation(
        'test-operation-1',
        OperationType.PAYMENT_PROCESSING,
        failedOperation,
        { userId: 123 }
      );

      const status = errorRecoveryService.getQueueStatus();
      expect(status.queueSize).toBe(1);
      expect(status.operations[0].id).toBe('test-operation-1');
      expect(status.operations[0].operationType).toBe(OperationType.PAYMENT_PROCESSING);
    });

    it('should retry queued operations at scheduled intervals', async () => {
      const failedOperation = jest.fn()
        .mockRejectedValueOnce(new Error('still failing'))
        .mockResolvedValue('eventually succeeds');
      
      await errorRecoveryService.queueFailedOperation(
        'retry-test',
        OperationType.DATABASE_OPERATION,
        failedOperation
      );

      expect(errorRecoveryService.getQueueStatus().queueSize).toBe(1);

      // Fast forward to trigger first retry
      jest.advanceTimersByTime(30000); // 30 seconds (processing interval)
      await new Promise(resolve => setTimeout(resolve, 0)); // Allow promises to resolve

      // Operation should still be in queue (failed on first retry)
      expect(errorRecoveryService.getQueueStatus().queueSize).toBe(1);
      expect(failedOperation).toHaveBeenCalledTimes(1);

      // Fast forward to trigger second retry
      jest.advanceTimersByTime(90000); // Another 90 seconds (retry delay)
      await new Promise(resolve => setTimeout(resolve, 0));

      // Operation should now be removed from queue (succeeded on second retry)
      expect(errorRecoveryService.getQueueStatus().queueSize).toBe(0);
      expect(failedOperation).toHaveBeenCalledTimes(2);
    });

    it('should give up on operations after max retries', async () => {
      const persistentFailure = jest.fn().mockRejectedValue(new Error('persistent failure'));
      
      await errorRecoveryService.queueFailedOperation(
        'persistent-failure',
        OperationType.SMS_NOTIFICATION,
        persistentFailure
      );

      // Simulate multiple retry attempts
      for (let i = 0; i < 6; i++) {
        jest.advanceTimersByTime(30000);
        await new Promise(resolve => setTimeout(resolve, 0));
        if (i < 4) {
          jest.advanceTimersByTime(60000 * Math.pow(2, i)); // Exponential backoff
        }
      }

      // Operation should be removed after 5 failed retries
      expect(errorRecoveryService.getQueueStatus().queueSize).toBe(0);
      expect(persistentFailure).toHaveBeenCalledTimes(5);
    });
  });

  describe('strategy factories', () => {
    it('should create payment strategy with fallbacks', () => {
      const primaryProcessor = jest.fn();
      const fallbackProcessor = jest.fn();
      
      const strategy = ErrorRecoveryService.createPaymentStrategy(
        primaryProcessor,
        fallbackProcessor
      );

      expect(strategy.operationType).toBe(OperationType.PAYMENT_PROCESSING);
      expect(strategy.primaryOperation).toBe(primaryProcessor);
      expect(strategy.fallbackOperations).toContain(fallbackProcessor);
      expect(strategy.retryConfig).toBeDefined();
      expect(strategy.onFallbackUsed).toBeDefined();
      expect(strategy.onAllFailed).toBeDefined();
    });

    it('should create booking strategy with fallbacks', () => {
      const primaryCreator = jest.fn();
      const fallbackCreator = jest.fn();
      
      const strategy = ErrorRecoveryService.createBookingStrategy(
        primaryCreator,
        fallbackCreator
      );

      expect(strategy.operationType).toBe(OperationType.BOOKING_CREATION);
      expect(strategy.primaryOperation).toBe(primaryCreator);
      expect(strategy.fallbackOperations).toContain(fallbackCreator);
    });

    it('should create notification strategy with multiple channels', () => {
      const primaryNotification = jest.fn();
      const emailFallback = jest.fn();
      const smsFallback = jest.fn();
      
      const strategy = ErrorRecoveryService.createNotificationStrategy(
        primaryNotification,
        [emailFallback, smsFallback]
      );

      expect(strategy.operationType).toBe(OperationType.EMAIL_NOTIFICATION);
      expect(strategy.primaryOperation).toBe(primaryNotification);
      expect(strategy.fallbackOperations).toEqual([emailFallback, smsFallback]);
    });
  });

  describe('concurrent recovery operations', () => {
    it('should handle multiple concurrent recovery operations', async () => {
      const strategy1 = {
        operationType: OperationType.PAYMENT_PROCESSING,
        primaryOperation: jest.fn().mockResolvedValue('payment success'),
        fallbackOperations: []
      };

      const strategy2 = {
        operationType: OperationType.BOOKING_CREATION,
        primaryOperation: jest.fn().mockResolvedValue('booking success'),
        fallbackOperations: []
      };

      const [result1, result2] = await Promise.all([
        errorRecoveryService.executeWithRecovery(strategy1),
        errorRecoveryService.executeWithRecovery(strategy2)
      ]);

      expect(result1.success).toBe(true);
      expect(result1.result).toBe('payment success');
      expect(result2.success).toBe(true);
      expect(result2.result).toBe('booking success');
    });
  });

  describe('queue management', () => {
    it('should provide accurate queue status', async () => {
      const operation1 = jest.fn().mockRejectedValue(new Error('fail'));
      const operation2 = jest.fn().mockRejectedValue(new Error('fail'));
      
      await errorRecoveryService.queueFailedOperation('op1', OperationType.PAYMENT_PROCESSING, operation1);
      await errorRecoveryService.queueFailedOperation('op2', OperationType.EMAIL_NOTIFICATION, operation2);

      const status = errorRecoveryService.getQueueStatus();
      
      expect(status.queueSize).toBe(2);
      expect(status.isProcessing).toBe(false);
      expect(status.operations).toHaveLength(2);
      expect(status.operations.map(op => op.id)).toEqual(['op1', 'op2']);
      expect(status.operations.map(op => op.operationType)).toEqual([
        OperationType.PAYMENT_PROCESSING,
        OperationType.EMAIL_NOTIFICATION
      ]);
    });

    it('should track retry counts correctly', async () => {
      jest.useFakeTimers();
      
      const failingOperation = jest.fn().mockRejectedValue(new Error('keeps failing'));
      
      await errorRecoveryService.queueFailedOperation('failing-op', OperationType.DATABASE_OPERATION, failingOperation);

      // Trigger a few retry attempts
      for (let i = 0; i < 3; i++) {
        jest.advanceTimersByTime(30000);
        await new Promise(resolve => setTimeout(resolve, 0));
        if (i < 2) {
          jest.advanceTimersByTime(60000 * Math.pow(2, i));
        }
      }

      const status = errorRecoveryService.getQueueStatus();
      expect(status.operations[0].retryCount).toBe(3);
      
      jest.useRealTimers();
    });
  });

  describe('error handling edge cases', () => {
    it('should handle operations that throw non-Error objects', async () => {
      const primaryOperation = jest.fn().mockRejectedValue('string error');
      const fallbackOperation = jest.fn().mockResolvedValue('fallback success');

      const strategy = {
        operationType: OperationType.EXTERNAL_API_CALL,
        primaryOperation,
        fallbackOperations: [fallbackOperation]
      };

      const result = await errorRecoveryService.executeWithRecovery(strategy);
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('fallback success');
      expect(result.errors[0]).toBeInstanceOf(Error);
    });

    it('should handle async operation timeouts', async () => {
      const slowOperation = () => new Promise((resolve) => {
        setTimeout(() => resolve('too slow'), 10000);
      });
      
      const fastFallback = jest.fn().mockResolvedValue('fast fallback');

      const strategy = {
        operationType: OperationType.EXTERNAL_API_CALL,
        primaryOperation: slowOperation,
        fallbackOperations: [fastFallback],
        retryConfig: {
          maxAttempts: 1,
          baseDelayMs: 100
        }
      };

      // This test would need actual timeout implementation in the recovery service
      // For now, we'll test that the fallback is used when primary fails
      const result = await errorRecoveryService.executeWithRecovery({
        ...strategy,
        primaryOperation: jest.fn().mockRejectedValue(new Error('timeout'))
      });

      expect(result.success).toBe(true);
      expect(result.result).toBe('fast fallback');
      expect(result.usedFallback).toBe(true);
    });
  });
});