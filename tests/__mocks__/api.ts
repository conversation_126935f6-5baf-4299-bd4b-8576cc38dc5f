import { http, HttpResponse } from 'msw'
import { setupServer } from 'msw/node'
import { 
  createUserResponse, 
  createPropertyResponse, 
  createBookingResponse, 
  createReviewResponse 
} from '../fixtures/factories'

// Mock API handlers
export const handlers = [
  // Auth endpoints
  http.post('/api/auth/register', () => {
    return HttpResponse.json({
      user: createUserResponse(),
      token: 'mock-jwt-token'
    }, { status: 201 })
  }),

  http.post('/api/auth/login', () => {
    return HttpResponse.json({
      user: createUserResponse(),
      token: 'mock-jwt-token'
    })
  }),

  http.get('/api/auth/me', () => {
    return HttpResponse.json(createUserResponse())
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ message: 'Successfully logged out' })
  }),

  // Properties endpoints
  http.get('/api/properties', ({ request }) => {
    const url = new URL(request.url)
    const featured = url.searchParams.get('featured')
    const location = url.searchParams.get('location')
    
    let properties = [
      createPropertyResponse({ id: 1, featured: true }),
      createPropertyResponse({ id: 2, title: 'Mountain Retreat', location: 'Colorado, USA' }),
      createPropertyResponse({ id: 3, title: 'Beach House', location: 'Malibu, California' })
    ]

    if (featured === 'true') {
      properties = properties.filter(p => p.featured)
    }

    if (location) {
      properties = properties.filter(p => 
        p.location.toLowerCase().includes(location.toLowerCase())
      )
    }

    return HttpResponse.json(properties)
  }),

  http.get('/api/properties/:id', ({ params }) => {
    return HttpResponse.json(createPropertyResponse({ id: Number(params.id) }))
  }),

  http.post('/api/properties', () => {
    return HttpResponse.json(createPropertyResponse(), { status: 201 })
  }),

  http.put('/api/properties/:id', ({ params }) => {
    return HttpResponse.json(createPropertyResponse({ id: Number(params.id) }))
  }),

  http.delete('/api/properties/:id', () => {
    return new HttpResponse(null, { status: 204 })
  }),

  // Bookings endpoints
  http.get('/api/bookings', () => {
    return HttpResponse.json([
      { 
        ...createBookingResponse(), 
        property: createPropertyResponse() 
      }
    ])
  }),

  http.post('/api/bookings', () => {
    return HttpResponse.json(createBookingResponse(), { status: 201 })
  }),

  http.get('/api/check-availability/:propertyId', () => {
    return HttpResponse.json({ available: true })
  }),

  // Reviews endpoints
  http.get('/api/properties/:propertyId/reviews', () => {
    return HttpResponse.json({
      reviews: [createReviewResponse()],
      averageRating: 4.5,
      totalReviews: 1
    })
  }),

  http.post('/api/reviews', () => {
    return HttpResponse.json(createReviewResponse(), { status: 201 })
  }),

  // File upload endpoint
  http.post('/api/upload', () => {
    return HttpResponse.json({
      urls: ['/uploads/mock-image-1.jpg', '/uploads/mock-image-2.jpg']
    })
  }),

  // Profile endpoints
  http.get('/api/profile', () => {
    return HttpResponse.json(createUserResponse())
  }),

  http.patch('/api/profile', () => {
    return HttpResponse.json(createUserResponse())
  }),

  // Error handlers for testing error scenarios
  http.get('/api/properties/999', () => {
    return HttpResponse.json({ message: 'Property not found' }, { status: 404 })
  }),

  http.post('/api/auth/login-error', () => {
    return HttpResponse.json({ message: 'Invalid credentials' }, { status: 401 })
  }),
]

// Create server instance
export const server = setupServer(...handlers)

// Helper functions for tests
export const mockApiError = (path: string, status: number = 500, message: string = 'Server error') => {
  server.use(
    http.get(path, () => {
      return HttpResponse.json({ message }, { status })
    })
  )
}

export const mockApiSuccess = (path: string, data: any, status: number = 200) => {
  server.use(
    http.get(path, () => {
      return HttpResponse.json(data, { status })
    })
  )
}