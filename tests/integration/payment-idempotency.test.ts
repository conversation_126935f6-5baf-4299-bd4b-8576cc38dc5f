import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '../../server';
import { db } from '../../server/db';
import { bookings, properties, users, paymentOrders } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { generateToken } from '../integration/api-helpers';

describe('Payment Idempotency', () => {
  let testUser: any;
  let testProperty: any;
  let testBooking: any;
  let authToken: string;

  beforeEach(async () => {
    // Clean up test data
    await db.delete(paymentOrders).where(eq(paymentOrders.bookingId, 999));
    await db.delete(bookings).where(eq(bookings.userId, 999));
    await db.delete(properties).where(eq(properties.ownerId, 999));
    await db.delete(users).where(eq(users.id, 999));

    // Create test user
    const [user] = await db.insert(users).values({
      id: 999,
      email: '<EMAIL>',
      phone: '+919999999998',
      isVerified: true,
      role: 'user'
    }).returning();
    testUser = user;

    // Create test property
    const [property] = await db.insert(properties).values({
      id: 999,
      name: 'Test Payment Property',
      location: 'Test Location',
      fullDayPrice: 2000,
      halfDayPrice: 1000,
      ownerId: 999,
      isActive: true
    }).returning();
    testProperty = property;

    // Create test booking
    const [booking] = await db.insert(bookings).values({
      id: 999,
      propertyId: testProperty.id,
      userId: testUser.id,
      bookingDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      bookingType: 'full_day',
      guests: 4,
      totalPrice: 2000,
      status: 'pending_payment'
    }).returning();
    testBooking = booking;

    authToken = generateToken(testUser);
  });

  afterEach(async () => {
    // Cleanup
    await db.delete(paymentOrders).where(eq(paymentOrders.bookingId, 999));
    await db.delete(bookings).where(eq(bookings.userId, 999));
    await db.delete(properties).where(eq(properties.ownerId, 999));
    await db.delete(users).where(eq(users.id, 999));
  });

  it('should prevent duplicate payment orders with same idempotency key', async () => {
    const paymentData = {
      bookingId: testBooking.id,
      currency: 'INR',
      customerDetails: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9999999999'
      },
      gstDetails: {
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      }
    };

    // Create multiple concurrent payment order requests
    const promises = Array(5).fill(null).map(() => 
      request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
    );

    const results = await Promise.allSettled(promises);

    // At least one should succeed
    const successful = results.filter(r => 
      r.status === 'fulfilled' && 
      (r.value as any).status >= 200 && 
      (r.value as any).status < 300
    );

    expect(successful.length).toBeGreaterThanOrEqual(1);

    // Check database - should have only one payment order for this booking
    const dbPaymentOrders = await db.select()
      .from(paymentOrders)
      .where(eq(paymentOrders.bookingId, testBooking.id));

    expect(dbPaymentOrders.length).toBeGreaterThanOrEqual(1);
    
    // All payment orders should have different idempotency keys but same booking
    const uniqueIdempotencyKeys = new Set(dbPaymentOrders.map(po => po.idempotencyKey));
    expect(uniqueIdempotencyKeys.size).toBe(dbPaymentOrders.length);
  });

  it('should handle payment verification idempotency', async () => {
    // First create a payment order
    const paymentData = {
      bookingId: testBooking.id,
      currency: 'INR',
      customerDetails: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9999999999'
      },
      gstDetails: {
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      }
    };

    const orderResponse = await request(app)
      .post('/api/payments/create-order')
      .set('Authorization', `Bearer ${authToken}`)
      .send(paymentData)
      .expect(201);

    const paymentOrder = orderResponse.body.data.paymentOrder;

    // Mock payment verification data
    const verificationData = {
      razorpay_order_id: paymentOrder.razorpayOrderId,
      razorpay_payment_id: 'pay_test_12345',
      razorpay_signature: 'test_signature_12345',
      orderId: paymentOrder.id
    };

    // Try to verify the same payment multiple times concurrently
    const promises = Array(3).fill(null).map(() => 
      request(app)
        .post('/api/payments/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .send(verificationData)
    );

    const results = await Promise.allSettled(promises);

    // Count the different response types
    const responses = results.map(r => {
      if (r.status === 'fulfilled') {
        return (r.value as any).status;
      }
      return 'error';
    });

    // Should handle multiple verification attempts gracefully
    // (exact behavior depends on implementation)
    expect(responses.some(status => status >= 200 && status < 300)).toBe(true);
  });

  it('should maintain payment amount consistency across retries', async () => {
    const paymentData = {
      bookingId: testBooking.id,
      currency: 'INR',
      customerDetails: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9999999999'
      },
      gstDetails: {
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      }
    };

    // Create payment order
    const orderResponse = await request(app)
      .post('/api/payments/create-order')
      .set('Authorization', `Bearer ${authToken}`)
      .send(paymentData)
      .expect(201);

    const responseData = orderResponse.body.data;
    
    // Verify amount calculation consistency
    expect(responseData.totalAmount).toBeGreaterThan(0);
    expect(responseData.breakdown.baseAmount).toBeGreaterThan(0);
    expect(responseData.breakdown.totalAmount).toBe(responseData.totalAmount);
    
    // Amount should be 30% of booking total price for advance payment
    const expectedAdvanceAmount = Math.round(testBooking.totalPrice * 0.30);
    expect(responseData.breakdown.baseAmount).toBeCloseTo(expectedAdvanceAmount, 0);
  });

  it('should prevent payment processing with invalid booking states', async () => {
    // Update booking to confirmed state
    await db.update(bookings)
      .set({ status: 'confirmed' })
      .where(eq(bookings.id, testBooking.id));

    const paymentData = {
      bookingId: testBooking.id,
      currency: 'INR',
      customerDetails: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9999999999'
      },
      gstDetails: {
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      }
    };

    // Try to create payment order for confirmed booking
    const response = await request(app)
      .post('/api/payments/create-order')
      .set('Authorization', `Bearer ${authToken}`)
      .send(paymentData);

    // Should reject payment creation for non-pending bookings
    expect(response.status).toBe(400);
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});