import { describe, test, expect, beforeEach, beforeAll, afterAll, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { registerRoutes } from '../../server/routes';
import { 
  createTestUser, 
  createTestOwner,
  createTestProperty,
  createTestBooking,
  generateTestToken
} from './api-helpers';
import { db } from '../../server/db';
import { paymentOrders, paymentTransactions, gstRecords, securitySessions } from '../../shared/schema';

// Mock external services
vi.mock('razorpay', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      orders: {
        create: vi.fn().mockResolvedValue({
          id: 'order_test123',
          receipt: 'booking_1_123456789',
          amount: 10000,
          currency: 'INR',
          status: 'created'
        })
      },
      payments: {
        capture: vi.fn().mockResolvedValue({
          id: 'pay_test123',
          order_id: 'order_test123',
          amount: 10000,
          status: 'captured'
        }),
        refund: vi.fn().mockResolvedValue({
          id: 'rfnd_test123',
          payment_id: 'pay_test123',
          amount: 5000,
          status: 'processed'
        })
      }
    }))
  };
});

// Mock crypto for signature verification
vi.mock('crypto', async () => {
  const actual = await vi.importActual('crypto');
  return {
    ...actual,
    createHmac: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    digest: vi.fn().mockReturnValue('valid_signature'),
    timingSafeEqual: vi.fn().mockReturnValue(true),
    randomBytes: vi.fn().mockReturnValue({ toString: vi.fn().mockReturnValue('random_string') })
  };
});

let app: express.Application;
let server: any;

beforeAll(async () => {
  app = express();
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  
  server = await registerRoutes(app);
});

afterAll(async () => {
  if (server) {
    server.close();
  }
});

describe('Payment Endpoints Integration', () => {
  let testUser: any;
  let testOwner: any;
  let testProperty: any;
  let testBooking: any;
  let userToken: string;

  beforeEach(async () => {
    // Clean up tables
    await db.delete(paymentTransactions);
    await db.delete(paymentOrders);
    await db.delete(gstRecords);
    await db.delete(securitySessions);

    // Create test data
    testOwner = await createTestOwner({ 
      username: 'paymentowner', 
      email: '<EMAIL>' 
    });
    
    testUser = await createTestUser({ 
      username: 'paymentuser', 
      email: '<EMAIL>' 
    });
    
    testProperty = await createTestProperty(testOwner.id, {
      title: 'Payment Test Property',
      halfDayPrice: 5000,
      fullDayPrice: 8000,
      location: 'Test Location'
    });
    
    testBooking = await createTestBooking(testUser.id, testProperty.id, {
      bookingDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      bookingType: 'full_day',
      guests: 6,
      status: 'pending'
    });
    
    userToken = generateTestToken(testUser.id, 'user');
  });

  describe('POST /api/payments/create-order', () => {
    const validOrderData = {
      bookingId: 1, // Will be set in tests
      currency: 'INR',
      customerDetails: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+919876543210'
      },
      gstDetails: {
        supplierState: 'KA',
        recipientState: 'TN',
        serviceType: 'accommodation'
      }
    };

    test('should create payment order successfully', async () => {
      const orderData = { ...validOrderData, bookingId: testBooking.id };
      
      const response = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.paymentOrder).toBeDefined();
      expect(response.body.data.paymentOrder.razorpayOrderId).toBe('order_test123');
      expect(response.body.data.gstCalculation).toBeDefined();
      expect(response.body.data.totalAmount).toBeGreaterThan(8000); // Base amount + GST
      expect(response.body.data.breakdown).toBeDefined();
      expect(response.body.data.breakdown.baseAmount).toBe(8215); // 8000 + 15 (cleaning) + 200 (2 extra guests)
      expect(response.body.data.breakdown.gstAmount).toBeGreaterThan(0);
    });

    test('should reject order creation for non-existent booking', async () => {
      const orderData = { ...validOrderData, bookingId: 99999 };
      
      const response = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Booking not found');
    });

    test('should reject order creation for other user\'s booking', async () => {
      const otherUser = await createTestUser({ 
        username: 'otheruser', 
        email: '<EMAIL>' 
      });
      const otherToken = generateTestToken(otherUser.id, 'user');
      
      const orderData = { ...validOrderData, bookingId: testBooking.id };
      
      const response = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${otherToken}`)
        .send(orderData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('own bookings');
    });

    test('should enforce rate limiting on payment creation', async () => {
      const orderData = { ...validOrderData, bookingId: testBooking.id };
      
      // Make 3 requests (limit is 3 per 5 minutes)
      for (let i = 0; i < 3; i++) {
        await request(app)
          .post('/api/payments/create-order')
          .set('Authorization', `Bearer ${userToken}`)
          .send(orderData);
      }
      
      // 4th request should be rate limited
      const response = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData)
        .expect(429);

      expect(response.body.error).toContain('Too many payment creation attempts');
    });

    test('should validate required fields', async () => {
      const invalidOrderData = { ...validOrderData };
      delete invalidOrderData.customerDetails.email;
      
      const response = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(invalidOrderData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('email');
    });

    test('should require authentication', async () => {
      const orderData = { ...validOrderData, bookingId: testBooking.id };
      
      const response = await request(app)
        .post('/api/payments/create-order')
        .send(orderData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/payments/verify', () => {
    let paymentOrder: any;

    beforeEach(async () => {
      // Create a payment order first
      const orderData = {
        bookingId: testBooking.id,
        currency: 'INR',
        customerDetails: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+919876543210'
        },
        gstDetails: {
          supplierState: 'KA',
          recipientState: 'TN',
          serviceType: 'accommodation'
        }
      };

      const orderResponse = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData);

      paymentOrder = orderResponse.body.data.paymentOrder;
    });

    test('should verify payment signature successfully', async () => {
      const verifyData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: paymentOrder.id
      };

      const response = await request(app)
        .post('/api/payments/verify')
        .set('Authorization', `Bearer ${userToken}`)
        .send(verifyData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.paymentId).toBe('pay_test123');
      expect(response.body.data.orderId).toBe('order_test123');
    });

    test('should reject invalid payment signature', async () => {
      // Mock crypto to return false for signature verification
      const crypto = await import('crypto');
      vi.mocked(crypto.timingSafeEqual).mockReturnValue(false);

      const verifyData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'invalid_signature',
        orderId: paymentOrder.id
      };

      const response = await request(app)
        .post('/api/payments/verify')
        .set('Authorization', `Bearer ${userToken}`)
        .send(verifyData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('verification failed');

      // Reset mock
      vi.mocked(crypto.timingSafeEqual).mockReturnValue(true);
    });

    test('should reject verification for non-existent order', async () => {
      const verifyData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: 99999
      };

      const response = await request(app)
        .post('/api/payments/verify')
        .set('Authorization', `Bearer ${userToken}`)
        .send(verifyData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Payment order not found');
    });

    test('should validate required signature fields', async () => {
      const verifyData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        // missing razorpay_signature
        orderId: paymentOrder.id
      };

      const response = await request(app)
        .post('/api/payments/verify')
        .set('Authorization', `Bearer ${userToken}`)
        .send(verifyData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/payments/process', () => {
    let paymentOrder: any;

    beforeEach(async () => {
      // Create a payment order
      const orderData = {
        bookingId: testBooking.id,
        currency: 'INR',
        customerDetails: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+919876543210'
        },
        gstDetails: {
          supplierState: 'KA',
          recipientState: 'TN',
          serviceType: 'accommodation'
        }
      };

      const orderResponse = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData);

      paymentOrder = orderResponse.body.data.paymentOrder;
    });

    test('should process payment successfully for normal transactions', async () => {
      const processData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: paymentOrder.razorpayOrderId,
        bookingId: testBooking.id,
        idempotencyKey: 'process_test_key'
      };

      const response = await request(app)
        .post('/api/payments/process')
        .set('Authorization', `Bearer ${userToken}`)
        .send(processData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.captured).toBe(true);
      expect(response.body.data.bookingStatus).toBe('confirmed');
      expect(response.body.data.verificationData).toBeDefined();
    });

    test('should require 2FA for high-value transactions', async () => {
      // Create a high-value booking
      const expensiveProperty = await createTestProperty(testOwner.id, {
        title: 'Expensive Property',
        halfDayPrice: 15000,
        fullDayPrice: 25000,
        location: 'Premium Location'
      });
      
      const expensiveBooking = await createTestBooking(testUser.id, expensiveProperty.id, {
        bookingDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        bookingType: 'full_day',
        guests: 4,
        status: 'pending'
      });

      // Create payment order for expensive booking
      const orderResponse = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          bookingId: expensiveBooking.id,
          currency: 'INR',
          customerDetails: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+919876543210'
          },
          gstDetails: {
            supplierState: 'KA',
            recipientState: 'TN',
            serviceType: 'accommodation'
          }
        });

      const expensiveOrder = orderResponse.body.data.paymentOrder;

      const processData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: expensiveOrder.razorpayOrderId,
        bookingId: expensiveBooking.id,
        idempotencyKey: 'expensive_process_key'
        // No verificationToken provided
      };

      const response = await request(app)
        .post('/api/payments/process')
        .set('Authorization', `Bearer ${userToken}`)
        .send(processData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Two-factor authentication required');
    });

    test('should process high-value transaction with valid 2FA token', async () => {
      // Create expensive booking (same as previous test)
      const expensiveProperty = await createTestProperty(testOwner.id, {
        title: 'Expensive Property 2',
        halfDayPrice: 15000,
        fullDayPrice: 25000,
        location: 'Premium Location 2'
      });
      
      const expensiveBooking = await createTestBooking(testUser.id, expensiveProperty.id, {
        bookingDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        bookingType: 'full_day',
        guests: 4,
        status: 'pending'
      });

      // Create 2FA session
      const verificationToken = 'valid_2fa_token_123';
      await db.insert(securitySessions).values({
        userId: testUser.id,
        sessionId: 'session_123',
        sessionType: '2fa_payment',
        sessionData: {
          sessionId: 'session_123',
          bookingId: expensiveBooking.id,
          amount: 25000,
          method: 'razorpay',
          verified: true,
          verificationToken
        },
        expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from now
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Create payment order
      const orderResponse = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          bookingId: expensiveBooking.id,
          currency: 'INR',
          customerDetails: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+919876543210'
          },
          gstDetails: {
            supplierState: 'KA',
            recipientState: 'TN',
            serviceType: 'accommodation'
          }
        });

      const expensiveOrder = orderResponse.body.data.paymentOrder;

      const processData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: expensiveOrder.razorpayOrderId,
        bookingId: expensiveBooking.id,
        idempotencyKey: 'expensive_2fa_process_key',
        verificationToken
      };

      const response = await request(app)
        .post('/api/payments/process')
        .set('Authorization', `Bearer ${userToken}`)
        .send(processData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.captured).toBe(true);
    });

    test('should detect payment amount mismatch', async () => {
      // Mock payment order with different amount
      const [updatedOrder] = await db.update(paymentOrders)
        .set({ amount: 50000 }) // Much higher than expected
        .where(eq(paymentOrders.id, paymentOrder.id))
        .returning();

      const processData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: paymentOrder.razorpayOrderId,
        bookingId: testBooking.id,
        idempotencyKey: 'mismatch_test_key'
      };

      const response = await request(app)
        .post('/api/payments/process')
        .set('Authorization', `Bearer ${userToken}`)
        .send(processData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('amount mismatch');
    });

    test('should reject invalid 2FA verification token', async () => {
      // Create expensive booking
      const expensiveProperty = await createTestProperty(testOwner.id, {
        title: 'Expensive Property 3',
        halfDayPrice: 15000,
        fullDayPrice: 25000,
        location: 'Premium Location 3'
      });
      
      const expensiveBooking = await createTestBooking(testUser.id, expensiveProperty.id, {
        bookingDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        bookingType: 'full_day',
        guests: 4,
        status: 'pending'
      });

      // Create payment order
      const orderResponse = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          bookingId: expensiveBooking.id,
          currency: 'INR',
          customerDetails: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+919876543210'
          },
          gstDetails: {
            supplierState: 'KA',
            recipientState: 'TN',
            serviceType: 'accommodation'
          }
        });

      const expensiveOrder = orderResponse.body.data.paymentOrder;

      const processData = {
        razorpay_order_id: 'order_test123',
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'valid_signature',
        orderId: expensiveOrder.razorpayOrderId,
        bookingId: expensiveBooking.id,
        idempotencyKey: 'invalid_2fa_key',
        verificationToken: 'invalid_token'
      };

      const response = await request(app)
        .post('/api/payments/process')
        .set('Authorization', `Bearer ${userToken}`)
        .send(processData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid or expired 2FA verification');
    });
  });

  describe('POST /api/payments/capture', () => {
    test('should capture payment successfully', async () => {
      const captureData = {
        paymentId: 'pay_test123',
        amount: 10000,
        idempotencyKey: 'capture_test_key'
      };

      const response = await request(app)
        .post('/api/payments/capture')
        .set('Authorization', `Bearer ${userToken}`)
        .send(captureData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.captured).toBe(true);
      expect(response.body.data.transaction).toBeDefined();
    });

    test('should validate capture parameters', async () => {
      const captureData = {
        paymentId: 'pay_test123',
        // missing amount
        idempotencyKey: 'capture_test_key'
      };

      const response = await request(app)
        .post('/api/payments/capture')
        .set('Authorization', `Bearer ${userToken}`)
        .send(captureData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/payments/refund', () => {
    test('should process refund successfully', async () => {
      const refundData = {
        paymentId: 'pay_test123',
        amount: 5000,
        reason: 'Customer cancellation',
        idempotencyKey: 'refund_test_key'
      };

      const response = await request(app)
        .post('/api/payments/refund')
        .set('Authorization', `Bearer ${userToken}`)
        .send(refundData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.refunded).toBe(true);
      expect(response.body.data.refund).toBeDefined();
      expect(response.body.data.refund.id).toBe('rfnd_test123');
    });

    test('should validate refund parameters', async () => {
      const refundData = {
        paymentId: 'pay_test123',
        amount: 5000,
        // missing reason
        idempotencyKey: 'refund_test_key'
      };

      const response = await request(app)
        .post('/api/payments/refund')
        .set('Authorization', `Bearer ${userToken}`)
        .send(refundData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/payments/order/:id', () => {
    let paymentOrder: any;

    beforeEach(async () => {
      // Create a payment order
      const orderResponse = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          bookingId: testBooking.id,
          currency: 'INR',
          customerDetails: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+919876543210'
          },
          gstDetails: {
            supplierState: 'KA',
            recipientState: 'TN',
            serviceType: 'accommodation'
          }
        });

      paymentOrder = orderResponse.body.data.paymentOrder;
    });

    test('should retrieve payment order successfully', async () => {
      const response = await request(app)
        .get(`/api/payments/order/${paymentOrder.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(paymentOrder.id);
      expect(response.body.data.razorpayOrderId).toBe('order_test123');
    });

    test('should reject access to other user\'s payment orders', async () => {
      const otherUser = await createTestUser({ 
        username: 'otheruser2', 
        email: '<EMAIL>' 
      });
      const otherToken = generateTestToken(otherUser.id, 'user');

      const response = await request(app)
        .get(`/api/payments/order/${paymentOrder.id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('own bookings');
    });

    test('should return 404 for non-existent order', async () => {
      const response = await request(app)
        .get('/api/payments/order/99999')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Payment order not found');
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce general payment rate limiting', async () => {
      const orderData = {
        bookingId: testBooking.id,
        currency: 'INR',
        customerDetails: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+919876543210'
        },
        gstDetails: {
          supplierState: 'KA',
          recipientState: 'TN',
          serviceType: 'accommodation'
        }
      };

      // Make requests up to the limit (10 per 15 minutes for general endpoints)
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/payments/verify')
            .set('Authorization', `Bearer ${userToken}`)
            .send({
              razorpay_order_id: 'order_test',
              razorpay_payment_id: 'pay_test',
              razorpay_signature: 'sig_test',
              orderId: 1
            })
        );
      }

      await Promise.all(promises);

      // Next request should be rate limited
      const response = await request(app)
        .post('/api/payments/verify')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          razorpay_order_id: 'order_test',
          razorpay_payment_id: 'pay_test',
          razorpay_signature: 'sig_test',
          orderId: 1
        })
        .expect(429);

      expect(response.body.error).toContain('Too many payment attempts');
    });
  });

  describe('Security Tests', () => {
    test('should log audit events for successful payments', async () => {
      // This test would verify audit logging, but since we're mocking the audit logger,
      // we can't easily test the actual database inserts. In a real scenario, we'd
      // check the audit log tables for the expected entries.
      expect(true).toBe(true); // Placeholder
    });

    test('should handle Razorpay service errors gracefully', async () => {
      // Mock Razorpay to throw an error
      const Razorpay = await import('razorpay');
      const mockRazorpay = vi.mocked(Razorpay.default).mock.results[0].value;
      mockRazorpay.orders.create.mockRejectedValueOnce(new Error('Razorpay service unavailable'));

      const orderData = {
        bookingId: testBooking.id,
        currency: 'INR',
        customerDetails: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+919876543210'
        },
        gstDetails: {
          supplierState: 'KA',
          recipientState: 'TN',
          serviceType: 'accommodation'
        }
      };

      const response = await request(app)
        .post('/api/payments/create-order')
        .set('Authorization', `Bearer ${userToken}`)
        .send(orderData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Failed to create payment order');

      // Reset mock
      mockRazorpay.orders.create.mockResolvedValue({
        id: 'order_test123',
        receipt: 'booking_1_123456789',
        amount: 10000,
        currency: 'INR',
        status: 'created'
      });
    });
  });
});