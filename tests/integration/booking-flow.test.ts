import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import { registerRoutes } from '../../server/routes/index';

// Mock the config for testing
vi.mock('../../server/config', () => ({
  config: {
    isDevelopment: () => true,
    isProduction: () => false,
    jwt: { secret: 'test-jwt-secret' },
    cookie: { secret: 'test-cookie-secret' },
    app: { 
      nodeEnv: 'test',
      useSecureCookies: false
    },
    sendgrid: { available: false },
    twilio: { available: false },
    cloudinary: { available: false }
  }
}));

// Mock storage service
const mockStorage = {
  createUser: vi.fn(),
  getUserByEmail: vi.fn(),
  getUser: vi.fn(),
  createProperty: vi.fn(),
  getProperty: vi.fn(),
  checkAvailability: vi.fn(),
  createBooking: vi.fn(),
  getBooking: vi.fn(),
  getBookingsWithProperty: vi.fn(),
  updateBookingStatus: vi.fn(),
};

vi.mock('../../server/storage', () => ({
  storage: mockStorage
}));

describe('Booking Flow Integration Tests', () => {
  let app: express.Application;
  let server: any;
  let userToken: string;
  let ownerToken: string;
  let testProperty: any;
  let testUser: any;
  let testOwner: any;

  beforeAll(async () => {
    app = express();
    
    // Set up middleware
    app.use(express.json());
    app.use(express.urlencoded({ extended: false }));
    
    server = await registerRoutes(app);
  });

  afterAll(async () => {
    if (server && server.close) {
      server.close();
    }
  });

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Set up test data
    testUser = {
      id: 1,
      email: '<EMAIL>',
      username: 'testuser',
      fullName: 'Test User',
      role: 'user',
      password: 'hashedpassword'
    };

    testOwner = {
      id: 2,
      email: '<EMAIL>',
      username: 'testowner',
      fullName: 'Test Owner',
      role: 'owner',
      password: 'hashedpassword'
    };

    testProperty = {
      id: 1,
      ownerId: 2,
      title: 'Test Farmhouse',
      description: 'A beautiful test farmhouse',
      location: 'Test Location',
      halfDayPrice: 1000,
      fullDayPrice: 1800,
      bedrooms: 3,
      bathrooms: 2,
      amenities: ['WiFi', 'Pool'],
      images: ['test-image.jpg'],
      status: 'active',
      featured: false,
      createdAt: new Date()
    };
  });

  describe('End-to-End Booking Flow', () => {
    it('should complete a full booking flow: register user → login → view property → check availability → create booking', async () => {
      // Step 1: Register a new user
      mockStorage.getUserByEmail.mockResolvedValueOnce(null); // User doesn't exist
      mockStorage.createUser.mockResolvedValueOnce(testUser);

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          fullName: 'Test User',
          role: 'user'
        });

      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.email).toBe('<EMAIL>');
      userToken = registerResponse.body.data.token;

      // Step 2: Owner creates a property
      mockStorage.getUserByEmail.mockResolvedValueOnce(null);
      mockStorage.createUser.mockResolvedValueOnce(testOwner);

      const ownerRegisterResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testowner',
          email: '<EMAIL>',
          password: 'OwnerPassword123!',
          fullName: 'Test Owner',
          role: 'owner'
        });

      ownerToken = ownerRegisterResponse.body.data.token;

      mockStorage.createProperty.mockResolvedValueOnce(testProperty);

      const propertyResponse = await request(app)
        .post('/api/v1/properties')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send({
          title: 'Test Farmhouse',
          description: 'A beautiful test farmhouse',
          location: 'Test Location',
          halfDayPrice: 1000,
          fullDayPrice: 1800,
          bedrooms: 3,
          bathrooms: 2,
          amenities: ['WiFi', 'Pool'],
          images: ['test-image.jpg']
        });

      expect(propertyResponse.status).toBe(201);
      expect(propertyResponse.body.success).toBe(true);

      // Step 3: User views the property
      mockStorage.getProperty.mockResolvedValueOnce(testProperty);

      const viewPropertyResponse = await request(app)
        .get('/api/v1/properties/1');

      expect(viewPropertyResponse.status).toBe(200);
      expect(viewPropertyResponse.body.success).toBe(true);
      expect(viewPropertyResponse.body.data.id).toBe(1);

      // Step 4: User checks availability
      mockStorage.getProperty.mockResolvedValueOnce(testProperty);
      mockStorage.checkAvailability.mockResolvedValueOnce(true);

      const availabilityResponse = await request(app)
        .get('/api/v1/bookings/check-availability/1')
        .query({
          date: '2024-07-15',
          type: 'full_day'
        });

      expect(availabilityResponse.status).toBe(200);
      expect(availabilityResponse.body.success).toBe(true);
      expect(availabilityResponse.body.data.available).toBe(true);

      // Step 5: User creates a booking
      const testBooking = {
        id: 1,
        propertyId: 1,
        userId: 1,
        bookingDate: '2024-07-15',
        bookingType: 'full_day',
        guests: 4,
        totalPrice: 1800,
        status: 'confirmed',
        createdAt: new Date()
      };

      mockStorage.getProperty.mockResolvedValueOnce(testProperty);
      mockStorage.checkAvailability.mockResolvedValueOnce(true);
      mockStorage.createBooking.mockResolvedValueOnce(testBooking);

      const bookingResponse = await request(app)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          propertyId: 1,
          bookingDate: '2024-07-15',
          bookingType: 'full_day',
          guests: 4,
          totalPrice: 1800
        });

      expect(bookingResponse.status).toBe(201);
      expect(bookingResponse.body.success).toBe(true);
      expect(bookingResponse.body.data.id).toBe(1);
      expect(bookingResponse.body.data.status).toBe('confirmed');

      // Step 6: User views their booking
      mockStorage.getBooking.mockResolvedValueOnce(testBooking);

      const viewBookingResponse = await request(app)
        .get('/api/v1/bookings/1')
        .set('Authorization', `Bearer ${userToken}`);

      expect(viewBookingResponse.status).toBe(200);
      expect(viewBookingResponse.body.success).toBe(true);
      expect(viewBookingResponse.body.data.id).toBe(1);
    });

    it('should handle concurrent booking attempts for the same property and date', async () => {
      // Set up initial state
      mockStorage.getUserByEmail.mockResolvedValue(null);
      mockStorage.createUser.mockResolvedValue(testUser);
      
      // Register user
      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          fullName: 'Test User',
          role: 'user'
        });
      
      userToken = registerResponse.body.data.token;

      // Mock property exists
      mockStorage.getProperty.mockResolvedValue(testProperty);

      // First booking attempt - should succeed
      mockStorage.checkAvailability.mockResolvedValueOnce(true);
      mockStorage.createBooking.mockResolvedValueOnce({
        id: 1,
        propertyId: 1,
        userId: 1,
        bookingDate: '2024-07-15',
        bookingType: 'full_day',
        guests: 4,
        totalPrice: 1800,
        status: 'confirmed'
      });

      const firstBookingResponse = await request(app)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          propertyId: 1,
          bookingDate: '2024-07-15',
          bookingType: 'full_day',
          guests: 4,
          totalPrice: 1800
        });

      expect(firstBookingResponse.status).toBe(201);

      // Second booking attempt for same date/property - should fail
      mockStorage.checkAvailability.mockResolvedValueOnce(false);

      const secondBookingResponse = await request(app)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          propertyId: 1,
          bookingDate: '2024-07-15',
          bookingType: 'full_day',
          guests: 2,
          totalPrice: 1800
        });

      expect(secondBookingResponse.status).toBe(409);
      expect(secondBookingResponse.body.success).toBe(false);
      expect(secondBookingResponse.body.error.code).toBe('CONFLICT_ERROR');
    });

    it('should handle booking status updates by property owner', async () => {
      // Set up user and owner tokens
      mockStorage.getUserByEmail.mockResolvedValue(null);
      mockStorage.createUser
        .mockResolvedValueOnce(testUser)
        .mockResolvedValueOnce(testOwner);

      // Register user
      const userResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          fullName: 'Test User',
          role: 'user'
        });
      userToken = userResponse.body.data.token;

      // Register owner
      const ownerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testowner',
          email: '<EMAIL>',
          password: 'OwnerPassword123!',
          fullName: 'Test Owner',
          role: 'owner'
        });
      ownerToken = ownerResponse.body.data.token;

      // Mock existing booking
      const testBooking = {
        id: 1,
        propertyId: 1,
        userId: 1,
        bookingDate: '2024-07-15',
        bookingType: 'full_day',
        guests: 4,
        totalPrice: 1800,
        status: 'confirmed'
      };

      mockStorage.getBooking.mockResolvedValueOnce(testBooking);
      mockStorage.getProperty.mockResolvedValueOnce(testProperty);
      mockStorage.updateBookingStatus.mockResolvedValueOnce({
        ...testBooking,
        status: 'cancelled'
      });

      // Owner updates booking status
      const updateResponse = await request(app)
        .patch('/api/v1/bookings/1/status')
        .set('Authorization', `Bearer ${ownerToken}`)
        .send({ status: 'cancelled' });

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.data.status).toBe('cancelled');
    });

    it('should prevent unauthorized booking status updates', async () => {
      // Set up user token (not owner)
      mockStorage.getUserByEmail.mockResolvedValue(null);
      mockStorage.createUser.mockResolvedValueOnce(testUser);

      const userResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          fullName: 'Test User',
          role: 'user'
        });
      userToken = userResponse.body.data.token;

      // Mock existing booking
      const testBooking = {
        id: 1,
        propertyId: 1,
        userId: 1,
        bookingDate: '2024-07-15',
        bookingType: 'full_day',
        guests: 4,
        totalPrice: 1800,
        status: 'confirmed'
      };

      mockStorage.getBooking.mockResolvedValueOnce(testBooking);
      mockStorage.getProperty.mockResolvedValueOnce(testProperty);

      // User (not owner) tries to update booking status - should fail
      const updateResponse = await request(app)
        .patch('/api/v1/bookings/1/status')
        .set('Authorization', `Bearer ${userToken}`)
        .send({ status: 'cancelled' });

      expect(updateResponse.status).toBe(403);
      expect(updateResponse.body.success).toBe(false);
      expect(updateResponse.body.error.code).toBe('AUTHORIZATION_ERROR');
    });

    it('should validate booking data and prevent invalid bookings', async () => {
      // Set up user token
      mockStorage.getUserByEmail.mockResolvedValue(null);
      mockStorage.createUser.mockResolvedValueOnce(testUser);

      const userResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          fullName: 'Test User',
          role: 'user'
        });
      userToken = userResponse.body.data.token;

      // Test invalid booking data
      const invalidBookingResponse = await request(app)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          propertyId: 'invalid', // Should be number
          bookingDate: 'invalid-date',
          bookingType: 'invalid-type',
          guests: -1, // Should be positive
          totalPrice: 'invalid' // Should be number
        });

      expect(invalidBookingResponse.status).toBe(400);
      expect(invalidBookingResponse.body.success).toBe(false);
      expect(invalidBookingResponse.body.error.code).toBe('VALIDATION_ERROR');

      // Test booking for non-existent property
      mockStorage.getProperty.mockResolvedValueOnce(null);

      const nonExistentPropertyResponse = await request(app)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          propertyId: 999,
          bookingDate: '2024-07-15',
          bookingType: 'full_day',
          guests: 4,
          totalPrice: 1800
        });

      expect(nonExistentPropertyResponse.status).toBe(404);
      expect(nonExistentPropertyResponse.body.success).toBe(false);
      expect(nonExistentPropertyResponse.body.error.code).toBe('NOT_FOUND');
    });
  });
});