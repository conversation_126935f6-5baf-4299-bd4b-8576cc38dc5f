import { describe, test, expect, beforeAll, afterAll } from 'vitest'
import request from 'supertest'
import { createServer } from 'http'
import express from 'express'
import { registerRoutes } from '../../server/routes'

let app: express.Express
let server: any

beforeAll(async () => {
  app = express()
  server = await registerRoutes(app)
})

afterAll(async () => {
  if (server) {
    server.close()
  }
})

describe('Legal Pages API Routes', () => {
  test('API health check works', async () => {
    const response = await request(app)
      .get('/api/properties')
      .expect(200)

    expect(Array.isArray(response.body)).toBe(true)
  })

  describe('Error Handling', () => {
    test('handles non-existent API routes gracefully', async () => {
      const response = await request(app)
        .get('/api/non-existent-route')
        .expect(404)
    })
  })
})