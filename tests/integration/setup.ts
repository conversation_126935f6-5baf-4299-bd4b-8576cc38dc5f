// Set environment variables BEFORE importing any modules that use them
process.env.NODE_ENV = 'test'
process.env.JWT_SECRET = 'test-jwt-secret-key-12345'
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://farmhouse_user:farmhouse_password@localhost:5433/farmhouse_rental'

import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'
import { sql } from 'drizzle-orm'
import { db } from '../../server/db'
import { storage } from '../../server/storage'

// Test database setup
beforeAll(async () => {
  console.log('Using DATABASE_URL:', process.env.DATABASE_URL)
  
  console.log('Setting up test database...')
  
  // Initialize test database schema if needed
  try {
    // You might want to run migrations here
    // await migrate(db, { migrationsFolder: './migrations' })
    console.log('Test database setup complete')
  } catch (error) {
    console.error('Failed to setup test database:', error)
    throw error
  }
})

beforeEach(async () => {
  // REMOVED: Database cleanup that was causing data loss
  // Tests should use separate test database or mock data
  console.log('Test setup - database cleanup disabled for data safety')
})

afterEach(() => {
  // Clear all mocks after each test
  vi.clearAllMocks()
})

afterAll(async () => {
  // REMOVED: Final cleanup that was causing data loss
  // Tests should use separate test database or mock data
  vi.resetAllMocks()
  console.log('Test cleanup complete - database preserved')
})