import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest'
import request from 'supertest'
import express from 'express'
import jwt from 'jsonwebtoken'
import { config } from '../../server/config'
import authRoutes from '../../server/routes/auth'
import { TokenBlacklistService } from '../../server/services/TokenBlacklistService'
import { cacheService } from '../../server/services/CacheService'

// Mock storage and config for integration tests
vi.mock('../../server/storage', () => ({
  storage: {
    getUserByEmail: vi.fn(),
    createUser: vi.fn(),
    getUser: vi.fn(),
    updateUser: vi.fn()
  }
}))

vi.mock('../../server/config', () => ({
  config: {
    jwt: {
      secret: 'test-integration-secret'
    },
    app: {
      useSecureCookies: false,
      nodeEnv: 'test'
    }
  }
}))

describe('Token Blacklisting Integration Tests', () => {
  let app: express.Application
  let testUser: any
  let authToken: string

  beforeEach(async () => {
    // Set up test app
    app = express()
    app.use(express.json())
    app.use(express.urlencoded({ extended: false }))
    
    // Mock cookie parser
    app.use((req: any, res, next) => {
      req.cookies = req.cookies || {}
      res.cookie = vi.fn()
      res.clearCookie = vi.fn()
      next()
    })

    app.use('/api/auth', authRoutes)

    // Create test user and token
    testUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      fullName: 'Test User',
      role: 'user',
      password: 'hashedpassword'
    }

    authToken = jwt.sign(
      { userId: testUser.id, role: testUser.role },
      config.jwt.secret,
      { expiresIn: '1h' }
    )

    // Clear any existing cache
    await cacheService.clear()
    vi.clearAllMocks()
  })

  afterEach(async () => {
    await cacheService.clear()
    vi.restoreAllMocks()
  })

  describe('Logout with Token Blacklisting', () => {
    test('should blacklist token on logout', async () => {
      // Mock authentication by adding token to request
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Logout successful')

      // Verify token is blacklisted
      const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(authToken)
      expect(isBlacklisted).toBe(true)
    })

    test('should blacklist token from cookie on logout', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Cookie', `auth_token=${authToken}`)

      expect(response.status).toBe(200)

      // Verify token is blacklisted
      const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(authToken)
      expect(isBlacklisted).toBe(true)
    })

    test('should handle logout without token gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')

      // Should still return success even without token
      expect(response.status).toBe(401) // No token = no auth
    })

    test('should handle logout with invalid token', async () => {
      const invalidToken = 'invalid.jwt.token'

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${invalidToken}`)

      expect(response.status).toBe(401)
    })
  })

  describe('Logout All Devices', () => {
    test('should blacklist token on logout-all', async () => {
      const response = await request(app)
        .post('/api/auth/logout-all')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Logged out from all devices')

      // Verify token is blacklisted
      const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(authToken)
      expect(isBlacklisted).toBe(true)
    })

    test('should call blacklistUserTokens for logout-all', async () => {
      const blacklistUserTokensSpy = vi.spyOn(TokenBlacklistService, 'blacklistUserTokens')

      await request(app)
        .post('/api/auth/logout-all')
        .set('Authorization', `Bearer ${authToken}`)

      expect(blacklistUserTokensSpy).toHaveBeenCalledWith(testUser.id)
    })
  })

  describe('Authentication with Blacklisted Tokens', () => {
    test('should reject blacklisted token in subsequent requests', async () => {
      // First, logout to blacklist the token
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)

      // Then try to use the same token for a protected endpoint
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Token has been revoked')
    })

    test('should accept valid non-blacklisted token', async () => {
      // Mock storage to return user
      const { storage } = await import('../../server/storage')
      vi.mocked(storage.getUser).mockResolvedValue(testUser)

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
    })

    test('should handle blacklist check errors gracefully', async () => {
      // Mock TokenBlacklistService to throw error
      vi.spyOn(TokenBlacklistService, 'isTokenBlacklisted').mockRejectedValue(new Error('Cache error'))

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)

      // Should handle error gracefully (exact behavior depends on error handling implementation)
      expect(response.status).toBeGreaterThanOrEqual(400)
    })
  })

  describe('Token Lifecycle Integration', () => {
    test('should handle complete token lifecycle', async () => {
      // 1. Token should work initially
      const { storage } = await import('../../server/storage')
      vi.mocked(storage.getUser).mockResolvedValue(testUser)

      let response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)

      // 2. Logout should blacklist token
      response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)

      // 3. Token should be rejected after logout
      response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Token has been revoked')

      // 4. Verify token is in blacklist
      const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(authToken)
      expect(isBlacklisted).toBe(true)
    })

    test('should handle multiple tokens independently', async () => {
      const token1 = jwt.sign({ userId: 1, role: 'user' }, config.jwt.secret)
      const token2 = jwt.sign({ userId: 2, role: 'user' }, config.jwt.secret)

      // Blacklist only token1
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token1}`)

      // Token1 should be blacklisted
      let isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token1)
      expect(isBlacklisted).toBe(true)

      // Token2 should not be blacklisted
      isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token2)
      expect(isBlacklisted).toBe(false)
    })
  })

  describe('Admin Security Endpoints', () => {
    let adminToken: string

    beforeEach(() => {
      adminToken = jwt.sign(
        { userId: 999, role: 'admin' },
        config.jwt.secret,
        { expiresIn: '1h' }
      )
    })

    test('should get blacklist statistics for admin', async () => {
      // First blacklist a token to have some stats
      await TokenBlacklistService.blacklistToken(authToken)

      const response = await request(app)
        .get('/api/auth/admin/security/blacklisted-tokens')
        .set('Authorization', `Bearer ${adminToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('count')
      expect(response.body.data).toHaveProperty('memoryUsage')
      expect(response.body.data.count).toBeGreaterThanOrEqual(1)
    })

    test('should deny access to non-admin users for security endpoints', async () => {
      const response = await request(app)
        .get('/api/auth/admin/security/blacklisted-tokens')
        .set('Authorization', `Bearer ${authToken}`) // Regular user token

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Insufficient permissions')
    })

    test('should allow admin to cleanup tokens', async () => {
      const response = await request(app)
        .post('/api/auth/admin/security/cleanup-tokens')
        .set('Authorization', `Bearer ${adminToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Token cleanup completed')
    })

    test('should require authentication for admin endpoints', async () => {
      const response = await request(app)
        .get('/api/auth/admin/security/blacklisted-tokens')

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Authentication required')
    })
  })

  describe('Concurrent Token Operations', () => {
    test('should handle concurrent logout requests', async () => {
      const tokens = Array.from({ length: 5 }, (_, i) => 
        jwt.sign({ userId: i + 1, role: 'user' }, config.jwt.secret)
      )

      // Logout all tokens concurrently
      const logoutPromises = tokens.map(token =>
        request(app)
          .post('/api/auth/logout')
          .set('Authorization', `Bearer ${token}`)
      )

      const responses = await Promise.all(logoutPromises)

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      // All tokens should be blacklisted
      const blacklistChecks = await Promise.all(
        tokens.map(token => TokenBlacklistService.isTokenBlacklisted(token))
      )

      blacklistChecks.forEach(isBlacklisted => {
        expect(isBlacklisted).toBe(true)
      })
    })

    test('should handle concurrent authentication attempts', async () => {
      const { storage } = await import('../../server/storage')
      vi.mocked(storage.getUser).mockResolvedValue(testUser)

      // Make multiple concurrent requests with same token
      const authPromises = Array(10).fill(null).map(() =>
        request(app)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${authToken}`)
      )

      const responses = await Promise.all(authPromises)

      // All should succeed (token not blacklisted)
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
    })
  })

  describe('Error Handling and Edge Cases', () => {
    test('should handle cache service errors during authentication', async () => {
      // Mock cache service to throw error
      vi.spyOn(cacheService, 'exists').mockRejectedValue(new Error('Cache unavailable'))

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)

      // Should handle gracefully (behavior depends on implementation)
      expect(response.status).toBeGreaterThanOrEqual(400)
    })

    test('should handle malformed tokens in blacklist operations', async () => {
      const malformedToken = 'not.a.valid.jwt.token.format'

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${malformedToken}`)

      expect(response.status).toBe(401) // Should fail authentication
    })

    test('should handle very long tokens', async () => {
      // Create an extremely long but valid token
      const longPayload = {
        userId: 1,
        role: 'user',
        longData: 'a'.repeat(5000) // 5KB of data
      }
      const longToken = jwt.sign(longPayload, config.jwt.secret)

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${longToken}`)

      expect(response.status).toBe(200)

      // Should be able to blacklist long tokens
      const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(longToken)
      expect(isBlacklisted).toBe(true)
    })

    test('should handle expired tokens gracefully', async () => {
      const expiredToken = jwt.sign(
        { userId: 1, role: 'user' },
        config.jwt.secret,
        { expiresIn: '-1h' } // Expired 1 hour ago
      )

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${expiredToken}`)

      expect(response.status).toBe(401) // Should fail authentication
    })
  })

  describe('Memory and Performance', () => {
    test('should handle large number of blacklisted tokens efficiently', async () => {
      // Blacklist many tokens
      const tokens = Array.from({ length: 100 }, (_, i) => 
        jwt.sign({ userId: i, role: 'user' }, config.jwt.secret)
      )

      // Blacklist all tokens
      await Promise.all(
        tokens.map(token => TokenBlacklistService.blacklistToken(token))
      )

      // Verify all are blacklisted efficiently
      const start = Date.now()
      const blacklistChecks = await Promise.all(
        tokens.map(token => TokenBlacklistService.isTokenBlacklisted(token))
      )
      const duration = Date.now() - start

      // All should be blacklisted
      blacklistChecks.forEach(isBlacklisted => {
        expect(isBlacklisted).toBe(true)
      })

      // Should complete reasonably quickly (adjust threshold as needed)
      expect(duration).toBeLessThan(1000) // Less than 1 second
    })
  })
})