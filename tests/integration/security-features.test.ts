import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import express from 'express';

// Mock all dependencies before imports
const mockCacheService = {
  get: vi.fn(),
  set: vi.fn(),
  delete: vi.fn(),
  keys: vi.fn()
};

const mockDb = {
  select: vi.fn(() => ({
    from: vi.fn(() => ({
      where: vi.fn(() => ({
        limit: vi.fn(() => Promise.resolve([]))
      }))
    }))
  }))
};

vi.mock('../../server/services/CacheService', () => ({
  cacheService: mockCacheService,
  CacheService: {
    generateKey: (...args: any[]) => args.join(':'),
    getOrSet: vi.fn()
  }
}));

vi.mock('../../server/db', () => ({
  db: mockDb
}));

vi.mock('../../server/utils/logger', () => ({
  log: vi.fn()
}));

// Import after mocking
import { validateInput, phoneNumberSchema, emailSchema } from '../../server/utils/input-validation';
import { createRateLimitMiddleware, AdvancedRateLimiter } from '../../server/utils/rate-limiting';
import { OTPRateLimitService } from '../../server/services/OTPRateLimitService';

describe('Security Features Integration', () => {
  let app: express.Application;
  let rateLimiter: AdvancedRateLimiter;
  let otpRateLimitService: OTPRateLimitService;

  beforeAll(() => {
    // Setup test app
    app = express();
    app.use(express.json());
    
    rateLimiter = AdvancedRateLimiter.getInstance();
    otpRateLimitService = new OTPRateLimitService();
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Reset cache responses
    mockCacheService.get.mockResolvedValue(null);
    mockCacheService.set.mockResolvedValue(undefined);
    mockCacheService.delete.mockResolvedValue(true);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Input Validation Integration', () => {
    beforeEach(() => {
      // Setup validation endpoint
      app.post('/test/validate-user', validateInput(phoneNumberSchema.extend({
        email: emailSchema,
        name: phoneNumberSchema.optional()
      })), (req, res) => {
        res.json({ success: true, data: req.body });
      });
    });

    it('should validate and sanitize phone numbers', async () => {
      const response = await request(app)
        .post('/test/validate-user')
        .send({
          phoneNumber: '98765 43210', // With spaces
          email: '<EMAIL>', // Mixed case
        });

      expect(response.status).toBe(200);
      expect(response.body.data.phoneNumber).toBe('+919876543210');
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    it('should reject invalid input', async () => {
      const response = await request(app)
        .post('/test/validate-user')
        .send({
          phoneNumber: '123', // Too short
          email: 'invalid-email'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should handle SQL injection attempts', async () => {
      const response = await request(app)
        .post('/test/validate-user')
        .send({
          phoneNumber: "'; DROP TABLE users; --",
          email: '<EMAIL>'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should sanitize XSS attempts', async () => {
      const response = await request(app)
        .post('/test/validate-user')
        .send({
          phoneNumber: '9876543210',
          email: '<script>alert("xss")</script>@example.com'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting Integration', () => {
    beforeEach(() => {
      // Setup rate limited endpoints
      app.post('/test/rate-limited', 
        createRateLimitMiddleware('test-endpoint', {
          maxRequests: 3,
          windowMs: 60000
        }),
        (req, res) => {
          res.json({ success: true, message: 'Request processed' });
        }
      );

      app.post('/test/progressive-rate-limited',
        createRateLimitMiddleware('progressive-endpoint', {
          maxRequests: 2,
          windowMs: 60000,
          progressive: true,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          multiplier: 2
        }),
        (req, res) => {
          res.json({ success: true, message: 'Progressive request processed' });
        }
      );
    });

    it('should allow requests within limit', async () => {
      // Mock rate limit check to allow requests
      mockCacheService.get.mockResolvedValue(null);

      for (let i = 0; i < 3; i++) {
        const response = await request(app)
          .post('/test/rate-limited')
          .send({ test: `data-${i}` });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.headers['x-ratelimit-limit']).toBe('3');
      }
    });

    it('should block requests exceeding limit', async () => {
      // Mock rate limit exceeded scenario
      const now = Date.now();
      mockCacheService.get.mockResolvedValue({
        count: 4,
        resetTime: now + 30000,
        blocked: true,
        blockUntil: now + 10000
      });

      const response = await request(app)
        .post('/test/rate-limited')
        .send({ test: 'data' });

      expect(response.status).toBe(429);
      expect(response.body.error).toBe('Rate limit exceeded');
      expect(response.body.retryAfter).toBeDefined();
    });

    it('should handle progressive rate limiting', async () => {
      // First few requests should pass
      mockCacheService.get
        .mockResolvedValueOnce(null) // Rate limit check
        .mockResolvedValueOnce(0);   // Violation count

      const response1 = await request(app)
        .post('/test/progressive-rate-limited')
        .send({ test: 'data1' });

      expect(response1.status).toBe(200);

      // Simulate violation
      mockCacheService.get
        .mockResolvedValueOnce({
          count: 3,
          resetTime: Date.now() + 30000,
          blocked: false
        })
        .mockResolvedValueOnce(1); // Previous violations

      const response2 = await request(app)
        .post('/test/progressive-rate-limited')
        .send({ test: 'data2' });

      expect(response2.status).toBe(429);
    });

    it('should fail open on cache errors', async () => {
      mockCacheService.get.mockRejectedValue(new Error('Cache service unavailable'));

      const response = await request(app)
        .post('/test/rate-limited')
        .send({ test: 'data' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should include rate limit headers', async () => {
      mockCacheService.get.mockResolvedValue({
        count: 1,
        resetTime: Date.now() + 30000,
        blocked: false
      });

      const response = await request(app)
        .post('/test/rate-limited')
        .send({ test: 'data' });

      expect(response.headers['x-ratelimit-limit']).toBe('3');
      expect(response.headers['x-ratelimit-remaining']).toBeDefined();
      expect(response.headers['x-ratelimit-reset']).toBeDefined();
    });
  });

  describe('OTP Rate Limiting Integration', () => {
    it('should enforce OTP request limits', async () => {
      const identifier = '<EMAIL>';

      // First request should be allowed
      mockCacheService.get
        .mockResolvedValueOnce(50) // Daily count
        .mockResolvedValueOnce(null) // No existing attempts
        .mockResolvedValueOnce(null); // No last sent time

      const result1 = await otpRateLimitService.canRequestOTP(identifier);
      expect(result1.allowed).toBe(true);
      expect(result1.attemptsLeft).toBe(3);

      // Record the request
      await otpRateLimitService.recordOTPRequest(identifier);

      // Second request after cooldown should be blocked
      const now = Date.now();
      mockCacheService.get
        .mockResolvedValueOnce(51) // Daily count
        .mockResolvedValueOnce({
          count: 1,
          lastAttempt: now - 1000,
          blocked: false
        })
        .mockResolvedValueOnce(now - 30000); // Last sent 30 seconds ago

      const result2 = await otpRateLimitService.canRequestOTP(identifier);
      expect(result2.allowed).toBe(false);
      expect(result2.reason).toBe('Please wait before requesting another OTP');
      expect(result2.waitTime).toBeGreaterThan(0);
    });

    it('should enforce daily OTP limits', async () => {
      const identifier = '<EMAIL>';

      // Mock daily limit exceeded
      mockCacheService.get.mockResolvedValueOnce(100); // At daily limit

      const result = await otpRateLimitService.canRequestOTP(identifier);

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Daily OTP limit exceeded');
      expect(result.waitTime).toBeGreaterThan(0);
    });

    it('should enforce OTP verification limits', async () => {
      const identifier = '<EMAIL>';

      // Allow first few verifications
      mockCacheService.get.mockResolvedValue(null);

      const result1 = await otpRateLimitService.canVerifyOTP(identifier);
      expect(result1.allowed).toBe(true);
      expect(result1.attemptsLeft).toBe(5);

      // Mock too many verification attempts
      const now = Date.now();
      mockCacheService.get.mockResolvedValue({
        count: 6,
        lastAttempt: now - 1000,
        blocked: true,
        blockUntil: now + 5000
      });

      const result2 = await otpRateLimitService.canVerifyOTP(identifier);
      expect(result2.allowed).toBe(false);
      expect(result2.reason).toBe('Too many verification attempts');
      expect(result2.waitTime).toBe(5);
    });

    it('should clear verification attempts on success', async () => {
      const identifier = '<EMAIL>';

      await otpRateLimitService.recordOTPVerificationAttempt(identifier, true);

      expect(mockCacheService.delete).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify:<EMAIL>')
      );
    });

    it('should track failed verification attempts', async () => {
      const identifier = '<EMAIL>';

      mockCacheService.get.mockResolvedValue({
        count: 2,
        lastAttempt: Date.now() - 60000,
        blocked: false
      });

      await otpRateLimitService.recordOTPVerificationAttempt(identifier, false);

      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('otp:verify:<EMAIL>'),
        expect.objectContaining({
          count: 3,
          blocked: false
        }),
        300
      );
    });
  });

  describe('Security Headers Integration', () => {
    beforeEach(() => {
      // Setup endpoint with security headers
      app.use((req, res, next) => {
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        res.setHeader('Content-Security-Policy', "default-src 'self'");
        next();
      });

      app.get('/test/secure', (req, res) => {
        res.json({ secure: true });
      });
    });

    it('should include security headers in responses', async () => {
      const response = await request(app)
        .get('/test/secure');

      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBe('max-age=31536000; includeSubDomains');
      expect(response.headers['content-security-policy']).toBe("default-src 'self'");
    });
  });

  describe('Combined Security Scenarios', () => {
    beforeEach(() => {
      // Setup comprehensive security endpoint
      app.post('/test/secure-otp-request',
        createRateLimitMiddleware('otp-request', {
          maxRequests: 5,
          windowMs: 300000 // 5 minutes
        }),
        validateInput(phoneNumberSchema.extend({
          email: emailSchema
        })),
        async (req, res) => {
          const { phoneNumber, email } = req.body;
          
          // Check OTP rate limits
          const canRequest = await otpRateLimitService.canRequestOTP(email);
          if (!canRequest.allowed) {
            return res.status(429).json({
              success: false,
              error: canRequest.reason,
              waitTime: canRequest.waitTime
            });
          }

          // Simulate OTP sending
          await otpRateLimitService.recordOTPRequest(email);

          res.json({
            success: true,
            message: 'OTP sent successfully',
            attemptsLeft: canRequest.attemptsLeft
          });
        }
      );
    });

    it('should handle valid secure OTP request', async () => {
      // Mock successful rate limit checks
      mockCacheService.get.mockResolvedValue(null);

      const response = await request(app)
        .post('/test/secure-otp-request')
        .send({
          phoneNumber: '9876543210',
          email: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('OTP sent successfully');
    });

    it('should reject invalid input in secure endpoint', async () => {
      const response = await request(app)
        .post('/test/secure-otp-request')
        .send({
          phoneNumber: '123', // Invalid
          email: 'invalid-email'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should enforce rate limits before OTP limits', async () => {
      // Mock rate limit exceeded
      const now = Date.now();
      mockCacheService.get.mockResolvedValue({
        count: 6,
        resetTime: now + 30000,
        blocked: true,
        blockUntil: now + 10000
      });

      const response = await request(app)
        .post('/test/secure-otp-request')
        .send({
          phoneNumber: '9876543210',
          email: '<EMAIL>'
        });

      expect(response.status).toBe(429);
      expect(response.body.error).toBe('Rate limit exceeded');
    });

    it('should enforce OTP limits after passing rate limits', async () => {
      // Mock rate limit passed but OTP limit exceeded
      mockCacheService.get
        .mockResolvedValueOnce(null) // Rate limit check passes
        .mockResolvedValueOnce(100); // Daily OTP limit exceeded

      const response = await request(app)
        .post('/test/secure-otp-request')
        .send({
          phoneNumber: '9876543210',
          email: '<EMAIL>'
        });

      expect(response.status).toBe(429);
      expect(response.body.error).toBe('Daily OTP limit exceeded');
      expect(response.body.waitTime).toBeGreaterThan(0);
    });

    it('should handle concurrent requests safely', async () => {
      // Simulate concurrent requests
      mockCacheService.get.mockResolvedValue(null);

      const requests = Array(3).fill(null).map((_, i) =>
        request(app)
          .post('/test/secure-otp-request')
          .send({
            phoneNumber: '9876543210',
            email: `concurrent${i}@example.com`
          })
      );

      const responses = await Promise.all(requests);

      responses.forEach((response, i) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });
  });

  describe('Error Boundary Integration', () => {
    beforeEach(() => {
      // Setup endpoint that can trigger errors
      app.post('/test/error-prone', async (req, res) => {
        const { triggerError } = req.body;

        if (triggerError === 'cache') {
          mockCacheService.get.mockRejectedValueOnce(new Error('Cache service down'));
        } else if (triggerError === 'validation') {
          throw new Error('VALIDATION_ERROR: Invalid data format');
        } else if (triggerError === 'database') {
          throw new Error('terminating connection due to administrator command');
        }

        res.json({ success: true });
      });

      // Error boundary middleware
      app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
        console.error('Error caught by boundary:', error.message);

        let statusCode = 500;
        let message = 'Internal Server Error';

        if (error.message.includes('VALIDATION_ERROR')) {
          statusCode = 400;
          message = 'Validation Error';
        } else if (error.message.includes('terminating connection')) {
          statusCode = 503;
          message = 'Service Temporarily Unavailable';
        }

        res.status(statusCode).json({
          success: false,
          error: message,
          timestamp: new Date().toISOString()
        });
      });
    });

    it('should handle cache service errors gracefully', async () => {
      const response = await request(app)
        .post('/test/error-prone')
        .send({ triggerError: 'cache' });

      // Should still respond (fail open)
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should handle validation errors appropriately', async () => {
      const response = await request(app)
        .post('/test/error-prone')
        .send({ triggerError: 'validation' });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation Error');
      expect(response.body.timestamp).toBeDefined();
    });

    it('should handle database connection errors', async () => {
      const response = await request(app)
        .post('/test/error-prone')
        .send({ triggerError: 'database' });

      expect(response.status).toBe(503);
      expect(response.body.error).toBe('Service Temporarily Unavailable');
    });
  });

  describe('Performance Impact of Security Features', () => {
    it('should maintain performance with security layers', async () => {
      const startTime = Date.now();

      // Setup endpoint with all security features
      app.post('/test/performance-secure',
        createRateLimitMiddleware('perf-test', {
          maxRequests: 100,
          windowMs: 60000
        }),
        validateInput(phoneNumberSchema.extend({
          email: emailSchema
        })),
        (req, res) => {
          res.json({ success: true, processingTime: Date.now() - startTime });
        }
      );

      // Mock successful operations
      mockCacheService.get.mockResolvedValue(null);

      const response = await request(app)
        .post('/test/performance-secure')
        .send({
          phoneNumber: '9876543210',
          email: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.processingTime).toBeLessThan(1000); // Should be fast
    });

    it('should handle high concurrency with security features', async () => {
      // Setup concurrent endpoint
      app.post('/test/concurrent-secure',
        createRateLimitMiddleware('concurrent-test', {
          maxRequests: 50,
          windowMs: 60000
        }),
        (req, res) => {
          res.json({ success: true, requestId: req.body.id });
        }
      );

      mockCacheService.get.mockResolvedValue(null);

      // Simulate concurrent requests
      const concurrentRequests = Array(10).fill(null).map((_, i) =>
        request(app)
          .post('/test/concurrent-secure')
          .send({ id: i })
      );

      const responses = await Promise.all(concurrentRequests);

      responses.forEach((response, i) => {
        expect(response.status).toBe(200);
        expect(response.body.requestId).toBe(i);
      });
    });
  });

  describe('Security Configuration Edge Cases', () => {
    it('should handle malformed rate limit data', async () => {
      app.post('/test/malformed-data',
        createRateLimitMiddleware('malformed-test', {
          maxRequests: 5,
          windowMs: 60000
        }),
        (req, res) => {
          res.json({ success: true });
        }
      );

      // Mock malformed cache data
      mockCacheService.get.mockResolvedValue({
        invalidProperty: 'invalid',
        count: 'not-a-number'
      });

      const response = await request(app)
        .post('/test/malformed-data')
        .send({ test: 'data' });

      // Should handle gracefully and allow request
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should handle very large timestamps', async () => {
      const futureTime = Date.now() + (10 * 365 * 24 * 60 * 60 * 1000); // 10 years

      mockCacheService.get.mockResolvedValue({
        count: 1,
        resetTime: futureTime,
        blocked: false
      });

      const canRequest = await otpRateLimitService.canRequestOTP('<EMAIL>');

      expect(canRequest.allowed).toBe(true);
    });

    it('should handle missing cache data gracefully', async () => {
      mockCacheService.get.mockResolvedValue(undefined);

      const result = await otpRateLimitService.getRateLimitStatus('<EMAIL>');

      expect(result).toEqual({
        otpRequests: { count: 0, maxAttempts: 3, blocked: false },
        verifyAttempts: { count: 0, maxAttempts: 5, blocked: false },
        dailyCount: 0
      });
    });
  });
});