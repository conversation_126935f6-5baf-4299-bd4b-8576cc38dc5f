import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { db } from '../../server/db';
import { smsTemplates, smsLogs } from '../../shared/schema';
import { templateService } from '../../server/services/TemplateService';
import { dltSMSService } from '../../server/services/DLTSMSService';
import { eq } from 'drizzle-orm';

// Integration tests for SMS template workflow
describe('SMS Template Workflow Integration', () => {
  let testTemplateId: number;
  const testTemplateKey = 'test_booking_confirmation';

  beforeAll(async () => {
    // Setup test template
    const testTemplate = await templateService.createTemplate({
      key: testTemplateKey,
      name: 'Test Booking Confirmation',
      content: 'Your booking for {#var#} on {#var#} is confirmed. Total: Rs. {#var#}',
      dltTemplateId: 'TEST_DLT_ID_123',
      category: 'transactional',
      variables: ['property_name', 'booking_date', 'amount'],
      status: 'active'
    });
    testTemplateId = testTemplate.id;
  });

  afterAll(async () => {
    // Cleanup test data
    await db.delete(smsLogs).where(eq(smsLogs.templateId, testTemplateId));
    await db.delete(smsTemplates).where(eq(smsTemplates.id, testTemplateId));
  });

  beforeEach(async () => {
    // Clean up any logs before each test
    await db.delete(smsLogs).where(eq(smsLogs.templateId, testTemplateId));
  });

  describe('Template CRUD Operations', () => {
    it('should create, read, update, and delete templates', async () => {
      // Create
      const newTemplate = await templateService.createTemplate({
        key: 'test_crud_template',
        name: 'Test CRUD',
        content: 'Test {#var#}',
        dltTemplateId: 'CRUD_TEST_123',
        category: 'transactional',
        variables: ['name'],
        status: 'draft'
      });

      expect(newTemplate.id).toBeDefined();
      expect(newTemplate.key).toBe('test_crud_template');

      // Read
      const retrieved = await templateService.getTemplateById(newTemplate.id);
      expect(retrieved).toBeTruthy();
      expect(retrieved!.name).toBe('Test CRUD');

      // Update
      const updated = await templateService.updateTemplate(newTemplate.id, {
        name: 'Updated CRUD',
        status: 'active'
      });
      expect(updated!.name).toBe('Updated CRUD');
      expect(updated!.status).toBe('active');

      // Delete
      const deleted = await templateService.deleteTemplate(newTemplate.id);
      expect(deleted).toBe(true);

      // Verify deletion
      const notFound = await templateService.getTemplateById(newTemplate.id);
      expect(notFound).toBeNull();
    });

    it('should handle duplicate key creation', async () => {
      await expect(templateService.createTemplate({
        key: testTemplateKey, // Duplicate key
        name: 'Duplicate Test',
        content: 'Test',
        dltTemplateId: 'DUP123',
        category: 'transactional',
        variables: [],
        status: 'active'
      })).rejects.toThrow('Template key already exists');
    });
  });

  describe('Template Variable Processing', () => {
    it('should replace variables correctly in template content', async () => {
      const template = await templateService.getActiveTemplateByKey(testTemplateKey);
      expect(template).toBeTruthy();

      const variables = {
        property_name: 'Sunset Villa',
        booking_date: '2024-12-25',
        amount: '7500'
      };

      const message = await templateService.replaceTemplateVariables(template!, variables);
      expect(message).toBe('Your booking for Sunset Villa on 2024-12-25 is confirmed. Total: Rs. 7500');
    });

    it('should validate required variables', async () => {
      const template = await templateService.getActiveTemplateByKey(testTemplateKey);
      expect(template).toBeTruthy();

      const incompleteVariables = {
        property_name: 'Sunset Villa',
        booking_date: '2024-12-25'
        // missing amount
      };

      await expect(
        templateService.replaceTemplateVariables(template!, incompleteVariables)
      ).rejects.toThrow('Missing required variable: amount');
    });
  });

  describe('SMS Logging Integration', () => {
    it('should log SMS messages with template reference', async () => {
      const logData = {
        templateId: testTemplateId,
        recipientPhone: '+919876543210',
        messageContent: 'Test SMS message content',
        status: 'pending' as const
      };

      const smsLog = await templateService.logSmsMessage(logData);
      expect(smsLog.id).toBeDefined();
      expect(smsLog.templateId).toBe(testTemplateId);
      expect(smsLog.recipientPhone).toBe('+919876543210');
      expect(smsLog.status).toBe('pending');

      // Update status
      const updatedLog = await templateService.updateSmsLogStatus(
        smsLog.id, 
        'sent', 
        'MSG_TEST_123'
      );
      
      expect(updatedLog!.status).toBe('sent');
      expect(updatedLog!.twilioMessageSid).toBe('MSG_TEST_123');
    });

    it('should retrieve SMS logs by template', async () => {
      // Create multiple log entries
      const logPromises = Array.from({ length: 3 }, (_, i) => 
        templateService.logSmsMessage({
          templateId: testTemplateId,
          recipientPhone: `+9187654321${i}`,
          messageContent: `Test message ${i}`,
          status: 'sent'
        })
      );

      await Promise.all(logPromises);

      const logs = await templateService.getSmsLogsByTemplate(testTemplateId, 10);
      expect(logs.length).toBe(3);
      expect(logs.every(log => log.templateId === testTemplateId)).toBe(true);
    });

    it('should retrieve SMS logs by phone number', async () => {
      const testPhone = '+919999888777';
      
      // Create logs for specific phone number
      await templateService.logSmsMessage({
        templateId: testTemplateId,
        recipientPhone: testPhone,
        messageContent: 'First message',
        status: 'sent'
      });

      await templateService.logSmsMessage({
        templateId: testTemplateId,
        recipientPhone: testPhone,
        messageContent: 'Second message',
        status: 'delivered'
      });

      const logs = await templateService.getSmsLogsByPhone(testPhone);
      expect(logs.length).toBe(2);
      expect(logs.every(log => log.recipientPhone === testPhone)).toBe(true);
    });
  });

  describe('Template Analytics', () => {
    it('should generate template usage analytics', async () => {
      // Create some log entries with different statuses
      await templateService.logSmsMessage({
        templateId: testTemplateId,
        recipientPhone: '+919111222333',
        messageContent: 'Success message 1',
        status: 'sent'
      });

      await templateService.logSmsMessage({
        templateId: testTemplateId,
        recipientPhone: '+919111222444',
        messageContent: 'Success message 2', 
        status: 'sent'
      });

      await templateService.logSmsMessage({
        templateId: testTemplateId,
        recipientPhone: '+919111222555',
        messageContent: 'Failed message',
        status: 'failed'
      });

      const analytics = await templateService.getTemplateAnalytics(testTemplateId);
      expect(analytics.length).toBe(1);
      
      const templateStats = analytics[0];
      expect(templateStats.total_sends).toBe('3');
      expect(templateStats.successful_sends).toBe('2');
      expect(templateStats.failed_sends).toBe('1');
      expect(parseFloat(templateStats.success_rate)).toBeCloseTo(66.67, 1);
    });

    it('should get overall template usage stats', async () => {
      const stats = await templateService.getTemplateUsageStats(7); // Last 7 days
      expect(Array.isArray(stats)).toBe(true);
      // Stats structure depends on actual data, so we just verify it returns an array
    });
  });

  describe('DLT SMS Service Integration', () => {
    it('should retrieve and use database templates', async () => {
      const availableTemplates = await dltSMSService.getAvailableTemplates();
      expect(availableTemplates).toContain(testTemplateKey);

      const templateInfo = await dltSMSService.getTemplateInfo(testTemplateKey);
      expect(templateInfo).toBeTruthy();
      expect(templateInfo!.key).toBe(testTemplateKey);
      expect(templateInfo!.dltTemplateId).toBe('TEST_DLT_ID_123');
    });

    it('should validate templates during SMS sending', async () => {
      // Test with valid variables
      const validResult = await dltSMSService.sendSMS({
        to: '+919876543210',
        templateId: testTemplateKey,
        variables: {
          property_name: 'Green Valley Farm',
          booking_date: '2024-12-15',
          amount: '5000'
        }
      });

      // In development mode, this should succeed with fallback
      expect(validResult.success).toBe(true);
      expect(validResult.messageSid).toContain('dev-fallback');

      // Verify SMS was logged
      const logs = await templateService.getSmsLogsByTemplate(testTemplateId);
      expect(logs.length).toBeGreaterThan(0);
      
      const latestLog = logs[0];
      expect(latestLog.recipientPhone).toBe('+919876543210');
      expect(latestLog.messageContent).toContain('Green Valley Farm');
      expect(latestLog.status).toBe('sent');
    });

    it('should handle template validation failures', async () => {
      const invalidResult = await dltSMSService.sendSMS({
        to: '+919876543210',
        templateId: testTemplateKey,
        variables: {
          property_name: 'Green Valley Farm'
          // Missing required variables: booking_date, amount
        }
      });

      expect(invalidResult.success).toBe(false);
      expect(invalidResult.error).toContain('Missing required variables');
    });

    it('should handle non-existent template gracefully', async () => {
      const result = await dltSMSService.sendSMS({
        to: '+919876543210',
        templateId: 'non_existent_template',
        variables: {}
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found or not active');
    });
  });

  describe('Service Health Monitoring', () => {
    it('should report service health accurately', async () => {
      const health = await dltSMSService.getServiceHealth();
      
      expect(health).toBeDefined();
      expect(health.templatesLoaded).toBeGreaterThan(0);
      expect(health.availableTemplates).toContain(testTemplateKey);
      expect(typeof health.twilioConnected).toBe('boolean');
      expect(typeof health.dltConfigured).toBe('boolean');
    });

    it('should test connection when configured', async () => {
      const connectionTest = await dltSMSService.testConnection();
      expect(typeof connectionTest).toBe('boolean');
    });
  });

  describe('Template Status Management', () => {
    it('should handle active/inactive template states', async () => {
      // Deactivate template
      const deactivated = await templateService.deactivateTemplate(testTemplateId);
      expect(deactivated!.status).toBe('inactive');

      // Verify inactive template is not returned by getActiveTemplateByKey
      const inactiveTemplate = await templateService.getActiveTemplateByKey(testTemplateKey);
      expect(inactiveTemplate).toBeNull();

      // Reactivate template
      const reactivated = await templateService.activateTemplate(testTemplateId);
      expect(reactivated!.status).toBe('active');

      // Verify active template is now returned
      const activeTemplate = await templateService.getActiveTemplateByKey(testTemplateKey);
      expect(activeTemplate).toBeTruthy();
      expect(activeTemplate!.status).toBe('active');
    });
  });
});