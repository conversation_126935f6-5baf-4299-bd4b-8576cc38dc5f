import request from 'supertest'
import jwt from 'jsonwebtoken'
import { expect } from 'vitest'
import { storage } from '../../server/storage'
import { createUserData, createOwnerData } from '../fixtures/factories'

// JWT secret for testing
const TEST_JWT_SECRET = process.env.JWT_SECRET || 'test-jwt-secret-key-12345'

// Helper to create and insert test user
export async function createTestUser(userData?: any) {
  const user = await createUserData(userData)
  // Use raw password since storage.createUser will hash it
  const userWithRawPassword = {
    ...user,
    password: 'testpassword123!' // Use raw password, not hashed
  }
  const createdUser = await storage.createUser(userWithRawPassword)
  return createdUser
}

// Helper to create and insert test owner
export async function createTestOwner(userData?: any) {
  const owner = await createOwnerData(userData)
  // Use raw password since storage.createUser will hash it
  const ownerWithRawPassword = {
    ...owner,
    password: 'testpassword123!' // Use raw password, not hashed
  }
  const createdOwner = await storage.createUser(ownerWithRawPassword)
  return createdOwner
}

// Helper to generate JWT token for testing
export function generateTestToken(userId: number, role: string = 'user') {
  return jwt.sign(
    { userId, role, jti: 'test-token' },
    TEST_JWT_SECRET,
    { 
      expiresIn: '1h',
      audience: 'farmhouse-rental-app',
      issuer: 'farmhouse-rental-api'
    }
  )
}

// Helper to make authenticated requests
export function authenticatedRequest(app: any, token: string) {
  return {
    get: (path: string) => request(app).get(path).set('Authorization', `Bearer ${token}`),
    post: (path: string) => request(app).post(path).set('Authorization', `Bearer ${token}`),
    put: (path: string) => request(app).put(path).set('Authorization', `Bearer ${token}`),
    patch: (path: string) => request(app).patch(path).set('Authorization', `Bearer ${token}`),
    delete: (path: string) => request(app).delete(path).set('Authorization', `Bearer ${token}`),
  }
}

// Helper to create test property
export async function createTestProperty(ownerId: number, propertyData?: any) {
  const property = {
    ownerId,
    title: 'Test Farmhouse',
    description: 'A beautiful test farmhouse',
    location: 'Test Location',
    halfDayPrice: 150,
    fullDayPrice: 250,
    bedrooms: 3,
    bathrooms: 2,
    amenities: ['WiFi', 'Pool'],
    images: ['/uploads/test.jpg'],
    status: 'active',
    featured: false,
    ...propertyData
  }
  
  return await storage.createProperty(property)
}

// Helper to create test booking
export async function createTestBooking(propertyId: number, userId: number, bookingData?: any) {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  const booking = {
    propertyId,
    userId,
    bookingDate: tomorrow.toISOString().split('T')[0],
    bookingType: 'full_day' as const,
    guests: 4,
    totalPrice: 265,
    status: 'confirmed',
    ...bookingData
  }
  
  return await storage.createBooking(booking)
}

// Helper to create test review
export async function createTestReview(propertyId: number, userId: number, bookingId: number, reviewData?: any) {
  const review = {
    propertyId,
    userId,
    bookingId,
    rating: '5' as const,
    comment: 'Great place!',
    ...reviewData
  }
  
  return await storage.createReview(review)
}

// Helper to wait for async operations
export function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// Helper to validate response structure
export function expectValidUser(user: any) {
  expect(user).toHaveProperty('id')
  expect(user).toHaveProperty('email')
  expect(user).toHaveProperty('fullName')
  expect(user).toHaveProperty('role')
  expect(user).not.toHaveProperty('password') // Password should never be returned
  
  // Phone is optional for traditional registration users
  if (user.phone !== undefined) {
    expect(typeof user.phone).toBe('string')
  }
}

export function expectValidProperty(property: any) {
  expect(property).toHaveProperty('id')
  expect(property).toHaveProperty('title')
  expect(property).toHaveProperty('description')
  expect(property).toHaveProperty('location')
  expect(property).toHaveProperty('halfDayPrice')
  expect(property).toHaveProperty('fullDayPrice')
  expect(property).toHaveProperty('bedrooms')
  expect(property).toHaveProperty('bathrooms')
  expect(property).toHaveProperty('amenities')
  expect(property).toHaveProperty('images')
  expect(Array.isArray(property.amenities)).toBe(true)
  expect(Array.isArray(property.images)).toBe(true)
}

export function expectValidBooking(booking: any) {
  expect(booking).toHaveProperty('id')
  expect(booking).toHaveProperty('propertyId')
  expect(booking).toHaveProperty('userId')
  expect(booking).toHaveProperty('bookingDate')
  expect(booking).toHaveProperty('bookingType')
  expect(booking).toHaveProperty('guests')
  expect(booking).toHaveProperty('totalPrice')
  expect(booking).toHaveProperty('status')
}

export function expectValidReview(review: any) {
  expect(review).toHaveProperty('id')
  expect(review).toHaveProperty('propertyId')
  expect(review).toHaveProperty('userId')
  expect(review).toHaveProperty('rating')
  expect(review).toHaveProperty('comment')
  expect(['1', '2', '3', '4', '5']).toContain(review.rating)
}