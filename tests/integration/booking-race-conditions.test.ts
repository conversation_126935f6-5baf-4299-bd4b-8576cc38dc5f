import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '../../server';
import { db } from '../../server/db';
import { bookings, properties, users } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { generateToken } from '../integration/api-helpers';

describe('Booking Race Conditions', () => {
  let testUser: any;
  let testProperty: any;
  let authToken: string;
  const bookingDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow

  beforeEach(async () => {
    // Clean up test data
    await db.delete(bookings).where(eq(bookings.userId, 999));
    await db.delete(properties).where(eq(properties.ownerId, 999));
    await db.delete(users).where(eq(users.id, 999));

    // Create test user
    const [user] = await db.insert(users).values({
      id: 999,
      email: '<EMAIL>',
      phone: '+919999999999',
      isVerified: true,
      role: 'user'
    }).returning();
    testUser = user;

    // Create test property
    const [property] = await db.insert(properties).values({
      id: 999,
      name: 'Test Race Property',
      location: 'Test Location',
      fullDayPrice: 1000,
      halfDayPrice: 500,
      ownerId: 999,
      isActive: true
    }).returning();
    testProperty = property;

    authToken = generateToken(testUser);
  });

  afterEach(async () => {
    // Cleanup
    await db.delete(bookings).where(eq(bookings.userId, 999));
    await db.delete(properties).where(eq(properties.ownerId, 999));
    await db.delete(users).where(eq(users.id, 999));
  });

  it('should prevent duplicate bookings under high concurrency', async () => {
    const bookingData = {
      propertyId: testProperty.id,
      bookingDate: bookingDate.toISOString().split('T')[0],
      bookingType: 'full_day',
      guests: 4,
      totalPrice: 1000,
      specialRequests: 'Race condition test'
    };

    // Create 10 concurrent booking requests for the same slot
    const promises = Array(10).fill(null).map(() => 
      request(app)
        .post('/api/bookings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(bookingData)
    );

    const results = await Promise.allSettled(promises);
    
    // Count successful bookings
    const successful = results.filter(r => 
      r.status === 'fulfilled' && 
      (r.value as any).status >= 200 && 
      (r.value as any).status < 300
    );

    // Count conflict errors
    const conflicts = results.filter(r => 
      r.status === 'fulfilled' && 
      (r.value as any).status === 409
    );

    // Exactly one booking should succeed, others should get conflict errors
    expect(successful).toHaveLength(1);
    expect(conflicts.length).toBeGreaterThan(0);
    expect(successful.length + conflicts.length).toBe(10);

    // Verify only one booking exists in database
    const dbBookings = await db.select()
      .from(bookings)
      .where(eq(bookings.propertyId, testProperty.id));
    
    expect(dbBookings).toHaveLength(1);
  });

  it('should handle concurrent morning and full day booking conflicts', async () => {
    const morningBooking = {
      propertyId: testProperty.id,
      bookingDate: bookingDate.toISOString().split('T')[0],
      bookingType: 'morning',
      guests: 2,
      totalPrice: 500,
      specialRequests: 'Morning booking'
    };

    const fullDayBooking = {
      propertyId: testProperty.id,
      bookingDate: bookingDate.toISOString().split('T')[0],
      bookingType: 'full_day',
      guests: 4,
      totalPrice: 1000,
      specialRequests: 'Full day booking'
    };

    // Try to create morning and full day bookings concurrently
    const promises = [
      request(app)
        .post('/api/bookings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(morningBooking),
      
      request(app)
        .post('/api/bookings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(fullDayBooking)
    ];

    const results = await Promise.allSettled(promises);
    
    const successful = results.filter(r => 
      r.status === 'fulfilled' && 
      (r.value as any).status >= 200 && 
      (r.value as any).status < 300
    );

    // Only one should succeed due to conflict
    expect(successful).toHaveLength(1);

    // Verify only one booking exists
    const dbBookings = await db.select()
      .from(bookings)
      .where(eq(bookings.propertyId, testProperty.id));
    
    expect(dbBookings).toHaveLength(1);
  });

  it('should maintain data consistency during concurrent booking attempts', async () => {
    const bookingData = {
      propertyId: testProperty.id,
      bookingDate: bookingDate.toISOString().split('T')[0],
      bookingType: 'full_day',
      guests: 6,
      totalPrice: 1200,
      specialRequests: 'Consistency test'
    };

    // Create many concurrent requests
    const promises = Array(50).fill(null).map((_, index) => 
      request(app)
        .post('/api/bookings')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          ...bookingData,
          specialRequests: `Consistency test ${index}`
        })
    );

    await Promise.allSettled(promises);

    // Check database consistency
    const dbBookings = await db.select()
      .from(bookings)
      .where(eq(bookings.propertyId, testProperty.id));

    // Should have exactly one booking
    expect(dbBookings).toHaveLength(1);
    
    // Booking should have valid data
    const booking = dbBookings[0];
    expect(booking.propertyId).toBe(testProperty.id);
    expect(booking.userId).toBe(testUser.id);
    expect(booking.status).toBe('pending_payment');
    expect(booking.totalPrice).toBeGreaterThan(0);
  });
});