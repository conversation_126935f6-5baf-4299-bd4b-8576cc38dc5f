import { describe, test, expect, beforeEach, beforeAll, afterAll, vi } from 'vitest'
import request from 'supertest'
import express from 'express'
import { registerRoutes } from '../../server/routes'

let app: express.Application
let server: any

beforeAll(async () => {
  // Set up development environment
  process.env.NODE_ENV = 'development'
  
  app = express()
  app.use(express.json())
  app.use(express.urlencoded({ extended: true }))
  
  server = await registerRoutes(app)
})

afterAll(async () => {
  if (server) {
    server.close()
  }
  // Clean up environment
  delete process.env.NODE_ENV
})

describe('Development Authentication System', () => {
  beforeEach(() => {
    // Ensure we're in development mode for each test
    process.env.NODE_ENV = 'development'
  })

  describe('Test Phone Numbers', () => {
    const testNumbers = [
      '+919999999999',
      '+919000999888', 
      '+919391010188'
    ]

    testNumbers.forEach(phoneNumber => {
      test(`recognizes ${phoneNumber} as test number`, async () => {
        const response = await request(app)
          .post('/api/auth/otp/send-otp')
          .send({
            identifier: phoneNumber,
            type: 'sms'
          })

        expect(response.status).toBe(200)
        expect(response.body.message).toContain('Development OTP sent')
        expect(response.body.code || response.body.message).toContain('999999')
      })

      test(`allows master code 999999 for ${phoneNumber}`, async () => {
        // First send OTP
        await request(app)
          .post('/api/auth/otp/send-otp')
          .send({
            identifier: phoneNumber,
            type: 'sms'
          })

        // Then verify with master code
        const response = await request(app)
          .post('/api/auth/otp/verify-otp-login')
          .send({
            identifier: phoneNumber,
            code: '999999',
            type: 'sms'
          })

        expect(response.status).toBe(200)
        expect(response.body).toHaveProperty('user')
        expect(response.body).toHaveProperty('token')
      })
    })
  })

  describe('Master Code Functionality', () => {
    test('master code 999999 works for all test numbers', async () => {
      const testNumber = '+919999999999'
      
      // Send OTP
      const sendResponse = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      expect(sendResponse.status).toBe(200)

      // Verify with master code
      const verifyResponse = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      expect(verifyResponse.status).toBe(200)
      expect(verifyResponse.body.user.phone).toBe(testNumber)
    })

    test('master code does not work for non-test numbers in development', async () => {
      const regularNumber = '+911234567890'
      
      // Send OTP (this might fail or succeed depending on implementation)
      const sendResponse = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: regularNumber,
          type: 'sms'
        })

      // If OTP sending succeeds, master code should not work for regular numbers
      if (sendResponse.status === 200) {
        const verifyResponse = await request(app)
          .post('/api/auth/otp/verify-otp-login')
          .send({
            identifier: regularNumber,
            code: '999999',
            type: 'sms'
          })

        // Master code should not work for non-test numbers
        expect(verifyResponse.status).toBe(400)
        expect(verifyResponse.body.message).toContain('Invalid OTP')
      }
    })

    test('wrong OTP fails even for test numbers', async () => {
      const testNumber = '+919999999999'
      
      // Send OTP
      await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      // Try with wrong code
      const verifyResponse = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '123456',
          type: 'sms'
        })

      expect(verifyResponse.status).toBe(400)
      expect(verifyResponse.body.message).toContain('Invalid OTP')
    })
  })

  describe('Enhanced Rate Limits in Development', () => {
    test('allows higher rate limits for test numbers', async () => {
      const testNumber = '+919999999999'
      
      // Make multiple rapid requests (more than normal production limit)
      const promises = Array(15).fill(null).map(() =>
        request(app)
          .post('/api/auth/otp/send-otp')
          .send({
            identifier: testNumber,
            type: 'sms'
          })
      )

      const responses = await Promise.all(promises)
      
      // Should allow more requests in development
      const successfulRequests = responses.filter(res => res.status === 200)
      expect(successfulRequests.length).toBeGreaterThan(5) // More than production limit
    })

    test('still enforces some rate limiting', async () => {
      const testNumber = '+919999999999'
      
      // Make many rapid requests to eventually hit limit
      const promises = Array(60).fill(null).map(() =>
        request(app)
          .post('/api/auth/otp/send-otp')
          .send({
            identifier: testNumber,
            type: 'sms'
          })
      )

      const responses = await Promise.all(promises)
      
      // Should eventually hit rate limit
      const rateLimitedResponses = responses.filter(res => res.status === 429)
      expect(rateLimitedResponses.length).toBeGreaterThan(0)
    })
  })

  describe('Environment Detection', () => {
    test('automatically detects development environment', async () => {
      const testNumber = '+919999999999'
      
      const response = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      expect(response.status).toBe(200)
      expect(response.body.message).toContain('Development')
    })

    test('development features only work in development mode', async () => {
      // Temporarily set to production
      process.env.NODE_ENV = 'production'
      
      const testNumber = '+919999999999'
      
      const response = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      // Should not mention development or provide master code
      if (response.status === 200) {
        expect(response.body.message).not.toContain('Development')
        expect(response.body.message).not.toContain('999999')
      }
      
      // Reset to development
      process.env.NODE_ENV = 'development'
    })
  })

  describe('SMS Cost Savings', () => {
    test('no actual SMS sent for test numbers', async () => {
      const testNumber = '+919999999999'
      
      const response = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      expect(response.status).toBe(200)
      expect(response.body.message).toContain('Development')
      // In real implementation, this would verify no Twilio API call was made
    })

    test('provides master code directly in response', async () => {
      const testNumber = '+919999999999'
      
      const response = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      expect(response.status).toBe(200)
      // Master code should be mentioned in response for convenience
      expect(response.body.message || response.body.code).toContain('999999')
    })
  })

  describe('User Registration Flow', () => {
    test('complete registration flow with test number', async () => {
      const testNumber = '+919999999999'
      
      // Step 1: Send OTP
      const otpResponse = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      expect(otpResponse.status).toBe(200)

      // Step 2: Verify OTP with master code
      const verifyResponse = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      expect(verifyResponse.status).toBe(200)
      expect(verifyResponse.body).toHaveProperty('user')
      expect(verifyResponse.body.user.phone).toBe(testNumber)

      // Step 3: Complete profile if needed
      if (verifyResponse.body.needsProfileCompletion) {
        const profileResponse = await request(app)
          .put('/api/auth/complete-profile')
          .set('Authorization', `Bearer ${verifyResponse.body.token}`)
          .send({
            fullName: 'Test User',
            username: 'testuser123'
          })

        expect([200, 404]).toContain(profileResponse.status) // 404 if endpoint doesn't exist
      }
    })

    test('handles returning users with test numbers', async () => {
      const testNumber = '+919000999888'
      
      // Register user first
      await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      const firstLogin = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      expect(firstLogin.status).toBe(200)
      const userId = firstLogin.body.user.id

      // Login again with same number
      await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      const secondLogin = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      expect(secondLogin.status).toBe(200)
      expect(secondLogin.body.user.id).toBe(userId) // Same user
    })
  })

  describe('Error Handling', () => {
    test('handles invalid phone number format', async () => {
      const invalidNumber = '123456789'
      
      const response = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: invalidNumber,
          type: 'sms'
        })

      expect(response.status).toBe(400)
      expect(response.body.message).toContain('Invalid phone number')
    })

    test('handles missing identifier', async () => {
      const response = await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          type: 'sms'
        })

      expect(response.status).toBe(400)
      expect(response.body.message).toContain('identifier')
    })

    test('handles OTP verification without sending first', async () => {
      const testNumber = '+919999999999'
      
      const response = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      // May succeed if OTP session exists, or fail appropriately
      expect([200, 400]).toContain(response.status)
      if (response.status === 400) {
        expect(response.body.message).toContain('OTP')
      }
    })
  })

  describe('Integration with Existing Auth', () => {
    test('OTP login creates JWT token compatible with existing auth', async () => {
      const testNumber = '+919999999999'
      
      // Login via OTP
      await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      const loginResponse = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      expect(loginResponse.status).toBe(200)
      const token = loginResponse.body.token

      // Use token to access protected endpoint
      const meResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)

      expect(meResponse.status).toBe(200)
      expect(meResponse.body.phone).toBe(testNumber)
    })

    test('OTP login sets authentication cookies', async () => {
      const testNumber = '+919999999999'
      
      await request(app)
        .post('/api/auth/otp/send-otp')
        .send({
          identifier: testNumber,
          type: 'sms'
        })

      const loginResponse = await request(app)
        .post('/api/auth/otp/verify-otp-login')
        .send({
          identifier: testNumber,
          code: '999999',
          type: 'sms'
        })

      expect(loginResponse.status).toBe(200)
      expect(loginResponse.headers['set-cookie']).toBeDefined()
      
      const cookies = loginResponse.headers['set-cookie']
      const authCookie = cookies.find((cookie: string) => 
        cookie.startsWith('auth_token=')
      )
      expect(authCookie).toBeDefined()
    })
  })
})