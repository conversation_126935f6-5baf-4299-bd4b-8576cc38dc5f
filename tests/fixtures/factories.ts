import bcrypt from 'bcrypt'
import type { User, Property, Booking, Review } from '../../shared/schema'

// User factory
export async function createUserData(overrides: Partial<User> = {}): Promise<Omit<User, 'id' | 'createdAt'>> {
  const hashedPassword = await bcrypt.hash('testpassword123!', 10)
  
  return {
    username: 'testuser',
    password: hashedPassword,
    email: '<EMAIL>',
    fullName: 'Test User',
    phone: '+1234567890',
    address: '123 Test Street, Test City',
    bio: 'Test user bio',
    role: 'user',
    termsAccepted: true,
    privacyPolicyAccepted: true,
    cookiePolicyAccepted: true,
    dataProcessingConsent: true,
    marketingConsent: false,
    consentTimestamp: new Date(),
    ...overrides,
  }
}

export async function createOwnerData(overrides: Partial<User> = {}): Promise<Omit<User, 'id' | 'createdAt'>> {
  return createUserData({
    username: 'testowner',
    email: '<EMAIL>',
    fullName: 'Test Owner',
    role: 'owner',
    ...overrides,
  })
}

// Property factory
export function createPropertyData(overrides: Partial<Property> = {}): Omit<Property, 'id' | 'createdAt'> {
  return {
    ownerId: 1,
    title: 'Beautiful Test Farmhouse',
    description: 'A stunning farmhouse perfect for relaxation with beautiful views and modern amenities.',
    location: 'Napa Valley, California',
    halfDayPrice: 150,
    fullDayPrice: 250,
    bedrooms: 3,
    bathrooms: 2,
    amenities: ['WiFi', 'Pool', 'Kitchen', 'Parking', 'Garden'],
    images: ['/uploads/test-farmhouse1.jpg', '/uploads/test-farmhouse2.jpg'],
    status: 'active',
    featured: false,
    ...overrides,
  }
}

// Booking factory
export function createBookingData(overrides: Partial<Booking> = {}): Omit<Booking, 'id' | 'createdAt'> {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  return {
    propertyId: 1,
    userId: 1,
    bookingDate: tomorrow.toISOString().split('T')[0],
    bookingType: 'full_day',
    guests: 4,
    totalPrice: 265, // 250 + 15 cleaning fee
    status: 'confirmed',
    ...overrides,
  }
}

// Review factory
export function createReviewData(overrides: Partial<Review> = {}): Omit<Review, 'id' | 'createdAt' | 'updatedAt'> {
  return {
    propertyId: 1,
    userId: 1,
    bookingId: 1,
    rating: '5',
    comment: 'Amazing farmhouse! Perfect for a weekend getaway. Highly recommend to anyone looking for a peaceful retreat.',
    response: null,
    ...overrides,
  }
}

// API response factories for mocking
export function createUserResponse(user: Partial<User> = {}) {
  return {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    fullName: 'Test User',
    role: 'user',
    ...user,
  }
}

export function createPropertyResponse(property: Partial<Property> = {}) {
  return {
    id: 1,
    ownerId: 1,
    title: 'Beautiful Test Farmhouse',
    description: 'A stunning farmhouse perfect for relaxation',
    location: 'Napa Valley, California',
    halfDayPrice: 150,
    fullDayPrice: 250,
    bedrooms: 3,
    bathrooms: 2,
    amenities: ['WiFi', 'Pool', 'Kitchen'],
    images: ['/uploads/test-farmhouse1.jpg'],
    status: 'active',
    featured: false,
    createdAt: new Date().toISOString(),
    ...property,
  }
}

export function createBookingResponse(booking: Partial<Booking> = {}) {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  return {
    id: 1,
    propertyId: 1,
    userId: 1,
    bookingDate: tomorrow.toISOString().split('T')[0],
    bookingType: 'full_day' as const,
    guests: 4,
    totalPrice: 265,
    status: 'confirmed',
    createdAt: new Date().toISOString(),
    ...booking,
  }
}

export function createReviewResponse(review: Partial<Review> = {}) {
  return {
    id: 1,
    propertyId: 1,
    userId: 1,
    bookingId: 1,
    rating: '5' as const,
    comment: 'Amazing farmhouse! Highly recommend.',
    response: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...review,
  }
}