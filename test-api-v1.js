/**
 * Simple test script to verify the new v1 API endpoints work correctly
 */

const BASE_URL = 'http://localhost:5000';

// Test function to make API calls
async function testApiEndpoint(method, endpoint, data = null, token = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`${method} ${endpoint}:`, {
      status: response.status,
      success: result.success !== undefined ? result.success : response.ok,
      message: result.message || result.error || 'No message',
      hasData: !!result.data
    });
    
    return { response, result };
  } catch (error) {
    console.error(`${method} ${endpoint} ERROR:`, error.message);
    return { error };
  }
}

async function runApiTests() {
  console.log('🧪 Testing API v1 Endpoints\n');

  // Test 1: Health check
  console.log('1. Testing health endpoints:');
  await testApiEndpoint('GET', '/api/health');
  await testApiEndpoint('GET', '/api/v1/health');

  // Test 2: Properties endpoints
  console.log('\n2. Testing properties endpoints:');
  await testApiEndpoint('GET', '/api/properties');
  await testApiEndpoint('GET', '/api/v1/properties');
  
  // Test 3: Legacy vs v1 auth endpoints
  console.log('\n3. Testing auth endpoints (should fail without credentials):');
  await testApiEndpoint('GET', '/api/auth/me');
  await testApiEndpoint('GET', '/api/v1/users/me');
  
  // Test 4: Test new user endpoints structure
  console.log('\n4. Testing new user endpoints structure:');
  await testApiEndpoint('GET', '/api/v1/users/me/properties');
  await testApiEndpoint('GET', '/api/v1/users/me/bookings');
  await testApiEndpoint('GET', '/api/v1/users/me/reviews');

  console.log('\n✅ API v1 endpoint tests completed');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runApiTests().catch(console.error);
}

module.exports = { testApiEndpoint, runApiTests };