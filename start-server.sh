#!/bin/bash

# Kill any existing server processes
pkill -f "tsx server" 2>/dev/null || true
pkill -f "node.*server" 2>/dev/null || true

# Wait for cleanup
sleep 2

# Start the server in background with proper logging
echo "Starting development server..."
NODE_ENV=development nohup tsx server/index.ts > server.log 2>&1 &
SERVER_PID=$!

# Save the PID
echo $SERVER_PID > server.pid

# Wait and check if server started successfully
sleep 5
if curl -s http://localhost:5000/api/health > /dev/null 2>&1; then
    echo "✅ Server started successfully (PID: $SERVER_PID)"
    echo "🌐 Application available at: http://localhost:5000"
else
    echo "❌ Server failed to start"
    exit 1
fi