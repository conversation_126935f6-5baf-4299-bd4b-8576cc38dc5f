{"include": ["client/src/**/*", "shared/**/*", "server/**/*", "types/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "strictNullChecks": true, "strictPropertyInitialization": true, "strictBindCallApply": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "downlevelIteration": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"]}}}