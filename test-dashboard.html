<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard</title>
</head>
<body>
    <h1>Testing Dashboard Access</h1>
    <script>
        // Test if we can access the server
        fetch('/api/health')
            .then(response => response.json())
            .then(data => {
                console.log('Server health check:', data);
                document.body.innerHTML += '<p>✅ Server is accessible</p>';
            })
            .catch(error => {
                console.error('Server health check failed:', error);
                document.body.innerHTML += '<p>❌ Server is not accessible</p>';
            });

        // Test if we can access owner dashboard route
        fetch('/owner/dashboard')
            .then(response => response.text())
            .then(html => {
                console.log('Owner dashboard HTML length:', html.length);
                if (html.includes('id="root"')) {
                    document.body.innerHTML += '<p>✅ Owner dashboard route returns React app</p>';
                } else {
                    document.body.innerHTML += '<p>❌ Owner dashboard route does not return React app</p>';
                }
            })
            .catch(error => {
                console.error('Owner dashboard test failed:', error);
                document.body.innerHTML += '<p>❌ Owner dashboard route failed</p>';
            });

        // Test auth endpoint
        fetch('/api/auth/me')
            .then(response => response.json())
            .then(data => {
                console.log('Auth check:', data);
                if (data.error && data.error.code === 'AUTHENTICATION_ERROR') {
                    document.body.innerHTML += '<p>✅ Auth endpoint working (returns auth error as expected)</p>';
                } else {
                    document.body.innerHTML += '<p>❓ Auth endpoint returned unexpected response</p>';
                }
            })
            .catch(error => {
                console.error('Auth check failed:', error);
                document.body.innerHTML += '<p>❌ Auth endpoint failed</p>';
            });
    </script>
</body>
</html>