#!/bin/bash

# Farmhouse Application Startup Script
# This script handles all networking issues and starts the server reliably

echo "==============================================="
echo "🏡 FARMHOUSE APPLICATION STARTUP"
echo "==============================================="
echo

cd /mnt/c/Users/<USER>/Documents/Farmhouse

# Kill any existing processes
echo "🔧 Cleaning up existing processes..."
pkill -f "simple-server" 2>/dev/null || true
pkill -f "node.*5000" 2>/dev/null || true

# Wait for processes to fully terminate
sleep 2

# Check if port is available
if ss -tlnp | grep -q :5000; then
    echo "❌ Port 5000 is still in use. Waiting..."
    sleep 5
fi

# Start the server with proper error handling
echo "🚀 Starting Farmhouse server..."
echo "📂 Serving from: $(pwd)/dist/public"
echo "🌐 YouTube video features are ready!"
echo

# Start server in background with logging
nohup node simple-server.mjs > server.log 2>&1 &
SERVER_PID=$!

# Wait for server to start
sleep 3

# Check if server started successfully
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ Server started successfully!"
    echo "🆔 Process ID: $SERVER_PID"
    echo
    echo "==============================================="
    echo "🌐 ACCESS YOUR WEBSITE:"
    echo "==============================================="
    echo "📍 Primary URL: http://localhost:5000"
    echo "📍 Alternative: http://127.0.0.1:5000"
    echo "📱 API Test: http://localhost:5000/api/test"
    echo "❤️ Health Check: http://localhost:5000/health"
    echo
    echo "==============================================="
    echo "🎬 FEATURES AVAILABLE:"
    echo "==============================================="
    echo "📺 YouTube Video Upload"
    echo "📁 File Upload (Images & Videos)"
    echo "🏠 Property Management"
    echo "📊 Owner Dashboard"
    echo "💾 Media Management"
    echo
    echo "==============================================="
    echo "🛠️ WINDOWS USERS:"
    echo "==============================================="
    echo "If you can't access the website:"
    echo "1. Double-click 'CLICK-TO-FIX-WSL.bat'"
    echo "2. Run it as Administrator"
    echo "3. Wait 10 seconds and try again"
    echo
    echo "Server is running! Check server.log for details."
else
    echo "❌ Server failed to start!"
    echo "📝 Check server.log for errors:"
    cat server.log
    exit 1
fi