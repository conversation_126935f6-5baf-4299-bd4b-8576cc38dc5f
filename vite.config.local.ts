import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";

export default defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(path.dirname(import.meta.url.replace('file://', '')), "client", "src"),
      "@shared": path.resolve(path.dirname(import.meta.url.replace('file://', '')), "shared"),
      "@assets": path.resolve(path.dirname(import.meta.url.replace('file://', '')), "attached_assets"),
    },
  },
  root: path.resolve(path.dirname(import.meta.url.replace('file://', '')), "client"),
  build: {
    outDir: path.resolve(path.dirname(import.meta.url.replace('file://', '')), "dist/public"),
    emptyOutDir: true,
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    allowedHosts: "all",
    hmr: {
      port: 5173,
    },
  },
});