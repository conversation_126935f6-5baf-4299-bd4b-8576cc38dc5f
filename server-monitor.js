#!/usr/bin/env node

/**
 * Robust Server Monitor for Farmhouse Rental Platform
 * 
 * This script ensures the server stays running continuously without interruptions.
 * It automatically restarts the server if it crashes and handles port conflicts.
 */

const { spawn } = require('child_process');
const { execSync } = require('child_process');

class ServerMonitor {
  constructor() {
    this.serverProcess = null;
    this.restartAttempts = 0;
    this.maxRestartAttempts = 10;
    this.isShuttingDown = false;
    this.port = 5000;
  }

  log(message) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${message}`);
  }

  error(message) {
    const timestamp = new Date().toLocaleTimeString();
    console.error(`[${timestamp}] ERROR: ${message}`);
  }

  // Kill any existing processes on port 5000
  cleanupPort() {
    try {
      this.log('Cleaning up existing processes on port 5000...');
      
      // Kill tsx processes
      try {
        execSync('pkill -f "tsx server/index.ts"', { stdio: 'ignore' });
      } catch (e) {
        // Process might not exist
      }

      // Kill any node processes on port 5000
      try {
        execSync('pkill -f "node.*5000"', { stdio: 'ignore' });
      } catch (e) {
        // Process might not exist
      }

      // Force kill anything using port 5000
      try {
        execSync('fuser -k 5000/tcp', { stdio: 'ignore' });
      } catch (e) {
        // Port might not be in use
      }

      // Wait for cleanup
      setTimeout(() => {
        this.log('Port cleanup completed');
      }, 1000);

    } catch (error) {
      this.log('Port cleanup completed (some steps failed, but continuing...)');
    }
  }

  // Start the server process
  startServer() {
    if (this.isShuttingDown) {
      return;
    }

    this.log('Starting server process...');
    
    // Clean up any existing processes first
    this.cleanupPort();

    // Wait a moment for cleanup to complete
    setTimeout(() => {
      // Start the server in development mode for stability
      this.serverProcess = spawn('tsx', ['server/index.ts'], {
        env: {
          ...process.env,
          NODE_ENV: 'development'
        },
        stdio: ['inherit', 'inherit', 'inherit']
      });

      this.serverProcess.on('exit', (code, signal) => {
        if (this.isShuttingDown) {
          this.log('Server shutdown complete');
          return;
        }

        this.error(`Server process exited with code ${code} and signal ${signal}`);
        this.handleServerCrash();
      });

      this.serverProcess.on('error', (error) => {
        this.error(`Server process error: ${error.message}`);
        this.handleServerCrash();
      });

      this.log('Server process started successfully');
      this.restartAttempts = 0; // Reset counter on successful start

    }, 2000);
  }

  // Handle server crashes and restarts
  handleServerCrash() {
    if (this.isShuttingDown) {
      return;
    }

    this.restartAttempts++;
    
    if (this.restartAttempts >= this.maxRestartAttempts) {
      this.error(`Maximum restart attempts (${this.maxRestartAttempts}) reached. Stopping monitor.`);
      process.exit(1);
    }

    const restartDelay = Math.min(5000 * this.restartAttempts, 30000); // Exponential backoff, max 30 seconds
    this.log(`Restarting server in ${restartDelay}ms (attempt ${this.restartAttempts}/${this.maxRestartAttempts})`);

    setTimeout(() => {
      this.startServer();
    }, restartDelay);
  }

  // Graceful shutdown
  shutdown() {
    this.isShuttingDown = true;
    this.log('Initiating graceful shutdown...');

    if (this.serverProcess) {
      this.serverProcess.kill('SIGTERM');
      
      // Force kill after 10 seconds if it doesn't shutdown gracefully
      setTimeout(() => {
        if (this.serverProcess && !this.serverProcess.killed) {
          this.log('Force killing server process...');
          this.serverProcess.kill('SIGKILL');
        }
        process.exit(0);
      }, 10000);
    } else {
      process.exit(0);
    }
  }

  // Start monitoring
  start() {
    this.log('🚀 Starting Farmhouse Rental Server Monitor');
    this.log('🔧 This will ensure your app runs continuously without interruptions');

    // Handle shutdown signals
    process.on('SIGTERM', () => this.shutdown());
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGHUP', () => this.shutdown());

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.error(`Uncaught exception in monitor: ${error.message}`);
      // Don't exit, just log and continue
    });

    process.on('unhandledRejection', (reason, promise) => {
      this.error(`Unhandled rejection in monitor: ${reason}`);
      // Don't exit, just log and continue
    });

    // Start the server
    this.startServer();

    // Health check every 30 seconds
    setInterval(() => {
      if (!this.isShuttingDown && this.serverProcess && !this.serverProcess.killed) {
        this.log('✅ Server is running healthy');
      }
    }, 30000);
  }
}

// Start the monitor
const monitor = new ServerMonitor();
monitor.start();