#!/bin/bash

# Stable Server Startup Script
# This script ensures the farmhouse rental app runs continuously without interruptions

echo "🚀 Starting Stable Farmhouse Rental Server..."

# Kill any existing processes on port 5000
echo "🧹 Cleaning up existing processes..."
pkill -f "tsx server/index.ts" 2>/dev/null || true
pkill -f "node.*5000" 2>/dev/null || true
fuser -k 5000/tcp 2>/dev/null || true

# Wait a moment for cleanup
sleep 2

# Ensure no processes are still using the port
if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Port 5000 still in use, forcing cleanup..."
    kill -9 $(lsof -Pi :5000 -sTCP:LISTEN -t) 2>/dev/null || true
    sleep 2
fi

# Start the server in development mode (more stable than production builds)
echo "🌱 Starting server in development mode for maximum stability..."
export NODE_ENV=development

# Use exec to replace the shell process with the server process
# This ensures the server stays running and handles signals properly
exec tsx server/index.ts