# Test Direct Owner Messaging with Your Phone

## 🎯 Quick Test Setup

### Step 1: Use Your Phone as <PERSON><PERSON>'s WhatsApp

1. **Temporarily update** <PERSON><PERSON>'s WhatsApp number to your phone number:

```bash
npx tsx -e "
import dotenv from 'dotenv';
dotenv.config({ path: '.env.development' });
import { db } from './server/db';
import { users } from './shared/schema';
import { eq } from 'drizzle-orm';

(async () => {
  await db.update(users)
    .set({ whatsappNumber: 'YOUR_PHONE_NUMBER' }) // Replace with your number
    .where(eq(users.email, '<EMAIL>'));
  console.log('Updated Ayesha WhatsApp to your number');
})();
"
```

### Step 2: Set up Twilio with YOUR number

1. Go to Twilio Console
2. Buy a WhatsApp number OR use sandbox
3. Set webhook to: `https://your-ngrok-url.ngrok.io/api/whatsapp/webhook`
4. Connect YOUR phone number to receive messages

### Step 3: Test the Flow

1. **Friend messages your Twilio number** saying: "Hi, I want to book MM farm"
2. **Your system** identifies you as Ayesha
3. **Responds** with MM farm property details
4. **Friend continues** booking flow
5. **You both get** confirmation messages

## 🔄 Alternative: Manual WhatsApp Test

### What You Need:
- Your phone with WhatsApp
- A friend's phone
- ngrok running
- Your server running

### The Test:
1. **Give your friend** the test commands
2. **They use a web tool** to send HTTP requests to your webhook
3. **Simulate** them messaging your WhatsApp
4. **See responses** in your server logs

### Commands for Friend:
```bash
# Step 1: Initial contact
curl -X POST https://your-ngrok-url.ngrok.io/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "Body": "Hi, I want to book your farmhouse",
    "From": "whatsapp:+************",
    "To": "whatsapp:+YOUR_PHONE_NUMBER"
  }'

# Step 2: Select property
curl -X POST https://your-ngrok-url.ngrok.io/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "Body": "1",
    "From": "whatsapp:+************", 
    "To": "whatsapp:+YOUR_PHONE_NUMBER"
  }'
```

## 📱 Real-World Setup (For Production)

### For Each Owner:
1. **WhatsApp Business API** account
2. **Verify business** with Facebook/Meta
3. **Get API access** (paid service ~$10-50/month)
4. **Connect webhook** to your server
5. **Customers message directly**

### Benefits:
- ✅ Customers message real owner numbers
- ✅ Owners get notifications on their phones
- ✅ Automatic booking system
- ✅ No third-party numbers needed

## 🎮 Easiest Test Right Now

Replace `YOUR_PHONE_NUMBER` with your actual number and run:

```bash
# Update database
npx tsx -e "
import dotenv from 'dotenv';
dotenv.config({ path: '.env.development' });
import { db } from './server/db';
import { users } from './shared/schema';
import { eq } from 'drizzle-orm';

(async () => {
  await db.update(users)
    .set({ whatsappNumber: '************' }) // Your number here
    .where(eq(users.email, '<EMAIL>'));
  console.log('Updated');
})();
"

# Test booking
curl -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "Body": "Hi, I want to book MM farm",
    "From": "whatsapp:+************",
    "To": "whatsapp:+************"
  }'
```

This will show you exactly how customers would book by messaging the owner directly!