services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: farmhouse-db
    environment:
      POSTGRES_DB: farmhouse_rental
      POSTGRES_USER: farmhouse_user
      POSTGRES_PASSWORD: farmhouse_password
    ports:
      - "5432:5432"
    volumes:
      - farmhouse_db_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U farmhouse_user -d farmhouse_rental"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Farmhouse Application (Frontend + Backend)
  app:
    build: .
    container_name: farmhouse-app
    ports:
      - "3001:5000"
    env_file:
      - .env
    depends_on:
      database:
        condition: service_healthy
    volumes:
      # Mount uploads directory for local file storage
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:5000/api/properties"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Adminer for database management
  adminer:
    image: adminer:latest
    container_name: farmhouse-adminer
    ports:
      - "8080:8080"
    depends_on:
      - database
    environment:
      ADMINER_DEFAULT_SERVER: database

volumes:
  farmhouse_db_data: