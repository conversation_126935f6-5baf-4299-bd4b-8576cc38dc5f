import { defineConfig } from "drizzle-kit";
import { config as loadEnv } from 'dotenv';
import { resolve } from 'path';
import { existsSync } from 'fs';

// Load environment variables in the same order as the server
const nodeEnv = process.env.NODE_ENV || 'development';
const envFiles = [
  `.env.${nodeEnv}`,
  '.env.local',
  '.env'
];

envFiles.forEach((envFile, index) => {
  const envPath = resolve(process.cwd(), envFile);
  if (existsSync(envPath)) {
    // Use override for .env.development to ensure it takes priority
    const shouldOverride = index === 0 && envFile.includes('.env.development');
    loadEnv({ path: envPath, override: shouldOverride });
  }
});

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL, ensure the database is provisioned");
}

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
});
