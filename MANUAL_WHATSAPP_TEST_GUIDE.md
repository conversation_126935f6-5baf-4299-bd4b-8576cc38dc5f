# 📱 Manual WhatsApp Booking Test Guide

## 🎯 Quick Start - Manual Testing

Since you want to test manually, here's the complete step-by-step guide to test the WhatsApp booking integration:

## 🔧 **Step 1: Start the Server**

```bash
# Start the development server
npm run dev

# Server should start on http://localhost:3000
# WhatsApp webhook will be available at http://localhost:3000/api/whatsapp/webhook
```

## 🌐 **Step 2: Expose Server via ngrok**

You need ngrok to expose your local server to the internet for Twilio webhooks:

```bash
# Install ngrok if you haven't already
npm install -g ngrok

# Expose port 3000
ngrok http 3000

# Copy the HTTPS URL (e.g., https://abc123.ngrok-free.app)
# ⚠️ IMPORTANT: Use the HTTPS URL, not HTTP
```

## 📱 **Step 3: Configure Twilio WhatsApp Sandbox**

1. **Go to Twilio Console**:
   - Visit: https://console.twilio.com/
   - Login with your Twilio account

2. **Navigate to WhatsApp Sandbox**:
   - Go to: Messaging → Try it out → Send a WhatsApp message
   - Or visit: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn

3. **Set Webhook URL**:
   - In the "Sandbox Configuration" section
   - Set webhook URL to: `https://your-ngrok-url.ngrok-free.app/api/whatsapp/webhook`
   - **Example**: `https://abc123.ngrok-free.app/api/whatsapp/webhook`
   - Click "Save Configuration"

4. **Join the Sandbox**:
   - Send the join code from your WhatsApp to: ******** 523 8886**
   - Example: Send "join <your-sandbox-code>" 

## 🏠 **Step 4: Set Up Test Owner (Use Database Studio)**

```bash
# Open Drizzle Studio for easy database editing
npm run db:studio

# Go to http://localhost:4983
```

In Drizzle Studio:

1. **Go to the "users" table**
2. **Find or create an owner user**:
   - If no owner exists, click "Add Row" and create:
     ```
     username: test_owner
     password: test123
     email: <EMAIL>
     fullName: Test Owner
     phone: 9908225188
     whatsappNumber: +919908225188  ← IMPORTANT!
     role: owner
     ```

3. **Go to the "properties" table**
4. **Create a test property** (if none exists):
   ```
   ownerId: [ID of the owner you just created]
   title: Test Farmhouse
   description: Beautiful farmhouse for testing
   location: Test Location
   halfDayPrice: 3000
   fullDayPrice: 5000
   weekdayHalfDayPrice: 2500
   weekdayFullDayPrice: 4500
   weekendHalfDayPrice: 3500
   weekendFullDayPrice: 6000
   bedrooms: 3
   bathrooms: 2
   amenities: ["Swimming Pool", "Garden", "Kitchen"]
   images: ["test1.jpg"]
   videos: []
   status: active
   featured: true
   ```

## 📱 **Step 5: Test the WhatsApp Flow**

Now for the fun part! Test the complete booking flow:

### **Method A: Direct Owner Number (Recommended)**
If you have access to the owner's WhatsApp number (+919908225188):
- Message this number directly from your personal WhatsApp

### **Method B: Twilio Sandbox (Alternative)**
Use the Twilio sandbox number and trigger owner flow:

1. **Send initial message to ****** 523 8886**:
   ```
   owner hi
   ```
   (The word "owner" triggers the owner-specific flow)

## 🎬 **Expected Flow Conversation**

Here's exactly what should happen:

### **Step 1: Initial Contact**
**You send**: `"Hi, I want to book a farmhouse"`

**Bot responds**:
```
🏡 Welcome to Test Owner's Farmhouse Rentals!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📋 Available Properties:

1. **Test Farmhouse**
   📍 Test Location
   🛏️ 3 bedrooms, 2 bathrooms
   💰 Morning: ₹3,000/day
   💰 Full Day: ₹5,000/day
   ✨ Swimming Pool, Garden, Kitchen

📝 To book:
Reply with the property number (1, 2, 3...) you'd like to book

💡 Or type "info 1" to get more details about a property
```

### **Step 2: Property Selection**
**You send**: `"1"`

**Bot responds**: 
```
✅ Great choice! **Test Farmhouse**

📅 **Select Your Dates**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📅 **January 2025**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

```
 Mo Tu We Th Fr Sa Su
─────────────────────
      1  2  3  4  5
  6  7  8  9 10 11 12
 13 14 15 16 17 18 19
 20 21 22 23 24 25 26
 27 28 29 30 31
```

✅ Available  ❌ Booked  🟡 Pending

📋 **How to book:**
• Reply with day number (e.g., "15")
• For range: "15-18" or "15 to 18"
• Type "tomorrow" for next day
• Type "weekend" for this weekend
• Type "next" for next month calendar
• Type "prev" for previous month calendar

💡 Please select your dates from the calendar above
```

### **Step 3: Date Selection**
**You send**: `"25"` (or any available date)

**Bot responds**:
```
✅ **Available!**
Sunday, 25 January 2025 is available for booking.

⏰ **Choose your stay type:**

1. **Morning Visit** (9 AM - 6 PM)
2. **Full Day** (Check-in 2 PM, checkout next day)

Reply with "1" or "2"
```

### **Step 4: Stay Type Selection**
**You send**: `"1"`

**Bot responds**:
```
📅 **Dates confirmed:**
Check-in: 2025-01-25
Stay type: Morning Visit

👥 **Guest Information needed:**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Please provide the following details:

📝 **Format:**
Name: [Your full name]
Phone: [Your contact number]
Guests: [Number of guests]

💡 **Example:**
Name: Rajesh Kumar
Phone: 9876543210
Guests: 8

📞 You can also add any special requests!
```

### **Step 5: Guest Details**
**You send**:
```
Name: John Doe
Phone: 9876543210
Guests: 6
Need vegetarian meals
```

**Bot responds**:
```
✅ **Booking Summary**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🏡 **Property:** Test Farmhouse
📅 **Date:** 2025-01-25
⏰ **Stay:** Morning Visit
👤 **Guest:** John Doe
📞 **Phone:** 9876543210
👥 **Guests:** 6
📝 **Special Requests:** Need vegetarian meals

💰 **Total Amount:** ₹3,500
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

✅ **To confirm booking, reply:** "CONFIRM"
❌ **To cancel, reply:** "CANCEL"
✏️ **To modify, reply:** "CHANGE"
```

### **Step 6: Booking Confirmation**
**You send**: `"CONFIRM"`

**Bot responds**:
```
🎉 **Booking Confirmed!**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📝 **Booking Reference:** WA1704627234567
📅 **Calendar ID:** #1234
🏡 **Property:** Test Farmhouse
📅 **Date:** 2025-01-25
👥 **Guests:** 6
💰 **Total:** ₹3,500

📞 **Owner will contact you shortly** for:
• Payment details
• Property directions
• Final arrangements

✅ Your booking has been added to the owner's calendar!
```

**Owner also receives notification**:
```
🔔 **New Booking Alert!**

📝 **Reference:** WA1704627234567
📅 **Calendar Entry:** #1234
🏡 **Property:** Test Farmhouse
👤 **Customer:** John Doe
📞 **Phone:** 9876543210
📅 **Date:** 2025-01-25
👥 **Guests:** 6
💰 **Amount:** ₹3,500
📱 **Customer WhatsApp:** whatsapp:+919876543210

✅ **This booking has been automatically added to your calendar.**
Please contact the customer to confirm payment and arrangements.
```

## 🌐 **Step 6: Verify Website Sync**

1. **Open the BookAFarm website**: http://localhost:3000
2. **Go to owner dashboard** (you may need to login)
3. **Check the calendar** - you should see the WhatsApp booking appear in real-time
4. **Verify the booking details** match what was created via WhatsApp

## 🧪 **Quick Test Endpoints**

While testing, you can also use these endpoints to debug:

```bash
# Test calendar view generation
curl http://localhost:3000/api/whatsapp/test-calendar/1

# Test owner lookup
curl http://localhost:3000/api/whatsapp/test-owner/1

# Check booking status
curl http://localhost:3000/api/v1/calendar/1
```

## 🔍 **Debugging Tips**

### **If No Response from WhatsApp**:
1. Check ngrok is still running and URL hasn't changed
2. Verify webhook URL in Twilio Console
3. Check server logs for incoming webhook requests
4. Ensure you joined the sandbox correctly

### **If "Owner Not Found" Error**:
1. Verify `whatsappNumber` field is set in users table
2. Check the phone number format (+919908225188)
3. Ensure user role is 'owner'

### **If Calendar Not Syncing**:
1. Check WebSocket connection in browser dev tools
2. Verify CalendarService is creating bookings
3. Check for any database errors in server logs

### **Server Logs to Watch**:
```bash
# In another terminal, watch server logs
npm run dev

# Look for these log messages:
# ✅ "WhatsApp webhook verification"
# ✅ "Processing WhatsApp message"
# ✅ "Owner booking flow started"
# ✅ "Calendar booking created successfully"
# ✅ "WebSocket broadcast"
```

## 🎉 **Success Criteria**

You'll know it's working when:

✅ **WhatsApp bot responds** to your initial message  
✅ **Property list displays** with correct pricing  
✅ **Interactive calendar** shows month view  
✅ **Date selection** validates and confirms availability  
✅ **Guest details** are collected properly  
✅ **Booking confirmation** creates calendar entry  
✅ **Website calendar** updates in real-time  
✅ **Owner receives notification** with booking details  

## 🆘 **Need Help?**

If you run into issues:

1. **Check server logs** - they show exactly what's happening
2. **Verify database setup** - use Drizzle Studio to inspect data
3. **Test webhook delivery** - check Twilio Console delivery logs
4. **Monitor WebSocket** - use browser dev tools to see real-time updates

**The WhatsApp booking integration is fully implemented and ready to test!** 🚀