declare module 'razorpay' {
  interface RazorpayOptions {
    key_id: string;
    key_secret: string;
  }

  interface OrderOptions {
    amount: number;
    currency: string;
    receipt: string;
    notes?: Record<string, string>;
  }

  interface Order {
    id: string;
    receipt: string;
    amount: number;
    currency: string;
    created_at: number;
    status: string;
    notes?: Record<string, string>;
  }

  interface Payment {
    id: string;
    order_id: string;
    amount: number;
    currency: string;
    status: string;
    method: string;
    captured: boolean;
    created_at: number;
  }

  interface RefundOptions {
    amount: number;
    notes?: Record<string, string>;
  }

  interface Refund {
    id: string;
    payment_id: string;
    amount: number;
    currency: string;
    status: string;
    created_at: number;
    notes?: Record<string, string>;
  }

  interface Orders {
    create(options: OrderOptions): Promise<Order>;
    fetch(orderId: string): Promise<Order>;
  }

  interface Payments {
    capture(paymentId: string, amount: number, currency: string): Promise<Payment>;
    fetch(paymentId: string): Promise<Payment>;
    refund(paymentId: string, options: RefundOptions): Promise<Refund>;
  }

  class Razorpay {
    constructor(options: RazorpayOptions);
    orders: Orders;
    payments: Payments;
  }

  export = Razorpay;
}