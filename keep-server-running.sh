#!/bin/bash

# This script ensures the server keeps running despite workflow restarts

echo "🔄 Ensuring server stability..."

# Function to start server if not running
ensure_server_running() {
    if ! curl -s http://localhost:5000/api/health > /dev/null 2>&1; then
        echo "⚠️ Server not responding, starting..."
        ./start-server.sh
    else
        echo "✅ Server is running"
    fi
}

# Start server if needed
ensure_server_running

# Keep checking every 10 seconds
while true; do
    sleep 10
    ensure_server_running
done