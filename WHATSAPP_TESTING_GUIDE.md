# WhatsApp Testing Guide

## 🎯 Quick Start for Real WhatsApp Testing

### Step 1: Set Up Twilio (5 minutes)

1. **Create free Twilio account**: https://www.twilio.com/try-twilio
2. **Get your credentials** from T<PERSON><PERSON> Console:
   - Account SID
   - Auth Token
   - WhatsApp Sandbox Number (like: whatsapp:+***********)

### Step 2: Configure Your Environment

Add to `.env.development`:
```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
```

### Step 3: Expose Your Local Server

```bash
# Install ngrok
npm install -g ngrok

# Expose your local server
ngrok http 5000
```

You'll get a URL like: `https://abc123.ngrok.io`

### Step 4: Configure Twilio Webhook

1. Go to Twilio Console → Messaging → Try it out → Send a WhatsApp message
2. Set the webhook URL to: `https://YOUR_NGROK_URL.ngrok.io/api/whatsapp/webhook`
3. Set method to: POST

### Step 5: Join Twilio Sandbox

Send this message from your phone's WhatsApp:
```
join [your-sandbox-code]
```
To: ****** 523 8886

### Step 6: Test the Flow

Send any of these messages to the Twilio number:
- "Hi, I want to book a farmhouse"
- "Check availability"
- "Book"

## 📱 Testing Owner-Specific Flows

### Option A: Use Real Owner Numbers

```bash
# Run the setup script to enter real numbers
npx tsx scripts/setup-real-whatsapp.ts
```

Enter actual WhatsApp numbers for owners when prompted.

### Option B: Test with Sandbox

The Twilio sandbox acts as a central number. To test owner-specific flows:

1. **Modify the routing logic** in `server/routes/whatsapp.ts` to identify owners by message content:

```typescript
// Example: "Owner1: I want to book"
if (message.body.startsWith('Owner1:')) {
  // Route to Owner 1
}
```

## 🧪 Complete Test Flow

1. **Customer sends**: "Hi, I want to book your farmhouse"
   - **System responds**: Property list

2. **Customer sends**: "1"
   - **System responds**: Date selection prompt

3. **Customer sends**: "Tomorrow, full day"
   - **System responds**: Guest details request

4. **Customer sends**: 
   ```
   Name: John Doe
   Phone: **********
   Guests: 4
   ```
   - **System responds**: Booking summary

5. **Customer sends**: "CONFIRM"
   - **System responds**: Booking confirmation

## 🔧 Troubleshooting

### Check Server Logs
```bash
# In your server terminal, you should see:
"Received WhatsApp webhook"
"Routing to owner-specific flow"
"Started owner booking flow"
```

### Test Webhook Directly
```bash
curl -X POST https://YOUR_NGROK_URL.ngrok.io/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "Body": "Test message",
    "From": "whatsapp:+************",
    "To": "whatsapp:+***********"
  }'
```

### Common Issues

1. **"Webhook not responding"**
   - Make sure ngrok is running
   - Check server is running on port 5000
   - Verify webhook URL in Twilio

2. **"No response from bot"**
   - Check .env variables are loaded
   - Verify Twilio credentials
   - Check server logs for errors

3. **"Can't join sandbox"**
   - Make sure you're texting the right number
   - Use the exact join code from Twilio Console

## 🚀 Production Setup

For production with real WhatsApp Business:

1. **Get WhatsApp Business API access**
2. **Verify your business**
3. **Get dedicated WhatsApp number**
4. **Configure production webhooks**
5. **Set up owner numbers in database**

## 📝 Test Scenarios

### Scenario 1: Basic Booking
```
You: Hi
Bot: [Shows properties]
You: 1
Bot: [Asks for dates]
You: Tomorrow
Bot: [Asks for details]
You: Name: Test, Phone: 9999999999, Guests: 2
Bot: [Shows summary]
You: CONFIRM
Bot: [Booking confirmed]
```

### Scenario 2: Property Info
```
You: Hi
Bot: [Shows properties]
You: info 1
Bot: [Detailed property info]
```

### Scenario 3: Cancel Flow
```
You: Hi
Bot: [Shows properties]
You: cancel
Bot: [Flow cancelled]
```

## 💡 Tips

- Keep ngrok running while testing
- Watch server logs for debugging
- Test with different phone numbers
- Try edge cases (cancel, invalid inputs)
- Save successful booking IDs for verification

Ready to test with real WhatsApp! 🎉