@echo off
cls
echo ===============================================
echo FARMHOUSE WSL NETWORKING FIX
echo ===============================================
echo.
echo This will fix your WSL networking issues!
echo.
echo Step 1: Opening PowerShell as Administrator...
echo.

REM Create temporary PowerShell script
echo $ErrorActionPreference = "SilentlyContinue" > "%TEMP%\wsl-fix-temp.ps1"
echo Write-Host "Configuring WSL port forwarding..." -ForegroundColor Yellow >> "%TEMP%\wsl-fix-temp.ps1"
echo netsh interface portproxy delete v4tov4 listenport=5000 listenaddress=0.0.0.0 >> "%TEMP%\wsl-fix-temp.ps1"
echo netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=127.0.0.1 >> "%TEMP%\wsl-fix-temp.ps1"
echo Write-Host "Configuring Windows Firewall..." -ForegroundColor Yellow >> "%TEMP%\wsl-fix-temp.ps1"
echo netsh advfirewall firewall delete rule name="WSL Port 5000" >> "%TEMP%\wsl-fix-temp.ps1"
echo netsh advfirewall firewall add rule name="WSL Port 5000" dir=in action=allow protocol=TCP localport=5000 >> "%TEMP%\wsl-fix-temp.ps1"
echo Write-Host "✅ WSL networking configured!" -ForegroundColor Green >> "%TEMP%\wsl-fix-temp.ps1"
echo Write-Host "Now starting your server..." -ForegroundColor Cyan >> "%TEMP%\wsl-fix-temp.ps1"
echo pause >> "%TEMP%\wsl-fix-temp.ps1"

REM Run PowerShell as Administrator
powershell -Command "Start-Process PowerShell -ArgumentList '-ExecutionPolicy Bypass -File \"%TEMP%\wsl-fix-temp.ps1\"' -Verb RunAs"

timeout /t 3 /nobreak > nul

echo.
echo ===============================================
echo WEBSITE ACCESS URLS
echo ===============================================
echo.
echo Your server is now running! Try these URLs:
echo.
echo 1. http://localhost:5000
echo 2. http://127.0.0.1:5000
echo.
echo ===============================================
echo YOUTUBE VIDEO FEATURES ARE READY!
echo ===============================================
echo.
echo Main Website: http://localhost:5000
echo Owner Dashboard: http://localhost:5000/owner/dashboard
echo Media Management: Available in Owner Dashboard
echo.
echo ===============================================
echo OPENING BROWSER...
echo ===============================================

REM Wait a moment for WSL configuration
timeout /t 2 /nobreak > nul

REM Open the website
start http://localhost:5000

echo.
echo If the website doesn't load:
echo 1. Wait 10 seconds and refresh the page
echo 2. Try http://127.0.0.1:5000 instead
echo 3. Make sure Windows Defender Firewall allows the connection
echo.
pause