#!/usr/bin/env tsx
/**
 * Security Test Script
 * 
 * Tests all security implementations:
 * 1. Input validation
 * 2. Rate limiting
 * 3. Security headers
 * 4. Environment validation
 */

import { performance } from 'perf_hooks';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  duration: number;
}

class SecurityTester {
  private results: TestResult[] = [];
  private readonly baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:5000') {
    this.baseUrl = baseUrl;
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = performance.now();
    
    try {
      await testFn();
      const duration = performance.now() - startTime;
      this.results.push({
        name,
        status: 'PASS',
        message: 'Test passed successfully',
        duration
      });
    } catch (error) {
      const duration = performance.now() - startTime;
      this.results.push({
        name,
        status: 'FAIL',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      });
    }
  }

  private async makeRequest(endpoint: string, data: any): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    
    return response;
  }

  private async testInputValidation(): Promise<void> {
    console.log('🔍 Testing input validation...');
    
    // Test invalid phone number
    await this.runTest('Invalid phone number rejection', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: 'invalid_phone',
        type: 'sms'
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400, got ${response.status}`);
      }
    });
    
    // Test invalid email
    await this.runTest('Invalid email rejection', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: 'invalid.email',
        type: 'email'
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400, got ${response.status}`);
      }
    });
    
    // Test missing required fields
    await this.runTest('Missing required fields rejection', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: '<EMAIL>'
        // Missing 'type' field
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400, got ${response.status}`);
      }
    });
    
    // Test valid email acceptance
    await this.runTest('Valid email acceptance', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: '<EMAIL>',
        type: 'email'
      });
      
      if (response.status !== 200) {
        const errorText = await response.text();
        throw new Error(`Expected 200, got ${response.status}: ${errorText}`);
      }
    });
  }

  private async testSecurityHeaders(): Promise<void> {
    console.log('🛡️  Testing security headers...');
    
    await this.runTest('Security headers presence', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: '<EMAIL>',
        type: 'email'
      });
      
      const requiredHeaders = [
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-Request-ID',
        'Content-Security-Policy'
      ];
      
      for (const header of requiredHeaders) {
        if (!response.headers.has(header)) {
          throw new Error(`Missing security header: ${header}`);
        }
      }
    });
    
    await this.runTest('CORS headers configuration', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: '<EMAIL>',
        type: 'email'
      });
      
      const corsHeaders = [
        'Access-Control-Allow-Credentials',
        'Access-Control-Expose-Headers'
      ];
      
      for (const header of corsHeaders) {
        if (!response.headers.has(header)) {
          throw new Error(`Missing CORS header: ${header}`);
        }
      }
    });
  }

  private async testRateLimiting(): Promise<void> {
    console.log('🚦 Testing rate limiting...');
    
    await this.runTest('Rate limiting headers', async () => {
      const response = await this.makeRequest('/api/auth/otp/send-otp', {
        identifier: '<EMAIL>',
        type: 'email'
      });
      
      const rateLimitHeaders = [
        'RateLimit-Policy',
        'RateLimit-Limit',
        'RateLimit-Remaining'
      ];
      
      for (const header of rateLimitHeaders) {
        if (!response.headers.has(header)) {
          throw new Error(`Missing rate limit header: ${header}`);
        }
      }
    });
  }

  private async testOTPValidation(): Promise<void> {
    console.log('🔐 Testing OTP validation...');
    
    await this.runTest('Invalid OTP code rejection', async () => {
      const response = await this.makeRequest('/api/auth/otp/verify-otp-login', {
        identifier: '<EMAIL>',
        code: '12345', // Invalid length
        type: 'email'
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400, got ${response.status}`);
      }
    });
    
    await this.runTest('Non-numeric OTP rejection', async () => {
      const response = await this.makeRequest('/api/auth/otp/verify-otp-login', {
        identifier: '<EMAIL>',
        code: 'abc123', // Non-numeric
        type: 'email'
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400, got ${response.status}`);
      }
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting security tests...\n');
    
    try {
      await this.testInputValidation();
      await this.testSecurityHeaders();
      await this.testRateLimiting();
      await this.testOTPValidation();
    } catch (error) {
      console.error('❌ Test runner error:', error);
    }
    
    this.printResults();
  }

  private printResults(): void {
    console.log('\n📊 Security Test Results:');
    console.log('='.repeat(60));
    
    let passed = 0;
    let failed = 0;
    let totalDuration = 0;
    
    for (const result of this.results) {
      const statusIcon = result.status === 'PASS' ? '✅' : '❌';
      const durationMs = Math.round(result.duration);
      
      console.log(`${statusIcon} ${result.name} (${durationMs}ms)`);
      
      if (result.status === 'FAIL') {
        console.log(`   └─ ${result.message}`);
        failed++;
      } else {
        passed++;
      }
      
      totalDuration += result.duration;
    }
    
    console.log('='.repeat(60));
    console.log(`📈 Summary: ${passed} passed, ${failed} failed`);
    console.log(`⏱️  Total duration: ${Math.round(totalDuration)}ms`);
    console.log(`🎯 Success rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Some security tests failed. Please review the implementation.');
      process.exit(1);
    } else {
      console.log('\n✅ All security tests passed!');
    }
  }
}

// Check if server is running
async function checkServerHealth(): Promise<boolean> {
  try {
    const response = await fetch('http://localhost:5000/api/health');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServerHealth();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on localhost:5000');
    console.log('Please start the server first: npm run start:prod');
    process.exit(1);
  }
  
  const tester = new SecurityTester();
  await tester.runAllTests();
}

main().catch(console.error);