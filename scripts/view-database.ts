import { config } from 'dotenv';
import { resolve } from 'path';
import { db } from '../server/db';
import { smsTemplates, smsLogs, users, properties, bookings } from '../shared/schema';
import { desc, eq, sql } from 'drizzle-orm';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function viewDatabase() {
  console.log('🔍 Database Explorer\n');

  try {
    // 1. SMS Templates
    console.log('📱 SMS Templates:');
    console.log('=' + '='.repeat(50));
    const templates = await db
      .select()
      .from(smsTemplates)
      .orderBy(smsTemplates.createdAt);

    templates.forEach((template, index) => {
      console.log(`${index + 1}. ${template.name} (${template.key})`);
      console.log(`   Status: ${template.status}`);
      console.log(`   DLT ID: ${template.dltTemplateId}`);
      console.log(`   Content: ${template.content}`);
      console.log(`   Variables: ${JSON.stringify(template.variables)}`);
      console.log('');
    });

    // 2. Recent SMS Logs
    console.log('\n📊 Recent SMS Logs:');
    console.log('=' + '='.repeat(50));
    const logs = await db
      .select({
        id: smsLogs.id,
        templateName: smsTemplates.name,
        recipientPhone: smsLogs.recipientPhone,
        status: smsLogs.status,
        messageContent: smsLogs.messageContent,
        twilioSid: smsLogs.twilioMessageSid,
        createdAt: smsLogs.createdAt
      })
      .from(smsLogs)
      .leftJoin(smsTemplates, eq(smsLogs.templateId, smsTemplates.id))
      .orderBy(desc(smsLogs.createdAt))
      .limit(10);

    if (logs.length === 0) {
      console.log('   No SMS logs found.');
    } else {
      logs.forEach((log, index) => {
        console.log(`${index + 1}. ${log.templateName} → ${log.recipientPhone}`);
        console.log(`   Status: ${log.status}`);
        console.log(`   Message: ${log.messageContent}`);
        console.log(`   Sent: ${log.createdAt}`);
        if (log.twilioSid) {
          console.log(`   Twilio SID: ${log.twilioSid}`);
        }
        console.log('');
      });
    }

    // 3. Recent Users
    console.log('\n👥 Recent Users:');
    console.log('=' + '='.repeat(50));
    const recentUsers = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        phone: users.phone,
        role: users.role,
        isVerified: users.isVerified,
        createdAt: users.createdAt
      })
      .from(users)
      .orderBy(desc(users.createdAt))
      .limit(5);

    recentUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email})`);
      console.log(`   Phone: ${user.phone || 'Not provided'}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Verified: ${user.isVerified ? 'Yes' : 'No'}`);
      console.log(`   Joined: ${user.createdAt}`);
      console.log('');
    });

    // 4. Recent Properties
    console.log('\n🏠 Recent Properties:');
    console.log('=' + '='.repeat(50));
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        location: properties.location,
        status: properties.status,
        featured: properties.featured,
        halfDayPrice: properties.halfDayPrice,
        fullDayPrice: properties.fullDayPrice,
        createdAt: properties.createdAt
      })
      .from(properties)
      .orderBy(desc(properties.createdAt))
      .limit(5);

    recentProperties.forEach((property, index) => {
      console.log(`${index + 1}. ${property.title}`);
      console.log(`   Location: ${property.location}`);
      console.log(`   Status: ${property.status}`);
      console.log(`   Prices: ₹${property.halfDayPrice} (half-day) / ₹${property.fullDayPrice} (full-day)`);
      console.log(`   Featured: ${property.featured ? 'Yes' : 'No'}`);
      console.log(`   Created: ${property.createdAt}`);
      console.log('');
    });

    // 5. Recent Bookings
    console.log('\n📅 Recent Bookings:');
    console.log('=' + '='.repeat(50));
    const recentBookings = await db
      .select({
        id: bookings.id,
        propertyTitle: properties.title,
        userEmail: users.email,
        bookingDate: bookings.bookingDate,
        bookingType: bookings.bookingType,
        guests: bookings.guests,
        totalPrice: bookings.totalPrice,
        status: bookings.status,
        createdAt: bookings.createdAt
      })
      .from(bookings)
      .leftJoin(properties, eq(bookings.propertyId, properties.id))
      .leftJoin(users, eq(bookings.userId, users.id))
      .orderBy(desc(bookings.createdAt))
      .limit(5);

    if (recentBookings.length === 0) {
      console.log('   No bookings found.');
    } else {
      recentBookings.forEach((booking, index) => {
        console.log(`${index + 1}. ${booking.propertyTitle} - ${booking.userEmail}`);
        console.log(`   Date: ${booking.bookingDate} (${booking.bookingType})`);
        console.log(`   Guests: ${booking.guests}`);
        console.log(`   Price: ₹${booking.totalPrice}`);
        console.log(`   Status: ${booking.status}`);
        console.log(`   Booked: ${booking.createdAt}`);
        console.log('');
      });
    }

    // 6. Database Statistics
    console.log('\n📊 Database Statistics:');
    console.log('=' + '='.repeat(50));
    
    const stats = await Promise.all([
      db.select({ count: sql`count(*)` }).from(users),
      db.select({ count: sql`count(*)` }).from(properties),
      db.select({ count: sql`count(*)` }).from(bookings),
      db.select({ count: sql`count(*)` }).from(smsTemplates),
      db.select({ count: sql`count(*)` }).from(smsLogs)
    ]);

    console.log(`   Users: ${stats[0][0].count}`);
    console.log(`   Properties: ${stats[1][0].count}`);
    console.log(`   Bookings: ${stats[2][0].count}`);
    console.log(`   SMS Templates: ${stats[3][0].count}`);
    console.log(`   SMS Logs: ${stats[4][0].count}`);

  } catch (error) {
    console.error('❌ Database exploration failed:', error);
  }
}

// Command-line arguments
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--sms')) {
    // Show only SMS-related data
    console.log('📱 SMS System Data Only\n');
    
    const templates = await db.select().from(smsTemplates);
    const logs = await db.select().from(smsLogs).orderBy(desc(smsLogs.createdAt)).limit(10);
    
    console.log('Templates:', templates.length);
    console.log('SMS Logs:', logs.length);
    
    templates.forEach(t => console.log(`- ${t.name}: ${t.status}`));
    logs.forEach(l => console.log(`- ${l.recipientPhone}: ${l.status} (${l.createdAt})`));
    
  } else if (args.includes('--users')) {
    // Show only user data
    const users_data = await db.select().from(users).orderBy(desc(users.createdAt));
    console.log('👥 Users:', users_data.length);
    users_data.forEach(u => console.log(`- ${u.username} (${u.email}): ${u.role}`));
    
  } else if (args.includes('--bookings')) {
    // Show only booking data
    const bookings_data = await db.select().from(bookings).orderBy(desc(bookings.createdAt));
    console.log('📅 Bookings:', bookings_data.length);
    bookings_data.forEach(b => console.log(`- ${b.bookingDate}: ₹${b.totalPrice} (${b.status})`));
    
  } else {
    // Show all data
    await viewDatabase();
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

export { viewDatabase };