#!/bin/bash

# Local CI Validation Script
# This script simulates the CI pipeline locally for validation before pushing

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}🔍 Local CI Pipeline Validation${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Function to run and check command with detailed counts
run_check() {
    local cmd="$1"
    local description="$2"
    local test_type="$3"
    echo -e "${YELLOW}Running: $description${NC}"
    echo "Command: $cmd"
    
    # Capture output
    local output
    output=$(eval "$cmd" 2>&1)
    local exit_code=$?
    
    # Show output
    echo "$output"
    
    # Extract test counts if this is a test command
    if [[ "$test_type" == "test" ]]; then
        local passed=$(echo "$output" | grep -oE "Tests.*[0-9]+ passed \([0-9]+\)" | grep -oE "\([0-9]+\)" | tr -d "()")
        local failed=0
        if [ $exit_code -ne 0 ]; then
            failed=$(echo "$output" | grep -oE "[0-9]+ failed" | grep -oE "[0-9]+" | head -1)
            failed=${failed:-"unknown"}
        fi
        
        if [ $exit_code -eq 0 ]; then
            echo -e "${GREEN}✅ $description - PASSED${NC}"
            echo -e "${GREEN}   ✅ Passed: ${passed:-0}   ❌ Failed: 0   📊 Total: ${passed:-0}${NC}"
        else
            echo -e "${RED}❌ $description - FAILED${NC}"
            echo -e "${RED}   ✅ Passed: ${passed:-0}   ❌ Failed: ${failed}   📊 Total: N/A${NC}"
        fi
    else
        if [ $exit_code -eq 0 ]; then
            echo -e "${GREEN}✅ $description - PASSED${NC}"
        else
            echo -e "${RED}❌ $description - FAILED${NC}"
        fi
    fi
    
    echo ""
    return $exit_code
}

# Initialize status tracking
CHECKS_PASSED=0
CHECKS_FAILED=0

# Check 1: TypeScript compilation
if run_check "npm run check" "TypeScript Type Check" "build"; then
    ((CHECKS_PASSED++))
else
    ((CHECKS_FAILED++))
fi

# Check 2: Unit tests
if run_check "npm run test:unit" "Unit Tests" "test"; then
    ((CHECKS_PASSED++))
else
    ((CHECKS_FAILED++))
fi

# Check 3: Integration tests (requires database)
echo -e "${YELLOW}Checking if PostgreSQL is running...${NC}"
if command -v docker &> /dev/null && docker ps | grep -q postgres; then
    if run_check "npm run test:integration" "Integration Tests" "test"; then
        ((CHECKS_PASSED++))
    else
        ((CHECKS_FAILED++))
    fi
else
    echo -e "${YELLOW}⚠️  PostgreSQL not running - skipping integration tests${NC}"
    echo "To run integration tests: docker-compose up -d"
    echo ""
fi

# Check 4: Build validation
if run_check "npm run build" "Build Check" "build"; then
    ((CHECKS_PASSED++))
else
    ((CHECKS_FAILED++))
fi

# Check 5: E2E tests (optional)
echo -e "${YELLOW}Running E2E tests (optional - won't fail validation)...${NC}"
if command -v npx playwright &> /dev/null; then
    if npm run test:e2e; then
        echo -e "${GREEN}✅ E2E Tests - PASSED (Optional)${NC}"
    else
        echo -e "${YELLOW}⚠️  E2E Tests - FAILED (Optional - not blocking)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Playwright not installed - skipping E2E tests${NC}"
    echo "To install: npx playwright install --with-deps"
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}📊 Local CI Validation Summary${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo -e "${BLUE}🎯 MANDATORY CI CHECKS:${NC}"
echo -e "   ${GREEN}✅ Checks Passed: $CHECKS_PASSED${NC}"
echo -e "   ${RED}❌ Checks Failed: $CHECKS_FAILED${NC}"
echo -e "   📊 Total Checks: $((CHECKS_PASSED + CHECKS_FAILED))"
echo ""

if [ $CHECKS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL MANDATORY CHECKS PASSED!${NC}"
    echo -e "${GREEN}✨ Ready to push to GitHub - CI will succeed${NC}"
    echo ""
    echo -e "${BLUE}🚀 Next steps:${NC}"
    echo "   1. git add ."
    echo "   2. git commit -m 'Your commit message'"
    echo "   3. git push origin your-branch"
    echo "   4. Create pull request"
    echo ""
    echo -e "${GREEN}🛡️  Branch protection: All mandatory tests will pass${NC}"
    exit 0
else
    echo -e "${RED}❌ SOME MANDATORY CHECKS FAILED${NC}"
    echo -e "${RED}🔧 Please fix failing checks before pushing${NC}"
    echo ""
    echo -e "${BLUE}📋 What to fix:${NC}"
    if [ $CHECKS_FAILED -gt 0 ]; then
        echo "   - Review error messages above"
        echo "   - Fix failing tests or compilation errors"
        echo "   - Run 'npm run ci:validate' again to verify"
    fi
    echo ""
    echo -e "${RED}⚠️  CI will fail - do not push until fixed${NC}"
    exit 1
fi