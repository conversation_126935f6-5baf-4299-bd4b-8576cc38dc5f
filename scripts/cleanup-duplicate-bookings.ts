#!/usr/bin/env tsx

/**
 * Cleanup Script: Remove Duplicate Bookings
 * 
 * This script identifies and handles duplicate bookings that occurred due to race conditions.
 * It keeps the earliest created booking and cancels the duplicates.
 */

// Load environment configuration first
import '../server/env-loader';

import { storage } from '../server/storage';
import { db } from '../server/db';
import { bookings } from '../shared/schema';
import { eq, and, or } from 'drizzle-orm';

interface DuplicateBooking {
  id: number;
  propertyId: number;
  bookingDate: string;
  bookingType: string;
  userId: number;
  status: string;
  createdAt: Date;
  totalPrice: number;
}

async function findDuplicateBookings(): Promise<DuplicateBooking[]> {
  console.log('🔍 Scanning for duplicate bookings...');
  
  const allBookings = await db
    .select()
    .from(bookings)
    .where(
      or(
        eq(bookings.status, 'confirmed'),
        eq(bookings.status, 'pending_payment')
      )
    );

  // Group bookings by property + date + type
  const bookingGroups = new Map<string, DuplicateBooking[]>();
  
  for (const booking of allBookings) {
    const key = `${booking.propertyId}-${booking.bookingDate}-${booking.bookingType}`;
    if (!bookingGroups.has(key)) {
      bookingGroups.set(key, []);
    }
    bookingGroups.get(key)!.push(booking as DuplicateBooking);
  }

  // Find groups with duplicates
  const duplicates: DuplicateBooking[] = [];
  for (const [key, group] of bookingGroups) {
    if (group.length > 1) {
      console.log(`📅 Found ${group.length} duplicate bookings for ${key}`);
      // Sort by creation time, keep the first, mark others as duplicates
      group.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      duplicates.push(...group.slice(1)); // All except the first one
    }
  }

  return duplicates;
}

async function handleDuplicateBookings(duplicates: DuplicateBooking[]): Promise<void> {
  console.log(`🚨 Found ${duplicates.length} duplicate bookings to process`);
  
  if (duplicates.length === 0) {
    console.log('✅ No duplicate bookings found!');
    return;
  }

  // Group duplicates by user to send notifications
  const userDuplicates = new Map<number, DuplicateBooking[]>();
  for (const duplicate of duplicates) {
    if (!userDuplicates.has(duplicate.userId)) {
      userDuplicates.set(duplicate.userId, []);
    }
    userDuplicates.get(duplicate.userId)!.push(duplicate);
  }

  console.log(`👥 Affecting ${userDuplicates.size} users`);

  // Process each duplicate booking
  for (const duplicate of duplicates) {
    try {
      console.log(`❌ Cancelling duplicate booking ID: ${duplicate.id}`);
      
      // Update the booking status to cancelled
      await db
        .update(bookings)
        .set({
          status: 'cancelled',
          specialRequests: `[AUTO-CANCELLED: Duplicate booking detected on ${new Date().toISOString()}] ${duplicate.specialRequests || ''}`
        })
        .where(eq(bookings.id, duplicate.id));
      
      console.log(`   ✅ Booking ${duplicate.id} cancelled successfully`);
    } catch (error) {
      console.error(`   ❌ Failed to cancel booking ${duplicate.id}:`, error);
    }
  }

  // Print summary report
  console.log('\n📊 CLEANUP SUMMARY:');
  console.log(`   • Total duplicates found: ${duplicates.length}`);
  console.log(`   • Users affected: ${userDuplicates.size}`);
  
  for (const [userId, userDups] of userDuplicates) {
    console.log(`   • User ${userId}: ${userDups.length} duplicate bookings cancelled`);
  }
}

async function generateRefundReport(duplicates: DuplicateBooking[]): Promise<void> {
  console.log('\n💰 REFUND REPORT:');
  
  const refundsNeeded = duplicates.filter(d => d.status === 'confirmed');
  let totalRefundAmount = 0;
  
  if (refundsNeeded.length === 0) {
    console.log('   ✅ No refunds needed - all duplicates were pending_payment');
    return;
  }

  console.log(`   🔄 ${refundsNeeded.length} bookings need refunds:`);
  
  for (const booking of refundsNeeded) {
    totalRefundAmount += booking.totalPrice;
    console.log(`   • Booking ${booking.id}: ₹${booking.totalPrice} (User: ${booking.userId})`);
  }
  
  console.log(`   💸 Total refund amount: ₹${totalRefundAmount}`);
  console.log('   📧 Consider sending refund notifications to affected users');
}

async function main() {
  console.log('🧹 Starting duplicate booking cleanup...\n');
  
  try {
    // Find all duplicate bookings  
    const duplicates = await findDuplicateBookings();
    
    if (duplicates.length === 0) {
      console.log('✅ No duplicate bookings found. System is clean!');
      return;
    }

    // Handle the duplicates
    await handleDuplicateBookings(duplicates);
    
    // Generate refund report
    await generateRefundReport(duplicates);
    
    console.log('\n✅ Duplicate booking cleanup completed successfully!');
    console.log('\n🔒 Next steps:');
    console.log('   1. Apply the database migration: npm run db:migrate');
    console.log('   2. Deploy the updated booking logic');
    console.log('   3. Monitor for any new duplicates');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Script completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { findDuplicateBookings, handleDuplicateBookings };