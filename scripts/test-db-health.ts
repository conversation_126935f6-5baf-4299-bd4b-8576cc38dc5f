#!/usr/bin/env tsx

/**
 * Database Health Test Script
 * 
 * This script tests the improved database configuration for Neon PostgreSQL.
 */

// Load environment configuration first
import '../server/env-loader';

import { databaseManager } from '../server/utils/database';

async function testDatabaseHealth(): Promise<void> {
  console.log('🔧 Testing improved database configuration for Neon...\n');
  
  try {
    // Test basic connection
    console.log('1. Testing basic connection...');
    const start = Date.now();
    const isConnected = await databaseManager.testConnection();
    const duration = Date.now() - start;
    
    if (isConnected) {
      console.log(`✅ Connection successful (${duration}ms)`);
    } else {
      console.log(`❌ Connection failed (${duration}ms)`);
      return;
    }
    
    // Get current metrics
    console.log('\n2. Current pool metrics:');
    const metrics = databaseManager.getMetrics();
    const poolStats = databaseManager.getPoolStats();
    
    console.log(`   📊 Total connections: ${poolStats.total}`);
    console.log(`   🆓 Idle connections: ${poolStats.idle}`);
    console.log(`   ⏳ Waiting connections: ${poolStats.waiting}`);
    console.log(`   ❌ Connection errors: ${metrics.connectionErrors}`);
    console.log(`   🕐 Last health check: ${metrics.lastHealthCheck?.toLocaleString() || 'None'}`);
    
    // Test concurrent connections
    console.log('\n3. Testing concurrent connections...');
    const concurrentTests = Array.from({ length: 3 }, async (_, i) => {
      const testStart = Date.now();
      try {
        const result = await databaseManager.testConnection();
        const testDuration = Date.now() - testStart;
        console.log(`   ✅ Concurrent test ${i + 1}: ${result ? 'SUCCESS' : 'FAILED'} (${testDuration}ms)`);
        return result;
      } catch (error) {
        const testDuration = Date.now() - testStart;
        console.log(`   ❌ Concurrent test ${i + 1}: FAILED (${testDuration}ms) - ${(error as Error).message}`);
        return false;
      }
    });
    
    const results = await Promise.all(concurrentTests);
    const successCount = results.filter(r => r).length;
    console.log(`   📊 Concurrent test results: ${successCount}/3 successful`);
    
    // Test health check
    console.log('\n4. Testing health check...');
    const healthStart = Date.now();
    const isHealthy = await databaseManager.performHealthCheck();
    const healthDuration = Date.now() - healthStart;
    
    if (isHealthy) {
      console.log(`✅ Health check passed (${healthDuration}ms)`);
    } else {
      console.log(`❌ Health check failed (${healthDuration}ms)`);
    }
    
    // Final metrics
    console.log('\n5. Final metrics:');
    const finalMetrics = databaseManager.getMetrics();
    const finalPoolStats = databaseManager.getPoolStats();
    
    console.log(`   📊 Total connections: ${finalPoolStats.total}`);
    console.log(`   🆓 Idle connections: ${finalPoolStats.idle}`);
    console.log(`   ⏳ Waiting connections: ${finalPoolStats.waiting}`);
    console.log(`   ❌ Connection errors: ${finalMetrics.connectionErrors}`);
    
    if (finalMetrics.connectionErrors === 0) {
      console.log('\n🎉 All tests passed with no connection errors!');
    } else {
      console.log(`\n⚠️ Some connection errors occurred: ${finalMetrics.connectionErrors}`);
      if (finalMetrics.lastError) {
        console.log(`   Last error: ${finalMetrics.lastError.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Database health test failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await testDatabaseHealth();
    
    console.log('\n📝 Configuration improvements:');
    console.log('   • Increased connection timeout to 45s for Neon cold starts');
    console.log('   • Reduced max connections to 3 for development (Neon optimized)');
    console.log('   • Added ETIMEDOUT and Neon-specific error recovery');
    console.log('   • Extended health check interval to 2 minutes');
    console.log('   • Added SSL configuration for Neon');
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Database health test completed');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}