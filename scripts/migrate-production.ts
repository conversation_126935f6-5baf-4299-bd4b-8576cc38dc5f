import { config } from 'dotenv';
import { resolve } from 'path';
import { execSync } from 'child_process';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function runProductionMigration() {
  console.log('🚀 Running Production Database Migration...\n');

  try {
    // Check if DATABASE_URL is loaded
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found in .env.production');
      console.log('Please ensure .env.production contains DATABASE_URL');
      process.exit(1);
    }

    console.log('✅ DATABASE_URL loaded from .env.production');
    console.log(`📍 Database: ${process.env.DATABASE_URL.split('@')[1] || 'configured'}`);

    // Run drizzle migration
    console.log('\n📝 Applying database schema changes...');
    execSync('npx drizzle-kit push', { 
      stdio: 'inherit',
      env: { ...process.env }
    });

    console.log('\n✅ Production migration completed successfully!');
    console.log('\n🔄 Next steps:');
    console.log('1. Run: npm run seed-production-templates');
    console.log('2. Test: npm run test-production-sms');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  runProductionMigration();
}

export { runProductionMigration };