#!/usr/bin/env tsx

import '../server/env-loader.js';
import { db } from '../server/db.js';
import { properties } from '../shared/schema.js';
import { eq } from 'drizzle-orm';

async function testDirectQuery() {
  console.log('🔍 Testing direct database query...');
  
  try {
    console.log('📊 Attempting to count properties...');
    const allProperties = await db.select().from(properties);
    console.log(`✅ Found ${allProperties.length} properties in database`);
    
    if (allProperties.length > 0) {
      console.log('📋 First property:', {
        id: allProperties[0].id,
        title: allProperties[0].title,
        location: allProperties[0].location,
        featured: allProperties[0].featured
      });
    }
    
    // Test featured filter
    const featuredProperties = await db.select().from(properties).where(eq(properties.featured, true));
    console.log(`⭐ Found ${featuredProperties.length} featured properties`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error querying database:', error);
    process.exit(1);
  }
}

testDirectQuery();