#!/usr/bin/env -S npx tsx

import { db } from '../server/db';

async function addWhatsAppColumn() {
  console.log('Adding WhatsApp column to users table...');
  
  try {
    // Add the column if it doesn't exist
    await db.execute(`ALTER TABLE users ADD COLUMN IF NOT EXISTS whatsapp_number TEXT`);
    console.log('✅ WhatsApp column added successfully');
    
    // Add index for faster lookups
    await db.execute(`CREATE INDEX IF NOT EXISTS users_whatsapp_idx ON users(whatsapp_number)`);
    console.log('✅ WhatsApp index created');
    
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      console.log('✅ WhatsApp column already exists');
    } else {
      console.error('Error:', error.message);
    }
  }
}

addWhatsAppColumn().then(() => {
  console.log('Database setup complete');
  process.exit(0);
}).catch((error) => {
  console.error('Failed:', error);
  process.exit(1);
});