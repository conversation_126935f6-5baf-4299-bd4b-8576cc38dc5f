import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

interface SmsTemplateData {
  key: string;
  name: string;
  content: string;
  dltTemplateId: string;
  category: string;
  variables: string[];
  status: string;
}

const templates: SmsTemplateData[] = [
  {
    key: 'booking_confirmation',
    name: 'BKAFARM_BOOKINGCONFIRM',
    content: 'BookAFarm booking for {#var#} on {#var#} confirmed. Thank you for choosing us!',
    dltTemplateId: '1207175138826492810',
    category: 'transactional',
    variables: ['property_name', 'booking_date'],
    status: 'active'
  },
  {
    key: 'payment_confirmation',
    name: 'BKAFARM_PAYMENTCONFIRM',
    content: 'Your payment of Rs. {#var#} for booking {#var#} has been received successfully. Thank you for choosing BookAFarm.',
    dltTemplateId: '1207175138762110631',
    category: 'transactional',
    variables: ['amount', 'property_name'],
    status: 'active'
  },
  {
    key: 'booking_cancelled',
    name: 'BKAFARM_CANCELLATION',
    content: 'BookAFarm booking for {#var#} on {#var#} has been cancelled. Refund will be processed within 3-5 business days.',
    dltTemplateId: 'PLACEHOLDER_CANCELLATION',
    category: 'transactional',
    variables: ['property_name', 'booking_date'],
    status: 'draft'
  },
  {
    key: 'booking_reminder',
    name: 'BKAFARM_REMINDER',
    content: 'Reminder: Your BookAFarm booking for {#var#} is scheduled for {#var#}. Check-in time: {#var#}. Contact: {#var#}',
    dltTemplateId: 'PLACEHOLDER_REMINDER',
    category: 'transactional',
    variables: ['property_name', 'booking_date', 'checkin_time', 'contact_number'],
    status: 'draft'
  },
  {
    key: 'otp_verification',
    name: 'BKAFARM_OTP',
    content: 'Your BookAFarm OTP is {#var#}. Please do not share it with anyone. Valid for 10 min.',
    dltTemplateId: '1207175206018864766',
    category: 'transactional',
    variables: ['otp_code'],
    status: 'active'
  },
  {
    key: 'owner_booking_alert',
    name: 'BKAFARM_OWNERALERT',
    content: 'New booking received for {#var#} on {#var#}. Guest: {#var#}. Amount: Rs. {#var#}. Check dashboard for details.',
    dltTemplateId: 'PLACEHOLDER_OWNER_ALERT',
    category: 'transactional',
    variables: ['property_name', 'booking_date', 'guest_name', 'amount'],
    status: 'draft'
  }
];

async function seedSMSTemplates() {
  console.log('🌱 Starting SMS template seeding...');

  try {
    for (const template of templates) {
      console.log(`\n📝 Processing template: ${template.key}`);

      // Check if template already exists
      const existingTemplate = await db
        .select()
        .from(smsTemplates)
        .where(eq(smsTemplates.key, template.key))
        .limit(1);

      if (existingTemplate.length > 0) {
        console.log(`   ⚠️  Template '${template.key}' already exists, skipping...`);
        continue;
      }

      // Insert new template
      const insertedTemplate = await db
        .insert(smsTemplates)
        .values({
          key: template.key,
          name: template.name,
          content: template.content,
          dltTemplateId: template.dltTemplateId,
          category: template.category,
          variables: template.variables,
          status: template.status
        })
        .returning();

      console.log(`   ✅ Template '${template.key}' inserted successfully`);
      console.log(`   📄 Content: ${template.content}`);
      console.log(`   🔢 DLT ID: ${template.dltTemplateId}`);
      console.log(`   📊 Status: ${template.status}`);
    }

    console.log('\n🎉 SMS template seeding completed successfully!');
    
    // Display summary
    const totalTemplates = await db
      .select()
      .from(smsTemplates);
    
    const activeTemplates = await db
      .select()
      .from(smsTemplates)
      .where(eq(smsTemplates.status, 'active'));

    console.log(`\n📊 Summary:`);
    console.log(`   • Total templates in database: ${totalTemplates.length}`);
    console.log(`   • Active templates: ${activeTemplates.length}`);

  } catch (error) {
    console.error('❌ Error seeding SMS templates:', error);
    process.exit(1);
  }
}

async function listExistingTemplates() {
  console.log('\n📋 Existing SMS templates:');
  
  try {
    const templates = await db
      .select({
        id: smsTemplates.id,
        key: smsTemplates.key,
        name: smsTemplates.name,
        status: smsTemplates.status,
        dltTemplateId: smsTemplates.dltTemplateId
      })
      .from(smsTemplates)
      .orderBy(smsTemplates.createdAt);

    if (templates.length === 0) {
      console.log('   No templates found.');
      return;
    }

    templates.forEach((template, index) => {
      console.log(`\n   ${index + 1}. ${template.key}`);
      console.log(`      Name: ${template.name}`);
      console.log(`      Status: ${template.status}`);
      console.log(`      DLT ID: ${template.dltTemplateId}`);
    });

  } catch (error) {
    console.error('❌ Error listing templates:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--list')) {
    await listExistingTemplates();
  } else if (args.includes('--seed')) {
    await seedSMSTemplates();
  } else {
    console.log('📱 SMS Template Management Script');
    console.log('\nUsage:');
    console.log('  npm run seed-sms-templates --seed     # Seed templates');
    console.log('  npm run seed-sms-templates --list     # List existing templates');
    console.log('\nExamples:');
    console.log('  tsx scripts/seed-sms-templates.ts --seed');
    console.log('  tsx scripts/seed-sms-templates.ts --list');
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  main()
    .then(() => {
      console.log('\n✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script failed:', error);
      process.exit(1);
    });
}

export { seedSMSTemplates, listExistingTemplates };