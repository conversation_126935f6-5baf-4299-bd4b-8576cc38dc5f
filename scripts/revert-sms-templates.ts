import { config } from 'dotenv';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
config({ path: resolve(__dirname, '../.env.production') });

async function revertSMSTemplates() {
  console.log('⏪ Reverting SMS templates to original format with header...');

  try {
    // Revert OTP template
    const otpResult = await db
      .update(smsTemplates)
      .set({
        content: 'Your BookAFarm OTP is {#var#}. Please do not share it with anyone. Valid for 10 min.',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'otp_verification'))
      .returning();

    if (otpResult.length > 0) {
      console.log('✅ OTP template reverted successfully');
      console.log(`   Content: ${otpResult[0].content}`);
    } else {
      console.log('⚠️  OTP template not found');
    }

    // Revert payment confirmation template
    const paymentResult = await db
      .update(smsTemplates)
      .set({
        content: 'Your payment of Rs. {#var#} for booking {#var#} has been received successfully. Thank you for choosing BookAFarm.',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'payment_confirmation'))
      .returning();

    if (paymentResult.length > 0) {
      console.log('✅ Payment confirmation template reverted successfully');
      console.log(`   Content: ${paymentResult[0].content}`);
    } else {
      console.log('⚠️  Payment confirmation template not found');
    }

    console.log('\n🎉 SMS template reversion completed!');
    console.log('📱 Templates now use original header format - fix the whitespace in Twilio DLT template instead');

  } catch (error) {
    console.error('❌ Error reverting SMS templates:', error);
    process.exit(1);
  }
}

// Run the revert
revertSMSTemplates()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  });