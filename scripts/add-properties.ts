import { config } from "dotenv";
import { resolve } from "path";

// Load environment variables
config({ path: resolve(process.cwd(), '.env.development') });

import { db } from "../server/db";
import { users, properties } from "../shared/schema";
import { eq } from "drizzle-orm";

async function addProperties() {
  console.log("Adding sample properties...");
  
  try {
    // Get owner users
    const owners = await db.select().from(users).where(eq(users.role, 'owner'));
    console.log(`Found ${owners.length} owners`);
    
    if (owners.length === 0) {
      console.log("No owners found. Creating a test owner...");
      const [testOwner] = await db.insert(users).values({
        username: 'test_owner',
        password: '$2b$10$RirF4t40o54TmRzHERsS5upJQnAa8iDOpvWsBeBhar3PpKW62WAU6', // password123
        email: '<EMAIL>',
        fullName: 'Test Owner',
        phone: '+************',
        address: 'Test Location, India',
        bio: 'Test farmhouse owner',
        role: 'owner',
        isVerified: true,
        termsAccepted: true,
        privacyPolicyAccepted: true,
        cookiePolicyAccepted: true,
        dataProcessingConsent: true,
        marketingConsent: false
      }).returning();
      owners.push(testOwner);
    }
    
    // Add sample properties
    const sampleProperties = [
      {
        title: "Sunrise Farm Retreat",
        description: "Beautiful farmhouse with scenic views and modern amenities. Perfect for weekend getaways and family gatherings.",
        location: "Shamshabad, Hyderabad, Telangana",
        halfDayPrice: 2500,
        fullDayPrice: 3500,
        weekdayHalfDayPrice: 2200,
        weekdayFullDayPrice: 3200,
        weekendHalfDayPrice: 2800,
        weekendFullDayPrice: 3800,
        bedrooms: 4,
        bathrooms: 3,
        amenities: ['wifi', 'parking', 'kitchen', 'garden', 'pool'],
        images: ['/images/property1-1.jpg', '/images/property1-2.jpg'],
        status: 'active' as const,
        featured: true,
        ownerId: owners[0].id
      },
      {
        title: "Heritage Villa",
        description: "Traditional Telangana architecture with modern comforts. Experience rural life with luxury amenities.",
        location: "Chevella, Rangareddy District, Telangana", 
        halfDayPrice: 3000,
        fullDayPrice: 4200,
        weekdayHalfDayPrice: 2700,
        weekdayFullDayPrice: 3900,
        weekendHalfDayPrice: 3300,
        weekendFullDayPrice: 4500,
        bedrooms: 5,
        bathrooms: 4,
        amenities: ['wifi', 'parking', 'kitchen', 'garden', 'pool', 'bbq'],
        images: ['/images/property2-1.jpg', '/images/property2-2.jpg'],
        status: 'active' as const,
        featured: true,
        ownerId: owners[Math.min(1, owners.length - 1)].id
      },
      {
        title: "Eco Farm Stay",
        description: "Sustainable farmhouse with organic farming activities. Great for nature lovers and eco-conscious travelers.",
        location: "Vikarabad, Telangana",
        halfDayPrice: 2000,
        fullDayPrice: 2800,
        weekdayHalfDayPrice: 1800,
        weekdayFullDayPrice: 2500,
        weekendHalfDayPrice: 2200,
        weekendFullDayPrice: 3000,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['parking', 'kitchen', 'garden', 'organic_farm'],
        images: ['/images/property3-1.jpg', '/images/property3-2.jpg'],
        status: 'active' as const,
        featured: false,
        ownerId: owners[Math.min(2, owners.length - 1)].id
      }
    ];
    
    for (const property of sampleProperties) {
      const [newProperty] = await db.insert(properties).values(property).returning();
      console.log(`Created property: ${newProperty.title} (ID: ${newProperty.id})`);
    }
    
    console.log("✅ Sample properties added successfully!");
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Error adding properties:", error);
    process.exit(1);
  }
}

addProperties();