import { config } from 'dotenv';
import { resolve } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function testOTPOnly() {
  console.log('🧪 Testing OTP Template Only...\n');

  try {
    // Check if DATABASE_URL is loaded
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found in .env.production');
      return;
    }

    console.log('✅ DATABASE_URL loaded');

    // Check OTP template specifically
    console.log('\n📱 Checking OTP template...');
    const otpTemplate = await db
      .select()
      .from(smsTemplates)
      .where(eq(smsTemplates.key, 'otp_verification'))
      .limit(1);

    if (otpTemplate.length === 0) {
      console.log('❌ OTP template not found');
      return;
    }

    const template = otpTemplate[0];
    console.log(`   ✅ Template found: ${template.name}`);
    console.log(`   📝 Status: ${template.status}`);
    console.log(`   🔢 DLT ID: ${template.dltTemplateId}`);
    console.log(`   📄 Content: ${template.content}`);
    console.log(`   🔧 Variables: ${JSON.stringify(template.variables)}`);

    // Test variable replacement for OTP
    console.log('\n🔧 Testing OTP variable replacement...');
    const testOTP = '123456';
    let message = template.content;
    const variables = template.variables as string[];
    
    variables.forEach(varName => {
      if (varName === 'otp_code') {
        message = message.replace('{#var#}', testOTP);
      }
    });
    
    console.log(`   ✅ Test OTP: ${testOTP}`);
    console.log(`   ✅ Final message: "${message}"`);
    
    // Verify the message matches expected format
    const expectedMessage = 'Your BookAFarm OTP is 123456. Please do not share it with anyone. Valid for 10 min.';
    const messageMatches = message === expectedMessage;
    
    console.log(`   ${messageMatches ? '✅' : '❌'} Message format: ${messageMatches ? 'CORRECT' : 'INCORRECT'}`);
    
    if (!messageMatches) {
      console.log(`   Expected: "${expectedMessage}"`);
      console.log(`   Actual: "${message}"`);
    }

    // Check Twilio configuration for OTP
    console.log('\n📞 Checking Twilio configuration for OTP...');
    const requiredVars = ['TWILIO_ACCOUNT_SID', 'TWILIO_AUTH_TOKEN', 'TWILIO_MESSAGING_SID'];
    
    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        console.log(`   ✅ ${varName}: configured`);
      } else {
        console.log(`   ❌ ${varName}: not configured`);
      }
    });

    console.log('\n🎉 OTP template test completed!');
    console.log('\n📋 Summary:');
    console.log(`   ✅ OTP template is ${template.status}`);
    console.log(`   ✅ DLT Template ID: ${template.dltTemplateId}`);
    console.log(`   ✅ Variable: otp_code`);
    console.log(`   ✅ Message format: ${messageMatches ? 'Valid' : 'Invalid'}`);
    
    console.log('\n🚀 Ready for production OTP SMS!');
    console.log('   - Template: BKAFARM_OTP');
    console.log('   - DLT ID: 1207175206018864766');
    console.log('   - Variable: {otp_code}');

  } catch (error) {
    console.error('❌ OTP test failed:', error);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testOTPOnly()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testOTPOnly };