#!/usr/bin/env tsx
/**
 * Real-time Database Health Monitor
 * Run this alongside your application to monitor connection health
 */

// Load environment configuration FIRST
import "../server/env-loader";

import { databaseManager } from '../server/utils/database';
import { logger } from '../server/services/LoggerService';

const REFRESH_INTERVAL = 1000; // 1 second
const HISTORY_SIZE = 60; // Keep last 60 seconds

interface HealthSnapshot {
  timestamp: Date;
  metrics: {
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    connectionErrors: number;
    healthCheckPassed: boolean;
  };
  poolStats: {
    total: number;
    idle: number;
    waiting: number;
  };
}

class HealthMonitor {
  private history: HealthSnapshot[] = [];
  private lastErrorCount = 0;
  private startTime = Date.now();
  
  async collectSnapshot(): Promise<HealthSnapshot> {
    const metrics = databaseManager.getMetrics();
    const poolStats = databaseManager.getPoolStats();
    
    // Quick health check
    let healthCheckPassed = true;
    try {
      await databaseManager.testConnection();
    } catch {
      healthCheckPassed = false;
    }
    
    return {
      timestamp: new Date(),
      metrics: {
        totalConnections: metrics.totalConnections,
        activeConnections: metrics.activeConnections,
        idleConnections: metrics.idleConnections,
        connectionErrors: metrics.connectionErrors,
        healthCheckPassed
      },
      poolStats
    };
  }
  
  addSnapshot(snapshot: HealthSnapshot) {
    this.history.push(snapshot);
    if (this.history.length > HISTORY_SIZE) {
      this.history.shift();
    }
  }
  
  getErrorRate(): number {
    if (this.history.length < 2) return 0;
    
    const recentErrors = this.history[this.history.length - 1].metrics.connectionErrors;
    const oldErrors = this.history[0].metrics.connectionErrors;
    const timeDiff = (this.history[this.history.length - 1].timestamp.getTime() - 
                     this.history[0].timestamp.getTime()) / 1000;
    
    return timeDiff > 0 ? (recentErrors - oldErrors) / timeDiff : 0;
  }
  
  displayDashboard(snapshot: HealthSnapshot) {
    // Clear console and move cursor to top
    console.clear();
    
    const uptime = Math.floor((Date.now() - this.startTime) / 1000);
    const uptimeStr = `${Math.floor(uptime / 60)}m ${uptime % 60}s`;
    
    console.log('┌─────────────────────────────────────────────────────────┐');
    console.log('│           🏥 Database Health Monitor (Phase 1)           │');
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log(`│ Uptime: ${uptimeStr.padEnd(47)} │`);
    console.log(`│ Time: ${new Date().toLocaleTimeString().padEnd(49)} │`);
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log('│                    CONNECTION POOL                       │');
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log(`│ Total Connections:  ${String(snapshot.poolStats.total).padEnd(36)} │`);
    console.log(`│ Active Connections: ${String(snapshot.metrics.activeConnections).padEnd(36)} │`);
    console.log(`│ Idle Connections:   ${String(snapshot.metrics.idleConnections).padEnd(36)} │`);
    console.log(`│ Waiting Queue:      ${String(snapshot.poolStats.waiting).padEnd(36)} │`);
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log('│                      HEALTH STATUS                       │');
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log(`│ Health Check:       ${(snapshot.metrics.healthCheckPassed ? '✅ PASS' : '❌ FAIL').padEnd(36)} │`);
    console.log(`│ Total Errors:       ${String(snapshot.metrics.connectionErrors).padEnd(36)} │`);
    console.log(`│ Error Rate:         ${(this.getErrorRate().toFixed(2) + '/sec').padEnd(36)} │`);
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log('│                    RECENT ACTIVITY                       │');
    console.log('├─────────────────────────────────────────────────────────┤');
    
    // Show recent error trend
    if (snapshot.metrics.connectionErrors > this.lastErrorCount) {
      console.log(`│ ⚠️  New errors detected: ${snapshot.metrics.connectionErrors - this.lastErrorCount}${' '.repeat(30)} │`);
      this.lastErrorCount = snapshot.metrics.connectionErrors;
    } else {
      console.log(`│ ✅ No new errors${' '.repeat(40)} │`);
    }
    
    // Show connection activity graph (last 20 seconds)
    const recentHistory = this.history.slice(-20);
    if (recentHistory.length > 0) {
      console.log('├─────────────────────────────────────────────────────────┤');
      console.log('│ Active Connections (last 20s):                          │');
      console.log('│                                                         │');
      
      const maxActive = Math.max(...recentHistory.map(h => h.metrics.activeConnections), 1);
      const graphHeight = 5;
      
      for (let row = graphHeight; row >= 0; row--) {
        let line = '│ ';
        if (row === 0) {
          line += '  ';
        } else {
          line += String(Math.round(maxActive * row / graphHeight)).padStart(2) + ' ';
        }
        
        for (const h of recentHistory) {
          const height = Math.round(h.metrics.activeConnections * graphHeight / maxActive);
          line += height >= row ? '█' : ' ';
        }
        
        line += ' '.repeat(57 - line.length) + '│';
        console.log(line);
      }
    }
    
    console.log('└─────────────────────────────────────────────────────────┘');
    console.log('\nPress Ctrl+C to exit\n');
    
    // Show last error if any
    const lastError = databaseManager.getMetrics().lastError;
    if (lastError) {
      console.log(`Last Error: ${lastError.message}`);
    }
  }
  
  async start() {
    console.log('Starting database health monitor...');
    console.log('Waiting for initial data...\n');
    
    setInterval(async () => {
      try {
        const snapshot = await this.collectSnapshot();
        this.addSnapshot(snapshot);
        this.displayDashboard(snapshot);
      } catch (error) {
        console.error('Monitor error:', error);
      }
    }, REFRESH_INTERVAL);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n\nShutting down monitor...');
  await databaseManager.shutdown();
  process.exit(0);
});

// Start monitor
const monitor = new HealthMonitor();
monitor.start().catch(console.error);