#!/usr/bin/env tsx

/**
 * Check July 30 Bookings Script
 * 
 * This script checks all bookings on July 30, 2025 regardless of status
 * to understand what the UI might be showing.
 */

// Load environment configuration first
import '../server/env-loader';

import { db } from '../server/db';

async function checkJuly30Bookings(): Promise<void> {
  console.log('🔍 Checking ALL bookings on July 30, 2025 (all statuses)...\n');
  
  try {
    // Get ALL bookings for July 30, 2025 regardless of status
    const bookingsResult = await db.execute(`
      SELECT 
        b.id, b.property_id, b.user_id, b.booking_date, b.booking_type,
        b.guests, b.total_price, b.status, b.created_at,
        p.title as property_name, p.location,
        u.full_name, u.phone, u.email
      FROM bookings b
      JOIN properties p ON b.property_id = p.id
      JOIN users u ON b.user_id = u.id
      WHERE DATE(b.booking_date) = '2025-07-30'
      ORDER BY b.property_id, b.status, b.created_at ASC
    `);
    
    console.log(`📋 Found ${bookingsResult.rows.length} total bookings for July 30, 2025:\n`);
    
    if (bookingsResult.rows.length === 0) {
      console.log('❌ No bookings found for July 30, 2025');
      return;
    }
    
    // Group by property and show all statuses
    const propertyGroups = new Map();
    
    for (const booking of bookingsResult.rows) {
      const b = booking as any;
      const key = b.property_id;
      
      if (!propertyGroups.has(key)) {
        propertyGroups.set(key, []);
      }
      propertyGroups.get(key).push(b);
    }
    
    // Display bookings grouped by property
    for (const [propertyId, bookings] of propertyGroups.entries()) {
      const firstBooking = bookings[0];
      console.log(`🏠 PROPERTY: ${firstBooking.property_name} (ID: ${propertyId})`);
      console.log(`   📍 Location: ${firstBooking.location}`);
      console.log(`   📊 Total bookings: ${bookings.length}\n`);
      
      bookings.forEach((b: any, index: number) => {
        const statusEmoji = b.status === 'confirmed' ? '✅' : 
                           b.status === 'pending_payment' ? '⏳' : 
                           b.status === 'cancelled' ? '❌' : '❓';
        
        console.log(`   ${index + 1}. ${statusEmoji} Booking ID: ${b.id} (${b.status.toUpperCase()})`);
        console.log(`      👤 User: ${b.full_name} (ID: ${b.user_id})`);
        console.log(`      📱 Phone: ${b.phone || 'N/A'}`);
        console.log(`      📅 Type: ${b.booking_type}, Guests: ${b.guests}`);
        console.log(`      💰 Price: ₹${b.total_price}`);
        console.log(`      📅 Created: ${new Date(b.created_at).toLocaleString()}\n`);
      });
      
      console.log('---\n');
    }
    
    // Summary by status
    const statusCounts = new Map();
    for (const booking of bookingsResult.rows) {
      const b = booking as any;
      statusCounts.set(b.status, (statusCounts.get(b.status) || 0) + 1);
    }
    
    console.log(`📊 BOOKING STATUS SUMMARY for July 30, 2025:`);
    for (const [status, count] of statusCounts.entries()) {
      const emoji = status === 'confirmed' ? '✅' : 
                   status === 'pending_payment' ? '⏳' : 
                   status === 'cancelled' ? '❌' : '❓';
      console.log(`   ${emoji} ${status}: ${count} bookings`);
    }
    
    // Check for potential UI display issues
    console.log(`\n🔍 POTENTIAL UI ISSUES:`);
    
    const cancelledBookings = bookingsResult.rows.filter((b: any) => b.status === 'cancelled');
    if (cancelledBookings.length > 0) {
      console.log(`   ⚠️ Found ${cancelledBookings.length} cancelled bookings that might still be showing in UI`);
    }
    
    const activeBookings = bookingsResult.rows.filter((b: any) => 
      b.status === 'confirmed' || b.status === 'pending_payment'
    );
    console.log(`   ✅ Active bookings: ${activeBookings.length}`);
    
    // Check for different booking types
    const bookingTypes = new Set(bookingsResult.rows.map((b: any) => b.booking_type));
    console.log(`   📋 Booking types found: ${Array.from(bookingTypes).join(', ')}`);
    
  } catch (error) {
    console.error('❌ Error checking July 30 bookings:', error);
    throw error;
  }
}

async function main() {
  try {
    await checkJuly30Bookings();
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 July 30 booking check completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { checkJuly30Bookings };