#!/usr/bin/env tsx

/**
 * Find All Duplicates Script
 * 
 * This script finds all duplicate bookings in the system and shows phone numbers.
 */

// Load environment configuration first
import '../server/env-loader';

import { db } from '../server/db';

async function findAllDuplicates(): Promise<void> {
  console.log('🔍 Finding all duplicate bookings in the system...\n');
  
  try {
    // Find all duplicate bookings (same property, date, type, active status)
    const duplicatesResult = await db.execute(`
      WITH duplicate_bookings AS (
        SELECT 
          property_id, 
          booking_date, 
          booking_type,
          COUNT(*) as booking_count
        FROM bookings 
        WHERE status IN ('confirmed', 'pending_payment')
        GROUP BY property_id, booking_date, booking_type
        HAVING COUNT(*) > 1
      )
      SELECT 
        b.id, b.property_id, b.user_id, b.booking_date, b.booking_type,
        b.guests, b.total_price, b.status, b.created_at,
        p.title as property_name, p.location,
        u.full_name, u.phone, u.email,
        db.booking_count
      FROM bookings b
      JOIN properties p ON b.property_id = p.id
      JOIN users u ON b.user_id = u.id
      JOIN duplicate_bookings db ON 
        b.property_id = db.property_id AND 
        b.booking_date = db.booking_date AND 
        b.booking_type = db.booking_type
      WHERE b.status IN ('confirmed', 'pending_payment')
      ORDER BY b.property_id, b.booking_date, b.booking_type, b.created_at ASC
    `);
    
    console.log(`📋 Found ${duplicatesResult.rows.length} duplicate bookings:\n`);
    
    if (duplicatesResult.rows.length === 0) {
      console.log('✅ No duplicate bookings found in the system');
      return;
    }
    
    // Group duplicates
    const duplicateGroups = new Map();
    
    for (const booking of duplicatesResult.rows) {
      const b = booking as any;
      const key = `${b.property_id}-${b.booking_date}-${b.booking_type}`;
      
      if (!duplicateGroups.has(key)) {
        duplicateGroups.set(key, []);
      }
      duplicateGroups.get(key).push(b);
    }
    
    // Display duplicates by group
    let groupNumber = 1;
    let totalDuplicates = 0;
    let totalRefundNeeded = 0;
    
    for (const [key, bookings] of duplicateGroups.entries()) {
      const [propertyId, date, type] = key.split('-');
      const firstBooking = bookings[0];
      
      console.log(`🔴 GROUP ${groupNumber}: ${firstBooking.property_name} (ID: ${propertyId})`);
      console.log(`   📅 Date: ${new Date(date).toLocaleDateString()}, Type: ${type}`);
      console.log(`   📊 ${bookings.length} duplicate bookings found:\n`);
      
      bookings.forEach((b: any, index: number) => {
        const label = index === 0 ? '✅ KEEP (earliest)' : '❌ DUPLICATE (should be cancelled)';
        console.log(`   ${index + 1}. Booking ID: ${b.id} ${label}`);
        console.log(`      👤 User: ${b.full_name} (ID: ${b.user_id})`);
        console.log(`      📱 Phone: ${b.phone}`);
        console.log(`      💰 Price: ₹${b.total_price}`);
        console.log(`      📅 Created: ${new Date(b.created_at).toLocaleString()}`);
        console.log(`      📊 Status: ${b.status}\n`);
        
        if (index > 0) {
          totalDuplicates++;
          totalRefundNeeded += parseFloat(b.total_price);
        }
      });
      
      groupNumber++;
    }
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`   🔴 Total duplicate groups: ${duplicateGroups.size}`);
    console.log(`   ❌ Total bookings to cancel: ${totalDuplicates}`);
    console.log(`   💰 Total refund needed: ₹${totalRefundNeeded.toLocaleString()}`);
    
    // Look for phone numbers similar to 8297373632
    console.log(`\n🔍 Searching for phone numbers similar to 8297373632...`);
    const phoneSearchResult = await db.execute(`
      SELECT full_name, phone, email, id
      FROM users 
      WHERE phone LIKE '%829737%' OR phone LIKE '%97373632%'
      ORDER BY phone
    `);
    
    if (phoneSearchResult.rows.length > 0) {
      console.log(`   Found ${phoneSearchResult.rows.length} similar phone numbers:`);
      phoneSearchResult.rows.forEach((row: any) => {
        console.log(`   📱 ${row.phone} - ${row.full_name} (ID: ${row.id})`);
      });
    } else {
      console.log(`   ❌ No similar phone numbers found`);
    }
    
  } catch (error) {
    console.error('❌ Error finding duplicates:', error);
    throw error;
  }
}

async function main() {
  try {
    await findAllDuplicates();
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Duplicate search completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { findAllDuplicates };