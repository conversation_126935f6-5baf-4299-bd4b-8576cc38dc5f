#!/usr/bin/env -S npx tsx

/**
 * Setup Owner WhatsApp Numbers for Testing
 * This script adds WhatsApp numbers to property owners
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.development') });

import { db } from '../server/db';
import { users, properties } from '@shared/schema';
import { eq, and, isNull } from 'drizzle-orm';

async function setupOwnerWhatsApp() {
  console.log('🏡 Setting up Owner WhatsApp Numbers...\n');

  try {
    // Get all property owners
    const allOwners = await db.select({
      id: users.id,
      fullName: users.fullName,
      email: users.email,
      role: users.role,
      phone: users.phone,
      whatsappNumber: users.whatsappNumber
    })
    .from(users)
    .where(eq(users.role, 'owner'));

    console.log(`Found ${allOwners.length} property owners`);
    
    // Filter owners without WhatsApp numbers
    const ownersWithoutWhatsApp = allOwners.filter(o => !o.whatsappNumber);
    console.log(`${ownersWithoutWhatsApp.length} owners need WhatsApp configuration\n`);

    // Sample WhatsApp numbers for testing (Indian numbers)
    const testPhoneNumbers = [
      '************',  // Owner 1
      '************',  // Owner 2
      '************',  // Owner 3
      '************',  // Owner 4
      '************'   // Owner 5
    ];

    // Update owners with WhatsApp numbers
    for (let i = 0; i < Math.min(ownersWithoutWhatsApp.length, testPhoneNumbers.length); i++) {
      const owner = ownersWithoutWhatsApp[i];
      const whatsappNumber = testPhoneNumbers[i];

      await db.update(users)
        .set({ 
          whatsappNumber: whatsappNumber,
          phone: owner.phone || whatsappNumber // Set phone if not already set
        })
        .where(eq(users.id, owner.id));

      console.log(`✅ Updated ${owner.fullName} with WhatsApp: +${whatsappNumber}`);
    }

    // Display owner-property mapping
    console.log('\n📱 Owner WhatsApp to Property Mapping:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    const ownersWithProperties = await db.select({
      ownerId: users.id,
      ownerName: users.fullName,
      ownerPhone: users.phone,
      ownerWhatsApp: users.whatsappNumber,
      propertyId: properties.id,
      propertyTitle: properties.title,
      propertyLocation: properties.location
    })
    .from(users)
    .leftJoin(properties, eq(properties.ownerId, users.id))
    .where(and(
      eq(users.role, 'owner'),
      eq(properties.status, 'active')
    ));

    // Group by owner
    const ownerGroups = new Map();
    ownersWithProperties.forEach(row => {
      if (!ownerGroups.has(row.ownerId)) {
        ownerGroups.set(row.ownerId, {
          name: row.ownerName,
          phone: row.ownerPhone,
          whatsapp: row.ownerWhatsApp,
          properties: []
        });
      }
      ownerGroups.get(row.ownerId).properties.push({
        id: row.propertyId,
        title: row.propertyTitle,
        location: row.propertyLocation
      });
    });

    ownerGroups.forEach((owner, ownerId) => {
      console.log(`👤 ${owner.name}`);
      console.log(`📱 WhatsApp: +${owner.whatsapp || owner.phone || 'Not configured'}`);
      console.log(`📞 Phone: +${owner.phone || 'Not set'}`);
      console.log(`🏡 Properties:`);
      owner.properties.forEach((prop, index) => {
        console.log(`   ${index + 1}. ${prop.title} (${prop.location})`);
      });
      console.log('');
    });

    console.log('🎯 How to Test:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    console.log('1. Send WhatsApp message TO any owner\'s number above');
    console.log('2. Customer will see ONLY that owner\'s properties');
    console.log('3. Booking will go directly to that owner');
    console.log('4. Owner gets notification when booking is confirmed\n');

    console.log('📝 Test Message Examples:');
    console.log('• "Hi, I want to book your farmhouse"');
    console.log('• "Check availability for this weekend"');
    console.log('• "Book"');
    console.log('• "Hello"\n');

    console.log('✅ Owner WhatsApp setup complete!');

  } catch (error) {
    console.error('❌ Error setting up owner WhatsApp:', error);
    process.exit(1);
  }
}

// Run the setup
setupOwnerWhatsApp().then(() => {
  console.log('\n🚀 Ready for owner-based WhatsApp booking!');
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});