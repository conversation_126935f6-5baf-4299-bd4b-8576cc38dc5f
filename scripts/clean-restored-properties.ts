import { config } from "dotenv";
import { resolve } from "path";

// Load environment variables
config({ path: resolve(process.cwd(), '.env.development') });

import { db } from "../server/db";
import { properties } from "../shared/schema";
import { eq } from "drizzle-orm";

async function cleanRestoredProperties() {
  console.log("🗑️ Removing old restored properties with placeholder images...");
  
  const restoredTitles = [
    'Sunrise Farmhouse Retreat',
    'Heritage Villa Chevella', 
    'Eco Farm Stay Vikarabad',
    'Royal Retreat Medak',
    'Rustic Charm Nizamabad',
    'Lakeside Paradise Warangal',
    'Mountain View Resort Adilabad',
    'Countryside Manor Karimnagar',
    'Serene Retreat Nalgonda',
    'Vintage Estate Khammam'
  ];
  
  try {
    let deletedCount = 0;
    
    for (const title of restoredTitles) {
      const deleted = await db.delete(properties).where(eq(properties.title, title)).returning();
      if (deleted.length > 0) {
        console.log(`✅ Removed: ${title}`);
        deletedCount++;
      }
    }
    
    const remaining = await db.select().from(properties);
    console.log(`🎉 Cleanup complete! Removed ${deletedCount} properties.`);
    console.log(`📊 Remaining properties: ${remaining.length}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Error cleaning properties:", error);
    process.exit(1);
  }
}

cleanRestoredProperties();