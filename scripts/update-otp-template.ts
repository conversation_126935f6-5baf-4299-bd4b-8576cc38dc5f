import { config } from 'dotenv';
import { resolve } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function updateOTPTemplate() {
  console.log('🔄 Updating OTP Template with new DLT details...\n');

  try {
    // Check if DATABASE_URL is loaded
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found in .env.production');
      process.exit(1);
    }

    console.log('✅ Connected to production database');

    // Update the OTP template with the new DLT details
    const updatedTemplate = await db
      .update(smsTemplates)
      .set({
        name: 'BKAFARM_OTP',
        content: 'Your BookAFarm OTP is {#var#}. Please do not share it with anyone. Valid for 10 min.',
        dltTemplateId: '1207175206018864766',
        variables: ['otp_code'],
        status: 'active',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'otp_verification'))
      .returning();

    if (updatedTemplate.length === 0) {
      console.log('❌ OTP template not found');
      process.exit(1);
    }

    console.log('✅ OTP template updated successfully!');
    console.log(`📝 Template Key: ${updatedTemplate[0].key}`);
    console.log(`📝 Template Name: ${updatedTemplate[0].name}`);
    console.log(`📝 DLT ID: ${updatedTemplate[0].dltTemplateId}`);
    console.log(`📝 Content: ${updatedTemplate[0].content}`);
    console.log(`📝 Variables: ${JSON.stringify(updatedTemplate[0].variables)}`);
    console.log(`📝 Status: ${updatedTemplate[0].status}`);

    // Verify the update by fetching the template
    const verifyTemplate = await db
      .select()
      .from(smsTemplates)
      .where(eq(smsTemplates.key, 'otp_verification'))
      .limit(1);

    if (verifyTemplate.length > 0) {
      console.log('\n✅ Verification successful - Template updated in database');
    } else {
      console.log('\n❌ Verification failed - Template not found after update');
    }

    console.log('\n🎉 OTP template update completed!');

  } catch (error) {
    console.error('\n❌ Update failed:', error);
    process.exit(1);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  updateOTPTemplate()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Update failed:', error);
      process.exit(1);
    });
}

export { updateOTPTemplate };