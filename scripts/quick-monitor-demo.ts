#!/usr/bin/env tsx
import "../server/env-loader";
import { databaseManager } from '../server/utils/database';

async function quickDemo() {
  console.log('📊 Database Health Monitor - Quick Demo');
  console.log('=====================================\n');
  
  // Perform a quick health check
  const isHealthy = await databaseManager.testConnection();
  const metrics = databaseManager.getMetrics();
  const poolStats = databaseManager.getPoolStats();
  
  console.log('🏥 Current Health Status:');
  console.log(`   Connection: ${isHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
  console.log(`   Environment: ${process.env.NODE_ENV}`);
  console.log(`   Database: ${process.env.DATABASE_URL?.split('@')[1]?.split('/')[0] || 'configured'}\n`);
  
  console.log('📊 Connection Pool:');
  console.log(`   Total connections: ${poolStats.total}`);
  console.log(`   Active connections: ${metrics.activeConnections}`);
  console.log(`   Idle connections: ${metrics.idleConnections}`);
  console.log(`   Waiting queue: ${poolStats.waiting}\n`);
  
  console.log('⚡ Phase 1 Optimizations Active:');
  console.log(`   ✅ Connection timeout: 30s (was 45s)`);
  console.log(`   ✅ Idle timeout: 1 minute (was 4 minutes)`);
  console.log(`   ✅ Health checks: Every 30s (was 2 minutes)`);
  console.log(`   ✅ Max retries: 5 with exponential backoff`);
  console.log(`   ✅ Connection prewarming: Active`);
  console.log(`   ✅ Idle refresh: Active\n`);
  
  console.log('📈 Performance Metrics:');
  console.log(`   Connection errors: ${metrics.connectionErrors}`);
  if (metrics.lastError) {
    console.log(`   Last error: ${metrics.lastError.message}`);
  }
  if (metrics.lastHealthCheck) {
    const age = Math.round((Date.now() - metrics.lastHealthCheck.getTime()) / 1000);
    console.log(`   Last health check: ${age}s ago`);
  }
  
  console.log('\n💡 To see real-time monitoring, run: npm run monitor:db');
  
  await databaseManager.shutdown();
}

quickDemo().catch(console.error);