#!/usr/bin/env tsx

/**
 * Investigate Phone Duplicates Script
 * 
 * This script investigates duplicate bookings for a specific phone number.
 */

// Load environment configuration first
import '../server/env-loader';

import { db } from '../server/db';

async function investigatePhoneBookings(phone: string): Promise<void> {
  console.log(`🔍 Investigating bookings for phone ${phone}...\n`);
  
  try {
    // Find user by phone number
    const userResult = await db.execute(`
      SELECT id, full_name, phone, email 
      FROM users 
      WHERE phone = '${phone}'
    `);
    
    if (userResult.rows.length === 0) {
      console.log(`❌ No user found with phone ${phone}`);
      return;
    }
    
    const user = userResult.rows[0] as any;
    console.log(`👤 Found user: ID ${user.id}, Name: ${user.full_name}, Phone: ${user.phone}`);
    
    // Get all bookings for this user on July 30, 2025
    const bookingsResult = await db.execute(`
      SELECT 
        b.id, b.property_id, b.user_id, b.booking_date, b.booking_type,
        b.guests, b.total_price, b.status, b.created_at,
        p.title as property_name, p.location
      FROM bookings b
      JOIN properties p ON b.property_id = p.id
      WHERE b.user_id = ${user.id}
      AND b.booking_date = '2025-07-30'
      ORDER BY b.created_at ASC
    `);
    
    console.log(`\n📋 Found ${bookingsResult.rows.length} bookings for July 30, 2025:`);
    
    const duplicateGroups = new Map();
    
    for (const booking of bookingsResult.rows) {
      const b = booking as any;
      const key = `${b.property_id}-${b.booking_date}-${b.booking_type}`;
      
      if (!duplicateGroups.has(key)) {
        duplicateGroups.set(key, []);
      }
      duplicateGroups.get(key).push(b);
      
      console.log(`   • ID: ${b.id}, Property: ${b.property_name} (${b.property_id}), Type: ${b.booking_type}, Status: ${b.status}, Price: ₹${b.total_price}, Created: ${new Date(b.created_at).toLocaleString()}`);
    }
    
    // Find duplicates
    console.log(`\n🔍 Analyzing for duplicates:`);
    let duplicatesFound = false;
    const duplicatesToRemove: any[] = [];
    
    for (const [key, bookings] of duplicateGroups.entries()) {
      if (bookings.length > 1) {
        duplicatesFound = true;
        const [propertyId, date, type] = key.split('-');
        console.log(`\n⚠️ DUPLICATE GROUP: Property ${propertyId}, Date ${date}, Type ${type}`);
        console.log(`   Found ${bookings.length} duplicate bookings:`);
        
        // Keep the first booking (earliest created_at), mark others for removal
        const sortedBookings = bookings.sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        
        sortedBookings.forEach((b: any, index: number) => {
          const label = index === 0 ? '(KEEP - earliest)' : '(DUPLICATE - will be cancelled)';
          console.log(`     ${index + 1}. ID: ${b.id}, Status: ${b.status}, Created: ${new Date(b.created_at).toLocaleString()} ${label}`);
          
          if (index > 0) {
            duplicatesToRemove.push(b);
          }
        });
      }
    }
    
    if (!duplicatesFound) {
      console.log('✅ No duplicates found for this user');
      return;
    }
    
    // Report what would be cleaned up
    console.log(`\n📊 Cleanup Summary:`);
    console.log(`   • Total duplicate bookings to cancel: ${duplicatesToRemove.length}`);
    
    let totalRefund = 0;
    duplicatesToRemove.forEach((b: any) => {
      totalRefund += parseInt(b.total_price);
      console.log(`   • Booking ID ${b.id}: ₹${b.total_price} refund needed`);
    });
    
    console.log(`   • Total refund amount: ₹${totalRefund}`);
    
    return { duplicatesToRemove, totalRefund };
    
  } catch (error) {
    console.error('❌ Error investigating bookings:', error);
    throw error;
  }
}

async function main() {
  try {
    const phone = '8297373632';
    await investigatePhoneBookings(phone);
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Investigation completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { investigatePhoneBookings };