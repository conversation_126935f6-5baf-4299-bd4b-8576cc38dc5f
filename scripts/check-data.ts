import { config } from "dotenv";
import { resolve } from "path";

// Load environment variables
config({ path: resolve(process.cwd(), '.env.development') });

import { db } from "../server/db";
import { users, properties } from "../shared/schema";

async function checkData() {
  console.log("Checking database data...");
  
  try {
    const userCount = await db.select().from(users);
    console.log(`Found ${userCount.length} users`);
    
    const propertyCount = await db.select().from(properties);
    console.log(`Found ${propertyCount.length} properties`);
    
    if (propertyCount.length > 0) {
      console.log("Sample property:", JSON.stringify(propertyCount[0], null, 2));
    }
    
    process.exit(0);
  } catch (error) {
    console.error("Error checking data:", error);
    process.exit(1);
  }
}

checkData();