#!/usr/bin/env tsx

/**
 * Manual WhatsApp Testing Setup Script
 * 
 * This script sets up everything needed for manual WhatsApp booking testing
 */

import { db } from '../server/db';
import { users, properties } from '../shared/schema';
import { eq, and } from 'drizzle-orm';

async function setupManualTest() {
  console.log('🔧 Setting up manual WhatsApp booking test...\n');

  try {
    // 1. Check existing owners
    console.log('📋 Checking existing owners...');
    const owners = await db.select({
      id: users.id,
      fullName: users.fullName,
      phone: users.phone,
      whatsappNumber: users.whatsappNumber,
      email: users.email
    })
    .from(users)
    .where(eq(users.role, 'owner'));

    if (owners.length === 0) {
      console.log('❌ No owners found. Creating test owner...');
      
      // Create test owner
      const [newOwner] = await db.insert(users).values({
        username: 'test_owner_manual',
        password: 'test123',
        email: '<EMAIL>',
        fullName: 'Manual Test Owner',
        phone: '9908225188',
        whatsappNumber: '+919908225188', // This is the key field
        role: 'owner'
      }).returning();

      console.log(`✅ Created test owner: ${newOwner.fullName} (ID: ${newOwner.id})`);
      console.log(`📱 WhatsApp Number: ${newOwner.whatsappNumber}`);
    } else {
      console.log(`✅ Found ${owners.length} existing owner(s):`);
      owners.forEach(owner => {
        console.log(`   - ${owner.fullName} (ID: ${owner.id})`);
        console.log(`     📞 Phone: ${owner.phone || 'Not set'}`);
        console.log(`     📱 WhatsApp: ${owner.whatsappNumber || 'Not set'}`);
        console.log(`     📧 Email: ${owner.email}`);
        console.log('');
      });

      // Update first owner with WhatsApp number if not set
      const firstOwner = owners[0];
      if (!firstOwner.whatsappNumber) {
        console.log(`🔄 Setting WhatsApp number for ${firstOwner.fullName}...`);
        await db.update(users)
          .set({ whatsappNumber: '+919908225188' })
          .where(eq(users.id, firstOwner.id));
        console.log('✅ WhatsApp number updated!');
      }
    }

    // 2. Check properties for the owner
    console.log('\n🏡 Checking properties...');
    const ownerWithWhatsApp = await db.select()
      .from(users)
      .where(and(
        eq(users.role, 'owner'),
        eq(users.whatsappNumber, '+919908225188')
      ))
      .limit(1);

    if (ownerWithWhatsApp.length === 0) {
      throw new Error('No owner with WhatsApp number found');
    }

    const ownerId = ownerWithWhatsApp[0].id;
    const ownerProperties = await db.select()
      .from(properties)
      .where(eq(properties.ownerId, ownerId));

    if (ownerProperties.length === 0) {
      console.log('❌ No properties found. Creating test property...');
      
      const [newProperty] = await db.insert(properties).values({
        ownerId: ownerId,
        title: 'Sunset Villa Farmhouse',
        description: 'A beautiful farmhouse perfect for weekend getaways with family and friends. Features a swimming pool, garden, and all modern amenities.',
        location: 'Lonavala, Maharashtra',
        halfDayPrice: 3000,
        fullDayPrice: 5000,
        weekdayHalfDayPrice: 2500,
        weekdayFullDayPrice: 4500,
        weekendHalfDayPrice: 3500,
        weekendFullDayPrice: 6000,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['Swimming Pool', 'Garden', 'Kitchen', 'WiFi', 'Parking', 'BBQ Area'],
        images: ['sunset-villa-1.jpg', 'sunset-villa-2.jpg', 'sunset-villa-pool.jpg'],
        videos: ['sunset-villa-tour.mp4'],
        status: 'active',
        featured: true
      }).returning();

      console.log(`✅ Created test property: ${newProperty.title} (ID: ${newProperty.id})`);
    } else {
      console.log(`✅ Found ${ownerProperties.length} existing property(ies):`);
      ownerProperties.forEach(property => {
        console.log(`   - ${property.title} (ID: ${property.id})`);
        console.log(`     📍 ${property.location}`);
        console.log(`     💰 Morning: ₹${property.halfDayPrice}, Full Day: ₹${property.fullDayPrice}`);
        console.log('');
      });
    }

    console.log('🎉 Manual test setup complete!\n');
    
    // Display test instructions
    console.log('📱 MANUAL TESTING INSTRUCTIONS');
    console.log('=' .repeat(50));
    console.log('');
    console.log('🔧 SETUP STEPS:');
    console.log('');
    console.log('1. Start the server:');
    console.log('   npm run dev');
    console.log('');
    console.log('2. Expose via ngrok (for Twilio webhook):');
    console.log('   ngrok http 3000');
    console.log('   Copy the https URL (e.g., https://abc123.ngrok-free.app)');
    console.log('');
    console.log('3. Configure Twilio WhatsApp Sandbox:');
    console.log('   - Go to Twilio Console → Messaging → Try it out → Send a WhatsApp message');
    console.log('   - Set webhook URL to: https://your-ngrok-url.ngrok-free.app/api/whatsapp/webhook');
    console.log('   - Join sandbox by sending "join <code>" to +1 415 523 8886');
    console.log('');
    console.log('📱 TEST THE FLOW:');
    console.log('');
    console.log('4. From your WhatsApp, message the test owner number:');
    console.log('   +919908225188');
    console.log('');
    console.log('5. OR trigger owner flow with any message containing "owner":');
    console.log('   Send: "owner hi" to the Twilio sandbox number');
    console.log('');
    console.log('📝 EXPECTED FLOW:');
    console.log('');
    console.log('Step 1: You send: "Hi, I want to book"');
    console.log('→ Bot shows property list with prices');
    console.log('');
    console.log('Step 2: You reply: "1"');
    console.log('→ Bot shows interactive calendar');
    console.log('');
    console.log('Step 3: You reply: "25" (or any available date)');
    console.log('→ Bot asks for stay type (morning/full day)');
    console.log('');
    console.log('Step 4: You reply: "1" (morning visit)');
    console.log('→ Bot asks for guest details');
    console.log('');
    console.log('Step 5: You reply:');
    console.log('Name: Your Name');
    console.log('Phone: 9876543210');
    console.log('Guests: 6');
    console.log('→ Bot shows booking summary');
    console.log('');
    console.log('Step 6: You reply: "CONFIRM"');
    console.log('→ Bot confirms booking and adds to calendar');
    console.log('');
    console.log('🌐 VERIFY WEBSITE SYNC:');
    console.log('');
    console.log('7. Open BookAFarm.com dashboard');
    console.log('8. Check if WhatsApp booking appears in calendar');
    console.log('9. Verify real-time updates');
    console.log('');
    console.log('🧪 QUICK TEST ENDPOINTS:');
    console.log('');
    console.log('Test calendar view:');
    console.log(`GET http://localhost:3000/api/whatsapp/test-calendar/${ownerProperties[0]?.id || 1}`);
    console.log('');
    console.log('Test owner lookup:');
    console.log('GET http://localhost:3000/api/whatsapp/test-owner/1');
    console.log('');
    console.log('🔍 DEBUGGING:');
    console.log('');
    console.log('- Check server logs for WhatsApp message processing');
    console.log('- Verify webhook delivery in Twilio Console');
    console.log('- Check database for new calendar entries');
    console.log('- Monitor WebSocket connections in browser dev tools');
    console.log('');
    console.log('🆘 TROUBLESHOOTING:');
    console.log('');
    console.log('- If no response: Check ngrok URL and webhook configuration');
    console.log('- If owner not found: Verify WhatsApp number in database');
    console.log('- If calendar not syncing: Check WebSocket connection');
    console.log('- If booking fails: Check server logs for errors');

  } catch (error) {
    console.error('❌ Error setting up manual test:', error);
    process.exit(1);
  }
}

// Run setup
setupManualTest()
  .then(() => {
    console.log('\n✅ Ready for manual testing!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to setup manual test:', error);
    process.exit(1);
  });