import { config } from 'dotenv';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
config({ path: resolve(__dirname, '../.env.production') });

async function fixOTPTemplateWhitespace() {
  console.log('🔧 Adding whitespace before header in OTP template to match Jio DLT...');

  try {
    // Update only the OTP template to match Jio DLT format
    const otpResult = await db
      .update(smsTemplates)
      .set({
        content: 'Your BookAFarm OTP is {#var#}. Please do not share it with anyone. Valid for 10 min.',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'otp_verification'))
      .returning();

    if (otpResult.length > 0) {
      console.log('✅ OTP template updated successfully');
      console.log(`   New content: ${JSON.stringify(otpResult[0].content)}`);
      console.log('   📝 Note: Leading whitespace added to match Jio DLT approved template');
    } else {
      console.log('⚠️  OTP template not found');
    }

    console.log('\n🎉 OTP template whitespace fix completed!');
    console.log('📱 Template now matches Jio DLT format - should allow BKAFARM Alpha Sender ID');

  } catch (error) {
    console.error('❌ Error fixing OTP template whitespace:', error);
    process.exit(1);
  }
}

// Run the fix
fixOTPTemplateWhitespace()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  });