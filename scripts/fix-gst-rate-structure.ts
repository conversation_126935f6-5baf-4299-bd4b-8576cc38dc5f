import { sql } from 'drizzle-orm';
import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.production'), override: true });
config({ path: resolve(process.cwd(), '.env') });

import { db } from '../server/db';

async function addRateStructureColumn() {
  try {
    console.log('Checking if rate_structure column exists...');
    
    // First, check if the column already exists
    const columnCheck = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'gst_rate_configurations' 
      AND column_name = 'rate_structure'
    `);
    
    if (columnCheck.length === 0) {
      console.log('Column does not exist. Adding rate_structure column...');
      
      // Add the column
      await db.execute(sql`
        ALTER TABLE gst_rate_configurations 
        ADD COLUMN rate_structure json NOT NULL DEFAULT '{}'::json
      `);
      
      console.log('✅ rate_structure column added successfully!');
    } else {
      console.log('✅ rate_structure column already exists');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error adding rate_structure column:', error);
    process.exit(1);
  }
}

addRateStructureColumn();