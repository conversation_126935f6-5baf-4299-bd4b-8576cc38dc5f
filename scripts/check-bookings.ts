#!/usr/bin/env tsx

/**
 * Check Bookings Script
 * 
 * This script checks the current state of bookings to understand
 * what duplicates exist and test the constraints.
 */

// Load environment configuration first
import '../server/env-loader';

import { db } from '../server/db';

async function checkBookings(): Promise<void> {
  console.log('📋 Checking current bookings...\n');
  
  try {
    // Get all bookings for property 44 on July 30
    const bookings = await db.execute(`
      SELECT 
        id, property_id, user_id, booking_date, booking_type, 
        guests, total_price, status, created_at
      FROM bookings 
      WHERE property_id = 44 
      AND booking_date = '2025-07-30'
      ORDER BY created_at ASC
    `);
    
    console.log(`🏠 Found ${bookings.rows.length} bookings for property 44 on July 30:`);
    for (const booking of bookings.rows) {
      const b = booking as any;
      console.log(`   • ID: ${b.id}, User: ${b.user_id}, Type: ${b.booking_type}, Status: ${b.status}, Price: ₹${b.total_price}`);
    }

    // Check for active bookings only
    const activeBookings = await db.execute(`
      SELECT 
        id, property_id, user_id, booking_date, booking_type, 
        guests, total_price, status, created_at
      FROM bookings 
      WHERE property_id = 44 
      AND booking_date = '2025-07-30'
      AND status IN ('confirmed', 'pending_payment')
      ORDER BY created_at ASC
    `);
    
    console.log(`\n✅ Active bookings (confirmed/pending_payment): ${activeBookings.rows.length}`);
    for (const booking of activeBookings.rows) {
      const b = booking as any;
      console.log(`   • ID: ${b.id}, User: ${b.user_id}, Type: ${b.booking_type}, Status: ${b.status}`);
    }

  } catch (error) {
    console.error('❌ Error checking bookings:', error);
    throw error;
  }
}

async function testConstraintWithRealData(): Promise<void> {
  console.log('\n🧪 Testing constraint with real booking data...');
  
  try {
    // Get the first active booking to test duplication
    const activeBookings = await db.execute(`
      SELECT property_id, booking_date, booking_type, status
      FROM bookings 
      WHERE status IN ('confirmed', 'pending_payment')
      LIMIT 1
    `);
    
    if (activeBookings.rows.length === 0) {
      console.log('⚠️ No active bookings found to test with');
      return;
    }

    const testBooking = activeBookings.rows[0] as any;
    console.log(`📋 Testing duplicate of: Property ${testBooking.property_id}, Date ${testBooking.booking_date}, Type ${testBooking.booking_type}`);
    
    try {
      await db.execute(`
        INSERT INTO bookings (property_id, user_id, booking_date, booking_type, guests, total_price, status, created_at)
        VALUES (${testBooking.property_id}, 999, '${testBooking.booking_date}', '${testBooking.booking_type}', 1, 1000, 'confirmed', NOW())
      `);
      console.log('❌ Test failed: Duplicate booking was allowed!');
    } catch (error) {
      if (error instanceof Error && error.message.includes('duplicate key')) {
        console.log('✅ Test passed: Duplicate booking correctly blocked');
        console.log(`   Error: ${error.message}`);
      } else {
        console.log('⚠️ Test inconclusive:', error instanceof Error ? error.message : 'Unknown error');
      }
    }

  } catch (error) {
    console.error('❌ Error testing constraints:', error);
  }
}

async function main() {
  try {
    await checkBookings();
    await testConstraintWithRealData();
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Check completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}