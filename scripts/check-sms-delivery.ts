import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function checkSMSDelivery() {
  console.log('🔍 Checking SMS Delivery Status...\n');

  try {
    const messageSid = process.argv[2] || 'MM2ce3d2de72f3252ea22099661c4f43f9';
    
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    console.log(`📞 Checking Message SID: ${messageSid}`);
    
    // Fetch message details
    const message = await client.messages(messageSid).fetch();
    
    console.log('\n📋 Message Details:');
    console.log(`   Status: ${message.status}`);
    console.log(`   Direction: ${message.direction}`);
    console.log(`   From: ${message.from}`);
    console.log(`   To: ${message.to}`);
    console.log(`   Body: ${message.body}`);
    console.log(`   Date Created: ${message.dateCreated}`);
    console.log(`   Date Sent: ${message.dateSent}`);
    console.log(`   Date Updated: ${message.dateUpdated}`);
    console.log(`   Error Code: ${message.errorCode || 'None'}`);
    console.log(`   Error Message: ${message.errorMessage || 'None'}`);
    console.log(`   Price: ${message.price || 'Unknown'}`);
    console.log(`   Price Unit: ${message.priceUnit || 'Unknown'}`);
    console.log(`   Messaging Service SID: ${message.messagingServiceSid}`);
    console.log(`   Account SID: ${message.accountSid}`);
    
    // Check status meanings
    console.log('\n📊 Status Analysis:');
    switch (message.status) {
      case 'accepted':
        console.log('   ✅ Message accepted by Twilio');
        break;
      case 'queued':
        console.log('   ⏳ Message queued for delivery');
        break;
      case 'sending':
        console.log('   📤 Message currently being sent');
        break;
      case 'sent':
        console.log('   ✅ Message sent to carrier');
        break;
      case 'delivered':
        console.log('   ✅ Message delivered to recipient');
        break;
      case 'undelivered':
        console.log('   ❌ Message failed to deliver');
        break;
      case 'failed':
        console.log('   ❌ Message failed to send');
        break;
      case 'receiving':
        console.log('   📥 Message being received');
        break;
      case 'received':
        console.log('   ✅ Message received by Twilio');
        break;
      default:
        console.log(`   ❓ Unknown status: ${message.status}`);
    }
    
    // Provide recommendations based on status
    if (message.status === 'sent' && !message.dateSent) {
      console.log('\n💡 Recommendations:');
      console.log('   • Message sent to carrier but delivery not confirmed');
      console.log('   • Check if phone number is active and can receive SMS');
      console.log('   • Verify phone number format (+91XXXXXXXXXX)');
      console.log('   • Check if carrier supports SMS delivery');
    }
    
    if (message.errorCode) {
      console.log('\n❌ Error Details:');
      console.log(`   Error Code: ${message.errorCode}`);
      console.log(`   Error Message: ${message.errorMessage}`);
      
      // Common error codes
      const errorCodes: { [key: string]: string } = {
        '30001': 'Queue overflow - too many messages queued',
        '30002': 'Account suspended',
        '30003': 'Unreachable destination handset',
        '30004': 'Message blocked by carrier',
        '30005': 'Unknown destination handset',
        '30006': 'Landline or unreachable carrier',
        '30007': 'Carrier violation',
        '30008': 'Unknown error',
        '21211': 'Invalid To phone number',
        '21614': 'To number is not a valid mobile number',
        '21408': 'Permission to send SMS not enabled',
        '21610': 'Message cannot be sent to landline',
      };
      
      if (errorCodes[message.errorCode]) {
        console.log(`   Explanation: ${errorCodes[message.errorCode]}`);
      }
    }
    
    // Check recent messages to the same number
    console.log('\n📱 Recent Messages to Same Number:');
    const recentMessages = await client.messages.list({
      to: message.to,
      limit: 5,
      dateSentAfter: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
    });
    
    recentMessages.forEach((msg, index) => {
      console.log(`   ${index + 1}. ${msg.sid} - ${msg.status} - ${msg.dateCreated}`);
    });
    
  } catch (error: any) {
    console.error('❌ Failed to check SMS delivery:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  checkSMSDelivery()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Check failed:', error);
      process.exit(1);
    });
}

export { checkSMSDelivery };