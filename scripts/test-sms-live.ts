import { config } from 'dotenv';
import { resolve } from 'path';
import { DLTSMSService } from '../server/services/DLTSMSService';

// Set NODE_ENV to production first
process.env.NODE_ENV = 'production';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function testLiveSMS() {
  console.log('📱 Testing Live SMS Functionality...\n');

  try {
    // Get phone number from command line argument
    const phoneNumber = process.argv[2];
    
    if (!phoneNumber) {
      console.log('❌ Please provide a phone number as argument');
      console.log('Usage: npx tsx scripts/test-sms-live.ts +91**********');
      console.log('Usage: npx tsx scripts/test-sms-live.ts **********');
      return;
    }

    console.log(`📞 Testing SMS to: ${phoneNumber}`);

    // Initialize DLT SMS Service
    const dltService = new DLTSMSService();

    // Test 1: Check service health
    console.log('\n1. Checking service health...');
    const health = await dltService.getServiceHealth();
    console.log(`   - Twilio Connected: ${health.twilioConnected}`);
    console.log(`   - DLT Configured: ${health.dltConfigured}`);
    console.log(`   - Templates Available: ${health.availableTemplates.length}`);
    console.log(`   - Template List: ${health.availableTemplates.join(', ')}`);

    if (!health.twilioConnected) {
      console.log('❌ Twilio is not connected. Check your .env.production file');
      return;
    }

    // Test 2: Send OTP SMS
    console.log('\n2. Sending OTP SMS...');
    const testOTP = Math.floor(100000 + Math.random() * 900000).toString();
    console.log(`   Generated OTP: ${testOTP}`);

    const otpResult = await dltService.sendOTP(phoneNumber, testOTP);
    
    if (otpResult.success) {
      console.log(`   ✅ OTP SMS sent successfully!`);
      console.log(`   📱 Message SID: ${otpResult.messageSid}`);
      console.log(`   📞 Check your phone: ${phoneNumber}`);
      console.log(`   🔢 Expected OTP: ${testOTP}`);
    } else {
      console.log(`   ❌ OTP SMS failed: ${otpResult.error}`);
    }

    // Test 3: Send booking confirmation SMS
    console.log('\n3. Sending booking confirmation SMS...');
    const bookingResult = await dltService.sendBookingConfirmation(
      phoneNumber,
      'Test Farm Property',
      '2024-12-25'
    );

    if (bookingResult.success) {
      console.log(`   ✅ Booking SMS sent successfully!`);
      console.log(`   📱 Message SID: ${bookingResult.messageSid}`);
      console.log(`   📞 Check your phone for booking confirmation`);
    } else {
      console.log(`   ❌ Booking SMS failed: ${bookingResult.error}`);
    }

    // Test 4: Connection test
    console.log('\n4. Testing Twilio connection...');
    const connectionTest = await dltService.testConnection();
    console.log(`   Connection Status: ${connectionTest ? '✅ Connected' : '❌ Failed'}`);

    console.log('\n🎉 SMS Testing Complete!');
    console.log('\n📋 What to check:');
    console.log('1. Check your phone for received SMS messages');
    console.log('2. Verify OTP format matches your DLT template');
    console.log('3. Check SMS delivery status in Twilio console');
    console.log('4. Monitor SMS logs in your database');

  } catch (error) {
    console.error('❌ SMS test failed:', error);
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testLiveSMS()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testLiveSMS };