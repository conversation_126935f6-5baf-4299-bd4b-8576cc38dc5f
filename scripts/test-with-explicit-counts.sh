#!/bin/bash

# Enhanced test runner that always shows explicit pass/fail counts
# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

run_tests_with_counts() {
    local test_cmd="$1"
    local test_name="$2"
    
    echo -e "${YELLOW}Running $test_name...${NC}"
    echo "----------------------------------------"
    
    # Capture output and count tests
    local output
    output=$($test_cmd 2>&1)
    local exit_code=$?
    
    # Show the output
    echo "$output"
    
    # Extract test counts using reliable sed approach
    local total_line=$(echo "$output" | grep -E "Tests.*passed.*\([0-9]+\)")
    local passed=0
    local total=0
    local failed=0
    
    if [ -n "$total_line" ]; then
        # Extract passed count: "Tests  199 passed (199)"
        passed=$(echo "$total_line" | sed -n 's/.*Tests[[:space:]]*\([0-9]*\)[[:space:]]*passed.*/\1/p')
        # Extract total count from parentheses: "(199)"  
        total=$(echo "$total_line" | sed -n 's/.*(\([0-9]*\)).*/\1/p')
        # Calculate failed
        failed=$((total - passed))
        
        # Handle explicit failed count if present (overrides calculation)
        local failed_match=$(echo "$output" | grep -oE "[0-9]+ failed" | head -1)
        if [ -n "$failed_match" ]; then
            failed=$(echo "$failed_match" | grep -oE "[0-9]+")
            total=$((passed + failed))
        fi
    fi
    
    # Display explicit counts
    echo ""
    echo -e "${CYAN}📊 $test_name Results:${NC}"
    if [ $exit_code -eq 0 ]; then
        echo -e "   Status: ${GREEN}✅ PASSED${NC}"
    else
        echo -e "   Status: ${RED}❌ FAILED${NC}"
    fi
    echo -e "   ${GREEN}✅ Passed: $passed${NC}   ${RED}❌ Failed: $failed${NC}   📊 Total: $total"
    echo ""
    
    return $exit_code
}

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}🧪 Test Results with Explicit Counts${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Run unit tests
run_tests_with_counts "npm run test:unit" "Unit Tests"
UNIT_EXIT=$?

# Run integration tests
run_tests_with_counts "npm run test:integration" "Integration Tests"  
INTEGRATION_EXIT=$?

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}📊 COMPREHENSIVE SUMMARY${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

if [ $UNIT_EXIT -eq 0 ] && [ $INTEGRATION_EXIT -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL MANDATORY TESTS PASSED${NC}"
    echo -e "${GREEN}✨ Both unit and integration tests are 100% passing${NC}"
    echo -e "${GREEN}🛡️  Ready for CI/CD pipeline${NC}"
else
    echo -e "${RED}❌ SOME MANDATORY TESTS FAILED${NC}"
    echo -e "${RED}🔧 Fix failing tests before deploying${NC}"
fi

echo ""
echo -e "${BLUE}========================================${NC}"

exit $((UNIT_EXIT + INTEGRATION_EXIT))