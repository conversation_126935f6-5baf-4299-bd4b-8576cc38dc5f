import { config } from 'dotenv';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
config({ path: resolve(__dirname, '../.env.production') });

async function fixSMSTemplates() {
  console.log('🔧 Fixing SMS templates to remove leading space and header format...');

  try {
    // Fix OTP template
    const otpResult = await db
      .update(smsTemplates)
      .set({
        content: 'Your BookAFarm OTP is {#var#}. Please do not share it with anyone. Valid for 10 min.',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'otp_verification'))
      .returning();

    if (otpResult.length > 0) {
      console.log('✅ OTP template updated successfully');
      console.log(`   New content: ${otpResult[0].content}`);
    } else {
      console.log('⚠️  OTP template not found');
    }

    // Fix payment confirmation template
    const paymentResult = await db
      .update(smsTemplates)
      .set({
        content: 'Your payment of Rs. {#var#} for booking {#var#} has been received successfully. Thank you for choosing BookAFarm.',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'payment_confirmation'))
      .returning();

    if (paymentResult.length > 0) {
      console.log('✅ Payment confirmation template updated successfully');
      console.log(`   New content: ${paymentResult[0].content}`);
    } else {
      console.log('⚠️  Payment confirmation template not found');
    }

    console.log('\n🎉 SMS template fixes completed!');
    console.log('📱 Templates now use clean format without header - should allow Alpha Sender ID to work');

  } catch (error) {
    console.error('❌ Error fixing SMS templates:', error);
    process.exit(1);
  }
}

// Run the fix
fixSMSTemplates()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  });