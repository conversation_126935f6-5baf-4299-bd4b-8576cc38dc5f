import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Set NODE_ENV to production first
process.env.NODE_ENV = 'production';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function verifyTwilioCredentials() {
  console.log('🔍 Verifying Twilio Credentials...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const messagingSid = process.env.TWILIO_MESSAGING_SID;
    
    console.log('📋 Current Credentials:');
    console.log(`   Account SID: ${accountSid}`);
    console.log(`   Auth Token: ${authToken ? authToken.substring(0, 10) + '...' : 'Missing'}`);
    console.log(`   Messaging SID: ${messagingSid}`);
    
    if (!accountSid || !authToken) {
      console.log('❌ Missing Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(accountSid, authToken);
    
    console.log('\n🔐 Testing Authentication...');
    
    try {
      // Test authentication by fetching account details
      const account = await client.api.accounts(accountSid).fetch();
      console.log(`✅ Authentication successful!`);
      console.log(`   Account Name: ${account.friendlyName}`);
      console.log(`   Account Status: ${account.status}`);
      console.log(`   Account Type: ${account.type}`);
      
      // Test messaging service
      if (messagingSid) {
        console.log('\n📱 Testing Messaging Service...');
        try {
          const messagingService = await client.messaging.v1.services(messagingSid).fetch();
          console.log(`✅ Messaging service found: ${messagingService.friendlyName}`);
        } catch (msgError: any) {
          console.log(`❌ Messaging service error: ${msgError.message}`);
          console.log(`   Code: ${msgError.code}`);
        }
      }
      
    } catch (authError: any) {
      console.log(`❌ Authentication failed: ${authError.message}`);
      console.log(`   Error Code: ${authError.code}`);
      console.log(`   Status: ${authError.status}`);
      
      if (authError.code === 20003) {
        console.log('\n💡 Error 20003 Solutions:');
        console.log('   1. Check if Account SID and Auth Token are correct');
        console.log('   2. Verify credentials in Twilio Console');
        console.log('   3. Make sure the account is not suspended');
        console.log('   4. Check if you are using the correct Account SID (should start with AC)');
      }
    }
    
  } catch (error: any) {
    console.error('❌ Verification failed:', error.message);
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  verifyTwilioCredentials()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    });
}

export { verifyTwilioCredentials };