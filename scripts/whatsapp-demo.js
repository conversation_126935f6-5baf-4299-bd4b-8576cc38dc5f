#!/usr/bin/env node

/**
 * WhatsApp Booking Integration Demo
 * 
 * This script demonstrates how the WhatsApp booking integration works with BookAFarm.
 * It shows how property owners can receive direct booking commands via WhatsApp.
 */

const { WhatsAppMessageParser } = require('../server/services/WhatsAppMessageParser');

console.log('🚀 WhatsApp Booking Integration Demo for BookAFarm\n');
console.log('=' .repeat(60));

// Initialize the message parser
const parser = new WhatsAppMessageParser();

// Demo messages to test
const demoMessages = [
  "Book August 20 to August 22 for <PERSON>",
  "Book December 25 to December 28 for <PERSON>",
  "book Aug 15-17 for <PERSON>", 
  "Book September 10 to September 12 for <PERSON>",
  "Check availability for next week",
  "Cancel my booking",
  "Hello, I need help"
];

console.log('📱 Testing Natural Language Booking Commands:\n');

demoMessages.forEach((message, index) => {
  console.log(`${index + 1}. Message: "${message}"`);
  
  const result = parser.parseMessage(message);
  
  console.log(`   Intent: ${result.intent} (confidence: ${result.confidence})`);
  
  if (result.entities.dates?.checkIn && result.entities.dates?.checkOut) {
    console.log(`   Check-in: ${result.entities.dates.checkIn.toDateString()}`);
    console.log(`   Check-out: ${result.entities.dates.checkOut.toDateString()}`);
  }
  
  if (result.entities.guestName) {
    console.log(`   Guest: ${result.entities.guestName}`);
  }
  
  if (result.entities.guests) {
    console.log(`   Guest count: ${result.entities.guests}`);
  }
  
  console.log('');
});

console.log('=' .repeat(60));
console.log('🔄 End-to-End Booking Flow:\n');

console.log('1. Property owner sets up WhatsApp number mapping:');
console.log('   - Owner phone: +919876543210');
console.log('   - Property: "Sunny Farmhouse" (Property ID: 123)');
console.log('');

console.log('2. Customer sends WhatsApp message to owner:');
const bookingMessage = "Book August 20 to August 22 for John";
console.log(`   "${bookingMessage}"`);
console.log('');

console.log('3. System processes the message:');
const parsed = parser.parseMessage(bookingMessage);
console.log(`   ✅ Intent detected: ${parsed.intent} (${parsed.confidence} confidence)`);
console.log(`   ✅ Guest name extracted: ${parsed.entities.guestName}`);
console.log(`   ✅ Date range parsed: ${parsed.entities.dates?.checkIn?.toDateString()} to ${parsed.entities.dates?.checkOut?.toDateString()}`);
console.log('');

console.log('4. System workflow:');
console.log('   📞 Find owner by phone number (+919876543210)');
console.log('   🏡 Get owner\'s properties (Sunny Farmhouse)');
console.log('   📅 Check calendar availability');
console.log('   ✅ Create booking in CalendarService');
console.log('   📱 Send WhatsApp confirmation');
console.log('');

console.log('5. Confirmation message sent to customer:');
console.log('   "✅ Booking Confirmed!');
console.log('   🏡 Property: Sunny Farmhouse');
console.log('   📅 Dates: Aug 20, 2025 to Aug 22, 2025');
console.log('   👤 Guest: John');
console.log('   📱 Reference: #12345');
console.log('   🔗 View calendar: https://bookafarm.com/calendar/123"');
console.log('');

console.log('=' .repeat(60));
console.log('⚙️ Setup Requirements:\n');

console.log('Environment Variables (.env):');
console.log('   TWILIO_ACCOUNT_SID=**********************************');
console.log('   TWILIO_AUTH_TOKEN=992e4453d12380312563dfb212ccc775');
console.log('   TWILIO_WHATSAPP_NUMBER=whatsapp:+***********');
console.log('   WHATSAPP_WEBHOOK_URL=https://your-domain.com/api/whatsapp/webhook');
console.log('   WHATSAPP_WEBHOOK_TOKEN=your-webhook-verification-token');
console.log('');

console.log('Database Setup:');
console.log('   - Users table should have whatsappNumber field');
console.log('   - Map owner phone numbers to their properties');
console.log('   - Ensure CalendarService is configured for bookings');
console.log('');

console.log('Webhook Configuration:');
console.log('   - Set Twilio webhook URL: POST /api/whatsapp/webhook');
console.log('   - Webhook handles incoming WhatsApp messages');
console.log('   - Routes to OwnerWhatsAppService for processing');
console.log('');

console.log('🎉 Integration Complete! Property owners can now receive booking commands like:');
console.log('   "Book August 20 to August 22 for John"');
console.log('   and create bookings instantly in BookAFarm calendar!');