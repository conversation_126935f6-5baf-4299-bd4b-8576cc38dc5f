#!/usr/bin/env tsx

import '../server/env-loader.js';
import { propertyCacheService } from '../server/services/PropertyCacheService.js';

async function clearCache() {
  console.log('🧹 Clearing property cache...\n');
  
  try {
    // Clear all property cache
    await propertyCacheService.invalidateAllPropertyCache();
    console.log('✅ All property cache cleared');

    // Clear search cache
    await propertyCacheService.invalidateSearchCache();
    console.log('✅ Search cache cleared');

    // Clear featured properties cache
    await propertyCacheService.invalidateCache('featured-properties');
    console.log('✅ Featured properties cache cleared');

    // Clear location suggestions cache
    await propertyCacheService.invalidateCache('location-suggestions');
    console.log('✅ Location suggestions cache cleared');

    console.log('\n🎉 All caches cleared successfully!');
    console.log('ℹ️ The API should now return fresh data from the database');
    
  } catch (error) {
    console.error('❌ Error clearing cache:', error);
  }
}

// Run the cache clear
clearCache().catch(console.error);