import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function debugDLTSMS() {
  console.log('🔍 Debugging DLT SMS Parameters...\n');

  try {
    const phoneNumber = process.argv[2] || '**********';
    
    console.log(`📞 Testing SMS to: +91${phoneNumber}`);
    
    // Check environment variables
    const requiredVars = [
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN', 
      'TWILIO_MESSAGING_SID',
      'DLT_ENTITY_ID'
    ];
    
    console.log('\n📋 Environment Variables:');
    for (const varName of requiredVars) {
      const value = process.env[varName];
      console.log(`   ${varName}: ${value ? '✅ Set' : '❌ Missing'}`);
    }

    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    const testMessage = 'Your BookAFarm OTP is 123456. Please do not share it with anyone. Valid for 10 min.';
    const normalizedPhone = `+91${phoneNumber}`;
    
    console.log('\n🧪 Test 1: Basic SMS (no DLT parameters)');
    try {
      const result1 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        messagingServiceSid: process.env.TWILIO_MESSAGING_SID
      });
      console.log(`   ✅ Success: ${result1.sid}`);
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
    }

    console.log('\n🧪 Test 2: With contentSid only');
    try {
      const result2 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        messagingServiceSid: process.env.TWILIO_MESSAGING_SID,
        contentSid: 'HX8f259b1401b80e4beb66add9feb0ecc3'
      });
      console.log(`   ✅ Success: ${result2.sid}`);
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
    }

    console.log('\n🧪 Test 3: With contentSid and contentVariables');
    try {
      const result3 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        messagingServiceSid: process.env.TWILIO_MESSAGING_SID,
        contentSid: 'HX8f259b1401b80e4beb66add9feb0ecc3',
        contentVariables: JSON.stringify({otp_code: '123456'})
      });
      console.log(`   ✅ Success: ${result3.sid}`);
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
    }

    console.log('\n🧪 Test 4: Check if contentSid is a valid Content Template');
    try {
      // Try to fetch the content template
      const contentTemplate = await client.content.contents('HX8f259b1401b80e4beb66add9feb0ecc3').fetch();
      console.log(`   ✅ Content template exists: ${contentTemplate.friendlyName}`);
    } catch (error: any) {
      console.log(`   ❌ Content template not found: ${error.message}`);
      console.log('   💡 This might be the issue - DLT template ID might not be a Twilio Content SID');
    }

    console.log('\n🧪 Test 5: Try without contentSid (standard SMS)');
    try {
      const result5 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        messagingServiceSid: process.env.TWILIO_MESSAGING_SID
      });
      console.log(`   ✅ Success without DLT: ${result5.sid}`);
      console.log('   💡 Standard SMS works - issue is with DLT parameters');
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
    }

    console.log('\n📋 Analysis:');
    console.log('- If Test 1 or 5 succeeds: Basic SMS works');
    console.log('- If Test 2/3 fails: DLT template ID is not a valid Twilio Content SID');
    console.log('- The DLT template ID from telecom operator might be different from Twilio Content SID');
    
  } catch (error) {
    console.error('❌ Debug test failed:', error);
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  debugDLTSMS()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Debug failed:', error);
      process.exit(1);
    });
}

export { debugDLTSMS };