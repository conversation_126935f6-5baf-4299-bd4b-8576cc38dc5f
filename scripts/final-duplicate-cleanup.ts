#!/usr/bin/env tsx

/**
 * Final Duplicate Cleanup Script
 * 
 * This script cancels the remaining duplicate booking found for July 30, 2025.
 */

// Load environment configuration first
import '../server/env-loader';

import { db } from '../server/db';

async function cancelRemainingDuplicate(): Promise<void> {
  console.log('🧹 Cancelling remaining duplicate booking...\n');
  
  try {
    // Cancel booking ID 43 (the later duplicate)
    const bookingId = 43;
    
    console.log(`📋 Cancelling booking ID ${bookingId}...`);
    
    // Get booking details before cancelling
    const bookingResult = await db.execute(`
      SELECT 
        b.id, b.property_id, b.user_id, b.booking_date, b.booking_type,
        b.guests, b.total_price, b.status, b.created_at,
        p.title as property_name,
        u.full_name, u.phone
      FROM bookings b
      JOIN properties p ON b.property_id = p.id
      JOIN users u ON b.user_id = u.id
      WHERE b.id = ${bookingId}
    `);
    
    if (bookingResult.rows.length === 0) {
      console.log(`❌ Booking ID ${bookingId} not found`);
      return;
    }
    
    const booking = bookingResult.rows[0] as any;
    console.log(`📊 Booking details:`);
    console.log(`   • ID: ${booking.id}`);
    console.log(`   • Property: ${booking.property_name} (${booking.property_id})`);
    console.log(`   • User: ${booking.full_name} (${booking.user_id})`);
    console.log(`   • Phone: ${booking.phone}`);
    console.log(`   • Date: ${new Date(booking.booking_date).toLocaleDateString()}`);
    console.log(`   • Type: ${booking.booking_type}`);
    console.log(`   • Price: ₹${booking.total_price}`);
    console.log(`   • Status: ${booking.status}`);
    console.log(`   • Created: ${new Date(booking.created_at).toLocaleString()}`);
    
    // Cancel the booking
    const updateResult = await db.execute(`
      UPDATE bookings 
      SET status = 'cancelled' 
      WHERE id = ${bookingId}
      RETURNING id, status
    `);
    
    if (updateResult.rows.length > 0) {
      console.log(`\n✅ Booking ID ${bookingId} successfully cancelled`);
      console.log(`💰 Refund needed: ₹${booking.total_price}`);
      
      // Verify the cancellation
      const verifyResult = await db.execute(`
        SELECT COUNT(*) as active_count
        FROM bookings 
        WHERE property_id = ${booking.property_id}
        AND booking_date = '${booking.booking_date.split('T')[0]}'
        AND booking_type = '${booking.booking_type}'
        AND status IN ('confirmed', 'pending_payment')
      `);
      
      const activeCount = (verifyResult.rows[0] as any).active_count;
      console.log(`\n🔍 Verification: ${activeCount} active bookings remain for this property/date/type`);
      
      if (activeCount <= 1) {
        console.log(`✅ Duplicate resolved - only ${activeCount} active booking(s) remain`);
      } else {
        console.log(`⚠️ Warning: Still ${activeCount} active bookings for same property/date/type`);
      }
      
    } else {
      console.log(`❌ Failed to cancel booking ID ${bookingId}`);
    }
    
  } catch (error) {
    console.error('❌ Error cancelling duplicate:', error);
    throw error;
  }
}

async function main() {
  try {
    await cancelRemainingDuplicate();
    
    console.log('\n🎉 Final cleanup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Clear cache again to ensure UI shows fresh data');
    console.log('   2. Verify UI no longer shows duplicates');
    console.log('   3. Process refund for cancelled booking');
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { cancelRemainingDuplicate };