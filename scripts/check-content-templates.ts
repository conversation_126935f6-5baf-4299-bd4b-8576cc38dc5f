import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function checkContentTemplates() {
  console.log('🔍 Checking Content Templates...\n');

  try {
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    console.log('📝 Fetching all Content Templates...');
    
    // List all content templates
    const templates = await client.content.v1.contents.list();
    
    console.log(`\n📋 Found ${templates.length} Content Templates:`);
    
    if (templates.length === 0) {
      console.log('   ❌ No Content Templates found');
      console.log('   💡 You need to create a Content Template first');
    } else {
      templates.forEach((template, index) => {
        console.log(`\n   ${index + 1}. ${template.friendlyName} (${template.sid})`);
        console.log(`      Status: ${template.status}`);
        console.log(`      Language: ${template.language}`);
        console.log(`      Date Created: ${template.dateCreated}`);
        console.log(`      Date Updated: ${template.dateUpdated}`);
        console.log(`      Variables: ${template.variables ? JSON.stringify(template.variables) : 'None'}`);
        console.log(`      Types: ${template.types ? JSON.stringify(template.types) : 'None'}`);
      });
    }
    
    // Check specific template
    const targetSid = 'HX8f259b1401b80e4beb66add9feb0ecc3';
    console.log(`\n🔍 Checking specific template: ${targetSid}`);
    
    try {
      const specificTemplate = await client.content.v1.contents(targetSid).fetch();
      console.log(`   ✅ Template found: ${specificTemplate.friendlyName}`);
      console.log(`   Status: ${specificTemplate.status}`);
      console.log(`   Language: ${specificTemplate.language}`);
      console.log(`   Variables: ${JSON.stringify(specificTemplate.variables)}`);
    } catch (error: any) {
      console.log(`   ❌ Template not found: ${error.message}`);
      console.log(`   💡 This explains error 21655 - ContentSid is invalid`);
    }
    
    // Show how to create a new template
    console.log('\n💡 Recommendations:');
    if (templates.length === 0) {
      console.log('   1. Go to Twilio Console → Content Template Builder');
      console.log('   2. Create a new SMS template');
      console.log('   3. Use the template content: "Your BookAFarm OTP is {{1}}. Please do not share it with anyone. Valid for 10 min."');
      console.log('   4. Add variable: otp_code');
      console.log('   5. Save and get the new ContentSid');
    } else {
      console.log('   ✅ You have Content Templates available');
      console.log('   📝 Update your database with a valid ContentSid from the list above');
    }
    
    // Check if we can create a template programmatically
    console.log('\n🔧 Alternative: Create template programmatically');
    console.log('   Run: npx tsx scripts/create-content-template.ts');
    
  } catch (error: any) {
    console.error('❌ Failed to check content templates:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  checkContentTemplates()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Check failed:', error);
      process.exit(1);
    });
}

export { checkContentTemplates };