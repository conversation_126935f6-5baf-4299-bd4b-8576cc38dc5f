import { config } from 'dotenv';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
config({ path: resolve(__dirname, '../.env.production') });

async function simplifyOTPTemplate() {
  console.log('🔧 Simplifying OTP template - removing header for better Alpha Sender ID compatibility...');

  try {
    // Update OTP template to simple format
    const otpResult = await db
      .update(smsTemplates)
      .set({
        content: 'Your BookAFarm OTP is {#var#}. Please do not share it with anyone. Valid for 10 min.',
        updatedAt: new Date()
      })
      .where(eq(smsTemplates.key, 'otp_verification'))
      .returning();

    if (otpResult.length > 0) {
      console.log('✅ OTP template simplified successfully');
      console.log(`   New content: ${otpResult[0].content}`);
      console.log('   📝 Note: Clean format should work better with BKAFARM Alpha Sender ID');
    } else {
      console.log('⚠️  OTP template not found');
    }

    console.log('\n🎉 OTP template simplification completed!');
    console.log('📱 Template now uses clean format - should work with BKAFARM sender ID');

  } catch (error) {
    console.error('❌ Error simplifying OTP template:', error);
    process.exit(1);
  }
}

// Run the simplification
simplifyOTPTemplate()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  });