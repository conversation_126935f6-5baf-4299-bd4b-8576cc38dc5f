import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function testBasicSMS() {
  console.log('🔍 Testing Basic SMS (no Content Template)...\n');

  try {
    const phoneNumber = process.argv[2] || '**********';
    
    console.log(`📞 Testing SMS to: +91${phoneNumber}`);
    
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    const testMessage = 'Your BookAFarm OTP is 123456. Please do not share it with anyone. Valid for 10 min.';
    const normalizedPhone = `+91${phoneNumber}`;
    
    console.log('\n🧪 Test 1: Basic SMS via Messaging Service (no ContentSid)');
    try {
      const result1 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        messagingServiceSid: process.env.TWILIO_MESSAGING_SID
      });
      console.log(`   ✅ Success: ${result1.sid}`);
      
      // Wait a moment then check status
      setTimeout(async () => {
        try {
          const status = await client.messages(result1.sid).fetch();
          console.log(`   Status: ${status.status}`);
          console.log(`   Error: ${status.errorCode || 'None'}`);
        } catch (e) {
          console.log(`   ❌ Could not fetch status: ${e}`);
        }
      }, 2000);
      
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
    }

    console.log('\n🧪 Test 2: Direct SMS with purchased phone number');
    try {
      const result2 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        from: '+12692302231' // The phone number we just bought
      });
      console.log(`   ✅ Success: ${result2.sid}`);
      
      // Wait a moment then check status
      setTimeout(async () => {
        try {
          const status = await client.messages(result2.sid).fetch();
          console.log(`   Status: ${status.status}`);
          console.log(`   Error: ${status.errorCode || 'None'}`);
        } catch (e) {
          console.log(`   ❌ Could not fetch status: ${e}`);
        }
      }, 2000);
      
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
    }

    console.log('\n⏳ Waiting 5 seconds to check delivery status...');
    
  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testBasicSMS()
    .then(() => {
      // Keep process alive for status checks
      setTimeout(() => process.exit(0), 6000);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testBasicSMS };