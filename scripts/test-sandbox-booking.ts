#!/usr/bin/env -S npx tsx

/**
 * Test WhatsApp Booking through Twilio Sandbox
 * This simulates the booking flow for testing
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.development') });

async function testSandboxBooking() {
  const WEBHOOK_URL = 'http://localhost:5000/api/whatsapp/webhook';
  
  console.log('🧪 Testing WhatsApp Booking Flow');
  console.log('==================================\n');

  // Simulate messages from customer to <PERSON><PERSON>'s properties
  const messages = [
    {
      step: 'Initial Contact',
      body: 'Hi, I want to book MM farm',
      from: 'whatsapp:+************',
      to: 'whatsapp:+************' // <PERSON><PERSON>'s WhatsApp
    },
    {
      step: 'Select Property', 
      body: '1',
      from: 'whatsapp:+************',
      to: 'whatsapp:+************'
    },
    {
      step: 'Select Date',
      body: 'Tomorrow, full day',
      from: 'whatsapp:+************',
      to: 'whatsapp:+************'
    },
    {
      step: 'Guest Details',
      body: 'Name: Test Customer\nPhone: 9999999999\nGuests: 4',
      from: 'whatsapp:+************',
      to: 'whatsapp:+************'
    },
    {
      step: 'Confirm Booking',
      body: 'CONFIRM',
      from: 'whatsapp:+************',
      to: 'whatsapp:+************'
    }
  ];

  for (const [index, msg] of messages.entries()) {
    console.log(`Step ${index + 1}: ${msg.step}`);
    console.log('----------------------------');
    
    try {
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          AccountSid: 'ACtest',
          Body: msg.body,
          From: msg.from,
          To: msg.to,
          MessageSid: `TEST_${Date.now()}_${index}`
        })
      });

      if (response.ok) {
        console.log(`✅ ${msg.step} - Success`);
        console.log(`   Message: "${msg.body}"`);
      } else {
        console.log(`❌ ${msg.step} - Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${msg.step} - Error:`, error);
    }

    // Wait between messages
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('');
  }

  console.log('\n📊 Test Complete!');
  console.log('=================');
  console.log('Check your server logs to see:');
  console.log('• Owner-specific routing to Ayesha');
  console.log('• MM farm property listing');
  console.log('• Booking flow progression');
  console.log('• Confirmation messages\n');
}

testSandboxBooking().catch(console.error);