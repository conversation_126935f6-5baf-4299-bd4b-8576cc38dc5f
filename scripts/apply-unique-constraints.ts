#!/usr/bin/env tsx

/**
 * Apply Unique Constraints Script
 * 
 * This script applies the unique constraints to prevent duplicate bookings
 * by executing raw SQL commands.
 */

// Load environment configuration first
import '../server/env-loader';

import { db } from '../server/db';

async function applyUniqueConstraints(): Promise<void> {
  console.log('🔒 Applying unique constraints to prevent duplicate bookings...\n');
  
  try {
    // Apply the first unique constraint for general duplicate prevention
    console.log('📋 Creating bookings_no_duplicates_idx...');
    await db.execute(`
      CREATE UNIQUE INDEX IF NOT EXISTS bookings_no_duplicates_idx 
      ON bookings (property_id, booking_date, booking_type) 
      WHERE status IN ('confirmed', 'pending_payment')
    `);
    console.log('✅ bookings_no_duplicates_idx created successfully');

    // Apply the second unique constraint for full_day exclusivity
    console.log('📋 Creating bookings_fullday_exclusive_idx...');
    await db.execute(`
      CREATE UNIQUE INDEX IF NOT EXISTS bookings_fullday_exclusive_idx 
      ON bookings (property_id, booking_date) 
      WHERE booking_type = 'full_day' AND status IN ('confirmed', 'pending_payment')
    `);
    console.log('✅ bookings_fullday_exclusive_idx created successfully');

    // Verify the constraints were created
    console.log('\n🔍 Verifying constraints...');
    const indexes = await db.execute(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'bookings' 
      AND indexname IN ('bookings_no_duplicates_idx', 'bookings_fullday_exclusive_idx')
    `);
    
    console.log(`📊 Found ${indexes.rows.length} unique constraints:`);
    for (const index of indexes.rows) {
      console.log(`   • ${(index as any).indexname}`);
    }

    console.log('\n✅ Unique constraints applied successfully!');
    console.log('\n🔒 Protection now active:');
    console.log('   • No duplicate bookings for same property/date/type');
    console.log('   • Full-day bookings block all other bookings on that date');
    console.log('   • Morning bookings blocked if full-day exists');
    
  } catch (error) {
    console.error('❌ Error applying unique constraints:', error);
    throw error;
  }
}

async function testConstraints(): Promise<void> {
  console.log('\n🧪 Testing constraints with sample data...');
  
  try {
    // Test 1: Try to create a duplicate booking (should fail)
    console.log('Test 1: Attempting to create duplicate booking...');
    try {
      await db.execute(`
        INSERT INTO bookings (property_id, user_id, booking_date, booking_type, guests, total_price, status, created_at)
        VALUES (44, 13, '2025-07-30', 'morning', 1, 3415, 'confirmed', NOW())
      `);
      console.log('❌ Test failed: Duplicate booking was allowed');
    } catch (error) {
      if (error instanceof Error && error.message.includes('duplicate key')) {
        console.log('✅ Test passed: Duplicate booking correctly blocked');
      } else {
        console.log('⚠️ Test inconclusive:', error instanceof Error ? error.message : 'Unknown error');
      }
    }

    console.log('\n🎉 Constraint testing completed');
    
  } catch (error) {
    console.error('❌ Error testing constraints:', error);
  }
}

async function main() {
  try {
    await applyUniqueConstraints();
    await testConstraints();
    
    console.log('\n🎉 All operations completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Deploy the updated booking logic');
    console.log('   2. Monitor for booking conflicts in logs');
    console.log('   3. Test the booking flow in the UI');
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Script completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { applyUniqueConstraints, testConstraints };