#!/usr/bin/env tsx
/**
 * Database Stability Test Script
 * 
 * Tests the robustness of the new database connection management
 * and error boundary systems under various stress conditions.
 */

import { performance } from 'perf_hooks';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  details: string;
  metrics?: any;
}

class StabilityTester {
  private results: TestResult[] = [];
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:5000') {
    this.baseUrl = baseUrl;
  }

  private async runTest(
    name: string, 
    testFn: () => Promise<any>,
    expectedToPass: boolean = true
  ): Promise<void> {
    const startTime = performance.now();
    
    try {
      const result = await testFn();
      const duration = performance.now() - startTime;
      
      this.results.push({
        name,
        status: expectedToPass ? 'PASS' : 'FAIL',
        duration,
        details: expectedToPass ? 'Test completed successfully' : 'Test unexpectedly passed',
        metrics: result
      });
    } catch (error) {
      const duration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.results.push({
        name,
        status: expectedToPass ? 'FAIL' : 'PASS',
        duration,
        details: expectedToPass ? `Test failed: ${errorMessage}` : 'Test failed as expected'
      });
    }
  }

  private async makeRequest(endpoint: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}${endpoint}`);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  async testDatabaseHealth(): Promise<void> {
    console.log('🔍 Testing database health monitoring...');

    await this.runTest('Database health endpoint', async () => {
      return this.makeRequest('/api/health/database');
    });

    await this.runTest('Connection pool metrics', async () => {
      return this.makeRequest('/api/health/database/pool');
    });

    await this.runTest('Basic health check', async () => {
      return this.makeRequest('/api/health');
    });
  }

  async testErrorBoundaries(): Promise<void> {
    console.log('🛡️ Testing error boundaries...');

    // Test invalid phone number (should be handled gracefully)
    await this.runTest('Invalid input validation', async () => {
      const response = await fetch(`${this.baseUrl}/api/auth/otp/send-otp`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          identifier: 'invalid_phone',
          type: 'sms'
        })
      });

      if (response.status !== 400) {
        throw new Error(`Expected 400, got ${response.status}`);
      }
      
      return response.json();
    });

    // Test rate limiting (should be handled gracefully)
    await this.runTest('Rate limiting error boundary', async () => {
      // Make multiple requests to trigger rate limiting
      const requests = Array(10).fill(null).map(() =>
        fetch(`${this.baseUrl}/api/auth/otp/send-otp`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            identifier: '<EMAIL>',
            type: 'email'
          })
        })
      );

      const responses = await Promise.all(requests);
      const rateLimited = responses.some(r => r.status === 429);
      
      if (!rateLimited) {
        throw new Error('Rate limiting not triggered');
      }
      
      return { rateLimited: true, statusCodes: responses.map(r => r.status) };
    });
  }

  async testConnectionResilience(): Promise<void> {
    console.log('💪 Testing connection resilience...');

    // Test multiple concurrent database operations
    await this.runTest('Concurrent database operations', async () => {
      const operations = Array(20).fill(null).map(async (_, i) => {
        try {
          const response = await this.makeRequest('/api/health/database');
          return { operation: i, success: true, data: response };
        } catch (error) {
          return { operation: i, success: false, error: (error as Error).message };
        }
      });

      const results = await Promise.all(operations);
      const successCount = results.filter(r => r.success).length;
      const successRate = successCount / results.length;

      if (successRate < 0.8) { // At least 80% should succeed
        throw new Error(`Low success rate: ${successRate * 100}%`);
      }

      return { 
        totalOperations: results.length, 
        successful: successCount, 
        successRate: successRate * 100 
      };
    });
  }

  async testRecovery(): Promise<void> {
    console.log('🔄 Testing error recovery...');

    // Test that the system can recover from errors
    await this.runTest('Error recovery capabilities', async () => {
      // First, try to cause some errors
      const errorRequests = Array(5).fill(null).map(() =>
        fetch(`${this.baseUrl}/api/auth/otp/send-otp`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            identifier: 'invalid',
            type: 'invalid'
          })
        })
      );

      await Promise.all(errorRequests);

      // Then test that normal operations still work
      const response = await fetch(`${this.baseUrl}/api/health`);
      if (!response.ok) {
        throw new Error('System did not recover from errors');
      }

      const healthData = await response.json();
      return { recovered: true, health: healthData };
    });
  }

  async testPerformance(): Promise<void> {
    console.log('⚡ Testing performance under load...');

    await this.runTest('Response time under load', async () => {
      const startTime = performance.now();
      
      // Make multiple requests simultaneously
      const requests = Array(50).fill(null).map(() =>
        this.makeRequest('/api/health')
      );

      const results = await Promise.all(requests);
      const totalTime = performance.now() - startTime;
      const avgResponseTime = totalTime / requests.length;

      if (avgResponseTime > 1000) { // More than 1 second average
        throw new Error(`Slow response time: ${avgResponseTime.toFixed(2)}ms average`);
      }

      return {
        totalRequests: requests.length,
        totalTime: totalTime.toFixed(2),
        averageResponseTime: avgResponseTime.toFixed(2),
        successful: results.length
      };
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting stability tests...\n');

    try {
      await this.testDatabaseHealth();
      await this.testErrorBoundaries();
      await this.testConnectionResilience();
      await this.testRecovery();
      await this.testPerformance();
    } catch (error) {
      console.error('❌ Test runner error:', error);
    }

    this.printResults();
  }

  private printResults(): void {
    console.log('\n📊 Stability Test Results:');
    console.log('='.repeat(70));

    let passed = 0;
    let failed = 0;
    let totalDuration = 0;

    for (const result of this.results) {
      const statusIcon = result.status === 'PASS' ? '✅' : '❌';
      const durationMs = Math.round(result.duration);
      
      console.log(`${statusIcon} ${result.name} (${durationMs}ms)`);
      console.log(`   └─ ${result.details}`);
      
      if (result.metrics) {
        console.log(`   └─ Metrics:`, JSON.stringify(result.metrics, null, 6));
      }
      console.log('');

      if (result.status === 'PASS') {
        passed++;
      } else {
        failed++;
      }
      
      totalDuration += result.duration;
    }

    console.log('='.repeat(70));
    console.log(`📈 Summary: ${passed} passed, ${failed} failed`);
    console.log(`⏱️  Total duration: ${Math.round(totalDuration)}ms`);
    console.log(`🎯 Success rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Some stability tests failed. Review the implementation.');
      process.exit(1);
    } else {
      console.log('\n✅ All stability tests passed!');
      console.log('🎉 Database connection management and error boundaries are working correctly!');
    }
  }
}

// Check if server is running
async function checkServerHealth(): Promise<boolean> {
  try {
    const response = await fetch('http://localhost:5000/api/health');
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServerHealth();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on localhost:5000');
    console.log('Please start the server first: npm run start:prod');
    process.exit(1);
  }
  
  console.log('🔧 Database Stability Test Suite');
  console.log('Testing connection pooling, error boundaries, and resilience\n');
  
  const tester = new StabilityTester();
  await tester.runAllTests();
}

main().catch(console.error);