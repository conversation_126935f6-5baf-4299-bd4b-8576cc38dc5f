#!/bin/bash

echo "=== Testing Owner-Specific WhatsApp Booking Flow ==="
echo ""

# Test 1: Simulate customer messaging owner's WhatsApp number
echo "1. Testing customer message to owner's WhatsApp:"
curl -s -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "AccountSid": "ACc1f08eb6e0ffedb65a746706d7d2d62f",
    "Body": "Hi, I want to book your farmhouse",
    "From": "whatsapp:+************",
    "To": "whatsapp:+************",
    "MessageSid": "SM_OWNER_TEST_1"
  }' -w "\nStatus: %{http_code}\n"

echo ""
echo "2. Testing property selection:"
sleep 2
curl -s -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "AccountSid": "ACc1f08eb6e0ffedb65a746706d7d2d62f",
    "Body": "1",
    "From": "whatsapp:+************",
    "To": "whatsapp:+************",
    "MessageSid": "SM_OWNER_TEST_2"
  }' -w "\nStatus: %{http_code}\n"

echo ""
echo "3. Testing date selection:"
sleep 2
curl -s -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "AccountSid": "ACc1f08eb6e0ffedb65a746706d7d2d62f",
    "Body": "Dec 25, full day",
    "From": "whatsapp:+************",
    "To": "whatsapp:+************",
    "MessageSid": "SM_OWNER_TEST_3"
  }' -w "\nStatus: %{http_code}\n"

echo ""
echo "4. Testing guest details:"
sleep 2
curl -s -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "AccountSid": "ACc1f08eb6e0ffedb65a746706d7d2d62f",
    "Body": "Name: Test Customer\nPhone: **********\nGuests: 6",
    "From": "whatsapp:+************",
    "To": "whatsapp:+************",
    "MessageSid": "SM_OWNER_TEST_4"
  }' -w "\nStatus: %{http_code}\n"

echo ""
echo "5. Testing booking confirmation:"
sleep 2
curl -s -X POST http://localhost:5000/api/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "AccountSid": "ACc1f08eb6e0ffedb65a746706d7d2d62f",
    "Body": "CONFIRM",
    "From": "whatsapp:+************",
    "To": "whatsapp:+************",
    "MessageSid": "SM_OWNER_TEST_5"
  }' -w "\nStatus: %{http_code}\n"

echo ""
echo "✅ Owner-specific flow test completed!"
echo ""
echo "Check server logs to see:"
echo "• 'Routing to owner-specific flow'"
echo "• Owner properties being listed"
echo "• Flow progression through all steps"
echo "• Calendar booking creation"
echo "• Confirmations sent to both parties"