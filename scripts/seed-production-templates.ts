import { config } from 'dotenv';
import { resolve } from 'path';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

// Import after loading env vars
import { seedSMSTemplates, listExistingTemplates } from './seed-sms-templates';

async function runProductionSeeding() {
  console.log('🌱 Seeding SMS Templates in Production...\n');

  try {
    // Check if DATABASE_URL is loaded
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found in .env.production');
      process.exit(1);
    }

    console.log('✅ Connected to production database');
    
    // List existing templates first
    console.log('\n📋 Current templates in database:');
    await listExistingTemplates();

    // Seed templates
    console.log('\n🌱 Seeding new templates...');
    await seedSMSTemplates();

    console.log('\n📋 Final template list:');
    await listExistingTemplates();

    console.log('\n✅ Production template seeding completed!');
    console.log('\n🔄 Next step: Test SMS functionality');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  runProductionSeeding();
}

export { runProductionSeeding };