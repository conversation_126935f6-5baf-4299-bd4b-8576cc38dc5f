#!/usr/bin/env tsx

import '../server/env-loader.js';
import { db } from '../server/db.js';
import { users, properties, bookings, reviews } from '../shared/schema.js';

async function checkDatabase() {
  console.log('🔍 Checking database state...\n');
  
  try {
    // Check users
    const allUsers = await db.select().from(users);
    console.log(`👤 Users in database: ${allUsers.length}`);
    if (allUsers.length > 0) {
      console.log('   Users found:');
      allUsers.forEach(user => {
        console.log(`   - ${user.email} (${user.role}) - ${user.fullName}`);
      });
    }
    console.log();

    // Check properties
    const allProperties = await db.select().from(properties);
    console.log(`🏠 Properties in database: ${allProperties.length}`);
    if (allProperties.length > 0) {
      console.log('   Properties found:');
      allProperties.forEach(property => {
        console.log(`   - ${property.title} (${property.location}) - Owner ID: ${property.ownerId}`);
      });
    }
    console.log();

    // Check bookings
    const allBookings = await db.select().from(bookings);
    console.log(`📅 Bookings in database: ${allBookings.length}`);
    console.log();

    // Check reviews
    const allReviews = await db.select().from(reviews);
    console.log(`⭐ Reviews in database: ${allReviews.length}`);
    console.log();

    // Summary
    console.log('📊 Database Summary:');
    console.log(`   Users: ${allUsers.length}`);
    console.log(`   Properties: ${allProperties.length}`);
    console.log(`   Bookings: ${allBookings.length}`);
    console.log(`   Reviews: ${allReviews.length}`);
    
    if (allUsers.length === 0) {
      console.log('\n⚠️  No users found. The database appears to be empty.');
    } else if (allProperties.length === 0) {
      console.log('\n⚠️  Users found but no properties. Missing property data is the issue.');
    }

  } catch (error) {
    console.error('❌ Error checking database:', error);
  }
}

// Run the check
checkDatabase().catch(console.error);