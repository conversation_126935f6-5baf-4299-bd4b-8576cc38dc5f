import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function checkAccountStatus() {
  console.log('🔍 Checking Twilio Account Status...\n');

  try {
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    console.log(`📞 Checking Account: ${process.env.TWILIO_ACCOUNT_SID}`);
    
    // Fetch account details
    const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    
    console.log('\n📋 Account Details:');
    console.log(`   Account SID: ${account.sid}`);
    console.log(`   Friendly Name: ${account.friendlyName}`);
    console.log(`   Status: ${account.status}`);
    console.log(`   Type: ${account.type}`);
    console.log(`   Date Created: ${account.dateCreated}`);
    console.log(`   Date Updated: ${account.dateUpdated}`);
    console.log(`   Auth Token: ${account.authToken ? 'Present' : 'Missing'}`);
    
    // Check balance
    console.log('\n💰 Account Balance:');
    try {
      const balance = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).balance.fetch();
      console.log(`   Current Balance: ${balance.balance} ${balance.currency}`);
    } catch (error: any) {
      console.log(`   ❌ Could not fetch balance: ${error.message}`);
    }

    // Check trial status
    console.log('\n🔍 Trial Status Analysis:');
    if (account.type === 'Trial') {
      console.log('   ❌ Account is still on TRIAL plan');
      console.log('   💡 You need to upgrade to Full account to use Alpha Sender IDs');
    } else if (account.type === 'Full') {
      console.log('   ✅ Account is upgraded to FULL plan');
    } else {
      console.log(`   ❓ Account type: ${account.type}`);
    }
    
    // Check capabilities
    console.log('\n📱 Testing Capabilities:');
    try {
      // Try to list available phone numbers for purchase
      const availableNumbers = await client.availablePhoneNumbers('US').local.list({ limit: 1 });
      console.log(`   📞 Can browse phone numbers: ${availableNumbers.length > 0 ? 'Yes' : 'No'}`);
    } catch (error: any) {
      console.log(`   ❌ Cannot browse phone numbers: ${error.message}`);
    }

    // Check existing phone numbers
    console.log('\n📞 Owned Phone Numbers:');
    const ownedNumbers = await client.incomingPhoneNumbers.list({ limit: 10 });
    if (ownedNumbers.length === 0) {
      console.log('   ❌ No phone numbers owned');
      console.log('   💡 Consider buying a phone number for SMS');
    } else {
      ownedNumbers.forEach((num, index) => {
        console.log(`   ${index + 1}. ${num.phoneNumber} (${num.friendlyName})`);
        console.log(`      SMS: ${num.capabilities.sms}, Voice: ${num.capabilities.voice}, MMS: ${num.capabilities.mms}`);
      });
    }

    // Recommendations
    console.log('\n💡 Recommendations:');
    if (account.type === 'Trial') {
      console.log('   🚨 URGENT: Account upgrade not complete');
      console.log('   📞 Go to Twilio Console → Billing → Upgrade Account');
      console.log('   💳 Ensure payment method is added and verified');
    } else {
      console.log('   ✅ Account is properly upgraded');
      if (ownedNumbers.length === 0) {
        console.log('   📞 Consider buying a phone number for backup SMS capability');
      }
    }
    
  } catch (error: any) {
    console.error('❌ Failed to check account status:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  checkAccountStatus()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Check failed:', error);
      process.exit(1);
    });
}

export { checkAccountStatus };