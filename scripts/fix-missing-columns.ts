#!/usr/bin/env tsx

import '../server/env-loader.js';
import { db } from '../server/db.js';
import { sql } from 'drizzle-orm';

async function fixMissingColumns() {
  console.log('🔧 Adding missing pricing columns to properties table...\n');
  
  try {
    // Add missing columns if they don't exist
    const queries = [
      "ALTER TABLE properties ADD COLUMN IF NOT EXISTS weekday_half_day_price DOUBLE PRECISION",
      "ALTER TABLE properties ADD COLUMN IF NOT EXISTS weekday_full_day_price DOUBLE PRECISION", 
      "ALTER TABLE properties ADD COLUMN IF NOT EXISTS weekend_half_day_price DOUBLE PRECISION",
      "ALTER TABLE properties ADD COLUMN IF NOT EXISTS weekend_full_day_price DOUBLE PRECISION"
    ];

    for (const query of queries) {
      console.log(`Executing: ${query}`);
      await db.execute(sql.raw(query));
      console.log('✅ Success\n');
    }

    // Check if we also need the videos column
    console.log('Checking for videos column...');
    try {
      await db.execute(sql.raw("ALTER TABLE properties ADD COLUMN IF NOT EXISTS videos JSONB DEFAULT '[]'::JSONB NOT NULL"));
      console.log('✅ Videos column added if missing\n');
    } catch (error) {
      console.log('ℹ️ Videos column already exists\n');
    }

    // Check if we need latitude/longitude columns
    console.log('Checking for location columns...');
    try {
      await db.execute(sql.raw("ALTER TABLE properties ADD COLUMN IF NOT EXISTS latitude DOUBLE PRECISION"));
      await db.execute(sql.raw("ALTER TABLE properties ADD COLUMN IF NOT EXISTS longitude DOUBLE PRECISION"));
      console.log('✅ Location columns added if missing\n');
    } catch (error) {
      console.log('ℹ️ Location columns already exist\n');
    }

    console.log('🎉 Database schema update completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
  }
}

// Run the fix
fixMissingColumns().catch(console.error);