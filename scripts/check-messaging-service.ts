import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function checkMessagingService() {
  console.log('🔍 Checking Messaging Service Configuration...\n');

  try {
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    if (!process.env.TWILIO_MESSAGING_SID) {
      console.log('❌ Missing TWILIO_MESSAGING_SID');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    console.log(`📞 Checking Messaging Service: ${process.env.TWILIO_MESSAGING_SID}`);
    
    // Fetch messaging service details
    const messagingService = await client.messaging.v1.services(process.env.TWILIO_MESSAGING_SID).fetch();
    
    console.log('\n📋 Messaging Service Details:');
    console.log(`   Service SID: ${messagingService.sid}`);
    console.log(`   Friendly Name: ${messagingService.friendlyName}`);
    console.log(`   Inbound Request URL: ${messagingService.inboundRequestUrl || 'None'}`);
    console.log(`   Inbound Method: ${messagingService.inboundMethod || 'None'}`);
    console.log(`   Fallback URL: ${messagingService.fallbackUrl || 'None'}`);
    console.log(`   Status Callback URL: ${messagingService.statusCallback || 'None'}`);
    console.log(`   Sticky Sender: ${messagingService.stickySender}`);
    console.log(`   Smart Encoding: ${messagingService.smartEncoding}`);
    console.log(`   MMS Converter: ${messagingService.mmsConverter}`);
    console.log(`   Fallback to Long Code: ${messagingService.fallbackToLongCode}`);
    console.log(`   Area Code Geomatch: ${messagingService.areaCodeGeomatch}`);
    console.log(`   Validity Period: ${messagingService.validityPeriod}`);
    console.log(`   Synchronous Validation: ${messagingService.synchronousValidation}`);
    console.log(`   Usecase: ${messagingService.usecase || 'None'}`);
    console.log(`   Date Created: ${messagingService.dateCreated}`);
    console.log(`   Date Updated: ${messagingService.dateUpdated}`);
    
    // Check phone numbers associated with the messaging service
    console.log('\n📱 Phone Numbers in Messaging Service:');
    const phoneNumbers = await client.messaging.v1.services(process.env.TWILIO_MESSAGING_SID).phoneNumbers.list();
    
    if (phoneNumbers.length === 0) {
      console.log('   ❌ NO PHONE NUMBERS FOUND');
      console.log('   💡 This is the cause of error 21703!');
      console.log('   💡 You need to add phone numbers to your messaging service');
    } else {
      phoneNumbers.forEach((phoneNumber, index) => {
        console.log(`   ${index + 1}. ${phoneNumber.phoneNumber} (${phoneNumber.sid})`);
        console.log(`      Country Code: ${phoneNumber.countryCode}`);
        console.log(`      Capabilities: SMS=${phoneNumber.capabilities.sms}, MMS=${phoneNumber.capabilities.mms}, Voice=${phoneNumber.capabilities.voice}`);
      });
    }
    
    // Check short codes
    console.log('\n📧 Short Codes in Messaging Service:');
    const shortCodes = await client.messaging.v1.services(process.env.TWILIO_MESSAGING_SID).shortCodes.list();
    
    if (shortCodes.length === 0) {
      console.log('   ❌ No short codes found');
    } else {
      shortCodes.forEach((shortCode, index) => {
        console.log(`   ${index + 1}. ${shortCode.shortCode} (${shortCode.sid})`);
        console.log(`      Country Code: ${shortCode.countryCode}`);
      });
    }
    
    // Check alpha senders
    console.log('\n📮 Alpha Senders in Messaging Service:');
    const alphaSenders = await client.messaging.v1.services(process.env.TWILIO_MESSAGING_SID).alphaSenders.list();
    
    if (alphaSenders.length === 0) {
      console.log('   ❌ No alpha senders found');
    } else {
      alphaSenders.forEach((alphaSender, index) => {
        console.log(`   ${index + 1}. ${alphaSender.alphaSender} (${alphaSender.sid})`);
        console.log(`      Capabilities: SMS=${alphaSender.capabilities.sms}, MMS=${alphaSender.capabilities.mms}`);
      });
    }
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    if (phoneNumbers.length === 0 && shortCodes.length === 0 && alphaSenders.length === 0) {
      console.log('   🚨 CRITICAL: No sending numbers configured in messaging service!');
      console.log('   📞 Add a phone number or short code to your messaging service');
      console.log('   🇮🇳 For India SMS, you may need an alpha sender ID');
      console.log('   🔧 Go to Twilio Console > Messaging > Services > Your Service > Senders');
    } else {
      console.log('   ✅ Messaging service has senders configured');
      console.log('   🔍 Check if the senders support SMS to India (+91 numbers)');
    }
    
  } catch (error: any) {
    console.error('❌ Failed to check messaging service:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  checkMessagingService()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Check failed:', error);
      process.exit(1);
    });
}

export { checkMessagingService };