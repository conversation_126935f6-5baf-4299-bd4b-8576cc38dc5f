import { config } from "dotenv";
import { resolve } from "path";

// Load environment variables
config({ path: resolve(process.cwd(), '.env.development') });

import { db } from "../server/db";
import { users, properties } from "../shared/schema";
import { eq } from "drizzle-orm";

async function createTestProperty() {
  console.log("Creating a test property...");
  
  try {
    // Get any owner
    const owners = await db.select().from(users).where(eq(users.role, 'owner')).limit(1);
    
    if (owners.length === 0) {
      console.log("No owners found! Creating a test owner...");
      const [testOwner] = await db.insert(users).values({
        username: 'testowner',
        password: '$2b$10$RirF4t40o54TmRzHERsS5upJQnAa8iDOpvWsBeBhar3PpKW62WAU6',
        email: '<EMAIL>',
        fullName: 'Test Owner',
        phone: '+919999999999',
        address: 'Test Address',
        bio: 'Test owner for FullCalendar demo',
        role: 'owner',
        isVerified: true,
        termsAccepted: true,
        privacyPolicyAccepted: true,
        cookiePolicyAccepted: true,
        dataProcessingConsent: true,
        marketingConsent: false
      }).returning();
      owners.push(testOwner);
    }

    const testProperty = {
      title: "Test Farmhouse",
      description: "A test property for FullCalendar integration demo",
      location: "Test Location, India",
      halfDayPrice: 2000,
      fullDayPrice: 3000,
      weekdayHalfDayPrice: 1800,
      weekdayFullDayPrice: 2800,
      weekendHalfDayPrice: 2200,
      weekendFullDayPrice: 3200,
      bedrooms: 3,
      bathrooms: 2,
      amenities: ['wifi', 'parking', 'kitchen'],
      images: ['/images/test.jpg'],
      status: 'active' as const,
      featured: true,
      ownerId: owners[0].id
    };

    const [newProperty] = await db.insert(properties).values(testProperty).returning();
    console.log(`✅ Created test property: ${newProperty.title} (ID: ${newProperty.id})`);
    
    // Verify it exists
    const allProperties = await db.select().from(properties);
    console.log(`Total properties in database: ${allProperties.length}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Error creating test property:", error);
    process.exit(1);
  }
}

createTestProperty();