import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function buyPhoneNumber() {
  console.log('🔍 Looking for SMS-capable phone numbers...\n');

  try {
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    console.log('📞 Searching for available phone numbers...');
    
    // Search for US numbers (most reliable for international SMS)
    const usNumbers = await client.availablePhoneNumbers('US').local.list({
      smsEnabled: true,
      limit: 5
    });
    
    console.log(`\n📱 Found ${usNumbers.length} SMS-capable US numbers:`);
    usNumbers.forEach((num, index) => {
      console.log(`   ${index + 1}. ${num.phoneNumber} (${num.locality}, ${num.region})`);
      console.log(`      SMS: ${num.capabilities.sms}, Voice: ${num.capabilities.voice}, MMS: ${num.capabilities.mms}`);
    });
    
    if (usNumbers.length > 0) {
      const selectedNumber = usNumbers[0];
      console.log(`\n💡 Would you like to buy: ${selectedNumber.phoneNumber}?`);
      console.log('   This will cost approximately $1/month');
      console.log('   This number can send SMS to India');
      console.log('\n   To buy this number, run:');
      console.log(`   npx tsx scripts/buy-phone-number.ts buy ${selectedNumber.phoneNumber}`);
    }
    
    // Check if user wants to buy
    const shouldBuy = process.argv[2] === 'buy';
    const numberToBuy = process.argv[3];
    
    if (shouldBuy && numberToBuy) {
      console.log(`\n💳 Attempting to purchase: ${numberToBuy}`);
      
      try {
        const purchasedNumber = await client.incomingPhoneNumbers.create({
          phoneNumber: numberToBuy,
          smsUrl: 'https://api.bookafarm.com/api/sms/webhook', // Your SMS webhook
          smsMethod: 'POST'
        });
        
        console.log(`✅ Successfully purchased: ${purchasedNumber.phoneNumber}`);
        console.log(`   Phone Number SID: ${purchasedNumber.sid}`);
        console.log(`   Monthly Cost: $${purchasedNumber.price || '1.00'}`);
        
        // Add to messaging service
        if (process.env.TWILIO_MESSAGING_SID) {
          console.log('\n📱 Adding to messaging service...');
          try {
            await client.messaging.v1.services(process.env.TWILIO_MESSAGING_SID)
              .phoneNumbers.create({
                phoneNumberSid: purchasedNumber.sid
              });
            console.log('✅ Added to messaging service successfully');
          } catch (error: any) {
            console.log(`❌ Failed to add to messaging service: ${error.message}`);
          }
        }
        
      } catch (error: any) {
        console.error(`❌ Failed to purchase number: ${error.message}`);
        console.error(`   Error Code: ${error.code}`);
      }
    }
    
  } catch (error: any) {
    console.error('❌ Failed to search for phone numbers:', error.message);
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  buyPhoneNumber()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Search failed:', error);
      process.exit(1);
    });
}

export { buyPhoneNumber };