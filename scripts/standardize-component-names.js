/**
 * Component Name Standardization Script
 * 
 * This script standardizes component file names and organization according to
 * the established naming conventions:
 * - Business components: PascalCase with feature-based folders
 * - UI components: Keep existing kebab-case (shadcn/ui convention)
 * - Page components: PascalCase with organized structure
 */

const fs = require('fs');
const path = require('path');

// Define component categories and their naming conventions
const COMPONENT_CATEGORIES = {
  // Business/Feature components - should use PascalCase
  BUSINESS_COMPONENTS: [
    'BookingForm',
    'PropertyCard', 
    'PropertyList',
    'AuthGuard',
    'ProtectedRoute',
    'PropertyForm',
    'PricingSection',
    'MediaManagementSection',
    'PaymentModal',
    'ReviewSection',
    'SwiggyStyleAuthModal',
    'FeaturedCarousel',
    'ImageUpload',
    'SocialShare',
    'CloudinaryUpload',
    'PricingIntegrationDemo',
    'PricingUpdateFeedback',
    'PricingManagementTab',
    'PropertyPricingCard',
    'PropertyPricingComparison',
    'FraudDetectionAlerts',
    'PaymentStatus',
    'PaymentSummary',
    'SecurityAlerts',
    'TwoFactorModal',
    'ErrorBoundary',
    'SearchForm',
    'PageTransition',
    'PropertyQuickPreview',
    'LocationMap',
    'InteractivePropertyMap',
    'OptimizedPropertyDashboard',
    'PerformanceDashboard',
    'Calendar'
  ],
  
  // UI components - keep kebab-case (shadcn/ui convention)
  UI_COMPONENTS: [
    'accordion',
    'alert-dialog',
    'alert',
    'aspect-ratio',
    'avatar',
    'badge',
    'breadcrumb',
    'button',
    'calendar',
    'card',
    'carousel',
    'chart',
    'checkbox',
    'collapsible',
    'command',
    'context-menu',
    'currency-input',
    'dialog',
    'drawer',
    'dropdown-menu',
    'form',
    'hover-card',
    'input-otp',
    'input',
    'label',
    'menubar',
    'navigation-menu',
    'pagination',
    'popover',
    'progress',
    'radio-group',
    'resizable',
    'scroll-area',
    'select',
    'separator',
    'sheet',
    'sidebar',
    'skeleton',
    'slider',
    'switch',
    'table',
    'tabs',
    'textarea',
    'toast',
    'toaster',
    'toggle-group',
    'toggle',
    'tooltip'
  ],

  // Shared/utility components - PascalCase
  SHARED_COMPONENTS: [
    'CookieConsent',
    'FarmhouseLoader', 
    'Footer',
    'Header',
    'LazyImage',
    'LegalDocumentModal',
    'LoadingSpinner',
    'LocationFilter',
    'LocationRecommendations',
    'OwnerInterestForm',
    'ValidatedForm'
  ]
};

// Proposed new file structure
const NEW_STRUCTURE = {
  'BookingManagement': {
    components: ['BookingForm', 'BookingCard', 'BookingList', 'BookingDetailsModal', 'BookingFilters', 'BookingStatusButton'],
    path: 'components/BookingManagement'
  },
  'PropertyManagement': {
    components: ['PropertyCard', 'PropertyList', 'PropertyForm', 'PropertyQuickPreview', 'PropertyPricingCard', 'PropertyPricingComparison'],
    path: 'components/PropertyManagement'
  },
  'PricingManagement': {
    components: ['PricingSection', 'PricingManagementTab', 'PricingIntegrationDemo', 'PricingUpdateFeedback', 'SeasonalPricingManager', 'CurrencySelector'],
    path: 'components/PricingManagement'
  },
  'AuthenticationSecurity': {
    components: ['AuthGuard', 'ProtectedRoute', 'SwiggyStyleAuthModal', 'TwoFactorModal', 'SecurityAlerts', 'FraudDetectionAlerts'],
    path: 'components/AuthenticationSecurity'
  },
  'PaymentProcessing': {
    components: ['PaymentModal', 'PaymentStatus', 'PaymentSummary'],
    path: 'components/PaymentProcessing'
  },
  'MediaContent': {
    components: ['MediaManagementSection', 'CloudinaryUpload', 'ImageUpload', 'FeaturedCarousel', 'LazyImage'],
    path: 'components/MediaContent'
  },
  'Navigation': {
    components: ['Header', 'Footer', 'LocationMap', 'InteractivePropertyMap', 'LocationFilter', 'LocationRecommendations'],
    path: 'components/Navigation'
  },
  'UserInterface': {
    components: ['ErrorBoundary', 'LoadingSpinner', 'FarmhouseLoader', 'PageTransition', 'SocialShare'],
    path: 'components/UserInterface'
  },
  'Forms': {
    components: ['ValidatedForm', 'SearchForm', 'OwnerInterestForm', 'LoginForm', 'SignupForm', 'OTPInput', 'OTPVerification', 'PhoneInput'],
    path: 'components/Forms'
  },
  'LegalCompliance': {
    components: ['CookieConsent', 'LegalDocumentModal'],
    path: 'components/LegalCompliance'
  },
  'Analytics': {
    components: ['OptimizedPropertyDashboard', 'PerformanceDashboard', 'ReviewSection'],
    path: 'components/Analytics'
  }
};

/**
 * Generate component migration plan
 */
function generateMigrationPlan() {
  const migrationPlan = {
    moves: [],
    renames: [],
    creates: [],
    summary: {
      totalComponents: 0,
      componentsMoved: 0,
      foldersCreated: 0
    }
  };

  // Create folder structure
  Object.values(NEW_STRUCTURE).forEach(({ path: folderPath }) => {
    migrationPlan.creates.push({
      type: 'folder',
      path: `client/src/${folderPath}`,
      action: `Create feature folder: ${folderPath}`
    });
    migrationPlan.summary.foldersCreated++;
  });

  // Plan component moves
  Object.entries(NEW_STRUCTURE).forEach(([featureName, { components, path: targetPath }]) => {
    components.forEach(componentName => {
      // Check current location and plan move
      const currentLocations = [
        `client/src/components/${componentName}.tsx`,
        `client/src/components/${componentName}/index.tsx`,
        `client/src/components/${componentName}/${componentName}.tsx`,
        `client/src/components/booking/${componentName}.tsx`,
        `client/src/components/auth/${componentName}.tsx`,
        `client/src/components/pricing/${componentName}.tsx`
      ];

      const targetLocation = `client/src/${targetPath}/${componentName}/index.tsx`;
      
      migrationPlan.moves.push({
        component: componentName,
        feature: featureName,
        from: 'current location (to be determined)',
        to: targetLocation,
        action: `Move ${componentName} to ${targetPath}`
      });
      
      migrationPlan.summary.componentsMoved++;
      migrationPlan.summary.totalComponents++;
    });
  });

  return migrationPlan;
}

/**
 * Generate index.ts barrel exports for each feature folder
 */
function generateBarrelExports() {
  const barrelExports = {};

  Object.entries(NEW_STRUCTURE).forEach(([featureName, { components, path: folderPath }]) => {
    const exports = components.map(componentName => 
      `export { ${componentName} } from './${componentName}';`
    ).join('\n');

    const indexContent = `/**
 * ${featureName} Components
 * Barrel export for all ${featureName.toLowerCase()} related components
 */

${exports}

// Export types if available
${components.map(componentName => 
  `export type { ${componentName}Props } from './${componentName}';`
).join('\n')}`;

    barrelExports[folderPath] = indexContent;
  });

  return barrelExports;
}

/**
 * Generate updated component template
 */
function generateComponentTemplate(componentName, featureName) {
  return `import React from 'react';

interface ${componentName}Props {
  // Define props here
}

/**
 * ${componentName} Component
 * 
 * Part of ${featureName} feature module
 * 
 * @component
 * @example
 * <${componentName} />
 */
export const ${componentName}: React.FC<${componentName}Props> = (props) => {
  return (
    <div>
      {/* ${componentName} implementation */}
    </div>
  );
};

export default ${componentName};`;
}

/**
 * Main execution
 */
function main() {
  console.log('🏗️  Component Standardization Plan\n');
  
  const migrationPlan = generateMigrationPlan();
  
  console.log('📋 Migration Summary:');
  console.log(`  • Total Components: ${migrationPlan.summary.totalComponents}`);
  console.log(`  • Components to Move: ${migrationPlan.summary.componentsMoved}`);
  console.log(`  • Folders to Create: ${migrationPlan.summary.foldersCreated}\n`);
  
  console.log('📁 New Folder Structure:');
  Object.entries(NEW_STRUCTURE).forEach(([featureName, { components, path }]) => {
    console.log(`  ${path}/`);
    components.forEach(component => {
      console.log(`    ├── ${component}/`);
      console.log(`    │   ├── index.tsx`);
      console.log(`    │   ├── ${component}.tsx`);
      console.log(`    │   ├── ${component}.test.tsx`);
      console.log(`    │   └── ${component}.stories.tsx`);
    });
    console.log('    └── index.ts (barrel export)');
    console.log('');
  });

  console.log('🔄 Component Moves:');
  migrationPlan.moves.slice(0, 10).forEach(move => {
    console.log(`  • ${move.component} → ${move.to}`);
  });
  if (migrationPlan.moves.length > 10) {
    console.log(`  ... and ${migrationPlan.moves.length - 10} more\n`);
  }

  console.log('📦 Barrel Exports Generated:');
  const barrelExports = generateBarrelExports();
  Object.keys(barrelExports).forEach(folderPath => {
    console.log(`  • ${folderPath}/index.ts`);
  });

  console.log('\n✅ Standardization plan complete!');
  console.log('\n📋 Next Steps:');
  console.log('  1. Create new folder structure');
  console.log('  2. Move components to appropriate folders'); 
  console.log('  3. Update import statements throughout codebase');
  console.log('  4. Create barrel exports');
  console.log('  5. Update build configuration if needed');
  console.log('  6. Run tests to ensure everything works');
  
  return {
    migrationPlan,
    barrelExports,
    NEW_STRUCTURE
  };
}

// Export for use in other scripts
module.exports = {
  COMPONENT_CATEGORIES,
  NEW_STRUCTURE,
  generateMigrationPlan,
  generateBarrelExports,
  generateComponentTemplate,
  main
};

// Run if called directly
if (require.main === module) {
  main();
}