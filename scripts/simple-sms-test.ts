import { config } from 'dotenv';
import { resolve } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function simpleSMSTest() {
  console.log('📱 Simple SMS Template Test\n');

  try {
    // Test database connection
    console.log('1. Testing database connection...');
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found in .env.production');
      return;
    }
    console.log('   ✅ DATABASE_URL loaded');

    // Check if templates exist
    console.log('\n2. Checking templates in database...');
    const templates = await db.select().from(smsTemplates);
    console.log(`   ✅ Found ${templates.length} templates`);

    templates.forEach((template, index) => {
      console.log(`   ${index + 1}. ${template.key} (${template.status})`);
    });

    // Check booking confirmation template specifically
    console.log('\n3. Checking your booking confirmation template...');
    const bookingTemplate = templates.find(t => t.key === 'booking_confirmation');
    
    if (bookingTemplate) {
      console.log('   ✅ Booking confirmation template found!');
      console.log(`   📄 Content: ${bookingTemplate.content}`);
      console.log(`   🔢 DLT ID: ${bookingTemplate.dltTemplateId}`);
      console.log(`   📊 Status: ${bookingTemplate.status}`);
      console.log(`   🔧 Variables: ${JSON.stringify(bookingTemplate.variables)}`);
    } else {
      console.log('   ❌ Booking confirmation template not found');
    }

    // Test variable replacement
    if (bookingTemplate) {
      console.log('\n4. Testing variable replacement...');
      const variables = {
        property_name: 'Green Valley Farm',
        booking_date: '2024-12-25'
      };
      
      let message = bookingTemplate.content;
      const templateVars = bookingTemplate.variables as string[];
      
      templateVars.forEach(varName => {
        const value = variables[varName as keyof typeof variables];
        message = message.replace('{#var#}', value);
      });
      
      console.log(`   ✅ Final message: "${message}"`);
    }

    // Check Twilio config
    console.log('\n5. Checking Twilio configuration...');
    const twilioVars = [
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN', 
      'TWILIO_MESSAGING_SID',
      'DLT_ENTITY_ID'
    ];
    
    twilioVars.forEach(varName => {
      if (process.env[varName]) {
        console.log(`   ✅ ${varName}: configured`);
      } else {
        console.log(`   ⚠️  ${varName}: not configured`);
      }
    });

    console.log('\n🎉 Simple SMS test completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Ensure all Twilio variables are in .env.production');
    console.log('2. Run: npm run start');
    console.log('3. Make a test booking through your app');
    console.log('4. Check if SMS is received');

  } catch (error) {
    console.error('❌ Simple SMS test failed:', error);
    process.exit(1);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  simpleSMSTest()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { simpleSMSTest };