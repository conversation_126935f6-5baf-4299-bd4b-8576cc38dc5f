#!/usr/bin/env ts-node

/**
 * TypeScript Error Fix Script
 * 
 * This script automatically fixes common TypeScript errors:
 * - Missing null checks
 * - Import path corrections
 * - Type annotation fixes
 * - Duplicate export removal
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { execSync } from 'child_process';

interface FixRule {
  pattern: RegExp;
  replacement: string;
  description: string;
}

const COMMON_FIXES: FixRule[] = [
  // Fix null check issues
  {
    pattern: /(\w+)\.current(\s*\??\.\w+)/g,
    replacement: '$1.current?$2',
    description: 'Add null check for refs'
  },
  
  // Fix import paths for shared constants
  {
    pattern: /from ['"]\.\.\/\.\.\/shared\/constants['"]/g,
    replacement: "from '../../../shared/constants'",
    description: 'Fix shared constants import path'
  },
  
  // Fix type assertion issues
  {
    pattern: /as any\[\]/g,
    replacement: 'as unknown[]',
    description: 'Replace any[] with unknown[]'
  },
  
  // Fix optional property access
  {
    pattern: /(\w+)\.(\w+)\s*\|\|\s*undefined/g,
    replacement: '$1?.$2',
    description: 'Use optional chaining'
  }
];

class TypeScriptErrorFixer {
  private fixedFiles: string[] = [];
  private errors: string[] = [];

  async fixCommonErrors(): Promise<void> {
    console.log('🔧 Starting TypeScript error fixes...\n');

    // Get list of TypeScript files with errors
    const errorFiles = this.getFilesWithErrors();
    
    for (const file of errorFiles) {
      try {
        await this.fixFile(file);
      } catch (error) {
        this.errors.push(`Failed to fix ${file}: ${error}`);
      }
    }

    this.reportResults();
  }

  private getFilesWithErrors(): string[] {
    try {
      // Run TypeScript and capture errors
      const output = execSync('npx tsc --noEmit 2>&1', { 
        encoding: 'utf8',
        cwd: process.cwd() 
      });
      
      // Extract file paths from error output
      const fileMatches = output.match(/(\S+\.tsx?)\(\d+,\d+\):/g) || [];
      const files = [...new Set(fileMatches.map(match => 
        match.replace(/\(\d+,\d+\):/, '')
      ))];
      
      return files.filter(file => existsSync(file));
    } catch (error) {
      // TypeScript exits with non-zero code when errors exist
      const output = (error as any).stdout || '';
      const fileMatches = output.match(/(\S+\.tsx?)\(\d+,\d+\):/g) || [];
      const files = [...new Set(fileMatches.map((match: string) => 
        match.replace(/\(\d+,\d+\):/, '')
      ))];
      
      return files.filter(file => existsSync(file));
    }
  }

  private async fixFile(filePath: string): Promise<void> {
    if (!existsSync(filePath)) {
      return;
    }

    let content = readFileSync(filePath, 'utf8');
    let modified = false;

    // Apply common fixes
    for (const fix of COMMON_FIXES) {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      
      if (content !== originalContent) {
        modified = true;
        console.log(`  ✅ ${fix.description} in ${filePath}`);
      }
    }

    // Apply file-specific fixes
    if (filePath.includes('Context.tsx')) {
      // Fix context-specific issues
      content = this.fixContextFile(content, filePath);
    }

    if (filePath.includes('hooks/')) {
      // Fix hook-specific issues
      content = this.fixHookFile(content, filePath);
    }

    if (filePath.includes('server/')) {
      // Fix server-specific issues
      content = this.fixServerFile(content, filePath);
    }

    if (modified) {
      writeFileSync(filePath, content);
      this.fixedFiles.push(filePath);
    }
  }

  private fixContextFile(content: string, filePath: string): string {
    // Remove duplicate type exports
    content = content.replace(/export type \{[^}]+\};?\s*$/gm, '');
    
    // Fix fullName type issues
    content = content.replace(
      /fullName: string \| undefined/g,
      'fullName: string'
    );
    
    // Fix getToken property access
    content = content.replace(
      /(\w+)\.getToken/g,
      '$1.getToken?'
    );

    return content;
  }

  private fixHookFile(content: string, filePath: string): string {
    // Fix JSX in .ts files by ensuring React import
    if (filePath.endsWith('.ts') && content.includes('<')) {
      content = content.replace(
        /^import React,?\s*\{/m,
        'import React, {'
      );
      
      if (!content.includes('import React')) {
        content = "import React from 'react';\n" + content;
      }
    }

    return content;
  }

  private fixServerFile(content: string, filePath: string): string {
    // Fix logger parameter issues
    content = content.replace(
      /logger\.(\w+)\([^,]+, ([^,]+), ([^,]+), \{/g,
      'logger.$1($2, $3, {'
    );

    // Fix database import issues
    content = content.replace(
      /import \{ database \}/g,
      'import { db as database }'
    );

    // Fix error type issues
    content = content.replace(
      /} catch \((\w+): any\)/g,
      '} catch ($1: unknown)'
    );

    return content;
  }

  private reportResults(): void {
    console.log('\n📊 TypeScript Error Fix Results:');
    console.log(`  Fixed files: ${this.fixedFiles.length}`);
    
    if (this.fixedFiles.length > 0) {
      console.log('  Files modified:');
      this.fixedFiles.forEach(file => console.log(`    • ${file}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.errors.forEach(error => console.log(`    • ${error}`));
    }

    console.log('\n💡 Next steps:');
    console.log('  1. Run "npm run check" to verify remaining errors');
    console.log('  2. Manually fix complex type errors');
    console.log('  3. Consider updating TypeScript configuration if needed');
  }
}

async function main() {
  const fixer = new TypeScriptErrorFixer();
  await fixer.fixCommonErrors();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { TypeScriptErrorFixer };