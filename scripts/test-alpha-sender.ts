import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Set NODE_ENV to production first
process.env.NODE_ENV = 'production';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function testAlphaSender() {
  console.log('🔍 Testing Alpha Sender Direct SMS...\n');

  try {
    const phoneNumber = process.argv[2] || '**********';
    
    console.log(`📞 Testing Alpha Sender SMS to: +91${phoneNumber}`);
    
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    
    if (!accountSid || !authToken) {
      console.log('❌ Missing Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(accountSid, authToken);
    
    const testMessage = 'Your BookAFarm OTP is 123456. Please do not share it with anyone. Valid for 10 min.';
    const normalizedPhone = `+91${phoneNumber}`;
    
    console.log('\n🧪 Test 1: Direct SMS with Alpha Sender ID');
    try {
      const result1 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        from: 'BKAFARM' // Direct alpha sender
      });
      console.log(`   ✅ Success: ${result1.sid}`);
      console.log(`   From: ${result1.from}`);
      console.log(`   Status: ${result1.status}`);
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      
      if (error.code === 21606) {
        console.log('   💡 Alpha Sender ID not configured for this destination');
      }
    }

    console.log('\n🧪 Test 2: Check Alpha Sender registration status');
    try {
      // List all messaging services to find alpha senders
      const services = await client.messaging.v1.services.list();
      
      for (const service of services) {
        console.log(`\n   Service: ${service.friendlyName} (${service.sid})`);
        
        try {
          const alphaSenders = await client.messaging.v1.services(service.sid).alphaSenders.list();
          
          if (alphaSenders.length > 0) {
            alphaSenders.forEach(sender => {
              console.log(`   📮 Alpha Sender: ${sender.alphaSender}`);
              console.log(`      SID: ${sender.sid}`);
              console.log(`      Capabilities: SMS=${sender.capabilities?.sms}, MMS=${sender.capabilities?.mms}`);
            });
          } else {
            console.log('   ❌ No Alpha Senders found');
          }
        } catch (alphaError: any) {
          console.log(`   ❌ Error fetching alpha senders: ${alphaError.message}`);
        }
      }
    } catch (error: any) {
      console.log(`   ❌ Failed to check alpha senders: ${error.message}`);
    }
    
  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testAlphaSender()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testAlphaSender };