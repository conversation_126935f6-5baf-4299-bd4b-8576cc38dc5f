
#!/usr/bin/env tsx
import { spawn } from 'child_process';
import { promises as fs } from 'fs';

let restartCount = 0;
const maxRestarts = 5;

async function startServer() {
  console.log('🚀 Starting development server...');
  
  const server = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'development' }
  });

  server.on('close', (code) => {
    if (code !== 0 && restartCount < maxRestarts) {
      restartCount++;
      console.log(`⚠️  Server crashed (exit code: ${code}). Restarting... (${restartCount}/${maxRestarts})`);
      setTimeout(startServer, 2000); // Wait 2 seconds before restart
    } else if (restartCount >= maxRestarts) {
      console.error('❌ Server crashed too many times. Please check the logs.');
      process.exit(1);
    }
  });

  server.on('error', (error) => {
    console.error('❌ Failed to start server:', error);
    if (restartCount < maxRestarts) {
      restartCount++;
      setTimeout(startServer, 2000);
    }
  });
}

// Clean up any existing processes
console.log('🧹 Cleaning up existing processes...');
try {
  const { exec } = require('child_process');
  exec('pkill -f "npm run dev" || true');
  exec('pkill -f "vite" || true');
  exec('pkill -f "tsx" || true');
} catch (error) {
  // Ignore cleanup errors
}

setTimeout(startServer, 1000);
