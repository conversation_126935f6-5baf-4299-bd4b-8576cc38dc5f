import { db } from "../server/db";
import { sql } from "drizzle-orm";

async function addVideosColumn() {
  try {
    console.log("Adding videos column to properties table...");
    
    // Check if the videos column already exists
    const columnExists = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'properties' 
      AND column_name = 'videos'
      AND table_schema = 'public'
    `);
    
    if (columnExists.length > 0) {
      console.log("Videos column already exists in properties table");
      return;
    }
    
    // Add the videos column
    await db.execute(sql`
      ALTER TABLE properties ADD COLUMN videos JSONB DEFAULT '[]'::JSONB NOT NULL;
    `);
    
    console.log("✅ Successfully added videos column to properties table");
    
  } catch (error) {
    console.error("❌ Error adding videos column:", error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the migration
addVideosColumn();