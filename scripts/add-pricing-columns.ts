#!/usr/bin/env tsx

import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { db } from '../server/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runMigration() {
  try {
    console.log('Running pricing columns migration...');
    
    // Read the migration SQL file
    const migrationSQL = readFileSync(
      join(__dirname, '..', 'migrations', '0007_add_pricing_columns.sql'),
      'utf-8'
    );
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      console.log(`Executing: ${statement.substring(0, 50)}...`);
      await db.execute(statement);
    }
    
    console.log('✅ Migration completed successfully!');
    console.log('Added columns:');
    console.log('- weekday_half_day_price');
    console.log('- weekday_full_day_price'); 
    console.log('- weekend_half_day_price');
    console.log('- weekend_full_day_price');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });