#!/usr/bin/env tsx

/**
 * Clear Booking Cache Script
 * 
 * This script clears booking-related cache after duplicate booking cleanup
 * to ensure fresh availability data is served.
 */

// Load environment configuration first
import '../server/env-loader';

import { propertyCacheService } from '../server/services/PropertyCacheService';

async function clearBookingCache(): Promise<void> {
  console.log('🧹 Clearing booking-related cache after duplicate cleanup...\n');
  
  try {
    // Clear cache for property 44 (where duplicates were cleaned)
    console.log('📋 Clearing property 44 booking cache...');
    await propertyCacheService.invalidatePropertyCache(44, 'booking');
    console.log('✅ Property 44 cache cleared');

    // Clear all property-related cache to ensure fresh data
    console.log('📋 Clearing all property cache to ensure fresh data...');
    await propertyCacheService.invalidateAllPropertyCache();
    console.log('✅ All property cache cleared (search, availability, details, etc.)');

    console.log('\n🎉 All booking-related cache cleared successfully!');
    console.log('\n📝 Benefits:');
    console.log('   • Fresh availability data will be served');
    console.log('   • Search results will show correct booking status');
    console.log('   • Property details will reflect actual availability');
    console.log('   • No stale duplicate booking data in cache');
    
  } catch (error) {
    console.error('❌ Error clearing cache:', error);
    throw error;
  }
}

async function main() {
  try {
    await clearBookingCache();
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('\n🎉 Cache clearing completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { clearBookingCache };