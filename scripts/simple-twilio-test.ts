import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function simpleTwilioTest() {
  console.log('📱 Simple Twilio SMS Test...\n');

  try {
    const phoneNumber = process.argv[2] || '**********';
    console.log(`📞 Testing SMS to: +91${phoneNumber}`);

    // Check required Twilio variables
    const twilioSid = process.env.TWILIO_ACCOUNT_SID;
    const twilioToken = process.env.TWILIO_AUTH_TOKEN;
    const messagingSid = process.env.TWILIO_MESSAGING_SID;

    if (!twilioSid || !twilioToken || !messagingSid) {
      console.log('❌ Missing Twilio credentials in .env.production');
      console.log(`   TWILIO_ACCOUNT_SID: ${twilioSid ? '✅' : '❌'}`);
      console.log(`   TWILIO_AUTH_TOKEN: ${twilioToken ? '✅' : '❌'}`);
      console.log(`   TWILIO_MESSAGING_SID: ${messagingSid ? '✅' : '❌'}`);
      return;
    }

    // Initialize Twilio client
    const client = twilio(twilioSid, twilioToken);

    // Test 1: Send OTP SMS
    console.log('\n1. Testing OTP SMS...');
    const otpMessage = 'Your BookAFarm OTP is 456789. Please do not share it with anyone. Valid for 10 min.';
    
    try {
      const result = await client.messages.create({
        body: otpMessage,
        to: `+91${phoneNumber}`,
        messagingServiceSid: messagingSid
      });
      console.log(`   ✅ OTP SMS sent successfully!`);
      console.log(`   📱 Message SID: ${result.sid}`);
      console.log(`   📱 Status: ${result.status}`);
    } catch (error: any) {
      console.log(`   ❌ OTP SMS failed: ${error.message}`);
    }

    // Test 2: Send Booking Confirmation SMS
    console.log('\n2. Testing Booking Confirmation SMS...');
    const bookingMessage = 'BookAFarm booking for Green Valley Farm on 2024-12-25 confirmed. Thank you for choosing us!';
    
    try {
      const result = await client.messages.create({
        body: bookingMessage,
        to: `+91${phoneNumber}`,
        messagingServiceSid: messagingSid
      });
      console.log(`   ✅ Booking SMS sent successfully!`);
      console.log(`   📱 Message SID: ${result.sid}`);
      console.log(`   📱 Status: ${result.status}`);
    } catch (error: any) {
      console.log(`   ❌ Booking SMS failed: ${error.message}`);
    }

    console.log('\n🎉 SMS Test Complete!');
    console.log('\n📱 Check your phone for the following messages:');
    console.log('1. OTP message with code 456789');
    console.log('2. Booking confirmation for Green Valley Farm');
    
    console.log('\n✅ If you received both messages, SMS functionality is working!');
    console.log('✅ The DLT templates are correctly formatted and being delivered');
    console.log('✅ Your Twilio messaging service is configured for DLT compliance');

  } catch (error) {
    console.error('❌ SMS test failed:', error);
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  simpleTwilioTest()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { simpleTwilioTest };