#!/usr/bin/env tsx
/**
 * Database Resilience Test Script
 * Tests Phase 1 improvements: connection recovery, exponential backoff, and refresh mechanisms
 */

// Load environment configuration FIRST
import "../server/env-loader";

import { databaseManager, db } from '../server/utils/database';
import { logger } from '../server/services/LoggerService';
import { properties } from '../shared/schema';

async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testBasicConnection() {
  console.log('\n🧪 Test 1: Basic Connection Test');
  console.log('================================');
  
  try {
    const isHealthy = await databaseManager.testConnection();
    console.log(`✅ Database connection: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);
    
    const metrics = databaseManager.getMetrics();
    console.log(`📊 Connection Metrics:`);
    console.log(`   - Total connections: ${metrics.totalConnections}`);
    console.log(`   - Active connections: ${metrics.activeConnections}`);
    console.log(`   - Idle connections: ${metrics.idleConnections}`);
    console.log(`   - Connection errors: ${metrics.connectionErrors}`);
  } catch (error) {
    console.error('❌ Connection test failed:', error);
  }
}

async function testQueryPerformance() {
  console.log('\n🧪 Test 2: Query Performance Test');
  console.log('==================================');
  
  const iterations = 10;
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    try {
      await db.select().from(properties).limit(1);
      const duration = Date.now() - start;
      times.push(duration);
      console.log(`   Query ${i + 1}: ${duration}ms`);
    } catch (error) {
      console.error(`   Query ${i + 1}: FAILED -`, error);
    }
    await sleep(100); // Small delay between queries
  }
  
  if (times.length > 0) {
    const avg = times.reduce((a, b) => a + b, 0) / times.length;
    const max = Math.max(...times);
    const min = Math.min(...times);
    console.log(`\n📊 Performance Summary:`);
    console.log(`   - Average: ${avg.toFixed(2)}ms`);
    console.log(`   - Min: ${min}ms`);
    console.log(`   - Max: ${max}ms`);
  }
}

async function testConnectionRecovery() {
  console.log('\n🧪 Test 3: Connection Recovery Test');
  console.log('====================================');
  console.log('⚠️  This test simulates connection failures');
  
  // Force a connection error by trying to query with an invalid operation
  console.log('\n1. Triggering connection errors...');
  
  // Test 1: Show retry with exponential backoff
  console.log('\n   Part 1: Testing exponential backoff...');
  let attemptCount = 0;
  try {
    await databaseManager.withConnection(async (client) => {
      attemptCount++;
      // Fail first 3 attempts to show backoff
      if (attemptCount <= 3) {
        throw new Error('ECONNRESET: Simulated connection reset');
      }
      // 4th attempt succeeds
      const result = await client.query('SELECT 1 as test');
      return result.rows[0];
    }, 'recovery-test-backoff');
    
    console.log(`   ✅ Success after ${attemptCount} attempts (exponential backoff worked!)`);
  } catch (error: any) {
    console.log(`   ❌ Failed after all retries - ${error.message}`);
  }
  
  await sleep(1000);
  
  // Test 2: Show successful immediate recovery
  console.log('\n   Part 2: Testing immediate recovery...');
  for (let i = 0; i < 3; i++) {
    try {
      const result = await databaseManager.withConnection(async (client) => {
        const res = await client.query('SELECT $1::text as status', ['OK']);
        return res.rows[0].status;
      }, 'recovery-test-success');
      
      console.log(`   Attempt ${i + 1}: ✅ Success - connection healthy`);
    } catch (error: any) {
      console.log(`   Attempt ${i + 1}: ❌ Failed - ${error.message}`);
    }
    
    await sleep(500);
  }
  
  const metrics = databaseManager.getMetrics();
  console.log(`\n📊 Recovery Metrics:`);
  console.log(`   - Connection errors: ${metrics.connectionErrors}`);
  console.log(`   - Last error: ${metrics.lastError?.message || 'None'}`);
}

async function testConcurrentConnections() {
  console.log('\n🧪 Test 4: Concurrent Connections Test');
  console.log('=======================================');
  
  const concurrentQueries = 20;
  console.log(`📊 Running ${concurrentQueries} concurrent queries...`);
  
  const startTime = Date.now();
  const promises = [];
  
  for (let i = 0; i < concurrentQueries; i++) {
    promises.push(
      db.select().from(properties).limit(1).then(() => ({
        id: i,
        success: true,
        time: Date.now() - startTime
      })).catch(error => ({
        id: i,
        success: false,
        error: error.message,
        time: Date.now() - startTime
      }))
    );
  }
  
  const results = await Promise.all(promises);
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`\n📊 Concurrency Results:`);
  console.log(`   - Successful: ${successful}/${concurrentQueries}`);
  console.log(`   - Failed: ${failed}/${concurrentQueries}`);
  console.log(`   - Total time: ${Date.now() - startTime}ms`);
  
  if (failed > 0) {
    console.log(`\n❌ Failed queries:`);
    results.filter(r => !r.success).forEach(r => {
      console.log(`   - Query ${r.id}: ${(r as any).error}`);
    });
  }
  
  const poolStats = databaseManager.getPoolStats();
  console.log(`\n📊 Pool Statistics:`);
  console.log(`   - Total connections: ${poolStats.total}`);
  console.log(`   - Idle connections: ${poolStats.idle}`);
  console.log(`   - Waiting count: ${poolStats.waiting}`);
}

async function testHealthCheckMechanism() {
  console.log('\n🧪 Test 5: Health Check Mechanism');
  console.log('==================================');
  
  console.log('📊 Performing manual health check...');
  const healthResult = await databaseManager.performHealthCheck();
  console.log(`   - Health check result: ${healthResult ? '✅ PASS' : '❌ FAIL'}`);
  
  const metrics = databaseManager.getMetrics();
  if (metrics.lastHealthCheck) {
    const age = Date.now() - metrics.lastHealthCheck.getTime();
    console.log(`   - Last health check: ${Math.round(age / 1000)}s ago`);
  }
  
  console.log('\n📊 Waiting for automatic health check (5s interval)...');
  console.log('   (Waiting 12 seconds to observe health checks)');
  
  // Monitor health checks for 12 seconds (enough to see if they're working)
  let checks = 0;
  let lastHealthCheckTime = metrics.lastHealthCheck?.getTime();
  const checkInterval = setInterval(() => {
    const newMetrics = databaseManager.getMetrics();
    if (newMetrics.lastHealthCheck && 
        newMetrics.lastHealthCheck.getTime() !== lastHealthCheckTime) {
      checks++;
      lastHealthCheckTime = newMetrics.lastHealthCheck.getTime();
      console.log(`   ✅ Health check ${checks} completed at ${new Date().toLocaleTimeString()}`);
    }
  }, 1000);
  
  await sleep(12000);
  clearInterval(checkInterval);
  
  if (checks === 0) {
    console.log('   ⚠️  No automatic health checks observed in 12s window');
  } else {
    console.log(`   ✅ Observed ${checks} automatic health check(s)`);
  }
}

async function runAllTests() {
  console.log('🚀 Database Resilience Test Suite');
  console.log('=================================');
  console.log('Testing Phase 1 improvements...\n');
  
  try {
    // Set shorter health check interval for testing
    databaseManager.updateHealthCheckInterval(5000); // 5 seconds
    
    // Give the database time to initialize and prewarm
    console.log('⏳ Waiting for connection prewarming...');
    await sleep(2000);
    
    await testBasicConnection();
    await testQueryPerformance();
    await testConnectionRecovery();
    await testConcurrentConnections();
    await testHealthCheckMechanism();
    
    console.log('\n✅ All tests completed!');
    console.log('\n📊 Final Metrics:');
    const finalMetrics = databaseManager.getMetrics();
    console.log(`   - Total connections: ${finalMetrics.totalConnections}`);
    console.log(`   - Connection errors: ${finalMetrics.connectionErrors}`);
    console.log(`   - Active connections: ${finalMetrics.activeConnections}`);
    console.log(`   - Idle connections: ${finalMetrics.idleConnections}`);
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
  } finally {
    console.log('\n🔚 Shutting down database connections...');
    await databaseManager.shutdown();
    process.exit(0);
  }
}

// Run tests
runAllTests().catch(console.error);