import { config } from 'dotenv';
import { resolve } from 'path';
import twilio from 'twilio';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

async function testDirectSMS() {
  console.log('🔍 Testing Direct SMS (bypassing messaging service)...\n');

  try {
    const phoneNumber = process.argv[2] || '**********';
    
    console.log(`📞 Testing SMS to: +91${phoneNumber}`);
    
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.log('❌ Missing required Twilio credentials');
      return;
    }

    // Initialize Twilio client
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    const testMessage = 'Your BookAFarm OTP is 123456. Please do not share it with anyone. Valid for 10 min.';
    const normalizedPhone = `+91${phoneNumber}`;
    
    console.log('\n🧪 Test 1: Direct SMS with Alpha Sender ID');
    try {
      const result1 = await client.messages.create({
        body: testMessage,
        to: normalizedPhone,
        from: 'BKAFARM' // Direct alpha sender
      });
      console.log(`   ✅ Success: ${result1.sid}`);
    } catch (error: any) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   Code: ${error.code}`);
    }

    console.log('\n🧪 Test 2: Get available phone numbers');
    try {
      const phoneNumbers = await client.incomingPhoneNumbers.list({ limit: 10 });
      console.log(`   📱 Available phone numbers: ${phoneNumbers.length}`);
      
      if (phoneNumbers.length > 0) {
        const smsCapableNumbers = phoneNumbers.filter(num => num.capabilities.sms);
        console.log(`   📱 SMS-capable numbers: ${smsCapableNumbers.length}`);
        
        if (smsCapableNumbers.length > 0) {
          const testNumber = smsCapableNumbers[0];
          console.log(`   📱 Using: ${testNumber.phoneNumber}`);
          
          console.log('\n🧪 Test 3: Direct SMS with Twilio number');
          try {
            const result3 = await client.messages.create({
              body: testMessage,
              to: normalizedPhone,
              from: testNumber.phoneNumber
            });
            console.log(`   ✅ Success: ${result3.sid}`);
          } catch (error: any) {
            console.log(`   ❌ Failed: ${error.message}`);
            console.log(`   Code: ${error.code}`);
          }
        }
      } else {
        console.log('   ❌ No phone numbers found - you need to buy a phone number');
      }
    } catch (error: any) {
      console.log(`   ❌ Failed to get phone numbers: ${error.message}`);
    }

    console.log('\n🧪 Test 4: Check Alpha Sender registration');
    try {
      // Check if alpha sender is registered
      const alphaSenders = await client.messaging.v1.services.list();
      console.log(`   📮 Found ${alphaSenders.length} messaging services`);
      
      for (const service of alphaSenders) {
        const senders = await client.messaging.v1.services(service.sid).alphaSenders.list();
        senders.forEach(sender => {
          console.log(`   📮 Alpha Sender: ${sender.alphaSender} (${sender.sid})`);
        });
      }
    } catch (error: any) {
      console.log(`   ❌ Failed to check alpha senders: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Direct SMS test failed:', error);
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testDirectSMS()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testDirectSMS };