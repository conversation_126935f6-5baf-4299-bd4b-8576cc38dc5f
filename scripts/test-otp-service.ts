import { config } from 'dotenv';
import { resolve } from 'path';
import { dltSMSService } from '../server/services/DLTSMSService';

// Set NODE_ENV to production first
process.env.NODE_ENV = 'production';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function testOTPService() {
  console.log('🔍 Testing OTP Service...\n');

  try {
    const phoneNumber = process.argv[2] || '9391010188';
    const otpCode = '123456';
    
    console.log(`📞 Testing OTP service for: +91${phoneNumber}`);
    console.log(`🔐 OTP Code: ${otpCode}`);
    
    // Test the DLT SMS service sendOTP method
    const result = await dltSMSService.sendOTP(`+91${phoneNumber}`, otpCode);
    
    console.log('\n📋 OTP Service Result:');
    console.log(`   Success: ${result.success}`);
    console.log(`   Message SID: ${result.messageSid || 'None'}`);
    console.log(`   Error: ${result.error || 'None'}`);
    
    if (result.success) {
      console.log('\n✅ OTP service is working correctly!');
    } else {
      console.log('\n❌ OTP service is failing - this explains the API error');
      console.log('💡 The issue is likely in the SMS delivery configuration');
    }
    
  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
    console.error('💡 This error would cause the API to return OTP_INVALID');
  }
}

// Check if this is the main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testOTPService()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testOTPService };