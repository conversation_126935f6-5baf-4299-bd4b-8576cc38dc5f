import { config } from 'dotenv';
import { resolve } from 'path';
import { db } from '../server/db';
import { smsTemplates } from '../shared/schema';
import { eq } from 'drizzle-orm';
import { DLTSMSService } from '../server/services/DLTSMSService';

// Set NODE_ENV to production first
process.env.NODE_ENV = 'production';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

async function testOTPTemplate() {
  console.log('🧪 Testing OTP Template Integration...\n');

  try {
    // Check if DATABASE_URL is loaded
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found in .env.production');
      process.exit(1);
    }

    console.log('✅ Connected to production database');

    // 1. Verify template exists and is active
    console.log('\n1. Verifying OTP template...');
    const otpTemplate = await db
      .select()
      .from(smsTemplates)
      .where(eq(smsTemplates.key, 'otp_verification'))
      .limit(1);

    if (otpTemplate.length === 0) {
      console.log('❌ OTP template not found');
      process.exit(1);
    }

    const template = otpTemplate[0];
    console.log(`   ✅ Template found: ${template.name}`);
    console.log(`   📝 Status: ${template.status}`);
    console.log(`   🔢 DLT ID: ${template.dltTemplateId}`);
    console.log(`   📄 Content: ${template.content}`);
    console.log(`   🔧 Variables: ${JSON.stringify(template.variables)}`);

    if (template.status !== 'active') {
      console.log('❌ Template is not active');
      process.exit(1);
    }

    // 2. Test message formatting
    console.log('\n2. Testing message formatting...');
    const testOTP = '123456';
    const expectedMessage = template.content.replace('{#var#}', testOTP);
    console.log(`   ✅ Test OTP: ${testOTP}`);
    console.log(`   ✅ Expected message: "${expectedMessage}"`);

    // 3. Test DLT SMS Service
    console.log('\n3. Testing DLT SMS Service integration...');
    const dltSmsService = new DLTSMSService();
    
    // Test service health
    const health = await dltSmsService.getServiceHealth();
    console.log(`   📊 Service Health:`);
    console.log(`      - Twilio Connected: ${health.twilioConnected}`);
    console.log(`      - DLT Configured: ${health.dltConfigured}`);
    console.log(`      - Templates Loaded: ${health.templatesLoaded}`);
    console.log(`      - Available Templates: ${health.availableTemplates.join(', ')}`);

    // 4. Test OTP sending (dry run)
    console.log('\n4. Testing OTP sending (dry run)...');
    const testPhoneNumber = '+917777777777'; // Test phone number
    const testOtpCode = '654321';

    try {
      const result = await dltSmsService.sendOTP(testPhoneNumber, testOtpCode);
      console.log(`   📱 SMS Result: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      if (result.success) {
        console.log(`   ✅ Message SID: ${result.messageSid}`);
        console.log(`   ✅ OTP SMS sending works correctly!`);
      } else {
        console.log(`   ❌ Error: ${result.error}`);
      }
    } catch (error) {
      console.log(`   ❌ SMS sending failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 5. Verify template usage in logs
    console.log('\n5. Checking recent SMS logs...');
    const logs = await db.query.smsLogs.findMany({
      with: {
        template: true
      },
      limit: 5,
      orderBy: (smsLogs, { desc }) => [desc(smsLogs.createdAt)]
    });

    console.log(`   📊 Found ${logs.length} recent SMS logs`);
    logs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.template?.name || 'Unknown'} → ${log.recipientPhone}`);
      console.log(`      Status: ${log.status}`);
      console.log(`      Created: ${log.createdAt}`);
    });

    console.log('\n🎉 OTP template integration test completed!');
    console.log('\n📋 Summary:');
    console.log(`   ✅ OTP template is active and ready`);
    console.log(`   ✅ DLT SMS service is integrated`);
    console.log(`   ✅ Template ID: ${template.dltTemplateId}`);
    console.log(`   ✅ Variable: otp_code`);
    
    console.log('\n🔄 Next steps:');
    console.log('1. Test login/register flow to trigger OTP SMS');
    console.log('2. Monitor SMS logs for delivery status');
    console.log('3. Verify OTP codes are received correctly');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testOTPTemplate()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testOTPTemplate };