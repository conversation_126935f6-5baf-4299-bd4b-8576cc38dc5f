#!/bin/bash

# Test execution script with detailed summary report
# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}🧪 Running Comprehensive Test Suite${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Initialize status variables
UNIT_STATUS=""
INTEGRATION_STATUS=""
E2E_STATUS=""
UNIT_EXIT_CODE=0
INTEGRATION_EXIT_CODE=0
E2E_EXIT_CODE=0

# Initialize count variables
UNIT_PASSED=0
UNIT_FAILED=0
UNIT_TOTAL=0
INTEGRATION_PASSED=0
INTEGRATION_FAILED=0
INTEGRATION_TOTAL=0
E2E_PASSED=0
E2E_FAILED=0
E2E_TOTAL=0

# Function to extract test counts from vitest output
extract_vitest_counts() {
    local output="$1"
    local passed=$(echo "$output" | grep -oE "[0-9]+ passed" | grep -oE "[0-9]+" | head -1)
    local failed=$(echo "$output" | grep -oE "[0-9]+ failed" | grep -oE "[0-9]+" | head -1)
    
    # If no explicit counts found, parse from "Tests X passed (Y)" format
    if [ -z "$passed" ]; then
        passed=$(echo "$output" | grep -oE "Tests.*[0-9]+ passed \([0-9]+\)" | grep -oE "\([0-9]+\)" | tr -d "()")
    fi
    
    # Default to 0 if not found
    passed=${passed:-0}
    failed=${failed:-0}
    
    echo "$passed $failed"
}

# Function to extract test counts from playwright output
extract_playwright_counts() {
    local output="$1"
    local passed=$(echo "$output" | grep -oE "[0-9]+ passed" | grep -oE "[0-9]+")
    local failed=$(echo "$output" | grep -oE "[0-9]+ failed" | grep -oE "[0-9]+")
    
    # Default to 0 if not found
    passed=${passed:-0}
    failed=${failed:-0}
    
    echo "$passed $failed"
}

# Run Unit Tests
echo -e "${YELLOW}📋 Running Unit Tests...${NC}"
echo "----------------------------------------"
UNIT_OUTPUT=$(npm run test:unit 2>&1)
UNIT_EXIT_CODE=$?
echo "$UNIT_OUTPUT"

# Extract unit test counts
UNIT_COUNTS=$(extract_vitest_counts "$UNIT_OUTPUT")
UNIT_PASSED=$(echo $UNIT_COUNTS | cut -d' ' -f1)
UNIT_FAILED=$(echo $UNIT_COUNTS | cut -d' ' -f2)
UNIT_TOTAL=$((UNIT_PASSED + UNIT_FAILED))

if [ $UNIT_EXIT_CODE -eq 0 ]; then
    UNIT_STATUS="✅ PASSED"
else
    UNIT_STATUS="❌ FAILED"
fi
echo ""

# Run Integration Tests
echo -e "${YELLOW}🔗 Running Integration Tests...${NC}"
echo "----------------------------------------"
INTEGRATION_OUTPUT=$(npm run test:integration 2>&1)
INTEGRATION_EXIT_CODE=$?
echo "$INTEGRATION_OUTPUT"

# Extract integration test counts
INTEGRATION_COUNTS=$(extract_vitest_counts "$INTEGRATION_OUTPUT")
INTEGRATION_PASSED=$(echo $INTEGRATION_COUNTS | cut -d' ' -f1)
INTEGRATION_FAILED=$(echo $INTEGRATION_COUNTS | cut -d' ' -f2)
INTEGRATION_TOTAL=$((INTEGRATION_PASSED + INTEGRATION_FAILED))

if [ $INTEGRATION_EXIT_CODE -eq 0 ]; then
    INTEGRATION_STATUS="✅ PASSED"
else
    INTEGRATION_STATUS="❌ FAILED"
fi
echo ""

# Run E2E Tests
echo -e "${YELLOW}🌐 Running End-to-End Tests...${NC}"
echo "----------------------------------------"
E2E_OUTPUT=$(npm run test:e2e 2>&1)
E2E_EXIT_CODE=$?
echo "$E2E_OUTPUT"

# Extract E2E test counts
E2E_COUNTS=$(extract_playwright_counts "$E2E_OUTPUT")
E2E_PASSED=$(echo $E2E_COUNTS | cut -d' ' -f1)
E2E_FAILED=$(echo $E2E_COUNTS | cut -d' ' -f2)
E2E_TOTAL=$((E2E_PASSED + E2E_FAILED))

if [ $E2E_EXIT_CODE -eq 0 ]; then
    E2E_STATUS="✅ PASSED"
else
    E2E_STATUS="❌ FAILED"
fi
echo ""

# Calculate totals
TOTAL_PASSED=$((UNIT_PASSED + INTEGRATION_PASSED + E2E_PASSED))
TOTAL_FAILED=$((UNIT_FAILED + INTEGRATION_FAILED + E2E_FAILED))
TOTAL_TESTS=$((TOTAL_PASSED + TOTAL_FAILED))

# Display Detailed Summary
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}📊 DETAILED TEST EXECUTION SUMMARY${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

echo -e "${CYAN}📋 Unit Tests (70% Target):${NC}"
echo -e "   Status: ${UNIT_STATUS}"
echo -e "   ${GREEN}✅ Passed: ${UNIT_PASSED}${NC}   ${RED}❌ Failed: ${UNIT_FAILED}${NC}   📊 Total: ${UNIT_TOTAL}"
echo ""

echo -e "${CYAN}🔗 Integration Tests (20% Target):${NC}"
echo -e "   Status: ${INTEGRATION_STATUS}"
echo -e "   ${GREEN}✅ Passed: ${INTEGRATION_PASSED}${NC}   ${RED}❌ Failed: ${INTEGRATION_FAILED}${NC}   📊 Total: ${INTEGRATION_TOTAL}"
echo ""

echo -e "${CYAN}🌐 End-to-End Tests (10% Target):${NC}"
echo -e "   Status: ${E2E_STATUS}"
echo -e "   ${GREEN}✅ Passed: ${E2E_PASSED}${NC}   ${RED}❌ Failed: ${E2E_FAILED}${NC}   📊 Total: ${E2E_TOTAL}"
echo ""

echo -e "${BLUE}----------------------------------------${NC}"
echo -e "${CYAN}🎯 OVERALL SUMMARY:${NC}"
echo -e "   ${GREEN}✅ Total Passed: ${TOTAL_PASSED}${NC}"
echo -e "   ${RED}❌ Total Failed: ${TOTAL_FAILED}${NC}"
echo -e "   📊 Total Tests: ${TOTAL_TESTS}"
echo ""

# Overall status with detailed breakdown
MANDATORY_EXIT_CODE=$((UNIT_EXIT_CODE + INTEGRATION_EXIT_CODE))
if [ $MANDATORY_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}🎉 MANDATORY TESTS: ALL PASSED${NC}"
    echo -e "${GREEN}✨ Ready for Production Deployment!${NC}"
    
    if [ $E2E_EXIT_CODE -eq 0 ]; then
        echo -e "${GREEN}🌟 BONUS: E2E Tests Also Passed!${NC}"
    else
        echo -e "${YELLOW}ℹ️  E2E Tests Failed (Optional - Won't Block Merge)${NC}"
    fi
else
    echo -e "${RED}❌ MANDATORY TESTS FAILED${NC}"
    echo -e "${RED}🔧 Fix Unit/Integration Tests Before Merge${NC}"
fi

echo ""
echo -e "${BLUE}========================================${NC}"

# Exit with appropriate code (only mandatory tests affect exit code)
exit $MANDATORY_EXIT_CODE