#!/usr/bin/env -S npx tsx

/**
 * Setup Real WhatsApp Numbers for Testing
 * This script allows you to configure actual WhatsApp numbers
 */

import dotenv from 'dotenv';
import path from 'path';
import readline from 'readline';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.development') });

import { db } from '../server/db';
import { users, properties } from '../shared/schema';
import { eq, and } from 'drizzle-orm';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(query, resolve);
  });
};

async function setupRealWhatsApp() {
  console.log('🏡 Setup Real WhatsApp Numbers for Owners');
  console.log('==========================================\n');

  try {
    // Get all owners
    const owners = await db
      .select({
        id: users.id,
        fullName: users.fullName,
        email: users.email,
        phone: users.phone,
        whatsappNumber: users.whatsappNumber
      })
      .from(users)
      .where(eq(users.role, 'owner'));

    if (owners.length === 0) {
      console.log('❌ No property owners found.');
      process.exit(0);
    }

    console.log(`Found ${owners.length} property owner(s):\n`);

    // Display owners
    owners.forEach((owner, index) => {
      console.log(`${index + 1}. ${owner.fullName}`);
      console.log(`   Email: ${owner.email}`);
      console.log(`   Current WhatsApp: ${owner.whatsappNumber || 'Not set'}`);
      console.log('');
    });

    console.log('📱 Enter WhatsApp Numbers');
    console.log('-------------------------');
    console.log('Format: Country code + number (e.g., 919876543210)');
    console.log('Press Enter to skip an owner\n');

    // Collect WhatsApp numbers
    for (const owner of owners) {
      console.log(`\n${owner.fullName}:`);
      const currentNumber = owner.whatsappNumber ? ` (current: ${owner.whatsappNumber})` : '';
      const whatsappNumber = await question(`Enter WhatsApp number${currentNumber}: `);
      
      if (whatsappNumber.trim()) {
        // Clean the number (remove spaces, +, etc.)
        const cleanNumber = whatsappNumber.replace(/[^\d]/g, '');
        
        if (cleanNumber.length >= 10) {
          await db
            .update(users)
            .set({ 
              whatsappNumber: cleanNumber,
              phone: owner.phone || cleanNumber
            })
            .where(eq(users.id, owner.id));
          
          console.log(`✅ Updated with: ${cleanNumber}`);
        } else {
          console.log(`⚠️  Invalid number format, skipping...`);
        }
      } else {
        console.log(`⏭️  Skipped`);
      }
    }

    // Display final configuration
    console.log('\n📊 Final Configuration:');
    console.log('=======================\n');

    const updatedOwners = await db
      .select({
        ownerId: users.id,
        ownerName: users.fullName,
        ownerWhatsApp: users.whatsappNumber,
        propertyId: properties.id,
        propertyTitle: properties.title
      })
      .from(users)
      .leftJoin(properties, eq(properties.ownerId, users.id))
      .where(and(
        eq(users.role, 'owner'),
        eq(properties.status, 'active')
      ));

    // Group by owner
    const ownerGroups = new Map();
    updatedOwners.forEach(row => {
      if (!ownerGroups.has(row.ownerId)) {
        ownerGroups.set(row.ownerId, {
          name: row.ownerName,
          whatsapp: row.ownerWhatsApp,
          properties: []
        });
      }
      if (row.propertyId) {
        ownerGroups.get(row.ownerId).properties.push(row.propertyTitle);
      }
    });

    ownerGroups.forEach((owner) => {
      if (owner.whatsapp) {
        console.log(`👤 ${owner.name}`);
        console.log(`📱 WhatsApp: +${owner.whatsapp}`);
        console.log(`🏡 Properties: ${owner.properties.length}`);
        owner.properties.forEach((prop: string, i: number) => {
          console.log(`   ${i + 1}. ${prop}`);
        });
        console.log('');
      }
    });

    console.log('\n🎯 Testing Instructions:');
    console.log('========================');
    console.log('1. Make sure your server is running (npm run dev)');
    console.log('2. Set up ngrok: ngrok http 5000');
    console.log('3. Configure Twilio webhook with ngrok URL');
    console.log('4. Send WhatsApp message to Twilio sandbox number');
    console.log('5. Include owner WhatsApp in message or use routing logic\n');

    console.log('✅ Real WhatsApp numbers configured!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    rl.close();
  }
}

// Run setup
setupRealWhatsApp().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Failed:', error);
  process.exit(1);
});