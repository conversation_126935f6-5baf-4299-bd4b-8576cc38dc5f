#!/usr/bin/env ts-node

/**
 * Dead Code Detection Script
 * 
 * This script uses multiple tools to identify dead code patterns:
 * - ts-prune for unused exports
 * - ESLint for unused imports and variables
 * - Custom analysis for unused configurations
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

interface DeadCodeReport {
  timestamp: string;
  summary: {
    unusedExports: number;
    unusedImports: number;
    unusedVariables: number;
    unusedConfigurations: number;
    totalIssues: number;
  };
  details: {
    unusedExports: Array<{
      file: string;
      export: string;
      line: number;
    }>;
    unusedImports: Array<{
      file: string;
      import: string;
      line: number;
    }>;
    unusedConfigurations: Array<{
      file: string;
      config: string;
      reason: string;
    }>;
  };
  recommendations: string[];
}

class DeadCodeDetector {
  private report: DeadCodeReport = {
    timestamp: new Date().toISOString(),
    summary: {
      unusedExports: 0,
      unusedImports: 0,
      unusedVariables: 0,
      unusedConfigurations: 0,
      totalIssues: 0
    },
    details: {
      unusedExports: [],
      unusedImports: [],
      unusedConfigurations: []
    },
    recommendations: []
  };

  async detectDeadCode(): Promise<DeadCodeReport> {
    console.log('🔍 Starting dead code detection...\n');

    await this.detectUnusedExports();
    await this.detectUnusedImports();
    await this.detectUnusedConfigurations();
    this.generateRecommendations();
    
    this.report.summary.totalIssues = 
      this.report.summary.unusedExports + 
      this.report.summary.unusedImports + 
      this.report.summary.unusedConfigurations;

    return this.report;
  }

  private async detectUnusedExports(): Promise<void> {
    console.log('📦 Detecting unused exports with ts-prune...');
    
    try {
      const output = execSync('npx ts-prune --project tsconfig.json', { 
        encoding: 'utf8',
        cwd: process.cwd()
      });
      
      const lines = output.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        const match = line.match(/^(.+):(\d+) - (.+)$/);
        if (match) {
          const [, file, lineNum, exportName] = match;
          this.report.details.unusedExports.push({
            file: file.replace(process.cwd() + '/', ''),
            export: exportName,
            line: parseInt(lineNum)
          });
          this.report.summary.unusedExports++;
        }
      }
      
      console.log(`   Found ${this.report.summary.unusedExports} unused exports`);
    } catch (error) {
      console.warn('   ⚠️ ts-prune failed, skipping unused exports detection');
    }
  }

  private async detectUnusedImports(): Promise<void> {
    console.log('📥 Detecting unused imports with ESLint...');
    
    try {
      const output = execSync(
        'npx eslint --config .eslintrc.deadcode.js --ext .ts,.tsx --format json server/ client/ shared/',
        { 
          encoding: 'utf8',
          cwd: process.cwd()
        }
      );
      
      const results = JSON.parse(output);
      
      for (const result of results) {
        if (result.messages) {
          for (const message of result.messages) {
            if (message.ruleId === 'unused-imports/no-unused-imports') {
              this.report.details.unusedImports.push({
                file: result.filePath.replace(process.cwd() + '/', ''),
                import: message.message,
                line: message.line
              });
              this.report.summary.unusedImports++;
            }
          }
        }
      }
      
      console.log(`   Found ${this.report.summary.unusedImports} unused imports`);
    } catch (error) {
      // ESLint returns non-zero exit code when issues found, parse stderr
      try {
        const output = (error as any).stdout;
        if (output) {
          const results = JSON.parse(output);
          // Process results same as above
          console.log(`   Found issues in ESLint output`);
        }
      } catch (parseError) {
        console.warn('   ⚠️ ESLint failed, skipping unused imports detection');
      }
    }
  }

  private async detectUnusedConfigurations(): Promise<void> {
    console.log('⚙️ Detecting unused configurations...');
    
    const configPatterns = [
      {
        file: 'server/config/DeveloperExperience.ts',
        configs: ['autoSeedOnStartup', 'outputCredentials', 'includeExamples'],
        usageFiles: ['server/index.ts', 'server/config/**/*.ts']
      },
      {
        file: 'client/src/config/security.ts',
        configs: ['DEFAULT_SECURITY_CONFIG'],
        usageFiles: ['client/src/**/*.tsx', 'client/src/**/*.ts']
      }
    ];

    for (const pattern of configPatterns) {
      if (existsSync(pattern.file)) {
        const fileContent = readFileSync(pattern.file, 'utf8');
        
        for (const config of pattern.configs) {
          if (fileContent.includes(config)) {
            // Check if config is used in any of the usage files
            let isUsed = false;
            
            for (const usagePattern of pattern.usageFiles) {
              try {
                const grepOutput = execSync(
                  `find . -path "./node_modules" -prune -o -name "${usagePattern.split('/').pop()}" -type f -exec grep -l "${config}" {} +`,
                  { encoding: 'utf8', cwd: process.cwd() }
                );
                
                if (grepOutput.trim() && !grepOutput.includes(pattern.file)) {
                  isUsed = true;
                  break;
                }
              } catch (error) {
                // Grep found no matches, continue
              }
            }
            
            if (!isUsed) {
              this.report.details.unusedConfigurations.push({
                file: pattern.file,
                config,
                reason: 'Configuration defined but never used'
              });
              this.report.summary.unusedConfigurations++;
            }
          }
        }
      }
    }
    
    console.log(`   Found ${this.report.summary.unusedConfigurations} unused configurations`);
  }

  private generateRecommendations(): void {
    const recommendations: string[] = [];
    
    if (this.report.summary.unusedExports > 0) {
      recommendations.push(
        `Remove ${this.report.summary.unusedExports} unused exports to reduce bundle size`
      );
    }
    
    if (this.report.summary.unusedImports > 0) {
      recommendations.push(
        `Clean up ${this.report.summary.unusedImports} unused imports to improve build performance`
      );
    }
    
    if (this.report.summary.unusedConfigurations > 0) {
      recommendations.push(
        `Remove ${this.report.summary.unusedConfigurations} unused configuration options`
      );
    }
    
    if (this.report.summary.totalIssues > 20) {
      recommendations.push(
        'Consider setting up automated dead code detection in CI/CD pipeline'
      );
    }
    
    if (this.report.summary.totalIssues > 50) {
      recommendations.push(
        'High amount of dead code detected - consider gradual cleanup sprint'
      );
    }
    
    recommendations.push(
      'Run "npm run deadcode:fix" to automatically fix unused imports',
      'Use ts-prune regularly to monitor unused exports',
      'Consider implementing import/export analysis in code review process'
    );
    
    this.report.recommendations = recommendations;
  }
}

async function main() {
  const detector = new DeadCodeDetector();
  const report = await detector.detectDeadCode();
  
  // Save report to file
  const reportPath = join(process.cwd(), 'dead-code-report.json');
  writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // Display summary
  console.log('\n📊 Dead Code Detection Summary:');
  console.log(`   Unused Exports: ${report.summary.unusedExports}`);
  console.log(`   Unused Imports: ${report.summary.unusedImports}`);
  console.log(`   Unused Configurations: ${report.summary.unusedConfigurations}`);
  console.log(`   Total Issues: ${report.summary.totalIssues}`);
  
  console.log('\n💡 Recommendations:');
  report.recommendations.forEach(rec => console.log(`   • ${rec}`));
  
  console.log(`\n📄 Full report saved to: ${reportPath}`);
  
  // Exit with error code if issues found (for CI/CD)
  process.exit(report.summary.totalIssues > 0 ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { DeadCodeDetector };