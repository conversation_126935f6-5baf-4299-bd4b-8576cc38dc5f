#!/usr/bin/env tsx
/**
 * Database Stability Migration Script
 * 
 * This script helps migrate existing database operations to use the new
 * stable connection management and error boundaries.
 */

import { promises as fs } from 'fs';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { glob } from 'glob';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = resolve(__dirname, '..');

interface MigrationIssue {
  file: string;
  line: number;
  issue: string;
  suggestion: string;
  priority: 'high' | 'medium' | 'low';
}

class DatabaseMigrationAnalyzer {
  private issues: MigrationIssue[] = [];

  async analyzeProject(): Promise<void> {
    console.log('🔍 Analyzing project for database stability issues...');
    
    // Find all TypeScript files in server directory
    const serverFiles = await glob('server/**/*.ts', { 
      cwd: projectRoot,
      absolute: true 
    });

    for (const file of serverFiles) {
      await this.analyzeFile(file);
    }
  }

  private async analyzeFile(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const lines = content.split('\n');
      const relativePath = filePath.replace(projectRoot + '/', '');

      lines.forEach((line, index) => {
        this.checkLine(line, index + 1, relativePath);
      });
    } catch (error) {
      console.warn(`⚠️ Could not analyze ${filePath}:`, (error as Error).message);
    }
  }

  private checkLine(line: string, lineNumber: number, file: string): void {
    // Check for direct pool usage
    if (line.includes('pool.connect()') && !line.includes('withConnection')) {
      this.issues.push({
        file,
        line: lineNumber,
        issue: 'Direct pool.connect() usage',
        suggestion: 'Replace with withConnection() for better error handling and connection management',
        priority: 'high'
      });
    }

    // Check for missing error boundaries
    if (line.includes('await db.') && !line.includes('withDatabaseErrorBoundary')) {
      this.issues.push({
        file,
        line: lineNumber,
        issue: 'Database operation without error boundary',
        suggestion: 'Wrap with withDatabaseErrorBoundary() for better error handling',
        priority: 'medium'
      });
    }

    // Check for manual connection release
    if (line.includes('client.release()') && !line.includes('finally')) {
      this.issues.push({
        file,
        line: lineNumber,
        issue: 'Manual client.release() without finally block',
        suggestion: 'Use withConnection() to ensure proper connection cleanup',
        priority: 'high'
      });
    }

    // Check for raw database queries
    if (line.includes('pool.query(') || line.includes('client.query(')) {
      this.issues.push({
        file,
        line: lineNumber,
        issue: 'Raw database query without connection management',
        suggestion: 'Use withConnection() for better connection pooling',
        priority: 'medium'
      });
    }

    // Check for missing transaction management
    if (line.includes('BEGIN') || line.includes('COMMIT') || line.includes('ROLLBACK')) {
      this.issues.push({
        file,
        line: lineNumber,
        issue: 'Manual transaction management',
        suggestion: 'Use withTransaction() for automatic transaction handling',
        priority: 'medium'
      });
    }

    // Check for no timeout handling
    if (line.includes('await db.') && !line.includes('timeout') && !line.includes('withConnection')) {
      this.issues.push({
        file,
        line: lineNumber,
        issue: 'Database operation without timeout protection',
        suggestion: 'Use withConnection() which includes built-in timeout handling',
        priority: 'low'
      });
    }
  }

  generateReport(): void {
    console.log('\n📊 Database Stability Analysis Report');
    console.log('='.repeat(60));

    if (this.issues.length === 0) {
      console.log('✅ No critical database stability issues found!');
      return;
    }

    // Group by priority
    const byPriority = this.issues.reduce((acc, issue) => {
      acc[issue.priority] = acc[issue.priority] || [];
      acc[issue.priority].push(issue);
      return acc;
    }, {} as Record<string, MigrationIssue[]>);

    // Report high priority issues first
    ['high', 'medium', 'low'].forEach(priority => {
      const issues = byPriority[priority] || [];
      if (issues.length === 0) return;

      const emoji = priority === 'high' ? '🔴' : priority === 'medium' ? '🟡' : '🔵';
      console.log(`\n${emoji} ${priority.toUpperCase()} PRIORITY (${issues.length} issues):`);

      issues.forEach(issue => {
        console.log(`\n  📁 ${issue.file}:${issue.line}`);
        console.log(`     Issue: ${issue.issue}`);
        console.log(`     💡 Suggestion: ${issue.suggestion}`);
      });
    });

    // Summary
    console.log('\n📈 Summary:');
    console.log(`   🔴 High Priority: ${byPriority.high?.length || 0} issues`);
    console.log(`   🟡 Medium Priority: ${byPriority.medium?.length || 0} issues`);
    console.log(`   🔵 Low Priority: ${byPriority.low?.length || 0} issues`);
    console.log(`   📊 Total: ${this.issues.length} issues`);
  }

  async generateFixScript(): Promise<void> {
    const highPriorityIssues = this.issues.filter(issue => issue.priority === 'high');
    
    if (highPriorityIssues.length === 0) {
      console.log('\n✅ No critical issues requiring automatic fixes');
      return;
    }

    console.log('\n🔧 Generating database stability fixes...');

    const fixScript = `#!/usr/bin/env tsx
/**
 * Auto-generated Database Stability Fixes
 * Generated on ${new Date().toISOString()}
 * 
 * This script contains suggested fixes for critical database stability issues.
 * Review each change before applying!
 */

import { promises as fs } from 'fs';

// High priority fixes needed:
${highPriorityIssues.map(issue => `
// ${issue.file}:${issue.line}
// Issue: ${issue.issue}
// Suggestion: ${issue.suggestion}
`).join('')}

async function applyFixes() {
  console.log('🔧 Applying database stability fixes...');
  
  // TODO: Implement automatic fixes for common patterns
  // For now, this serves as a reference for manual fixes
  
  console.log('✅ Manual review and fixes required');
  console.log('📋 See comments above for specific issues and suggestions');
}

applyFixes().catch(console.error);
`;

    await fs.writeFile(resolve(projectRoot, 'scripts/fix-database-stability.ts'), fixScript);
    console.log('✅ Fix script generated: scripts/fix-database-stability.ts');
  }
}

// Health check for current database implementation
async function checkCurrentHealth(): Promise<void> {
  console.log('🏥 Checking current database health...');
  
  try {
    // Import the new database manager
    const { databaseManager } = await import('../server/utils/database');
    
    // Test connection
    const isHealthy = await databaseManager.testConnection();
    console.log(`   Connection: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    
    // Get metrics
    const metrics = databaseManager.getMetrics();
    console.log(`   Connection Errors: ${metrics.connectionErrors}`);
    console.log(`   Last Health Check: ${metrics.lastHealthCheck || 'Never'}`);
    
    // Get pool stats
    const poolStats = databaseManager.getPoolStats();
    console.log(`   Pool Stats: ${poolStats.total} total, ${poolStats.idle} idle, ${poolStats.waiting} waiting`);
    
  } catch (error) {
    console.error('❌ Error checking database health:', (error as Error).message);
  }
}

async function main() {
  console.log('🚀 Starting Database Stability Migration Analysis...\n');
  
  // Check current health
  await checkCurrentHealth();
  
  // Analyze for issues
  const analyzer = new DatabaseMigrationAnalyzer();
  await analyzer.analyzeProject();
  analyzer.generateReport();
  
  // Generate fixes
  await analyzer.generateFixScript();
  
  console.log('\n📋 Next Steps:');
  console.log('1. Review the issues identified above');
  console.log('2. Update critical database operations to use withConnection()');
  console.log('3. Add error boundaries to database operations');
  console.log('4. Test the application with the new database manager');
  console.log('5. Monitor connection pool metrics in production');
  
  console.log('\n🔗 Key Files to Update:');
  console.log('   - Use: import { withConnection, withTransaction } from "./utils/database"');
  console.log('   - Use: import { withDatabaseErrorBoundary } from "./utils/error-boundary"');
  console.log('   - Replace: pool.connect() with withConnection()');
  console.log('   - Replace: manual transactions with withTransaction()');
}

main().catch(console.error);