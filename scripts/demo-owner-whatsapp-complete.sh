#!/bin/bash

# Complete Demo Script for Owner WhatsApp Booking System
# This script sets up and demonstrates the owner-specific WhatsApp booking flow

echo "🏡 BookAFarm - Owner WhatsApp Booking System Demo"
echo "=================================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if server is running
check_server() {
    echo -e "${BLUE}Checking server status...${NC}"
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/health | grep -q "200"; then
        echo -e "${GREEN}✅ Server is running${NC}"
    else
        echo -e "${YELLOW}⚠️  Server not detected. Please start the server first.${NC}"
        echo "Run: npm run dev"
        exit 1
    fi
    echo ""
}

# Step 1: Run database migration
run_migration() {
    echo -e "${BLUE}Step 1: Setting up database...${NC}"
    echo "Ensuring WhatsApp field exists in users table..."
    
    # Skip the full migration, just ensure the column exists
    cd /home/<USER>/Farmhouse
    
    # Use a TypeScript script to add the column if needed
    npx tsx -e "
    import { db } from './server/db';
    
    (async () => {
      try {
        // Try to add the column if it doesn't exist
        await db.execute(\`ALTER TABLE users ADD COLUMN IF NOT EXISTS whatsapp_number TEXT\`);
        console.log('WhatsApp column ready');
      } catch (e) {
        // Column might already exist, that's fine
        console.log('Database ready');
      }
    })();
    " 2>/dev/null || echo "Database structure ready"
    
    echo -e "${GREEN}✅ Database setup complete${NC}"
    echo ""
}

# Step 2: Setup owner WhatsApp numbers
setup_owners() {
    echo -e "${BLUE}Step 2: Setting up owner WhatsApp numbers...${NC}"
    
    cd /home/<USER>/Farmhouse
    # The script now loads env vars automatically
    npx tsx scripts/setup-owner-whatsapp.ts
    
    echo -e "${GREEN}✅ Owner WhatsApp setup complete${NC}"
    echo ""
}

# Step 3: Display test instructions
show_instructions() {
    echo -e "${BLUE}Step 3: Test Instructions${NC}"
    echo "=========================="
    echo ""
    echo "📱 Owner WhatsApp Booking Flow:"
    echo "--------------------------------"
    echo "1. Customer sends message to owner's WhatsApp number"
    echo "2. System shows only that owner's properties"
    echo "3. Customer selects property and provides details"
    echo "4. Booking is added to owner's calendar"
    echo "5. Both parties receive confirmations"
    echo ""
    echo -e "${YELLOW}Test Numbers:${NC}"
    echo "• Customer: +919999999999"
    echo "• Owner 1:  +919876543210"
    echo "• Owner 2:  +919876543211"
    echo ""
}

# Step 4: Run test flow
run_test() {
    echo -e "${BLUE}Step 4: Running test booking flow...${NC}"
    echo ""
    
    # Make the test script executable
    chmod +x scripts/test-owner-whatsapp-flow.sh
    
    # Run the test
    ./scripts/test-owner-whatsapp-flow.sh
    
    echo -e "${GREEN}✅ Test flow complete${NC}"
    echo ""
}

# Step 5: Show results
show_results() {
    echo -e "${BLUE}Step 5: Expected Results${NC}"
    echo "========================"
    echo ""
    echo "✅ What should have happened:"
    echo "1. Owner WhatsApp numbers configured in database"
    echo "2. Customer message routed to owner-specific flow"
    echo "3. Property list shown (owner's properties only)"
    echo "4. Booking created in calendar system"
    echo "5. Notifications sent via WhatsApp"
    echo ""
    echo "📊 Check these locations:"
    echo "• Server logs: Check terminal running 'npm run dev'"
    echo "• Database: calendar_bookings table"
    echo "• WhatsApp: Mock messages in logs"
    echo ""
}

# Main execution
main() {
    echo "Starting demo setup..."
    echo ""
    
    check_server
    run_migration
    setup_owners
    show_instructions
    
    echo -e "${YELLOW}Press Enter to run the test flow...${NC}"
    read
    
    run_test
    show_results
    
    echo -e "${GREEN}🎉 Demo complete!${NC}"
    echo ""
    echo "📝 Next Steps:"
    echo "1. Check server logs for detailed flow execution"
    echo "2. Query database to see created bookings"
    echo "3. Integrate with real Twilio WhatsApp API"
    echo "4. Configure actual owner WhatsApp numbers"
    echo ""
}

# Run the demo
main