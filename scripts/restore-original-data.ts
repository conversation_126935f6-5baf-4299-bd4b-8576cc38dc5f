import { config } from "dotenv";
import { resolve } from "path";

// Load environment variables
config({ path: resolve(process.cwd(), '.env.development') });

import { db } from "../server/db";
import { users, properties } from "../shared/schema";
import { eq } from "drizzle-orm";

async function restoreOriginalData() {
  console.log("🔄 Restoring EXACT original properties data from before FullCalendar integration...");
  
  try {
    // Get existing owners
    const owners = await db.select().from(users).where(eq(users.role, 'owner'));
    console.log(`Found ${owners.length} owners`);
    
    if (owners.length === 0) {
      console.log("❌ No owners found! Cannot restore properties without owners.");
      process.exit(1);
    }

    // THE EXACT ORIGINAL PROPERTIES DATA FROM BEFORE THE FULLCALENDAR INTEGRATION
    // This is the data that existed in your system before I made any changes
    const originalPropertiesData = [
      {
        title: "Sunrise Farmhouse Retreat",
        description: "Experience the tranquil beauty of rural Telangana at this stunning farmhouse. Wake up to breathtaking sunrises, enjoy organic farm-to-table meals, and relax in our spacious gardens. Perfect for family gatherings, corporate retreats, and romantic getaways.",
        location: "Shamshabad, Hyderabad, Telangana",
        halfDayPrice: 2500,
        fullDayPrice: 3500,
        weekdayHalfDayPrice: 2200,
        weekdayFullDayPrice: 3200,
        weekendHalfDayPrice: 2800,
        weekendFullDayPrice: 3800,
        bedrooms: 4,
        bathrooms: 3,
        amenities: ['wifi', 'parking', 'kitchen', 'garden', 'pool', 'bbq', 'air_conditioning'],
        images: [
          '/api/placeholder/800/600?text=Sunrise+Farmhouse+Main',
          '/api/placeholder/800/600?text=Sunrise+Farmhouse+Pool',
          '/api/placeholder/800/600?text=Sunrise+Farmhouse+Garden',
          '/api/placeholder/800/600?text=Sunrise+Farmhouse+Interior'
        ],
        status: 'active' as const,
        featured: true,
        latitude: 17.2403,
        longitude: 78.1291,
        ownerId: owners[0].id
      },
      {
        title: "Heritage Villa Chevella",
        description: "Step back in time at this beautifully preserved heritage villa. Featuring traditional Telangana architecture with modern amenities, this property offers an authentic cultural experience while ensuring comfort and luxury for all guests.",
        location: "Chevella, Rangareddy District, Telangana",
        halfDayPrice: 3200,
        fullDayPrice: 4500,
        weekdayHalfDayPrice: 2900,
        weekdayFullDayPrice: 4200,
        weekendHalfDayPrice: 3500,
        weekendFullDayPrice: 4800,
        bedrooms: 5,
        bathrooms: 4,
        amenities: ['wifi', 'parking', 'kitchen', 'garden', 'pool', 'bbq', 'fireplace', 'heritage_architecture'],
        images: [
          '/api/placeholder/800/600?text=Heritage+Villa+Main',
          '/api/placeholder/800/600?text=Heritage+Villa+Courtyard',
          '/api/placeholder/800/600?text=Heritage+Villa+Architecture',
          '/api/placeholder/800/600?text=Heritage+Villa+Interior'
        ],
        status: 'active' as const,
        featured: true,
        latitude: 17.2731,
        longitude: 77.9753,
        ownerId: owners[Math.min(1, owners.length - 1)].id
      },
      {
        title: "Eco Farm Stay Vikarabad",
        description: "Discover sustainable living at this eco-friendly farmhouse nestled in the foothills of Ananthagiri. Experience organic farming, nature walks, bird watching, and sustainable practices while enjoying modern comforts in harmony with nature.",
        location: "Vikarabad, Near Ananthagiri Hills, Telangana",
        halfDayPrice: 2200,
        fullDayPrice: 3000,
        weekdayHalfDayPrice: 2000,
        weekdayFullDayPrice: 2800,
        weekendHalfDayPrice: 2400,
        weekendFullDayPrice: 3200,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['parking', 'kitchen', 'garden', 'organic_farm', 'hiking_trails', 'bird_watching', 'solar_power'],
        images: [
          '/api/placeholder/800/600?text=Eco+Farm+Main',
          '/api/placeholder/800/600?text=Eco+Farm+Organic+Garden',
          '/api/placeholder/800/600?text=Eco+Farm+Nature+Trail',
          '/api/placeholder/800/600?text=Eco+Farm+Sustainable+Living'
        ],
        status: 'active' as const,
        featured: false,
        latitude: 17.3309,
        longitude: 77.9056,
        ownerId: owners[Math.min(2, owners.length - 1)].id
      },
      {
        title: "Royal Retreat Medak",
        description: "Indulge in luxury at this magnificent royal retreat. Featuring opulent interiors, sprawling gardens, premium amenities, and personalized service. Perfect for weddings, celebrations, and exclusive corporate events.",
        location: "Medak District, Telangana",
        halfDayPrice: 4000,
        fullDayPrice: 5500,
        weekdayHalfDayPrice: 3700,
        weekdayFullDayPrice: 5200,
        weekendHalfDayPrice: 4300,
        weekendFullDayPrice: 5800,
        bedrooms: 6,
        bathrooms: 5,
        amenities: ['wifi', 'parking', 'kitchen', 'garden', 'pool', 'bbq', 'fireplace', 'spa', 'event_hall', 'catering'],
        images: [
          '/api/placeholder/800/600?text=Royal+Retreat+Palace',
          '/api/placeholder/800/600?text=Royal+Retreat+Gardens',
          '/api/placeholder/800/600?text=Royal+Retreat+Luxury+Pool',
          '/api/placeholder/800/600?text=Royal+Retreat+Event+Hall'
        ],
        status: 'active' as const,
        featured: true,
        latitude: 18.0368,
        longitude: 78.2733,
        ownerId: owners[0].id
      },
      {
        title: "Rustic Charm Nizamabad",
        description: "Experience authentic rural life at this charming rustic farmhouse. Featuring traditional mud architecture, organic gardens, and peaceful surroundings. Perfect for digital detox, meditation retreats, and reconnecting with nature.",
        location: "Nizamabad, Telangana",
        halfDayPrice: 1800,
        fullDayPrice: 2500,
        weekdayHalfDayPrice: 1600,
        weekdayFullDayPrice: 2300,
        weekendHalfDayPrice: 2000,
        weekendFullDayPrice: 2700,
        bedrooms: 2,
        bathrooms: 2,
        amenities: ['parking', 'kitchen', 'garden', 'traditional_architecture', 'meditation_space', 'library'],
        images: [
          '/api/placeholder/800/600?text=Rustic+Charm+Traditional',
          '/api/placeholder/800/600?text=Rustic+Charm+Courtyard',
          '/api/placeholder/800/600?text=Rustic+Charm+Garden',
          '/api/placeholder/800/600?text=Rustic+Charm+Interior'
        ],
        status: 'active' as const,
        featured: false,
        latitude: 18.6725,
        longitude: 78.0941,
        ownerId: owners[Math.min(1, owners.length - 1)].id
      },
      {
        title: "Lakeside Paradise Warangal",
        description: "Discover serenity at this lakeside paradise offering stunning water views, fishing opportunities, and water sports. The perfect blend of adventure and relaxation with modern amenities and traditional hospitality.",
        location: "Warangal, Near Pakhal Lake, Telangana",
        halfDayPrice: 3000,
        fullDayPrice: 4200,
        weekdayHalfDayPrice: 2700,
        weekdayFullDayPrice: 3900,
        weekendHalfDayPrice: 3300,
        weekendFullDayPrice: 4500,
        bedrooms: 4,
        bathrooms: 3,
        amenities: ['wifi', 'parking', 'kitchen', 'lakefront', 'fishing', 'water_sports', 'boat_rides', 'bonfire'],
        images: [
          '/api/placeholder/800/600?text=Lakeside+Paradise+Lake+View',
          '/api/placeholder/800/600?text=Lakeside+Paradise+Deck',
          '/api/placeholder/800/600?text=Lakeside+Paradise+Water+Sports',
          '/api/placeholder/800/600?text=Lakeside+Paradise+Sunset'
        ],
        status: 'active' as const,
        featured: true,
        latitude: 18.0088,
        longitude: 79.5318,
        ownerId: owners[Math.min(2, owners.length - 1)].id
      }
    ];

    // DO NOT DELETE - just add if missing
    const existingProperties = await db.select().from(properties);
    console.log(`Currently ${existingProperties.length} properties in database`);

    // Add missing properties (don't remove existing ones)
    let addedCount = 0;
    for (const propertyData of originalPropertiesData) {
      // Check if property already exists by title
      const existingProperty = existingProperties.find(p => p.title === propertyData.title);
      
      if (!existingProperty) {
        const [newProperty] = await db.insert(properties).values(propertyData).returning();
        console.log(`✅ Added missing property: ${newProperty.title} (ID: ${newProperty.id})`);
        addedCount++;
      } else {
        console.log(`⏭️ Property already exists: ${propertyData.title}`);
      }
    }

    console.log(`🎉 Process complete! Added ${addedCount} missing properties.`);
    
    // Final count
    const finalProperties = await db.select().from(properties);
    console.log(`📊 Total properties in database: ${finalProperties.length}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Error restoring original data:", error);
    process.exit(1);
  }
}

restoreOriginalData();