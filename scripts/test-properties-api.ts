#!/usr/bin/env tsx

import '../server/env-loader.js';
import { storage } from '../server/storage.js';
import { db } from '../server/db.js';
import { properties } from '../shared/schema.js';

async function testPropertiesAPI() {
  console.log('🧪 Testing Properties API Layer...\n');
  
  try {
    // Direct database query
    console.log('1. Direct database query:');
    const directProperties = await db.select().from(properties);
    console.log(`   Found ${directProperties.length} properties directly from database`);
    
    if (directProperties.length > 0) {
      const sample = directProperties[0];
      console.log(`   Sample property: ${sample.title} - Status: ${sample.status}`);
      console.log(`   Sample property columns:`, Object.keys(sample));
    }
    console.log();

    // Storage layer query
    console.log('2. Storage layer query:');
    const storageProperties = await storage.getProperties();
    console.log(`   Found ${storageProperties.length} properties via storage layer`);
    
    if (storageProperties.length > 0) {
      const sample = storageProperties[0];
      console.log(`   Sample property: ${sample.title} - Status: ${sample.status}`);
    }
    console.log();

    // Try with different filters
    console.log('3. Testing with featured filter:');
    const featuredProperties = await storage.getProperties(true);
    console.log(`   Found ${featuredProperties.length} featured properties`);
    console.log();

    console.log('4. Testing with no filters but explicit undefined:');
    const allProperties = await storage.getProperties(undefined, undefined, undefined, undefined, undefined, undefined);
    console.log(`   Found ${allProperties.length} properties with explicit undefined filters`);
    console.log();

    // Check specific property
    if (directProperties.length > 0) {
      console.log('5. Testing getProperty by ID:');
      const property = await storage.getProperty(directProperties[0].id);
      console.log(`   Retrieved property by ID: ${property ? property.title : 'null'}`);
    }

  } catch (error) {
    console.error('❌ Error testing properties API:', error);
  }
}

// Run the test
testPropertiesAPI().catch(console.error);