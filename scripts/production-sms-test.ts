import { config } from 'dotenv';
import { resolve } from 'path';

// Load production environment variables
config({ path: resolve(process.cwd(), '.env.production') });

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';

import { dltSMSService } from '../server/services/DLTSMSService';
import { templateService } from '../server/services/TemplateService';

async function testProductionSMS() {
  console.log('🚀 Testing SMS Templates in Production\n');

  try {
    // 1. Check service health
    console.log('1. Checking SMS service health...');
    const health = await dltSMSService.getServiceHealth();
    console.log('   ✅ Service Health:', health);

    if (!health.twilioConnected) {
      console.log('   ⚠️  Twilio not connected - check credentials');
      return;
    }

    // 2. List available templates
    console.log('\n2. Available templates:');
    const templates = await dltSMSService.getAvailableTemplates();
    templates.forEach((template, index) => {
      console.log(`   ${index + 1}. ${template}`);
    });

    // 3. Test booking confirmation template
    console.log('\n3. Testing booking confirmation SMS...');
    const testPhone = process.env.TEST_PHONE_NUMBER || '+919876543210';
    
    const result = await dltSMSService.sendBookingConfirmation(
      testPhone,
      'Green Valley Farm',
      '2024-12-25'
    );

    if (result.success) {
      console.log('   ✅ SMS sent successfully!');
      console.log(`   📱 Message SID: ${result.messageSid}`);
    } else {
      console.log('   ❌ SMS failed:', result.error);
    }

    // 4. Check recent SMS logs
    console.log('\n4. Recent SMS logs:');
    const template = await templateService.getActiveTemplateByKey('booking_confirmation');
    if (template) {
      const logs = await templateService.getSmsLogsByTemplate(template.id, 3);
      logs.forEach((log, index) => {
        console.log(`   ${index + 1}. ${log.recipientPhone} - ${log.status} - ${log.createdAt}`);
      });
    }

    // 5. Template analytics
    console.log('\n5. Template analytics:');
    const analytics = await templateService.getTemplateAnalytics();
    analytics.forEach((stat: any) => {
      console.log(`   ${stat.key}: ${stat.total_sends} sends, ${stat.success_rate}% success rate`);
    });

  } catch (error) {
    console.error('❌ Production SMS test failed:', error);
  }
}

// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  testProductionSMS()
    .then(() => {
      console.log('\n✅ Production SMS test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Production SMS test failed:', error);
      process.exit(1);
    });
}

export { testProductionSMS };