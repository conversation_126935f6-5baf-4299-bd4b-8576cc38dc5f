import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    globals: true,
    css: true,
    include: ['tests/unit/**/*.test.{ts,tsx}'],
    exclude: ['tests/e2e/**/*', 'tests/integration/**/*', 'node_modules/**/*'],
    reporters: ['verbose'],
    testTimeout: 10000,
    environmentMatchGlobs: [
      ['tests/unit/architecture/**', 'node']
    ],
    coverage: {
      reporter: ['text', 'json', 'html'],
      include: [
        'client/src/**/*.{ts,tsx}',
        'server/**/*.{ts,tsx}',
        'shared/**/*.{ts,tsx}'
      ],
      exclude: [
        'node_modules/**',
        'tests/**',
        '**/*.d.ts',
        '**/*.config.{ts,js}',
        'client/src/main.tsx',
        'server/index.ts'
      ]
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './client/src'),
      '@shared': path.resolve(__dirname, './shared'),
      '@server': path.resolve(__dirname, './server'),
      'razorpay': path.resolve(__dirname, './types/razorpay.d.ts')
    }
  }
})